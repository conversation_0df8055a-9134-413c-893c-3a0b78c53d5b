---
title: AiHubMix 提供商配置
description: 学习如何在 LobeChat 中配置和使用 AiHubMix 提供商
tags:
  - AiHubMix
  - 提供商配置
  - 配置指南
---

# AiHubMix 提供商配置

AiHubMix 是一个 AI 模型聚合平台，通过统一的 OpenAI 兼容 API 接口提供多种 AI 模型的访问服务。本指南将帮助您在 LobeChat 中设置 AiHubMix 提供商。

## 前置条件

在使用 AiHubMix API 之前，您需要：

1. **创建 AiHubMix 账户**
   - 访问 [AiHubMix](https://lobe.li/MZmv94N)
   - 注册账户

2. **获取 API 密钥**
   - 登录您的 AiHubMix 控制台
   - 导航到 API 设置
   - 生成用于 LobeChat 的 API 密钥

## 配置

### 环境变量

在您的 `.env` 文件中添加以下环境变量：

```bash
# 启用 AiHubMix 提供商
ENABLED_AIHUBMIX=1

# AiHubMix API 密钥（必需）
AIHUBMIX_API_KEY=your_aihubmix_api_key
```

### 可用模型

AiHubMix 提供多种热门 AI 模型的访问，包括：

- **GPT-4o Mini** - OpenAI 的高性价比小型模型
- **GPT-4o** - OpenAI 的旗舰多模态模型
- **Claude 3.5 Sonnet** - Anthropic 的高级推理模型
- **Claude 3.5 Haiku** - 快速高效的 Claude 模型
- **Gemini Pro 1.5** - Google 的长上下文支持模型
- **DeepSeek V3** - 具有高级推理能力的模型

## 使用方法

1. **配置 API 密钥**
   - 在环境变量中设置您的 AiHubMix API 密钥
   - 重启您的 LobeChat 实例

2. **选择模型**
   - 进入 LobeChat 设置
   - 导航到语言模型
   - 选择 AiHubMix 作为您的提供商
   - 从可用模型中选择

3. **开始对话**
   - 创建新对话
   - 选择 AiHubMix 模型
   - 开始您的对话

## 功能特性

- **多模型访问**：通过单一 API 访问各种 AI 模型
- **OpenAI 兼容**：使用标准 OpenAI API 格式
- **函数调用**：支持兼容模型的函数调用功能
- **视觉能力**：部分模型支持图像分析
- **模型获取**：自动获取可用模型列表

## 故障排除

### 常见问题

1. **401 认证错误**
   - 验证您的 API 密钥是否正确
   - 确保 API 密钥具有适当的权限
   - 检查您的账户是否有足够的积分

2. **模型不可用**
   - 某些模型可能有使用限制
   - 查看 AiHubMix 文档了解模型可用性
   - 验证您的账户等级是否支持请求的模型

3. **速率限制**
   - AiHubMix 可能根据您的计划有速率限制
   - 考虑升级您的计划以获得更高的限制

## 支持

如需更多支持：

- 访问 [AiHubMix 文档](https://docs.aihubmix.com/)
- 查看 [模型列表](https://docs.aihubmix.com/cn/api/Model-List)
- 联系 AiHubMix 支持团队解决 API 相关问题
