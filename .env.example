# add a access code to lock your lobe-chat application, you can set a long password to avoid leaking. If this value contains a comma, it is a password array.
# ACCESS_CODE=lobe66

# Specify your API Key selection method, currently supporting `random` and `turn`.
# API_KEY_SELECT_MODE=random


########################################
########## AI Provider Service #########
########################################

### OpenAI ###

# you openai api key
OPENAI_API_KEY=sk-xxxxxxxxx

# use a proxy to connect to the OpenAI API
# OPENAI_PROXY_URL=https://api.openai.com/v1

# add your custom model name, multi model separate by comma. for example gpt-3.5-1106,gpt-4-1106
# OPENAI_MODEL_LIST=gpt-3.5-turbo


### Azure OpenAI ###

# you can learn azure OpenAI Service on https://learn.microsoft.com/en-us/azure/ai-services/openai/overview
# use Azure OpenAI Service by uncomment the following line

# The API key you applied for on the Azure OpenAI account page, which can be found in the "Keys and Endpoints" section.
# AZURE_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# The endpoint you applied for on the Azure OpenAI account page, which can be found in the "Keys and Endpoints" section.
# AZURE_ENDPOINT=https://docs-test-001.openai.azure.com

# Azure's API version, follows the YYYY-MM-DD format
# AZURE_API_VERSION=2024-10-21


### Anthropic Service ####

# ANTHROPIC_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# use a proxy to connect to the Anthropic API
# ANTHROPIC_PROXY_URL=https://api.anthropic.com


### Google AI  ####

# GOOGLE_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx


### AWS Bedrock  ###

# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=xxxxxxxxxxxxxxxxxxx
# AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx


### Ollama AI ####

# You can use ollama to get and run LLM locally, learn more about it via https://github.com/ollama/ollama

# The local/remote ollama service url
# OLLAMA_PROXY_URL=http://127.0.0.1:11434

# OLLAMA_MODEL_LIST=your_ollama_model_names


### OpenRouter Service ###

# OPENROUTER_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# OPENROUTER_MODEL_LIST=model1,model2,model3


### Mistral AI ###

# MISTRAL_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Perplexity Service ###

# PERPLEXITY_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Groq Service ####

# GROQ_API_KEY=gsk_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

#### 01.AI Service ####

# ZEROONE_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### TogetherAI Service ###

# TOGETHERAI_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### ZhiPu AI ###

# ZHIPU_API_KEY=xxxxxxxxxxxxxxxxxxx.xxxxxxxxxxxxx

### Moonshot AI  ####

# MOONSHOT_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Minimax AI  ####

# MINIMAX_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### DeepSeek AI  ####

# DEEPSEEK_PROXY_URL=https://api.deepseek.com/v1
# DEEPSEEK_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Qiniu AI  ####

# QINIU_PROXY_URL=https://api.qnaigc.com/v1
# QINIU_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Qwen AI  ####

# QWEN_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Cloudflare Workers AI  ####

# CLOUDFLARE_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# CLOUDFLARE_BASE_URL_OR_ACCOUNT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### SiliconCloud AI  ####

# SILICONCLOUD_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx


### TencentCloud AI  ####

# TENCENT_CLOUD_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### PPIO ####

# PPIO_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### INFINI-AI ###

# INFINIAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### ModelScope ###

# MODELSCOPE_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### AiHubMix ###

# AIHUBMIX_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx


########################################
############ Market Service ############
########################################

# The LobeChat agents market index url
# AGENTS_INDEX_URL=https://chat-agents.lobehub.com

########################################
############ Plugin Service ############
########################################

# The LobeChat plugins store index url
# PLUGINS_INDEX_URL=https://chat-plugins.lobehub.com

# set the plugin settings
# the format is `plugin-identifier:key1=value1;key2=value2`, multiple settings fields are separated by semicolons `;`, multiple plugin settings are separated by commas `,`.
# PLUGIN_SETTINGS=search-engine:SERPAPI_API_KEY=xxxxx

########################################
####### Doc / Changelog Service ########
########################################

# Use in Changelog / Document service cdn url prefix
# DOC_S3_PUBLIC_DOMAIN=https://xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Use in dev cdn workflow
# DOC_S3_ACCESS_KEY_ID=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# DOC_S3_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx


########################################
##### S3 Object Storage Service ########
########################################

# S3 keys
# S3_ACCESS_KEY_ID=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# S3_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Bucket name
# S3_BUCKET=lobechat

# Bucket request endpoint
# S3_ENDPOINT=https://xxxxxxxxxxxxxxxxxxxxxxxxxxxxx.r2.cloudflarestorage.com

# Public access domain for the bucket
# S3_PUBLIC_DOMAIN=https://s3-for-lobechat.your-domain.com

# Bucket region, such as us-west-1, generally not needed to add
# but some service providers may require configuration
# S3_REGION=us-west-1


########################################
############ Auth Service ##############
########################################


# Clerk related configurations

# Clerk public key and secret key
#NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_xxxxxxxxxxx
#CLERK_SECRET_KEY=sk_live_xxxxxxxxxxxxxxxxxxxxxx

# you need to config the clerk webhook secret key if you want to use the clerk with database
#CLERK_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxx


# NextAuth related configurations
# NEXT_PUBLIC_ENABLE_NEXT_AUTH=1
# NEXT_AUTH_SECRET=

# Auth0 configurations
# AUTH_AUTH0_ID=
# AUTH_AUTH0_SECRET=
# AUTH_AUTH0_ISSUER=https://your-domain.auth0.com

########################################
########## Server Database #############
########################################

# Specify the service mode as server if you want to use the server database
# NEXT_PUBLIC_SERVICE_MODE=server

# Postgres database URL
# DATABASE_URL=postgres://username:password@host:port/database

# use `openssl rand -base64 32` to generate a key for the encryption of the database
# we use this key to encrypt the user api key and proxy url
#KEY_VAULTS_SECRET=xxxxx/xxxxxxxxxxxxxx=

# Specify the Embedding model and Reranker model(unImplemented)
# DEFAULT_FILES_CONFIG="embedding_model=openai/embedding-text-3-small,reranker_model=cohere/rerank-english-v3.0,query_mode=full_text"

########################################
########## MCP Service Config ##########
########################################

# MCP tool call timeout (milliseconds)
# MCP_TOOL_TIMEOUT=60000
