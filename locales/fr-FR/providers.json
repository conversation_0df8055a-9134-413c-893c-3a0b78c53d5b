{"ai21": {"description": "AI21 Labs construit des modèles de base et des systèmes d'intelligence artificielle pour les entreprises, accélérant l'application de l'intelligence artificielle générative en production."}, "ai360": {"description": "360 AI est une plateforme de modèles et de services IA lancée par la société 360, offrant divers modèles avancés de traitement du langage naturel, y compris 360GPT2 Pro, 360GPT Pro, 360GPT Turbo et 360GPT Turbo Responsibility 8K. Ces modèles combinent de grands paramètres et des capacités multimodales, largement utilisés dans la génération de texte, la compréhension sémantique, les systèmes de dialogue et la génération de code. Grâce à une stratégie de tarification flexible, 360 AI répond à des besoins variés des utilisateurs, soutenant l'intégration des développeurs et favorisant l'innovation et le développement des applications intelligentes."}, "aihubmix": {"description": "AiHubMix offre un accès à divers modèles d'IA via une interface API unifiée."}, "anthropic": {"description": "Anthropic est une entreprise axée sur la recherche et le développement en intelligence artificielle, offrant une gamme de modèles linguistiques avancés, tels que Claude 3.5 Sonnet, Claude 3 Sonnet, Claude 3 Opus et Claude 3 Haiku. Ces modèles atteignent un équilibre idéal entre intelligence, rapidité et coût, adaptés à divers scénarios d'application, allant des charges de travail d'entreprise aux réponses rapides. Claude 3.5 Sonnet, en tant que dernier modèle, a excellé dans plusieurs évaluations tout en maintenant un bon rapport qualité-prix."}, "azure": {"description": "Azure propose une variété de modèles IA avancés, y compris GPT-3.5 et la dernière série GPT-4, prenant en charge divers types de données et tâches complexes, tout en s'engageant à fournir des solutions IA sécurisées, fiables et durables."}, "azureai": {"description": "Azure propose une variété de modèles d'IA avancés, y compris GPT-3.5 et la dernière série GPT-4, prenant en charge divers types de données et des tâches complexes, s'engageant à fournir des solutions d'IA sécurisées, fiables et durables."}, "baichuan": {"description": "Baichuan Intelligent est une entreprise spécialisée dans le développement de grands modèles d'intelligence artificielle, dont les modèles excellent dans les tâches en chinois telles que l'encyclopédie de connaissances, le traitement de longs textes et la création, surpassant les modèles dominants étrangers. Baichuan Intelligent possède également des capacités multimodales de premier plan, se distinguant dans plusieurs évaluations autorisées. Ses modèles incluent Baichuan 4, Baichuan 3 Turbo et Baichuan 3 Turbo 128k, chacun optimisé pour différents scénarios d'application, offrant des solutions à bon rapport qualité-prix."}, "bedrock": {"description": "Bedrock est un service proposé par Amazon AWS, axé sur la fourniture de modèles linguistiques et visuels avancés pour les entreprises. Sa famille de modèles comprend la série Claude d'Anthropic, la série Llama 3.1 de Meta, etc., offrant une variété d'options allant des modèles légers aux modèles haute performance, prenant en charge des tâches telles que la génération de texte, les dialogues et le traitement d'images, adaptées aux applications d'entreprise de différentes tailles et besoins."}, "cloudflare": {"description": "Exécutez des modèles d'apprentissage automatique alimentés par GPU sans serveur sur le réseau mondial de Cloudflare."}, "cohere": {"description": "Cohere vous apporte les modèles multilingues les plus avancés, des fonctionnalités de recherche sophistiquées et un espace de travail AI sur mesure pour les entreprises modernes - le tout intégré dans une plateforme sécurisée."}, "deepseek": {"description": "DeepSeek est une entreprise spécialisée dans la recherche et l'application des technologies d'intelligence artificielle, dont le dernier modèle, DeepSeek-V2.5, combine des capacités de dialogue général et de traitement de code, réalisant des améliorations significatives dans l'alignement des préférences humaines, les tâches d'écriture et le suivi des instructions."}, "fal": {"description": "Plateforme média générative destinée aux développeurs"}, "fireworksai": {"description": "Fireworks AI est un fournisseur de services de modèles linguistiques avancés, axé sur les appels de fonction et le traitement multimodal. Son dernier modèle, Firefunction V2, basé sur Llama-3, est optimisé pour les appels de fonction, les dialogues et le suivi des instructions. Le modèle de langage visuel FireLLaVA-13B prend en charge les entrées mixtes d'images et de texte. D'autres modèles notables incluent la série Llama et la série Mixtral, offrant un support efficace pour le suivi et la génération d'instructions multilingues."}, "giteeai": {"description": "L'API serverless de gitee ai fournit aux développeurs d'IA un service d'api d'inférence grand modèle prêt à l'emploi."}, "github": {"description": "Avec les modèles GitHub, les développeurs peuvent devenir des ingénieurs en IA et créer avec les modèles d'IA les plus avancés de l'industrie."}, "google": {"description": "La série Gemini de Google est son modèle IA le plus avancé et polyvalent, développé par Google DeepMind, conçu pour le multimédia, prenant en charge la compréhension et le traitement sans couture de texte, code, images, audio et vidéo. Adapté à divers environnements, des centres de données aux appareils mobiles, il améliore considérablement l'efficacité et l'applicabilité des modèles IA."}, "groq": {"description": "Le moteur d'inférence LPU de Groq a excellé dans les derniers tests de référence des grands modèles de langage (LLM), redéfinissant les normes des solutions IA grâce à sa vitesse et son efficacité impressionnantes. Groq représente une vitesse d'inférence instantanée, montrant de bonnes performances dans les déploiements basés sur le cloud."}, "higress": {"description": "Higress est une passerelle API cloud-native, née au sein d'Alibaba pour résoudre les problèmes de rechargement de Tengine affectant les connexions persistantes, ainsi que le manque de capacités d'équilibrage de charge pour gRPC/Dubbo."}, "huggingface": {"description": "L'API d'inférence HuggingFace offre un moyen rapide et gratuit d'explorer des milliers de modèles adaptés à diverses tâches. Que vous soyez en train de prototyper une nouvelle application ou d'expérimenter les capacités de l'apprentissage automatique, cette API vous permet d'accéder instantanément à des modèles performants dans de nombreux domaines."}, "hunyuan": {"description": "Un modèle de langage développé par Ten<PERSON>, doté d'une puissante capacité de création en chinois, d'une capacité de raisonnement logique dans des contextes complexes, ainsi que d'une capacité fiable d'exécution des tâches."}, "infiniai": {"description": "Fournit aux développeurs d'applications des services de grands modèles performants, faciles à utiliser et sécurisés, couvrant l'ensemble du processus, de la conception des grands modèles à leur déploiement en tant que service."}, "internlm": {"description": "Organisation open source dédiée à la recherche et au développement d'outils pour les grands modèles. Fournit à tous les développeurs d'IA une plateforme open source efficace et facile à utiliser, rendant les technologies de pointe en matière de grands modèles et d'algorithmes accessibles."}, "jina": {"description": "Jina AI, fondée en 2020, est une entreprise leader dans le domaine de l'IA de recherche. Notre plateforme de recherche de base comprend des modèles vectoriels, des réarrangeurs et de petits modèles de langage, aidant les entreprises à construire des applications de recherche génératives et multimodales fiables et de haute qualité."}, "lmstudio": {"description": "LM Studio est une application de bureau pour développer et expérimenter des LLM sur votre ordinateur."}, "minimax": {"description": "MiniMax est une entreprise de technologie d'intelligence artificielle générale fondée en 2021, dédiée à la co-création d'intelligence avec les utilisateurs. MiniMax a développé de manière autonome différents modèles de grande taille, y compris un modèle de texte MoE à un trillion de paramètres, un modèle vocal et un modèle d'image. Elle a également lancé des applications telles que Conch AI."}, "mistral": {"description": "<PERSON><PERSON><PERSON> propose des modèles avancés généraux, professionnels et de recherche, largement utilisés dans des domaines tels que le raisonnement complexe, les tâches multilingues et la génération de code. Grâce à une interface d'appel de fonction, les utilisateurs peuvent intégrer des fonctionnalités personnalisées pour des applications spécifiques."}, "modelscope": {"description": "ModelScope est une plateforme de modèles en tant que service lancée par Alibaba Cloud, offrant une riche gamme de modèles d'IA et de services d'inférence."}, "moonshot": {"description": "Moonshot est une plateforme open source lancée par Beijing Dark Side Technology Co., Ltd., offrant divers modèles de traitement du langage naturel, avec des applications dans des domaines variés, y compris mais sans s'y limiter, la création de contenu, la recherche académique, les recommandations intelligentes, le diagnostic médical, etc., prenant en charge le traitement de longs textes et des tâches de génération complexes."}, "novita": {"description": "Novita AI est une plateforme offrant des services API pour divers grands modèles de langage et la génération d'images IA, flexible, fiable et rentable. Elle prend en charge les derniers modèles open source tels que Llama3, Mistral, et fournit des solutions API complètes, conviviales et évolutives pour le développement d'applications IA, adaptées à la croissance rapide des startups IA."}, "nvidia": {"description": "NVIDIA NIM™ fournit des conteneurs pour l'inférence de microservices accélérés par GPU auto-hébergés, prenant en charge le déploiement de modèles d'IA pré-entraînés et personnalisés dans le cloud, les centres de données, les PC personnels RTX™ AI et les stations de travail."}, "ollama": {"description": "Les modèles proposés par Ollama couvrent largement des domaines tels que la génération de code, les calculs mathématiques, le traitement multilingue et les interactions conversationnelles, répondant à des besoins diversifiés pour le déploiement en entreprise et la localisation."}, "openai": {"description": "OpenAI est un institut de recherche en intelligence artificielle de premier plan au monde, dont les modèles, tels que la série GPT, font progresser les frontières du traitement du langage naturel. OpenAI s'engage à transformer plusieurs secteurs grâce à des solutions IA innovantes et efficaces. Leurs produits offrent des performances et une rentabilité remarquables, largement utilisés dans la recherche, le commerce et les applications innovantes."}, "openrouter": {"description": "OpenRouter est une plateforme de service fournissant des interfaces pour divers modèles de pointe, prenant en charge OpenAI, Anthropic, LLaMA et plus encore, adaptée à des besoins de développement et d'application diversifiés. Les utilisateurs peuvent choisir de manière flexible le modèle et le prix optimaux en fonction de leurs besoins, améliorant ainsi l'expérience IA."}, "perplexity": {"description": "Perplexity est un fournisseur de modèles de génération de dialogue de premier plan, offrant divers modèles avancés Llama 3.1, prenant en charge les applications en ligne et hors ligne, particulièrement adaptés aux tâches complexes de traitement du langage naturel."}, "ppio": {"description": "PPIO Paiouyun offre des services API de modèles open source stables et rentables, prenant en charge toute la gamme DeepSeek, Llama, Qwen et d'autres grands modèles de pointe dans l'industrie."}, "qiniu": {"description": "Qiniu est un fournisseur de services de cloud, offrant des API de IA de haute vitesse et d'efficacité, incluant des modèles Alibaba, avec des options flexibles pour la construction et l'application d'applications IA."}, "qwen": {"description": "<PERSON><PERSON>wen est un modèle de langage à grande échelle développé de manière autonome par Alibaba Cloud, doté de puissantes capacités de compréhension et de génération du langage naturel. Il peut répondre à diverses questions, créer du contenu écrit, exprimer des opinions, rédiger du code, etc., jouant un rôle dans plusieurs domaines."}, "sambanova": {"description": "SambaNova Cloud permet aux développeurs d'utiliser facilement les meilleurs modèles open source et de bénéficier de la vitesse d'inférence la plus rapide."}, "search1api": {"description": "Search1API offre un accès à la série de modèles DeepSeek pouvant se connecter à Internet selon les besoins, y compris les versions standard et rapide, avec un choix de modèles de différentes tailles de paramètres."}, "sensenova": {"description": "SenseNova, soutenue par la puissante infrastructure de SenseTime, offre des services de modèles de grande taille complets, efficaces et faciles à utiliser."}, "siliconcloud": {"description": "SiliconFlow s'engage à accélérer l'AGI pour le bénéfice de l'humanité, en améliorant l'efficacité de l'IA à grande échelle grâce à une pile GenAI facile à utiliser et à faible coût."}, "spark": {"description": "Le modèle Spark de iFlytek offre de puissantes capacités IA dans plusieurs domaines et langues, utilisant des technologies avancées de traitement du langage naturel pour construire des applications innovantes adaptées à divers scénarios verticaux tels que le matériel intelligent, la santé intelligente et la finance intelligente."}, "stepfun": {"description": "Le modèle StepFun est doté de capacités multimodales et de raisonnement complexe de premier plan dans l'industrie, prenant en charge la compréhension de textes très longs et des fonctionnalités puissantes de moteur de recherche autonome."}, "taichu": {"description": "L'Institut d'automatisation de l'Académie chinoise des sciences et l'Institut de recherche en intelligence artificielle de Wuhan ont lancé une nouvelle génération de grands modèles multimodaux, prenant en charge des tâches de questions-réponses complètes, de création de texte, de génération d'images, de compréhension 3D, d'analyse de signaux, avec des capacités cognitives, de compréhension et de création renforcées, offrant une toute nouvelle expérience interactive."}, "tencentcloud": {"description": "La capacité atomique du moteur de connaissance (LLM Knowledge Engine Atomic Power) est une capacité de question-réponse complète développée sur la base du moteur de connaissance, destinée aux entreprises et aux développeurs. Elle offre la possibilité de créer et de développer des applications de modèles de manière flexible. Vous pouvez assembler votre service de modèle exclusif en utilisant plusieurs capacités atomiques, en appelant des services tels que l'analyse de documents, la séparation, l'embedding, la réécriture multi-tours, etc., pour personnaliser les affaires AI spécifiques à votre entreprise."}, "togetherai": {"description": "Together AI s'engage à réaliser des performances de pointe grâce à des modèles IA innovants, offrant une large capacité de personnalisation, y compris un support d'évolutivité rapide et un processus de déploiement intuitif, répondant à divers besoins d'entreprise."}, "upstage": {"description": "Upstage se concentre sur le développement de modèles IA pour divers besoins commerciaux, y compris Solar LLM et Document AI, visant à réaliser une intelligence générale artificielle (AGI) pour le travail. Créez des agents de dialogue simples via l'API Chat, et prenez en charge les appels de fonction, la traduction, l'intégration et les applications spécifiques à un domaine."}, "v0": {"description": "v0 est un assistant de programmation en binôme. Il vous suffit de décrire vos idées en langage naturel, et il génère le code et l'interface utilisateur (UI) pour votre projet."}, "vertexai": {"description": "La série Gemini de Google est son modèle d'IA le plus avancé et polyvalent, développé par Google DeepMind, conçu pour être multimodal, prenant en charge la compréhension et le traitement sans couture de texte, de code, d'images, d'audio et de vidéo. Adapté à divers environnements, des centres de données aux appareils mobiles, il améliore considérablement l'efficacité et l'applicabilité des modèles d'IA."}, "vllm": {"description": "vLLM est une bibliothèque rapide et facile à utiliser pour l'inférence et les services LLM."}, "volcengine": {"description": "La plateforme de développement des services de grands modèles lancée par ByteDance, offrant des services d'appel de modèles riches en fonctionnalités, sécurisés et compétitifs en termes de prix. Elle propose également des fonctionnalités de bout en bout telles que les données de modèle, le réglage fin, l'inférence et l'évaluation, garantissant ainsi le succès de votre développement d'applications AI."}, "wenxin": {"description": "Plateforme de développement et de services d'applications AI natives et de modèles de grande envergure, tout-en-un pour les entreprises, offrant la chaîne d'outils la plus complète et facile à utiliser pour le développement de modèles d'intelligence artificielle générative et le développement d'applications."}, "xai": {"description": "xAI est une entreprise dédiée à la construction d'intelligences artificielles pour accélérer les découvertes scientifiques humaines. Notre mission est de promouvoir notre compréhension commune de l'univers."}, "xinference": {"description": "Xorbits Inference (Xinference) est une plateforme open source conçue pour simplifier l'exécution et l'intégration de divers modèles d'IA. Grâce à Xinference, vous pouvez utiliser n'importe quel LLM open source, modèle d'embedding ou modèle multimodal pour effectuer des inférences dans le cloud ou en local, et créer des applications IA puissantes."}, "zeroone": {"description": "01.AI se concentre sur les technologies d'intelligence artificielle de l'ère IA 2.0, promouvant activement l'innovation et l'application de \"l'homme + l'intelligence artificielle\", utilisant des modèles puissants et des technologies IA avancées pour améliorer la productivité humaine et réaliser l'autonomisation technologique."}, "zhipu": {"description": "Zhipu AI propose une plateforme ouverte de modèles multimodaux et linguistiques, prenant en charge une large gamme de scénarios d'application IA, y compris le traitement de texte, la compréhension d'images et l'assistance à la programmation."}}