{"azure": {"azureApiVersion": {"desc": "Version de l'API Azure, au format YYYY-MM-DD, consultez [la dernière version](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "Obt<PERSON>r la liste", "title": "Version de l'API Azure"}, "empty": "Veuillez saisir l'ID du modèle pour ajouter le premier modèle", "endpoint": {"desc": "Lors de l'inspection des ressources sur le portail Azure, vous pouvez trouver cette valeur dans la section 'Clés et points de terminaison'", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Adresse de l'API Azure"}, "modelListPlaceholder": "Sélectionnez ou ajoutez le modèle OpenAI que vous avez déployé", "title": "Azure OpenAI", "token": {"desc": "Lors de l'inspection des ressources sur le portail Azure, vous pouvez trouver cette valeur dans la section 'Clés et points de terminaison'. Vous pouvez utiliser KEY1 ou KEY2", "placeholder": "Clé API Azure", "title": "Clé API"}}, "azureai": {"azureApiVersion": {"desc": "Version de l'API Azure, au format YYYY-MM-DD. Consultez la [dernière version](https://learn.microsoft.com/fr-fr/azure/ai-services/openai/reference#chat-completions)", "fetch": "Obt<PERSON>r la liste", "title": "Version de l'API Azure"}, "endpoint": {"desc": "Trouvez le point de terminaison d'inférence du modèle Azure AI dans l'aperçu du projet Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Point de terminaison Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Trouvez la clé API dans l'aperçu du projet Azure AI", "placeholder": "Clé Azure", "title": "Clé"}}, "bedrock": {"accessKeyId": {"desc": "Saisissez l'ID de clé d'accès AWS", "placeholder": "ID de clé d'accès AWS", "title": "ID de clé d'accès AWS"}, "checker": {"desc": "Vérifiez si l'AccessKeyId / SecretAccessKey est correctement saisi"}, "region": {"desc": "Saisissez la région AWS", "placeholder": "Région AWS", "title": "Région AWS"}, "secretAccessKey": {"desc": "Saisissez la clé d'accès secrète AWS", "placeholder": "Clé d'accès secrète AWS", "title": "Clé d'accès secrète AWS"}, "sessionToken": {"desc": "Si vous utilisez AWS SSO/STS, ve<PERSON><PERSON>z entrer votre jeton de session AWS", "placeholder": "Jeton de session AWS", "title": "Jeton de session AWS (facultatif)"}, "title": "Bedrock", "unlock": {"customRegion": "Région de service personnalisée", "customSessionToken": "Jeton de session personnalisé", "description": "Entrez votre ID de clé d'accès AWS / SecretAccessKey pour commencer la session. L'application ne stockera pas votre configuration d'authentification.", "imageGenerationDescription": "Saisissez votre AWS AccessKeyId / SecretAccessKey pour commencer la génération. L'application ne conservera pas vos informations d'authentification.", "title": "Utiliser des informations d'authentification Bedrock personnalisées"}}, "cloudflare": {"apiKey": {"desc": "Veuillez remplir l'Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Saisir l'ID de compte Cloudflare ou l'adresse API personnalisée", "placeholder": "ID de compte Cloudflare / URL API personnalisée", "title": "ID de compte Cloudflare / adresse API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Veuillez entrer votre clé API", "title": "Clé API"}, "basicTitle": "Informations de base", "configTitle": "Informations de configuration", "confirm": "<PERSON><PERSON><PERSON>", "createSuccess": "Création réussie", "description": {"placeholder": "Description du fournisseur (facultatif)", "title": "Description du fournisseur"}, "id": {"desc": "Identifiant unique du fournisseur de services, qui ne peut pas être modifié après sa création", "format": "Ne peut contenir que des chiffres, des lettres minuscules, des tirets (-) et des underscores (_) ", "placeholder": "Utilisez uniquement des lettres minuscules, par exemple openai, non modifiable après création", "required": "Veuillez entrer l'ID du fournisseur", "title": "ID du fournisseur"}, "logo": {"required": "Veuillez télécharger un logo valide pour le fournisseur", "title": "Logo du fournisseur"}, "name": {"placeholder": "Veuillez entrer le nom d'affichage du fournisseur", "required": "Veuillez entrer le nom du fournisseur", "title": "Nom du fournisseur"}, "proxyUrl": {"required": "<PERSON><PERSON><PERSON><PERSON> remplir l'adresse du proxy", "title": "<PERSON><PERSON><PERSON> du proxy"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Veuillez sélectionner le type de SDK", "title": "Format de requête"}, "title": "C<PERSON>er un fournisseur AI personnalisé"}, "github": {"personalAccessToken": {"desc": "Entrez votre PAT GitHub, cliquez [ici](https://github.com/settings/tokens) pour en créer un.", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Entrez votre jeton Hu<PERSON>, cliquez [ici](https://huggingface.co/settings/tokens) pour en créer un", "placeholder": "hf_xxxxxxxxx", "title": "<PERSON><PERSON>"}}, "list": {"title": {"disabled": "Fournisseur non activé", "enabled": "Fournisseur activé"}}, "menu": {"addCustomProvider": "Ajouter un fournisseur personnalisé", "all": "<PERSON>ut", "list": {"disabled": "Non activé", "enabled": "Activé"}, "notFound": "Aucun résultat trouvé", "searchProviders": "Rechercher des fournisseurs...", "sort": "Tri personnalisé"}, "ollama": {"checker": {"desc": "Vérifiez si l'adresse du proxy est correctement saisie", "title": "Vérification de la connectivité"}, "customModelName": {"desc": "A<PERSON><PERSON>z un modèle personnalisé, séparez les modèles multiples par des virgules (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Nom du modèle personnal<PERSON>"}, "download": {"desc": "Ollama est en train de télécharger ce modèle, ve<PERSON><PERSON><PERSON> essayer de ne pas fermer cette page. Le téléchargement reprendra à l'endroit où il a été interrompu.", "failed": "Échec du téléchargement du modèle, veuillez vérifier votre connexion réseau ou les paramètres d'Ollama, puis rées<PERSON>ez", "remainingTime": "Temps restant", "speed": "Vitesse de téléchargement", "title": "Téléchargement du modèle {{model}} en cours"}, "endpoint": {"desc": "Doit inclure http(s)://, peut rester vide si non spécifié localement", "title": "<PERSON><PERSON><PERSON> du proxy"}, "title": "Ollama", "unlock": {"cancel": "Annuler le téléchargement", "confirm": "Télécharger", "description": "Entrez l'étiquette de votre modèle Ollama pour continuer la session.", "downloaded": "{{completed}} / {{total}}", "starting": "Début du téléchargement...", "title": "Télécharger le modèle Ollama spécifié"}}, "providerModels": {"config": {"aesGcm": "Votre clé et votre adresse de proxy seront chiffrées à l'aide de l'algorithme de chiffrement <1>AES-GCM</1>", "apiKey": {"desc": "Veuillez entrer votre {{name}} clé API", "descWithUrl": "Veuillez saisir votre clé API {{name}}, <3>cliquez ici pour l'obtenir</3>", "placeholder": "{{name}} clé API", "title": "Clé API"}, "baseURL": {"desc": "Doit inclure http(s)://", "invalid": "Veuillez entrer une URL valide", "placeholder": "https://your-proxy-url.com/v1", "title": "Adresse du proxy API"}, "checker": {"button": "Vérifier", "desc": "Tester si la clé API et l'adresse de proxy sont correctement renseignées", "pass": "Vérification réussie", "title": "Vérification de connectivité"}, "fetchOnClient": {"desc": "Le mode de requête client lancera directement la requête de session depuis le navigateur, ce qui peut améliorer la vitesse de réponse", "title": "Utiliser le mode de requête client"}, "helpDoc": "Guide de configuration", "responsesApi": {"desc": "Utilise la nouvelle norme de format de requête d'OpenAI, débloquant des fonctionnalités avancées telles que les chaînes de pensée", "title": "Utiliser la norme Responses API"}, "waitingForMore": "D'autres modèles sont en <1>planification d'intégration</1>, restez à l'écoute"}, "createNew": {"title": "C<PERSON>er un modèle AI personnalisé"}, "item": {"config": "Configurer le modèle", "customModelCards": {"addNew": "<PERSON><PERSON><PERSON> et ajouter le modèle {{id}}", "confirmDelete": "Vous allez supprimer ce modèle personnalisé, une fois supprimé, il ne pourra pas être récupéré, veuillez agir avec prudence."}, "delete": {"confirm": "Confirmer la suppression du modèle {{displayName}} ?", "success": "Suppression réussie", "title": "Supp<PERSON><PERSON> le modèle"}, "modelConfig": {"azureDeployName": {"extra": "Champ utilisé pour la demande réelle dans Azure OpenAI", "placeholder": "Veuillez entrer le nom de déploiement du modèle dans Azure", "title": "Nom de déploiement du modèle"}, "deployName": {"extra": "Ce champ sera utilisé comme ID de modèle lors de l'envoi de la demande", "placeholder": "Veuillez entrer le nom ou l'ID de déploiement réel du modèle", "title": "Nom de déploiement du modèle"}, "displayName": {"placeholder": "Veuillez entrer le nom d'affichage du modèle, par exemple ChatGPT, GPT-4, etc.", "title": "Nom d'affichage du modèle"}, "files": {"extra": "La mise en œuvre actuelle du téléchargement de fichiers n'est qu'une solution de contournement, à essayer à vos risques et périls. Veuillez attendre la mise en œuvre complète des capacités de téléchargement de fichiers.", "title": "Téléchargement de fichiers pris en charge"}, "functionCall": {"extra": "Cette configuration activera uniquement la capacité du modèle à utiliser des outils, permettant ainsi d'ajouter des plugins de type outil au modèle. Cependant, la prise en charge de l'utilisation réelle des outils dépend entièrement du modèle lui-même, veuil<PERSON><PERSON> tester la disponibilité par vous-même.", "title": "Support de l'utilisation des outils"}, "id": {"extra": "Une fois créé, il ne peut pas être modifié et sera utilisé comme identifiant du modèle lors de l'appel à l'IA", "placeholder": "Veuillez entrer l'identifiant du modèle, par exemple gpt-4o ou claude-3.5-sonnet", "title": "ID du modèle"}, "modalTitle": "Configuration du modèle personnalisé", "reasoning": {"extra": "Cette configuration activera uniquement la capacité de réflexion approfondie du modèle. Les résultats dépendent entièrement du modèle lui-même, veuil<PERSON><PERSON> tester si ce modèle possède une capacité de réflexion approfondie utilisable.", "title": "Support de la réflexion approfondie"}, "tokens": {"extra": "Définir le nombre maximal de tokens pris en charge par le modèle", "title": "Fenêtre de contexte maximale", "unlimited": "Illimité"}, "vision": {"extra": "Cette configuration n'activera que la configuration de téléchargement d'images dans l'application, la prise en charge de la reconnaissance dépend entièrement du modèle lui-même, veuillez tester la disponibilité des capacités de reconnaissance visuelle de ce modèle.", "title": "Reconnaissance visuelle prise en charge"}}, "pricing": {"image": "${{amount}}/image", "inputCharts": "${{amount}}/M caractères", "inputMinutes": "${{amount}}/minutes", "inputTokens": "Entrée ${{amount}}/M", "outputTokens": "Sortie ${{amount}}/M"}, "releasedAt": "<PERSON><PERSON><PERSON> {{releasedAt}}"}, "list": {"addNew": "Ajouter un modèle", "disabled": "Non activé", "disabledActions": {"showMore": "<PERSON><PERSON><PERSON><PERSON> tout"}, "empty": {"desc": "Veuillez créer un modèle personnalisé ou importer un modèle pour commencer à l'utiliser.", "title": "Aucun modèle disponible"}, "enabled": "Activé", "enabledActions": {"disableAll": "<PERSON><PERSON><PERSON><PERSON> tout", "enableAll": "Activer tout", "sort": "Trier les modèles personnalisés"}, "enabledEmpty": "Aucun modèle activé pour le moment, veuillez activer vos modèles préférés dans la liste ci-dessous~", "fetcher": {"clear": "Effacer les modèles récupérés", "fetch": "Récupérer la liste des modèles", "fetching": "Récupération de la liste des modèles en cours...", "latestTime": "<PERSON><PERSON><PERSON> mise à jour : {{time}}", "noLatestTime": "Aucune liste récupérée pour le moment"}, "resetAll": {"conform": "Êtes-vous sûr de vouloir réinitialiser toutes les modifications du modèle actuel ? Après la réinitialisation, la liste des modèles actuels reviendra à l'état par défaut", "success": "Réinitialisation réussie", "title": "Réinitialiser toutes les modifications"}, "search": "Rechercher des modèles...", "searchResult": "Trouvé {{count}} modèle(s)", "title": "Liste des modèles", "total": "Un total de {{count}} modèles disponibles"}, "searchNotFound": "Aucun résultat trouvé"}, "sortModal": {"success": "Mise à jour du tri réussie", "title": "Tri personnalisé", "update": "Mettre à jour"}, "updateAiProvider": {"confirmDelete": "Vous allez supprimer ce fournisseur AI, une fois supprimé, il ne pourra pas être récupéré, confirmez-vous la suppression ?", "deleteSuccess": "Suppression réussie", "tooltip": "Mettre à jour la configuration de base du fournisseur", "updateSuccess": "Mise à jour réussie"}, "updateCustomAiProvider": {"title": "Mettre à jour la configuration du fournisseur de services AI personnalisé"}, "vertexai": {"apiKey": {"desc": "Entrez vos clés Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Clés Vertex AI"}}, "zeroone": {"title": "01.<PERSON> Zéro Un Tout"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}