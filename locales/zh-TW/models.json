{"01-ai/yi-1.5-34b-chat": {"description": "零一萬物，最新開源微調模型，340億參數，微調支持多種對話場景，高質量訓練數據，對齊人類偏好。"}, "01-ai/yi-1.5-9b-chat": {"description": "零一萬物，最新開源微調模型，90億參數，微調支持多種對話場景，高質量訓練數據，對齊人類偏好。"}, "360/deepseek-r1": {"description": "【360部署版】DeepSeek-R1在後訓練階段大規模使用了強化學習技術，在僅有極少標註數據的情況下，極大提升了模型推理能力。在數學、程式碼、自然語言推理等任務上，性能比肩 OpenAI o1 正式版。"}, "360gpt-pro": {"description": "360GPT Pro 作為 360 AI 模型系列的重要成員，以高效的文本處理能力滿足多樣化的自然語言應用場景，支持長文本理解和多輪對話等功能。"}, "360gpt-pro-trans": {"description": "翻譯專用模型，深度微調優化，翻譯效果領先。"}, "360gpt-turbo": {"description": "360GPT Turbo 提供強大的計算和對話能力，具備出色的語義理解和生成效率，是企業和開發者理想的智能助理解決方案。"}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K 強調語義安全和責任導向，專為對內容安全有高度要求的應用場景設計，確保用戶體驗的準確性與穩健性。"}, "360gpt2-o1": {"description": "360gpt2-o1 使用樹搜索構建思維鏈，並引入了反思機制，使用強化學習訓練，模型具備自我反思與糾錯的能力。"}, "360gpt2-pro": {"description": "360GPT2 Pro 是 360 公司推出的高級自然語言處理模型，具備卓越的文本生成和理解能力，尤其在生成與創作領域表現出色，能夠處理複雜的語言轉換和角色演繹任務。"}, "360zhinao2-o1": {"description": "360zhinao2-o1 使用樹搜索構建思維鏈，並引入了反思機制，使用強化學習訓練，模型具備自我反思與糾錯的能力。"}, "4.0Ultra": {"description": "Spark4.0 Ultra 是星火大模型系列中最為強大的版本，在升級聯網搜索鏈路同時，提升對文本內容的理解和總結能力。它是用於提升辦公生產力和準確響應需求的全方位解決方案，是引領行業的智能產品。"}, "AnimeSharp": {"description": "AnimeSharp（又名 “4x‑AnimeSharp”） 是 Kim2091 基於 ESRGAN 架構開發的開源超解析度模型，專注於動漫風格圖像的放大與銳化。它於 2022 年 2 月由 “4x-TextSharpV1” 重命名而來，原本亦適用於文字圖像，但性能針對動漫內容進行了大幅優化。"}, "Baichuan2-Turbo": {"description": "採用搜索增強技術實現大模型與領域知識、全網知識的全面連結。支持PDF、Word等多種文檔上傳及網址輸入，信息獲取及時、全面，輸出結果準確、專業。"}, "Baichuan3-Turbo": {"description": "針對企業高頻場景優化，效果大幅提升，高性價比。相對於Baichuan2模型，內容創作提升20%，知識問答提升17%，角色扮演能力提升40%。整體效果比GPT3.5更優。"}, "Baichuan3-Turbo-128k": {"description": "具備 128K 超長上下文窗口，針對企業高頻場景優化，效果大幅提升，高性價比。相對於Baichuan2模型，內容創作提升20%，知識問答提升17%，角色扮演能力提升40%。整體效果比GPT3.5更優。"}, "Baichuan4": {"description": "模型能力國內第一，在知識百科、長文本、生成創作等中文任務上超越國外主流模型。還具備行業領先的多模態能力，多項權威評測基準表現優異。"}, "Baichuan4-Air": {"description": "模型能力國內第一，在知識百科、長文本、生成創作等中文任務上超越國外主流模型。還具備行業領先的多模態能力，多項權威評測基準表現優異。"}, "Baichuan4-Turbo": {"description": "模型能力國內第一，在知識百科、長文本、生成創作等中文任務上超越國外主流模型。還具備行業領先的多模態能力，多項權威評測基準表現優異。"}, "DeepSeek-R1": {"description": "最先進的高效 LLM，擅長推理、數學和程式設計。"}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1——DeepSeek 套件中更大更智能的模型——被蒸餾到 Llama 70B 架構中。基於基準測試和人工評估，該模型比原始 Llama 70B 更智能，尤其在需要數學和事實精確性的任務上表現出色。"}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "基於 Qwen2.5-Math-1.5B 的 DeepSeek-R1 蒸餾模型，通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆。"}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "基於 Qwen2.5-14B 的 DeepSeek-R1 蒸餾模型，通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆。"}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1 系列通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆，超越 OpenAI-o1-mini 水平。"}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "基於 Qwen2.5-Math-7B 的 DeepSeek-R1 蒸餾模型，通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆。"}, "DeepSeek-V3": {"description": "DeepSeek-V3 是一款由深度求索公司自研的MoE模型。DeepSeek-V3 多項評測成績超越了 Qwen2.5-72B 和 Llama-3.1-405B 等其他開源模型，並在性能上和世界頂尖的閉源模型 GPT-4o 以及 Claude-3.5-Sonnet 不分伯仲。"}, "Doubao-lite-128k": {"description": "Doubao-lite 擁有極致的響應速度，更好的性價比，為客戶不同場景提供更靈活的選擇。支持128k上下文視窗的推理和精調。"}, "Doubao-lite-32k": {"description": "Doubao-lite擁有極致的響應速度，更好的性價比，為客戶不同場景提供更靈活的選擇。支持32k上下文視窗的推理和精調。"}, "Doubao-lite-4k": {"description": "Doubao-lite擁有極致的響應速度，更好的性價比，為客戶不同場景提供更靈活的選擇。支持4k上下文視窗的推理和精調。"}, "Doubao-pro-128k": {"description": "效果最好的主力模型，適合處理複雜任務，在參考問答、總結摘要、創作、文本分類、角色扮演等場景都有很好的效果。支持128k上下文視窗的推理和精調。"}, "Doubao-pro-32k": {"description": "效果最好的主力模型，適合處理複雜任務，在參考問答、總結摘要、創作、文本分類、角色扮演等場景都有很好的效果。支持32k上下文視窗的推理和精調。"}, "Doubao-pro-4k": {"description": "效果最好的主力模型，適合處理複雜任務，在參考問答、總結摘要、創作、文本分類、角色扮演等場景都有很好的效果。支持4k上下文視窗的推理和精調。"}, "DreamO": {"description": "DreamO 是由字節跳動與北京大學聯合研發的開源圖像定制生成模型，旨在透過統一架構支持多任務圖像生成。它採用高效的組合建模方法，可根據用戶指定的身份、主體、風格、背景等多個條件生成高度一致且定制化的圖像。"}, "ERNIE-3.5-128K": {"description": "百度自研的旗艦級大規模語言模型，覆蓋海量中英文語料，具有強大的通用能力，可滿足絕大部分對話問答、創作生成、插件應用場景要求；支持自動對接百度搜索插件，保障問答信息時效。"}, "ERNIE-3.5-8K": {"description": "百度自研的旗艦級大規模語言模型，覆蓋海量中英文語料，具有強大的通用能力，可滿足絕大部分對話問答、創作生成、插件應用場景要求；支持自動對接百度搜索插件，保障問答信息時效。"}, "ERNIE-3.5-8K-Preview": {"description": "百度自研的旗艦級大規模語言模型，覆蓋海量中英文語料，具有強大的通用能力，可滿足絕大部分對話問答、創作生成、插件應用場景要求；支持自動對接百度搜索插件，保障問答信息時效。"}, "ERNIE-4.0-8K-Latest": {"description": "百度自研的旗艦級超大規模語言模型，相較ERNIE 3.5實現了模型能力全面升級，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。"}, "ERNIE-4.0-8K-Preview": {"description": "百度自研的旗艦級超大規模語言模型，相較ERNIE 3.5實現了模型能力全面升級，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。"}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "百度自研的旗艦級超大規模大語言模型，綜合效果表現優異，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。相較於 ERNIE 4.0 在性能表現上更為優秀。"}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "百度自研的旗艦級超大規模語言模型，綜合效果表現出色，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。相較於ERNIE 4.0在性能表現上更優秀。"}, "ERNIE-Character-8K": {"description": "百度自研的垂直場景大語言模型，適合遊戲NPC、客服對話、對話角色扮演等應用場景，人設風格更為鮮明、一致，指令遵循能力更強，推理性能更優。"}, "ERNIE-Lite-Pro-128K": {"description": "百度自研的輕量級大語言模型，兼顧優異的模型效果與推理性能，效果比ERNIE Lite更優，適合低算力AI加速卡推理使用。"}, "ERNIE-Speed-128K": {"description": "百度2024年最新發布的自研高性能大語言模型，通用能力優異，適合作為基座模型進行精調，更好地處理特定場景問題，同時具備極佳的推理性能。"}, "ERNIE-Speed-Pro-128K": {"description": "百度2024年最新發布的自研高性能大語言模型，通用能力優異，效果比ERNIE Speed更優，適合作為基座模型進行精調，更好地處理特定場景問題，同時具備極佳的推理性能。"}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev 是由 Black Forest Labs 開發的一款基於 Rectified Flow Transformer 架構的多模態圖像生成與編輯模型，擁有 12B（120 億）參數規模，專注於在給定上下文條件下生成、重構、增強或編輯圖像。該模型結合了擴散模型的可控生成優勢與 Transformer 的上下文建模能力，支持高品質圖像輸出，廣泛適用於圖像修復、圖像補全、視覺場景重構等任務。"}, "FLUX.1-dev": {"description": "FLUX.1-dev 是由 Black Forest Labs 開發的一款開源多模態語言模型（Multimodal Language Model, MLLM），專為圖文任務優化，融合了圖像和文本的理解與生成能力。它建立在先進的大語言模型（如 Mistral-7B）基礎上，透過精心設計的視覺編碼器與多階段指令微調，實現了圖文協同處理與複雜任務推理的能力。"}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) 是一種創新模型，適合多領域應用和複雜任務。"}, "HelloMeme": {"description": "HelloMeme 是一個可以根據你提供的圖片或動作，自動生成表情包、動圖或短影片的 AI 工具。它不需要你有任何繪畫或程式設計基礎，只需準備好參考圖片，它就能幫你做出好看、有趣、風格一致的內容。"}, "HiDream-I1-Full": {"description": "HiDream-E1-Full 是由智象未來（HiDream.ai）推出的一款開源多模態圖像編輯大模型，基於先進的 Diffusion Transformer 架構，並結合強大的語言理解能力（內嵌 LLaMA 3.1-8B-Instruct），支持透過自然語言指令進行圖像生成、風格遷移、局部編輯和內容重繪，具備出色的圖文理解與執行能力。"}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled 是一款輕量級的文生圖模型，經過蒸餾優化，能夠快速生成高品質的圖像，特別適用於低資源環境和即時生成任務。"}, "InstantCharacter": {"description": "InstantCharacter 是由騰訊 AI 團隊於 2025 年發布的一款無需微調（tuning-free）的個性化角色生成模型，旨在實現高保真、跨場景的一致角色生成。該模型支持僅基於一張參考圖像對角色進行建模，並能夠將該角色靈活遷移到各種風格、動作和背景中。"}, "InternVL2-8B": {"description": "InternVL2-8B 是一款強大的視覺語言模型，支持圖像與文本的多模態處理，能夠精確識別圖像內容並生成相關描述或回答。"}, "InternVL2.5-26B": {"description": "InternVL2.5-26B 是一款強大的視覺語言模型，支持圖像與文本的多模態處理，能夠精確識別圖像內容並生成相關描述或回答。"}, "Kolors": {"description": "Kolors 是由快手 Kolors 團隊開發的文生圖模型。由數十億的參數訓練，在視覺品質、中文語義理解和文本渲染方面有顯著優勢。"}, "Kwai-Kolors/Kolors": {"description": "Kolors 是由快手 Kolors 團隊開發的基於潛在擴散的大規模文本到圖像生成模型。該模型透過數十億文本-圖像對的訓練，在視覺品質、複雜語義準確性以及中英文字元渲染方面展現出顯著優勢。它不僅支持中英文輸入，在理解和生成中文特定內容方面也表現出色。"}, "Llama-3.2-11B-Vision-Instruct": {"description": "在高解析度圖像上表現出色的圖像推理能力，適用於視覺理解應用。"}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "適用於視覺理解代理應用的高級圖像推理能力。"}, "Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1指令調優的文本模型，針對多語言對話用例進行了優化，在許多可用的開源和封閉聊天模型中，在常見行業基準上表現優異。"}, "Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1指令調優的文本模型，針對多語言對話用例進行了優化，在許多可用的開源和封閉聊天模型中，在常見行業基準上表現優異。"}, "Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1指令調優的文本模型，針對多語言對話用例進行了優化，在許多可用的開源和封閉聊天模型中，在常見行業基準上表現優異。"}, "Meta-Llama-3.2-1B-Instruct": {"description": "先進的最尖端小型語言模型，具備語言理解、卓越的推理能力和文本生成能力。"}, "Meta-Llama-3.2-3B-Instruct": {"description": "先進的最尖端小型語言模型，具備語言理解、卓越的推理能力和文本生成能力。"}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 是 Llama 系列最先進的多語言開源大型語言模型，以極低成本體驗媲美 405B 模型的性能。基於 Transformer 結構，並透過監督微調（SFT）和人類反饋強化學習（RLHF）提升有用性和安全性。其指令調優版本專為多語言對話優化，在多項行業基準上表現優於眾多開源和封閉聊天模型。知識截止日期為 2023 年 12 月"}, "MiniMax-M1": {"description": "全新自研推理模型。全球領先：80K思維鏈 x 1M輸入，效果比肩海外頂尖模型。"}, "MiniMax-Text-01": {"description": "在 MiniMax-01系列模型中，我們做了大膽創新：首次大規模實現線性注意力機制，傳統 Transformer架構不再是唯一的選擇。這個模型的參數量高達4560億，其中單次激活459億。模型綜合性能比肩海外頂尖模型，同時能夠高效處理全球最長400萬token的上下文，是GPT-4o的32倍，Claude-3.5-Sonnet的20倍。"}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 是開源權重的大規模混合注意力推理模型，擁有 4560 億參數，每個 Token 可激活約 459 億參數。模型原生支援 100 萬 Token 的超長上下文，並透過閃電注意力機制，在 10 萬 Token 的生成任務中相比 DeepSeek R1 節省 75% 的浮點運算量。同時，MiniMax-M1 採用 MoE（混合專家）架構，結合 CISPO 演算法與混合注意力設計的高效強化學習訓練，在長輸入推理與真實軟體工程場景中實現了業界領先的性能。"}, "Moonshot-Kimi-K2-Instruct": {"description": "總參數 1T，激活參數 32B。非思維模型中，在前沿知識、數學和編碼方面達到頂尖水平，更擅長通用 Agent 任務。針對代理任務進行了精心優化，不僅能回答問題，還能採取行動。最適用於即興、通用聊天和代理體驗，是一款無需長時間思考的反射級模型。"}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) 是高精度的指令模型，適用於複雜計算。"}, "OmniConsistency": {"description": "OmniConsistency 透過引入大規模 Diffusion Transformers（DiTs）和配對風格化資料，提升圖像到圖像（Image-to-Image）任務中的風格一致性和泛化能力，避免風格退化。"}, "Phi-3-medium-128k-instruct": {"description": "相同的Phi-3-medium模型，但具有更大的上下文大小，適用於RAG或少量提示。"}, "Phi-3-medium-4k-instruct": {"description": "一個14B參數模型，質量優於Phi-3-mini，專注於高質量、推理密集型數據。"}, "Phi-3-mini-128k-instruct": {"description": "相同的Phi-3-mini模型，但具有更大的上下文大小，適用於RAG或少量提示。"}, "Phi-3-mini-4k-instruct": {"description": "Phi-3系列中最小的成員。優化了質量和低延遲。"}, "Phi-3-small-128k-instruct": {"description": "相同的Phi-3-small模型，但具有更大的上下文大小，適用於RAG或少量提示。"}, "Phi-3-small-8k-instruct": {"description": "一個7B參數模型，質量優於Phi-3-mini，專注於高質量、推理密集型數據。"}, "Phi-3.5-mini-instruct": {"description": "Phi-3-mini模型的更新版。"}, "Phi-3.5-vision-instrust": {"description": "Phi-3-vision模型的更新版。"}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct 是 Qwen2 系列中的指令微調大語言模型，參數規模為 7B。該模型基於 Transformer 架構，採用了 SwiGLU 激活函數、注意力 QKV 偏置和組查詢注意力等技術。它能夠處理大規模輸入。該模型在語言理解、生成、多語言能力、編碼、數學和推理等多個基準測試中表現出色，超越了大多數開源模型，並在某些任務上展現出與專有模型相當的競爭力。Qwen2-7B-Instruct 在多項評測中均優於 Qwen1.5-7B-Chat，顯示出顯著的性能提升"}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct 是阿里雲發布的最新大語言模型系列之一。該 7B 模型在編碼和數學等領域具有顯著改進的能力。該模型還提供了多語言支持，覆蓋超過 29 種語言，包括中文、英文等。模型在指令跟隨、理解結構化數據以及生成結構化輸出（尤其是 JSON）方面都有顯著提升"}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct 是阿里雲發布的代碼特定大語言模型系列的最新版本。該模型在 Qwen2.5 的基礎上，通過 5.5 萬億個 tokens 的訓練，顯著提升了代碼生成、推理和修復能力。它不僅增強了編碼能力，還保持了數學和通用能力的優勢。模型為代碼智能體等實際應用提供了更全面的基礎"}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL 是 Qwen 系列的新成員，具備強大的視覺理解能力，能分析圖像中的文字、圖表和版面配置，並能理解長影片和捕捉事件。它可以進行推理、操作工具，支援多格式物件定位和生成結構化輸出，優化了影片理解的動態解析度與影格率訓練，並提升了視覺編碼器效率。"}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking 是由智譜 AI 和清華大學 KEG 實驗室聯合發布的一款開源視覺語言模型（VLM），專為處理複雜的多模態認知任務而設計。該模型基於 GLM-4-9B-0414 基礎模型，通過引入「思維鏈」（Chain-of-Thought）推理機制和採用強化學習策略，顯著提升了其跨模態的推理能力和穩定性。"}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat 是智譜 AI 推出的 GLM-4 系列預訓練模型中的開源版本。該模型在語義、數學、推理、代碼和知識等多個方面表現出色。除了支持多輪對話外，GLM-4-9B-Chat 還具備網頁瀏覽、代碼執行、自定義工具調用（Function Call）和長文本推理等高級功能。模型支持 26 種語言，包括中文、英文、日文、韓文和德文等。在多項基準測試中，GLM-4-9B-Chat 展現了優秀的性能，如 AlignBench-v2、MT-Bench、MMLU 和 C-Eval 等。該模型支持最大 128K 的上下文長度，適用於學術研究和商業應用"}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 是一款強化學習（RL）驅動的推理模型，解決了模型中的重複性和可讀性問題。在 RL 之前，DeepSeek-R1 引入了冷啟動數據，進一步優化了推理性能。它在數學、代碼和推理任務中與 OpenAI-o1 表現相當，並且透過精心設計的訓練方法，提升了整體效果。"}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B 是基於 Qwen2.5-Math-7B 透過知識蒸餾技術所獲得的模型。該模型使用 DeepSeek-R1 生成的 80 萬個精選樣本進行微調，展現出優異的推理能力。在多個基準測試中表現出色，其中在 MATH-500 上達到了 92.8% 的準確率，在 AIME 2024 上達到了 55.5% 的通過率，在 CodeForces 上獲得了 1189 的評分，作為 7B 規模的模型展示了較強的數學和程式設計能力。"}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 是一款擁有 6710 億參數的混合專家（MoE）語言模型，採用多頭潛在注意力（MLA）和 DeepSeekMoE 架構，結合無輔助損失的負載平衡策略，優化推理和訓練效率。透過在 14.8 萬億高質量tokens上預訓練，並進行監督微調和強化學習，DeepSeek-V3 在性能上超越其他開源模型，接近領先閉源模型。"}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 是一款具備超強程式碼和 Agent 能力的 MoE 架構基礎模型，總參數 1T，激活參數 32B。在通用知識推理、程式設計、數學、Agent 等主要類別的基準性能測試中，K2 模型的性能超過其他主流開源模型。"}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview 是一款獨具創新的自然語言處理模型，能夠高效處理複雜的對話生成與上下文理解任務。"}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview 是由 Qwen 團隊開發的專注於視覺推理能力的研究型模型，其在複雜場景理解和解決視覺相關的數學問題方面具有獨特優勢。"}, "Qwen/QwQ-32B": {"description": "QwQ 是 Qwen 系列的推理模型。與傳統的指令調優模型相比，QwQ 具備思考和推理能力，能夠在下游任務中實現顯著增強的性能，尤其是在解決困難問題方面。QwQ-32B 是中型推理模型，能夠在與最先進的推理模型（如 DeepSeek-R1、o1-mini）的對比中取得有競爭力的性能。該模型採用 RoPE、SwiGLU、RMSNorm 和 Attention QKV bias 等技術，具有 64 層網絡結構和 40 個 Q 注意力頭（GQA 架構中 KV 為 8 個）。"}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview是Qwen 最新的實驗性研究模型，專注於提升AI推理能力。通過探索語言混合、遞歸推理等複雜機制，主要優勢包括強大的推理分析能力、數學和編程能力。與此同時，也存在語言切換問題、推理循環、安全性考量、其他能力方面的差異。"}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 是先進的通用語言模型，支持多種指令類型。"}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct 是 Qwen2 系列中的指令微調大語言模型，參數規模為 72B。該模型基於 Transformer 架構，採用了 SwiGLU 激活函數、注意力 QKV 偏置和組查詢注意力等技術。它能夠處理大規模輸入。該模型在語言理解、生成、多語言能力、編碼、數學和推理等多個基準測試中表現出色，超越了大多數開源模型，並在某些任務上展現出與專有模型相當的競爭力"}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL 是 Qwen-VL 模型的最新迭代版本，在視覺理解基準測試中達到了最先進的性能。"}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5是全新的大型語言模型系列，旨在優化指令式任務的處理。"}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5是全新的大型語言模型系列，旨在優化指令式任務的處理。"}, "Qwen/Qwen2.5-72B-Instruct": {"description": "阿里雲通義千問團隊開發的大型語言模型"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 是全新的大型語言模型系列，具有更強的理解和生成能力。"}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 是全新的大型語言模型系列，旨在優化指令式任務的處理。"}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5是全新的大型語言模型系列，旨在優化指令式任務的處理。"}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 是全新的大型語言模型系列，旨在優化指令式任務的處理。"}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder 專注於代碼編寫。"}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct 是阿里雲發布的代碼特定大語言模型系列的最新版本。該模型在 Qwen2.5 的基礎上，通過 5.5 萬億個 tokens 的訓練，顯著提升了代碼生成、推理和修復能力。它不僅增強了編碼能力，還保持了數學和通用能力的優勢。模型為代碼智能體等實際應用提供了更全面的基礎"}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct 是通義千問團隊推出的多模態大型模型，屬於 Qwen2.5-VL 系列的一部分。該模型不僅能精準識別常見物體，更能分析圖像中的文字、圖表、圖標、圖形與版面配置。它可作為視覺智能代理，具備推理能力並能動態操作工具，擁有使用電腦與手機的實用功能。此外，此模型能精確定位圖像中的物件，並為發票、表格等文件生成結構化輸出。相較於前代模型 Qwen2-VL，此版本透過強化學習在數學與問題解決能力方面獲得顯著提升，回應風格也更貼近人類偏好。"}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL 是 Qwen2.5 系列中的視覺語言模型。該模型在多方面有顯著提升：具備更強的視覺理解能力，能夠識別常見物體、分析文本、圖表和版面配置；作為視覺代理能夠推理並動態指導工具使用；支援理解超過 1 小時的長影片並捕捉關鍵事件；能夠透過生成邊界框或點準確定位圖像中的物體；支援生成結構化輸出，尤其適用於發票、表格等掃描資料。"}, "Qwen/Qwen3-14B": {"description": "Qwen3 是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent 和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent 和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 是由阿里雲通義千問團隊開發的 Qwen3 系列中的一款旗艦級混合專家（MoE）大語言模型。該模型擁有 2350 億總參數，每次推理激活 220 億參數。它是作為 Qwen3-235B-A22B 非思考模式的更新版本發布的，專注於在指令遵循、邏輯推理、文本理解、數學、科學、程式設計及工具使用等通用能力上實現顯著提升。此外，模型增強了對多語言長尾知識的覆蓋，並能更好地對齊用戶在主觀和開放性任務上的偏好，以生成更有幫助和更高品質的文本。"}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 是由阿里巴巴通義千問團隊開發的 Qwen3 系列大型語言模型中的一員，專注於高難度的複雜推理任務。該模型基於混合專家（MoE）架構，總參數量達 2350 億，而在處理每個 token 時僅激活約 220 億參數，從而在保持強大性能的同時提高了計算效率。作為一個專門的“思考”模型，它在邏輯推理、數學、科學、程式設計和學術基準測試等需要人類專業知識的任務上表現顯著提升，達到了開源思考模型中的頂尖水平。此外，模型還增強了通用能力，如指令遵循、工具使用和文本生成，並原生支持 256K 的長上下文理解能力，非常適合用於需要深度推理和處理長文件的場景。"}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent 和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 是 Qwen3-30B-A3B 非思考模式的更新版本。這是一個擁有 305 億總參數和 33 億激活參數的混合專家（MoE）模型。該模型在多個方面進行了關鍵增強，包括顯著提升了指令遵循、邏輯推理、文本理解、數學、科學、編碼和工具使用等通用能力。同時，它在多語言的長尾知識覆蓋範圍上取得了實質性進展，並能更好地與用戶在主觀和開放式任務中的偏好對齊，從而能夠生成更有幫助的回覆和更高品質的文本。此外，該模型的長文本理解能力也增強到了 256K。此模型僅支援非思考模式，其輸出中不會生成 `<think></think>` 標籤。"}, "Qwen/Qwen3-32B": {"description": "Qwen3 是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent 和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "Qwen/Qwen3-8B": {"description": "Qwen3 是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent 和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "Qwen2-72B-Instruct": {"description": "Qwen2 是 Qwen 模型的最新系列，支持 128k 上下文，對比當前最優的開源模型，Qwen2-72B 在自然語言理解、知識、代碼、數學及多語言等多項能力上均顯著超越當前領先的模型。"}, "Qwen2-7B-Instruct": {"description": "Qwen2 是 Qwen 模型的最新系列，能夠超越同等規模的最優開源模型甚至更大規模的模型，Qwen2 7B 在多個評測上取得顯著的優勢，尤其是在代碼及中文理解上。"}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B是一款強大的視覺語言模型，支持圖像與文本的多模態處理，能夠精確識別圖像內容並生成相關描述或回答。"}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct 是一款140億參數的大語言模型，性能表現優秀，優化中文和多語言場景，支持智能問答、內容生成等應用。"}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct 是一款320億參數的大語言模型，性能表現均衡，優化中文和多語言場景，支持智能問答、內容生成等應用。"}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct 支持 16k 上下文，生成長文本超過 8K。支持 function call 與外部系統無縫互動，極大提升了靈活性和擴展性。模型知識明顯增加，並且大幅提高了編碼和數學能力，多語言支持超過 29 種。"}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct 是一款70億參數的大語言模型，支持函數調用與外部系統無縫互動，極大提升了靈活性和擴展性。優化中文和多語言場景，支持智能問答、內容生成等應用。"}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct 是一款基於大規模預訓練的程式指令模型，具備強大的程式理解和生成能力，能夠高效地處理各種程式任務，特別適合智能程式編寫、自動化腳本生成和程式問題解答。"}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct 是一款專為代碼生成、代碼理解和高效開發場景設計的大型語言模型，採用了業界領先的32B參數規模，能夠滿足多樣化的程式需求。"}, "Qwen3-235B": {"description": "Qwen3-235B-A22B，MoE（混合專家模型）模型，引入了「混合推理模式」，支援用戶在「思考模式」和「非思考模式」之間無縫切換，支援119種語言和方言理解與推理，並具備強大的工具調用能力，在綜合能力、程式碼與數學、多語言能力、知識與推理等多項基準測試中，都能與DeepSeek R1、OpenAI o1、o3-mini、Grok 3和谷歌Gemini 2.5 Pro等目前市場上的主流大型模型競爭。"}, "Qwen3-32B": {"description": "Qwen3-32B，稠密模型（Dense Model），引入了「混合推理模式」，支援用戶在「思考模式」和「非思考模式」之間無縫切換，由於模型架構改進、訓練資料增加以及更有效的訓練方法，整體性能與Qwen2.5-72B表現相當。"}, "SenseChat": {"description": "基礎版本模型 (V4)，4K上下文長度，通用能力強大"}, "SenseChat-128K": {"description": "基礎版本模型 (V4)，128K上下文長度，在長文本理解及生成等任務中表現出色"}, "SenseChat-32K": {"description": "基礎版本模型 (V4)，32K上下文長度，靈活應用於各類場景"}, "SenseChat-5": {"description": "最新版本模型 (V5.5)，128K上下文長度，在數學推理、英文對話、指令跟隨以及長文本理解等領域能力顯著提升，比肩GPT-4o"}, "SenseChat-5-1202": {"description": "是基於 V5.5 的最新版本，較上版本在中英文基礎能力、聊天、理科知識、文科知識、寫作、數理邏輯、字數控制等幾個維度的表現有顯著提升。"}, "SenseChat-5-Cantonese": {"description": "32K上下文長度，在粵語的對話理解上超越了GPT-4，在知識、推理、數學及程式編寫等多個領域均能與GPT-4 Turbo相媲美"}, "SenseChat-5-beta": {"description": "部分性能優於 SenseCat-5-1202"}, "SenseChat-Character": {"description": "標準版模型，8K上下文長度，高響應速度"}, "SenseChat-Character-Pro": {"description": "高級版模型，32K上下文長度，能力全面提升，支持中/英文對話"}, "SenseChat-Turbo": {"description": "適用於快速問答、模型微調場景"}, "SenseChat-Turbo-1202": {"description": "是最新的輕量版本模型，達到全量模型90%以上能力，顯著降低推理成本。"}, "SenseChat-Vision": {"description": "最新版本模型 (V5.5)，支持多圖的輸入，全面實現模型基礎能力優化，在對象屬性識別、空間關係、動作事件識別、場景理解、情感識別、邏輯常識推理和文本理解生成上都實現了較大提升。"}, "SenseNova-V6-5-Pro": {"description": "透過對多模態、語言及推理資料的全面更新與訓練策略的優化，新模型在多模態推理和泛化指令跟隨能力上實現了顯著提升，支援高達128k的上下文視窗，並在OCR與文旅IP識別等專項任務中表現卓越。"}, "SenseNova-V6-5-Turbo": {"description": "透過對多模態、語言及推理資料的全面更新與訓練策略的優化，新模型在多模態推理和泛化指令跟隨能力上實現了顯著提升，支援高達128k的上下文視窗，並在OCR與文旅IP識別等專項任務中表現卓越。"}, "SenseNova-V6-Pro": {"description": "實現圖片、文本、視頻能力的原生統一，突破傳統多模態分立局限，在OpenCompass和SuperCLUE評測中斬獲雙冠軍。"}, "SenseNova-V6-Reasoner": {"description": "兼顧視覺、語言深度推理，實現慢思考和深度推理，呈現完整的思維鏈過程。"}, "SenseNova-V6-Turbo": {"description": "實現圖片、文本、視頻能力的原生統一，突破傳統多模態分立局限，在多模基礎能力、語言基礎能力等核心維度全面領先，文理兼修，在多項測評中多次位列國內外第一梯隊水平。"}, "Skylark2-lite-8k": {"description": "雲雀（Skylark）第二代模型，Skylark2-lite 模型有較高的回應速度，適用於實時性要求高、成本敏感、對模型精度要求不高的場景，上下文窗口長度為 8k。"}, "Skylark2-pro-32k": {"description": "雲雀（Skylark）第二代模型，Skylark2-pro 版本有較高的模型精度，適用於較為複雜的文本生成場景，如專業領域文案生成、小說創作、高品質翻譯等，上下文窗口長度為 32k。"}, "Skylark2-pro-4k": {"description": "雲雀（Skylark）第二代模型，Skylark2-pro 模型有較高的模型精度，適用於較為複雜的文本生成場景，如專業領域文案生成、小說創作、高品質翻譯等，上下文窗口長度為 4k。"}, "Skylark2-pro-character-4k": {"description": "雲雀（Skylark）第二代模型，Skylark2-pro-character 模型具有優秀的角色扮演和聊天能力，擅長根據用戶 prompt 要求扮演不同角色與用戶展開聊天，角色風格突出，對話內容自然流暢，適用於構建聊天機器人、虛擬助手和在線客服等場景，有較高的回應速度。"}, "Skylark2-pro-turbo-8k": {"description": "雲雀（Skylark）第二代模型，Skylark2-pro-turbo-8k 推理更快，成本更低，上下文窗口長度為 8k。"}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 是 GLM 系列的新一代開源模型，擁有 320 億參數。該模型性能可與 OpenAI 的 GPT 系列和 DeepSeek 的 V3/R1 系列相媲美。"}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 是 GLM 系列的小型模型，擁有 90 億參數。該模型繼承了 GLM-4-32B 系列的技術特點，但提供了更輕量級的部署選擇。儘管規模較小，GLM-4-9B-0414 仍在程式碼生成、網頁設計、SVG 圖形生成和基於搜索的寫作等任務上展現出色能力。"}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking 是由智譜 AI 和清華大學 KEG 實驗室聯合發布的一款開源視覺語言模型（VLM），專為處理複雜的多模態認知任務而設計。該模型基於 GLM-4-9B-0414 基礎模型，通過引入「思維鏈」（Chain-of-Thought）推理機制和採用強化學習策略，顯著提升了其跨模態的推理能力和穩定性。"}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 是一個具有深度思考能力的推理模型。該模型基於 GLM-4-32B-0414 通過冷啟動和擴展強化學習開發，並在數學、程式碼和邏輯任務上進行了進一步訓練。與基礎模型相比，GLM-Z1-32B-0414 顯著提升了數學能力和解決複雜任務的能力。"}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 是 GLM 系列的小型模型，僅有 90 億參數，但保持了開源傳統的同時展現出驚人的能力。儘管規模較小，該模型在數學推理和通用任務上仍表現出色，其整體性能在同等規模的開源模型中已處於領先水平。"}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 是一個具有沉思能力的深度推理模型（與 OpenAI 的 Deep Research 對標）。與典型的深度思考模型不同，沉思模型採用更長時間的深度思考來解決更開放和複雜的問題。"}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B 開放源碼版本，為會話應用提供優化後的對話體驗。"}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B 是首個使用強化學習訓練的長上下文大型推理模型（LRM），專門針對長文本推理任務進行優化。該模型透過漸進式上下文擴展的強化學習框架，實現了從短上下文到長上下文的穩定遷移。在七個長上下文文件問答基準測試中，QwenLong-L1-32B 超越了 OpenAI-o3-mini 和 Qwen3-235B-A22B 等旗艦模型，性能可媲美 Claude-3.7-Sonnet-Thinking。該模型特別擅長數學推理、邏輯推理和多跳推理等複雜任務。"}, "Yi-34B-Chat": {"description": "Yi-1.5-34B 在保持原系列模型優秀的通用語言能力的前提下，通過增量訓練 5 千億高質量 token，大幅提高了數學邏輯和代碼能力。"}, "abab5.5-chat": {"description": "面向生產力場景，支持複雜任務處理和高效文本生成，適用於專業領域應用。"}, "abab5.5s-chat": {"description": "專為中文人設對話場景設計，提供高質量的中文對話生成能力，適用於多種應用場景。"}, "abab6.5g-chat": {"description": "專為多語種人設對話設計，支持英文及其他多種語言的高質量對話生成。"}, "abab6.5s-chat": {"description": "適用於廣泛的自然語言處理任務，包括文本生成、對話系統等。"}, "abab6.5t-chat": {"description": "針對中文人設對話場景優化，提供流暢且符合中文表達習慣的對話生成能力。"}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 是一款最先進的大型語言模型，經過強化學習和冷啟動數據的優化，具有出色的推理、數學和編程性能。"}, "accounts/fireworks/models/deepseek-v3": {"description": "Deepseek 提供的強大 Mixture-of-Experts (MoE) 語言模型，總參數量為 671B，每個標記激活 37B 參數。"}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Llama 3 70B 指令模型，專為多語言對話和自然語言理解優化，性能優於多數競爭模型。"}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Llama 3 8B 指令模型，優化用於對話及多語言任務，表現卓越且高效。"}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Llama 3 8B 指令模型（HF 版本），與官方實現結果一致，具備高度一致性和跨平台兼容性。"}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Llama 3.1 405B 指令模型，具備超大規模參數，適合複雜任務和高負載場景下的指令跟隨。"}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Llama 3.1 70B 指令模型，提供卓越的自然語言理解和生成能力，是對話及分析任務的理想選擇。"}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Llama 3.1 8B 指令模型，專為多語言對話優化，能夠在常見行業基準上超越多數開源及閉源模型。"}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Meta的11B參數指令調整圖像推理模型。該模型針對視覺識別、圖像推理、圖像描述和回答關於圖像的一般性問題進行了優化。該模型能夠理解視覺數據，如圖表和圖形，並通過生成文本描述圖像細節來弥合視覺與語言之間的差距。"}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B 指令模型是Meta推出的一款輕量級多語言模型。該模型旨在提高效率，與更大型的模型相比，在延遲和成本方面提供了顯著的改進。該模型的示例用例包括查詢和提示重寫以及寫作輔助。"}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Meta的90B參數指令調整圖像推理模型。該模型針對視覺識別、圖像推理、圖像描述和回答關於圖像的一般性問題進行了優化。該模型能夠理解視覺數據，如圖表和圖形，並通過生成文本描述圖像細節來弥合視覺與語言之間的差距。"}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct 是 Llama 3.1 70B 的 12 月更新版本。該模型在 Llama 3.1 70B（於 2024 年 7 月發布）的基礎上進行了改進，增強了工具調用、多語言文本支持、數學和編程能力。該模型在推理、數學和指令遵循方面達到了行業領先水平，並且能夠提供與 3.1 405B 相似的性能，同時在速度和成本上具有顯著優勢。"}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "24B 參數模型，具備與更大型模型相當的最先進能力。"}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B 指令模型，大規模參數和多專家架構，全方位支持複雜任務的高效處理。"}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B 指令模型，多專家架構提供高效的指令跟隨及執行。"}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMax L2 13B 模型，結合新穎的合併技術，擅長敘事和角色扮演。"}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision 指令模型，輕量級多模態模型，能夠處理複雜的視覺和文本信息，具備較強的推理能力。"}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "QwQ模型是由 Qwen 團隊開發的實驗性研究模型，專注於增強 AI 推理能力。"}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Qwen-VL 模型的 72B 版本是阿里巴巴最新迭代的成果，代表了近一年的創新。"}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 是由阿里雲 Qwen 團隊開發的一系列僅包含解碼器的語言模型。這些模型提供不同的大小，包括 0.5B、1.5B、3B、7B、14B、32B 和 72B，並且有基礎版（base）和指令版（instruct）兩種變體。"}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct 是阿里雲發布的代碼特定大語言模型系列的最新版本。該模型在 Qwen2.5 的基礎上，通過 5.5 萬億個 tokens 的訓練，顯著提升了代碼生成、推理和修復能力。它不僅增強了編碼能力，還保持了數學和通用能力的優勢。模型為代碼智能體等實際應用提供了更全面的基礎"}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Large 模型，具備卓越的多語言處理能力，可用於各類語言生成和理解任務。"}, "ai21-jamba-1.5-large": {"description": "一個398B參數（94B活躍）多語言模型，提供256K長上下文窗口、函數調用、結構化輸出和基於實體的生成。"}, "ai21-jamba-1.5-mini": {"description": "一個52B參數（12B活躍）多語言模型，提供256K長上下文窗口、函數調用、結構化輸出和基於實體的生成。"}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "一個 398B 參數（94B 活躍）的多語言模型，提供 256K 長上下文視窗、函數呼叫、結構化輸出和基於事實的生成。"}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "一個 52B 參數（12B 活躍）的多語言模型，提供 256K 長上下文視窗、函數呼叫、結構化輸出和基於事實的生成。"}, "anthropic.claude-3-5-sonnet-********-v1:0": {"description": "Claude 3.5 Sonnet提升了行業標準，性能超過競爭對手模型和Claude 3 Opus，在廣泛的評估中表現出色，同時具有我們中等層級模型的速度和成本。"}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet 提升了行業標準，性能超越競爭對手模型和 Claude 3 Opus，在廣泛的評估中表現出色，同時具備我們中等層級模型的速度和成本。"}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku是Anthropic最快、最緊湊的模型，提供近乎即時的響應速度。它可以快速回答簡單的查詢和請求。客戶將能夠構建模仿人類互動的無縫AI體驗。Claude 3 Haiku可以處理圖像並返回文本輸出，具有200K的上下文窗口。"}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus是Anthropic最強大的AI模型，具有在高度複雜任務上的最先進性能。它可以處理開放式提示和未見過的場景，具有出色的流暢性和類人的理解能力。Claude 3 Opus展示了生成AI可能性的前沿。Claude 3 Opus可以處理圖像並返回文本輸出，具有200K的上下文窗口。"}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Anthropic的Claude 3 Sonnet在智能和速度之間達到了理想的平衡——特別適合企業工作負載。它以低於競爭對手的價格提供最大的效用，並被設計成為可靠的、高耐用的主力機，適用於規模化的AI部署。Claude 3 Sonnet可以處理圖像並返回文本輸出，具有200K的上下文窗口。"}, "anthropic.claude-instant-v1": {"description": "一款快速、經濟且仍然非常有能力的模型，可以處理包括日常對話、文本分析、總結和文檔問答在內的一系列任務。"}, "anthropic.claude-v2": {"description": "Anthropic在從複雜對話和創意內容生成到詳細指令跟隨的廣泛任務中都表現出高度能力的模型。"}, "anthropic.claude-v2:1": {"description": "Claude 2的更新版，具有雙倍的上下文窗口，以及在長文檔和RAG上下文中的可靠性、幻覺率和基於證據的準確性的改進。"}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku 是 Anthropic 的最快且最緊湊的模型，旨在實現近乎即時的響應。它具有快速且準確的定向性能。"}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus 是 Anthropic 用於處理高度複雜任務的最強大模型。它在性能、智能、流暢性和理解力方面表現卓越。"}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku 是 Anthropic 最快的下一代模型。與 Claude 3 Haiku 相比，Claude 3.5 Haiku 在各項技能上都有所提升，並在許多智力基準測試中超越了上一代最大的模型 Claude 3 Opus。"}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet 提供了超越 Opus 的能力和比 Sonnet 更快的速度，同時保持與 Sonnet 相同的價格。Sonnet 特別擅長程式設計、數據科學、視覺處理、代理任務。"}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet 是 Anthropic 迄今為止最智能的模型，也是市場上首個混合推理模型。Claude 3.7 Sonnet 可以產生近乎即時的回應或延長的逐步思考，使用者可以清晰地看到這些過程。Sonnet 特別擅長程式設計、數據科學、視覺處理、代理任務。"}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 是 Anthropic 用於處理高度複雜任務的最強大模型。它在性能、智慧、流暢性和理解力方面表現卓越。"}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 可以產生近乎即時的回應或延長的逐步思考，使用者可以清楚地看到這些過程。API 使用者還可以對模型思考的時間進行細緻的控制。"}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B 是一款 720 億參數、激活 160 億參的稀疏大型語言模型，它基於分組混合專家（MoGE）架構，它在專家選擇階段對專家進行分組，並約束 token 在每個組內激活等量專家，從而實現專家負載均衡，顯著提升模型在昇騰平台的部署效率。"}, "aya": {"description": "Aya 23 是 Cohere 推出的多語言模型，支持 23 種語言，為多元化語言應用提供便利。"}, "aya:35b": {"description": "Aya 23 是 Cohere 推出的多語言模型，支持 23 種語言，為多元化語言應用提供便利。"}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B百川智能開發的包含130億參數的開源可商用的大規模語言模型，在權威的中文和英文benchmark上均取得同尺寸最好的效果。"}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B 是由百度公司開發的一款基於混合專家（MoE）架構的大型語言模型。該模型總參數量為 3000 億，但在推理時每個 token 僅激活 470 億參數，從而在保證強大性能的同時兼顧了計算效率。作為 ERNIE 4.5 系列的核心模型之一，在文本理解、生成、推理和程式設計等任務上展現出卓越的能力。該模型採用了一種創新的多模態異構 MoE 預訓練方法，通過文本與視覺模態的聯合訓練，有效提升了模型的綜合能力，尤其在指令遵循和世界知識記憶方面效果突出。"}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse 是一款高性能的 32B 多語言模型，旨在通過指令調優、數據套利、偏好訓練和模型合併的創新，挑戰單語言模型的表現。它支持 23 種語言。"}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse 是一款高性能的 8B 多語言模型，旨在通過指令調優、數據套利、偏好訓練和模型合併的創新，挑戰單語言模型的表現。它支持 23 種語言。"}, "c4ai-aya-vision-32b": {"description": "Aya Vision 是一款最先進的多模態模型，在語言、文本和圖像能力的多個關鍵基準上表現出色。它支持 23 種語言。這個 320 億參數的版本專注於最先進的多語言表現。"}, "c4ai-aya-vision-8b": {"description": "Aya Vision 是一款最先進的多模態模型，在語言、文本和圖像能力的多個關鍵基準上表現出色。這個 80 億參數的版本專注於低延遲和最佳性能。"}, "charglm-3": {"description": "CharGLM-3專為角色扮演與情感陪伴設計，支持超長多輪記憶與個性化對話，應用廣泛。"}, "charglm-4": {"description": "CharGLM-4 專為角色扮演與情感陪伴設計，支持超長多輪記憶與個性化對話，應用廣泛。"}, "chatglm3": {"description": "ChatGLM3 是智譜 AI 與清華 KEG 實驗室發佈的閉源模型，經過海量中英標識符的預訓練與人類偏好對齊訓練，相比一代模型在 MMLU、C-Eval、GSM8K 分別取得了 16%、36%、280% 的提升，並登頂中文任務榜單 C-Eval。適用於對知識量、推理能力、創造力要求較高的場景，比如廣告文案、小說寫作、知識類寫作、代碼生成等。"}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base 是由智譜開發的 ChatGLM 系列最新一代的 60 億參數規模的開源的基礎模型。"}, "chatgpt-4o-latest": {"description": "ChatGPT-4o是一款動態模型，實時更新以保持當前最新版本。它結合了強大的語言理解與生成能力，適合於大規模應用場景，包括客戶服務、教育和技術支持。"}, "claude-2.0": {"description": "Claude 2 為企業提供了關鍵能力的進步，包括業界領先的 200K token 上下文、大幅降低模型幻覺的發生率、系統提示以及一個新的測試功能：工具調用。"}, "claude-2.1": {"description": "Claude 2 為企業提供了關鍵能力的進步，包括業界領先的 200K token 上下文、大幅降低模型幻覺的發生率、系統提示以及一個新的測試功能：工具調用。"}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku 是 Anthropic 最快的下一代模型。與 Claude 3 Haiku 相比，Claude 3.5 Haiku 在各項技能上都有所提升，並在許多智力基準測試中超越了上一代最大的模型 Claude 3 Opus。"}, "claude-3-5-sonnet-********": {"description": "Claude 3.5 Sonnet 提供了超越 Opus 的能力和比 Sonnet 更快的速度，同時保持與 Sonnet 相同的價格。Sonnet 特別擅長編程、數據科學、視覺處理、代理任務。"}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet 提供了超越 Opus 的能力和比 Sonnet 更快的速度，同時保持與 Sonnet 相同的價格。Sonnet 特別擅長編程、數據科學、視覺處理、代理任務。"}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet 提升了行業標準，性能超越競爭對手模型和 Claude 3 Opus，在廣泛的評估中表現出色，同時具備我們中等層級模型的速度和成本。"}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku 是 Anthropic 的最快且最緊湊的模型，旨在實現近乎即時的響應。它具有快速且準確的定向性能。"}, "claude-3-opus-20240229": {"description": "Claude 3 Opus 是 Anthropic 用於處理高度複雜任務的最強大模型。它在性能、智能、流暢性和理解力方面表現卓越。"}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet 在智能和速度方面為企業工作負載提供了理想的平衡。它以更低的價格提供最大效用，可靠且適合大規模部署。"}, "claude-opus-4-20250514": {"description": "Claude Opus 4 是 Anthropic 用於處理高度複雜任務的最強大模型。它在性能、智能、流暢性和理解力方面表現卓越。"}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet 可以產生近乎即時的回應或延長的逐步思考，使用者可以清晰地看到這些過程。API 使用者還可以對模型思考的時間進行細緻的控制"}, "codegeex-4": {"description": "CodeGeeX-4是一個強大的AI編程助手，支持多種編程語言的智能問答與代碼補全，提升開發效率。"}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B 是一個多語言代碼生成模型，支持包括代碼補全和生成、代碼解釋器、網絡搜索、函數調用、倉庫級代碼問答在內的全面功能，覆蓋軟件開發的各種場景。是參數少於 10B 的頂尖代碼生成模型。"}, "codegemma": {"description": "CodeGemma 專用于不同編程任務的輕量級語言模型，支持快速迭代和集成。"}, "codegemma:2b": {"description": "CodeGemma 專用于不同編程任務的輕量級語言模型，支持快速迭代和集成。"}, "codellama": {"description": "Code Llama 是一款專注於代碼生成和討論的 LLM，結合廣泛的編程語言支持，適用於開發者環境。"}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama 是一款專注於代碼生成和討論的 LLM，結合廣泛的編程語言支持，適用於開發者環境。"}, "codellama:13b": {"description": "Code Llama 是一款專注於代碼生成和討論的 LLM，結合廣泛的編程語言支持，適用於開發者環境。"}, "codellama:34b": {"description": "Code Llama 是一款專注於代碼生成和討論的 LLM，結合廣泛的編程語言支持，適用於開發者環境。"}, "codellama:70b": {"description": "Code Llama 是一款專注於代碼生成和討論的 LLM，結合廣泛的編程語言支持，適用於開發者環境。"}, "codeqwen": {"description": "CodeQwen1.5 是基於大量代碼數據訓練的大型語言模型，專為解決複雜編程任務。"}, "codestral": {"description": "Codestral 是 Mistral AI 的首款代碼模型，為代碼生成任務提供優異支持。"}, "codestral-latest": {"description": "Codestral 是專注於代碼生成的尖端生成模型，優化了中間填充和代碼補全任務。"}, "codex-mini-latest": {"description": "codex-mini-latest 是 o4-mini 的微調版本，專門用於 Codex CLI。對於直接透過 API 使用，我們推薦從 gpt-4.1 開始。"}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B 是一款為指令遵循、對話和編程設計的模型。"}, "cogview-4": {"description": "CogView-4 是智譜首個支援生成漢字的開源文生圖模型，在語義理解、圖像生成質量、中英文字生成能力等方面全面提升，支援任意長度的中英雙語輸入，能夠生成在給定範圍內的任意解析度圖像。"}, "cohere-command-r": {"description": "Command R是一個可擴展的生成模型，針對RAG和工具使用，旨在為企業提供生產級AI。"}, "cohere-command-r-plus": {"description": "Command R+是一個最先進的RAG優化模型，旨在應對企業級工作負載。"}, "cohere/Cohere-command-r": {"description": "Command R 是一個可擴展的生成模型，旨在針對 RAG 和工具使用，使企業能夠實現生產級 AI。"}, "cohere/Cohere-command-r-plus": {"description": "Command R+ 是一個最先進的 RAG 優化模型，旨在應對企業級工作負載。"}, "command": {"description": "一個遵循指令的對話模型，在語言任務中表現出高質量、更可靠，並且相比我們的基礎生成模型具有更長的上下文長度。"}, "command-a-03-2025": {"description": "Command A 是我們迄今為止性能最強的模型，在工具使用、代理、檢索增強生成（RAG）和多語言應用場景方面表現出色。Command A 具有 256K 的上下文長度，僅需兩塊 GPU 即可運行，並且相比於 Command R+ 08-2024，吞吐量提高了 150%。"}, "command-light": {"description": "一個更小、更快的 Command 版本，幾乎同樣強大，但速度更快。"}, "command-light-nightly": {"description": "為了縮短主要版本發布之間的時間間隔，我們推出了 Command 模型的每夜版本。對於 command-light 系列，這一版本稱為 command-light-nightly。請注意，command-light-nightly 是最新、最具實驗性且（可能）不穩定的版本。每夜版本會定期更新，且不會提前通知，因此不建議在生產環境中使用。"}, "command-nightly": {"description": "為了縮短主要版本發布之間的時間間隔，我們推出了 Command 模型的每夜版本。對於 Command 系列，這一版本稱為 command-cightly。請注意，command-nightly 是最新、最具實驗性且（可能）不穩定的版本。每夜版本會定期更新，且不會提前通知，因此不建議在生產環境中使用。"}, "command-r": {"description": "Command R 是優化用於對話和長上下文任務的 LLM，特別適合動態交互與知識管理。"}, "command-r-03-2024": {"description": "Command R 是一個遵循指令的對話模型，在語言任務方面表現出更高的質量、更可靠，並且相比以往模型具有更長的上下文長度。它可用於複雜的工作流程，如代碼生成、檢索增強生成（RAG）、工具使用和代理。"}, "command-r-08-2024": {"description": "command-r-08-2024 是 Command R 模型的更新版本，於 2024 年 8 月發布。"}, "command-r-plus": {"description": "Command R+ 是一款高性能的大型語言模型，專為真實企業場景和複雜應用而設計。"}, "command-r-plus-04-2024": {"description": "Command R+ 是一個遵循指令的對話模型，在語言任務方面表現出更高的質量、更可靠，並且相比以往模型具有更長的上下文長度。它最適用於複雜的 RAG 工作流和多步工具使用。"}, "command-r-plus-08-2024": {"description": "Command R+ 是一個遵循指令的對話模型，在語言任務方面表現出更高的品質、更可靠，並且相比以往模型具有更長的上下文長度。它最適用於複雜的 RAG 工作流和多步工具使用。"}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 是一個小型且高效的更新版本，於 2024 年 12 月發布。它在 RAG、工具使用、代理等需要複雜推理和多步處理的任務中表現出色。"}, "compound-beta": {"description": "Compound-beta 是一個複合 AI 系統，由 GroqCloud 中已經支持的多個開放可用的模型提供支持，可以智能地、有選擇地使用工具來回答用戶查詢。"}, "compound-beta-mini": {"description": "Compound-beta-mini 是一個複合 AI 系統，由 GroqCloud 中已經支持的公開可用模型提供支持，可以智能地、有選擇地使用工具來回答用戶查詢。"}, "computer-use-preview": {"description": "computer-use-preview 模型是專為「電腦使用工具」設計的專用模型，經過訓練以理解並執行電腦相關任務。"}, "dall-e-2": {"description": "第二代 DALL·E 模型，支持更真實、準確的圖像生成，解析度是第一代的4倍"}, "dall-e-3": {"description": "最新的 DALL·E 模型，於2023年11月發布。支持更真實、準確的圖像生成，具有更強的細節表現力"}, "databricks/dbrx-instruct": {"description": "DBRX Instruct 提供高可靠性的指令處理能力，支持多行業應用。"}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 是一款強化學習（RL）驅動的推理模型，解決了模型中的重複性和可讀性問題。在 RL 之前，DeepSeek-R1 引入了冷啟動數據，進一步優化了推理性能。它在數學、程式碼和推理任務中與 OpenAI-o1 表現相當，並且通過精心設計的訓練方法，提升了整體效果。"}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 透過利用增加的計算資源和在後訓練過程中引入演算法優化機制，顯著提高了其推理和推斷能力的深度。該模型在各種基準評估中表現出色，包括數學、程式設計和一般邏輯方面。其整體性能現已接近領先模型，如 O3 和 Gemini 2.5 Pro。"}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B 是透過從 DeepSeek-R1-0528 模型蒸餾思維鏈到 Qwen3 8B Base 獲得的模型。該模型在開源模型中達到了最先進（SOTA）的性能，在 AIME 2024 測試中超越了 Qwen3 8B 10%，並達到了 Qwen3-235B-thinking 的性能水準。該模型在數學推理、程式設計和通用邏輯等多個基準測試中表現出色，其架構與 Qwen3-8B 相同，但共享 DeepSeek-R1-0528 的分詞器配置。"}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1 蒸餾模型，通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 蒸餾模型，通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 蒸餾模型，通過強化學習與冷啟動數據優化推理性能，開源模型刷新多任務標杆。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B 是基於 Qwen2.5-32B 通過知識蒸餾得到的模型。該模型使用 DeepSeek-R1 生成的 80 萬個精選樣本進行微調，在數學、編程和推理等多個領域展現出卓越的性能。在 AIME 2024、MATH-500、GPQA Diamond 等多個基準測試中都取得了優異成績，其中在 MATH-500 上達到了 94.3% 的準確率，展現出強大的數學推理能力。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B 是基於 Qwen2.5-Math-7B 通過知識蒸餾得到的模型。該模型使用 DeepSeek-R1 生成的 80 萬個精選樣本進行微調，展現出優秀的推理能力。在多個基準測試中表現出色，其中在 MATH-500 上達到了 92.8% 的準確率，在 AIME 2024 上達到了 55.5% 的通過率，在 CodeForces 上獲得了 1189 的評分，作為 7B 規模的模型展示了較強的數學和編程能力。"}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 集合了先前版本的優秀特徵，增強了通用和編碼能力。"}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 是一款擁有 6710 億參數的混合專家（MoE）語言模型，採用多頭潛在注意力（MLA）和 DeepSeekMoE 架構，結合無輔助損失的負載平衡策略，優化推理和訓練效率。通過在 14.8 萬億高品質 tokens 上預訓練，並進行監督微調和強化學習，DeepSeek-V3 在性能上超越其他開源模型，接近領先閉源模型。"}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B 是為高複雜性對話訓練的先進模型。"}, "deepseek-ai/deepseek-r1": {"description": "最先進的高效 LLM，擅長推理、數學和編程。"}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 是一個基於 DeepSeekMoE-27B 開發的混合專家（MoE）視覺語言模型，採用稀疏激活的 MoE 架構，在僅激活 4.5B 參數的情況下實現了卓越性能。該模型在視覺問答、光學字符識別、文檔/表格/圖表理解和視覺定位等多個任務中表現優異。"}, "deepseek-chat": {"description": "融合通用與代碼能力的全新開源模型，不僅保留了原有 Chat 模型的通用對話能力和 Coder 模型的強大代碼處理能力，還更好地對齊了人類偏好。此外，DeepSeek-V2.5 在寫作任務、指令跟隨等多個方面也實現了大幅提升。"}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B 是一個代碼語言模型，基於 2 萬億數據訓練而成，其中 87% 為代碼，13% 為中英文語言。模型引入 16K 窗口大小和填空任務，提供項目級別的代碼補全和片段填充功能。"}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 是開源的混合專家代碼模型，在代碼任務方面表現優異，與 GPT4-Turbo 相媲美。"}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 是開源的混合專家代碼模型，在代碼任務方面表現優異，與 GPT4-Turbo 相媲美。"}, "deepseek-r1": {"description": "DeepSeek-R1 是一款強化學習（RL）驅動的推理模型，解決了模型中的重複性和可讀性問題。在 RL 之前，DeepSeek-R1 引入了冷啟動數據，進一步優化了推理性能。它在數學、程式碼和推理任務中與 OpenAI-o1 表現相當，並且通過精心設計的訓練方法，提升了整體效果。"}, "deepseek-r1-0528": {"description": "685B 滿血版模型，2025年5月28日發布。DeepSeek-R1 在後訓練階段大規模使用了強化學習技術，在僅有極少標註資料的情況下，大幅提升了模型推理能力。在數學、程式碼、自然語言推理等任務上，性能較高，能力較強。"}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B 快速版，支持即時聯網搜索，在保持模型性能的同時提供更快的響應速度。"}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B 標準版，支持即時聯網搜索，適合需要最新信息的對話和文本處理任務。"}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama 是基於 Llama 從 DeepSeek-R1 蒸餾而來的模型。"}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1——DeepSeek 套件中更大更智能的模型——被蒸餾到 Llama 70B 架構中。基於基準測試和人工評估，該模型比原始 Llama 70B 更智能，尤其在需要數學和事實精確性的任務上表現出色。"}, "deepseek-r1-distill-llama-8b": {"description": "DeepSeek-R1-Distill 系列模型透過知識蒸餾技術，將 DeepSeek-R1 生成的樣本對 Qwen、Llama 等開源模型進行微調後得到。"}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "2025年2月14日首次發布，由千帆大模型研發團隊以 Llama3_70B為base模型（Built with Meta Llama）蒸餾所得，蒸餾數據中也同步添加了千帆的語料。"}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "2025年2月14日首次發布，由千帆大模型研發團隊以 Llama3_8B為base模型（Built with Meta Llama）蒸餾所得，蒸餾數據中也同步添加了千帆的語料。"}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen 是基於 Qwen 從 DeepSeek-R1 蒸餾而來的模型。"}, "deepseek-r1-distill-qwen-1.5b": {"description": "DeepSeek-R1-Distill 系列模型透過知識蒸餾技術，將 DeepSeek-R1 生成的樣本對 Qwen、Llama 等開源模型進行微調後得到。"}, "deepseek-r1-distill-qwen-14b": {"description": "DeepSeek-R1-Distill 系列模型透過知識蒸餾技術，將 DeepSeek-R1 生成的樣本對 Qwen、Llama 等開源模型進行微調後得到。"}, "deepseek-r1-distill-qwen-32b": {"description": "DeepSeek-R1-Distill 系列模型透過知識蒸餾技術，將 DeepSeek-R1 生成的樣本對 Qwen、Llama 等開源模型進行微調後得到。"}, "deepseek-r1-distill-qwen-7b": {"description": "DeepSeek-R1-Distill 系列模型透過知識蒸餾技術，將 DeepSeek-R1 生成的樣本對 Qwen、Llama 等開源模型進行微調後得到。"}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 滿血快速版，支持即時聯網搜索，結合了 671B 參數的強大能力和更快的響應速度。"}, "deepseek-r1-online": {"description": "DeepSeek R1 滿血版，擁有 671B 參數，支持即時聯網搜索，具有更強大的理解和生成能力。"}, "deepseek-reasoner": {"description": "DeepSeek 推出的推理模型。在輸出最終回答之前，模型會先輸出一段思維鏈內容，以提升最終答案的準確性。"}, "deepseek-v2": {"description": "DeepSeek V2 是高效的 Mixture-of-Experts 語言模型，適用於經濟高效的處理需求。"}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B 是 DeepSeek 的設計代碼模型，提供強大的代碼生成能力。"}, "deepseek-v3": {"description": "DeepSeek-V3 為杭州深度求索人工智能基礎技術研究有限公司自研的 MoE 模型，其多項評測成績突出，在主流榜單中位列開源模型榜首。V3 相較 V2.5 模型生成速度實現 3 倍提升，為用戶帶來更加迅速流暢的使用體驗。"}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 為 671B 參數的 MoE 模型，在程式設計與技術能力、上下文理解與長文本處理等方面優勢突出。"}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 是一個 685B 參數的專家混合模型，是 DeepSeek 團隊旗艦聊天模型系列的最新迭代。\n\n它繼承了 [DeepSeek V3](/deepseek/deepseek-chat-v3) 模型，並在各種任務上表現出色。"}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 是一個 685B 參數的專家混合模型，是 DeepSeek 團隊旗艦聊天模型系列的最新迭代。\n\n它繼承了 [DeepSeek V3](/deepseek/deepseek-chat-v3) 模型，並在各種任務上表現出色。"}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 在僅有極少標註數據的情況下，極大提升了模型推理能力。在輸出最終回答之前，模型會先輸出一段思維鏈內容，以提升最終答案的準確性。"}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 在僅有極少標註資料的情況下，極大提升了模型推理能力。在輸出最終回答之前，模型會先輸出一段思維鏈內容，以提升最終答案的準確性。"}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 在僅有極少標註資料的情況下，極大提升了模型推理能力。在輸出最終回答之前，模型會先輸出一段思維鏈內容，以提升最終答案的準確性。"}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B是基於Llama3.3 70B的大型語言模型，該模型利用DeepSeek R1輸出的微調，實現了與大型前沿模型相當的競爭性能。"}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B是一種基於Llama-3.1-8B-Instruct的蒸餾大語言模型，通過使用DeepSeek R1的輸出進行訓練而得。"}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B是一種基於Qwen 2.5 14B的蒸餾大語言模型，通過使用DeepSeek R1的輸出進行訓練而得。該模型在多個基準測試中超越了OpenAI的o1-mini，取得了密集模型（dense models）的最新技術領先成果（state-of-the-art）。以下是一些基準測試的結果：\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\n該模型通過從DeepSeek R1的輸出中進行微調，展現了與更大規模的前沿模型相當的競爭性能。"}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B是一種基於Qwen 2.5 32B的蒸餾大語言模型，通過使用DeepSeek R1的輸出進行訓練而得。該模型在多個基準測試中超越了OpenAI的o1-mini，取得了密集模型（dense models）的最新技術領先成果（state-of-the-art）。以下是一些基準測試的結果：\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\n該模型通過從DeepSeek R1的輸出中進行微調，展現了與更大規模的前沿模型相當的競爭性能。"}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1是DeepSeek團隊發布的最新開源模型，具備非常強悍的推理性能，尤其在數學、編程和推理任務上達到了與OpenAI的o1模型相當的水平。"}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 在僅有極少標註數據的情況下，極大提升了模型推理能力。在輸出最終回答之前，模型會先輸出一段思維鏈內容，以提升最終答案的準確性。"}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3在推理速度方面實現了比之前模型的重大突破。在開源模型中排名第一，並可與全球最先進的閉源模型相媲美。DeepSeek-V3 采用了多頭潛在注意力（MLA）和DeepSeekMoE架構，這些架構在DeepSeek-V2中得到了全面驗證。此外，DeepSeek-V3開創了一種用於負載均衡的輔助無損策略，並設定了多標記預測訓練目標以獲得更強的性能。"}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3在推理速度方面實現了比之前模型的重大突破。在開源模型中排名第一，並可與全球最先進的閉源模型相媲美。DeepSeek-V3 采用了多頭潛在注意力（MLA）和DeepSeekMoE架構，這些架構在DeepSeek-V2中得到了全面驗證。此外，DeepSeek-V3開創了一種用於負載均衡的輔助無損策略，並設定了多標記預測訓練目標以獲得更強的性能。"}, "deepseek_r1": {"description": "DeepSeek-R1 是一款強化學習（RL）驅動的推理模型，解決了模型中的重複性和可讀性問題。在 RL 之前，DeepSeek-R1 引入了冷啟動數據，進一步優化了推理性能。它在數學、程式碼和推理任務中與 OpenAI-o1 表現相當，並且通過精心設計的訓練方法，提升了整體效果。"}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B 是基於 Llama-3.3-70B-Instruct 經過蒸餾訓練得到的模型。該模型是 DeepSeek-R1 系列的一部分，通過使用 DeepSeek-R1 生成的樣本進行微調，在數學、程式設計和推理等多個領域展現出優秀的性能。"}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B 是基於 Qwen2.5-14B 通過知識蒸餾得到的模型。該模型使用 DeepSeek-R1 生成的 80 萬個精選樣本進行微調，展現出優秀的推理能力。"}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B 是基於 Qwen2.5-32B 通過知識蒸餾得到的模型。該模型使用 DeepSeek-R1 生成的 80 萬個精選樣本進行微調，在數學、程式設計和推理等多個領域展現出卓越的性能。"}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite 全新一代輕量版模型，極致響應速度，效果與時延均達到全球一流水平。"}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k 基於 Doubao-1.5-Pro 全面升級版，整體效果大幅提升 10%。支持 256k 上下文窗口的推理，輸出長度支持最大 12k tokens。更高性能、更大窗口、超高性價比，適用於更廣泛的應用場景。"}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro 全新一代主力模型，性能全面升級，在知識、程式碼、推理等方面表現卓越。"}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5全新深度思考模型，在數學、程式設計、科學推理等專業領域及創意寫作等通用任務中表現突出，在AIME 2024、Codeforces、GPQA等多項權威基準上達到或接近業界第一梯隊水平。支持128k上下文窗口，16k輸出。"}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5全新深度思考模型（m 版本自帶原生多模態深度推理能力），在數學、程式設計、科學推理等專業領域及創意寫作等通用任務中表現突出，在AIME 2024、Codeforces、GPQA等多項權威基準上達到或接近業界第一梯隊水準。支持128k上下文視窗，16k輸出。"}, "doubao-1.5-thinking-vision-pro": {"description": "全新視覺深度思考模型，具備更強的通用多模態理解和推理能力，在59個公開評測基準中的37個上取得SOTA表現。"}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS 是一款原生面向圖形介面互動（GUI）的Agent模型。透過感知、推理和行動等類人能力，與 GUI 進行無縫互動。"}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite 全新升級的多模態大模型，支持任意解析度和極端長寬比圖像識別，增強視覺推理、文檔識別、細節信息理解和指令遵循能力。支持 128k 上下文窗口，輸出長度支持最大 16k tokens。"}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro 全新升級的多模態大模型，支持任意解析度和極端長寬比影像識別，增強視覺推理、文件識別、細節資訊理解和指令遵循能力。"}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro 全新升級的多模態大模型，支持任意解析度和極端長寬比影像識別，增強視覺推理、文件識別、細節資訊理解和指令遵循能力。"}, "doubao-lite-128k": {"description": "擁有極致的響應速度，更好的性價比，為客戶不同場景提供更靈活的選擇。支持128k上下文視窗的推理和精調。"}, "doubao-lite-32k": {"description": "擁有極致的響應速度，更好的性價比，為客戶不同場景提供更靈活的選擇。支持32k上下文視窗的推理和精調。"}, "doubao-lite-4k": {"description": "擁有極致的響應速度，更好的性價比，為客戶不同場景提供更靈活的選擇。支持4k上下文視窗的推理和精調。"}, "doubao-pro-256k": {"description": "效果最好的主力模型，適合處理複雜任務，在參考問答、總結摘要、創作、文本分類、角色扮演等場景都有很好的效果。支持256k上下文視窗的推理和精調。"}, "doubao-pro-32k": {"description": "效果最好的主力模型，適合處理複雜任務，在參考問答、總結摘要、創作、文本分類、角色扮演等場景都有很好的效果。支持32k上下文視窗的推理和精調。"}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 全新多模態深度思考模型，同時支援 auto/thinking/non-thinking 三種思考模式。non-thinking 模式下，模型效果相較 Doubao-1.5-pro/250115 大幅提升。支援 256k 上下文視窗，輸出長度支援最大 16k tokens。"}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash 推理速度極致的多模態深度思考模型，TPOT 僅需 10ms；同時支援文本和視覺理解，文本理解能力超越上一代 lite，視覺理解媲美友商 pro 系列模型。支援 256k 上下文視窗，輸出長度支援最大 16k tokens。"}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking 模型思考能力大幅強化，相較 Doubao-1.5-thinking-pro，在 Coding、Math、邏輯推理等基礎能力上進一步提升，支援視覺理解。支援 256k 上下文視窗，輸出長度支援最大 16k tokens。"}, "doubao-seedream-3-0-t2i-250415": {"description": "Doubao 圖片生成模型由字節跳動 Seed 團隊研發，支持文字與圖片輸入，提供高可控、高品質的圖片生成體驗。基於文本提示詞生成圖片。"}, "doubao-vision-lite-32k": {"description": "Doubao-vision 模型是豆包推出的多模態大模型，具備強大的圖片理解與推理能力，以及精準的指令理解能力。模型在影像文本資訊擷取、基於影像的推理任務上展現出強大的性能，能夠應用於更複雜、更廣泛的視覺問答任務。"}, "doubao-vision-pro-32k": {"description": "Doubao-vision 模型是豆包推出的多模態大模型，具備強大的圖片理解與推理能力，以及精準的指令理解能力。模型在影像文本資訊擷取、基於影像的推理任務上展現出強大的性能，能夠應用於更複雜、更廣泛的視覺問答任務。"}, "emohaa": {"description": "Emohaa是一個心理模型，具備專業諮詢能力，幫助用戶理解情感問題。"}, "ernie-3.5-128k": {"description": "百度自研的旗艦級大規模大語言模型，覆蓋海量中英文語料，具有強大的通用能力，可滿足絕大部分對話問答、創作生成、插件應用場景要求；支持自動對接百度搜索插件，保障問答信息時效。"}, "ernie-3.5-8k": {"description": "百度自研的旗艦級大規模大語言模型，覆蓋海量中英文語料，具有強大的通用能力，可滿足絕大部分對話問答、創作生成、插件應用場景要求；支持自動對接百度搜索插件，保障問答信息時效。"}, "ernie-3.5-8k-preview": {"description": "百度自研的旗艦級大規模大語言模型，覆蓋海量中英文語料，具有強大的通用能力，可滿足絕大部分對話問答、創作生成、插件應用場景要求；支持自動對接百度搜索插件，保障問答信息時效。"}, "ernie-4.0-8k-latest": {"description": "百度自研的旗艦級超大規模大語言模型，相較ERNIE 3.5實現了模型能力全面升級，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。"}, "ernie-4.0-8k-preview": {"description": "百度自研的旗艦級超大規模大語言模型，相較ERNIE 3.5實現了模型能力全面升級，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。"}, "ernie-4.0-turbo-128k": {"description": "百度自研的旗艦級超大規模大語言模型，綜合效果表現出色，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。相較於ERNIE 4.0在性能表現上更優秀"}, "ernie-4.0-turbo-8k-latest": {"description": "百度自研的旗艦級超大規模大語言模型，綜合效果表現出色，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。相較於ERNIE 4.0在性能表現上更優秀"}, "ernie-4.0-turbo-8k-preview": {"description": "百度自研的旗艦級超大規模大語言模型，綜合效果表現出色，廣泛適用於各領域複雜任務場景；支持自動對接百度搜索插件，保障問答信息時效。相較於ERNIE 4.0在性能表現上更優秀"}, "ernie-4.5-8k-preview": {"description": "文心大模型4.5是百度自主研發的新一代原生多模態基礎大模型，通過多個模態聯合建模實現協同優化，多模態理解能力優秀；具備更精進的語言能力，理解、生成、邏輯、記憶能力全面提升，去幻覺、邏輯推理、代碼能力顯著提升。"}, "ernie-4.5-turbo-128k": {"description": "文心4.5 Turbo在去幻覺、邏輯推理和程式碼能力等方面也有著明顯增強。對比文心4.5，速度更快、價格更低。模型能力全面提升，更好滿足多輪長歷史對話處理、長文檔理解問答任務。"}, "ernie-4.5-turbo-32k": {"description": "文心4.5 Turbo在去幻覺、邏輯推理和程式碼能力等方面也有著明顯增強。對比文心4.5，速度更快、價格更低。文本創作、知識問答等能力提升顯著。輸出長度及整句時延相較ERNIE 4.5有所增加。"}, "ernie-4.5-turbo-vl-32k": {"description": "文心一言大模型全新版本，圖片理解、創作、翻譯、程式碼等能力顯著提升，首次支持32K上下文長度，首Token時延顯著降低。"}, "ernie-char-8k": {"description": "百度自研的垂直場景大語言模型，適合遊戲NPC、客服對話、對話角色扮演等應用場景，人設風格更為鮮明、一致，指令遵循能力更強，推理性能更優。"}, "ernie-char-fiction-8k": {"description": "百度自研的垂直場景大語言模型，適合遊戲NPC、客服對話、對話角色扮演等應用場景，人設風格更為鮮明、一致，指令遵循能力更強，推理性能更優。"}, "ernie-irag-edit": {"description": "百度自研的 ERNIE iRAG Edit 圖像編輯模型支持基於圖片進行 erase（消除物件）、repaint（重繪物件）、variation（生成變體）等操作。"}, "ernie-lite-8k": {"description": "ERNIE Lite是百度自研的輕量級大語言模型，兼顧優異的模型效果與推理性能，適合低算力AI加速卡推理使用。"}, "ernie-lite-pro-128k": {"description": "百度自研的輕量級大語言模型，兼顧優異的模型效果與推理性能，效果比ERNIE Lite更優，適合低算力AI加速卡推理使用。"}, "ernie-novel-8k": {"description": "百度自研通用大語言模型，在小說續寫能力上有明顯優勢，也可用在短劇、電影等場景。"}, "ernie-speed-128k": {"description": "百度2024年最新發布的自研高性能大語言模型，通用能力優異，適合作為基座模型進行精調，更好地處理特定場景問題，同時具備極佳的推理性能。"}, "ernie-speed-pro-128k": {"description": "百度2024年最新發布的自研高性能大語言模型，通用能力優異，效果比ERNIE Speed更優，適合作為基座模型進行精調，更好地處理特定場景問題，同時具備極佳的推理性能。"}, "ernie-tiny-8k": {"description": "ERNIE Tiny是百度自研的超高性能大語言模型，部署與精調成本在文心系列模型中最低。"}, "ernie-x1-32k": {"description": "具備更強的理解、規劃、反思、進化能力。作為能力更全面的深度思考模型，文心X1兼具準確、創意和文采，在中文知識問答、文學創作、文稿寫作、日常對話、邏輯推理、複雜計算及工具調用等方面表現尤為出色。"}, "ernie-x1-32k-preview": {"description": "文心大模型X1具備更強的理解、規劃、反思、進化能力。作為能力更全面的深度思考模型，文心X1兼具準確、創意和文采，在中文知識問答、文學創作、文稿寫作、日常對話、邏輯推理、複雜計算及工具調用等方面表現尤為出色。"}, "ernie-x1-turbo-32k": {"description": "與ERNIE-X1-32K相比，模型效果和性能更佳。"}, "flux-1-schnell": {"description": "由 Black Forest Labs 開發的 120 億參數文生圖模型，採用潛在對抗擴散蒸餾技術，能夠在 1 到 4 步內生成高品質圖像。該模型性能媲美閉源替代品，並在 Apache-2.0 許可證下發布，適用於個人、科研和商業用途。"}, "flux-dev": {"description": "FLUX.1 [dev] 是一款面向非商業應用的開源權重、精煉模型。FLUX.1 [dev] 在保持了與 FLUX 專業版相近的圖像品質和指令遵循能力的同時，具備更高的運行效率。相較於同尺寸的標準模型，它在資源利用上更為高效。"}, "flux-kontext/dev": {"description": "Frontier 影像編輯模型。"}, "flux-merged": {"description": "FLUX.1-merged 模型結合了 \"DEV\" 在開發階段探索的深度特性和 \"Schnell\" 所代表的高速執行優勢。透過這一舉措，FLUX.1-merged 不僅提升了模型的性能界限，還拓寬了其應用範圍。"}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] 能夠處理文字和參考圖像作為輸入，無縫實現目標性的局部編輯和複雜的整體場景變換。"}, "flux-schnell": {"description": "FLUX.1 [schnell] 作為目前開源最先進的少步模型，不僅超越了同類競爭者，甚至還優於諸如 Midjourney v6.0 和 DALL·E 3 (HD) 等強大的非精煉模型。該模型經過專門微調，以保留預訓練階段的全部輸出多樣性，相較於當前市場上的最先進模型，FLUX.1 [schnell] 顯著提升了在視覺品質、指令遵從、尺寸/比例變化、字體處理及輸出多樣性等方面的可能，為用戶帶來更為豐富多樣的創意圖像生成體驗。"}, "flux.1-schnell": {"description": "具有120億參數的修正流變換器，能夠根據文本描述生成圖像。"}, "flux/schnell": {"description": "FLUX.1 [schnell] 是一個擁有120億參數的流式轉換器模型，能夠在1到4步內從文字生成高品質圖像，適合個人和商業用途。"}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning) 提供穩定並可調優的性能，是複雜任務解決方案的理想選擇。"}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning) 提供出色的多模態支持，專注於複雜任務的有效解決。"}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro 是 Google 的高性能 AI 模型，專為廣泛任務擴展而設計。"}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 是一款高效的多模態模型，支持廣泛應用的擴展。"}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 是一款高效的多模態模型，支持廣泛應用的擴展。"}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B 是一款高效的多模態模型，支持廣泛應用的擴展。"}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 是最新的實驗性模型，在文本和多模態用例中都有顯著的性能提升。"}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B 是一款高效的多模態模型，支援廣泛應用的擴展。"}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 提供了優化後的多模態處理能力，適用多種複雜任務場景。"}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash 是 Google 最新的多模態 AI 模型，具備快速處理能力，支持文本、圖像和視頻輸入，適用於多種任務的高效擴展。"}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 是可擴展的多模態 AI 解決方案，支持廣泛的複雜任務。"}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 是最新的生產就緒模型，提供更高品質的輸出，特別在數學、長上下文和視覺任務方面有顯著提升。"}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 提供出色的多模態處理能力，為應用開發帶來更大靈活性。"}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 結合最新優化技術，帶來更高效的多模態數據處理能力。"}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro 支持高達 200 萬個 tokens，是中型多模態模型的理想選擇，適用於複雜任務的多方面支持。"}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash 提供下一代功能和改進，包括卓越的速度、原生工具使用、多模態生成和1M令牌上下文窗口。"}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash 提供下一代功能和改進，包括卓越的速度、原生工具使用、多模態生成和1M令牌上下文窗口。"}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash 模型變體，針對成本效益和低延遲等目標進行了優化。"}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash 實驗模型，支持圖像生成"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash 模型變體，針對成本效益和低延遲等目標進行了優化。"}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash 模型變體，針對成本效益和低延遲等目標進行了優化。"}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash 預覽模型，支持圖像生成"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash 是 Google 性價比最高的模型，提供全面的功能。"}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite 是 Google 最小、性價比最高的模型，專為大規模使用而設計。"}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview 是 Google 最小、性價比最高的模型，專為大規模使用而設計。"}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview 是 Google 性價比最高的模型，提供全面的功能。"}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview 是 Google 性價比最高的模型，提供全面的功能。"}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro 是 Google 最先進的思維模型，能夠對程式碼、數學和 STEM 領域的複雜問題進行推理，以及使用長上下文分析大型資料集、程式碼庫和文件。"}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview 是 Google 最先進的思維模型，能夠對程式碼、數學和STEM領域的複雜問題進行推理，以及使用長上下文分析大型數據集、程式庫和文檔。"}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview 是 Google 最先進的思維模型，能夠對程式碼、數學和 STEM 領域的複雜問題進行推理，以及使用長上下文分析大型數據集、程式庫和文檔。"}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview 是 Google 最先進的思維模型，能夠對程式碼、數學和STEM領域的複雜問題進行推理，以及使用長上下文分析大型資料集、程式碼庫和文件。"}, "gemma-7b-it": {"description": "Gemma 7B 適合中小規模任務處理，兼具成本效益。"}, "gemma2": {"description": "Gemma 2 是 Google 推出的高效模型，涵蓋從小型應用到複雜數據處理的多種應用場景。"}, "gemma2-9b-it": {"description": "Gemma 2 9B 是一款優化用於特定任務和工具整合的模型。"}, "gemma2:27b": {"description": "Gemma 2 是 Google 推出的高效模型，涵蓋從小型應用到複雜數據處理的多種應用場景。"}, "gemma2:2b": {"description": "Gemma 2 是 Google 推出的高效模型，涵蓋從小型應用到複雜數據處理的多種應用場景。"}, "generalv3": {"description": "Spark Pro 是一款為專業領域優化的高性能大語言模型，專注數學、編程、醫療、教育等多個領域，並支持聯網搜索及內置天氣、日期等插件。其優化後模型在複雜知識問答、語言理解及高層次文本創作中展現出色表現和高效性能，是適合專業應用場景的理想選擇。"}, "generalv3.5": {"description": "Spark3.5 Max 為功能最為全面的版本，支持聯網搜索及眾多內置插件。其全面優化的核心能力以及系統角色設定和函數調用功能，使其在各種複雜應用場景中的表現極為優異和出色。"}, "glm-4": {"description": "GLM-4是發布於2024年1月的舊旗艦版本，目前已被更強的GLM-4-0520取代。"}, "glm-4-0520": {"description": "GLM-4-0520是最新模型版本，專為高度複雜和多樣化任務設計，表現卓越。"}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat 在語義、數學、推理、代碼和知識等多方面均表現出較高性能。還具備網頁瀏覽、代碼執行、自定義工具調用和長文本推理。支持包括日語、韓語、德語在內的 26 種語言。"}, "glm-4-air": {"description": "GLM-4-Air是性價比高的版本，性能接近GLM-4，提供快速度和實惠的價格。"}, "glm-4-air-250414": {"description": "GLM-4-Air 是性價比高的版本，性能接近GLM-4，提供快速度和實惠的價格。"}, "glm-4-airx": {"description": "GLM-4-AirX提供GLM-4-Air的高效版本，推理速度可達其2.6倍。"}, "glm-4-alltools": {"description": "GLM-4-AllTools是一個多功能智能體模型，優化以支持複雜指令規劃與工具調用，如網絡瀏覽、代碼解釋和文本生成，適用於多任務執行。"}, "glm-4-flash": {"description": "GLM-4-Flash是處理簡單任務的理想選擇，速度最快且價格最優惠。"}, "glm-4-flash-250414": {"description": "GLM-4-Flash 是處理簡單任務的理想選擇，速度最快且免費。"}, "glm-4-flashx": {"description": "GLM-4-FlashX 是 Flash 的增強版本，具備超快的推理速度。"}, "glm-4-long": {"description": "GLM-4-Long支持超長文本輸入，適合記憶型任務與大規模文檔處理。"}, "glm-4-plus": {"description": "GLM-4-Plus作為高智能旗艦，具備強大的處理長文本和複雜任務的能力，性能全面提升。"}, "glm-4.1v-thinking-flash": {"description": "GLM-4.1V-Thinking 系列模型是目前已知10B級別的VLM模型中性能最強的視覺模型，融合了同級別SOTA的各項視覺語言任務，包括影片理解、圖片問答、學科解題、OCR文字識別、文件和圖表解讀、GUI Agent、前端網頁程式設計、Grounding等，多項任務能力甚至超過8倍參數量的Qwen2.5-VL-72B。通過領先的強化學習技術，模型掌握了透過思維鏈推理的方式提升回答的準確性和豐富度，從最終效果和可解釋性等維度都顯著超過傳統的非thinking模型。"}, "glm-4.1v-thinking-flashx": {"description": "GLM-4.1V-Thinking 系列模型是目前已知10B級別的VLM模型中性能最強的視覺模型，融合了同級別SOTA的各項視覺語言任務，包括影片理解、圖片問答、學科解題、OCR文字識別、文件和圖表解讀、GUI Agent、前端網頁程式設計、Grounding等，多項任務能力甚至超過8倍參數量的Qwen2.5-VL-72B。通過領先的強化學習技術，模型掌握了透過思維鏈推理的方式提升回答的準確性和豐富度，從最終效果和可解釋性等維度都顯著超過傳統的非thinking模型。"}, "glm-4.5": {"description": "智譜最新旗艦模型，支持思考模式切換，綜合能力達到開源模型的 SOTA 水準，上下文長度可達128K。"}, "glm-4.5-air": {"description": "GLM-4.5 的輕量版，兼顧性能與性價比，可靈活切換混合思考模型。"}, "glm-4.5-airx": {"description": "GLM-4.5-Air 的極速版，響應速度更快，專為大規模高速度需求打造。"}, "glm-4.5-flash": {"description": "GLM-4.5 的免費版，推理、程式碼、智能體等任務表現出色。"}, "glm-4.5-x": {"description": "GLM-4.5 的極速版，在性能強勁的同時，生成速度可達 100 tokens/秒。"}, "glm-4v": {"description": "GLM-4V提供強大的圖像理解與推理能力，支持多種視覺任務。"}, "glm-4v-flash": {"description": "GLM-4V-Flash 專注於高效的單一圖像理解，適用於快速圖像解析的場景，例如即時圖像分析或批量圖像處理。"}, "glm-4v-plus": {"description": "GLM-4V-Plus具備對視頻內容及多圖片的理解能力，適合多模態任務。"}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus 具備對視頻內容及多圖片的理解能力，適合多模態任務。"}, "glm-z1-air": {"description": "推理模型: 具備強大推理能力，適用於需要深度推理的任務。"}, "glm-z1-airx": {"description": "極速推理：具有超快的推理速度和強大的推理效果。"}, "glm-z1-flash": {"description": "GLM-Z1 系列具備強大的複雜推理能力，在邏輯推理、數學、程式設計等領域表現優異。"}, "glm-z1-flashx": {"description": "高速低價：Flash增強版本，超快推理速度，更快並發保障。"}, "glm-zero-preview": {"description": "GLM-Zero-Preview具備強大的複雜推理能力，在邏輯推理、數學、程式設計等領域表現優異。"}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash 提供下一代功能和改進，包括卓越的速度、原生工具使用、多模態生成和1M令牌上下文窗口。"}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental 是 Google 最新的實驗性多模態 AI 模型，與歷史版本相比有一定的質量提升，特別是對於世界知識、程式碼和長上下文。"}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash 是 Google 最先進的主力模型，專為高階推理、編碼、數學和科學任務而設計。它包含內建的「思考」能力，使其能夠提供具有更高準確性和細緻上下文處理的回應。\n\n注意：此模型有兩個變體：思考和非思考。輸出定價根據思考能力是否啟用而有顯著差異。如果您選擇標準變體（不帶「:thinking」後綴），模型將明確避免生成思考令牌。\n\n要利用思考能力並接收思考令牌，您必須選擇「:thinking」變體，這將產生較高的思考輸出定價。\n\n此外，Gemini 2.5 Flash 可透過「推理最大令牌數」參數進行配置，如文件中所述 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash 是 Google 最先進的主力模型，專為高級推理、編碼、數學和科學任務而設計。它包含內建的「思考」能力，使其能夠提供具有更高準確性和細緻上下文處理的回應。\n\n注意：此模型有兩個變體：思考和非思考。輸出定價根據思考能力是否啟用而有顯著差異。如果您選擇標準變體（不帶「:thinking」後綴），模型將明確避免生成思考令牌。\n\n要利用思考能力並接收思考令牌，您必須選擇「:thinking」變體，這將產生更高的思考輸出定價。\n\n此外，Gemini 2.5 Flash 可通過「推理最大令牌數」參數進行配置，如文檔中所述 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash 是 Google 最先進的主力模型，專為高級推理、編碼、數學和科學任務而設計。它包含內建的「思考」能力，使其能夠提供具有更高準確性和細緻上下文處理的回應。\n\n注意：此模型有兩個變體：思考和非思考。輸出定價根據思考能力是否啟用而有顯著差異。如果您選擇標準變體（不帶「:thinking」後綴），模型將明確避免生成思考令牌。\n\n要利用思考能力並接收思考令牌，您必須選擇「:thinking」變體，這將產生更高的思考輸出定價。\n\n此外，Gemini 2.5 Flash 可通過「推理最大令牌數」參數進行配置，如文檔中所述 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro 是 Google 最先進的思維模型，能夠對程式碼、數學和 STEM 領域的複雜問題進行推理，以及使用長上下文分析大型資料集、程式碼庫和文件。"}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview 是 Google 最先進的思維模型，能夠對程式碼、數學和 STEM 領域的複雜問題進行推理，以及使用長上下文分析大型資料集、程式碼庫和文件。"}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash 提供了優化後的多模態處理能力，適用於多種複雜任務場景。"}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro 結合最新的優化技術，帶來更高效的多模態數據處理能力。"}, "google/gemma-2-27b": {"description": "Gemma 2 是 Google 推出的高效模型，涵蓋從小型應用到複雜數據處理的多種應用場景。"}, "google/gemma-2-27b-it": {"description": "Gemma 2 延續了輕量化與高效的設計理念。"}, "google/gemma-2-2b-it": {"description": "Google的輕量級指令調優模型"}, "google/gemma-2-9b": {"description": "Gemma 2 是 Google 推出的高效模型，涵蓋從小型應用到複雜數據處理的多種應用場景。"}, "google/gemma-2-9b-it": {"description": "Gemma 2 是 Google 輕量化的開源文本模型系列。"}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 是Google輕量化的開源文本模型系列。"}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) 提供基本的指令處理能力，適合輕量級應用。"}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B 是谷歌的一款開源語言模型，以其在效率和性能方面樹立了新的標準。"}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B 是谷歌的一款開源語言模型，以其在效率和性能方面設立了新的標準。"}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo，適用於各種文本生成和理解任務，Currently points to gpt-3.5-turbo-0125"}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo，適用於各種文本生成和理解任務，Currently points to gpt-3.5-turbo-0125"}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo，適用於各種文本生成和理解任務，Currently points to gpt-3.5-turbo-0125"}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo，適用於各種文本生成和理解任務，Currently points to gpt-3.5-turbo-0125"}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo，OpenAI 提供的高效模型，適用於聊天和文本生成任務，支持並行函數調用。"}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k，高容量文本生成模型，適合複雜任務。"}, "gpt-4": {"description": "GPT-4提供了一個更大的上下文窗口，能夠處理更長的文本輸入，適用於需要廣泛信息整合和數據分析的場景。"}, "gpt-4-0125-preview": {"description": "最新的GPT-4 Turbo模型具備視覺功能。現在，視覺請求可以使用JSON模式和函數調用。GPT-4 Turbo是一個增強版本，為多模態任務提供成本效益高的支持。它在準確性和效率之間找到平衡，適合需要進行實時交互的應用程序場景。"}, "gpt-4-0613": {"description": "GPT-4提供了一個更大的上下文窗口，能夠處理更長的文本輸入，適用於需要廣泛信息整合和數據分析的場景。"}, "gpt-4-1106-preview": {"description": "最新的GPT-4 Turbo模型具備視覺功能。現在，視覺請求可以使用JSON模式和函數調用。GPT-4 Turbo是一個增強版本，為多模態任務提供成本效益高的支持。它在準確性和效率之間找到平衡，適合需要進行實時交互的應用程序場景。"}, "gpt-4-32k": {"description": "GPT-4提供了一個更大的上下文窗口，能夠處理更長的文本輸入，適用於需要廣泛信息整合和數據分析的場景。"}, "gpt-4-32k-0613": {"description": "GPT-4提供了一個更大的上下文窗口，能夠處理更長的文本輸入，適用於需要廣泛信息整合和數據分析的場景。"}, "gpt-4-turbo": {"description": "最新的GPT-4 Turbo模型具備視覺功能。現在，視覺請求可以使用JSON模式和函數調用。GPT-4 Turbo是一個增強版本，為多模態任務提供成本效益高的支持。它在準確性和效率之間找到平衡，適合需要進行實時交互的應用程序場景。"}, "gpt-4-turbo-2024-04-09": {"description": "最新的GPT-4 Turbo模型具備視覺功能。現在，視覺請求可以使用JSON模式和函數調用。GPT-4 Turbo是一個增強版本，為多模態任務提供成本效益高的支持。它在準確性和效率之間找到平衡，適合需要進行實時交互的應用程序場景。"}, "gpt-4-turbo-preview": {"description": "最新的GPT-4 Turbo模型具備視覺功能。現在，視覺請求可以使用JSON模式和函數調用。GPT-4 Turbo是一個增強版本，為多模態任務提供成本效益高的支持。它在準確性和效率之間找到平衡，適合需要進行實時交互的應用程序場景。"}, "gpt-4-vision-preview": {"description": "最新的GPT-4 Turbo模型具備視覺功能。現在，視覺請求可以使用JSON模式和函數調用。GPT-4 Turbo是一個增強版本，為多模態任務提供成本效益高的支持。它在準確性和效率之間找到平衡，適合需要進行實時交互的應用程序場景。"}, "gpt-4.1": {"description": "GPT-4.1 是我們用於複雜任務的旗艦模型。它非常適合跨領域解決問題。"}, "gpt-4.1-mini": {"description": "GPT-4.1 mini 提供了智能、速度和成本之間的平衡，使其成為許多用例中具吸引力的模型。"}, "gpt-4.1-nano": {"description": "GPT-4.1 mini 提供了智能、速度和成本之間的平衡，使其成為許多用例中具吸引力的模型。"}, "gpt-4.5-preview": {"description": "GPT-4.5 的研究預覽版，它是我們迄今為止最大、最強大的 GPT 模型。它擁有廣泛的世界知識，並能更好地理解用戶意圖，使其在創造性任務和自主規劃方面表現出色。GPT-4.5 可接受文本和圖像輸入，並生成文本輸出（包括結構化輸出）。支持關鍵的開發者功能，如函數調用、批量 API 和串流輸出。在需要創造性、開放式思考和對話的任務（如寫作、學習或探索新想法）中，GPT-4.5 表現尤為出色。知識截止日期為 2023 年 10 月。"}, "gpt-4o": {"description": "ChatGPT-4o是一款動態模型，實時更新以保持當前最新版本。它結合了強大的語言理解與生成能力，適合於大規模應用場景，包括客戶服務、教育和技術支持。"}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o是一款動態模型，實時更新以保持當前最新版本。它結合了強大的語言理解與生成能力，適合於大規模應用場景，包括客戶服務、教育和技術支持。"}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o是一款動態模型，實時更新以保持當前最新版本。它結合了強大的語言理解與生成能力，適合於大規模應用場景，包括客戶服務、教育和技術支持。"}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o 是一款動態模型，實時更新以保持當前最新版本。它結合了強大的語言理解與生成能力，適合於大規模應用場景，包括客戶服務、教育和技術支持。"}, "gpt-4o-audio-preview": {"description": "GPT-4o Audio 模型，支持音頻輸入輸出"}, "gpt-4o-mini": {"description": "GPT-4o mini是OpenAI在GPT-4 Omni之後推出的最新模型，支持圖文輸入並輸出文本。作為他們最先進的小型模型，它比其他近期的前沿模型便宜很多，並且比GPT-3.5 Turbo便宜超過60%。它保持了最先進的智能，同時具有顯著的性價比。GPT-4o mini在MMLU測試中獲得了82%的得分，目前在聊天偏好上排名高於GPT-4。"}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Audio 模型，支援音訊輸入輸出"}, "gpt-4o-mini-realtime-preview": {"description": "GPT-4o-mini 實時版本，支持音頻和文本實時輸入輸出"}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini 搜尋預覽版是一個專門訓練用於理解和執行網頁搜尋查詢的模型，使用的是 Chat Completions API。除了代幣費用之外，網頁搜尋查詢還會按每次工具呼叫收取費用。"}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe 是一種使用 GPT-4o 轉錄音訊的語音轉文字模型。與原始 Whisper 模型相比，它降低了字詞錯誤率，並提升了語言識別和準確性。使用它來獲得更準確的轉錄。"}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS 是基於 GPT-4o mini 的文本轉語音模型，提供高品質的語音生成，同時降低成本。"}, "gpt-4o-realtime-preview": {"description": "GPT-4o 實時版本，支持音頻和文本實時輸入輸出"}, "gpt-4o-realtime-preview-2024-10-01": {"description": "GPT-4o 實時版本，支持音頻和文本實時輸入輸出"}, "gpt-4o-realtime-preview-2025-06-03": {"description": "GPT-4o 即時版本，支持音訊和文字即時輸入輸出"}, "gpt-4o-search-preview": {"description": "GPT-4o 搜尋預覽版是一個專門訓練用於理解和執行網頁搜尋查詢的模型，使用的是 Chat Completions API。除了代幣費用之外，網頁搜尋查詢還會按每次工具呼叫收取費用。"}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe 是一種使用 GPT-4o 轉錄音訊的語音轉文字模型。與原始 Whisper 模型相比，它降低了字詞錯誤率，並提升了語言識別和準確性。使用它來獲得更準確的轉錄。"}, "gpt-image-1": {"description": "ChatGPT 原生多模態圖片生成模型"}, "grok-2-1212": {"description": "該模型在準確性、指令遵循和多語言能力方面有所改進。"}, "grok-2-image-1212": {"description": "我們最新的圖像生成模型可以根據文本提示生成生動逼真的圖像。它在行銷、社交媒體和娛樂等領域的圖像生成方面表現出色。"}, "grok-2-vision-1212": {"description": "該模型在準確性、指令遵循和多語言能力方面有所改進。"}, "grok-3": {"description": "旗艦級模型，擅長資料擷取、程式設計和文本摘要等企業級應用，擁有金融、醫療、法律和科學等領域的深厚知識。"}, "grok-3-fast": {"description": "旗艦級模型，擅長資料擷取、程式設計和文本摘要等企業級應用，擁有金融、醫療、法律和科學等領域的深厚知識。"}, "grok-3-mini": {"description": "輕量級模型，對話前會先思考。運行快速、智能，適用於不需要深層領域知識的邏輯任務，並能獲取原始的思維軌跡。"}, "grok-3-mini-fast": {"description": "輕量級模型，對話前會先思考。運行快速、智能，適用於不需要深層領域知識的邏輯任務，並能獲取原始的思維軌跡。"}, "grok-4": {"description": "我們最新最強大的旗艦模型，在自然語言處理、數學計算和推理方面表現卓越 —— 是一款完美的全能型選手。"}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B 是一款合併了多個頂尖模型的創意與智能相結合的語言模型。"}, "hunyuan-a13b": {"description": "混元第一個混合推理模型，hunyuan-standard-256K 的升級版本，總參數80B，激活13B，預設為慢思考模式，支持透過參數或指令進行快慢思考模式切換，慢快思考切換方式為 query 前加/ no_think；整體能力相較上一代全面提升，特別是數學、科學、長文理解和 Agent 能力提升顯著。"}, "hunyuan-code": {"description": "混元最新代碼生成模型，經過 200B 高質量代碼數據增訓基座模型，迭代半年高質量 SFT 數據訓練，上下文長窗口長度增大到 8K，五大語言代碼生成自動評測指標上位居前列；五大語言 10 項考量各方面綜合代碼任務人工高質量評測上，性能處於第一梯隊。"}, "hunyuan-functioncall": {"description": "混元最新 MOE 架構 FunctionCall 模型，經過高質量的 FunctionCall 數據訓練，上下文窗口達 32K，在多個維度的評測指標上處於領先。"}, "hunyuan-large": {"description": "Hunyuan-large 模型總參數量約 389B，激活參數量約 52B，是當前業界參數規模最大、效果最好的 Transformer 架構的開源 MoE 模型。"}, "hunyuan-large-longcontext": {"description": "擅長處理長文任務如文檔摘要和文檔問答等，同時也具備處理通用文本生成任務的能力。在長文本的分析和生成上表現優異，能有效應對複雜和詳盡的長文內容處理需求。"}, "hunyuan-large-vision": {"description": "此模型適用於圖文理解場景，是基於混元Large訓練的視覺語言大模型，支援任意解析度多張圖片+文本輸入，生成文本內容，聚焦圖文理解相關任務，在多語言圖文理解能力上有顯著提升。"}, "hunyuan-lite": {"description": "升級為 MOE 結構，上下文窗口為 256k，在 NLP、代碼、數學、行業等多項評測集上領先眾多開源模型。"}, "hunyuan-lite-vision": {"description": "混元最新7B多模態模型，上下文窗口32K，支持中英文場景的多模態對話、圖像物體識別、文檔表格理解、多模態數學等，在多個維度上評測指標優於7B競品模型。"}, "hunyuan-pro": {"description": "萬億級參數規模 MOE-32K 長文模型。在各種 benchmark 上達到絕對領先的水平，具備複雜指令和推理能力，支持 functioncall，在多語言翻譯、金融法律醫療等領域應用重點優化。"}, "hunyuan-role": {"description": "混元最新版角色扮演模型，混元官方精調訓練推出的角色扮演模型，基於混元模型結合角色扮演場景數據集進行增訓，在角色扮演場景具有更好的基礎效果。"}, "hunyuan-standard": {"description": "採用更優的路由策略，同時緩解了負載均衡和專家趨同的問題。長文方面，大海撈針指標達到 99.9%。MOE-32K 性價比相對更高，在平衡效果和價格的同時，可實現對長文本輸入的處理。"}, "hunyuan-standard-256K": {"description": "採用更優的路由策略，同時緩解了負載均衡和專家趨同的問題。長文方面，大海撈針指標達到 99.9%。MOE-256K 在長度和效果上進一步突破，極大地擴展了可輸入長度。"}, "hunyuan-standard-vision": {"description": "混元最新多模態模型，支持多語種作答，中英文能力均衡。"}, "hunyuan-t1-20250321": {"description": "全面搭建模型文理科能力，長文本信息捕捉能力強。支持推理解答各種難度的數學/邏輯推理/科學/代碼等科學問題。"}, "hunyuan-t1-20250403": {"description": "提升專案級別程式碼生成能力；提升文本生成寫作品質；提升文本理解 topic 的多輪、tob 指令遵循和字詞理解能力；優化繁簡混雜和中英混雜輸出問題。"}, "hunyuan-t1-20250529": {"description": "優化文本創作、作文寫作，優化程式碼前端、數學、邏輯推理等理科能力，提升指令遵循能力。"}, "hunyuan-t1-20250711": {"description": "大幅提升高難度數學、邏輯和程式碼能力，優化模型輸出穩定性，提升模型長文能力。"}, "hunyuan-t1-latest": {"description": "業界首個超大規模 Hybrid-Transformer-Mamba 推理模型，擴展推理能力，超強解碼速度，進一步對齊人類偏好。"}, "hunyuan-t1-vision": {"description": "混元多模態理解深度思考模型，支援多模態原生長思維鏈，擅長處理各種圖片推理場景，在理科難題上相比快思考模型全面提升。"}, "hunyuan-t1-vision-20250619": {"description": "混元最新版 t1-vision 多模態理解深度思考模型，支持多模態原生長思維鏈，相較上一代預設版本模型全面提升。"}, "hunyuan-turbo": {"description": "混元全新一代大語言模型的預覽版，採用全新的混合專家模型（MoE）結構，相較於 hunyuan-pro 推理效率更快，效果表現更強。"}, "hunyuan-turbo-20241223": {"description": "本版本優化：數據指令scaling，大幅提升模型通用泛化能力；大幅提升數學、程式碼、邏輯推理能力；優化文本理解字詞理解相關能力；優化文本創作內容生成質量"}, "hunyuan-turbo-latest": {"description": "通用體驗優化，包括NLP理解、文本創作、閒聊、知識問答、翻譯、領域等；提升擬人性，優化模型情商；提升意圖模糊時模型主動澄清能力；提升字詞解析類問題的處理能力；提升創作的質量和可互動性；提升多輪體驗。"}, "hunyuan-turbo-vision": {"description": "混元新一代視覺語言旗艦大模型，採用全新的混合專家模型（MoE）結構，在圖文理解相關的基礎識別、內容創作、知識問答、分析推理等能力上相比前一代模型全面提升。"}, "hunyuan-turbos-20250313": {"description": "統一數學解題步驟的風格，加強數學多輪問答。文本創作優化回答風格，去除 AI 味，增加文采。"}, "hunyuan-turbos-20250416": {"description": "預訓練底座升級，增強底座的指令理解及遵循能力；對齊階段增強數學、程式碼、邏輯、科學等理科能力；提升文創寫作品質、文本理解、翻譯準確率、知識問答等文科能力；增強各領域 Agent 能力，重點加強多輪對話理解能力等。"}, "hunyuan-turbos-20250604": {"description": "預訓練底座升級，寫作、閱讀理解能力提升，較大幅度提升程式碼和理科能力，複雜指令遵循等持續提升。"}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS 混元旗艦大模型最新版本，具備更強的思考能力，更優的體驗效果。"}, "hunyuan-turbos-longtext-128k-20250325": {"description": "擅長處理長文任務如文檔摘要和文檔問答等，同時也具備處理通用文本生成任務的能力。在長文本的分析和生成上表現優異，能有效應對複雜和詳盡的長文內容處理需求。"}, "hunyuan-turbos-role-plus": {"description": "混元最新版角色扮演模型，混元官方精調訓練推出的角色扮演模型，基於混元模型結合角色扮演場景資料集進行增訓，在角色扮演場景具有更好的基礎效果。"}, "hunyuan-turbos-vision": {"description": "此模型適用於圖文理解場景，是基於混元最新 turbos 的新一代視覺語言旗艦大型模型，聚焦圖文理解相關任務，包括基於圖片的實體識別、知識問答、文案創作、拍照解題等方面，相較前一代模型全面提升。"}, "hunyuan-turbos-vision-20250619": {"description": "混元最新版 turbos-vision 視覺語言旗艦大型模型，在圖文理解相關的任務上，包括基於圖片的實體識別、知識問答、文案創作、拍照解題等方面，相較上一代預設版本模型全面提升。"}, "hunyuan-vision": {"description": "混元最新多模態模型，支持圖片 + 文本輸入生成文本內容。"}, "image-01": {"description": "全新圖像生成模型，畫面表現細膩，支持文生圖、圖生圖。"}, "image-01-live": {"description": "圖像生成模型，畫面表現細膩，支持文生圖並進行畫風設定。"}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 第四代文字轉圖像模型系列"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 第四代文字轉圖像模型系列 超級版"}, "imagen4/preview": {"description": "Google 最高品質的圖像生成模型"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 提供多場景下的智能對話解決方案。"}, "internlm2.5-latest": {"description": "我們最新的模型系列，有著卓越的推理性能，支持 1M 的上下文長度以及更強的指令跟隨和工具調用能力。"}, "internlm3-latest": {"description": "我們最新的模型系列，有著卓越的推理性能，領跑同量級開源模型。默認指向我們最新發布的 InternLM3 系列模型"}, "internvl2.5-latest": {"description": "我們仍在維護的 InternVL2.5 版本，具備優異且穩定的性能。默認指向我們最新發布的 InternVL2.5 系列模型，當前指向 internvl2.5-78b。"}, "internvl3-latest": {"description": "我們最新發布的多模態大模型，具備更強的圖文理解能力、長時序圖片理解能力，性能比肩頂尖閉源模型。默認指向我們最新發布的 InternVL 系列模型，當前指向 internvl3-78b。"}, "irag-1.0": {"description": "百度自研的 iRAG（image based RAG），檢索增強的文生圖技術，將百度搜尋的億級圖片資源與強大的基礎模型能力結合，即可生成各種超真實的圖片，整體效果遠遠超過文生圖原生系統，去除了 AI 味道，且成本極低。iRAG 具備無幻覺、超真實、立等可取等特點。"}, "jamba-large": {"description": "我們最強大、最先進的模型，專為處理企業級複雜任務而設計，具備卓越的性能。"}, "jamba-mini": {"description": "在同級別中最高效的模型，兼顧速度與品質，具備更小的體積。"}, "jina-deepsearch-v1": {"description": "深度搜索結合了網路搜索、閱讀和推理，可進行全面調查。您可以將其視為一個代理，接受您的研究任務 - 它會進行廣泛搜索並經過多次迭代，然後才能給出答案。這個過程涉及持續的研究、推理和從各個角度解決問題。這與直接從預訓練數據生成答案的標準大模型以及依賴一次性表面搜索的傳統 RAG 系統有著根本的不同。"}, "kimi-k2": {"description": "Kimi-K2 是一款 Moonshot AI 推出的具備超強程式碼和 Agent 能力的 MoE 架構基礎模型，總參數 1T，激活參數 32B。在通用知識推理、程式設計、數學、Agent 等主要類別的基準性能測試中，K2 模型的性能超過其他主流開源模型。"}, "kimi-k2-0711-preview": {"description": "kimi-k2 是一款具備超強程式碼和 Agent 能力的 MoE 架構基礎模型，總參數 1T，激活參數 32B。在通用知識推理、程式設計、數學、Agent 等主要類別的基準性能測試中，K2 模型的性能超越其他主流開源模型。"}, "kimi-latest": {"description": "Kimi 智能助手產品使用最新的 Kimi 大模型，可能包含尚未穩定的特性。支持圖片理解，同時會自動根據請求的上下文長度選擇 8k/32k/128k 模型作為計費模型"}, "kimi-thinking-preview": {"description": "kimi-thinking-preview 模型是月之暗面提供的具有多模態推理能力和通用推理能力的多模態思考模型，它擅長深度推理，幫助解決更多更難的事情"}, "learnlm-1.5-pro-experimental": {"description": "LearnLM 是一個實驗性的、特定於任務的語言模型，經過訓練以符合學習科學原則，可在教學和學習場景中遵循系統指令，充當專家導師等。"}, "learnlm-2.0-flash-experimental": {"description": "LearnLM 是一個實驗性的、特定於任務的語言模型，經過訓練以符合學習科學原則，可在教學和學習場景中遵循系統指令，充當專家導師等。"}, "lite": {"description": "Spark Lite 是一款輕量級大語言模型，具備極低的延遲與高效的處理能力，完全免費開放，支持即時在線搜索功能。其快速響應的特性使其在低算力設備上的推理應用和模型微調中表現出色，為用戶帶來出色的成本效益和智能體驗，尤其在知識問答、內容生成及搜索場景下表現不俗。"}, "llama-2-7b-chat": {"description": "Llama2 是由 Meta 開發並開源的大型語言模型（LLM）系列，這是一組從 70 億到 700 億參數不同規模、經過預訓練和微調的生成式文本模型。架構層面，Llama2 是一個使用優化型轉換器架構的自動回歸語言模型。調整後的版本使用有監督的微調（SFT）和帶有人類反饋的強化學習（RLHF）以對齊人類對有用性和安全性的偏好。Llama2 較 Llama 系列在多種學術數據集上有着更加不俗的表現，為大量其他模型提供了設計和開發的思路。"}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B 提供更強大的 AI 推理能力，適合複雜應用，支持超多的計算處理並保證高效和準確率。"}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B 是一款高效能模型，提供了快速的文本生成能力，非常適合需要大規模效率和成本效益的應用場景。"}, "llama-3.1-instruct": {"description": "Llama 3.1 指令微調模型針對對話場景進行了優化，在常見的行業基準測試中，超越了許多現有的開源聊天模型。"}, "llama-3.2-11b-vision-instruct": {"description": "在高解析度圖像上表現優異的圖像推理能力，適用於視覺理解應用。"}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "llama-3.2-90b-vision-instruct": {"description": "適合視覺理解代理應用的高階圖像推理能力。"}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "llama-3.2-vision-instruct": {"description": "Llama 3.2-Vision 指令微調模型針對視覺辨識、圖像推理、圖像描述及回答與圖像相關的常規問題進行了最佳化。"}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 是 Llama 系列最先進的多語言開源大型語言模型，以極低成本體驗媲美 405B 模型的性能。基於 Transformer 結構，並透過監督微調（SFT）和人類反饋強化學習（RLHF）提升有用性和安全性。其指令調優版本專為多語言對話優化，在多項行業基準上表現優於眾多開源和封閉聊天模型。知識截止日期為 2023 年 12 月"}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 多語言大語言模型 (LLM) 是 70B（文本輸入/文本輸出）中的預訓練和指令調整生成模型。Llama 3.3 指令調整的純文本模型針對多語言對話用例進行了優化，並且在常見行業基準上優於許多可用的開源和封閉式聊天模型。"}, "llama-3.3-instruct": {"description": "Llama 3.3 指令微調模型針對對話場景進行了優化，在常見的行業基準測試中，超越了許多現有的開源聊天模型。"}, "llama3-70b-8192": {"description": "Meta Llama 3 70B 提供無與倫比的複雜性處理能力，為高要求項目量身定制。"}, "llama3-8b-8192": {"description": "Meta Llama 3 8B 帶來優質的推理效能，適合多場景應用需求。"}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use 提供強大的工具調用能力，支持複雜任務的高效處理。"}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use 是針對高效工具使用優化的模型，支持快速並行計算。"}, "llama3.1": {"description": "Llama 3.1 是 Meta 推出的領先模型，支持高達 405B 參數，可應用於複雜對話、多語言翻譯和數據分析領域。"}, "llama3.1:405b": {"description": "Llama 3.1 是 Meta 推出的領先模型，支持高達 405B 參數，可應用於複雜對話、多語言翻譯和數據分析領域。"}, "llama3.1:70b": {"description": "Llama 3.1 是 Meta 推出的領先模型，支持高達 405B 參數，可應用於複雜對話、多語言翻譯和數據分析領域。"}, "llava": {"description": "LLaVA 是結合視覺編碼器和 Vicuna 的多模態模型，用於強大的視覺和語言理解。"}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B 提供視覺處理能力融合，通過視覺信息輸入生成複雜輸出。"}, "llava:13b": {"description": "LLaVA 是結合視覺編碼器和 Vicuna 的多模態模型，用於強大的視覺和語言理解。"}, "llava:34b": {"description": "LLaVA 是結合視覺編碼器和 Vicuna 的多模態模型，用於強大的視覺和語言理解。"}, "mathstral": {"description": "MathΣtral 專為科學研究和數學推理設計，提供有效的計算能力和結果解釋。"}, "max-32k": {"description": "Spark Max 32K 配置了大上下文處理能力，更強的上下文理解和邏輯推理能力，支持32K tokens的文本輸入，適用於長文檔閱讀、私有知識問答等場景。"}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct 是由無問芯穹完全自主訓練的大語言模型。Megrez-3B-Instruct 旨在通過軟硬協同理念，打造一款極速推理、小巧精悍、極易上手的端側智能解決方案。"}, "meta-llama-3-70b-instruct": {"description": "一個強大的70億參數模型，在推理、編碼和廣泛的語言應用中表現出色。"}, "meta-llama-3-8b-instruct": {"description": "一個多功能的8億參數模型，優化了對話和文本生成任務。"}, "meta-llama-3.1-405b-instruct": {"description": "Llama 3.1指令調整的文本模型，針對多語言對話用例進行優化，並在許多可用的開源和封閉聊天模型中超越了常見行業基準。"}, "meta-llama-3.1-70b-instruct": {"description": "Llama 3.1指令調整的文本模型，針對多語言對話用例進行優化，並在許多可用的開源和封閉聊天模型中超越了常見行業基準。"}, "meta-llama-3.1-8b-instruct": {"description": "Llama 3.1指令調整的文本模型，針對多語言對話用例進行優化，並在許多可用的開源和封閉聊天模型中超越了常見行業基準。"}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) 提供優秀的語言處理能力和出色的互動體驗。"}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 提供優秀的語言處理能力和出色的互動體驗。"}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) 是功能強大的聊天模型，支持複雜的對話需求。"}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) 提供多語言支持，涵蓋豐富的領域知識。"}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 多語言大語言模型 ( LLM ) 是 70B（文本輸入/文本輸出）中的預訓練和指令調整生成模型。 Llama 3.3 指令調整的純文本模型針對多語言對話用例進行了優化，並且在常見行業基準上優於許多可用的開源和封閉式聊天模型。"}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite 適合需要高效能和低延遲的環境。"}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo 提供卓越的語言理解和生成能力，適合最苛刻的計算任務。"}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite 適合資源受限的環境，提供出色的平衡性能。"}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo 是一款高效能的大語言模型，支持廣泛的應用場景。"}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B 是預訓練和指令調整的強大機型。"}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405B 的 Llama 3.1 Turbo 模型，為大數據處理提供超大容量的上下文支持，在超大規模的人工智慧應用中表現突出。"}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 是 Meta 推出的領先模型，支持高達 405B 參數，可應用於複雜對話、多語言翻譯和數據分析領域。"}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70B 模型經過精細調整，適用於高負載應用，量化至 FP8 提供更高效的計算能力和準確性，確保在複雜場景中的卓越表現。"}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8B 模型採用 FP8 量化，支持高達 131,072 個上下文標記，是開源模型中的佼佼者，適合複雜任務，表現優異於許多行業基準。"}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct 優化用於高品質對話場景，在各類人類評估中表現優異。"}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct 優化了高品質對話場景，性能優於許多閉源模型。"}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct 專為高品質對話而設計，在人類評估中表現突出，特別適合高互動場景。"}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct 是 Meta 推出的最新版本，優化了高品質對話場景，表現優於許多領先的閉源模型。"}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 提供多語言支持，是業界領先的生成模型之一。"}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 旨在處理結合視覺和文本數據的任務。它在圖像描述和視覺問答等任務中表現出色，跨越了語言生成和視覺推理之間的鴻溝。"}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 是 Llama 系列最先進的多語言開源大型語言模型，以極低成本體驗媲美 405B 模型的性能。基於 Transformer 結構，並透過監督微調（SFT）和人類反饋強化學習（RLHF）提升有用性和安全性。其指令調優版本專為多語言對話優化，在多項行業基準上表現優於眾多開源和封閉聊天模型。知識截止日期為 2023 年 12 月"}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 是 Llama 系列最先進的多語言開源大型語言模型，以極低成本體驗媲美 405B 模型的性能。基於 Transformer 結構，並透過監督微調（SFT）和人類反饋強化學習（RLHF）提升有用性和安全性。其指令調優版本專為多語言對話優化，在多項行業基準上表現優於眾多開源和封閉聊天模型。知識截止日期為 2023 年 12 月"}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct 是 Llama 3.1 Instruct 模型中最大、最強大的模型，是一款高度先進的對話推理和合成數據生成模型，也可以用作在特定領域進行專業持續預訓練或微調的基礎。Llama 3.1 提供的多語言大型語言模型 (LLMs) 是一組預訓練的、指令調整的生成模型，包括 8B、70B 和 405B 大小 (文本輸入/輸出)。Llama 3.1 指令調整的文本模型 (8B、70B、405B) 專為多語言對話用例進行了優化，並在常見的行業基準測試中超過了許多可用的開源聊天模型。Llama 3.1 旨在用於多種語言的商業和研究用途。指令調整的文本模型適用於類似助手的聊天，而預訓練模型可以適應各種自然語言生成任務。Llama 3.1 模型還支持利用其模型的輸出來改進其他模型，包括合成數據生成和精煉。Llama 3.1 是使用優化的變壓器架構的自回歸語言模型。調整版本使用監督微調 (SFT) 和帶有人類反饋的強化學習 (RLHF) 來符合人類對幫助性和安全性的偏好。"}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Meta Llama 3.1 70B Instruct的更新版，包括擴展的128K上下文長度、多語言性和改進的推理能力。Llama 3.1提供的多語言大型語言模型(LLMs)是一組預訓練的、指令調整的生成模型，包括8B、70B和405B大小(文本輸入/輸出)。Llama 3.1指令調整的文本模型(8B、70B、405B)專為多語言對話用例進行了優化，並在常見的行業基準測試中超過了許多可用的開源聊天模型。Llama 3.1旨在用於多種語言的商業和研究用途。指令調整的文本模型適用於類似助手的聊天，而預訓練模型可以適應各種自然語言生成任務。Llama 3.1模型還支持利用其模型的輸出來改進其他模型，包括合成數據生成和精煉。Llama 3.1是使用優化的變壓器架構的自回歸語言模型。調整版本使用監督微調(SFT)和帶有人類反饋的強化學習(RLHF)來符合人類對幫助性和安全性的偏好。"}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Meta Llama 3.1 8B Instruct的更新版，包括擴展的128K上下文長度、多語言性和改進的推理能力。Llama 3.1提供的多語言大型語言模型(LLMs)是一組預訓練的、指令調整的生成模型，包括8B、70B和405B大小(文本輸入/輸出)。Llama 3.1指令調整的文本模型(8B、70B、405B)專為多語言對話用例進行了優化，並在常見的行業基準測試中超過了許多可用的開源聊天模型。Llama 3.1旨在用於多種語言的商業和研究用途。指令調整的文本模型適用於類似助手的聊天，而預訓練模型可以適應各種自然語言生成任務。Llama 3.1模型還支持利用其模型的輸出來改進其他模型，包括合成數據生成和精煉。Llama 3.1是使用優化的變壓器架構的自回歸語言模型。調整版本使用監督微調(SFT)和帶有人類反饋的強化學習(RLHF)來符合人類對幫助性和安全性的偏好。"}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 是一款面向開發者、研究人員和企業的開放大型語言模型 (LLM)，旨在幫助他們構建、實驗並負責任地擴展他們的生成 AI 想法。作為全球社區創新的基礎系統的一部分，它非常適合內容創建、對話 AI、語言理解、研發和企業應用。"}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 是一款面向開發者、研究人員和企業的開放大型語言模型 (LLM)，旨在幫助他們構建、實驗並負責任地擴展他們的生成 AI 想法。作為全球社區創新的基礎系統的一部分，它非常適合計算能力和資源有限、邊緣設備和更快的訓練時間。"}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "在高解析度影像上表現出色的影像推理能力，適用於視覺理解應用。"}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "適用於視覺理解代理應用的高階影像推理能力。"}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 是 Llama 系列最先進的多語言開源大型語言模型，以極低成本體驗媲美 405B 模型的效能。基於 Transformer 結構，並透過監督微調（SFT）和人類回饋強化學習（RLHF）提升實用性和安全性。其指令調校版本專為多語言對話優化，在多項產業基準上表現優於眾多開源和封閉聊天模型。知識截止日期為 2023 年 12 月。"}, "meta/Meta-Llama-3-70B-Instruct": {"description": "一個強大的 700 億參數模型，在推理、編碼和廣泛的語言應用方面表現出色。"}, "meta/Meta-Llama-3-8B-Instruct": {"description": "一個多功能的 80 億參數模型，針對對話和文本生成任務進行優化。"}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 指令調校的文本模型，針對多語言對話用例進行優化，在許多可用的開源和封閉聊天模型中，在常見產業基準上表現優異。"}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 指令調校的文本模型，針對多語言對話用例進行優化，在許多可用的開源和封閉聊天模型中，在常見產業基準上表現優異。"}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 指令調校的文本模型，針對多語言對話用例進行優化，在許多可用的開源和封閉聊天模型中，在常見產業基準上表現優異。"}, "meta/llama-3.1-405b-instruct": {"description": "高級 LLM，支持合成數據生成、知識蒸餾和推理，適用於聊天機器人、編程和特定領域任務。"}, "meta/llama-3.1-70b-instruct": {"description": "賦能複雜對話，具備卓越的上下文理解、推理能力和文本生成能力。"}, "meta/llama-3.1-8b-instruct": {"description": "先進的最尖端模型，具備語言理解、卓越的推理能力和文本生成能力。"}, "meta/llama-3.2-11b-vision-instruct": {"description": "尖端的視覺-語言模型，擅長從圖像中進行高品質推理。"}, "meta/llama-3.2-1b-instruct": {"description": "先進的最尖端小型語言模型，具備語言理解、卓越的推理能力和文本生成能力。"}, "meta/llama-3.2-3b-instruct": {"description": "先進的最尖端小型語言模型，具備語言理解、卓越的推理能力和文本生成能力。"}, "meta/llama-3.2-90b-vision-instruct": {"description": "尖端的視覺-語言模型，擅長從圖像中進行高品質推理。"}, "meta/llama-3.3-70b-instruct": {"description": "先進的 LLM，擅長推理、數學、常識和函數調用。"}, "microsoft/Phi-3-medium-128k-instruct": {"description": "相同的 Phi-3-medium 模型，但具有更大的上下文大小，適用於 RAG 或少量提示。"}, "microsoft/Phi-3-medium-4k-instruct": {"description": "一個 140 億參數模型，品質優於 Phi-3-mini，重點關注高品質、推理密集型資料。"}, "microsoft/Phi-3-mini-128k-instruct": {"description": "相同的 Phi-3-mini 模型，但具有更大的上下文大小，適用於 RAG 或少量提示。"}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Phi-3 家族中最小的成員，針對品質和低延遲進行優化。"}, "microsoft/Phi-3-small-128k-instruct": {"description": "相同的 Phi-3-small 模型，但具有更大的上下文大小，適用於 RAG 或少量提示。"}, "microsoft/Phi-3-small-8k-instruct": {"description": "一個 70 億參數模型，品質優於 Phi-3-mini，重點關注高品質、推理密集型資料。"}, "microsoft/Phi-3.5-mini-instruct": {"description": "Phi-3-mini 模型的更新版。"}, "microsoft/Phi-3.5-vision-instruct": {"description": "Phi-3-vision 模型的更新版。"}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 是微軟AI提供的語言模型，在複雜對話、多語言、推理和智能助手領域表現尤為出色。"}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B 是微軟 AI 最先進的 Wizard 模型，顯示出極其競爭力的表現。"}, "minicpm-v": {"description": "MiniCPM-V 是 OpenBMB 推出的新一代多模態大模型，具備卓越的 OCR 識別和多模態理解能力，支持廣泛的應用場景。"}, "ministral-3b-latest": {"description": "Ministral 3B 是 Mistral 的全球頂尖邊緣模型。"}, "ministral-8b-latest": {"description": "Ministral 8B 是 Mistral 的性價比極高的邊緣模型。"}, "mistral": {"description": "Mistral 是 Mistral AI 發布的 7B 模型，適合多變的語言處理需求。"}, "mistral-ai/Mistral-Large-2411": {"description": "Mistral 的旗艦模型，適合需要大規模推理能力或高度專業化的複雜任務（合成文本生成、程式碼生成、RAG 或代理）。"}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo 是一種尖端的語言模型（LLM），在其尺寸類別中擁有最先進的推理、世界知識和編碼能力。"}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small 可用於任何需要高效率和低延遲的基於語言的任務。"}, "mistral-large": {"description": "Mixtral Large 是 Mistral 的旗艦模型，結合代碼生成、數學和推理的能力，支持 128k 上下文窗口。"}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 是一款先進的稠密大型語言模型（LLM），擁有 1230 億參數，具備最先進的推理、知識和編碼能力。"}, "mistral-large-latest": {"description": "Mistral Large 是旗艦大模型，擅長多語言任務、複雜推理和代碼生成，是高端應用的理想選擇。"}, "mistral-medium-latest": {"description": "Mistral Medium 3 以 8 倍的成本提供最先進的性能，並從根本上簡化了企業部署。"}, "mistral-nemo": {"description": "Mistral Nemo 由 Mistral AI 和 NVIDIA 合作推出，是高效性能的 12B 模型。"}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 大型語言模型（LLM）是 Mistral-Nemo-Base-2407 的指令微調版本。"}, "mistral-small": {"description": "Mistral Small可用於任何需要高效率和低延遲的語言任務。"}, "mistral-small-latest": {"description": "Mistral Small是一個成本效益高、快速且可靠的選擇，適用於翻譯、摘要和情感分析等用例。"}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct 以高性能著稱，適用於多種語言任務。"}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B 是按需 fine-tuning 的模型，為任務提供優化解答。"}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 提供高效的計算能力和自然語言理解，適合廣泛的應用。"}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B 是一款緊湊但高效能的模型，擅長批次處理和簡單任務，如分類和文本生成，具有良好的推理能力。"}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) 是一款超級大語言模型，支持極高的處理需求。"}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B 是預訓練的稀疏混合專家模型，用於通用性文本任務。"}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B 是一個稀疏專家模型，利用多個參數提高推理速度，適合處理多語言和代碼生成任務。"}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct 是一款兼具速度優化和長上下文支持的高性能行業標準模型。"}, "mistralai/mistral-nemo": {"description": "Mistral Nemo 是多語言支持和高性能編程的7.3B參數模型。"}, "mixtral": {"description": "Mixtral 是 Mistral AI 的專家模型，具有開源權重，並在代碼生成和語言理解方面提供支持。"}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B 提供高容錯的並行計算能力，適合複雜任務。"}, "mixtral:8x22b": {"description": "Mixtral 是 Mistral AI 的專家模型，具有開源權重，並在代碼生成和語言理解方面提供支持。"}, "moonshot-v1-128k": {"description": "Moonshot V1 128K 是一款擁有超長上下文處理能力的模型，適用於生成超長文本，滿足複雜的生成任務需求，能夠處理多達 128,000 個 tokens 的內容，非常適合科研、學術和大型文檔生成等應用場景。"}, "moonshot-v1-128k-vision-preview": {"description": "Kimi 視覺模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能夠理解圖片內容，包括圖片文字、圖片顏色和物體形狀等內容。"}, "moonshot-v1-32k": {"description": "Moonshot V1 32K 提供中等長度的上下文處理能力，能夠處理 32,768 個 tokens，特別適合生成各種長文檔和複雜對話，應用於內容創作、報告生成和對話系統等領域。"}, "moonshot-v1-32k-vision-preview": {"description": "Kimi 視覺模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能夠理解圖片內容，包括圖片文字、圖片顏色和物體形狀等內容。"}, "moonshot-v1-8k": {"description": "Moonshot V1 8K 專為生成短文本任務設計，具有高效的處理性能，能夠處理 8,192 個 tokens，非常適合簡短對話、速記和快速內容生成。"}, "moonshot-v1-8k-vision-preview": {"description": "Kimi 視覺模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能夠理解圖片內容，包括圖片文字、圖片顏色和物體形狀等內容。"}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto 可以根據當前上下文佔用的 Tokens 數量來選擇合適的模型"}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B 是一款開源程式碼大型模型，經過大規模強化學習優化，能輸出穩健、可直接投產的補丁。該模型在 SWE-bench Verified 上取得 60.4 % 的新高分，刷新了開源模型在缺陷修復、程式碼審查等自動化軟體工程任務上的紀錄。"}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 是一款具備超強程式碼和 Agent 能力的 MoE 架構基礎模型，總參數 1T，激活參數 32B。在通用知識推理、程式設計、數學、Agent 等主要類別的基準性能測試中，K2 模型的性能超過其他主流開源模型。"}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 是一款具備超強程式碼與代理能力的 MoE 架構基礎模型，總參數量達 1T，啟用參數 32B。在通用知識推理、程式設計、數學、代理等主要類別的基準效能測試中，K2 模型的表現超越其他主流開源模型。"}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B 是 Nous Hermes 2 的升級版本，包含最新的內部開發的數據集。"}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B 是由 NVIDIA 定制的大型語言模型，旨在提高 LLM 生成的回應對用戶查詢的幫助程度。該模型在 Arena Hard、AlpacaEval 2 LC 和 GPT-4-Turbo MT-Bench 等基準測試中表現出色，截至 2024 年 10 月 1 日，在所有三個自動對齊基準測試中排名第一。該模型使用 RLHF（特別是 REINFORCE）、Llama-3.1-Nemotron-70B-Reward 和 HelpSteer2-Preference 提示在 Llama-3.1-70B-Instruct 模型基礎上進行訓練"}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "獨特的語言模型，提供無與倫比的準確性和效率表現。"}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct 是 NVIDIA 定制的大型語言模型，旨在提高 LLM 生成的響應的幫助性。"}, "o1": {"description": "專注於高級推理和解決複雜問題，包括數學和科學任務。非常適合需要深入上下文理解和代理工作流程的應用程序。"}, "o1-mini": {"description": "o1-mini是一款針對程式設計、數學和科學應用場景而設計的快速、經濟高效的推理模型。該模型具有128K上下文和2023年10月的知識截止日期。"}, "o1-preview": {"description": "o1是OpenAI新的推理模型，適用於需要廣泛通用知識的複雜任務。該模型具有128K上下文和2023年10月的知識截止日期。"}, "o1-pro": {"description": "o1 系列模型經過強化學習訓練，能夠在回答前進行思考，並執行複雜的推理任務。o1-pro 模型使用了更多計算資源，以進行更深入的思考，從而持續提供更優質的回答。"}, "o3": {"description": "o3 是一款全能強大的模型，在多個領域表現出色。它為數學、科學、程式設計和視覺推理任務樹立了新標杆。它也擅長技術寫作和指令遵循。用戶可利用它分析文本、程式碼和圖像，解決多步驟的複雜問題。"}, "o3-deep-research": {"description": "o3-deep-research 是我們最先進的深度研究模型，專為處理複雜的多步驟研究任務而設計。它可以從網際網路搜尋和綜合資訊，也可以透過 MCP 連接器存取並利用你的自有資料。"}, "o3-mini": {"description": "o3-mini 是我們最新的小型推理模型，在與 o1-mini 相同的成本和延遲目標下提供高智能。"}, "o3-pro": {"description": "o3-pro 模型使用更多的計算來更深入地思考並始終提供更好的答案，僅支援 Responses API 下使用。"}, "o4-mini": {"description": "o4-mini 是我們最新的小型 o 系列模型。它專為快速有效的推理而優化，在編碼和視覺任務中表現出極高的效率和性能。"}, "o4-mini-deep-research": {"description": "o4-mini-deep-research 是我們更快速、更實惠的深度研究模型——非常適合處理複雜的多步驟研究任務。它可以從網際網路搜尋和綜合資訊，也可以透過 MCP 連接器存取並利用你的自有資料。"}, "open-codestral-mamba": {"description": "Codestral Mamba 是專注於代碼生成的 Mamba 2 語言模型，為先進的代碼和推理任務提供強力支持。"}, "open-mistral-7b": {"description": "Mistral 7B 是一款緊湊但高性能的模型，擅長批量處理和簡單任務，如分類和文本生成，具有良好的推理能力。"}, "open-mistral-nemo": {"description": "Mistral Nemo 是一個與 Nvidia 合作開發的 12B 模型，提供出色的推理和編碼性能，易於集成和替換。"}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B 是一個更大的專家模型，專注於複雜任務，提供出色的推理能力和更高的吞吐量。"}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B 是一個稀疏專家模型，利用多個參數提高推理速度，適合處理多語言和代碼生成任務。"}, "openai/gpt-4.1": {"description": "GPT-4.1 是我們用於複雜任務的旗艦模型。它非常適合跨領域解決問題。"}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini 提供了智能、速度和成本之間的平衡，使其成為許多用例中有吸引力的模型。"}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano 是最快、最具成本效益的 GPT-4.1 模型。"}, "openai/gpt-4o": {"description": "ChatGPT-4o 是一款動態模型，實時更新以保持當前最新版本。它結合了強大的語言理解與生成能力，適合於大規模應用場景，包括客戶服務、教育和技術支持。"}, "openai/gpt-4o-mini": {"description": "GPT-4o mini是OpenAI在GPT-4 Omni之後推出的最新模型，支持圖文輸入並輸出文本。作為他們最先進的小型模型，它比其他近期的前沿模型便宜很多，並且比GPT-3.5 Turbo便宜超過60%。它保持了最先進的智能，同時具有顯著的性價比。GPT-4o mini在MMLU測試中獲得了82%的得分，目前在聊天偏好上排名高於GPT-4。"}, "openai/o1": {"description": "o1 是 OpenAI 新的推理模型，支援圖文輸入並輸出文本，適用於需要廣泛通用知識的複雜任務。該模型具有 200K 上下文和 2023 年 10 月的知識截止日期。"}, "openai/o1-mini": {"description": "o1-mini是一款針對程式設計、數學和科學應用場景而設計的快速、經濟高效的推理模型。該模型具有128K上下文和2023年10月的知識截止日期。"}, "openai/o1-preview": {"description": "o1是OpenAI新的推理模型，適用於需要廣泛通用知識的複雜任務。該模型具有128K上下文和2023年10月的知識截止日期。"}, "openai/o3": {"description": "o3 是一款全能強大的模型，在多個領域表現出色。它為數學、科學、程式設計和視覺推理任務樹立了新標杆。它也擅長技術寫作和指令遵循。用戶可利用它分析文本、程式碼和圖像，解決多步驟的複雜問題。"}, "openai/o3-mini": {"description": "o3-mini 在與 o1-mini 相同的成本和延遲目標下提供高智能。"}, "openai/o3-mini-high": {"description": "o3-mini 高推理等級版，在與 o1-mini 相同的成本和延遲目標下提供高智能。"}, "openai/o4-mini": {"description": "o4-mini 專為快速有效的推理而優化，在編碼和視覺任務中表現出極高的效率和性能。"}, "openai/o4-mini-high": {"description": "o4-mini 高推理等級版，專為快速有效的推理而優化，在編碼和視覺任務中表現出極高的效率和性能。"}, "openrouter/auto": {"description": "根據上下文長度、主題和複雜性，你的請求將發送到 Llama 3 70B Instruct、Claude 3.5 Sonnet（自我調節）或 GPT-4o。"}, "phi3": {"description": "Phi-3 是微軟推出的輕量級開放模型，適用於高效集成和大規模知識推理。"}, "phi3:14b": {"description": "Phi-3 是微軟推出的輕量級開放模型，適用於高效集成和大規模知識推理。"}, "pixtral-12b-2409": {"description": "Pixtral模型在圖表和圖理解、文檔問答、多模態推理和指令遵循等任務上表現出強大的能力，能夠以自然分辨率和寬高比攝入圖像，還能夠在長達128K令牌的長上下文窗口中處理任意數量的圖像。"}, "pixtral-large-latest": {"description": "Pixtral Large 是一款擁有 1240 億參數的開源多模態模型，基於 Mistral Large 2 構建。這是我們多模態家族中的第二款模型，展現了前沿水平的圖像理解能力。"}, "pro-128k": {"description": "Spark Pro 128K 配置了特大上下文處理能力，能夠處理多達128K的上下文信息，特別適合需通篇分析和長期邏輯關聯處理的長文內容，可在複雜文本溝通中提供流暢一致的邏輯與多樣的引用支持。"}, "qvq-72b-preview": {"description": "QVQ模型是由 Qwen 團隊開發的實驗性研究模型，專注於提升視覺推理能力，尤其在數學推理領域。"}, "qvq-max": {"description": "通義千問 QVQ 視覺推理模型，支援視覺輸入及思維鏈輸出，在數學、程式設計、視覺分析、創作以及通用任務上都展現了更強的能力。"}, "qvq-plus": {"description": "視覺推理模型。支援視覺輸入及思維鏈輸出，繼qvq-max模型後推出的plus版本，相較於qvq-max模型，qvq-plus系列模型推理速度更快，效果和成本更均衡。"}, "qwen-coder-plus": {"description": "通義千問程式碼模型。"}, "qwen-coder-turbo": {"description": "通義千問程式碼模型。"}, "qwen-coder-turbo-latest": {"description": "通義千問代碼模型。"}, "qwen-long": {"description": "通義千問超大規模語言模型，支持長文本上下文，以及基於長文檔、多文檔等多個場景的對話功能。"}, "qwen-math-plus": {"description": "通義千問數學模型是專門用於數學解題的語言模型。"}, "qwen-math-plus-latest": {"description": "通義千問數學模型是專門用於數學解題的語言模型。"}, "qwen-math-turbo": {"description": "通義千問數學模型是專門用於數學解題的語言模型。"}, "qwen-math-turbo-latest": {"description": "通義千問數學模型是專門用於數學解題的語言模型。"}, "qwen-max": {"description": "通義千問千億級別超大規模語言模型，支持中文、英文等不同語言輸入，當前通義千問 2.5 產品版本背後的 API 模型。"}, "qwen-omni-turbo": {"description": "Qwen-Omni 系列模型支援輸入多種模態的資料，包括影片、音訊、圖片、文本，並輸出音訊與文本。"}, "qwen-plus": {"description": "通義千問超大規模語言模型增強版，支持中文、英文等不同語言輸入。"}, "qwen-turbo": {"description": "通義千問超大規模語言模型，支持中文、英文等不同語言輸入。"}, "qwen-vl-chat-v1": {"description": "通義千問VL支持靈活的交互方式，包括多圖、多輪問答、創作等能力的模型。"}, "qwen-vl-max": {"description": "通義千問超大規模視覺語言模型。相比增強版，再次提升視覺推理能力和指令遵循能力，提供更高的視覺感知和認知水準。"}, "qwen-vl-max-latest": {"description": "通義千問超大規模視覺語言模型。相比增強版，再次提升視覺推理能力和指令遵循能力，提供更高的視覺感知和認知水平。"}, "qwen-vl-ocr": {"description": "通義千問 OCR 是文字擷取專有模型，專注於文件、表格、試題、手寫體文字等類型影像的文字擷取能力。它能夠識別多種文字，目前支援的語言有：漢語、英語、法語、日語、韓語、德語、俄語、義大利語、越南語、阿拉伯語。"}, "qwen-vl-plus": {"description": "通義千問大規模視覺語言模型增強版。大幅提升細節識別能力和文字識別能力，支援超百萬像素解析度和任意長寬比規格的影像。"}, "qwen-vl-plus-latest": {"description": "通義千問大規模視覺語言模型增強版。大幅提升細節識別能力和文字識別能力，支持超百萬像素解析度和任意長寬比規格的圖像。"}, "qwen-vl-v1": {"description": "以Qwen-7B語言模型初始化，添加圖像模型，圖像輸入分辨率為448的預訓練模型。"}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2是全新的Qwen大型語言模型系列。Qwen2 7B是一個基於transformer的模型，在語言理解、多語言能力、編程、數學和推理方面表現出色。"}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 是全新的大型語言模型系列，具有更強的理解和生成能力。"}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL是Qwen-VL模型的最新迭代版本，在視覺理解基準測試中達到了最先進的性能，包括MathVista、DocVQA、RealWorldQA和MTVQA等。Qwen2-VL能夠理解超過20分鐘的視頻，用於高質量的基於視頻的問答、對話和內容創作。它還具備複雜推理和決策能力，可以與移動設備、機器人等集成，基於視覺環境和文本指令進行自動操作。除了英語和中文，Qwen2-VL現在還支持理解圖像中不同語言的文本，包括大多數歐洲語言、日語、韓語、阿拉伯語和越南語等。"}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct是阿里雲發布的最新大語言模型系列之一。該72B模型在編碼和數學等領域具有顯著改進的能力。該模型還提供了多語言支持，覆蓋超過29種語言，包括中文、英文等。模型在指令跟隨、理解結構化數據以及生成結構化輸出（尤其是JSON）方面都有顯著提升。"}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct是阿里雲發布的最新大語言模型系列之一。該32B模型在編碼和數學等領域具有顯著改進的能力。該模型提供了多語言支持，覆蓋超過29種語言，包括中文、英文等。模型在指令跟隨、理解結構化數據以及生成結構化輸出（尤其是JSON）方面都有顯著提升。"}, "qwen/qwen2.5-7b-instruct": {"description": "面向中文和英文的 LLM，針對語言、編程、數學、推理等領域。"}, "qwen/qwen2.5-coder-32b-instruct": {"description": "高級 LLM，支持代碼生成、推理和修復，涵蓋主流編程語言。"}, "qwen/qwen2.5-coder-7b-instruct": {"description": "強大的中型代碼模型，支持 32K 上下文長度，擅長多語言編程。"}, "qwen/qwen3-14b": {"description": "Qwen3-14B 是 Qwen3 系列中一個密集的 148 億參數因果語言模型，專為複雜推理和高效對話而設計。它支持在用於數學、編程和邏輯推理等任務的「思考」模式與用於通用對話的「非思考」模式之間無縫切換。該模型經過微調，可用於指令遵循、代理工具使用、創意寫作以及跨 100 多種語言和方言的多語言任務。它原生處理 32K 令牌上下文，並可使用基於 YaRN 的擴展擴展到 131K 令牌。"}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B 是 Qwen3 系列中一個密集的 148 億參數因果語言模型，專為複雜推理和高效對話而設計。它支持在用於數學、編程和邏輯推理等任務的「思考」模式與用於通用對話的「非思考」模式之間無縫切換。該模型經過微調，可用於指令遵循、代理工具使用、創意寫作以及跨 100 多種語言和方言的多語言任務。它原生處理 32K 令牌上下文，並可使用基於 YaRN 的擴展擴展到 131K 令牌。"}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B 是由 Qwen 開發的 235B 參數專家混合 (MoE) 模型，每次前向傳遞激活 22B 參數。它支持在用於複雜推理、數學和代碼任務的「思考」模式與用於一般對話效率的「非思考」模式之間無縫切換。該模型展示了強大的推理能力、多語言支持（100 多種語言和方言）、高級指令遵循和代理工具調用能力。它原生處理 32K 令牌上下文窗口，並使用基於 YaRN 的擴展擴展到 131K 令牌。"}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B 是由 Qwen 開發的 235B 參數專家混合 (MoE) 模型，每次前向傳遞激活 22B 參數。它支持在用於複雜推理、數學和代碼任務的「思考」模式與用於一般對話效率的「非思考」模式之間無縫切換。該模型展示了強大的推理能力、多語言支持（100 多種語言和方言）、高級指令遵循和代理工具調用能力。它原生處理 32K 令牌上下文窗口，並使用基於 YaRN 的擴展擴展到 131K 令牌。"}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 是 Qwen 大型語言模型系列的最新一代，具有密集和專家混合 (MoE) 架構，在推理、多語言支持和高級代理任務方面表現出色。其在複雜推理的思考模式和高效對話的非思考模式之間無縫切換的獨特能力確保了多功能、高品質的性能。\n\nQwen3 顯著優於 QwQ 和 Qwen2.5 等先前模型，提供卓越的數學、編碼、常識推理、創意寫作和互動對話能力。Qwen3-30B-A3B 變體包含 305 億個參數（33 億個激活參數）、48 層、128 個專家（每個任務激活 8 個），並支持高達 131K 令牌上下文（使用 YaRN），為開源模型樹立了新標準。"}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 是 Qwen 大型語言模型系列的最新一代，具有密集和專家混合 (MoE) 架構，在推理、多語言支持和高級代理任務方面表現出色。其在複雜推理的思考模式和高效對話的非思考模式之間無縫切換的獨特能力確保了多功能、高品質的性能。\n\nQwen3 顯著優於 QwQ 和 Qwen2.5 等先前模型，提供卓越的數學、編碼、常識推理、創意寫作和互動對話能力。Qwen3-30B-A3B 變體包含 305 億個參數（33 億個激活參數）、48 層、128 個專家（每個任務激活 8 個），並支持高達 131K 令牌上下文（使用 YaRN），為開源模型樹立了新標準。"}, "qwen/qwen3-32b": {"description": "Qwen3-32B 是 Qwen3 系列中一個密集的 328 億參數因果語言模型，針對複雜推理和高效對話進行了優化。它支持在用於數學、編碼和邏輯推理等任務的「思考」模式與用於更快、通用對話的「非思考」模式之間無縫切換。該模型在指令遵循、代理工具使用、創意寫作以及跨 100 多種語言和方言的多語言任務中表現出強大的性能。它原生處理 32K 令牌上下文，並可使用基於 YaRN 的擴展擴展到 131K 令牌。"}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B 是 Qwen3 系列中一個密集的 328 億參數因果語言模型，針對複雜推理和高效對話進行了優化。它支持在用於數學、編碼和邏輯推理等任務的「思考」模式與用於更快、通用對話的「非思考」模式之間無縫切換。該模型在指令遵循、代理工具使用、創意寫作以及跨 100 多種語言和方言的多語言任務中表現出強大的性能。它原生處理 32K 令牌上下文，並可使用基於 YaRN 的擴展擴展到 131K 令牌。"}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B 是 Qwen3 系列中一個密集的 82 億參數因果語言模型，專為推理密集型任務和高效對話而設計。它支持在用於數學、編碼和邏輯推理的「思考」模式與用於一般對話的「非思考」模式之間無縫切換。該模型經過微調，可用於指令遵循、代理集成、創意寫作以及跨 100 多種語言和方言的多語言使用。它原生支持 32K 令牌上下文窗口，並可通過 YaRN 擴展到 131K 令牌。"}, "qwen2": {"description": "Qwen2 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2-72b-instruct": {"description": "Qwen2 是 Qwen 團隊推出的新一代大型語言模型系列。它基於 Transformer 架構，並採用 SwiGLU 激活函數、注意力 QKV 偏置(attention QKV bias)、群組查詢注意力(group query attention)、滑動窗口注意力(mixture of sliding window attention)與全注意力的混合等技術。此外，Qwen 團隊還改進了適應多種自然語言和代碼的分詞器。"}, "qwen2-7b-instruct": {"description": "Qwen2 是 Qwen 團隊推出的新一代大型語言模型系列。它基於 Transformer 架構，並採用 SwiGLU 激活函數、注意力 QKV 偏置(attention QKV bias)、群組查詢注意力(group query attention)、滑動視窗注意力(mixture of sliding window attention)與全注意力的混合等技術。此外，Qwen 團隊還改進了適應多種自然語言和程式碼的分詞器。"}, "qwen2.5": {"description": "Qwen2.5 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2.5-14b-instruct": {"description": "通義千問2.5對外開源的14B規模的模型。"}, "qwen2.5-14b-instruct-1m": {"description": "通義千問2.5對外開源的72B規模的模型。"}, "qwen2.5-32b-instruct": {"description": "通義千問2.5對外開源的32B規模的模型。"}, "qwen2.5-72b-instruct": {"description": "通義千問2.5對外開源的72B規模的模型。"}, "qwen2.5-7b-instruct": {"description": "通義千問2.5對外開源的7B規模的模型。"}, "qwen2.5-coder-1.5b-instruct": {"description": "通義千問代碼模型開源版。"}, "qwen2.5-coder-14b-instruct": {"description": "通義千問程式碼模型開源版。"}, "qwen2.5-coder-32b-instruct": {"description": "通義千問代碼模型開源版。"}, "qwen2.5-coder-7b-instruct": {"description": "通義千問代碼模型開源版。"}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder 是 Qwen 系列中最新的程式碼專用大型語言模型（前身為 CodeQwen）。"}, "qwen2.5-instruct": {"description": "Qwen2.5 是 Qwen 大型語言模型的最新系列。對於 Qwen2.5，我們發佈了多個基礎語言模型和指令微調語言模型，參數範圍從 5 億到 72 億不等。"}, "qwen2.5-math-1.5b-instruct": {"description": "<PERSON><PERSON>-Math 模型具有強大的數學解題能力。"}, "qwen2.5-math-72b-instruct": {"description": "<PERSON><PERSON>-Math模型具有強大的數學解題能力。"}, "qwen2.5-math-7b-instruct": {"description": "<PERSON><PERSON>-Math模型具有強大的數學解題能力。"}, "qwen2.5-omni-7b": {"description": "Qwen-Omni 系列模型支援輸入多種模態的數據，包括視頻、音頻、圖片、文本，並輸出音頻與文本。"}, "qwen2.5-vl-32b-instruct": {"description": "Qwen2.5-VL 系列模型提升了模型的智能水準、實用性和適用性，使其在自然對話、內容創作、專業知識服務及程式碼開發等場景中表現更優。32B 版本使用了強化學習技術優化模型，與 Qwen2.5 VL 系列的其他模型相比，提供了更符合人類偏好的輸出風格、複雜數學問題的推理能力，以及影像細粒度理解與推理能力。"}, "qwen2.5-vl-72b-instruct": {"description": "指令跟隨、數學、解題、代碼整體提升，萬物識別能力提升，支持多樣格式直接精準定位視覺元素，支持對長視頻文件（最長10分鐘）進行理解和秒級別的事件時刻定位，能理解時間先後和快慢，基於解析和定位能力支持操控OS或Mobile的Agent，關鍵信息抽取能力和Json格式輸出能力強，此版本為72B版本，本系列能力最強的版本。"}, "qwen2.5-vl-7b-instruct": {"description": "指令跟隨、數學、解題、代碼整體提升，萬物識別能力提升，支持多樣格式直接精準定位視覺元素，支持對長視頻文件（最長10分鐘）進行理解和秒級別的事件時刻定位，能理解時間先後和快慢，基於解析和定位能力支持操控OS或Mobile的Agent，關鍵信息抽取能力和Json格式輸出能力強，此版本為72B版本，本系列能力最強的版本。"}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL 是 Qwen 模型系列中最新版本的視覺語言模型。"}, "qwen2.5:0.5b": {"description": "Qwen2.5 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2.5:1.5b": {"description": "Qwen2.5 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2.5:72b": {"description": "Qwen2.5 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2:0.5b": {"description": "Qwen2 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2:1.5b": {"description": "Qwen2 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen2:72b": {"description": "Qwen2 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen3": {"description": "Qwen3 是阿里巴巴的新一代大規模語言模型，以優異的性能支持多元化的應用需求。"}, "qwen3-0.6b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-1.7b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-14b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-235b-a22b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-235b-a22b-instruct-2507": {"description": "基於 Qwen3 的非思考模式開源模型，相較上一版本（通義千問3-235B-A22B）主觀創作能力與模型安全性均有小幅度提升。"}, "qwen3-235b-a22b-thinking-2507": {"description": "基於 Qwen3 的思考模式開源模型，相較上一版本（通義千問3-235B-A22B）邏輯能力、通用能力、知識增強及創作能力均有大幅提升，適用於高難度強推理場景。"}, "qwen3-30b-a3b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-30b-a3b-instruct-2507": {"description": "相較上一版本（Qwen3-30B-A3B）中英文和多語言整體通用能力有大幅提升。主觀開放類任務專項優化，顯著更加符合用戶偏好，能夠提供更有幫助性的回覆。"}, "qwen3-30b-a3b-thinking-2507": {"description": "基於Qwen3的思考模式開源模型，相較上一版本（通義千問3-30B-A3B）邏輯能力、通用能力、知識增強及創作能力均有大幅提升，適用於高難度強推理場景。"}, "qwen3-32b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-4b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-8b": {"description": "Qwen3是一款能力大幅提升的新一代通義千問大模型，在推理、通用、Agent和多語言等多個核心能力上均達到業界領先水平，並支持思考模式切換。"}, "qwen3-coder-480b-a35b-instruct": {"description": "通義千問程式碼模型開源版。最新的 qwen3-coder-480b-a35b-instruct 是基於 Qwen3 的程式碼生成模型，具有強大的 Coding Agent 能力，擅長工具調用和環境互動，能夠實現自主程式設計、程式碼能力卓越的同時兼具通用能力。"}, "qwen3-coder-plus": {"description": "通義千問程式碼模型。最新的 Qwen3-Coder-Plus 系列模型是基於 Qwen3 的程式碼生成模型，具有強大的 Coding Agent 能力，擅長工具調用和環境互動，能夠實現自主程式設計，程式碼能力卓越的同時兼具通用能力。"}, "qwq": {"description": "QwQ 是一個實驗研究模型，專注於提高 AI 推理能力。"}, "qwq-32b": {"description": "基於 Qwen2.5-32B 模型訓練的 QwQ 推理模型，通過強化學習大幅度提升了模型推理能力。模型數學代碼等核心指標（AIME 24/25、LiveCodeBench）以及部分通用指標（IFEval、LiveBench等）達到 DeepSeek-R1 滿血版水平，各指標均顯著超過同樣基於 Qwen2.5-32B 的 DeepSeek-R1-Distill-Qwen-32B。"}, "qwq-32b-preview": {"description": "QwQ模型是由 Qwen 團隊開發的實驗性研究模型，專注於增強 AI 推理能力。"}, "qwq-plus": {"description": "基於 Qwen2.5 模型訓練的 QwQ 推理模型，透過強化學習大幅度提升了模型推理能力。模型數學程式碼等核心指標（AIME 24/25、LiveCodeBench）以及部分通用指標（IFEval、LiveBench等）達到 DeepSeek-R1 滿血版水準。"}, "qwq_32b": {"description": "Qwen 系列中等規模的推理模型。與傳統的指令調優模型相比，具備思考和推理能力的 QwQ 在下游任務中，尤其是在解決難題時，能夠顯著提升性能。"}, "r1-1776": {"description": "R1-1776 是 DeepSeek R1 模型的一個版本，經過後訓練，可提供未經審查、無偏見的事實資訊。"}, "solar-mini": {"description": "Solar Mini 是一種緊湊型 LLM，性能優於 GPT-3.5，具備強大的多語言能力，支持英語和韓語，提供高效小巧的解決方案。"}, "solar-mini-ja": {"description": "Solar Mini (Ja) 擴展了 Solar Mini 的能力，專注於日語，同時在英語和韓語的使用中保持高效和卓越性能。"}, "solar-pro": {"description": "Solar Pro 是 Upstage 推出的一款高智能LLM，專注於單GPU的指令跟隨能力，IFEval得分80以上。目前支持英語，正式版本計劃於2024年11月推出，將擴展語言支持和上下文長度。"}, "sonar": {"description": "基於搜索上下文的輕量級搜索產品，比 Sonar Pro 更快、更便宜。"}, "sonar-deep-research": {"description": "Deep Research 進行全面的專家級研究，並將其綜合成可訪問、可行的報告。"}, "sonar-pro": {"description": "支持搜索上下文的高級搜索產品，支持高級查詢和跟進。"}, "sonar-reasoning": {"description": "由 DeepSeek 推理模型提供支持的新 API 產品。"}, "sonar-reasoning-pro": {"description": "由 DeepSeek 推理模型提供支援的新 API 產品。"}, "stable-diffusion-3-medium": {"description": "由 Stability AI 推出的最新文生圖大模型。這一版本在繼承了前代的優點上，對圖像品質、文本理解和風格多樣性等方面進行了顯著改進，能夠更準確地解讀複雜的自然語言提示，並生成更為精確和多樣化的圖像。"}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large 是一個具有8億參數的多模態擴散變壓器（MMDiT）文本到圖像生成模型，具備卓越的圖像品質和提示詞匹配度，支持生成 100 萬像素的高解析度圖像，且能夠在普通消費級硬體上高效運行。"}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo 是在 stable-diffusion-3.5-large 的基礎上採用對抗性擴散蒸餾（ADD）技術的模型，具備更快的速度。"}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 是以 stable-diffusion-v1.2 檢查點的權重進行初始化，並在 \"laion-aesthetics v2 5+\" 上以 512x512 的解析度進行了595k步的微調，減少了 10% 的文本條件化，以提高無分類器的引導採樣。"}, "stable-diffusion-xl": {"description": "stable-diffusion-xl 相較於 v1.5 做了重大的改進，並且與當前開源的文生圖 SOTA 模型 midjourney 效果相當。具體改進之處包括：更大的 unet backbone，是之前的 3 倍；增加了 refinement 模組用於改善生成圖片的品質；更高效的訓練技巧等。"}, "stable-diffusion-xl-base-1.0": {"description": "由 Stability AI 開發並開源的文生圖大模型，其創意圖像生成能力位居行業前列。具備出色的指令理解能力，能夠支持反向 Prompt 定義來精確生成內容。"}, "step-1-128k": {"description": "平衡性能與成本，適合一般場景。"}, "step-1-256k": {"description": "具備超長上下文處理能力，尤其適合長文檔分析。"}, "step-1-32k": {"description": "支持中等長度的對話，適用於多種應用場景。"}, "step-1-8k": {"description": "小型模型，適合輕量級任務。"}, "step-1-flash": {"description": "高速模型，適合實時對話。"}, "step-1.5v-mini": {"description": "該模型擁有強大的視頻理解能力。"}, "step-1o-turbo-vision": {"description": "該模型擁有強大的圖像理解能力，在數理、代碼領域強於1o。模型比1o更小，輸出速度更快。"}, "step-1o-vision-32k": {"description": "該模型擁有強大的圖像理解能力。相比於 step-1v 系列模型，擁有更強的視覺性能。"}, "step-1v-32k": {"description": "支持視覺輸入，增強多模態交互體驗。"}, "step-1v-8k": {"description": "小型視覺模型，適合基本的圖文任務。"}, "step-1x-edit": {"description": "該模型專注於圖像編輯任務，能夠根據用戶提供的圖片和文本描述，對圖片進行修改和增強。支持多種輸入格式，包括文本描述和示例圖像。模型能夠理解用戶的意圖，並生成符合要求的圖像編輯結果。"}, "step-1x-medium": {"description": "該模型擁有強大的圖像生成能力，支持文本描述作為輸入方式。具備原生的中文支持，能夠更好地理解和處理中文文本描述，並且能夠更準確地捕捉文本描述中的語義資訊，並將其轉化為圖像特徵，從而實現更精準的圖像生成。模型能夠根據輸入生成高解析度、高品質的圖像，並具備一定的風格遷移能力。"}, "step-2-16k": {"description": "支持大規模上下文交互，適合複雜對話場景。"}, "step-2-16k-exp": {"description": "step-2模型的實驗版本，包含最新的特性，滾動更新中。不推薦在正式生產環境使用。"}, "step-2-mini": {"description": "基於新一代自研Attention架構MFA的極速大模型，用極低成本達到和step1類似的效果，同時保持了更高的吞吐和更快響應時延。能夠處理通用任務，在程式碼能力上具備特長。"}, "step-2x-large": {"description": "階躍星辰新一代生圖模型，該模型專注於圖像生成任務，能夠根據用戶提供的文本描述，生成高品質的圖像。新模型生成圖片質感更真實，中英文文字生成能力更強。"}, "step-r1-v-mini": {"description": "該模型是擁有強大的圖像理解能力的推理大模型，能夠處理圖像和文字信息，經過深度思考後輸出文本生成文本內容。該模型在視覺推理領域表現突出，同時擁有第一梯隊的數學、程式碼、文本推理能力。上下文長度為100k。"}, "taichu_llm": {"description": "紫東太初語言大模型具備超強語言理解能力以及文本創作、知識問答、代碼編程、數學計算、邏輯推理、情感分析、文本摘要等能力。創新性地將大數據預訓練與多源豐富知識相結合，通過持續打磨算法技術，並不斷吸收海量文本數據中詞彙、結構、語法、語義等方面的新知識，實現模型效果不斷進化。為用戶提供更加便捷的信息和服務以及更為智能化的體驗。"}, "taichu_o1": {"description": "taichu_o1是新一代推理大模型，通過多模態互動和強化學習實現類人思維鏈，支持複雜決策推演，在保持高精度輸出的同時展現可模型推理的思維路徑，適用於策略分析與深度思考等場景。"}, "taichu_vl": {"description": "融合了圖像理解、知識遷移、邏輯歸因等能力，在圖文問答領域表現突出。"}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct 參數量800 億，啟動 130 億參數即可對標更大模型，支援「快思考/慢思考」混合推理；長文理解穩定；經 BFCL-v3 與 τ-Bench 驗證，Agent 能力領先；結合 GQA 與多量化格式，實現高效推理。"}, "text-embedding-3-large": {"description": "最強大的向量化模型，適用於英文和非英文任務"}, "text-embedding-3-small": {"description": "高效且經濟的新一代 Embedding 模型，適用於知識檢索、RAG 應用等場景"}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 是一個 32B 雙語（中英）開放權重語言模型，針對程式碼生成、函數調用和代理式任務進行了優化。它在 15T 高質量和重推理數據上進行了預訓練，並使用人類偏好對齊、拒絕採樣和強化學習進一步完善。該模型在複雜推理、工件生成和結構化輸出任務方面表現出色，在多個基準測試中達到了與 GPT-4o 和 DeepSeek-V3-0324 相當的性能。"}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 是一個 32B 雙語（中英）開放權重語言模型，針對程式碼生成、函數調用和代理式任務進行了優化。它在 15T 高質量和重推理數據上進行了預訓練，並使用人類偏好對齊、拒絕採樣和強化學習進一步完善。該模型在複雜推理、工件生成和結構化輸出任務方面表現出色，在多個基準測試中達到了與 GPT-4o 和 DeepSeek-V3-0324 相當的性能。"}, "thudm/glm-4-9b-chat": {"description": "智譜AI發布的GLM-4系列最新一代預訓練模型的開源版本。"}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 是 THUDM 開發的 GLM-4 系列中的 90 億參數語言模型。GLM-4-9B-0414 使用與其較大的 32B 對應模型相同的強化學習和對齊策略進行訓練，相對於其規模實現了高性能，使其適用於仍需要強大語言理解和生成能力的資源受限部署。"}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 是 GLM-4-32B 的增強推理變體，專為深度數學、邏輯和面向程式碼的問題解決而構建。它應用擴展強化學習（任務特定和基於通用成對偏好）來提高複雜多步驟任務的性能。與基礎 GLM-4-32B 模型相比，Z1 顯著提升了結構化推理和形式化領域的能力。\n\n該模型支持通過提示工程強制執行「思考」步驟，並為長格式輸出提供改進的連貫性。它針對代理工作流進行了優化，並支持長上下文（通過 YaRN）、JSON 工具調用和用於穩定推理的細粒度採樣配置。非常適合需要深思熟慮、多步驟推理或形式化推導的用例。"}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 是 GLM-4-32B 的增強推理變體，專為深度數學、邏輯和面向程式碼的問題解決而構建。它應用擴展強化學習（任務特定和基於通用成對偏好）來提高複雜多步驟任務的性能。與基礎 GLM-4-32B 模型相比，Z1 顯著提升了結構化推理和形式化領域的能力。\n\n該模型支持通過提示工程強制執行「思考」步驟，並為長格式輸出提供改進的連貫性。它針對代理工作流進行了優化，並支持長上下文（通過 YaRN）、JSON 工具調用和用於穩定推理的細粒度採樣配置。非常適合需要深思熟慮、多步驟推理或形式化推導的用例。"}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 是由 THUDM 開發的 GLM-4 系列中的 9B 參數語言模型。它採用了最初應用於更大 GLM-Z1 模型的技術，包括擴展強化學習、成對排名對齊以及對數學、代碼和邏輯等推理密集型任務的訓練。儘管其規模較小，但它在通用推理任務上表現出強大的性能，並在其權重級別中優於許多開源模型。"}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B 是 GLM-4-Z1 系列中的 32B 參數深度推理模型，針對需要長時間思考的複雜、開放式任務進行了優化。它建立在 glm-4-32b-0414 的基礎上，增加了額外的強化學習階段和多階段對齊策略，引入了旨在模擬擴展認知處理的「反思」能力。這包括迭代推理、多跳分析和工具增強的工作流程，例如搜索、檢索和引文感知合成。\n\n該模型在研究式寫作、比較分析和複雜問答方面表現出色。它支持用於搜索和導航原語（`search`、`click`、`open`、`finish`）的函數調用，從而可以在代理式管道中使用。反思行為由具有基於規則的獎勵塑造和延遲決策機制的多輪循環控制，並以 OpenAI 內部對齊堆疊等深度研究框架為基準。此變體適用於需要深度而非速度的場景。"}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera 透過合併 DeepSeek-R1 和 DeepSeek-V3 (0324) 創建，結合了 R1 的推理能力和 V3 的令牌效率改進。它基於 DeepSeek-MoE Transformer 架構，並針對通用文本生成任務進行了優化。\n\n該模型合併了兩個源模型的預訓練權重，以平衡推理、效率和指令遵循任務的性能。它根據 MIT 許可證發布，旨在用於研究和商業用途。"}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) 通過高效的策略和模型架構，提供增強的計算能力。"}, "tts-1": {"description": "最新的文本轉語音模型，針對即時場景優化速度"}, "tts-1-hd": {"description": "最新的文本轉語音模型，針對品質進行優化"}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) 適用於精細化指令任務，提供出色的語言處理能力。"}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet 提升了行業標準，性能超越競爭對手模型和 Claude 3 Opus，在廣泛的評估中表現出色，同時具備我們中等層級模型的速度和成本。"}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet 是 Anthropic 最快速的下一代模型。與 Claude 3 Haiku 相比，Claude 3.7 Sonnet 在各項技能上都有所提升，並在許多智力基準測試中超越了上一代最大的模型 Claude 3 Opus。"}, "v0-1.0-md": {"description": "v0-1.0-md 模型是透過 v0 API 提供服務的舊版模型"}, "v0-1.5-lg": {"description": "v0-1.5-lg 模型適用於高階思考或推理任務"}, "v0-1.5-md": {"description": "v0-1.5-md 模型適用於日常任務和使用者介面（UI）生成"}, "wan2.2-t2i-flash": {"description": "萬相2.2極速版，當前最新模型。在創意性、穩定性、寫實質感上全面升級，生成速度快，性價比高。"}, "wan2.2-t2i-plus": {"description": "萬相2.2專業版，當前最新模型。在創意性、穩定性、寫實質感上全面升級，生成細節豐富。"}, "wanx-v1": {"description": "基礎文生圖模型。對應通義萬相官網1.0通用模型。"}, "wanx2.0-t2i-turbo": {"description": "擅長質感人像，速度中等、成本較低。對應通義萬相官網2.0極速模型。"}, "wanx2.1-t2i-plus": {"description": "全面升級版本。生成圖像細節更豐富，速度稍慢。對應通義萬相官網2.1專業模型。"}, "wanx2.1-t2i-turbo": {"description": "全面升級版本。生成速度快、效果全面、綜合性價比高。對應通義萬相官網2.1極速模型。"}, "whisper-1": {"description": "通用語音識別模型，支持多語言語音識別、語音翻譯和語言識別。"}, "wizardlm2": {"description": "WizardLM 2 是微軟 AI 提供的語言模型，在複雜對話、多語言、推理和智能助手領域表現尤為出色。"}, "wizardlm2:8x22b": {"description": "WizardLM 2 是微軟 AI 提供的語言模型，在複雜對話、多語言、推理和智能助手領域表現尤為出色。"}, "x1": {"description": "Spark X1 模型將進一步升級，在原來數學任務國內領先的基礎上，推理、文本生成、語言理解等通用任務實現效果對標 OpenAI o1 和 DeepSeek R1。"}, "yi-1.5-34b-chat": {"description": "Yi-1.5 是 Yi 的升級版本。它使用 500B Tokens 的高品質語料庫在 Yi 上持續進行預訓練，並在 3M 個多樣化的微調樣本上進行微調。"}, "yi-large": {"description": "全新千億參數模型，提供超強問答及文本生成能力。"}, "yi-large-fc": {"description": "在 yi-large 模型的基礎上支持並強化了工具調用的能力，適用於各種需要搭建 agent 或 workflow 的業務場景。"}, "yi-large-preview": {"description": "初期版本，推薦使用 yi-large（新版本）"}, "yi-large-rag": {"description": "基於 yi-large 超強模型的高階服務，結合檢索與生成技術提供精準答案，實時全網檢索信息服務。"}, "yi-large-turbo": {"description": "超高性價比、卓越性能。根據性能和推理速度、成本，進行平衡性高精度調優。"}, "yi-lightning": {"description": "最新高性能模型，保證高品質輸出同時，推理速度大幅提升。"}, "yi-lightning-lite": {"description": "輕量化版本，推薦使用 yi-lightning。"}, "yi-medium": {"description": "中型尺寸模型升級微調，能力均衡，性價比高。深度優化指令遵循能力。"}, "yi-medium-200k": {"description": "200K 超長上下文窗口，提供長文本深度理解和生成能力。"}, "yi-spark": {"description": "小而精悍，輕量极速模型。提供強化數學運算和代碼編寫能力。"}, "yi-vision": {"description": "複雜視覺任務模型，提供高性能圖片理解、分析能力。"}, "yi-vision-v2": {"description": "複雜視覺任務模型，提供基於多張圖片的高性能理解、分析能力。"}, "zai-org/GLM-4.5": {"description": "GLM-4.5 是一款專為智能體應用打造的基礎模型，使用了混合專家（Mixture-of-Experts）架構。在工具調用、網頁瀏覽、軟體工程、前端程式設計領域進行了深度優化，支持無縫接入 Claude Code、Roo Code 等程式碼智能體中使用。GLM-4.5 採用混合推理模式，可以適應複雜推理和日常使用等多種應用場景。"}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air 是一款專為智能體應用打造的基礎模型，使用了混合專家（Mixture-of-Experts）架構。在工具調用、網頁瀏覽、軟體工程、前端程式設計領域進行了深度優化，支持無縫接入 Claude Code、Roo Code 等程式碼智能體中使用。GLM-4.5 採用混合推理模式，可以適應複雜推理和日常使用等多種應用場景。"}}