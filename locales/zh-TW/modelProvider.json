{"azure": {"azureApiVersion": {"desc": "Azure 的 API 版本，遵循 YYYY-MM-DD 格式，查閱[最新版本](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "獲取列表", "title": "Azure API 版本"}, "empty": "請輸入模型 ID 添加第一個模型", "endpoint": {"desc": "從 Azure 門戶檢查資源時，可在“金鑰和終點”部分中找到此值", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API 地址"}, "modelListPlaceholder": "請選擇或添加你部署的 OpenAI 模型", "title": "Azure OpenAI", "token": {"desc": "從 Azure 門戶檢查資源時，可在“金鑰和終點”部分中找到此值。 可以使用 KEY1 或 KEY2", "placeholder": "Azure API 金鑰", "title": "API 金鑰"}}, "azureai": {"azureApiVersion": {"desc": "Azure 的 API 版本，遵循 YYYY-MM-DD 格式，查閱[最新版本](https://learn.microsoft.com/zh-tw/azure/ai-services/openai/reference#chat-completions)", "fetch": "獲取列表", "title": "Azure API 版本"}, "endpoint": {"desc": "從 Azure AI 專案概述找到 Azure AI 模型推理終結點", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Azure AI 終結點"}, "title": "Azure OpenAI", "token": {"desc": "從 Azure AI 專案概述找到 API 密鑰", "placeholder": "Azure 密鑰", "title": "密鑰"}}, "bedrock": {"accessKeyId": {"desc": "填入AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "測試 AccessKeyId / SecretAccessKey 是否填寫正確"}, "region": {"desc": "填入 AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "填入 AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "如果你正在使用 AWS SSO/STS，請輸入你的 AWS Session Token", "placeholder": "AWS Session Token", "title": "AWS Session Token (可選)"}, "title": "Bedrock", "unlock": {"customRegion": "自定義服務區域", "customSessionToken": "自訂 Session Token", "description": "輸入你的 AWS AccessKeyId / SecretAccessKey 即可開始會話。應用程式不會記錄你的驗證配置", "imageGenerationDescription": "輸入你的 AWS AccessKeyId / SecretAccessKey 即可開始生成。應用不會記錄你的鑑權配置", "title": "使用自定義 Bedrock 驗證資訊"}}, "cloudflare": {"apiKey": {"desc": "請填入 Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "填入 Cloudflare 帳戶 ID 或 自定義 API 位址", "placeholder": "Cloudflare 帳戶 ID / 自定義 API 位址", "title": "Cloudflare 帳戶 ID / API 位址"}}, "createNewAiProvider": {"apiKey": {"placeholder": "請填寫你的 API Key", "title": "API Key"}, "basicTitle": "基本資訊", "configTitle": "配置信息", "confirm": "新建", "createSuccess": "新建成功", "description": {"placeholder": "服務商簡介（選填）", "title": "服務商簡介"}, "id": {"desc": "作為服務商唯一標識，創建後將不可修改", "format": "只能包含數字、小寫字母、連字符（-）和底線（_）", "placeholder": "建議全小寫，例如 openai，創建後將不可修改", "required": "請填寫服務商 ID", "title": "服務商 ID"}, "logo": {"required": "請上傳正確的服務商 Logo", "title": "服務商 Logo"}, "name": {"placeholder": "請輸入服務商的展示名稱", "required": "請填寫服務商名稱", "title": "服務商名稱"}, "proxyUrl": {"required": "請填寫代理地址", "title": "代理地址"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "請選擇 SDK 類型", "title": "請求格式"}, "title": "創建自定義 AI 服務商"}, "github": {"personalAccessToken": {"desc": "填入你的 Github 個人存取權杖，點擊[這裡](https://github.com/settings/tokens) 創建", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "填入你的 Hu<PERSON><PERSON><PERSON>，點擊 [這裡](https://huggingface.co/settings/tokens) 創建", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "list": {"title": {"disabled": "未啟用服務商", "enabled": "已啟用服務商"}}, "menu": {"addCustomProvider": "添加自定義服務商", "all": "全部", "list": {"disabled": "未啟用", "enabled": "已啟用"}, "notFound": "未找到搜索結果", "searchProviders": "搜索服務商...", "sort": "自定義排序"}, "ollama": {"checker": {"desc": "測試代理地址是否正確填寫", "title": "連通性檢查"}, "customModelName": {"desc": "增加自定義模型，多個模型使用逗號（,）隔開", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "自定義模型名稱"}, "download": {"desc": "<PERSON><PERSON><PERSON> 正在下載該模型，請儘量不要關閉本頁面。重新下載時將會中斷處繼續", "failed": "模型下載失敗，請檢查網路或 Ollama 設定後重試", "remainingTime": "剩餘時間", "speed": "下載速度", "title": "正在下載模型 {{model}}"}, "endpoint": {"desc": "必須包含http(s)://，本地未額外指定可留空", "title": "接口代理地址"}, "title": "Ollama", "unlock": {"cancel": "取消下載", "confirm": "下載", "description": "輸入你的 Ollama 模型標籤，完成即可繼續會話", "downloaded": "{{completed}} / {{total}}", "starting": "開始下載...", "title": "下載指定的 Ollama 模型"}}, "providerModels": {"config": {"aesGcm": "您的秘鑰與代理地址等將使用 <1>AES-GCM</1> 加密算法進行加密", "apiKey": {"desc": "請填寫你的 {{name}} API Key", "descWithUrl": "請填寫你的 {{name}} API Key，<3>點此獲取</3>", "placeholder": "{{name}} API Key", "title": "API Key"}, "baseURL": {"desc": "必須包含 http(s)://", "invalid": "請輸入合法的 URL", "placeholder": "https://your-proxy-url.com/v1", "title": "API 代理地址"}, "checker": {"button": "檢查", "desc": "測試 Api Key 與代理地址是否正確填寫", "pass": "檢查通過", "title": "連通性檢查"}, "fetchOnClient": {"desc": "客戶端請求模式將從瀏覽器直接發起會話請求，可提升響應速度", "title": "使用客戶端請求模式"}, "helpDoc": "配置教程", "responsesApi": {"desc": "採用 OpenAI 新一代請求格式規範，解鎖思維鏈等進階特性", "title": "使用 Responses API 規範"}, "waitingForMore": "更多模型正在 <1>計劃接入</1> 中，敬請期待"}, "createNew": {"title": "創建自定義 AI 模型"}, "item": {"config": "配置模型", "customModelCards": {"addNew": "創建並添加 {{id}} 模型", "confirmDelete": "即將刪除該自定義模型，刪除後將不可恢復，請謹慎操作。"}, "delete": {"confirm": "確認刪除模型 {{displayName}}？", "success": "刪除成功", "title": "刪除模型"}, "modelConfig": {"azureDeployName": {"extra": "在 Azure OpenAI 中實際請求的字段", "placeholder": "請輸入 Azure 中的模型部署名稱", "title": "模型部署名稱"}, "deployName": {"extra": "發送請求時會將該字段作為模型 ID", "placeholder": "請輸入模型實際部署的名稱或 id", "title": "模型部署名稱"}, "displayName": {"placeholder": "請輸入模型的展示名稱，例如 ChatGPT、GPT-4 等", "title": "模型展示名稱"}, "files": {"extra": "當前文件上傳實現僅為一種 Hack 方案，僅限自行嘗試。完整文件上傳能力請等待後續實現", "title": "支持文件上傳"}, "functionCall": {"extra": "此配置將僅開啟模型使用工具的能力，進而可以為模型添加工具類的插件。但是否支持真正使用工具完全取決於模型本身，請自行測試其可用性", "title": "支持工具使用"}, "id": {"extra": "創建後不可修改，調用 AI 時將作為模型 id 使用", "placeholder": "請輸入模型 id，例如 gpt-4o 或 claude-3.5-sonnet", "title": "模型 ID"}, "modalTitle": "自定義模型配置", "reasoning": {"extra": "此配置將僅開啟模型深度思考的能力，具體效果完全取決於模型本身，請自行測試該模型是否具備可用的深度思考能力", "title": "支持深度思考"}, "tokens": {"extra": "設定模型支持的最大 Token 數", "title": "最大上下文窗口", "unlimited": "無限制"}, "vision": {"extra": "此配置將僅開啟應用中的圖片上傳配置，是否支持識別完全取決於模型本身，請自行測試該模型的視覺識別能力可用性", "title": "支持視覺識別"}}, "pricing": {"image": "${{amount}}/圖片", "inputCharts": "${{amount}}/M 字符", "inputMinutes": "${{amount}}/分鐘", "inputTokens": "輸入 ${{amount}}/M", "outputTokens": "輸出 ${{amount}}/M"}, "releasedAt": "發佈於{{releasedAt}}"}, "list": {"addNew": "新增模型", "disabled": "未啟用", "disabledActions": {"showMore": "顯示全部"}, "empty": {"desc": "請創建自定義模型或拉取模型後開始使用吧", "title": "暫無可用模型"}, "enabled": "已啟用", "enabledActions": {"disableAll": "全部禁用", "enableAll": "全部啟用", "sort": "自訂模型排序"}, "enabledEmpty": "暫無啟用模型，請從下方列表中啟用心儀的模型吧~", "fetcher": {"clear": "清除取得的模型", "fetch": "取得模型列表", "fetching": "正在取得模型列表...", "latestTime": "上次更新時間：{{time}}", "noLatestTime": "尚未取得列表"}, "resetAll": {"conform": "確認重置當前模型的所有修改？重置後當前模型列表將會回到預設狀態", "success": "重置成功", "title": "重置所有修改"}, "search": "搜尋模型...", "searchResult": "搜尋到 {{count}} 個模型", "title": "模型列表", "total": "共 {{count}} 個模型可用"}, "searchNotFound": "未找到搜尋結果"}, "sortModal": {"success": "排序更新成功", "title": "自定義排序", "update": "更新"}, "updateAiProvider": {"confirmDelete": "即將刪除該 AI 服務商，刪除後將無法找回，確認是否刪除？", "deleteSuccess": "刪除成功", "tooltip": "更新服務商基礎配置", "updateSuccess": "更新成功"}, "updateCustomAiProvider": {"title": "更新自訂 AI 服務商配置"}, "vertexai": {"apiKey": {"desc": "填入你的 Vertex AI 金鑰", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Vertex AI 金鑰"}}, "zeroone": {"title": "01.AI 零一萬物"}, "zhipu": {"title": "智譜"}}