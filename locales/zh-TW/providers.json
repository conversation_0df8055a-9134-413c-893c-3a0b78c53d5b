{"ai21": {"description": "AI21 Labs 為企業構建基礎模型和人工智慧系統，加速生成性人工智慧在生產中的應用。"}, "ai360": {"description": "360 AI 是 360 公司推出的 AI 模型和服務平台，提供多種先進的自然語言處理模型，包括 360GPT2 Pro、360GPT Pro、360GPT Turbo 和 360GPT Turbo Responsibility 8K。這些模型結合了大規模參數和多模態能力，廣泛應用於文本生成、語義理解、對話系統與代碼生成等領域。通過靈活的定價策略，360 AI 滿足多樣化用戶需求，支持開發者集成，推動智能化應用的革新和發展。"}, "aihubmix": {"description": "AiHubMix 透過統一的 API 介面提供對多種 AI 模型的存取。"}, "anthropic": {"description": "Anthropic 是一家專注於人工智慧研究和開發的公司，提供了一系列先進的語言模型，如 Claude 3.5 Sonnet、Claude 3 Sonnet、Claude 3 Opus 和 Claude 3 Haiku。這些模型在智能、速度和成本之間取得了理想的平衡，適用於從企業級工作負載到快速響應的各種應用場景。Claude 3.5 Sonnet 作為其最新模型，在多項評估中表現優異，同時保持了較高的性價比。"}, "azure": {"description": "Azure 提供多種先進的 AI 模型，包括 GPT-3.5 和最新的 GPT-4 系列，支持多種數據類型和複雜任務，致力於安全、可靠和可持續的 AI 解決方案。"}, "azureai": {"description": "Azure 提供多種先進的 AI 模型，包括 GPT-3.5 和最新的 GPT-4 系列，支持多種數據類型和複雜任務，致力於安全、可靠和可持續的 AI 解決方案。"}, "baichuan": {"description": "百川智能是一家專注於人工智慧大模型研發的公司，其模型在國內知識百科、長文本處理和生成創作等中文任務上表現卓越，超越了國外主流模型。百川智能還具備行業領先的多模態能力，在多項權威評測中表現優異。其模型包括 Baichuan 4、Baichuan 3 Turbo 和 Baichuan 3 Turbo 128k 等，分別針對不同應用場景進行優化，提供高性價比的解決方案。"}, "bedrock": {"description": "Bedrock 是亞馬遜 AWS 提供的一項服務，專注於為企業提供先進的 AI 語言模型和視覺模型。其模型家族包括 Anthropic 的 Claude 系列、Meta 的 Llama 3.1 系列等，涵蓋從輕量級到高性能的多種選擇，支持文本生成、對話、圖像處理等多種任務，適用於不同規模和需求的企業應用。"}, "cloudflare": {"description": "在 Cloudflare 的全球網絡上運行由無伺服器 GPU 驅動的機器學習模型。"}, "cohere": {"description": "Cohere 為您帶來最前沿的多語言模型、先進的檢索功能以及為現代企業量身定制的 AI 工作空間 — 一切都集成在一個安全的平台中。"}, "deepseek": {"description": "DeepSeek 是一家專注於人工智慧技術研究和應用的公司，其最新模型 DeepSeek-V2.5 融合了通用對話和代碼處理能力，並在人類偏好對齊、寫作任務和指令跟隨等方面實現了顯著提升。"}, "fal": {"description": "面向開發者的生成式媒體平台"}, "fireworksai": {"description": "Fireworks AI 是一家領先的高級語言模型服務商，專注於功能調用和多模態處理。其最新模型 Firefunction V2 基於 Llama-3，優化用於函數調用、對話及指令跟隨。視覺語言模型 FireLLaVA-13B 支持圖像和文本混合輸入。其他 notable 模型包括 Llama 系列和 Mixtral 系列，提供高效的多語言指令跟隨與生成支持。"}, "giteeai": {"description": "Gitee AI 的 Serverless API 為 AI 開發者提供開箱即用的大模型推理 API 服務。"}, "github": {"description": "透過 GitHub 模型，開發者可以成為 AI 工程師，並使用業界領先的 AI 模型進行建設。"}, "google": {"description": "Google 的 Gemini 系列是其最先進、通用的 AI 模型，由 Google DeepMind 打造，專為多模態設計，支持文本、代碼、圖像、音頻和視頻的無縫理解與處理。適用於從數據中心到移動設備的多種環境，極大提升了 AI 模型的效率與應用廣泛性。"}, "groq": {"description": "Groq 的 LPU 推理引擎在最新的獨立大語言模型（LLM）基準測試中表現卓越，以其驚人的速度和效率重新定義了 AI 解決方案的標準。Groq 是一種即時推理速度的代表，在基於雲的部署中展現了良好的性能。"}, "higress": {"description": "Higress 是一款雲原生 API 網關，為了解決 Tengine reload 對長連接業務的影響，以及 gRPC/Dubbo 負載均衡能力不足而在阿里內部誕生。"}, "huggingface": {"description": "HuggingFace Inference API 提供了一種快速且免費的方式，讓您可以探索成千上萬種模型，適用於各種任務。無論您是在為新應用程式進行原型設計，還是在嘗試機器學習的功能，這個 API 都能讓您即時訪問多個領域的高性能模型。"}, "hunyuan": {"description": "由騰訊研發的大語言模型，具備強大的中文創作能力、複雜語境下的邏輯推理能力，以及可靠的任務執行能力"}, "infiniai": {"description": "為應用開發者提供高性能、易上手、安全可靠的大模型服務，覆蓋從大模型開發到大模型服務化部署的全流程。"}, "internlm": {"description": "致力於大模型研究與開發工具鏈的開源組織。為所有 AI 開發者提供高效、易用的開源平台，讓最前沿的大模型與算法技術觸手可及"}, "jina": {"description": "Jina AI 成立於 2020 年，是一家領先的搜索 AI 公司。我們的搜索底座平台包含了向量模型、重排器和小語言模型，可幫助企業構建可靠且高品質的生成式 AI 和多模態的搜索應用。"}, "lmstudio": {"description": "LM Studio 是一個用於在您的電腦上開發和實驗 LLMs 的桌面應用程式。"}, "minimax": {"description": "MiniMax 是 2021 年成立的通用人工智慧科技公司，致力於與用戶共創智能。MiniMax 自主研發了不同模態的通用大模型，其中包括萬億參數的 MoE 文本大模型、語音大模型以及圖像大模型。並推出了海螺 AI 等應用。"}, "mistral": {"description": "Mistral 提供先進的通用、專業和研究型模型，廣泛應用於複雜推理、多語言任務、代碼生成等領域，通過功能調用接口，用戶可以集成自定義功能，實現特定應用。"}, "modelscope": {"description": "ModelScope是阿里雲推出的模型即服務平台，提供豐富的AI模型和推理服務。"}, "moonshot": {"description": "Moonshot 是由北京月之暗面科技有限公司推出的開源平台，提供多種自然語言處理模型，應用領域廣泛，包括但不限於內容創作、學術研究、智能推薦、醫療診斷等，支持長文本處理和複雜生成任務。"}, "novita": {"description": "Novita AI 是一個提供多種大語言模型與 AI 圖像生成的 API 服務的平台，靈活、可靠且具有成本效益。它支持 Llama3、Mistral 等最新的開源模型，並為生成式 AI 應用開發提供了全面、用戶友好且自動擴展的 API 解決方案，適合 AI 初創公司的快速發展。"}, "nvidia": {"description": "NVIDIA NIM™ 提供容器，可用於自托管 GPU 加速推理微服務，支持在雲端、數據中心、RTX™ AI 個人電腦和工作站上部署預訓練和自定義 AI 模型。"}, "ollama": {"description": "Ollama 提供的模型廣泛涵蓋代碼生成、數學運算、多語種處理和對話互動等領域，支持企業級和本地化部署的多樣化需求。"}, "openai": {"description": "OpenAI 是全球領先的人工智慧研究機構，其開發的模型如 GPT 系列推動了自然語言處理的前沿。OpenAI 致力於透過創新和高效的 AI 解決方案改變多個行業。他們的產品具有顯著的性能和經濟性，廣泛用於研究、商業和創新應用。"}, "openrouter": {"description": "OpenRouter 是一個提供多種前沿大模型接口的服務平台，支持 OpenAI、Anthropic、LLaMA 及更多，適合多樣化的開發和應用需求。用戶可根據自身需求靈活選擇最優的模型和價格，助力 AI 體驗的提升。"}, "perplexity": {"description": "Perplexity 是一家領先的對話生成模型提供商，提供多種先進的 Llama 3.1 模型，支持在線和離線應用，特別適用於複雜的自然語言處理任務。"}, "ppio": {"description": "PPIO 派歐雲提供穩定、高性價比的開源模型 API 服務，支持 DeepSeek 全系列、Llama、<PERSON>wen 等行業領先的大模型。"}, "qiniu": {"description": "Qiniu 是領先的雲服務提供商，提供高性價比、穩定的實時和批量 AI 推理服務，包括 DeepSeek、Llama 和 Qwen 等行業領先的大模型。"}, "qwen": {"description": "通義千問是阿里雲自主研發的超大規模語言模型，具有強大的自然語言理解和生成能力。它可以回答各種問題、創作文字內容、表達觀點看法、撰寫代碼等，在多個領域發揮作用。"}, "sambanova": {"description": "SambaNova Cloud 讓開發者輕鬆使用最佳的開源模型，並享受最快的推理速度。"}, "search1api": {"description": "Search1API 提供可根據需要自行聯網的 DeepSeek 系列模型的訪問，包括標準版和快速版本，支持多種參數規模的模型選擇。"}, "sensenova": {"description": "商湯日日新，依托商湯大裝置的強大基礎支撐，提供高效易用的全棧大模型服務。"}, "siliconcloud": {"description": "SiliconFlow 致力於加速 AGI，以惠及人類，通過易用與成本低的 GenAI 堆疊提升大規模 AI 效率。"}, "spark": {"description": "科大訊飛星火大模型提供多領域、多語言的強大 AI 能力，利用先進的自然語言處理技術，構建適用於智能硬體、智慧醫療、智慧金融等多種垂直場景的創新應用。"}, "stepfun": {"description": "階級星辰大模型具備行業領先的多模態及複雜推理能力，支持超長文本理解和強大的自主調度搜索引擎功能。"}, "taichu": {"description": "中科院自動化研究所和武漢人工智慧研究院推出新一代多模態大模型，支持多輪問答、文本創作、圖像生成、3D理解、信號分析等全面問答任務，擁有更強的認知、理解、創作能力，帶來全新互動體驗。"}, "tencentcloud": {"description": "知識引擎原子能力（LLM Knowledge Engine Atomic Power）基於知識引擎研發的知識問答全鏈路能力，面向企業及開發者，提供靈活組建及開發模型應用的能力。您可透過多款原子能力組建您專屬的模型服務，調用文檔解析、拆分、embedding、多輪改寫等服務進行組裝，定制企業專屬 AI 業務。"}, "togetherai": {"description": "Together AI 致力於透過創新的 AI 模型實現領先的性能，提供廣泛的自定義能力，包括快速擴展支持和直觀的部署流程，滿足企業的各種需求。"}, "upstage": {"description": "Upstage 專注於為各種商業需求開發 AI 模型，包括 Solar LLM 和文檔 AI，旨在實現工作的人工通用智能（AGI）。通過 Chat API 創建簡單的對話代理，並支持功能調用、翻譯、嵌入以及特定領域應用。"}, "v0": {"description": "v0 是一個配對程式設計助手，你只需用自然語言描述想法，它就能為你的專案生成程式碼和使用者介面（UI）"}, "vertexai": {"description": "Google 的 Gemini 系列是其最先進、通用的 AI 模型，由 Google DeepMind 打造，專為多模態設計，支持文本、程式碼、圖像、音訊和視頻的無縫理解與處理。適用於從數據中心到行動裝置的多種環境，極大提升了 AI 模型的效率與應用廣泛性。"}, "vllm": {"description": "vLLM 是一個快速且易於使用的庫，用於 LLM 推理和服務。"}, "volcengine": {"description": "字節跳動推出的大模型服務的開發平台，提供功能豐富、安全以及具備價格競爭力的模型調用服務，同時提供模型數據、精調、推理、評測等端到端功能，全方位保障您的 AI 應用開發落地。"}, "wenxin": {"description": "企業級一站式大模型與AI原生應用開發及服務平台，提供最全面易用的生成式人工智慧模型開發、應用開發全流程工具鏈"}, "xai": {"description": "xAI 是一家致力於構建人工智慧以加速人類科學發現的公司。我們的使命是推動我們對宇宙的共同理解。"}, "xinference": {"description": "Xorbits推論（Xinference）是一個開源平台，用於簡化各種AI模型的運行與整合。透過Xinference，您可以在雲端或本地環境中使用任何開源LLM、嵌入模型和多模態模型進行推論，並創建強大的AI應用程式。"}, "zeroone": {"description": "01.AI 專注於 AI 2.0 時代的人工智慧技術，大力推動「人+人工智慧」的創新和應用，採用超強大模型和先進 AI 技術以提升人類生產力，實現技術賦能。"}, "zhipu": {"description": "智譜 AI 提供多模態與語言模型的開放平台，支持廣泛的 AI 應用場景，包括文本處理、圖像理解與編程輔助等。"}}