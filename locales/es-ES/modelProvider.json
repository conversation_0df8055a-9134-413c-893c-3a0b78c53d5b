{"azure": {"azureApiVersion": {"desc": "La versión de la API de Azure, siguiendo el formato AAAA-MM-DD, consulta la [última versión](https://learn.microsoft.com/es-es/azure/ai-services/openai/reference#chat-completions)", "fetch": "Obtener lista", "title": "Versión de la API de Azure"}, "empty": "Introduce el ID del modelo para agregar el primer modelo", "endpoint": {"desc": "Puedes encontrar este valor en la sección 'Claves y endpoint' al revisar tus recursos en el portal de Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Dirección de la API de Azure"}, "modelListPlaceholder": "Selecciona o agrega el modelo de OpenAI que has implementado", "title": "Azure OpenAI", "token": {"desc": "Puedes encontrar este valor en la sección 'Claves y endpoint' al revisar tus recursos en el portal de Azure. Puedes usar KEY1 o KEY2", "placeholder": "Clave API de Azure", "title": "Clave API"}}, "azureai": {"azureApiVersion": {"desc": "Versión de la API de Azure, siguiendo el formato AAAA-MM-DD, consulta la [última versión](https://learn.microsoft.com/es-es/azure/ai-services/openai/reference#chat-completions)", "fetch": "Obtener lista", "title": "Versión de la API de Azure"}, "endpoint": {"desc": "Encuentra el punto final de inferencia del modelo de Azure AI en la descripción general del proyecto de Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Punto final de Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Encuentra la clave API en la descripción general del proyecto de Azure AI", "placeholder": "Clave de <PERSON>zure", "title": "Clave"}}, "bedrock": {"accessKeyId": {"desc": "Introduce tu AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "Prueba si el AccessKeyId / SecretAccessKey se ha introducido correctamente"}, "region": {"desc": "Introduce tu región de AWS", "placeholder": "Región de AWS", "title": "Región de AWS"}, "secretAccessKey": {"desc": "Introduce tu AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Si estás utilizando AWS SSO/STS, introduce tu Token de Sesión de AWS", "placeholder": "Token de Sesión de AWS", "title": "Token de Sesión de AWS (opcional)"}, "title": "Bedrock", "unlock": {"customRegion": "Región de servicio personalizada", "customSessionToken": "Token de sesión personalizado", "description": "Introduce tu AWS AccessKeyId / SecretAccessKey para comenzar la sesión. La aplicación no guardará tu configuración de autenticación.", "imageGenerationDescription": "Introduce tu AWS AccessKeyId / SecretAccessKey para comenzar a generar. La aplicación no registrará tu configuración de autenticación", "title": "Usar información de autenticación de Bedrock personalizada"}}, "cloudflare": {"apiKey": {"desc": "Por favor complete la Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Ingrese el ID de cuenta de Cloudflare o la dirección URL personalizada de API", "placeholder": "ID de cuenta de Cloudflare / URL de API personalizada", "title": "ID de cuenta de Cloudflare / dirección URL de API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Por favor, introduce tu API Key", "title": "API Key"}, "basicTitle": "Información básica", "configTitle": "Información de configuración", "confirm": "<PERSON><PERSON><PERSON> nuevo", "createSuccess": "Creación exitosa", "description": {"placeholder": "Descripción del proveedor (opcional)", "title": "Descripción del proveedor"}, "id": {"desc": "Identificador único del proveedor de servicios, no se puede modificar una vez creado", "format": "Solo puede contener númer<PERSON>, letras minús<PERSON>, gui<PERSON> (-) y guiones bajos (_) ", "placeholder": "Se recomienda en minúsculas, por ejemplo openai, no se puede modificar después de crear", "required": "Por favor, introduce el ID del proveedor", "title": "ID del proveedor"}, "logo": {"required": "Por favor, sube un logo correcto del proveedor", "title": "Logo del proveedor"}, "name": {"placeholder": "Por favor, introduce el nombre del proveedor", "required": "Por favor, introduce el nombre del proveedor", "title": "Nombre del proveedor"}, "proxyUrl": {"required": "Por favor, introduce la dirección del proxy", "title": "Dirección del proxy"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Por favor, selecciona el tipo de SDK", "title": "Formato de solicitud"}, "title": "<PERSON><PERSON><PERSON>edor de AI personalizado"}, "github": {"personalAccessToken": {"desc": "Introduce tu PAT de Github, haz clic [aquí](https://github.com/settings/tokens) para crear uno", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Introduce tu token de HuggingFace, haz clic [aquí](https://huggingface.co/settings/tokens) para crear uno", "placeholder": "hf_xxxxxxxxx", "title": "Token de HuggingFace"}}, "list": {"title": {"disabled": "Proveedor no habilitado", "enabled": "Proveedor habilitado"}}, "menu": {"addCustomProvider": "Ag<PERSON><PERSON> proveedor personalizado", "all": "Todo", "list": {"disabled": "No habilitado", "enabled": "Habilitado"}, "notFound": "No se encontraron resultados de búsqueda", "searchProviders": "Buscar proveedores...", "sort": "Orden personalizado"}, "ollama": {"checker": {"desc": "Prueba si la dirección del proxy de la interfaz se ha introducido correctamente", "title": "Comprobación de conectividad"}, "customModelName": {"desc": "Añade modelos personalizados, separa múltiples modelos con comas (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Nombre de modelos personalizados"}, "download": {"desc": "Ollama está descargando este modelo, por favor intenta no cerrar esta página. La descarga se reanudará desde donde se interrumpió", "failed": "La descarga del modelo ha fallado, por favor verifica la red o la configuración de Ollama y vuelve a intentarlo", "remainingTime": "Tiempo restante", "speed": "Velocidad de descarga", "title": "Descargando el modelo {{model}} "}, "endpoint": {"desc": "Debe incluir http(s)://, se puede dejar vacío si no se especifica localmente", "title": "Dirección del proxy de la interfaz"}, "title": "Ollama", "unlock": {"cancel": "Cancel Download", "confirm": "Download", "description": "Enter your Ollama model tag to continue the session", "downloaded": "{{completed}} / {{total}}", "starting": "Starting download...", "title": "Download specified Ollama model"}}, "providerModels": {"config": {"aesGcm": "Tu clave y dirección del proxy se cifrarán utilizando el algoritmo de cifrado <1>AES-GCM</1>", "apiKey": {"desc": "Por favor, introduce tu {{name}} API Key", "descWithUrl": "Por favor, introduce tu clave API de {{name}}, <3>haz clic aquí para obtenerla</3>", "placeholder": "{{name}} API Key", "title": "API Key"}, "baseURL": {"desc": "Debe incluir http(s)://", "invalid": "Por favor, introduce una URL válida", "placeholder": "https://tu-direccion-proxy.com/v1", "title": "Dirección del proxy API"}, "checker": {"button": "Verificar", "desc": "Prueba si la API Key y la dirección del proxy están correctamente introducidas", "pass": "Verificación exitosa", "title": "Verificación de conectividad"}, "fetchOnClient": {"desc": "El modo de solicitud del cliente iniciará la solicitud de sesión directamente desde el navegador, lo que puede mejorar la velocidad de respuesta", "title": "Usar modo de solicitud del cliente"}, "helpDoc": "Guía de configuración", "responsesApi": {"desc": "Utiliza el nuevo formato de solicitud de OpenAI para desbloquear características avanzadas como cadenas de pensamiento", "title": "Uso de la especificación Responses API"}, "waitingForMore": "Más modelos están en <1>planificación de integración</1>, por favor, espera"}, "createNew": {"title": "Crear modelo de AI personalizado"}, "item": {"config": "Configurar modelo", "customModelCards": {"addNew": "Crear y agregar modelo {{id}}", "confirmDelete": "Estás a punto de eliminar este modelo personalizado, una vez eliminado no se puede recuperar, por favor actúa con precaución."}, "delete": {"confirm": "¿Confirmar eliminación del modelo {{displayName}}?", "success": "Eliminación exitosa", "title": "Eliminar modelo"}, "modelConfig": {"azureDeployName": {"extra": "Campo solicitado en Azure OpenAI", "placeholder": "Por favor, introduce el nombre de despliegue del modelo en Azure", "title": "Nombre de despliegue del modelo"}, "deployName": {"extra": "Este campo se enviará como ID del modelo al hacer la solicitud", "placeholder": "Introduce el nombre o ID real del modelo desplegado", "title": "Nombre de despliegue del modelo"}, "displayName": {"placeholder": "Por favor, introduce el nombre de visualización del modelo, por ejemplo, ChatGPT, GPT-4, etc.", "title": "Nombre de visualización del modelo"}, "files": {"extra": "La implementación actual de carga de archivos es solo una solución temporal, solo para prueba personal. La capacidad completa de carga de archivos estará disponible en futuras implementaciones.", "title": "Soporte para carga de archivos"}, "functionCall": {"extra": "Esta configuración solo habilitará la capacidad del modelo para usar herramientas, lo que permite agregar complementos de tipo herramienta al modelo. Sin embargo, si realmente se admiten las herramientas depende completamente del modelo en sí, por favor pruebe su disponibilidad", "title": "Soporte para el uso de herramientas"}, "id": {"extra": "No se puede modificar después de la creación, se utilizará como id del modelo al llamar a la IA", "placeholder": "Introduce el id del modelo, por ejemplo gpt-4o o claude-3.5-sonnet", "title": "ID del modelo"}, "modalTitle": "Configuración del modelo personalizado", "reasoning": {"extra": "Esta configuración solo activará la capacidad de pensamiento profundo del modelo, el efecto específico depende completamente del modelo en sí, por favor, pruebe si este modelo tiene la capacidad de pensamiento profundo utilizable", "title": "Soporte para pensamiento profundo"}, "tokens": {"extra": "Establecer el número máximo de tokens que el modelo puede soportar", "title": "Máximo de ventana de contexto", "unlimited": "Sin límite"}, "vision": {"extra": "Esta configuración solo habilitará la configuración de carga de imágenes en la aplicación, si se admite el reconocimiento depende completamente del modelo en sí, prueba la disponibilidad de la capacidad de reconocimiento visual de este modelo.", "title": "Soporte para reconocimiento visual"}}, "pricing": {"image": "${{amount}}/imagen", "inputCharts": "${{amount}}/M caracteres", "inputMinutes": "${{amount}}/minuto", "inputTokens": "Entrada ${{amount}}/M", "outputTokens": "Salida ${{amount}}/M"}, "releasedAt": "Publicado el {{releasedAt}}"}, "list": {"addNew": "Agregar modelo", "disabled": "No habilitado", "disabledActions": {"showMore": "<PERSON><PERSON> todo"}, "empty": {"desc": "Por favor, crea un modelo personalizado o importa un modelo para comenzar a usarlo.", "title": "No hay modelos disponibles"}, "enabled": "Habilitado", "enabledActions": {"disableAll": "<PERSON>habili<PERSON> todo", "enableAll": "Habil<PERSON>r todo", "sort": "Ordenar modelos personalizados"}, "enabledEmpty": "No hay modelos habilitados, por favor habilita los modelos que te gusten de la lista a continuación~", "fetcher": {"clear": "Eliminar modelos obtenidos", "fetch": "Obtener lista de modelos", "fetching": "Obteniendo lista de modelos...", "latestTime": "Última actualización: {{time}}", "noLatestTime": "Lista aún no obtenida"}, "resetAll": {"conform": "¿Confirmar el restablecimiento de todas las modificaciones del modelo actual? Después del restablecimiento, la lista de modelos actuales volverá al estado predeterminado", "success": "Restablecimiento exitoso", "title": "Restablecer todas las modificaciones"}, "search": "Buscar modelos...", "searchResult": "Se encontraron {{count}} modelos", "title": "Lista de modelos", "total": "Un total de {{count}} modelos disponibles"}, "searchNotFound": "No se encontraron resultados de búsqueda"}, "sortModal": {"success": "Orden actualizado con éxito", "title": "Orden personalizado", "update": "Actualizar"}, "updateAiProvider": {"confirmDelete": "Estás a punto de eliminar este proveedor de AI, una vez eliminado no se puede recuperar, ¿confirmar eliminación?", "deleteSuccess": "Eliminación exitosa", "tooltip": "Actualizar configuración básica del proveedor", "updateSuccess": "Actualización exitosa"}, "updateCustomAiProvider": {"title": "Actualizar la configuración del proveedor de IA personalizado"}, "vertexai": {"apiKey": {"desc": "Introduce tus claves de Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Claves de Vertex AI"}}, "zeroone": {"title": "01.<PERSON>ro Uno Todo"}, "zhipu": {"title": "Inteligencia de Mapa"}}