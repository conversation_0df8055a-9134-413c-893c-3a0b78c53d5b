{"01-ai/yi-1.5-34b-chat": {"description": "<PERSON><PERSON>, el último modelo de ajuste fino de código abierto, cuenta con 34 mil millones de parámetros, con ajuste fino que admite múltiples escenarios de conversación y datos de entrenamiento de alta calidad, alineados con las preferencias humanas."}, "01-ai/yi-1.5-9b-chat": {"description": "<PERSON><PERSON>, el último modelo de ajuste fino de código abierto, cuenta con 9 mil millones de parámetros, con ajuste fino que admite múltiples escenarios de conversación y datos de entrenamiento de alta calidad, alineados con las preferencias humanas."}, "360/deepseek-r1": {"description": "【Versión desplegada de 360】DeepSeek-R1 utiliza técnicas de aprendizaje por refuerzo a gran escala en la fase de post-entrenamiento, mejorando enormemente la capacidad de inferencia del modelo con muy pocos datos etiquetados. En tareas de matemáticas, código y razonamiento en lenguaje natural, su rendimiento es comparable al de la versión oficial de OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro, como un miembro importante de la serie de modelos de IA de 360, satisface diversas aplicaciones de procesamiento de lenguaje natural con su eficiente capacidad de manejo de textos, soportando la comprensión de textos largos y funciones de diálogo en múltiples turnos."}, "360gpt-pro-trans": {"description": "Modelo especializado en traducción, optimizado con un ajuste fino profundo, con resultados de traducción líderes."}, "360gpt-turbo": {"description": "360GPT Turbo ofrece potentes capacidades de cálculo y diálogo, con una excelente comprensión semántica y eficiencia de generación, siendo la solución ideal para empresas y desarrolladores como asistente inteligente."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K enfatiza la seguridad semántica y la responsabilidad, diseñado específicamente para aplicaciones que requieren altos estándares de seguridad de contenido, asegurando la precisión y robustez de la experiencia del usuario."}, "360gpt2-o1": {"description": "360gpt2-o1 utiliza la búsqueda en árbol para construir cadenas de pensamiento e introduce un mecanismo de reflexión, entrenado mediante aprendizaje por refuerzo, lo que le permite tener la capacidad de auto-reflexión y corrección de errores."}, "360gpt2-pro": {"description": "360GPT2 Pro es un modelo avanzado de procesamiento de lenguaje natural lanzado por la empresa 360, con una excelente capacidad de generación y comprensión de textos, destacándose especialmente en la generación y creación de contenido, capaz de manejar tareas complejas de conversión de lenguaje y representación de roles."}, "360zhinao2-o1": {"description": "360zhinao2-o1 utiliza búsqueda en árbol para construir cadenas de pensamiento e introduce un mecanismo de reflexión, entrenando el modelo con aprendizaje por refuerzo, lo que le confiere la capacidad de auto-reflexión y corrección de errores."}, "4.0Ultra": {"description": "Spark4.0 Ultra es la versión más poderosa de la serie de modelos grandes de Xinghuo, mejorando la comprensión y capacidad de resumen de contenido textual al actualizar la conexión de búsqueda en línea. Es una solución integral para mejorar la productividad en la oficina y responder con precisión a las necesidades, siendo un producto inteligente líder en la industria."}, "AnimeSharp": {"description": "AnimeSharp (también conocido como “4x‑AnimeSharp”) es un modelo de superresolución de código abierto desarrollado por Kim2091 basado en la arquitectura ESRGAN, enfocado en la ampliación y el afilado de imágenes con estilo anime. Fue renombrado en febrero de 2022 desde “4x-TextSharpV1”, originalmente también aplicable a imágenes de texto, pero con un rendimiento significativamente optimizado para contenido anime."}, "Baichuan2-Turbo": {"description": "Utiliza tecnología de búsqueda mejorada para lograr un enlace completo entre el gran modelo y el conocimiento del dominio, así como el conocimiento de toda la red. Soporta la carga de documentos en PDF, Word y otros formatos, así como la entrada de URL, proporcionando información oportuna y completa, con resultados precisos y profesionales."}, "Baichuan3-Turbo": {"description": "Optimizado para escenarios de alta frecuencia empresarial, con mejoras significativas en el rendimiento y una excelente relación calidad-precio. En comparación con el modelo Baichuan2, la creación de contenido mejora un 20%, las preguntas y respuestas de conocimiento un 17%, y la capacidad de interpretación de roles un 40%. En general, su rendimiento es superior al de GPT-3.5."}, "Baichuan3-Turbo-128k": {"description": "Con una ventana de contexto ultra larga de 128K, optimizado para escenarios de alta frecuencia empresarial, con mejoras significativas en el rendimiento y una excelente relación calidad-precio. En comparación con el modelo Baichuan2, la creación de contenido mejora un 20%, las preguntas y respuestas de conocimiento un 17%, y la capacidad de interpretación de roles un 40%. En general, su rendimiento es superior al de GPT-3.5."}, "Baichuan4": {"description": "El modelo tiene la mejor capacidad en el país, superando a los modelos principales extranjeros en tareas en chino como enciclopedias, textos largos y creación generativa. También cuenta con capacidades multimodales líderes en la industria, destacándose en múltiples evaluaciones de referencia autorizadas."}, "Baichuan4-Air": {"description": "El modelo más potente del país, superando a los modelos principales extranjeros en tareas en chino como enciclopedias, textos largos y creación generativa. También cuenta con capacidades multimodales líderes en la industria, destacándose en múltiples evaluaciones de referencia."}, "Baichuan4-Turbo": {"description": "El modelo más potente del país, superando a los modelos principales extranjeros en tareas en chino como enciclopedias, textos largos y creación generativa. También cuenta con capacidades multimodales líderes en la industria, destacándose en múltiples evaluaciones de referencia."}, "DeepSeek-R1": {"description": "LLM eficiente de última generación, experto en razonamiento, matemáticas y programación."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1, el modelo más grande e inteligente del conjunto DeepSeek, ha sido destilado en la arquitectura Llama 70B. Basado en pruebas de referencia y evaluaciones humanas, este modelo es más inteligente que el Llama 70B original, destacándose especialmente en tareas que requieren precisión matemática y factual."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "El modelo de destilación DeepSeek-R1 basado en Qwen2.5-Math-1.5B optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "El modelo de destilación DeepSeek-R1 basado en Qwen2.5-14B optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "La serie DeepSeek-R1 optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto, superando el nivel de OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "El modelo de destilación DeepSeek-R1 basado en Qwen2.5-Math-7B optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto."}, "DeepSeek-V3": {"description": "DeepSeek-V3 es un modelo MoE desarrollado internamente por la empresa DeepSeek. Los resultados de DeepSeek-V3 en múltiples evaluaciones superan a otros modelos de código abierto como Qwen2.5-72B y Llama-3.1-405B, y su rendimiento es comparable al de los modelos cerrados de primer nivel mundial como GPT-4o y Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite ofrece una velocidad de respuesta excepcional y una mejor relación calidad-precio, proporcionando opciones más flexibles para diferentes escenarios de los clientes. Soporta inferencia y ajuste fino con una ventana de contexto de 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite ofrece una velocidad de respuesta excepcional y una mejor relación calidad-precio, proporcionando opciones más flexibles para diferentes escenarios de los clientes. Soporta inferencia y ajuste fino con una ventana de contexto de 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite ofrece una velocidad de respuesta excepcional y una mejor relación calidad-precio, proporcionando opciones más flexibles para diferentes escenarios de los clientes. Soporta inferencia y ajuste fino con una ventana de contexto de 4k."}, "Doubao-pro-128k": {"description": "El modelo principal con mejor rendimiento, adecuado para tareas complejas, con excelentes resultados en preguntas de referencia, resúmenes, creación, clasificación de texto, juegos de rol y otros escenarios. Soporta inferencia y ajuste fino con una ventana de contexto de 128k."}, "Doubao-pro-32k": {"description": "El modelo principal con mejor rendimiento, adecuado para tareas complejas, con excelentes resultados en preguntas de referencia, resúmenes, creación, clasificación de texto, juegos de rol y otros escenarios. Soporta inferencia y ajuste fino con una ventana de contexto de 32k."}, "Doubao-pro-4k": {"description": "El modelo principal con mejor rendimiento, adecuado para tareas complejas, con excelentes resultados en preguntas de referencia, resúmenes, creación, clasificación de texto, juegos de rol y otros escenarios. Soporta inferencia y ajuste fino con una ventana de contexto de 4k."}, "DreamO": {"description": "DreamO es un modelo de generación de imágenes personalizado de código abierto desarrollado conjuntamente por ByteDance y la Universidad de Pekín, diseñado para soportar generación de imágenes multitarea mediante una arquitectura unificada. Utiliza un método eficiente de modelado combinado para generar imágenes altamente coherentes y personalizadas según múltiples condiciones especificadas por el usuario, como identidad, sujeto, estilo y fondo."}, "ERNIE-3.5-128K": {"description": "Modelo de lenguaje a gran escala de primera línea desarrollado por Baidu, que abarca una vasta cantidad de corpus en chino y en inglés, con potentes capacidades generales que pueden satisfacer la mayoría de los requisitos de preguntas y respuestas en diálogos, generación de contenido y aplicaciones de plugins; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información en las respuestas."}, "ERNIE-3.5-8K": {"description": "Modelo de lenguaje a gran escala de primera línea desarrollado por Baidu, que abarca una vasta cantidad de corpus en chino y en inglés, con potentes capacidades generales que pueden satisfacer la mayoría de los requisitos de preguntas y respuestas en diálogos, generación de contenido y aplicaciones de plugins; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información en las respuestas."}, "ERNIE-3.5-8K-Preview": {"description": "Modelo de lenguaje a gran escala de primera línea desarrollado por Baidu, que abarca una vasta cantidad de corpus en chino y en inglés, con potentes capacidades generales que pueden satisfacer la mayoría de los requisitos de preguntas y respuestas en diálogos, generación de contenido y aplicaciones de plugins; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información en las respuestas."}, "ERNIE-4.0-8K-Latest": {"description": "Modelo de lenguaje a gran escala ultra avanzado desarrollado por <PERSON>du, que ha logrado una actualización completa de las capacidades del modelo en comparación con ERNIE 3.5, siendo ampliamente aplicable a escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información en las respuestas."}, "ERNIE-4.0-8K-Preview": {"description": "Modelo de lenguaje a gran escala ultra avanzado desarrollado por <PERSON>du, que ha logrado una actualización completa de las capacidades del modelo en comparación con ERNIE 3.5, siendo ampliamente aplicable a escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información en las respuestas."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Modelo de lenguaje a gran escala desarrollado por <PERSON>, con un rendimiento general excepcional, ampliamente aplicable a escenas complejas en diversos campos; soporta la conexión automática al complemento de búsqueda de Baidu, garantizando la actualidad de la información de las preguntas y respuestas. En comparación con ERNIE 4.0, tiene un rendimiento superior."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Modelo de lenguaje a gran escala ultra avanzado desarrollado por <PERSON>du, con un rendimiento excepcional en efectos generales, siendo ampliamente aplicable a escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información en las respuestas. En comparación con ERNIE 4.0, ofrece un rendimiento superior."}, "ERNIE-Character-8K": {"description": "Modelo de lenguaje vertical desarrollado por <PERSON>, adecuado para aplicaciones como NPC en juegos, diálogos de servicio al cliente, y juegos de rol conversacionales, con un estilo de personaje más distintivo y coherente, y una mayor capacidad de seguir instrucciones, además de un rendimiento de inferencia superior."}, "ERNIE-Lite-Pro-128K": {"description": "Modelo de lenguaje ligero desarrollado por Baidu, que combina un excelente rendimiento del modelo con una alta eficiencia de inferencia, superando a ERNIE Lite, adecuado para su uso en tarjetas de aceleración de IA de bajo consumo."}, "ERNIE-Speed-128K": {"description": "Modelo de lenguaje de alto rendimiento desarrollado por Baidu, lanzado en 2024, con capacidades generales excepcionales, adecuado como modelo base para ajustes finos, manejando mejor problemas en escenarios específicos, y con un rendimiento de inferencia excelente."}, "ERNIE-Speed-Pro-128K": {"description": "Modelo de lenguaje de alto rendimiento desarrollado por Baidu, lanzado en 2024, con capacidades generales excepcionales, superando a ERNIE Speed, adecuado como modelo base para ajustes finos, manejando mejor problemas en escenarios específicos, y con un rendimiento de inferencia excelente."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev es un modelo multimodal de generación y edición de imágenes desarrollado por Black Forest Labs, basado en la arquitectura Rectified Flow Transformer, con una escala de 12 mil millones de parámetros. Se especializa en generar, reconstruir, mejorar o editar imágenes bajo condiciones contextuales dadas. Combina las ventajas de generación controlada de modelos de difusión con la capacidad de modelado contextual de Transformers, soportando salidas de alta calidad y aplicándose ampliamente en tareas como restauración de imágenes, completado y reconstrucción de escenas visuales."}, "FLUX.1-dev": {"description": "FLUX.1-dev es un modelo multimodal de lenguaje (MLLM) de código abierto desarrollado por Black Forest Labs, optimizado para tareas de texto e imagen, integrando capacidades de comprensión y generación tanto visual como textual. Está basado en avanzados modelos de lenguaje grande (como Mistral-7B) y mediante un codificador visual cuidadosamente diseñado y un ajuste fino por etapas con instrucciones, logra procesamiento colaborativo de texto e imagen y razonamiento para tareas complejas."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) es un modelo innovador, adecuado para aplicaciones en múltiples campos y tareas complejas."}, "HelloMeme": {"description": "HelloMeme es una herramienta de IA que puede generar automáticamente memes, GIFs o videos cortos basados en las imágenes o acciones que proporciones. No requiere conocimientos de dibujo o programación; solo necesitas preparar una imagen de referencia y la herramienta te ayudará a crear contenido atractivo, divertido y con estilo coherente."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full es un modelo de edición de imágenes multimodal de código abierto lanzado por HiDream.ai, basado en la avanzada arquitectura Diffusion Transformer y potenciado con una fuerte capacidad de comprensión del lenguaje (incorporando LLaMA 3.1-8B-Instruct). Soporta generación de imágenes, transferencia de estilo, edición local y redibujo de contenido mediante instrucciones en lenguaje natural, con excelentes habilidades de comprensión y ejecución texto-imagen."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled es un modelo ligero de generación de imágenes a partir de texto, optimizado mediante destilación para generar imágenes de alta calidad rápidamente, especialmente adecuado para entornos con recursos limitados y tareas de generación en tiempo real."}, "InstantCharacter": {"description": "InstantCharacter es un modelo de generación de personajes personalizados sin necesidad de ajuste fino, lanzado por el equipo de IA de Tencent en 2025, diseñado para lograr generación consistente y de alta fidelidad en múltiples escenarios. El modelo permite modelar un personaje basándose únicamente en una imagen de referencia y transferirlo de forma flexible a diversos estilos, acciones y fondos."}, "InternVL2-8B": {"description": "InternVL2-8B es un potente modelo de lenguaje visual, que admite el procesamiento multimodal de imágenes y texto, capaz de identificar con precisión el contenido de las imágenes y generar descripciones o respuestas relacionadas."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B es un potente modelo de lenguaje visual, que admite el procesamiento multimodal de imágenes y texto, capaz de identificar con precisión el contenido de las imágenes y generar descripciones o respuestas relacionadas."}, "Kolors": {"description": "Kolors es un modelo de generación de imágenes a partir de texto desarrollado por el equipo Kolors de Kuaishou. Entrenado con miles de millones de parámetros, destaca en calidad visual, comprensión semántica del chino y renderizado de texto."}, "Kwai-Kolors/Kolors": {"description": "Kolors es un modelo de generación de imágenes a partir de texto a gran escala basado en difusión latente, desarrollado por el equipo Kolors de Kuaishou. Entrenado con miles de millones de pares texto-imagen, muestra ventajas significativas en calidad visual, precisión semántica compleja y renderizado de caracteres en chino e inglés. Soporta entradas en ambos idiomas y sobresale en la comprensión y generación de contenido específico en chino."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Capacidad de razonamiento de imágenes excepcional en imágenes de alta resolución, adecuada para aplicaciones de comprensión visual."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Capacidad avanzada de razonamiento de imágenes para aplicaciones de agentes de comprensión visual."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Modelo de texto ajustado por instrucciones de Llama 3.1, optimizado para casos de uso de diálogos multilingües, que se destaca en muchos modelos de chat de código abierto y cerrados en benchmarks de la industria comunes."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Modelo de texto ajustado por instrucciones de Llama 3.1, optimizado para casos de uso de diálogos multilingües, que se destaca en muchos modelos de chat de código abierto y cerrados en benchmarks de la industria comunes."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Modelo de texto ajustado por instrucciones de Llama 3.1, optimizado para casos de uso de diálogos multilingües, que se destaca en muchos modelos de chat de código abierto y cerrados en benchmarks de la industria comunes."}, "Meta-Llama-3.2-1B-Instruct": {"description": "Modelo de lenguaje pequeño de última generación, con comprensión del lenguaje, excelente capacidad de razonamiento y generación de texto."}, "Meta-Llama-3.2-3B-Instruct": {"description": "Modelo de lenguaje pequeño de última generación, con comprensión del lenguaje, excelente capacidad de razonamiento y generación de texto."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 es el modelo de lenguaje de código abierto multilingüe más avanzado de la serie Llama, que ofrece un rendimiento comparable al modelo de 405B a un costo extremadamente bajo. Basado en la estructura Transformer, y mejorado en utilidad y seguridad a través de ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF). Su versión ajustada por instrucciones está optimizada para diálogos multilingües, superando a muchos modelos de chat de código abierto y cerrados en múltiples benchmarks de la industria. La fecha límite de conocimiento es diciembre de 2023."}, "MiniMax-M1": {"description": "Modelo de inferencia de desarrollo propio completamente nuevo. Líder mund<PERSON>: 80K cadenas de pensamiento x 1M de entradas, con un rendimiento comparable a los modelos de vanguardia internacionales."}, "MiniMax-Text-01": {"description": "En la serie de modelos MiniMax-01, hemos realizado una innovación audaz: la implementación a gran escala del mecanismo de atención lineal, donde la arquitectura Transformer tradicional ya no es la única opción. Este modelo tiene una cantidad de parámetros de hasta 456 mil millones, con 45.9 mil millones por activación. El rendimiento general del modelo es comparable a los mejores modelos internacionales, y puede manejar de manera eficiente contextos de hasta 4 millones de tokens, que es 32 veces más que GPT-4o y 20 veces más que Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 es un modelo de inferencia de atención mixta a gran escala con pesos de código abierto, que cuenta con 456 mil millones de parámetros, activando aproximadamente 45.9 mil millones de parámetros por token. El modelo soporta de forma nativa contextos ultra largos de hasta 1 millón de tokens y, gracias a su mecanismo de atención relámpago, reduce en un 75 % las operaciones de punto flotante en tareas de generación de 100 mil tokens en comparación con DeepSeek R1. Además, MiniMax-M1 utiliza una arquitectura MoE (Mezcla de Expertos), combinando el algoritmo CISPO y un diseño de atención mixta para un entrenamiento eficiente mediante aprendizaje reforzado, logrando un rendimiento líder en la industria en inferencia con entradas largas y escenarios reales de ingeniería de software."}, "Moonshot-Kimi-K2-Instruct": {"description": "Con un total de 1 billón de parámetros y 32 mil millones de parámetros activados, este modelo no reflexivo alcanza niveles de vanguardia en conocimiento avanzado, matemáticas y codificación, destacando en tareas generales de agentes. Optimizado para tareas de agentes, no solo responde preguntas sino que también puede actuar. Ideal para conversaciones improvisadas, chat general y experiencias de agentes, es un modelo de nivel reflexivo que no requiere largos tiempos de pensamiento."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) es un modelo de instrucciones de alta precisión, adecuado para cálculos complejos."}, "OmniConsistency": {"description": "OmniConsistency mejora la consistencia de estilo y la capacidad de generalización en tareas de imagen a imagen mediante la introducción de grandes Diffusion Transformers (DiTs) y datos estilizados emparejados, evitando la degradación del estilo."}, "Phi-3-medium-128k-instruct": {"description": "El mismo modelo Phi-3-medium, pero con un tamaño de contexto más grande para RAG o indicaciones de pocos disparos."}, "Phi-3-medium-4k-instruct": {"description": "Un modelo de 14B parámetros, que demuestra mejor calidad que Phi-3-mini, con un enfoque en datos densos de razonamiento de alta calidad."}, "Phi-3-mini-128k-instruct": {"description": "El mismo modelo Phi-3-mini, pero con un tamaño de contexto más grande para RAG o indicaciones de pocos disparos."}, "Phi-3-mini-4k-instruct": {"description": "El miembro más pequeño de la familia Phi-3. Optimizad<PERSON> tanto para calidad como para baja latencia."}, "Phi-3-small-128k-instruct": {"description": "El mismo modelo Phi-3-small, pero con un tamaño de contexto más grande para RAG o indicaciones de pocos disparos."}, "Phi-3-small-8k-instruct": {"description": "Un modelo de 7B parámetros, que demuestra mejor calidad que Phi-3-mini, con un enfoque en datos densos de razonamiento de alta calidad."}, "Phi-3.5-mini-instruct": {"description": "Versión actualizada del modelo Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "Versión actualizada del modelo Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct es un modelo de lenguaje a gran escala de ajuste fino por instrucciones dentro de la serie Qwen2, con un tamaño de parámetros de 7B. Este modelo se basa en la arquitectura Transformer, utilizando funciones de activación SwiGLU, sesgos de atención QKV y atención de consulta agrupada, entre otras técnicas. Es capaz de manejar entradas a gran escala. Este modelo ha destacado en múltiples pruebas de referencia en comprensión del lenguaje, generación, capacidad multilingüe, codificación, matemáticas y razonamiento, superando a la mayoría de los modelos de código abierto y mostrando competitividad comparable a modelos propietarios en ciertas tareas. Qwen2-7B-Instruct ha mostrado mejoras significativas en múltiples evaluaciones en comparación con Qwen1.5-7B-Chat."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct es uno de los últimos modelos de lenguaje a gran escala lanzados por Alibaba Cloud. Este modelo de 7B ha mejorado significativamente en áreas como codificación y matemáticas. También ofrece soporte multilingüe, abarcando más de 29 idiomas, incluidos chino e inglés. El modelo ha mostrado mejoras significativas en el seguimiento de instrucciones, comprensión de datos estructurados y generación de salidas estructuradas (especialmente JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct es la última versión de la serie de modelos de lenguaje a gran escala específicos para código lanzada por Alibaba Cloud. Este modelo, basado en Qwen2.5, ha mejorado significativamente la generación, razonamiento y reparación de código a través de un entrenamiento con 55 billones de tokens. No solo ha mejorado la capacidad de codificación, sino que también ha mantenido ventajas en habilidades matemáticas y generales. El modelo proporciona una base más completa para aplicaciones prácticas como agentes de código."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL es el nuevo miembro de la serie Qwen, con potentes capacidades de comprensión visual. <PERSON>uede analizar texto, gráficos y diseños en imágenes, comprender videos largos y capturar eventos. Es capaz de razonar, manipular herramientas, admitir el posicionamiento de objetos en múltiples formatos y generar salidas estructuradas. Optimiza la resolución dinámica y la tasa de cuadros para la comprensión de videos, además de mejorar la eficiencia del codificador visual."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking es un modelo de lenguaje visual (VLM) de código abierto lanzado conjuntamente por Zhipu AI y el laboratorio KEG de la Universidad de Tsinghua, diseñado específicamente para manejar tareas cognitivas multimodales complejas. Este modelo se basa en el modelo base GLM-4-9B-0414 y mejora significativamente su capacidad y estabilidad de razonamiento multimodal mediante la introducción del mecanismo de razonamiento \"Cadena de Pensamiento\" (Chain-of-Thought) y la adopción de estrategias de aprendizaje reforzado."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat es la versión de código abierto de la serie de modelos preentrenados GLM-4 lanzada por Zhipu AI. Este modelo destaca en semántica, matemáticas, razonamiento, código y conocimiento. Además de soportar diálogos de múltiples turnos, GLM-4-9B-Chat también cuenta con funciones avanzadas como navegación web, ejecución de código, llamadas a herramientas personalizadas (Function Call) y razonamiento de textos largos. El modelo admite 26 idiomas, incluidos chino, inglés, japonés, coreano y alemán. En múltiples pruebas de referencia, GLM-4-9B-Chat ha demostrado un rendimiento excepcional, como AlignBench-v2, MT-Bench, MMLU y C-Eval. Este modelo admite una longitud de contexto máxima de 128K, adecuado para investigación académica y aplicaciones comerciales."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 es un modelo de inferencia impulsado por aprendizaje por refuerzo (RL) que aborda problemas de repetitividad y legibilidad en el modelo. Antes del RL, DeepSeek-R1 introdujo datos de arranque en frío, optimizando aún más el rendimiento de inferencia. Se desempeña de manera comparable a OpenAI-o1 en tareas matemáticas, de código e inferencia, y mejora el rendimiento general a través de métodos de entrenamiento cuidadosamente diseñados."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-7B es un modelo obtenido mediante destilación de conocimiento basado en Qwen2.5-Math-7B. Este modelo se ha ajustado utilizando 800.000 muestras seleccionadas generadas por DeepSeek-R1, demostrando una excelente capacidad de razonamiento. Ha mostrado un rendimiento sobresaliente en múltiples pruebas de referencia, alcanzando un 92,8% de precisión en MATH-500, un 55,5% de tasa de aprobación en AIME 2024 y una puntuación de 1189 en CodeForces, lo que demuestra una fuerte capacidad matemática y de programación para un modelo de escala 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 es un modelo de lenguaje de expertos mixtos (MoE) con 671 mil millones de parámetros, que utiliza atención potencial de múl<PERSON>les cabezas (MLA) y la arquitectura DeepSeekMoE, combinando estrategias de balanceo de carga sin pérdidas auxiliares para optimizar la eficiencia de inferencia y entrenamiento. Preentrenado en 14.8 billones de tokens de alta calidad, y ajustado mediante supervisión y aprendizaje por refuerzo, DeepSeek-V3 supera a otros modelos de código abierto y se acerca a los modelos cerrados líderes."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 es un modelo base con arquitectura MoE que posee capacidades avanzadas de codificación y agentes, con un total de 1 billón de parámetros y 32 mil millones de parámetros activados. En pruebas de referencia en categorías principales como razonamiento general, programación, matemáticas y agentes, el rendimiento del modelo K2 supera a otros modelos de código abierto populares."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview es un modelo de procesamiento de lenguaje natural innovador, capaz de manejar de manera eficiente tareas complejas de generación de diálogos y comprensión del contexto."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview es un modelo de investigación desarrollado por el equipo de Qwen, enfocado en la capacidad de razonamiento visual, que tiene ventajas únicas en la comprensión de escenas complejas y en la resolución de problemas matemáticos relacionados con la visión."}, "Qwen/QwQ-32B": {"description": "QwQ es el modelo de inferencia de la serie Qwen. A diferencia de los modelos tradicionales de ajuste por instrucciones, QwQ posee habilidades de pensamiento e inferencia, lo que le permite lograr un rendimiento significativamente mejorado en tareas posteriores, especialmente en la resolución de problemas difíciles. QwQ-32B es un modelo de inferencia de tamaño mediano que puede competir en rendimiento con los modelos de inferencia más avanzados (como DeepSeek-R1, o1-mini). Este modelo utiliza tecnologías como RoPE, SwiGLU, RMSNorm y sesgo de atención QKV, y cuenta con una estructura de red de 64 capas y 40 cabezas de atención Q (en la arquitectura GQA, KV es de 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview es el último modelo de investigación experimental de Qwen, enfocado en mejorar la capacidad de razonamiento de la IA. A través de la exploración de mecanismos complejos como la mezcla de lenguajes y el razonamiento recursivo, sus principales ventajas incluyen una poderosa capacidad de análisis de razonamiento, así como habilidades matemáticas y de programación. Sin embargo, también presenta problemas de cambio de idioma, ciclos de razonamiento, consideraciones de seguridad y diferencias en otras capacidades."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 es un modelo de lenguaje general a<PERSON>o, que soporta múltiples tipos de instrucciones."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct es un modelo de lenguaje a gran escala de ajuste fino por instrucciones dentro de la serie Qwen2, con un tamaño de parámetros de 72B. Este modelo se basa en la arquitectura Transformer, utilizando funciones de activación SwiGLU, sesgos de atención QKV y atención de consulta agrupada, entre otras técnicas. Es capaz de manejar entradas a gran escala. Este modelo ha destacado en múltiples pruebas de referencia en comprensión del lenguaje, generación, capacidad multilingüe, codificación, matemáticas y razonamiento, superando a la mayoría de los modelos de código abierto y mostrando competitividad comparable a modelos propietarios en ciertas tareas."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL es la última iteración del modelo Qwen-VL, alcanzando un rendimiento de vanguardia en pruebas de comprensión visual."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 es una nueva serie de modelos de lenguaje a gran escala, diseñada para optimizar el procesamiento de tareas de instrucción."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 es una nueva serie de modelos de lenguaje a gran escala, diseñada para optimizar el procesamiento de tareas de instrucción."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Modelo de lenguaje de gran escala desarrollado por el equipo de Tongyi <PERSON>wen de Alibaba Cloud"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 es una nueva serie de grandes modelos de lenguaje, con capacidades de comprensión y generación más fuertes."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 es una nueva serie de grandes modelos de lenguaje, diseñada para optimizar el manejo de tareas instructivas."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 es una nueva serie de modelos de lenguaje a gran escala, diseñada para optimizar el procesamiento de tareas de instrucción."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 es una nueva serie de grandes modelos de lenguaje, diseñada para optimizar el manejo de tareas instructivas."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder se centra en la escritura de código."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct es la última versión de la serie de modelos de lenguaje a gran escala específicos para código lanzada por Alibaba Cloud. Este modelo, basado en Qwen2.5, ha mejorado significativamente la generación, razonamiento y reparación de código a través de un entrenamiento con 55 billones de tokens. No solo ha mejorado la capacidad de codificación, sino que también ha mantenido ventajas en habilidades matemáticas y generales. El modelo proporciona una base más completa para aplicaciones prácticas como agentes de código."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct es un modelo multimodal avanzado desarrollado por el equipo <PERSON>, que forma parte de la serie Qwen2.5-VL. Este modelo no solo domina el reconocimiento de objetos comunes, sino que también puede analizar texto, gr<PERSON><PERSON><PERSON>, iconos, diagramas y diseños en imágenes. Funciona como un agente visual inteligente capaz de razonar y manipular herramientas dinámicamente, con habilidades para operar computadoras y dispositivos móviles. Además, el modelo puede localizar con precisión objetos en imágenes y generar salidas estructuradas para documentos como facturas y tablas. En comparación con su predecesor Qwen2-VL, esta versión ha mejorado significativamente sus capacidades matemáticas y de resolución de problemas mediante aprendizaje por refuerzo, y su estilo de respuesta se ha optimizado para adaptarse mejor a las preferencias humanas."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL es el modelo de lenguaje visual de la serie Qwen2.5. Este modelo presenta mejoras significativas en múltiples aspectos: posee una mayor capacidad de comprensión visual, pudiendo reconocer objetos comunes, analizar texto, gráficos y diseños; como agente visual puede razonar y guiar dinámicamente el uso de herramientas; soporta la comprensión de videos largos de más de 1 hora capturando eventos clave; es capaz de localizar objetos en imágenes con precisión generando cuadros delimitadores o puntos; y admite la generación de salidas estructuradas, especialmente útil para datos escaneados como facturas o tablas."}, "Qwen/Qwen3-14B": {"description": "Qwen3 es un nuevo modelo de Tongyi Qianwen de próxima generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, general, agente y múltiples idiomas, y admite el cambio de modo de pensamiento."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 es un nuevo modelo de Tongyi Qianwen de próxima generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, general, agente y múltiples idiomas, y admite el cambio de modo de pensamiento."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 es un modelo de lenguaje grande híbrido experto (MoE) de nivel insignia desarrollado por el equipo Tongyi Qianwen de Alibaba Cloud. Cuenta con 235 mil millones de parámetros totales y activa 22 mil millones por inferencia. Es una versión actualizada del modo no reflexivo Qwen3-235B-A22B, enfocada en mejorar significativamente el cumplimiento de instrucciones, razonamiento lógico, comprensión textual, matemáticas, ciencias, programación y uso de herramientas. Además, amplía la cobertura de conocimientos multilingües y mejora la alineación con las preferencias del usuario en tareas subjetivas y abiertas para generar textos más útiles y de alta calidad."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 es un modelo de lenguaje grande de la serie Qwen3 desarrollado por el equipo Tongyi <PERSON> Alibaba, especializado en tareas complejas de razonamiento avanzado. Basado en arquitectura MoE, cuenta con 235 mil millones de parámetros totales y activa aproximadamente 22 mil millones por token, mejorando la eficiencia computacional sin sacrificar rendimiento. Como modelo dedicado al “pensamiento”, destaca en razonamiento lógico, matemáticas, ciencias, programación y pruebas académicas que requieren conocimiento experto, alcanzando niveles líderes en modelos reflexivos de código abierto. También mejora capacidades generales como cumplimiento de instrucciones, uso de herramientas y generación de texto, y soporta nativamente comprensión de contexto largo de hasta 256K tokens, ideal para escenarios que requieren razonamiento profundo y manejo de documentos extensos."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 es un nuevo modelo de Tongyi Qianwen de próxima generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, general, agente y múltiples idiomas, y admite el cambio de modo de pensamiento."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 es una versión actualizada del modelo Qwen3-30B-A3B en modo no reflexivo. Es un modelo de expertos mixtos (MoE) con un total de 30.5 mil millones de parámetros y 3.3 mil millones de parámetros activados. El modelo ha mejorado significativamente en varios aspectos, incluyendo el seguimiento de instrucciones, razonamiento lógico, comprensión de texto, matemáticas, ciencias, codificación y uso de herramientas. Además, ha logrado avances sustanciales en la cobertura de conocimientos multilingües de cola larga y se alinea mejor con las preferencias del usuario en tareas subjetivas y abiertas, generando respuestas más útiles y textos de mayor calidad. También se ha mejorado la capacidad de comprensión de textos largos hasta 256K. Este modelo solo soporta el modo no reflexivo y no genera etiquetas `<think></think>` en su salida."}, "Qwen/Qwen3-32B": {"description": "Qwen3 es un nuevo modelo de Tongyi Qianwen de próxima generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, general, agente y múltiples idiomas, y admite el cambio de modo de pensamiento."}, "Qwen/Qwen3-8B": {"description": "Qwen3 es un nuevo modelo de Tongyi Qianwen de próxima generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, general, agente y múltiples idiomas, y admite el cambio de modo de pensamiento."}, "Qwen2-72B-Instruct": {"description": "Qwen2 es la última serie del modelo Qwen, que admite un contexto de 128k. En comparación con los modelos de código abierto más óptimos actuales, Qwen2-72B supera significativamente a los modelos líderes actuales en comprensión del lenguaje natural, conocimiento, código, matemáticas y capacidades multilingües."}, "Qwen2-7B-Instruct": {"description": "Qwen2 es la última serie del modelo Qwen, capaz de superar a los modelos de código abierto de tamaño equivalente e incluso a modelos de mayor tamaño. Qwen2 7B ha logrado ventajas significativas en múltiples evaluaciones, especialmente en comprensión de código y chino."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B es un potente modelo de lenguaje visual que admite el procesamiento multimodal de imágenes y texto, capaz de identificar con precisión el contenido de las imágenes y generar descripciones o respuestas relacionadas."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct es un modelo de lenguaje grande de 14 mil millones de parámetros, con un rendimiento excelente, optimizado para escenarios en chino y multilingües, que admite aplicaciones de preguntas y respuestas inteligentes, generación de contenido, entre otros."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct es un modelo de lenguaje grande de 32 mil millones de parámetros, con un rendimiento equilibrado, optimizado para escenarios en chino y multilingües, que admite aplicaciones de preguntas y respuestas inteligentes, generación de contenido, entre otros."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct admite un contexto de 16k, generando textos largos de más de 8K. Soporta llamadas a funciones e interacción sin problemas con sistemas externos, lo que mejora enormemente la flexibilidad y escalabilidad. El conocimiento del modelo ha aumentado significativamente, y se ha mejorado considerablemente la capacidad de codificación y matemáticas, con soporte para más de 29 idiomas."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct es un modelo de lenguaje grande de 7 mil millones de parámetros, que admite llamadas a funciones e interacción sin problemas con sistemas externos, mejorando enormemente la flexibilidad y escalabilidad. Optimizado para escenarios en chino y multilingües, admite aplicaciones de preguntas y respuestas inteligentes, generación de contenido, entre otros."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct es un modelo de instrucciones de programación basado en un preentrenamiento a gran escala, con una potente capacidad de comprensión y generación de código, capaz de manejar eficientemente diversas tareas de programación, especialmente adecuado para la escritura inteligente de código, generación de scripts automatizados y resolución de problemas de programación."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct es un modelo de lenguaje grande diseñado específicamente para la generación de código, comprensión de código y escenarios de desarrollo eficiente, con una escala de 32B parámetros, líder en la industria, capaz de satisfacer diversas necesidades de programación."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B es un modelo MoE (modelo de expertos mixtos) que introduce el “modo de razonamiento mixto”, permitiendo a los usuarios cambiar sin problemas entre el “modo reflexivo” y el “modo no reflexivo”. Soporta la comprensión y el razonamiento en 119 idiomas y dialectos, y cuenta con una potente capacidad de invocación de herramientas. En pruebas de referencia que evalúan capacidades generales, código y matemáticas, multilingüismo, conocimiento y razonamiento, compite con los principales modelos del mercado como DeepSeek R1, OpenAI o1, o3-mini, Grok 3 y Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B es un modelo denso (Dense Model) que introduce el “modo de razonamiento mixto”, permitiendo a los usuarios cambiar sin problemas entre el “modo reflexivo” y el “modo no reflexivo”. G<PERSON>ias a mejoras en la arquitectura del modelo, aumento de datos de entrenamiento y métodos de entrenamiento más eficientes, su rendimiento general es comparable al de Qwen2.5-72B."}, "SenseChat": {"description": "Modelo de versión básica (V4), longitud de contexto de 4K, con potentes capacidades generales."}, "SenseChat-128K": {"description": "Modelo de versión básica (V4), longitud de contexto de 128K, se destaca en tareas de comprensión y generación de textos largos."}, "SenseChat-32K": {"description": "Modelo de versión básica (V4), longitud de contexto de 32K, aplicable de manera flexible en diversos escenarios."}, "SenseChat-5": {"description": "Modelo de última versión (V5.5), longitud de contexto de 128K, con capacidades significativamente mejoradas en razonamiento matemático, diálogos en inglés, seguimiento de instrucciones y comprensión de textos largos, comparable a GPT-4o."}, "SenseChat-5-1202": {"description": "Basado en la versión más reciente V5.5, con mejoras significativas en capacidades básicas en chino e inglés, chat, conocimientos científicos y humanísticos, redacción, lógica matemática y control de longitud de texto."}, "SenseChat-5-Cantonese": {"description": "Longitud de contexto de 32K, supera a GPT-4 en la comprensión de diálogos en cantonés, siendo comparable a GPT-4 Turbo en múltiples áreas como conocimiento, razonamiento, matemáticas y programación."}, "SenseChat-5-beta": {"description": "Rendimiento superior en algunos aspectos en comparación con SenseCat-5-1202"}, "SenseChat-Character": {"description": "<PERSON><PERSON>, longitud de contexto de 8K, alta velocidad de respuesta."}, "SenseChat-Character-Pro": {"description": "Modelo de versión avanzada, longitud de contexto de 32K, con capacidades completamente mejoradas, admite diálogos en chino/inglés."}, "SenseChat-Turbo": {"description": "Adecuado para preguntas rápidas y escenarios de ajuste fino del modelo."}, "SenseChat-Turbo-1202": {"description": "Es la última versión ligera del modelo, alcanzando más del 90% de la capacidad del modelo completo, reduciendo significativamente el costo de inferencia."}, "SenseChat-Vision": {"description": "La última versión del modelo (V5.5) admite la entrada de múltiples imágenes, logrando una optimización completa de las capacidades básicas del modelo, con mejoras significativas en el reconocimiento de atributos de objetos, relaciones espaciales, reconocimiento de eventos de acción, comprensión de escenas, reconocimiento de emociones, razonamiento lógico y comprensión y generación de texto."}, "SenseNova-V6-5-Pro": {"description": "Mediante una actualización integral de datos multimodales, lingüísticos y de razonamiento, junto con la optimización de estrategias de entrenamiento, el nuevo modelo ha logrado mejoras significativas en el razonamiento multimodal y la capacidad de seguimiento de instrucciones generalizadas. Soporta una ventana de contexto de hasta 128k y destaca en tareas especializadas como OCR y reconocimiento de IP en turismo y cultura."}, "SenseNova-V6-5-Turbo": {"description": "Mediante una actualización integral de datos multimodales, lingüísticos y de razonamiento, junto con la optimización de estrategias de entrenamiento, el nuevo modelo ha logrado mejoras significativas en el razonamiento multimodal y la capacidad de seguimiento de instrucciones generalizadas. Soporta una ventana de contexto de hasta 128k y destaca en tareas especializadas como OCR y reconocimiento de IP en turismo y cultura."}, "SenseNova-V6-Pro": {"description": "Logra una unificación nativa de capacidades de imagen, texto y video, superando las limitaciones tradicionales de la multimodalidad discreta, y ha ganado el doble campeonato en las evaluaciones de OpenCompass y SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "Equilibra el razonamiento profundo visual y lingüístico, logrando un pensamiento lento y un razonamiento profundo, presentando un proceso completo de cadena de pensamiento."}, "SenseNova-V6-Turbo": {"description": "Logra una unificación nativa de capacidades de imagen, texto y video, superando las limitaciones tradicionales de la multimodalidad discreta, liderando en dimensiones clave como capacidades multimodales y lingüísticas, combinando literatura y ciencia, y ocupando repetidamente el nivel de la primera división en múltiples evaluaciones tanto nacionales como internacionales."}, "Skylark2-lite-8k": {"description": "El modelo de segunda generación Skaylark (Skylark), el modelo Skylark2-lite, tiene una alta velocidad de respuesta, adecuado para escenarios donde se requiere alta inmediatez, sensibilidad de costos y baja necesidad de precisión del modelo, con una longitud de ventana de contexto de 8k."}, "Skylark2-pro-32k": {"description": "El modelo de segunda generación Skaylark (Skylark), la versión Skylark2-pro, cuenta con una alta precisión, adecuada para escenarios de generación de texto más complejos, como redacción de copy en campos especializados, creación de novelas y traducciones de alta calidad, con una longitud de ventana de contexto de 32k."}, "Skylark2-pro-4k": {"description": "El modelo de segunda generación Skaylark (Skylark), el modelo Skylark2-pro, tiene una alta precisión, adecuado para escenarios de generación de texto más complejos, como redacción de copy en campos especializados, creación de novelas y traducciones de alta calidad, con una longitud de ventana de contexto de 4k."}, "Skylark2-pro-character-4k": {"description": "El modelo de segunda generación Skaylark (Skylark), el modelo Skylark2-pro-character, presenta habilidades excepcionales para el juego de roles y la conversación, destacándose en interpretar diversos roles según las solicitudes del usuario, con un contenido conversacional natural y fluido, ideal para la construcción de chatbots, asistentes virtuales y servicios al cliente en línea, con una alta velocidad de respuesta."}, "Skylark2-pro-turbo-8k": {"description": "El modelo de segunda generación Skaylark (Skylark), Skylark2-pro-turbo-8k, ofrece una inferencia más rápida y costos más bajos, con una longitud de ventana de contexto de 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 es el nuevo modelo de código abierto de la serie GLM, con 32 mil millones de parámetros. Su rendimiento es comparable a las series GPT de OpenAI y V3/R1 de DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 es un modelo pequeño de la serie GLM, con 9 mil millones de parámetros. Este modelo hereda las características técnicas de la serie GLM-4-32B, pero ofrece opciones de implementación más ligeras. A pesar de su menor tamaño, GLM-4-9B-0414 sigue mostrando habilidades sobresalientes en tareas de generación de código, diseño web, generación de gráficos SVG y redacción basada en búsqueda."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking es un modelo de lenguaje visual (VLM) de código abierto lanzado conjuntamente por Zhipu AI y el laboratorio KEG de la Universidad de Tsinghua, diseñado específicamente para manejar tareas cognitivas multimodales complejas. Este modelo se basa en el modelo base GLM-4-9B-0414 y mejora significativamente su capacidad y estabilidad de razonamiento multimodal mediante la introducción del mecanismo de razonamiento \"Cadena de Pensamiento\" (Chain-of-Thought) y la adopción de estrategias de aprendizaje reforzado."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 es un modelo de inferencia con capacidad de pensamiento profundo. Este modelo se desarrolló a partir de GLM-4-32B-0414 mediante un arranque en frío y aprendizaje por refuerzo ampliado, y se entrenó adicionalmente en tareas de matemáticas, código y lógica. En comparación con el modelo base, GLM-Z1-32B-0414 mejora significativamente la capacidad matemática y la habilidad para resolver tareas complejas."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 es un modelo pequeño de la serie GLM, con solo 9 mil millones de parámetros, pero que muestra una capacidad sorprendente manteniendo la tradición de código abierto. A pesar de su menor tamaño, este modelo sigue destacando en razonamiento matemático y tareas generales, con un rendimiento general que se encuentra entre los mejores en modelos de código abierto de tamaño similar."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 es un modelo de inferencia profunda con capacidad de reflexión (en comparación con la investigación profunda de OpenAI). A diferencia de los modelos típicos de pensamiento profundo, el modelo de reflexión utiliza un tiempo de reflexión más prolongado para resolver problemas más abiertos y complejos."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B es una versión de código abierto, que proporciona una experiencia de conversación optimizada para aplicaciones de diálogo."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B es el primer modelo de razonamiento a gran escala con contexto largo entrenado mediante aprendizaje reforzado (LRM), optimizado para tareas de razonamiento con textos extensos. Utiliza un marco de aprendizaje reforzado con expansión progresiva de contexto, logrando una transición estable de contexto corto a largo. En siete pruebas de referencia de preguntas y respuestas con documentos de contexto largo, QwenLong-L1-32B supera a modelos insignia como OpenAI-o3-mini y Qwen3-235B-A22B, con un rendimiento comparable a Claude-3.7-Sonnet-Thinking. Destaca en razonamiento matemático, lógico y de múltiples saltos."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, manteniendo la excelente capacidad de lenguaje general de la serie original, ha mejorado significativamente la lógica matemática y la capacidad de codificación mediante un entrenamiento incremental de 500 mil millones de tokens de alta calidad."}, "abab5.5-chat": {"description": "Orientado a escenarios de productividad, admite el procesamiento de tareas complejas y la generación eficiente de texto, adecuado para aplicaciones en campos profesionales."}, "abab5.5s-chat": {"description": "Diseñado para escenarios de diálogo de personajes en chino, ofrece capacidades de generación de diálogos de alta calidad en chino, adecuado para diversas aplicaciones."}, "abab6.5g-chat": {"description": "Diseñado para diálogos de personajes multilingües, admite generación de diálogos de alta calidad en inglés y otros idiomas."}, "abab6.5s-chat": {"description": "Adecuado para una amplia gama de tareas de procesamiento de lenguaje natural, incluyendo generación de texto, sistemas de diálogo, etc."}, "abab6.5t-chat": {"description": "Optimizado para escenarios de diálogo de personajes en chino, ofrece capacidades de generación de diálogos fluidos y acordes con las expresiones chinas."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 es un modelo de lenguaje grande de última generación, optimizado mediante aprendizaje por refuerzo y datos de arranque en frío, con un rendimiento excepcional en razonamiento, matemáticas y programación."}, "accounts/fireworks/models/deepseek-v3": {"description": "Modelo de lenguaje potente de Deepseek, basado en Mixture-of-Experts (MoE), con un total de 671B de parámetros, activando 37B de parámetros por cada token."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "El modelo de instrucciones Llama 3 70B está optimizado para diálogos multilingües y comprensión del lenguaje natural, superando el rendimiento de la mayoría de los modelos competidores."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "El modelo de instrucciones Llama 3 8B está optimizado para diálogos y tareas multilingües, ofreciendo un rendimiento excepcional y eficiente."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "El modelo de instrucciones Llama 3 8B (versión HF) es consistente con los resultados de la implementación oficial, ofreciendo alta consistencia y compatibilidad multiplataforma."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "El modelo de instrucciones Llama 3.1 405B, con parámetros de gran escala, es adecuado para tareas complejas y seguimiento de instrucciones en escenarios de alta carga."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "El modelo de instrucciones Llama 3.1 70B ofrece una capacidad excepcional de comprensión y generación de lenguaje, siendo la elección ideal para tareas de diálogo y análisis."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "El modelo de instrucciones Llama 3.1 8B está optimizado para diálogos multilingües, capaz de superar la mayoría de los modelos de código abierto y cerrado en estándares de la industria."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Modelo de razonamiento de imágenes de 11B parámetros ajustado por Meta. Este modelo está optimizado para el reconocimiento visual, razonamiento de imágenes, descripción de imágenes y respuestas a preguntas generales sobre imágenes. <PERSON>uede entender datos visuales, como gráficos y diagramas, y cerrar la brecha entre la visión y el lenguaje generando descripciones textuales de los detalles de las imágenes."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "El modelo de instrucciones Llama 3.2 3B es un modelo multilingüe ligero lanzado por Meta. Está diseñado para mejorar la eficiencia, ofreciendo mejoras significativas en latencia y costos en comparación con modelos más grandes. Ejemplos de uso de este modelo incluyen consultas, reescritura de indicaciones y asistencia en la escritura."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Modelo de razonamiento de imágenes de 90B parámetros ajustado por Meta. Este modelo está optimizado para el reconocimiento visual, razonamiento de imágenes, descripción de imágenes y respuestas a preguntas generales sobre imágenes. <PERSON>uede entender datos visuales, como gráficos y diagramas, y cerrar la brecha entre la visión y el lenguaje generando descripciones textuales de los detalles de las imágenes."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct es la versión actualizada de diciembre de Llama 3.1 70B. Este modelo ha sido mejorado sobre la base de Llama 3.1 70B (lanzado en julio de 2024), mejorando la invocación de herramientas, el soporte de texto multilingüe, así como las capacidades matemáticas y de programación. El modelo alcanza niveles de liderazgo en la industria en razonamiento, matemáticas y cumplimiento de instrucciones, y puede ofrecer un rendimiento similar al de 3.1 405B, al tiempo que presenta ventajas significativas en velocidad y costo."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Modelo de 24B parámetros, con capacidades de vanguardia comparables a modelos más grandes."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "El modelo de instrucciones Mixtral MoE 8x22B, con parámetros a gran escala y arquitectura de múltiples expertos, soporta de manera integral el procesamiento eficiente de tareas complejas."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "El modelo de instrucciones Mixtral MoE 8x7B, con una arquitectura de múltiples expertos, ofrece un seguimiento y ejecución de instrucciones eficientes."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "El modelo MythoMax L2 13B combina técnicas de fusión innovadoras, destacándose en narración y juegos de rol."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "El modelo de instrucciones Phi 3 Vision es un modelo multimodal ligero, capaz de manejar información visual y textual compleja, con una fuerte capacidad de razonamiento."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "El modelo QwQ es un modelo de investigación experimental desarrollado por el equipo de Qwen, enfocado en mejorar la capacidad de razonamiento de la IA."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "La versión de 72B del modelo Qwen-VL es el resultado de la última iteración de Alibaba, representando casi un año de innovación."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 es una serie de modelos de lenguaje solo decodificadores desarrollados por el equipo Qwen de Alibaba Cloud. Estos modelos ofrecen diferentes tamaños, incluidos 0.5B, 1.5B, 3B, 7B, 14B, 32B y 72B, y tienen variantes base y de instrucciones."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct es la última versión de la serie de modelos de lenguaje a gran escala específicos para código lanzada por Alibaba Cloud. Este modelo, basado en Qwen2.5, ha mejorado significativamente la generación, razonamiento y reparación de código a través de un entrenamiento con 55 billones de tokens. No solo ha mejorado la capacidad de codificación, sino que también ha mantenido ventajas en habilidades matemáticas y generales. El modelo proporciona una base más completa para aplicaciones prácticas como agentes de código."}, "accounts/yi-01-ai/models/yi-large": {"description": "El modelo Yi-Large ofrece una capacidad de procesamiento multilingüe excepcional, adecuado para diversas tareas de generación y comprensión de lenguaje."}, "ai21-jamba-1.5-large": {"description": "Un modelo multilingüe de 398B parámetros (94B activos), que ofrece una ventana de contexto larga de 256K, llamada a funciones, salida estructurada y generación fundamentada."}, "ai21-jamba-1.5-mini": {"description": "Un modelo multilingüe de 52B parámetros (12B activos), que ofrece una ventana de contexto larga de 256K, llamada a funciones, salida estructurada y generación fundamentada."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Un modelo multilingüe de 398 mil millones de parámetros (94 mil millones activos), que ofrece una ventana de contexto larga de 256K, llamadas a funciones, salida estructurada y generación basada en hechos."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Un modelo multilingüe de 52 mil millones de parámetros (12 mil millones activos), que ofrece una ventana de contexto larga de 256K, llamadas a funciones, salida estructurada y generación basada en hechos."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet eleva el estándar de la industria, superando a modelos competidores y a Claude 3 Opus, destacándose en evaluaciones amplias, mientras mantiene la velocidad y costo de nuestros modelos de nivel medio."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet ha elevado los estándares de la industria, superando el rendimiento de modelos competidores y de Claude 3 Opus, destacándose en evaluaciones amplias, mientras mantiene la velocidad y el costo de nuestros modelos de nivel medio."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku es el modelo más rápido y compacto de Anthropic, ofreciendo una velocidad de respuesta casi instantánea. Puede responder rápidamente a consultas y solicitudes simples. Los clientes podrán construir experiencias de IA sin costuras que imiten la interacción humana. Claude 3 Haiku puede manejar imágenes y devolver salidas de texto, con una ventana de contexto de 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus es el modelo de IA más potente de Anthropic, con un rendimiento de vanguardia en tareas altamente complejas. Puede manejar indicaciones abiertas y escenarios no vistos, con una fluidez y comprensión humana excepcionales. Claude 3 Opus muestra la vanguardia de las posibilidades de la IA generativa. Claude 3 Opus puede manejar imágenes y devolver salidas de texto, con una ventana de contexto de 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet de Anthropic logra un equilibrio ideal entre inteligencia y velocidad, especialmente adecuado para cargas de trabajo empresariales. Ofrece la máxima utilidad a un costo inferior al de los competidores, diseñado para ser un modelo confiable y duradero, apto para implementaciones de IA a gran escala. Claude 3 Sonnet puede manejar imágenes y devolver salidas de texto, con una ventana de contexto de 200K."}, "anthropic.claude-instant-v1": {"description": "Un modelo rápido, económico y aún muy capaz, que puede manejar una variedad de tareas, incluyendo conversaciones cotidianas, análisis de texto, resúmenes y preguntas y respuestas de documentos."}, "anthropic.claude-v2": {"description": "Anthropic muestra un modelo con alta capacidad en una amplia gama de tareas, desde diálogos complejos y generación de contenido creativo hasta el seguimiento detallado de instrucciones."}, "anthropic.claude-v2:1": {"description": "La versión actualizada de Claude 2, con el doble de ventana de contexto, así como mejoras en la fiabilidad, tasa de alucinaciones y precisión basada en evidencia en contextos de documentos largos y RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku es el modelo más rápido y compacto de Anthropic, diseñado para lograr respuestas casi instantáneas. Tiene un rendimiento de orientación rápido y preciso."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus es el modelo más potente de Anthropic para manejar tareas altamente complejas. Destaca en rendimiento, inteligencia, fluidez y comprensión."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku es el modelo de próxima generación más rápido de Anthropic. En comparación con Claude 3 <PERSON><PERSON>, Claude 3.5 Haiku ha mejorado en todas las habilidades y ha superado al modelo más grande de la generación anterior, Claude 3 Opus, en muchas pruebas de inteligencia."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet ofrece capacidades que superan a Opus y una velocidad más rápida que Sonnet, manteniendo el mismo precio que Sonnet. Sonnet es especialmente hábil en programación, ciencia de datos, procesamiento visual y tareas de agente."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet es el modelo más inteligente de Anthropic hasta la fecha y el primer modelo de razonamiento híbrido en el mercado. Claude 3.7 Sonnet puede generar respuestas casi instantáneas o un pensamiento prolongado y gradual, permitiendo a los usuarios observar claramente estos procesos. Sonnet es especialmente hábil en programación, ciencia de datos, procesamiento visual y tareas de agente."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 es el modelo más potente de Anthropic para manejar tareas altamente complejas. Destaca por su rendimiento, inteligencia, fluidez y capacidad de comprensión excepcionales."}, "anthropic/claude-sonnet-4": {"description": "Claude <PERSON> 4 puede generar respuestas casi instantáneas o razonamientos prolongados paso a paso, que los usuarios pueden seguir claramente. Los usuarios de la API también pueden controlar con precisión el tiempo de reflexión del modelo."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B es un modelo de lenguaje grande disperso con 72 mil millones de parámetros y 16 mil millones de parámetros activados. Está basado en la arquitectura de expertos mixtos agrupados (MoGE), que agrupa expertos durante la selección y restringe la activación de un número igual de expertos por grupo para cada token, logrando un balance de carga entre expertos y mejorando significativamente la eficiencia de despliegue en la plataforma Ascend."}, "aya": {"description": "Aya 23 es un modelo multilingüe lanzado por Cohere, que admite 23 idiomas, facilitando aplicaciones de lenguaje diversas."}, "aya:35b": {"description": "Aya 23 es un modelo multilingüe lanzado por Cohere, que admite 23 idiomas, facilitando aplicaciones de lenguaje diversas."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B es un modelo de lenguaje de gran escala de código abierto y comercializable desarrollado por Baichuan Intelligence, que cuenta con 13 mil millones de parámetros y ha logrado los mejores resultados en benchmarks autorizados en chino e inglés."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B es un modelo de lenguaje grande desarrollado por Baidu basado en la arquitectura de expertos mixtos (MoE). Cuenta con un total de 300 mil millones de parámetros, pero durante la inferencia solo activa 47 mil millones por token, equilibrando un rendimiento potente con eficiencia computacional. Como uno de los modelos centrales de la serie ERNIE 4.5, destaca en tareas de comprensión, generación, razonamiento y programación de texto. Emplea un innovador método de preentrenamiento multimodal heterogéneo MoE, que combina entrenamiento conjunto de texto y visión, mejorando la capacidad integral del modelo, especialmente en el seguimiento de instrucciones y la memoria de conocimientos del mundo."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse es un modelo multilingüe de alto rendimiento de 32B, diseñado para desafiar el rendimiento de los modelos monolingües a través de innovaciones en ajuste por instrucciones, arbitraje de datos, entrenamiento de preferencias y fusión de modelos. Soporta 23 idiomas."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse es un modelo multilingüe de alto rendimiento de 8B, diseñado para desafiar el rendimiento de los modelos monolingües a través de innovaciones en ajuste por instrucciones, arbitraje de datos, entrenamiento de preferencias y fusión de modelos. Soporta 23 idiomas."}, "c4ai-aya-vision-32b": {"description": "Aya Vision es un modelo multimodal de última generación, que destaca en múltiples benchmarks clave de capacidades lingüísticas, textuales y visuales. Soporta 23 idiomas. Esta versión de 32B se centra en el rendimiento multilingüe de vanguardia."}, "c4ai-aya-vision-8b": {"description": "Aya Vision es un modelo multimodal de última generación, que destaca en múltiples benchmarks clave de capacidades lingüísticas, textuales y visuales. Esta versión de 8B se centra en baja latencia y rendimiento óptimo."}, "charglm-3": {"description": "CharGLM-3 está diseñado para juegos de rol y acompañamiento emocional, soportando memoria de múltiples rondas y diálogos personalizados, con aplicaciones amplias."}, "charglm-4": {"description": "CharGLM-4 está diseñado para el juego de roles y la compañía emocional, soportando memoria de múltiples turnos de larga duración y diálogos personalizados, con aplicaciones amplias."}, "chatglm3": {"description": "ChatGLM3 es un modelo de código cerrado desarrollado por Zhipu AI y el Laboratorio KEG de Tsinghua. Ha sido preentrenado con una gran cantidad de identificadores en chino e inglés y ajustado a las preferencias humanas. En comparación con el modelo de primera generación, ha logrado mejoras del 16%, 36% y 280% en MMLU, C-Eval y GSM8K, respectivamente, y ha alcanzado el primer lugar en el ranking de tareas en chino C-Eval. Es adecuado para escenarios que requieren un alto nivel de conocimiento, capacidad de razonamiento y creatividad, como la redacción de anuncios, la escritura de novelas, la redacción de contenido de conocimiento y la generación de código."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base es el modelo base de la última generación de la serie ChatGLM, desarrollado por Zhipu, con una escala de 6.000 millones de parámetros y de código abierto."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o es un modelo dinámico que se actualiza en tiempo real para mantener la versión más actual. Combina una poderosa comprensión y generación de lenguaje, adecuado para aplicaciones a gran escala, incluyendo servicio al cliente, educación y soporte técnico."}, "claude-2.0": {"description": "Claude 2 ofrece avances en capacidades clave para empresas, incluyendo un contexto líder en la industria de 200K tokens, una reducción significativa en la tasa de alucinaciones del modelo, indicaciones del sistema y una nueva función de prueba: llamadas a herramientas."}, "claude-2.1": {"description": "Claude 2 ofrece avances en capacidades clave para empresas, incluyendo un contexto líder en la industria de 200K tokens, una reducción significativa en la tasa de alucinaciones del modelo, indicaciones del sistema y una nueva función de prueba: llamadas a herramientas."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku es el modelo de próxima generación más rápido de Anthropic. En comparación con Claude 3 Hai<PERSON>, Claude 3.5 Haiku ha mejorado en todas las habilidades y ha superado al modelo más grande de la generación anterior, Claude 3 Opus, en muchas pruebas de referencia de inteligencia."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet ofrece capacidades que superan a Opus y una velocidad más rápida que Sonnet, manteniendo el mismo precio que Sonnet. Sonnet es especialmente bueno en programación, ciencia de datos, procesamiento visual y tareas de agentes."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet ofrece capacidades que superan a Opus y una velocidad más rápida que Sonnet, manteniendo el mismo precio que Sonnet. Sonnet es especialmente hábil en programación, ciencia de datos, procesamiento visual y tareas de agencia."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet es el modelo de IA más potente de Anthropic, con un rendimiento de vanguardia en tareas altamente complejas. <PERSON>uede manejar indicaciones abiertas y escenarios no vistos, con una fluidez y comprensión humana excepcionales. Claude 3.7 Sonnet muestra la vanguardia de las posibilidades de la IA generativa."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku es el modelo más rápido y compacto de Anthropic, diseñado para lograr respuestas casi instantáneas. Tiene un rendimiento de orientación rápido y preciso."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus es el modelo más potente de Anthropic para manejar tareas altamente complejas. Destaca en rendimiento, inteligencia, fluidez y comprensión."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet proporciona un equilibrio ideal entre inteligencia y velocidad para cargas de trabajo empresariales. Ofrece la máxima utilidad a un costo más bajo, siendo fiable y adecuado para implementaciones a gran escala."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 es el modelo más potente de Anthropic para manejar tareas altamente complejas. Se destaca en rendimiento, inteligencia, fluidez y comprensión."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet puede generar respuestas casi instantáneas o un pensamiento gradual prolongado, permitiendo a los usuarios ver claramente estos procesos. Los usuarios de la API también pueden tener un control detallado sobre el tiempo de pensamiento del modelo."}, "codegeex-4": {"description": "CodeGeeX-4 es un potente asistente de programación AI, que admite preguntas y respuestas inteligentes y autocompletado de código en varios lenguajes de programación, mejorando la eficiencia del desarrollo."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B es un modelo de generación de código multilingüe, que admite funciones completas, incluyendo autocompletado y generación de código, intérprete de código, búsqueda en la web, llamadas a funciones y preguntas y respuestas de código a nivel de repositorio, cubriendo diversos escenarios de desarrollo de software. Es un modelo de generación de código de primer nivel con menos de 10B de parámetros."}, "codegemma": {"description": "CodeGemma es un modelo de lenguaje ligero especializado en diversas tareas de programación, que admite iteraciones rápidas e integración."}, "codegemma:2b": {"description": "CodeGemma es un modelo de lenguaje ligero especializado en diversas tareas de programación, que admite iteraciones rápidas e integración."}, "codellama": {"description": "Code Llama es un LLM enfocado en la generación y discusión de código, combinando un amplio soporte para lenguajes de programación, adecuado para entornos de desarrolladores."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama es un LLM enfocado en la generación y discusión de código, que combina un amplio soporte para lenguajes de programación, adecuado para entornos de desarrolladores."}, "codellama:13b": {"description": "Code Llama es un LLM enfocado en la generación y discusión de código, combinando un amplio soporte para lenguajes de programación, adecuado para entornos de desarrolladores."}, "codellama:34b": {"description": "Code Llama es un LLM enfocado en la generación y discusión de código, combinando un amplio soporte para lenguajes de programación, adecuado para entornos de desarrolladores."}, "codellama:70b": {"description": "Code Llama es un LLM enfocado en la generación y discusión de código, combinando un amplio soporte para lenguajes de programación, adecuado para entornos de desarrolladores."}, "codeqwen": {"description": "CodeQwen1.5 es un modelo de lenguaje a gran escala entrenado con una gran cantidad de datos de código, diseñado para resolver tareas de programación complejas."}, "codestral": {"description": "Codestral es el primer modelo de código de Mistral AI, que proporciona un excelente soporte para tareas de generación de código."}, "codestral-latest": {"description": "Codestral es un modelo generativo de vanguardia enfocado en la generación de código, optimizado para tareas de completado de código y relleno intermedio."}, "codex-mini-latest": {"description": "codex-mini-latest es una versión ajustada de o4-mini, diseñada específicamente para Codex CLI. Para uso directo a través de la API, recomendamos comenzar con gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B es un modelo diseñado para seguir instrucciones, diálogos y programación."}, "cogview-4": {"description": "CogView-4 es el primer modelo de generación de imágenes a partir de texto de código abierto de Zhipu que admite la generación de caracteres chinos. Ofrece mejoras integrales en la comprensión semántica, la calidad de generación de imágenes y la capacidad de generar texto en chino e inglés. Soporta entradas bilingües en chino e inglés de cualquier longitud y puede generar imágenes en cualquier resolución dentro del rango especificado."}, "cohere-command-r": {"description": "Command R es un modelo generativo escalable dirigido a RAG y uso de herramientas para habilitar IA a escala de producción para empresas."}, "cohere-command-r-plus": {"description": "Command R+ es un modelo optimizado para RAG de última generación diseñado para abordar cargas de trabajo de nivel empresarial."}, "cohere/Cohere-command-r": {"description": "Command R es un modelo generativo escalable diseñado para su uso con RAG y herramientas, que permite a las empresas implementar IA de nivel productivo."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ es un modelo optimizado de última generación para RAG, diseñado para manejar cargas de trabajo empresariales."}, "command": {"description": "Un modelo de conversación que sigue instrucciones, ofreciendo alta calidad y fiabilidad en tareas lingüísticas, además de tener una longitud de contexto más larga que nuestros modelos de generación básicos."}, "command-a-03-2025": {"description": "Command A es nuestro modelo más potente hasta la fecha, destacando en el uso de herramientas, agentes, generación aumentada por recuperación (RAG) y aplicaciones multilingües. Command A tiene una longitud de contexto de 256K, puede ejecutarse con solo dos GPU y ha mejorado su rendimiento en un 150% en comparación con Command R+ 08-2024."}, "command-light": {"description": "Una versión más pequeña y rápida de Command, casi igual de potente, pero más rápida."}, "command-light-nightly": {"description": "Para acortar el intervalo entre lanzamientos de versiones principales, hemos lanzado versiones nocturnas del modelo Command. Para la serie command-light, esta versión se llama command-light-nightly. Tenga en cuenta que command-light-nightly es la versión más reciente, experimental y (posiblemente) inestable. Las versiones nocturnas se actualizan regularmente sin previo aviso, por lo que no se recomienda su uso en entornos de producción."}, "command-nightly": {"description": "Para acortar el intervalo entre lanzamientos de versiones principales, hemos lanzado versiones nocturnas del modelo Command. Para la serie Command, esta versión se llama command-cightly. Tenga en cuenta que command-nightly es la versión más reciente, experimental y (posiblemente) inestable. Las versiones nocturnas se actualizan regularmente sin previo aviso, por lo que no se recomienda su uso en entornos de producción."}, "command-r": {"description": "Command R es un LLM optimizado para tareas de diálogo y contexto largo, especialmente adecuado para interacciones dinámicas y gestión del conocimiento."}, "command-r-03-2024": {"description": "Command R es un modelo de conversación que sigue instrucciones, ofreciendo una mayor calidad y fiabilidad en tareas lingüísticas, además de tener una longitud de contexto más larga que los modelos anteriores. Se puede utilizar en flujos de trabajo complejos, como generación de código, generación aumentada por recuperación (RAG), uso de herramientas y agentes."}, "command-r-08-2024": {"description": "command-r-08-2024 es una versión actualizada del modelo Command R, lanzada en agosto de 2024."}, "command-r-plus": {"description": "Command R+ es un modelo de lenguaje de gran tamaño de alto rendimiento, diseñado para escenarios empresariales reales y aplicaciones complejas."}, "command-r-plus-04-2024": {"description": "Command R+ es un modelo de conversación que sigue instrucciones, ofreciendo una mayor calidad y fiabilidad en tareas lingüísticas, además de tener una longitud de contexto más larga que los modelos anteriores. Es ideal para flujos de trabajo complejos de RAG y uso de herramientas en múltiples pasos."}, "command-r-plus-08-2024": {"description": "Command R+ es un modelo de conversación que sigue instrucciones, mostrando una mayor calidad y fiabilidad en tareas lingüísticas, con una longitud de contexto más larga en comparación con modelos anteriores. Es más adecuado para flujos de trabajo RAG complejos y el uso de herramientas en múltiples pasos."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 es una versión pequeña y eficiente, lanzada en diciembre de 2024. Destaca en tareas que requieren razonamiento complejo y procesamiento en múltiples pasos, como RAG, uso de herramientas y agentes."}, "compound-beta": {"description": "Compound-beta es un sistema de IA compuesto, respaldado por múltiples modelos de acceso abierto ya soportados en GroqCloud, que puede utilizar herramientas de manera inteligente y selectiva para responder a consultas de los usuarios."}, "compound-beta-mini": {"description": "Compound-beta-mini es un sistema de IA compuesto, respaldado por modelos de acceso abierto ya soportados en GroqCloud, que puede utilizar herramientas de manera inteligente y selectiva para responder a consultas de los usuarios."}, "computer-use-preview": {"description": "El modelo computer-use-preview está diseñado exclusivamente para \"herramientas de uso informático\", entrenado para comprender y ejecutar tareas relacionadas con computadoras."}, "dall-e-2": {"description": "El segundo modelo DALL·E, que admite generación de imágenes más realistas y precisas, con una resolución cuatro veces mayor que la de la primera generación."}, "dall-e-3": {"description": "El modelo DALL·E más reciente, lanzado en noviembre de 2023. Admite generación de imágenes más realistas y precisas, con una mayor capacidad de detalle."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct ofrece capacidades de procesamiento de instrucciones de alta fiabilidad, soportando aplicaciones en múltiples industrias."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 es un modelo de inferencia impulsado por aprendizaje reforzado (RL) que aborda los problemas de repetitividad y legibilidad en el modelo. Antes de RL, DeepSeek-R1 introdujo datos de arranque en frío, optimizando aún más el rendimiento de la inferencia. Su desempeño en tareas matemáticas, de código e inferencia es comparable al de OpenAI-o1, y ha mejorado su efectividad general a través de métodos de entrenamiento cuidadosamente diseñados."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 mejora significativamente la profundidad de razonamiento e inferencia mediante el uso de recursos computacionales aumentados y la introducción de mecanismos de optimización algorítmica en el postentrenamiento. Este modelo destaca en diversas evaluaciones de referencia, incluyendo matemáticas, programación y lógica general. Su rendimiento global se acerca a modelos líderes como O3 y Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B es un modelo obtenido mediante destilación de cadenas de pensamiento del modelo DeepSeek-R1-0528 al Qwen3 8B Base. Este modelo alcanza el estado del arte (SOTA) entre modelos de código abierto, superando a Qwen3 8B en un 10% en la prueba AIME 2024 y alcanzando el nivel de rendimiento de Qwen3-235B-thinking. Sobresale en razonamiento matemático, programación y lógica general, compartiendo arquitectura con Qwen3-8B pero utilizando la configuración de tokenizador de DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "El modelo de destilación DeepSeek-R1 optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "El modelo de destilación DeepSeek-R1 optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "El modelo de destilación DeepSeek-R1 optimiza el rendimiento de inferencia mediante aprendizaje por refuerzo y datos de arranque en frío, actualizando el estándar de múltiples tareas en modelos de código abierto."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B es un modelo obtenido mediante destilación de conocimiento basado en Qwen2.5-32B. Este modelo se ajustó utilizando 800,000 muestras seleccionadas generadas por DeepSeek-R1, mostrando un rendimiento excepcional en múltiples campos como matemáticas, programación e inferencia. Ha obtenido excelentes resultados en varias pruebas de referencia, alcanzando una precisión del 94.3% en MATH-500, demostrando una fuerte capacidad de razonamiento matemático."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-7B es un modelo obtenido mediante destilación de conocimiento basado en Qwen2.5-Math-7B. Este modelo se ajustó utilizando 800,000 muestras seleccionadas generadas por DeepSeek-R1, mostrando un rendimiento excepcional en múltiples campos como matemáticas, programación e inferencia. Ha obtenido excelentes resultados en varias pruebas de referencia, alcanzando una precisión del 92.8% en MATH-500, una tasa de aprobación del 55.5% en AIME 2024, y una puntuación de 1189 en CodeForces, demostrando una fuerte capacidad matemática y de programación como modelo de 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 combina las excelentes características de versiones anteriores, mejorando la capacidad general y de codificación."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 es un modelo de lenguaje de expertos mixtos (MoE) con 6710 millones de parámetros, que utiliza atención latente de múl<PERSON>les cabezas (MLA) y la arquitectura DeepSeekMoE, combinando una estrategia de balanceo de carga sin pérdidas auxiliares para optimizar la eficiencia de inferencia y entrenamiento. Al ser preentrenado en 14.8 billones de tokens de alta calidad y realizar ajustes supervisados y aprendizaje reforzado, DeepSeek-V3 supera en rendimiento a otros modelos de código abierto, acercándose a los modelos cerrados líderes."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B es un modelo avanzado entrenado para diálogos de alta complejidad."}, "deepseek-ai/deepseek-r1": {"description": "LLM eficiente de última generación, experto en razonamiento, matemáticas y programación."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 es un modelo de lenguaje visual de expertos mixtos (MoE) desarrollado sobre DeepSeekMoE-27B, que utiliza una arquitectura MoE de activación dispersa, logrando un rendimiento excepcional al activar solo 4.5B de parámetros. Este modelo destaca en múltiples tareas como preguntas visuales, reconocimiento óptico de caracteres, comprensión de documentos/tablas/gráficos y localización visual."}, "deepseek-chat": {"description": "Un nuevo modelo de código abierto que fusiona capacidades generales y de codificación, que no solo conserva la capacidad de diálogo general del modelo Chat original y la potente capacidad de procesamiento de código del modelo Coder, sino que también se alinea mejor con las preferencias humanas. Además, DeepSeek-V2.5 ha logrado mejoras significativas en tareas de escritura, seguimiento de instrucciones y más."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B es un modelo de lenguaje de código, entrenado con 20 billones de datos, de los cuales el 87% son código y el 13% son lenguajes en chino e inglés. El modelo introduce un tamaño de ventana de 16K y tareas de llenado de espacios, proporcionando funciones de autocompletado de código a nivel de proyecto y llenado de fragmentos."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 es un modelo de código de expertos híbrido de código abierto, que destaca en tareas de codificación, comparable a GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 es un modelo de código de expertos híbrido de código abierto, que destaca en tareas de codificación, comparable a GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 es un modelo de inferencia impulsado por aprendizaje reforzado (RL) que aborda los problemas de repetitividad y legibilidad en el modelo. Antes de RL, DeepSeek-R1 introdujo datos de arranque en frío, optimizando aún más el rendimiento de la inferencia. Su desempeño en tareas matemáticas, de código e inferencia es comparable al de OpenAI-o1, y ha mejorado su efectividad general a través de métodos de entrenamiento cuidadosamente diseñados."}, "deepseek-r1-0528": {"description": "Modelo completo de 685 mil millones de parámetros, lanzado el 28 de mayo de 2025. DeepSeek-R1 utiliza técnicas de aprendizaje reforzado a gran escala en la fase de postentrenamiento, mejorando significativamente la capacidad de razonamiento del modelo con muy pocos datos etiquetados. Presenta alto rendimiento y gran capacidad en tareas de matemáticas, código y razonamiento en lenguaje natural."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B versión rápida, que soporta búsqueda en línea en tiempo real, ofreciendo una velocidad de respuesta más rápida mientras mantiene el rendimiento del modelo."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B versión estándar, que soporta búsqueda en línea en tiempo real, adecuada para tareas de conversación y procesamiento de textos que requieren información actualizada."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama es un modelo basado en Llama destilado a partir de DeepSeek-R1."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1, el modelo más grande e inteligente del conjunto DeepSeek, ha sido destilado en la arquitectura Llama 70B. Basado en pruebas de referencia y evaluaciones humanas, este modelo es más inteligente que el Llama 70B original, destacándose especialmente en tareas que requieren precisión matemática y factual."}, "deepseek-r1-distill-llama-8b": {"description": "El modelo de la serie DeepSeek-R1-Distill se obtiene mediante la técnica de destilación de conocimiento, ajustando muestras generadas por DeepSeek-R1 a modelos de código abierto como Qwen y Llama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Lanzado por primera vez el 14 de febrero de 2025, destilado por el equipo de desarrollo del modelo Qianfan a partir del modelo base Llama3_70B (Construido con Meta Llama), con datos de destilación que también incluyen el corpus de Qianfan."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Lanzado por primera vez el 14 de febrero de 2025, destilado por el equipo de desarrollo del modelo Qianfan a partir del modelo base Llama3_8B (Construido con Meta Llama), con datos de destilación que también incluyen el corpus de Qianfan."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen es un modelo basado en Qwen destilado a partir de DeepSeek-R1."}, "deepseek-r1-distill-qwen-1.5b": {"description": "El modelo de la serie DeepSeek-R1-Distill se obtiene mediante la técnica de destilación de conocimiento, ajustando muestras generadas por DeepSeek-R1 a modelos de código abierto como Qwen y Llama."}, "deepseek-r1-distill-qwen-14b": {"description": "El modelo de la serie DeepSeek-R1-Distill se obtiene mediante la técnica de destilación de conocimiento, ajustando muestras generadas por DeepSeek-R1 a modelos de código abierto como Qwen y Llama."}, "deepseek-r1-distill-qwen-32b": {"description": "El modelo de la serie DeepSeek-R1-Distill se obtiene mediante la técnica de destilación de conocimiento, ajustando muestras generadas por DeepSeek-R1 a modelos de código abierto como Qwen y Llama."}, "deepseek-r1-distill-qwen-7b": {"description": "El modelo de la serie DeepSeek-R1-Distill se obtiene mediante la técnica de destilación de conocimiento, ajustando muestras generadas por DeepSeek-R1 a modelos de código abierto como Qwen y Llama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 versión rápida completa, que soporta búsqueda en línea en tiempo real, combinando la potente capacidad de 671B de parámetros con una velocidad de respuesta más rápida."}, "deepseek-r1-online": {"description": "DeepSeek R1 versión completa, con 671B de parámetros, que soporta búsqueda en línea en tiempo real, con una capacidad de comprensión y generación más potente."}, "deepseek-reasoner": {"description": "Modelo de inferencia lanzado por DeepSeek. Antes de proporcionar la respuesta final, el modelo genera primero una cadena de pensamiento para mejorar la precisión de la respuesta final."}, "deepseek-v2": {"description": "DeepSeek V2 es un modelo de lenguaje Mixture-of-Experts eficiente, adecuado para necesidades de procesamiento económico."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B es el modelo de código de diseño de DeepSeek, que ofrece una potente capacidad de generación de código."}, "deepseek-v3": {"description": "DeepSeek-V3 es un modelo MoE desarrollado por Hangzhou DeepSeek Artificial Intelligence Technology Research Co., Ltd., que ha destacado en múltiples evaluaciones, ocupando el primer lugar en la lista de modelos de código abierto. En comparación con el modelo V2.5, la velocidad de generación se ha incrementado tres veces, brindando a los usuarios una experiencia de uso más rápida y fluida."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 es un modelo MoE de 671B parámetros, destacándose en habilidades de programación y técnicas, comprensión del contexto y procesamiento de textos largos."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 es un modelo experto de mezcla de 685B parámetros, la última iteración de la serie de modelos de chat insignia del equipo de DeepSeek.\n\nHereda el modelo [DeepSeek V3](/deepseek/deepseek-chat-v3) y se desempeña excepcionalmente en diversas tareas."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 es un modelo experto de mezcla de 685B parámetros, la última iteración de la serie de modelos de chat insignia del equipo de DeepSeek.\n\nHereda el modelo [DeepSeek V3](/deepseek/deepseek-chat-v3) y se desempeña excepcionalmente en diversas tareas."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 mejora significativamente la capacidad de razonamiento del modelo con muy pocos datos etiquetados. Antes de proporcionar la respuesta final, el modelo genera una cadena de pensamiento para mejorar la precisión de la respuesta final."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 mejora enormemente la capacidad de razonamiento del modelo con muy pocos datos etiquetados. Antes de generar la respuesta final, el modelo produce una cadena de pensamiento para aumentar la precisión de la respuesta."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 mejora enormemente la capacidad de razonamiento del modelo con muy pocos datos etiquetados. Antes de generar la respuesta final, el modelo produce una cadena de pensamiento para aumentar la precisión de la respuesta."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B es un modelo de lenguaje de gran tamaño basado en Llama3.3 70B, que utiliza el ajuste fino de la salida de DeepSeek R1 para lograr un rendimiento competitivo comparable a los modelos de vanguardia de gran tamaño."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B es un modelo de lenguaje grande destilado basado en Llama-3.1-8B-Instruct, entrenado utilizando la salida de DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B es un modelo de lenguaje grande destilado basado en Qwen 2.5 14B, entrenado utilizando la salida de DeepSeek R1. Este modelo ha superado a o1-mini de OpenAI en múltiples pruebas de referencia, logrando resultados de vanguardia en modelos densos. A continuación se presentan algunos resultados de las pruebas de referencia:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCalificación de CodeForces: 1481\nEste modelo, ajustado a partir de la salida de DeepSeek R1, muestra un rendimiento competitivo comparable al de modelos de vanguardia de mayor escala."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B es un modelo de lenguaje grande destilado basado en Qwen 2.5 32B, entrenado utilizando la salida de DeepSeek R1. Este modelo ha superado a o1-mini de OpenAI en múltiples pruebas de referencia, logrando resultados de vanguardia en modelos densos. A continuación se presentan algunos resultados de las pruebas de referencia:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCalificación de CodeForces: 1691\nEste modelo, ajustado a partir de la salida de DeepSeek R1, muestra un rendimiento competitivo comparable al de modelos de vanguardia de mayor escala."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 es el último modelo de código abierto lanzado por el equipo de DeepSeek, que cuenta con un rendimiento de inferencia excepcional, especialmente en tareas de matemáticas, programación y razonamiento, alcanzando niveles comparables al modelo o1 de OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 mejora significativamente la capacidad de razonamiento del modelo con muy pocos datos etiquetados. Antes de proporcionar la respuesta final, el modelo genera una cadena de pensamiento para mejorar la precisión de la respuesta final."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 ha logrado un avance significativo en la velocidad de inferencia en comparación con modelos anteriores. Se clasifica como el número uno entre los modelos de código abierto y puede competir con los modelos cerrados más avanzados del mundo. DeepSeek-V3 utiliza la arquitectura de atención multi-cabeza (MLA) y DeepSeekMoE, que han sido completamente validadas en DeepSeek-V2. Además, DeepSeek-V3 ha introducido una estrategia auxiliar sin pérdidas para el balanceo de carga y ha establecido objetivos de entrenamiento de predicción de múltiples etiquetas para lograr un rendimiento más robusto."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 ha logrado un avance significativo en la velocidad de inferencia en comparación con modelos anteriores. Se clasifica como el número uno entre los modelos de código abierto y puede competir con los modelos cerrados más avanzados del mundo. DeepSeek-V3 utiliza la arquitectura de atención multi-cabeza (MLA) y DeepSeekMoE, que han sido completamente validadas en DeepSeek-V2. Además, DeepSeek-V3 ha introducido una estrategia auxiliar sin pérdidas para el balanceo de carga y ha establecido objetivos de entrenamiento de predicción de múltiples etiquetas para lograr un rendimiento más robusto."}, "deepseek_r1": {"description": "DeepSeek-R1 es un modelo de inferencia impulsado por aprendizaje por refuerzo (RL), que resuelve problemas de repetitividad y legibilidad en el modelo. Antes de RL, DeepSeek-R1 introdujo datos de arranque en frío, optimizando aún más el rendimiento de inferencia. Su rendimiento en tareas de matemáticas, código y razonamiento es comparable al de OpenAI-o1, y mediante un método de entrenamiento cuidadosamente diseñado, se ha mejorado el efecto general."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B es un modelo obtenido a partir de Llama-3.3-70B-Instruct mediante entrenamiento de destilación. Este modelo es parte de la serie DeepSeek-R1, mostrando un rendimiento sobresaliente en matemáticas, programación y razonamiento mediante el ajuste con muestras generadas por DeepSeek-R1."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B es un modelo obtenido a partir de Qwen2.5-14B mediante destilación de conocimiento. Este modelo se ajustó utilizando 800,000 muestras seleccionadas generadas por DeepSeek-R1, mostrando una excelente capacidad de inferencia."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B es un modelo obtenido a partir de Qwen2.5-32B mediante destilación de conocimiento. Este modelo se ajustó utilizando 800,000 muestras seleccionadas generadas por DeepSeek-R1, mostrando un rendimiento excepcional en múltiples campos como matemáticas, programación y razonamiento."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite es un modelo ligero de nueva generación, con una velocidad de respuesta extrema, alcanzando niveles de rendimiento y latencia de clase mundial."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k es una versión mejorada de Doubao-1.5-Pro, con un aumento del 10% en el rendimiento general. Soporta razonamiento con una ventana de contexto de 256k y una longitud de salida de hasta 12k tokens. Mayor rendimiento, ventana más grande y una excelente relación calidad-precio, adecuado para una amplia gama de escenarios de aplicación."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro es un modelo de nueva generación, con un rendimiento completamente mejorado, destacando en conocimientos, código, razonamiento, entre otros."}, "doubao-1.5-thinking-pro": {"description": "El modelo de pensamiento profundo Doubao-1.5, completamente nuevo, destaca en campos especializados como matemáticas, programación y razonamiento científico, así como en tareas generales como la escritura creativa, alcanzando o acercándose al nivel de élite de la industria en múltiples estándares de referencia, como AIME 2024, Codeforces y GPQA. Soporta una ventana de contexto de 128k y una salida de 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 es un nuevo modelo de pensamiento profundo (la versión m incluye capacidades nativas de inferencia multimodal profunda), que destaca en matemáticas, programación, razonamiento científico y tareas generales como escritura creativa. Alcanza o se acerca al nivel de élite en benchmarks reconocidos como AIME 2024, Codeforces y GPQA. Soporta ventana de contexto de 128k y salida de 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Nuevo modelo de pensamiento profundo visual con capacidades avanzadas de comprensión e inferencia multimodal general, logrando resultados SOTA en 37 de 59 benchmarks públicos."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS es un modelo agente nativo orientado a la interacción con interfaces gráficas (GUI). Mediante capacidades humanas de percepción, razonamiento y acción, interactúa de forma fluida con la GUI."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite es un modelo multimodal de gran escala actualizado, que soporta el reconocimiento de imágenes de cualquier resolución y proporciones extremas, mejorando la capacidad de razonamiento visual, reconocimiento de documentos, comprensión de información detallada y seguimiento de instrucciones. Soporta una ventana de contexto de 128k, con una longitud de salida que admite hasta 16k tokens."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro es un modelo multimodal avanzado que soporta reconocimiento de imágenes con cualquier resolución y proporciones extremas, mejorando el razonamiento visual, reconocimiento de documentos, comprensión de detalles y seguimiento de instrucciones."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro es un modelo multimodal avanzado que soporta reconocimiento de imágenes con cualquier resolución y proporciones extremas, mejorando el razonamiento visual, reconocimiento de documentos, comprensión de detalles y seguimiento de instrucciones."}, "doubao-lite-128k": {"description": "Ofrece una velocidad de respuesta excepcional y una mejor relación calidad-precio, proporcionando opciones más flexibles para diferentes escenarios de los clientes. Soporta inferencia y ajuste fino con una ventana de contexto de 128k."}, "doubao-lite-32k": {"description": "Ofrece una velocidad de respuesta excepcional y una mejor relación calidad-precio, proporcionando opciones más flexibles para diferentes escenarios de los clientes. Soporta inferencia y ajuste fino con una ventana de contexto de 32k."}, "doubao-lite-4k": {"description": "Ofrece una velocidad de respuesta excepcional y una mejor relación calidad-precio, proporcionando opciones más flexibles para diferentes escenarios de los clientes. Soporta inferencia y ajuste fino con una ventana de contexto de 4k."}, "doubao-pro-256k": {"description": "El modelo principal con mejor rendimiento, adecuado para tareas complejas, con excelentes resultados en preguntas de referencia, resúmenes, creación, clasificación de texto, juegos de rol y otros escenarios. Soporta inferencia y ajuste fino con una ventana de contexto de 256k."}, "doubao-pro-32k": {"description": "El modelo principal con mejor rendimiento, adecuado para tareas complejas, con excelentes resultados en preguntas de referencia, resúmenes, creación, clasificación de texto, juegos de rol y otros escenarios. Soporta inferencia y ajuste fino con una ventana de contexto de 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 es un nuevo modelo multimodal de pensamiento profundo que soporta tres modos de pensamiento: automático, reflexivo y no reflexivo. En modo no reflexivo, el rendimiento del modelo mejora significativamente en comparación con Doubao-1.5-pro/250115. Soporta una ventana de contexto de 256k y una longitud máxima de salida de 16k tokens."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash es un modelo multimodal de pensamiento profundo con velocidad de inferencia extrema, TPOT de solo 10 ms; soporta comprensión tanto textual como visual, con capacidad de comprensión textual superior a la generación lite anterior y comprensión visual comparable a los modelos pro de la competencia. Soporta una ventana de contexto de 256k y una longitud máxima de salida de 16k tokens."}, "doubao-seed-1.6-thinking": {"description": "El modelo Doubao-Seed-1.6-thinking tiene una capacidad de pensamiento significativamente mejorada. En comparación con Doubao-1.5-thinking-pro, mejora aún más en habilidades básicas como programación, matemáticas y razonamiento lógico, y soporta comprensión visual. Soporta una ventana de contexto de 256k y una longitud máxima de salida de 16k tokens."}, "doubao-seedream-3-0-t2i-250415": {"description": "El modelo de generación de imágenes Doubao fue desarrollado por el equipo Seed de ByteDance, soporta entrada de texto e imagen, y ofrece una experiencia de generación de imágenes altamente controlable y de alta calidad. Genera imágenes basadas en indicaciones textuales."}, "doubao-vision-lite-32k": {"description": "El modelo Doubao-vision es un modelo multimodal desarrollado por Doubao, con potentes capacidades de comprensión e inferencia de imágenes, así como una precisa comprensión de instrucciones. El modelo muestra un rendimiento destacado en extracción de información texto-imagen y tareas de inferencia basadas en imágenes, aplicable a tareas de preguntas visuales más complejas y amplias."}, "doubao-vision-pro-32k": {"description": "El modelo Doubao-vision es un modelo multimodal desarrollado por Doubao, con potentes capacidades de comprensión e inferencia de imágenes, así como una precisa comprensión de instrucciones. El modelo muestra un rendimiento destacado en extracción de información texto-imagen y tareas de inferencia basadas en imágenes, aplicable a tareas de preguntas visuales más complejas y amplias."}, "emohaa": {"description": "Emohaa es un modelo psicológico con capacidades de consulta profesional, ayudando a los usuarios a comprender problemas emocionales."}, "ernie-3.5-128k": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, cubre una vasta cantidad de corpus en chino e inglés, con potentes capacidades generales que satisfacen la mayoría de los requisitos de preguntas y respuestas en diálogos, generación creativa y aplicaciones de plugins; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas."}, "ernie-3.5-8k": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, cubre una vasta cantidad de corpus en chino e inglés, con potentes capacidades generales que satisfacen la mayoría de los requisitos de preguntas y respuestas en diálogos, generación creativa y aplicaciones de plugins; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas."}, "ernie-3.5-8k-preview": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, cubre una vasta cantidad de corpus en chino e inglés, con potentes capacidades generales que satisfacen la mayoría de los requisitos de preguntas y respuestas en diálogos, generación creativa y aplicaciones de plugins; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas."}, "ernie-4.0-8k-latest": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, de ultra gran escala, ha logrado una actualización completa de capacidades en comparación con ERNIE 3.5, siendo ampliamente aplicable en escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas."}, "ernie-4.0-8k-preview": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, de ultra gran escala, ha logrado una actualización completa de capacidades en comparación con ERNIE 3.5, siendo ampliamente aplicable en escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas."}, "ernie-4.0-turbo-128k": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, de ultra gran escala, muestra un rendimiento excepcional en general, siendo ampliamente aplicable en escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas. En comparación con ERNIE 4.0, presenta un rendimiento superior."}, "ernie-4.0-turbo-8k-latest": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, de ultra gran escala, muestra un rendimiento excepcional en general, siendo ampliamente aplicable en escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas. En comparación con ERNIE 4.0, presenta un rendimiento superior."}, "ernie-4.0-turbo-8k-preview": {"description": "El modelo de lenguaje grande de bandera de Baidu, desarrollado internamente, de ultra gran escala, muestra un rendimiento excepcional en general, siendo ampliamente aplicable en escenarios de tareas complejas en diversos campos; soporta la integración automática con el plugin de búsqueda de Baidu, garantizando la actualidad de la información de preguntas y respuestas. En comparación con ERNIE 4.0, presenta un rendimiento superior."}, "ernie-4.5-8k-preview": {"description": "El modelo grande Wenxin 4.5 es un nuevo modelo base multimodal nativo desarrollado por Baidu, que logra una optimización colaborativa a través de modelado conjunto de múltiples modalidades, con excelentes capacidades de comprensión multimodal; presenta una capacidad lingüística más avanzada, con mejoras en comprensión, generación, lógica y memoria, así como una notable reducción de alucinaciones y mejoras en razonamiento lógico y capacidades de codificación."}, "ernie-4.5-turbo-128k": {"description": "ERNIE 4.5 Turbo presenta mejoras significativas en la eliminación de ilusiones, razonamiento lógico y capacidades de codificación. En comparación con ERNIE 4.5, es más rápido y más económico. Las capacidades del modelo se han mejorado de manera integral, satisfaciendo mejor el procesamiento de diálogos de larga historia y tareas de comprensión de documentos extensos."}, "ernie-4.5-turbo-32k": {"description": "ERNIE 4.5 Turbo también ha mejorado significativamente en la eliminación de ilusiones, razonamiento lógico y capacidades de codificación. En comparación con ERNIE 4.5, es más rápido y más económico. Las capacidades en creación de texto y preguntas y respuestas han mejorado notablemente. La longitud de salida y la latencia de oraciones completas han aumentado en comparación con ERNIE 4.5."}, "ernie-4.5-turbo-vl-32k": {"description": "Nueva versión del modelo de gran escala de ERNIE, con mejoras significativas en comprensión de imágenes, creación, traducción y codificación, que ahora soporta una longitud de contexto de 32K, con una latencia de primer token notablemente reducida."}, "ernie-char-8k": {"description": "Modelo de lenguaje grande de escenario vertical desarrollado internamente por Baidu, adecuado para aplicaciones como NPC de juegos, diálogos de servicio al cliente y juegos de rol de diálogos, con un estilo de personaje más distintivo y consistente, y una mayor capacidad de seguimiento de instrucciones y rendimiento de inferencia."}, "ernie-char-fiction-8k": {"description": "Modelo de lenguaje grande de escenario vertical desarrollado internamente por Baidu, adecuado para aplicaciones como NPC de juegos, diálogos de servicio al cliente y juegos de rol de diálogos, con un estilo de personaje más distintivo y consistente, y una mayor capacidad de seguimiento de instrucciones y rendimiento de inferencia."}, "ernie-irag-edit": {"description": "El modelo de edición de imágenes ERNIE iRAG desarrollado por Baidu soporta operaciones como borrar objetos, repintar objetos y generar variaciones basadas en imágenes."}, "ernie-lite-8k": {"description": "ERNIE Lite es un modelo de lenguaje grande ligero desarrollado internamente por Baidu, que combina un excelente rendimiento del modelo con una buena capacidad de inferencia, adecuado para su uso en tarjetas de aceleración de IA de bajo consumo."}, "ernie-lite-pro-128k": {"description": "Modelo de lenguaje grande ligero desarrollado internamente por Baidu, que combina un excelente rendimiento del modelo con una buena capacidad de inferencia, con un rendimiento superior al de ERNIE Lite, adecuado para su uso en tarjetas de aceleración de IA de bajo consumo."}, "ernie-novel-8k": {"description": "Modelo de lenguaje grande general desarrollado internamente por <PERSON>, con ventajas notables en la capacidad de continuar novelas, también aplicable en escenarios de cortometrajes y películas."}, "ernie-speed-128k": {"description": "El modelo de lenguaje grande de alto rendimiento desarrollado internamente por Baidu, lanzado en 2024, tiene capacidades generales excepcionales, adecuado como modelo base para ajustes finos, manejando mejor problemas específicos de escenarios, y con un excelente rendimiento de inferencia."}, "ernie-speed-pro-128k": {"description": "El modelo de lenguaje grande de alto rendimiento desarrollado internamente por Baidu, lanzado en 2024, tiene capacidades generales excepcionales, con un rendimiento superior al de ERNIE Speed, adecuado como modelo base para ajustes finos, manejando mejor problemas específicos de escenarios, y con un excelente rendimiento de inferencia."}, "ernie-tiny-8k": {"description": "ERNIE Tiny es un modelo de lenguaje grande de alto rendimiento desarrollado internamente por Baidu, con los costos de implementación y ajuste más bajos entre los modelos de la serie Wenxin."}, "ernie-x1-32k": {"description": "Posee una comprensión, planificación, reflexión y capacidad de evolución más fuertes. Como un modelo de pensamiento profundo más completo, ERNIE-X1 combina precisión, creatividad y elocuencia, destacándose especialmente en preguntas y respuestas en chino, creación literaria, redacción de documentos, diálogos cotidianos, razonamiento lógico, cálculos complejos y llamadas a herramientas."}, "ernie-x1-32k-preview": {"description": "El modelo grande Wenxin X1 posee una mayor capacidad de comprensión, planificación, reflexión y evolución. Como un modelo de pensamiento profundo más completo, Wenxin X1 combina precisión, creatividad y elocuencia, destacándose especialmente en preguntas y respuestas de conocimiento en chino, creación literaria, redacción de documentos, diálogos cotidianos, razonamiento lógico, cálculos complejos y llamadas a herramientas."}, "ernie-x1-turbo-32k": {"description": "Mejora en comparación con ERNIE-X1-32K, con mejores resultados y rendimiento."}, "flux-1-schnell": {"description": "Modelo de generación de imágenes a partir de texto con 12 mil millones de parámetros desarrollado por Black Forest Labs, que utiliza tecnología de destilación de difusión adversarial latente, capaz de generar imágenes de alta calidad en 1 a 4 pasos. Su rendimiento es comparable a alternativas propietarias y se publica bajo licencia Apache-2.0, apto para uso personal, investigación y comercial."}, "flux-dev": {"description": "FLUX.1 [dev] es un modelo refinado y de pesos abiertos para aplicaciones no comerciales. Mantiene una calidad de imagen y capacidad de seguimiento de instrucciones similar a la versión profesional de FLUX, pero con mayor eficiencia operativa. En comparación con modelos estándar de tamaño similar, es más eficiente en el uso de recursos."}, "flux-kontext/dev": {"description": "Modelo de edición de imágenes Frontier."}, "flux-merged": {"description": "El modelo FLUX.1-merged combina las características profundas exploradas durante la fase de desarrollo de “DEV” con las ventajas de ejecución rápida representadas por “Schnell”. Esta combinación no solo amplía los límites de rendimiento del modelo, sino que también amplía su rango de aplicaciones."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] puede procesar texto e imágenes de referencia como entrada, logrando sin problemas ediciones locales específicas y transformaciones complejas de escenas completas."}, "flux-schnell": {"description": "FLUX.1 [schnell], como el modelo de pocos pasos más avanzado de código abierto actualmente, supera no solo a competidores similares sino también a potentes modelos no refinados como Midjourney v6.0 y DALL·E 3 (HD). Este modelo ha sido ajustado específicamente para conservar toda la diversidad de salida de la etapa de preentrenamiento. En comparación con los modelos más avanzados del mercado, FLUX.1 [schnell] mejora significativamente la calidad visual, el cumplimiento de instrucciones, la variación de tamaño/proporción, el manejo de fuentes y la diversidad de salida, ofreciendo a los usuarios una experiencia de generación de imágenes creativas más rica y variada."}, "flux.1-schnell": {"description": "Transformador de flujo rectificado con 12 mil millones de parámetros, capaz de generar imágenes basadas en descripciones textuales."}, "flux/schnell": {"description": "FLUX.1 [schnell] es un modelo transformador de flujo con 12 mil millones de parámetros, capaz de generar imágenes de alta calidad a partir de texto en 1 a 4 pasos, adecuado para uso personal y comercial."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Ajuste) ofrece un rendimiento estable y ajustable, siendo una opción ideal para soluciones de tareas complejas."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Ajuste) proporciona un excelente soporte multimodal, centrado en la resolución efectiva de tareas complejas."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro es el modelo de IA de alto rendimiento de Google, diseñado para la escalabilidad en una amplia gama de tareas."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 es un modelo multimodal eficiente, que admite la escalabilidad para aplicaciones amplias."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 es un modelo multimodal eficiente, que admite una amplia gama de aplicaciones."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B es un modelo multimodal eficiente que admite una amplia gama de aplicaciones."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 es el último modelo experimental, con mejoras significativas en el rendimiento tanto en casos de uso de texto como multimodal."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B es un modelo multimodal eficiente que admite una amplia gama de aplicaciones escalables."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 ofrece capacidades de procesamiento multimodal optimizadas, adecuadas para diversas tareas complejas."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash es el último modelo de IA multimodal de Google, con capacidades de procesamiento rápido, que admite entradas de texto, imagen y video, adecuado para la escalabilidad eficiente en diversas tareas."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 es una solución de IA multimodal escalable, que admite una amplia gama de tareas complejas."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 es el último modelo listo para producción, que ofrece una calidad de salida superior, especialmente en tareas matemáticas, contextos largos y tareas visuales."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 ofrece excelentes capacidades de procesamiento multimodal, brindando mayor flexibilidad para el desarrollo de aplicaciones."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 combina las últimas tecnologías optimizadas para brindar capacidades de procesamiento de datos multimodales más eficientes."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro admite hasta 2 millones de tokens, siendo una opción ideal para modelos multimodales de tamaño medio, adecuados para un soporte multifacético en tareas complejas."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash ofrece funciones y mejoras de próxima generación, incluyendo velocidad excepcional, uso de herramientas nativas, generación multimodal y una ventana de contexto de 1M tokens."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash ofrece funciones y mejoras de próxima generación, incluyendo velocidad excepcional, uso de herramientas nativas, generación multimodal y una ventana de contexto de 1M tokens."}, "gemini-2.0-flash-exp": {"description": "Variante del modelo Gemini 2.0 Flash, optimizada para objetivos como la rentabilidad y la baja latencia."}, "gemini-2.0-flash-exp-image-generation": {"description": "Modelo experimental Gemini 2.0 Flash, que admite la generación de imágenes"}, "gemini-2.0-flash-lite": {"description": "Variante del modelo Gemini 2.0 Flash, optimizada para objetivos como la rentabilidad y la baja latencia."}, "gemini-2.0-flash-lite-001": {"description": "Variante del modelo Gemini 2.0 Flash, optimizada para objetivos como la rentabilidad y la baja latencia."}, "gemini-2.0-flash-preview-image-generation": {"description": "Modelo de vista previa Gemini 2.0 Flash, que admite la generación de imágenes"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash es el modelo de mejor relación calidad-precio de Google, que ofrece funcionalidades completas."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite es el modelo más pequeño y rentable de Google, diseñado para un uso a gran escala."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview es el modelo más pequeño y con mejor relación calidad-precio de Google, diseñado para un uso a gran escala."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview es el modelo más rentable de Google, que ofrece una funcionalidad completa."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview es el modelo de mejor relación calidad-precio de Google, que ofrece funcionalidades completas."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro es el modelo de pensamiento más avanzado de Google, capaz de razonar sobre problemas complejos en código, matemáticas y áreas STEM, así como de analizar grandes conjuntos de datos, bases de código y documentos utilizando contextos largos."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview es el modelo de pensamiento más avanzado de Google, capaz de razonar sobre problemas complejos en código, matemáticas y campos STEM, así como de analizar grandes conjuntos de datos, bibliotecas de código y documentos utilizando un contexto largo."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview es el modelo de pensamiento más avanzado de Google, capaz de razonar sobre problemas complejos en código, matemáticas y campos STEM, así como de analizar grandes conjuntos de datos, bibliotecas de código y documentos utilizando un análisis de contexto prolongado."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview es el modelo de pensamiento más avanzado de Google, capaz de razonar sobre problemas complejos en código, matemáticas y áreas STEM, así como analizar grandes conjuntos de datos, bases de código y documentos utilizando contextos extensos."}, "gemma-7b-it": {"description": "Gemma 7B es adecuado para el procesamiento de tareas de pequeña y mediana escala, combinando rentabilidad."}, "gemma2": {"description": "Gemma 2 es un modelo eficiente lanzado por Google, que abarca una variedad de escenarios de aplicación desde aplicaciones pequeñas hasta procesamiento de datos complejos."}, "gemma2-9b-it": {"description": "Gemma 2 9B es un modelo optimizado para la integración de tareas y herramientas específicas."}, "gemma2:27b": {"description": "Gemma 2 es un modelo eficiente lanzado por Google, que abarca una variedad de escenarios de aplicación desde aplicaciones pequeñas hasta procesamiento de datos complejos."}, "gemma2:2b": {"description": "Gemma 2 es un modelo eficiente lanzado por Google, que abarca una variedad de escenarios de aplicación desde aplicaciones pequeñas hasta procesamiento de datos complejos."}, "generalv3": {"description": "Spark Pro es un modelo de lenguaje grande de alto rendimiento optimizado para campos profesionales, enfocado en matemáticas, programación, medicina, educación y más, y soporta búsqueda en línea y plugins integrados como clima y fecha. Su modelo optimizado muestra un rendimiento excepcional y eficiente en preguntas y respuestas complejas, comprensión del lenguaje y creación de textos de alto nivel, siendo la opción ideal para escenarios de aplicación profesional."}, "generalv3.5": {"description": "Spark3.5 Max es la versión más completa, soportando búsque<PERSON> en línea y numerosos plugins integrados. Su capacidad central completamente optimizada, así como la configuración de roles del sistema y la función de llamada a funciones, hacen que su rendimiento en diversos escenarios de aplicación complejos sea excepcional y sobresaliente."}, "glm-4": {"description": "GLM-4 es la versión anterior lanzada en enero de 2024, actualmente ha sido reemplazada por el más potente GLM-4-0520."}, "glm-4-0520": {"description": "GLM-4-0520 es la última versión del modelo, diseñada para tareas altamente complejas y diversas, con un rendimiento excepcional."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat muestra un alto rendimiento en semántica, matemáticas, razonamiento, código y conocimiento. También cuenta con navegación web, ejecución de código, llamadas a herramientas personalizadas y razonamiento de textos largos. Soporta 26 idiomas, incluidos japonés, coreano y alemán."}, "glm-4-air": {"description": "GLM-4-Air es una versión de alto costo-beneficio, con un rendimiento cercano al GLM-4, ofreciendo velocidad y precios asequibles."}, "glm-4-air-250414": {"description": "GLM-4-Air es una versión de buena relación calidad-precio, con un rendimiento cercano al de GLM-4, ofreciendo velocidad rápida y un precio asequible."}, "glm-4-airx": {"description": "GLM-4-AirX ofrece una versión eficiente de GLM-4-Air, con velocidades de inferencia de hasta 2.6 veces."}, "glm-4-alltools": {"description": "GLM-4-AllTools es un modelo de agente multifuncional, optimizado para soportar planificación de instrucciones complejas y llamadas a herramientas, como navegación web, interpretación de código y generación de texto, adecuado para la ejecución de múltiples tareas."}, "glm-4-flash": {"description": "GLM-4-Flash es la opción ideal para tareas simples, con la velocidad más rápida y el precio más bajo."}, "glm-4-flash-250414": {"description": "GLM-4-Flash es la opción ideal para tareas simples, siendo la más rápida y gratuita."}, "glm-4-flashx": {"description": "GLM-4-FlashX es una versión mejorada de Flash, con una velocidad de inferencia ultrarrápida."}, "glm-4-long": {"description": "GLM-4-Long admite entradas de texto extremadamente largas, adecuado para tareas de memoria y procesamiento de documentos a gran escala."}, "glm-4-plus": {"description": "GLM-4-Plus, como buque insignia de alta inteligencia, tiene una poderosa capacidad para manejar textos largos y tareas complejas, con un rendimiento mejorado en general."}, "glm-4.1v-thinking-flash": {"description": "La serie GLM-4.1V-Thinking es el modelo visual más potente conocido en la categoría de VLMs de 10 mil millones de parámetros, integrando tareas de lenguaje visual de última generación (SOTA) en su nivel, incluyendo comprensión de video, preguntas sobre imágenes, resolución de problemas académicos, reconocimiento OCR, interpretación de documentos y gráficos, agentes GUI, codificación web frontend, grounding, entre otros. En muchas tareas, supera incluso a modelos con 8 veces más parámetros como Qwen2.5-VL-72B. Gracias a técnicas avanzadas de aprendizaje reforzado, el modelo domina el razonamiento mediante cadenas de pensamiento para mejorar la precisión y riqueza de las respuestas, superando significativamente a los modelos tradicionales sin pensamiento en términos de resultados y explicabilidad."}, "glm-4.1v-thinking-flashx": {"description": "La serie GLM-4.1V-Thinking es el modelo visual más potente conocido en la categoría de VLMs de 10 mil millones de parámetros, integrando tareas de lenguaje visual de última generación (SOTA) en su nivel, incluyendo comprensión de video, preguntas sobre imágenes, resolución de problemas académicos, reconocimiento OCR, interpretación de documentos y gráficos, agentes GUI, codificación web frontend, grounding, entre otros. En muchas tareas, supera incluso a modelos con 8 veces más parámetros como Qwen2.5-VL-72B. Gracias a técnicas avanzadas de aprendizaje reforzado, el modelo domina el razonamiento mediante cadenas de pensamiento para mejorar la precisión y riqueza de las respuestas, superando significativamente a los modelos tradicionales sin pensamiento en términos de resultados y explicabilidad."}, "glm-4.5": {"description": "El último modelo insignia de Zhipu, soporta modo de pensamiento, con capacidades integrales que alcanzan el nivel SOTA de modelos de código abierto y una longitud de contexto de hasta 128K."}, "glm-4.5-air": {"description": "Versión ligera de GLM-4.5 que equilibra rendimiento y costo, con capacidad flexible para cambiar entre modelos de pensamiento híbrido."}, "glm-4.5-airx": {"description": "Versión ultra rápida de GLM-4.5-Air, con respuesta más rápida, diseñada para demandas de gran escala y alta velocidad."}, "glm-4.5-flash": {"description": "Versión gratuita de GLM-4.5, con un desempeño destacado en tareas de inferencia, codificación y agentes inteligentes."}, "glm-4.5-x": {"description": "Versión ultra rápida de GLM-4.5, que combina un rendimiento potente con una velocidad de generación de hasta 100 tokens por segundo."}, "glm-4v": {"description": "GLM-4V proporciona una poderosa capacidad de comprensión e inferencia de imágenes, soportando diversas tareas visuales."}, "glm-4v-flash": {"description": "GLM-4V-Flash se centra en la comprensión eficiente de una única imagen, adecuada para escenarios de análisis de imágenes rápidos, como análisis de imágenes en tiempo real o procesamiento por lotes de imágenes."}, "glm-4v-plus": {"description": "GLM-4V-Plus tiene la capacidad de entender contenido de video y múltiples imágenes, adecuado para tareas multimodales."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus tiene la capacidad de comprender contenido de video y múltiples imágenes, adecuado para tareas multimodales."}, "glm-z1-air": {"description": "Modelo de inferencia: posee una poderosa capacidad de inferencia, adecuado para tareas que requieren razonamiento profundo."}, "glm-z1-airx": {"description": "Inferencia ultrarrápida: con una velocidad de inferencia extremadamente rápida y un potente efecto de razonamiento."}, "glm-z1-flash": {"description": "La serie GLM-Z1 posee una fuerte capacidad de razonamiento complejo, destacando en lógica, matemáticas y programación."}, "glm-z1-flashx": {"description": "Alta velocidad y bajo costo: versión mejorada Flash, con velocidad de inferencia ultrarrápida y mejor garantía de concurrencia."}, "glm-zero-preview": {"description": "GLM-Zero-Preview posee una poderosa capacidad de razonamiento complejo, destacándose en áreas como razonamiento lógico, matemáticas y programación."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash ofrece funciones y mejoras de próxima generación, incluyendo velocidad excepcional, uso de herramientas nativas, generación multimodal y una ventana de contexto de 1M tokens."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental es el último modelo de IA multimodal experimental de Google, con una mejora de calidad en comparación con versiones anteriores, especialmente en conocimiento del mundo, código y contexto largo."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash es el modelo principal más avanzado de Google, diseñado para tareas avanzadas de razonamiento, codificación, matemáticas y ciencias. Incluye una capacidad incorporada de \"pensamiento\" que le permite proporcionar respuestas con mayor precisión y un manejo detallado del contexto.\n\nNota: este modelo tiene dos variantes: con pensamiento y sin pensamiento. La tarificación de salida varía significativamente según si la capacidad de pensamiento está activada. Si elige la variante estándar (sin el sufijo \":thinking\"), el modelo evitará explícitamente generar tokens de pensamiento.\n\nPara aprovechar la capacidad de pensamiento y recibir tokens de pensamiento, debe seleccionar la variante \":thinking\", lo que generará una tarificación más alta para la salida de pensamiento.\n\nAdemás, Gemini 2.5 Flash se puede configurar mediante el parámetro \"máximo de tokens para razonamiento\", como se describe en la documentación (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash es el modelo principal más avanzado de Google, diseñado para razonamiento avanzado, codificación, matemáticas y tareas científicas. Incluye la capacidad de 'pensar' incorporada, lo que le permite proporcionar respuestas con mayor precisión y un manejo más detallado del contexto.\n\nNota: Este modelo tiene dos variantes: con pensamiento y sin pensamiento. La fijación de precios de salida varía significativamente según si la capacidad de pensamiento está activada. Si elige la variante estándar (sin el sufijo ':thinking'), el modelo evitará explícitamente generar tokens de pensamiento.\n\nPara aprovechar la capacidad de pensamiento y recibir tokens de pensamiento, debe elegir la variante ':thinking', lo que resultará en un precio de salida de pensamiento más alto.\n\nAdemás, Gemini 2.5 Flash se puede configurar a través del parámetro 'número máximo de tokens de razonamiento', como se describe en la documentación (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash es el modelo principal más avanzado de Google, diseñado para razonamiento avanzado, codificación, matemáticas y tareas científicas. Incluye la capacidad de 'pensar' incorporada, lo que le permite proporcionar respuestas con mayor precisión y un manejo más detallado del contexto.\n\nNota: Este modelo tiene dos variantes: con pensamiento y sin pensamiento. La fijación de precios de salida varía significativamente según si la capacidad de pensamiento está activada. Si elige la variante estándar (sin el sufijo ':thinking'), el modelo evitará explícitamente generar tokens de pensamiento.\n\nPara aprovechar la capacidad de pensamiento y recibir tokens de pensamiento, debe elegir la variante ':thinking', lo que resultará en un precio de salida de pensamiento más alto.\n\nAdemás, Gemini 2.5 Flash se puede configurar a través del parámetro 'número máximo de tokens de razonamiento', como se describe en la documentación (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro es el modelo de pensamiento más avanzado de Google, capaz de razonar sobre problemas complejos en código, matemáticas y áreas STEM, así como de analizar grandes conjuntos de datos, bases de código y documentos utilizando contextos extensos."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview es el modelo de pensamiento más avanzado de Google, capaz de razonar sobre problemas complejos en código, matemáticas y áreas STEM, así como de analizar grandes conjuntos de datos, bases de código y documentos utilizando contextos extensos."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash ofrece capacidades de procesamiento multimodal optimizadas, adecuadas para una variedad de escenarios de tareas complejas."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro combina las últimas tecnologías de optimización, proporcionando una capacidad de procesamiento de datos multimodal más eficiente."}, "google/gemma-2-27b": {"description": "Gemma 2 es un modelo eficiente lanzado por Google, que abarca una variedad de escenarios de aplicación desde aplicaciones pequeñas hasta procesamiento de datos complejos."}, "google/gemma-2-27b-it": {"description": "Gemma 2 continúa con el concepto de diseño ligero y eficiente."}, "google/gemma-2-2b-it": {"description": "Modelo de ajuste de instrucciones ligero de Google."}, "google/gemma-2-9b": {"description": "Gemma 2 es un modelo eficiente lanzado por Google, que abarca una variedad de escenarios de aplicación desde aplicaciones pequeñas hasta procesamiento de datos complejos."}, "google/gemma-2-9b-it": {"description": "Gemma 2 es una serie de modelos de texto de código abierto y ligeros de Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 es una serie de modelos de texto de código abierto y livianos de Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) ofrece capacidades básicas de procesamiento de instrucciones, adecuado para aplicaciones ligeras."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B es un modelo de lenguaje de código abierto de Google que establece nuevos estándares en eficiencia y rendimiento."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B es un modelo de lenguaje de código abierto de Google, que establece nuevos estándares en eficiencia y rendimiento."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo, adecuado para diversas tareas de generación y comprensión de texto, actualmente apunta a gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo, adecuado para diversas tareas de generación y comprensión de texto, actualmente apunta a gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo, adecuado para diversas tareas de generación y comprensión de texto, actualmente apunta a gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo, adecuado para diversas tareas de generación y comprensión de texto, actualmente apunta a gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo, un modelo eficiente proporcionado por OpenAI, es adecuado para tareas de conversación y generación de texto, con soporte para llamadas a funciones en paralelo."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k, un modelo de generación de texto de alta capacidad, adecuado para tareas complejas."}, "gpt-4": {"description": "GPT-4 ofrece una ventana de contexto más grande, capaz de manejar entradas de texto más largas, adecuado para escenarios que requieren integración de información amplia y análisis de datos."}, "gpt-4-0125-preview": {"description": "El último modelo GPT-4 Turbo cuenta con funciones visuales. Ahora, las solicitudes visuales pueden utilizar el modo JSON y llamadas a funciones. GPT-4 Turbo es una versión mejorada que ofrece soporte rentable para tareas multimodales. Encuentra un equilibrio entre precisión y eficiencia, adecuado para aplicaciones que requieren interacción en tiempo real."}, "gpt-4-0613": {"description": "GPT-4 ofrece una ventana de contexto más grande, capaz de manejar entradas de texto más largas, adecuado para escenarios que requieren integración de información amplia y análisis de datos."}, "gpt-4-1106-preview": {"description": "El último modelo GPT-4 Turbo cuenta con funciones visuales. Ahora, las solicitudes visuales pueden utilizar el modo JSON y llamadas a funciones. GPT-4 Turbo es una versión mejorada que ofrece soporte rentable para tareas multimodales. Encuentra un equilibrio entre precisión y eficiencia, adecuado para aplicaciones que requieren interacción en tiempo real."}, "gpt-4-32k": {"description": "GPT-4 ofrece una ventana de contexto más grande, capaz de manejar entradas de texto más largas, adecuado para escenarios que requieren integración de información amplia y análisis de datos."}, "gpt-4-32k-0613": {"description": "GPT-4 ofrece una ventana de contexto más grande, capaz de manejar entradas de texto más largas, adecuado para escenarios que requieren integración de información amplia y análisis de datos."}, "gpt-4-turbo": {"description": "El último modelo GPT-4 Turbo cuenta con funciones visuales. Ahora, las solicitudes visuales pueden utilizar el modo JSON y llamadas a funciones. GPT-4 Turbo es una versión mejorada que ofrece soporte rentable para tareas multimodales. Encuentra un equilibrio entre precisión y eficiencia, adecuado para aplicaciones que requieren interacción en tiempo real."}, "gpt-4-turbo-2024-04-09": {"description": "El último modelo GPT-4 Turbo cuenta con funciones visuales. Ahora, las solicitudes visuales pueden utilizar el modo JSON y llamadas a funciones. GPT-4 Turbo es una versión mejorada que ofrece soporte rentable para tareas multimodales. Encuentra un equilibrio entre precisión y eficiencia, adecuado para aplicaciones que requieren interacción en tiempo real."}, "gpt-4-turbo-preview": {"description": "El último modelo GPT-4 Turbo cuenta con funciones visuales. Ahora, las solicitudes visuales pueden utilizar el modo JSON y llamadas a funciones. GPT-4 Turbo es una versión mejorada que ofrece soporte rentable para tareas multimodales. Encuentra un equilibrio entre precisión y eficiencia, adecuado para aplicaciones que requieren interacción en tiempo real."}, "gpt-4-vision-preview": {"description": "El último modelo GPT-4 Turbo cuenta con funciones visuales. Ahora, las solicitudes visuales pueden utilizar el modo JSON y llamadas a funciones. GPT-4 Turbo es una versión mejorada que ofrece soporte rentable para tareas multimodales. Encuentra un equilibrio entre precisión y eficiencia, adecuado para aplicaciones que requieren interacción en tiempo real."}, "gpt-4.1": {"description": "GPT-4.1 es nuestro modelo insignia para tareas complejas. Es ideal para resolver problemas en múltiples dominios."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini ofrece un equilibrio entre inteligencia, velocidad y costo, lo que lo convierte en un modelo atractivo para muchos casos de uso."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini ofrece un equilibrio entre inteligencia, velocidad y costo, lo que lo convierte en un modelo atractivo para muchos casos de uso."}, "gpt-4.5-preview": {"description": "Versión de investigación de GPT-4.5, que es nuestro modelo GPT más grande y potente hasta la fecha. Posee un amplio conocimiento del mundo y puede comprender mejor la intención del usuario, lo que lo hace destacar en tareas creativas y planificación autónoma. GPT-4.5 acepta entradas de texto e imagen y genera salidas de texto (incluidas salidas estructuradas). Soporta funciones clave para desarrolladores, como llamadas a funciones, API por lotes y salida en streaming. En tareas que requieren pensamiento creativo, abierto y diálogo (como escritura, aprendizaje o exploración de nuevas ideas), GPT-4.5 brilla especialmente. La fecha límite de conocimiento es octubre de 2023."}, "gpt-4o": {"description": "ChatGPT-4o es un modelo dinámico que se actualiza en tiempo real para mantener la versión más actual. Combina una poderosa comprensión y generación de lenguaje, adecuado para aplicaciones a gran escala, incluyendo servicio al cliente, educación y soporte técnico."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o es un modelo dinámico que se actualiza en tiempo real para mantener la versión más actual. Combina una poderosa comprensión y generación de lenguaje, adecuado para aplicaciones a gran escala, incluyendo servicio al cliente, educación y soporte técnico."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o es un modelo dinámico que se actualiza en tiempo real para mantener la versión más actual. Combina una poderosa comprensión y generación de lenguaje, adecuado para aplicaciones a gran escala, incluyendo servicio al cliente, educación y soporte técnico."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o es un modelo dinámico que se actualiza en tiempo real para mantener la versión más reciente. Combina una poderosa comprensión del lenguaje con habilidades de generación, adecuada para escenarios de aplicación a gran escala, incluidos servicio al cliente, educación y soporte técnico."}, "gpt-4o-audio-preview": {"description": "Modelo de audio GPT-4o, que admite entrada y salida de audio."}, "gpt-4o-mini": {"description": "GPT-4o mini es el último modelo lanzado por OpenAI después de GPT-4 Omni, que admite entradas de texto e imagen y genera texto como salida. Como su modelo más avanzado de menor tamaño, es mucho más económico que otros modelos de vanguardia recientes y es más de un 60% más barato que GPT-3.5 Turbo. Mantiene una inteligencia de vanguardia mientras ofrece una relación calidad-precio significativa. GPT-4o mini obtuvo un puntaje del 82% en la prueba MMLU y actualmente se clasifica por encima de GPT-4 en preferencias de chat."}, "gpt-4o-mini-audio-preview": {"description": "Modelo GPT-4o mini Audio, que soporta entrada y salida de audio."}, "gpt-4o-mini-realtime-preview": {"description": "Versión en tiempo real de GPT-4o-mini, que admite entrada y salida de audio y texto en tiempo real."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini versión preliminar de búsqueda es un modelo entrenado específicamente para comprender y ejecutar consultas de búsqueda web, utilizando la API de Chat Completions. Además de los costos por tokens, las consultas de búsqueda web incurren en cargos por cada llamada a la herramienta."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe es un modelo de conversión de voz a texto que utiliza GPT-4o para transcribir audio. En comparación con el modelo Whisper original, mejora la tasa de error de palabras y aumenta la precisión y el reconocimiento del idioma. Úselo para obtener transcripciones más precisas."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS es un modelo de texto a voz basado en GPT-4o mini, que ofrece generación de voz de alta calidad a un costo más bajo."}, "gpt-4o-realtime-preview": {"description": "Versión en tiempo real de GPT-4o, que admite entrada y salida de audio y texto en tiempo real."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Versión en tiempo real de GPT-4o, que admite entrada y salida de audio y texto en tiempo real."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Versión en tiempo real de GPT-4o, que soporta entrada y salida de audio y texto en tiempo real."}, "gpt-4o-search-preview": {"description": "GPT-4o versión preliminar de búsqueda es un modelo entrenado específicamente para comprender y ejecutar consultas de búsqueda web, utilizando la API de Chat Completions. Además de los costos por tokens, las consultas de búsqueda web incurren en cargos por cada llamada a la herramienta."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe es un modelo de conversión de voz a texto que utiliza GPT-4o para transcribir audio. En comparación con el modelo Whisper original, mejora la tasa de error de palabras y aumenta la precisión y el reconocimiento del idioma. Úselo para obtener transcripciones más precisas."}, "gpt-image-1": {"description": "Modelo nativo multimodal de generación de imágenes de ChatGPT."}, "grok-2-1212": {"description": "Este modelo ha mejorado en precisión, cumplimiento de instrucciones y capacidades multilingües."}, "grok-2-image-1212": {"description": "Nuestro último modelo de generación de imágenes puede crear imágenes vívidas y realistas a partir de indicaciones textuales. Destaca en generación de imágenes para marketing, redes sociales y entretenimiento."}, "grok-2-vision-1212": {"description": "Este modelo ha mejorado en precisión, cumplimiento de instrucciones y capacidades multilingües."}, "grok-3": {"description": "Modelo insignia, experto en extracción de datos, programación y resumen de texto para aplicaciones empresariales, con profundo conocimiento en finanzas, medicina, derecho y ciencias."}, "grok-3-fast": {"description": "Modelo insignia, experto en extracción de datos, programación y resumen de texto para aplicaciones empresariales, con profundo conocimiento en finanzas, medicina, derecho y ciencias."}, "grok-3-mini": {"description": "Modelo ligero que piensa antes de responder. Rápido e inteligente, adecuado para tareas lógicas que no requieren conocimientos profundos de dominio y capaz de proporcionar la trayectoria original del pensamiento."}, "grok-3-mini-fast": {"description": "Modelo ligero que piensa antes de responder. Rápido e inteligente, adecuado para tareas lógicas que no requieren conocimientos profundos de dominio y capaz de proporcionar la trayectoria original del pensamiento."}, "grok-4": {"description": "Nuestro modelo insignia más reciente y potente, que destaca en procesamiento de lenguaje natural, c<PERSON><PERSON><PERSON>lo matemá<PERSON> y razonamiento — un competidor versátil y perfecto."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B es un modelo de lenguaje que combina creatividad e inteligencia, fusionando múltiples modelos de vanguardia."}, "hunyuan-a13b": {"description": "El primer modelo de razonamiento híbrido de Hunyuan, una versión mejorada de hunyuan-standard-256K, con un total de 80 mil millones de parámetros y 13 mil millones activados. Por defecto opera en modo de pensamiento lento, pero soporta cambio entre modos rápido y lento mediante parámetros o instrucciones, añadiendo / no_think antes de la consulta para alternar. Su capacidad general mejora integralmente respecto a la generación anterior, con avances notables en matemáticas, ciencias, comprensión de textos largos y habilidades de agente."}, "hunyuan-code": {"description": "El último modelo de generación de código de Hunyuan, entrenado con 200B de datos de código de alta calidad, con medio año de entrenamiento de datos SFT de alta calidad, aumentando la longitud de la ventana de contexto a 8K, destacándose en métricas automáticas de generación de código en cinco lenguajes; en evaluaciones de calidad humana de tareas de código en diez aspectos en cinco lenguajes, su rendimiento se encuentra en la primera categoría."}, "hunyuan-functioncall": {"description": "El último modelo FunctionCall de Hunyuan con arquitectura MOE, entrenado con datos de FunctionCall de alta calidad, con una ventana de contexto de 32K, liderando en múltiples dimensiones de métricas de evaluación."}, "hunyuan-large": {"description": "El modelo Hunyuan-large tiene un total de aproximadamente 389B de parámetros, con aproximadamente 52B de parámetros activados, siendo el modelo MoE de código abierto con la mayor escala de parámetros y el mejor rendimiento en la arquitectura Transformer en la industria actual."}, "hunyuan-large-longcontext": {"description": "Especializado en tareas de texto largo como resúmenes de documentos y preguntas y respuestas de documentos, también tiene la capacidad de manejar tareas generales de generación de texto. Destaca en el análisis y generación de textos largos, pudiendo abordar eficazmente las necesidades de procesamiento de contenido largo y complejo."}, "hunyuan-large-vision": {"description": "Este modelo es adecuado para escenarios de comprensión de imágenes y texto, basado en el modelo visual-lingüístico Hunyuan Large. Soporta entrada de múltiples imágenes de cualquier resolución junto con texto, generando contenido textual, con un enfoque en tareas relacionadas con la comprensión de imágenes y texto, mostrando mejoras significativas en capacidades multilingües."}, "hunyuan-lite": {"description": "Actualizado a una estructura MOE, con una ventana de contexto de 256k, lidera en múltiples conjuntos de evaluación en NLP, código, matemáticas, industria y más, superando a muchos modelos de código abierto."}, "hunyuan-lite-vision": {"description": "El modelo multimodal más reciente de 7B de Hunyuan, con una ventana de contexto de 32K, soporta diálogos multimodales en chino e inglés, reconocimiento de objetos en imágenes, comprensión de documentos y tablas, matemáticas multimodales, entre otros, superando a modelos competidores de 7B en múltiples dimensiones de evaluación."}, "hunyuan-pro": {"description": "Modelo de texto largo MOE-32K con un tamaño de parámetros de billones. Alcanzando niveles de liderazgo absoluto en varios benchmarks, con capacidades complejas de instrucciones y razonamiento, habilidades matemáticas complejas, soporte para llamadas a funciones, optimizado para aplicaciones en traducción multilingüe, finanzas, derecho y medicina."}, "hunyuan-role": {"description": "El último modelo de rol de Hunyuan, un modelo de rol ajustado y entrenado oficialmente por Hunyuan, que se basa en el modelo Hunyuan y se entrena con un conjunto de datos de escenarios de rol, logrando un mejor rendimiento en escenarios de rol."}, "hunyuan-standard": {"description": "Adopta una estrategia de enrutamiento mejorada, al tiempo que mitiga problemas de equilibrio de carga y convergencia de expertos. En el caso de textos largos, el índice de precisión alcanza el 99.9%. MOE-32K ofrece una mejor relación calidad-precio, equilibrando efectividad y costo, permitiendo el procesamiento de entradas de texto largo."}, "hunyuan-standard-256K": {"description": "Adopta una estrategia de enrutamiento mejorada, al tiempo que mitiga problemas de equilibrio de carga y convergencia de expertos. En el caso de textos largos, el índice de precisión alcanza el 99.9%. MOE-256K rompe barreras en longitud y efectividad, ampliando enormemente la longitud de entrada permitida."}, "hunyuan-standard-vision": {"description": "El modelo multimodal más reciente de Hunyuan, que soporta respuestas en múltiples idiomas, con capacidades equilibradas en chino e inglés."}, "hunyuan-t1-20250321": {"description": "Construye de manera integral las capacidades de modelos en ciencias exactas y humanidades, con una fuerte capacidad para capturar información de textos largos. Soporta la inferencia y respuesta a problemas científicos de diversas dificultades, incluyendo matemáticas, lógica, ciencias y código."}, "hunyuan-t1-20250403": {"description": "Mejora la capacidad de generación de código a nivel de proyecto; mejora la calidad de la escritura generada en texto; mejora la comprensión de temas en texto, el seguimiento de instrucciones tob en múltiples rondas y la comprensión de palabras; optimiza problemas de salida con mezcla de caracteres tradicionales y simplificados, así como mezcla de chino e inglés."}, "hunyuan-t1-20250529": {"description": "Optimiza la creación de textos, redacción de ensayos, mejora habilidades en programación frontend, matemáticas y razonamiento lógico, y aumenta la capacidad de seguir instrucciones."}, "hunyuan-t1-20250711": {"description": "Mejora significativa en habilidades avanzadas de matemáticas, lógica y codificación, optimización de la estabilidad de salida del modelo y aumento de la capacidad para textos largos."}, "hunyuan-t1-latest": {"description": "El primer modelo de inferencia híbrido de gran escala Hybrid-Transformer-Mamba de la industria, que amplía la capacidad de inferencia, ofrece una velocidad de decodificación excepcional y alinea aún más con las preferencias humanas."}, "hunyuan-t1-vision": {"description": "Modelo de pensamiento profundo multimodal Hunyuan, que soporta cadenas de pensamiento nativas multimodales, sobresale en diversos escenarios de razonamiento con imágenes y mejora significativamente en problemas científicos en comparación con modelos de pensamiento rápido."}, "hunyuan-t1-vision-20250619": {"description": "La última versión del modelo de pensamiento profundo multimodal t1-vision de Hunyuan, que soporta cadenas de pensamiento nativas multimodales, con mejoras integrales respecto a la versión predeterminada anterior."}, "hunyuan-turbo": {"description": "Versión preliminar de la nueva generación del modelo de lenguaje de Hunyuan, que utiliza una nueva estructura de modelo de expertos mixtos (MoE), con una eficiencia de inferencia más rápida y un rendimiento más fuerte en comparación con Hunyuan-Pro."}, "hunyuan-turbo-20241223": {"description": "Optimización de esta versión: escalado de instrucciones de datos, mejora significativa de la capacidad de generalización del modelo; mejora significativa de las capacidades de matemáticas, código y razonamiento lógico; optimización de la comprensión de texto y de palabras relacionadas; optimización de la calidad de generación de contenido en la creación de texto."}, "hunyuan-turbo-latest": {"description": "Optimización de la experiencia general, incluyendo comprensión de NLP, creación de texto, conversación casual, preguntas y respuestas de conocimiento, traducción, entre otros; mejora de la humanización, optimización de la inteligencia emocional del modelo; mejora de la capacidad del modelo para aclarar proactivamente en caso de ambigüedad en la intención; mejora de la capacidad de manejo de problemas de análisis de palabras; mejora de la calidad y la interactividad de la creación; mejora de la experiencia en múltiples turnos."}, "hunyuan-turbo-vision": {"description": "El nuevo modelo insignia de lenguaje visual de Hunyuan de nueva generación, que utiliza una nueva estructura de modelo de expertos mixtos (MoE), mejorando de manera integral las capacidades de reconocimiento básico, creación de contenido, preguntas y respuestas de conocimiento, y análisis y razonamiento en comparación con la generación anterior de modelos."}, "hunyuan-turbos-20250313": {"description": "Unificación del estilo de pasos para resolver problemas matemáticos, fortaleciendo las preguntas y respuestas multilínea en matemáticas. Optimización del estilo de respuesta en creación de texto, eliminando el tono artificial de IA y aumentando la elegancia literaria."}, "hunyuan-turbos-20250416": {"description": "Actualización de la base de preentrenamiento para fortalecer la comprensión y el seguimiento de instrucciones; mejora en matemáticas, programación, lógica y ciencias durante la fase de alineación; mejora en calidad de escritura creativa, comprensión de texto, precisión en traducción y preguntas de conocimiento en humanidades; refuerzo de capacidades de agentes en diversos campos, con especial énfasis en la comprensión de diálogos multilínea."}, "hunyuan-turbos-20250604": {"description": "Actualización de la base de preentrenamiento, mejora en la escritura y comprensión lectora, aumento significativo en habilidades de programación y ciencias, y progreso continuo en el seguimiento de instrucciones complejas."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS es la última versión del modelo insignia <PERSON><PERSON><PERSON>, con una mayor capacidad de pensamiento y una mejor experiencia."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Especializado en tareas de texto largo como resúmenes de documentos y preguntas sobre documentos, también tiene la capacidad de manejar tareas generales de generación de texto. Destaca en el análisis y generación de textos largos, capaz de abordar eficazmente las necesidades complejas y detalladas de procesamiento de contenido extenso."}, "hunyuan-turbos-role-plus": {"description": "Modelo de rol más reciente de Hunyuan, afinado oficialmente por Hunyuan, entrenado adicionalmente con conjuntos de datos de escenarios de juego de roles, ofreciendo mejores resultados básicos en dichos escenarios."}, "hunyuan-turbos-vision": {"description": "Este modelo está diseñado para escenarios de comprensión de imágenes y texto, basado en la última generación de modelos insignia visual-lingüísticos turbos de Hunyuan. Se enfoca en tareas relacionadas con la comprensión de imágenes, incluyendo reconocimiento de entidades basado en imágenes, preguntas de conocimiento, creación de textos y resolución de problemas mediante fotos, con mejoras integrales respecto a la generación anterior."}, "hunyuan-turbos-vision-20250619": {"description": "La última versión del modelo insignia visual-lingüístico turbos-vision de Hunyuan, que mejora integralmente la comprensión de imágenes y texto, incluyendo reconocimiento de entidades basado en imágenes, preguntas de conocimiento, creación de textos y resolución de problemas mediante fotos, respecto a la versión predeterminada anterior."}, "hunyuan-vision": {"description": "El último modelo multimodal de Hunyuan, que admite la entrada de imágenes y texto para generar contenido textual."}, "image-01": {"description": "Nuevo modelo de generación de imágenes con detalles finos, soporta generación de imágenes a partir de texto e imagen."}, "image-01-live": {"description": "Modelo de generación de imágenes con detalles finos, soporta generación a partir de texto y configuración de estilo artístico."}, "imagen-4.0-generate-preview-06-06": {"description": "Serie de modelos de texto a imagen de cuarta generación de Imagen"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Serie de modelos de texto a imagen de cuarta generación de Imagen, versión Ultra"}, "imagen4/preview": {"description": "El modelo de generación de imágenes de mayor calidad de Google."}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 ofrece soluciones de diálogo inteligente en múltiples escenarios."}, "internlm2.5-latest": {"description": "Nuestra última serie de modelos, con un rendimiento de inferencia excepcional, que admite una longitud de contexto de 1M y una mayor capacidad de seguimiento de instrucciones y llamadas a herramientas."}, "internlm3-latest": {"description": "Nuestra última serie de modelos, con un rendimiento de inferencia excepcional, lidera el mercado de modelos de código abierto de tamaño similar. Apunta por defecto a nuestra serie de modelos InternLM3 más reciente."}, "internvl2.5-latest": {"description": "La versión InternVL2.5 que seguimos manteniendo, que ofrece un rendimiento excelente y estable. Por defecto, apunta a nuestra serie de modelos InternVL2.5 más reciente, actualmente apuntando a internvl2.5-78b."}, "internvl3-latest": {"description": "Nuestro modelo multimodal más reciente, que posee una mayor capacidad de comprensión de texto e imagen, así como una comprensión de imágenes a largo plazo, con un rendimiento comparable a los mejores modelos cerrados. Por defecto, apunta a nuestra serie de modelos InternVL más reciente, actualmente apuntando a internvl3-78b."}, "irag-1.0": {"description": "iRAG (image based RAG) desarrollado por Baidu es una tecnología de generación de imágenes mejorada con recuperación, que combina los recursos de miles de millones de imágenes de búsqueda de Baidu con potentes capacidades de modelos base para generar imágenes ultra realistas. Su efecto supera ampliamente los sistemas nativos de generación de imágenes, eliminando el aspecto artificial de la IA y con costos muy bajos. iRAG se caracteriza por no generar alucinaciones, ultra realismo y resultados inmediatos."}, "jamba-large": {"description": "Nuestro modelo más potente y avanzado, diseñado para manejar tareas complejas a nivel empresarial, con un rendimiento excepcional."}, "jamba-mini": {"description": "El modelo más eficiente de su categoría, que combina velocidad y calidad, con un tamaño más pequeño."}, "jina-deepsearch-v1": {"description": "La búsqueda profunda combina la búsqueda en la web, la lectura y el razonamiento para realizar investigaciones exhaustivas. Puedes considerarlo como un agente que acepta tus tareas de investigación: realiza una búsqueda amplia y pasa por múltiples iteraciones antes de proporcionar una respuesta. Este proceso implica una investigación continua, razonamiento y resolución de problemas desde diferentes ángulos. Esto es fundamentalmente diferente de los grandes modelos estándar que generan respuestas directamente a partir de datos preentrenados y de los sistemas RAG tradicionales que dependen de búsquedas superficiales únicas."}, "kimi-k2": {"description": "Kimi-K2 es un modelo base con arquitectura MoE lanzado por Moonshot AI, con capacidades avanzadas de codificación y agentes, totalizando 1 billón de parámetros y 32 mil millones de parámetros activados. En pruebas de referencia en categorías principales como razonamiento general, programación, matemáticas y agentes, el rendimiento del modelo K2 supera a otros modelos de código abierto populares."}, "kimi-k2-0711-preview": {"description": "kimi-k2 es un modelo base con arquitectura MoE que posee capacidades excepcionales en código y agentes, con un total de 1T parámetros y 32B parámetros activados. En pruebas de rendimiento en categorías principales como razonamiento general, programación, matemáticas y agentes, el modelo K2 supera a otros modelos de código abierto populares."}, "kimi-latest": {"description": "El producto asistente inteligente Kimi utiliza el último modelo grande de Kimi, que puede incluir características que aún no están estables. Soporta la comprensión de imágenes y seleccionará automáticamente el modelo de facturación de 8k/32k/128k según la longitud del contexto de la solicitud."}, "kimi-thinking-preview": {"description": "El modelo kimi-thinking-preview, proporcionado por la cara oculta de la luna, es un modelo multimodal de pensamiento con capacidades de razonamiento multimodal y general, especializado en razonamiento profundo para ayudar a resolver problemas más complejos."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM es un modelo de lenguaje experimental y específico para tareas, entrenado para cumplir con los principios de la ciencia del aprendizaje, capaz de seguir instrucciones sistemáticas en escenarios de enseñanza y aprendizaje, actuando como un tutor experto, entre otros."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM es un modelo de lenguaje experimental y específico para tareas, entrenado para cumplir con los principios de la ciencia del aprendizaje, capaz de seguir instrucciones sistemáticas en escenarios de enseñanza y aprendizaje, actuando como un tutor experto, entre otros."}, "lite": {"description": "Spark Lite es un modelo de lenguaje grande y ligero, con una latencia extremadamente baja y una capacidad de procesamiento eficiente, completamente gratuito y de código abierto, que admite funciones de búsqueda en línea en tiempo real. Su característica de respuesta rápida lo hace destacar en aplicaciones de inferencia y ajuste de modelos en dispositivos de baja potencia, brindando a los usuarios una excelente relación costo-beneficio y experiencia inteligente, especialmente en escenarios de preguntas y respuestas, generación de contenido y búsqueda."}, "llama-2-7b-chat": {"description": "Llama2 es una serie de modelos de lenguaje de gran escala (LLM) desarrollados y publicados por Meta, que incluye modelos de texto generativo preentrenados y ajustados de diferentes tamaños, desde 7 mil millones hasta 70 mil millones de parámetros. A nivel de arquitectura, Llama2 es un modelo de lenguaje autoregresivo que utiliza una arquitectura de transformador optimizada. Las versiones ajustadas utilizan un ajuste de fine-tuning supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF) para alinear las preferencias de utilidad y seguridad humanas. Llama2 supera a la serie Llama en varios conjuntos de datos académicos, proporcionando ideas para el diseño y desarrollo de numerosos otros modelos."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B ofrece una capacidad de razonamiento AI más potente, adecuada para aplicaciones complejas, soportando un procesamiento computacional extenso y garantizando eficiencia y precisión."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B es un modelo de alto rendimiento que ofrece una rápida capacidad de generación de texto, ideal para aplicaciones que requieren eficiencia a gran escala y rentabilidad."}, "llama-3.1-instruct": {"description": "El modelo Llama 3.1 ajustado para instrucciones está optimizado para escenarios de conversación, superando a muchos modelos de chat de código abierto existentes en pruebas de referencia comunes de la industria."}, "llama-3.2-11b-vision-instruct": {"description": "Capacidad excepcional de razonamiento visual en imágenes de alta resolución, adecuada para aplicaciones de comprensión visual."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Destaca en tareas como la descripción de imágenes y preguntas visuales, cruzando la brecha entre la generación de lenguaje y el razonamiento visual."}, "llama-3.2-90b-vision-instruct": {"description": "Capacidad avanzada de razonamiento de imágenes para aplicaciones de agentes de comprensión visual."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Destaca en tareas como la descripción de imágenes y preguntas visuales, cruzando la brecha entre la generación de lenguaje y el razonamiento visual."}, "llama-3.2-vision-instruct": {"description": "El modelo Llama 3.2-Vision con ajuste fino de instrucciones está optimizado para reconocimiento visual, razonamiento sobre imágenes, descripción de imágenes y respuesta a preguntas generales relacionadas con imágenes."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 es el modelo de lenguaje de código abierto multilingüe más avanzado de la serie Llama, que ofrece un rendimiento comparable al modelo de 405B a un costo extremadamente bajo. Basado en la estructura Transformer, y mejorado en utilidad y seguridad a través de ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF). Su versión ajustada para instrucciones está optimizada para diálogos multilingües, superando a muchos modelos de chat de código abierto y cerrado en múltiples benchmarks de la industria. La fecha límite de conocimiento es diciembre de 2023."}, "llama-3.3-70b-versatile": {"description": "El modelo de lenguaje multilingüe Meta Llama 3.3 (LLM) es un modelo generativo preentrenado y ajustado para instrucciones de 70B (entrada/salida de texto). El modelo de texto puro ajustado para instrucciones de Llama 3.3 está optimizado para casos de uso de conversación multilingüe y supera a muchos modelos de chat de código abierto y cerrado en benchmarks industriales comunes."}, "llama-3.3-instruct": {"description": "El modelo de instrucción Llama 3.3, optimizado para escenarios de diálogo, supera a muchos modelos de chat de código abierto existentes en pruebas de referencia comunes de la industria."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B proporciona una capacidad de procesamiento de complejidad inigualable, diseñado a medida para proyectos de alta demanda."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B ofrece un rendimiento de razonamiento de alta calidad, adecuado para diversas necesidades de aplicación."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use ofrece una potente capacidad de invocación de herramientas, apoyando el procesamiento eficiente de tareas complejas."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use es un modelo optimizado para el uso eficiente de herramientas, que admite cálculos paralelos rápidos."}, "llama3.1": {"description": "Llama 3.1 es el modelo líder lanzado por Meta, que admite hasta 405B de parámetros, aplicable en diálogos complejos, traducción multilingüe y análisis de datos."}, "llama3.1:405b": {"description": "Llama 3.1 es el modelo líder lanzado por Meta, que admite hasta 405B de parámetros, aplicable en diálogos complejos, traducción multilingüe y análisis de datos."}, "llama3.1:70b": {"description": "Llama 3.1 es el modelo líder lanzado por Meta, que admite hasta 405B de parámetros, aplicable en diálogos complejos, traducción multilingüe y análisis de datos."}, "llava": {"description": "LLaVA es un modelo multimodal que combina un codificador visual y Vicuna, utilizado para una poderosa comprensión visual y lingüística."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B proporciona capacidades de procesamiento visual integradas, generando salidas complejas a partir de entradas de información visual."}, "llava:13b": {"description": "LLaVA es un modelo multimodal que combina un codificador visual y Vicuna, utilizado para una poderosa comprensión visual y lingüística."}, "llava:34b": {"description": "LLaVA es un modelo multimodal que combina un codificador visual y Vicuna, utilizado para una poderosa comprensión visual y lingüística."}, "mathstral": {"description": "MathΣtral está diseñado para la investigación científica y el razonamiento matemático, proporcionando capacidades de cálculo efectivas y explicación de resultados."}, "max-32k": {"description": "Spark Max 32K está equipado con una capacidad de procesamiento de contexto grande, con una comprensión contextual más fuerte y habilidades de razonamiento lógico, soportando entradas de texto de 32K tokens, adecuado para la lectura de documentos largos, preguntas y respuestas de conocimiento privado y otros escenarios."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct es un modelo de lenguaje grande entrenado completamente de forma autónoma por Wúwèn Xīnqióng. Megrez-3B-Instruct tiene como objetivo crear una solución de inteligencia periférica rápida, compacta y fácil de usar, basada en el concepto de colaboración entre hardware y software."}, "meta-llama-3-70b-instruct": {"description": "Un poderoso modelo de 70 mil millones de parámetros que sobresale en razonamiento, codificación y amplias aplicaciones de lenguaje."}, "meta-llama-3-8b-instruct": {"description": "Un modelo versátil de 8 mil millones de parámetros optimizado para tareas de diálogo y generación de texto."}, "meta-llama-3.1-405b-instruct": {"description": "Los modelos de texto solo ajustados por instrucciones Llama 3.1 están optimizados para casos de uso de diálogo multilingüe y superan muchos de los modelos de chat de código abierto y cerrados disponibles en los benchmarks de la industria."}, "meta-llama-3.1-70b-instruct": {"description": "Los modelos de texto solo ajustados por instrucciones Llama 3.1 están optimizados para casos de uso de diálogo multilingüe y superan muchos de los modelos de chat de código abierto y cerrados disponibles en los benchmarks de la industria."}, "meta-llama-3.1-8b-instruct": {"description": "Los modelos de texto solo ajustados por instrucciones Llama 3.1 están optimizados para casos de uso de diálogo multilingüe y superan muchos de los modelos de chat de código abierto y cerrados disponibles en los benchmarks de la industria."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) ofrece una excelente capacidad de procesamiento de lenguaje y una experiencia de interacción sobresaliente."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 ofrece excelentes capacidades de procesamiento del lenguaje y una experiencia de interacción excepcional."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) es un modelo de chat potente, que soporta necesidades de conversación complejas."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) ofrece soporte multilingüe, abarcando un amplio conocimiento en diversos campos."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Se destaca en tareas como descripción de imágenes y preguntas visuales, cruzando la brecha entre la generación de lenguaje y el razonamiento visual."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Se destaca en tareas como descripción de imágenes y preguntas visuales, cruzando la brecha entre la generación de lenguaje y el razonamiento visual."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Se destaca en tareas como descripción de imágenes y preguntas visuales, cruzando la brecha entre la generación de lenguaje y el razonamiento visual."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "El modelo de lenguaje grande multilingüe Meta Llama 3.3 (LLM) es un modelo generativo preentrenado y ajustado por instrucciones de 70B (entrada de texto/salida de texto). El modelo de texto puro ajustado por instrucciones de Llama 3.3 está optimizado para casos de uso de diálogo multilingüe y supera a muchos modelos de chat de código abierto y cerrados en benchmarks de la industria."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Se destaca en tareas como descripción de imágenes y preguntas visuales, cruzando la brecha entre la generación de lenguaje y el razonamiento visual."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite es ideal para entornos que requieren alto rendimiento y baja latencia."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo ofrece una capacidad excepcional de comprensión y generación de lenguaje, ideal para las tareas de cálculo más exigentes."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite es adecuado para entornos con recursos limitados, ofreciendo un excelente equilibrio de rendimiento."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo es un modelo de lenguaje de alto rendimiento, adecuado para una amplia gama de escenarios de aplicación."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B es un potente modelo de preentrenamiento y ajuste de instrucciones."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "El modelo Llama 3.1 Turbo de 405B proporciona un soporte de contexto de gran capacidad para el procesamiento de grandes datos, destacándose en aplicaciones de inteligencia artificial a gran escala."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 es el modelo líder lanzado por Meta, que soporta hasta 405B de parámetros, aplicable en diálogos complejos, traducción multilingüe y análisis de datos."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "El modelo Llama 3.1 70B está finamente ajustado para aplicaciones de alta carga, cuantificado a FP8 para ofrecer una capacidad de cálculo y precisión más eficientes, asegurando un rendimiento excepcional en escenarios complejos."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "El modelo Llama 3.1 8B utiliza cuantificación FP8, soportand<PERSON> hasta 131,072 tokens de contexto, destacándose entre los modelos de código abierto, ideal para tareas complejas y superando muchos estándares de la industria."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct está optimizado para escenarios de conversación de alta calidad, destacándose en diversas evaluaciones humanas."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct optimiza los escenarios de conversación de alta calidad, con un rendimiento superior a muchos modelos cerrados."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct está diseñado para conversaciones de alta calidad, destacándose en evaluaciones humanas, especialmente en escenarios de alta interacción."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct es la última versión lanzada por Meta, optimizada para escenarios de conversación de alta calidad, superando a muchos modelos cerrados líderes."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 ofrece soporte multilingüe y es uno de los modelos generativos más avanzados de la industria."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Destaca en tareas como la descripción de imágenes y preguntas visuales, superando la brecha entre la generación de lenguaje y el razonamiento visual."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 está diseñado para manejar tareas que combinan datos visuales y textuales. Destaca en tareas como la descripción de imágenes y preguntas visuales, superando la brecha entre la generación de lenguaje y el razonamiento visual."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 es el modelo de lenguaje de código abierto multilingüe más avanzado de la serie Llama, que ofrece un rendimiento comparable al modelo de 405B a un costo extremadamente bajo. Basado en la estructura Transformer, y mejorado en utilidad y seguridad a través de ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF). Su versión ajustada para instrucciones está optimizada para diálogos multilingües, superando a muchos modelos de chat de código abierto y cerrado en múltiples benchmarks de la industria. La fecha límite de conocimiento es diciembre de 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 es el modelo de lenguaje de código abierto multilingüe más avanzado de la serie Llama, que ofrece un rendimiento comparable al modelo de 405B a un costo extremadamente bajo. Basado en la estructura Transformer, y mejorado en utilidad y seguridad a través de ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF). Su versión ajustada para instrucciones está optimizada para diálogos multilingües, superando a muchos modelos de chat de código abierto y cerrado en múltiples benchmarks de la industria. La fecha límite de conocimiento es diciembre de 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct es el modelo más grande y potente de la serie Llama 3.1 Instruct, un modelo de generación de datos de diálogo y razonamiento altamente avanzado, que también puede servir como base para un preentrenamiento o ajuste fino especializado en dominios específicos. Los modelos de lenguaje de gran tamaño (LLMs) multilingües que ofrece Llama 3.1 son un conjunto de modelos generativos preentrenados y ajustados por instrucciones, que incluyen tamaños de 8B, 70B y 405B (entrada/salida de texto). Los modelos de texto ajustados por instrucciones de Llama 3.1 (8B, 70B, 405B) están optimizados para casos de uso de diálogo multilingüe y superan a muchos modelos de chat de código abierto disponibles en pruebas de referencia de la industria. Llama 3.1 está diseñado para usos comerciales y de investigación en múltiples idiomas. Los modelos de texto ajustados por instrucciones son adecuados para chats similares a asistentes, mientras que los modelos preentrenados pueden adaptarse a diversas tareas de generación de lenguaje natural. El modelo Llama 3.1 también admite el uso de su salida para mejorar otros modelos, incluida la generación de datos sintéticos y el refinamiento. Llama 3.1 es un modelo de lenguaje autorregresivo que utiliza una arquitectura de transformador optimizada. Las versiones ajustadas utilizan ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF) para alinearse con las preferencias humanas de ayuda y seguridad."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "La versión actualizada de Meta Llama 3.1 70B Instruct incluye una longitud de contexto ampliada de 128K, multilingüismo y capacidades de razonamiento mejoradas. Los modelos de lenguaje a gran escala (LLMs) de Llama 3.1 son un conjunto de modelos generativos preentrenados y ajustados por instrucciones, que incluyen tamaños de 8B, 70B y 405B (entrada/salida de texto). Los modelos de texto ajustados por instrucciones de Llama 3.1 (8B, 70B, 405B) están optimizados para casos de uso de diálogo multilingüe y superan muchos modelos de chat de código abierto disponibles en pruebas de referencia de la industria comunes. Llama 3.1 está diseñado para usos comerciales y de investigación en múltiples idiomas. Los modelos de texto ajustados por instrucciones son adecuados para chats similares a asistentes, mientras que los modelos preentrenados pueden adaptarse a diversas tareas de generación de lenguaje natural. El modelo Llama 3.1 también admite el uso de su salida de modelo para mejorar otros modelos, incluyendo la generación de datos sintéticos y refinamiento. Llama 3.1 es un modelo de lenguaje autoregresivo utilizando una arquitectura de transformador optimizada. La versión ajustada utiliza ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF) para alinearse con las preferencias humanas de utilidad y seguridad."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "La versión actualizada de Meta Llama 3.1 8B Instruct incluye una longitud de contexto ampliada de 128K, multilingüismo y capacidades de razonamiento mejoradas. Los modelos de lenguaje a gran escala (LLMs) de Llama 3.1 son un conjunto de modelos generativos preentrenados y ajustados por instrucciones, que incluyen tamaños de 8B, 70B y 405B (entrada/salida de texto). Los modelos de texto ajustados por instrucciones de Llama 3.1 (8B, 70B, 405B) están optimizados para casos de uso de diálogo multilingüe y superan muchos modelos de chat de código abierto disponibles en pruebas de referencia de la industria comunes. Llama 3.1 está diseñado para usos comerciales y de investigación en múltiples idiomas. Los modelos de texto ajustados por instrucciones son adecuados para chats similares a asistentes, mientras que los modelos preentrenados pueden adaptarse a diversas tareas de generación de lenguaje natural. El modelo Llama 3.1 también admite el uso de su salida de modelo para mejorar otros modelos, incluyendo la generación de datos sintéticos y refinamiento. Llama 3.1 es un modelo de lenguaje autoregresivo utilizando una arquitectura de transformador optimizada. La versión ajustada utiliza ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF) para alinearse con las preferencias humanas de utilidad y seguridad."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 es un modelo de lenguaje de gran tamaño (LLM) abierto dirigido a desarrolladores, investigadores y empresas, diseñado para ayudarles a construir, experimentar y escalar de manera responsable sus ideas de IA generativa. Como parte de un sistema base para la innovación de la comunidad global, es ideal para la creación de contenido, IA de diálogo, comprensión del lenguaje, I+D y aplicaciones empresariales."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 es un modelo de lenguaje de gran tamaño (LLM) abierto dirigido a desarrolladores, investigadores y empresas, diseñado para ayudarles a construir, experimentar y escalar de manera responsable sus ideas de IA generativa. Como parte de un sistema base para la innovación de la comunidad global, es ideal para dispositivos de borde con recursos y capacidades computacionales limitadas, así como para tiempos de entrenamiento más rápidos."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Destaca en razonamiento de imágenes de alta resolución, ideal para aplicaciones de comprensión visual."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Capacidades avanzadas de razonamiento de imágenes para aplicaciones de agentes de comprensión visual."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 es el modelo de lenguaje grande multilingüe de código abierto más avanzado de la serie Llama, que ofrece un rendimiento comparable a modelos de 405 mil millones de parámetros a un costo muy bajo. Basado en la arquitectura Transformer, mejorado mediante ajuste fino supervisado (SFT) y aprendizaje por refuerzo con retroalimentación humana (RLHF) para mejorar su utilidad y seguridad. Su versión ajustada por instrucciones está optimizada para diálogos multilingües y supera a muchos modelos de chat abiertos y cerrados en varios puntos de referencia industriales. Fecha de corte de conocimiento: diciembre de 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Un potente modelo de 70 mil millones de parámetros, que destaca en razonamiento, codificación y aplicaciones lingüísticas amplias."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Un modelo versátil de 8 mil millones de parámetros, optimizado para tareas de diálogo y generación de texto."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Modelo de texto ajustado por instrucciones Llama 3.1, optimizado para casos de uso de diálogo multilingüe, con un rendimiento destacado en muchos modelos de chat abiertos y cerrados disponibles y en puntos de referencia industriales comunes."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Modelo de texto ajustado por instrucciones Llama 3.1, optimizado para casos de uso de diálogo multilingüe, con un rendimiento destacado en muchos modelos de chat abiertos y cerrados disponibles y en puntos de referencia industriales comunes."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Modelo de texto ajustado por instrucciones Llama 3.1, optimizado para casos de uso de diálogo multilingüe, con un rendimiento destacado en muchos modelos de chat abiertos y cerrados disponibles y en puntos de referencia industriales comunes."}, "meta/llama-3.1-405b-instruct": {"description": "LLM avanzado, que soporta generación de datos sintéticos, destilación de conocimiento y razonamiento, adecuado para chatbots, programación y tareas de dominio específico."}, "meta/llama-3.1-70b-instruct": {"description": "Potencia diálogos complejos, con excelente comprensión del contexto, capacidad de razonamiento y generación de texto."}, "meta/llama-3.1-8b-instruct": {"description": "Modelo de última generación avanzado, con comprensión del lenguaje, excelente capacidad de razonamiento y generación de texto."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Modelo de visión-lenguaje de vanguardia, experto en razonamiento de alta calidad a partir de imágenes."}, "meta/llama-3.2-1b-instruct": {"description": "Modelo de lenguaje pequeño de última generación, con comprensión del lenguaje, excelente capacidad de razonamiento y generación de texto."}, "meta/llama-3.2-3b-instruct": {"description": "Modelo de lenguaje pequeño de última generación, con comprensión del lenguaje, excelente capacidad de razonamiento y generación de texto."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Modelo de visión-lenguaje de vanguardia, experto en razonamiento de alta calidad a partir de imágenes."}, "meta/llama-3.3-70b-instruct": {"description": "Modelo LL<PERSON>, experto en razonamiento, mate<PERSON><PERSON><PERSON><PERSON>, sentido común y llamadas a funciones."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "El mismo modelo Phi-3-medium, pero con un tamaño de contexto mayor, adecuado para RAG o indicaciones breves."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Un modelo de 14 mil millones de parámetros, con mejor calidad que Phi-3-mini, enfocado en datos de alta calidad y razonamiento intensivo."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "El mismo modelo Phi-3-mini, pero con un tamaño de contexto mayor, adecuado para RAG o indicaciones breves."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "El miembro más pequeño de la familia Phi-3, optimizado para calidad y baja latencia."}, "microsoft/Phi-3-small-128k-instruct": {"description": "El mismo modelo Phi-3-small, pero con un tamaño de contexto mayor, adecuado para RAG o indicaciones breves."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Un modelo de 7 mil millones de parámetros, con mejor calidad que Phi-3-mini, enfocado en datos de alta calidad y razonamiento intensivo."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Versión actualizada del modelo Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Versión actualizada del modelo Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 es un modelo de lenguaje proporcionado por Microsoft AI, que destaca en diálogos complejos, multilingüismo, razonamiento y asistentes inteligentes."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B es el modelo Wizard más avanzado de Microsoft AI, mostrando un rendimiento extremadamente competitivo."}, "minicpm-v": {"description": "MiniCPM-V es la nueva generación de modelos multimodales lanzada por OpenBMB, que cuenta con una excelente capacidad de reconocimiento OCR y comprensión multimodal, soportando una amplia gama de escenarios de aplicación."}, "ministral-3b-latest": {"description": "Ministral 3B es el modelo de borde de primer nivel mundial de Mistral."}, "ministral-8b-latest": {"description": "Ministral 8B es el modelo de borde con la mejor relación calidad-precio de Mistral."}, "mistral": {"description": "Mistral es un modelo de 7B lanzado por Mistral AI, adecuado para necesidades de procesamiento de lenguaje variables."}, "mistral-ai/Mistral-Large-2411": {"description": "El modelo insignia de Mistral, adecuado para tareas complejas que requieren capacidades de razonamiento a gran escala o alta especialización (generación de texto sintético, generación de código, RAG o agentes)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo es un modelo de lenguaje avanzado (LLM) que ofrece capacidades de razonamiento, conocimiento mundial y codificación líderes en su categoría de tamaño."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small está disponible para cualquier tarea basada en lenguaje que requiera alta eficiencia y baja latencia."}, "mistral-large": {"description": "Mixtral Large es el modelo insignia de Mistral, combinando capacidades de generación de código, matemáticas y razonamiento, soportando una ventana de contexto de 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 es un modelo avanzado de lenguaje denso (LLM) con 123 mil millones de parámetros, que posee capacidades de razonamiento, conocimiento y codificación de última generación."}, "mistral-large-latest": {"description": "Mistral Large es el modelo insignia, especializado en tareas multilingües, razonamiento complejo y generación de código, ideal para aplicaciones de alta gama."}, "mistral-medium-latest": {"description": "Mistral Medium 3 ofrece un rendimiento de vanguardia a un costo 8 veces menor y simplifica fundamentalmente el despliegue empresarial."}, "mistral-nemo": {"description": "Mistral Nemo, desarrollado en colaboración entre Mistral AI y NVIDIA, es un modelo de 12B de alto rendimiento."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 es un modelo de lenguaje grande (LLM) que es una versión ajustada por instrucciones de Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small se puede utilizar en cualquier tarea basada en lenguaje que requiera alta eficiencia y baja latencia."}, "mistral-small-latest": {"description": "Mistral Small es una opción rentable, rá<PERSON>a y confiable, adecuada para casos de uso como traducción, resumen y análisis de sentimientos."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct es conocido por su alto rendimiento, adecuado para diversas tareas de lenguaje."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B es un modelo ajustado bajo demanda, proporcionando respuestas optimizadas para tareas."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 ofrece una capacidad de cálculo eficiente y comprensión del lenguaje natural, adecuado para una amplia gama de aplicaciones."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B es un modelo compacto pero de alto rendimiento, ideal para tareas simples como clasificación y generación de texto, con buenas capacidades de razonamiento."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) es un modelo de lenguaje de gran tamaño, que soporta demandas de procesamiento extremadamente altas."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B es un modelo de expertos dispersos preentrenado, utilizado para tareas de texto de uso general."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B es un modelo de expertos dispersos que utiliza múltiples parámetros para aumentar la velocidad de razonamiento, adecuado para tareas de generación de múltiples idiomas y códigos."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct es un modelo de estándar industrial de alto rendimiento, optimizado para velocidad y soporte de contexto largo."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo es un modelo de 7.3B parámetros con soporte multilingüe y programación de alto rendimiento."}, "mixtral": {"description": "Mixtral es el modelo de expertos de Mistral AI, con pesos de código abierto, que ofrece soporte en generación de código y comprensión del lenguaje."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B ofrece una capacidad de cálculo paralelo de alta tolerancia a fallos, adecuada para tareas complejas."}, "mixtral:8x22b": {"description": "Mixtral es el modelo de expertos de Mistral AI, con pesos de código abierto, que ofrece soporte en generación de código y comprensión del lenguaje."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K es un modelo con capacidad de procesamiento de contexto ultra largo, adecuado para generar textos extensos, satisfaciendo las demandas de tareas de generación complejas, capaz de manejar hasta 128,000 tokens, ideal para aplicaciones en investigación, académicas y generación de documentos grandes."}, "moonshot-v1-128k-vision-preview": {"description": "El modelo visual Kimi (incluyendo moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, etc.) puede entender el contenido de las imágenes, incluyendo texto en imágenes, colores de imágenes y formas de objetos."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K ofrece capacidad de procesamiento de contexto de longitud media, capaz de manejar 32,768 tokens, especialmente adecuado para generar diversos documentos largos y diálogos complejos, aplicable en creación de contenido, generación de informes y sistemas de diálogo."}, "moonshot-v1-32k-vision-preview": {"description": "El modelo visual Kimi (incluyendo moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, etc.) puede entender el contenido de las imágenes, incluyendo texto en imágenes, colores de imágenes y formas de objetos."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K está diseñado para tareas de generación de texto corto, con un rendimiento de procesamiento eficiente, capaz de manejar 8,192 tokens, ideal para diálogos breves, toma de notas y generación rápida de contenido."}, "moonshot-v1-8k-vision-preview": {"description": "El modelo visual Kimi (incluyendo moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, etc.) puede entender el contenido de las imágenes, incluyendo texto en imágenes, colores de imágenes y formas de objetos."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto puede seleccionar el modelo adecuado según la cantidad de tokens ocupados en el contexto actual."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B es un modelo de código abierto de gran escala, optimizado mediante aprendizaje reforzado a gran escala, capaz de generar parches robustos y listos para producción. Este modelo alcanzó un nuevo récord del 60.4 % en SWE-bench Verified, estableciendo un nuevo estándar para modelos de código abierto en tareas automatizadas de ingeniería de software como la corrección de errores y la revisión de código."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 es un modelo base con arquitectura MoE que posee capacidades avanzadas de codificación y agentes, con un total de 1 billón de parámetros y 32 mil millones de parámetros activados. En pruebas de referencia en categorías principales como razonamiento general, programación, matemáticas y agentes, el rendimiento del modelo K2 supera a otros modelos de código abierto populares."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 es un modelo base con arquitectura MoE que cuenta con capacidades avanzadas de código y agentes, con un total de 1T parámetros y 32B parámetros activados. En pruebas de referencia en categorías principales como razonamiento de conocimiento general, programación, matemáticas y agentes, el modelo K2 supera el rendimiento de otros modelos de código abierto populares."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B es una versión mejorada de Nous Hermes 2, que incluye los conjuntos de datos más recientes desarrollados internamente."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B es un modelo de lenguaje a gran escala personalizado por NVIDIA, diseñado para mejorar la utilidad de las respuestas generadas por LLM a las consultas de los usuarios. Este modelo ha destacado en pruebas de referencia como Arena Hard, AlpacaEval 2 LC y GPT-4-Turbo MT-Bench, ocupando el primer lugar en los tres benchmarks de alineación automática hasta el 1 de octubre de 2024. El modelo se entrena utilizando RLHF (especialmente REINFORCE), Llama-3.1-Nemotron-70B-Reward y HelpSteer2-Preference sobre la base del modelo Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Modelo de lenguaje único, que ofrece una precisión y eficiencia inigualables."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct es un modelo de lenguaje grande personalizado por NVIDIA, diseñado para mejorar la utilidad de las respuestas generadas por LLM."}, "o1": {"description": "Se centra en el razonamiento avanzado y la resolución de problemas complejos, incluidas tareas matemáticas y científicas. Es muy adecuado para aplicaciones que requieren una comprensión profunda del contexto y flujos de trabajo de agentes."}, "o1-mini": {"description": "o1-mini es un modelo de inferencia rápido y rentable diseñado para aplicaciones de programación, matemáticas y ciencias. Este modelo tiene un contexto de 128K y una fecha de corte de conocimiento en octubre de 2023."}, "o1-preview": {"description": "o1 es el nuevo modelo de inferencia de OpenAI, adecuado para tareas complejas que requieren un amplio conocimiento general. Este modelo tiene un contexto de 128K y una fecha de corte de conocimiento en octubre de 2023."}, "o1-pro": {"description": "La serie o1 ha sido entrenada mediante aprendizaje reforzado para pensar antes de responder y ejecutar tareas de razonamiento complejas. El modelo o1-pro utiliza más recursos computacionales para un pensamiento más profundo, proporcionando respuestas de calidad superior de manera constante."}, "o3": {"description": "o3 es un modelo versátil y potente, que destaca en múltiples campos. Establece un nuevo estándar para tareas de matemáticas, ciencia, programación y razonamiento visual. También es hábil en redacción técnica y seguimiento de instrucciones. Los usuarios pueden utilizarlo para analizar texto, código e imágenes, resolviendo problemas complejos de múltiples pasos."}, "o3-deep-research": {"description": "o3-deep-research es nuestro modelo de investigación profunda más avanzado, diseñado específicamente para manejar tareas complejas de investigación en múltiples pasos. Puede buscar y sintetizar información de Internet, así como acceder y utilizar tus propios datos a través del conector MCP."}, "o3-mini": {"description": "o3-mini es nuestro último modelo de inferencia de tamaño pequeño, que ofrece alta inteligencia con los mismos objetivos de costo y latencia que o1-mini."}, "o3-pro": {"description": "El modelo o3-pro utiliza más capacidad computacional para pensar más profundamente y siempre ofrecer mejores respuestas, y solo está disponible para uso bajo la API de Responses."}, "o4-mini": {"description": "o4-mini es nuestro último modelo de la serie o en formato pequeño. Está optimizado para una inferencia rápida y efectiva, mostrando una alta eficiencia y rendimiento en tareas de codificación y visuales."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research es nuestro modelo de investigación profunda más rápido y asequible, ideal para manejar tareas complejas de investigación en múltiples pasos. Puede buscar y sintetizar información de Internet, así como acceder y utilizar tus propios datos a través del conector MCP."}, "open-codestral-mamba": {"description": "Codestral Mamba es un modelo de lenguaje Mamba 2 enfocado en la generación de código, que proporciona un fuerte apoyo para tareas avanzadas de codificación y razonamiento."}, "open-mistral-7b": {"description": "Mistral 7B es un modelo compacto pero de alto rendimiento, especializado en el procesamiento por lotes y tareas simples, como clasificación y generación de texto, con buenas capacidades de razonamiento."}, "open-mistral-nemo": {"description": "Mistral Nemo es un modelo de 12B desarrollado en colaboración con Nvidia, que ofrece un rendimiento de razonamiento y codificación excepcional, fácil de integrar y reemplazar."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B es un modelo de expertos más grande, enfocado en tareas complejas, que ofrece una excelente capacidad de razonamiento y un mayor rendimiento."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B es un modelo de expertos dispersos que utiliza múltiples parámetros para mejorar la velocidad de razonamiento, adecuado para el procesamiento de tareas de múltiples idiomas y generación de código."}, "openai/gpt-4.1": {"description": "GPT-4.1 es nuestro modelo insignia para tareas complejas. Es especialmente adecuado para resolver problemas interdisciplinarios."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini ofrece un equilibrio entre inteligencia, velocidad y costo, lo que lo convierte en un modelo atractivo para muchos casos de uso."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano es el modelo GPT-4.1 más rápido y rentable."}, "openai/gpt-4o": {"description": "ChatGPT-4o es un modelo dinámico que se actualiza en tiempo real para mantener la versión más actual. Combina una poderosa comprensión y generación de lenguaje, adecuado para escenarios de aplicación a gran escala, incluyendo servicio al cliente, educación y soporte técnico."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini es el modelo más reciente de OpenAI, lanzado después de GPT-4 Omni, que admite entradas de texto e imagen y genera texto como salida. Como su modelo más avanzado de tamaño pequeño, es mucho más económico que otros modelos de vanguardia recientes y más de un 60% más barato que GPT-3.5 Turbo. Mantiene una inteligencia de vanguardia mientras ofrece una relación calidad-precio notable. GPT-4o mini obtuvo un puntaje del 82% en la prueba MMLU y actualmente se clasifica por encima de GPT-4 en preferencias de chat."}, "openai/o1": {"description": "o1 es el nuevo modelo de razonamiento de OpenAI, que admite entradas de texto e imagen y produce texto, adecuado para tareas complejas que requieren un conocimiento general amplio. Este modelo cuenta con un contexto de 200K y una fecha de corte de conocimiento en octubre de 2023."}, "openai/o1-mini": {"description": "o1-mini es un modelo de inferencia rápido y rentable diseñado para aplicaciones de programación, matemáticas y ciencias. Este modelo tiene un contexto de 128K y una fecha de corte de conocimiento en octubre de 2023."}, "openai/o1-preview": {"description": "o1 es el nuevo modelo de inferencia de OpenAI, adecuado para tareas complejas que requieren un amplio conocimiento general. Este modelo tiene un contexto de 128K y una fecha de corte de conocimiento en octubre de 2023."}, "openai/o3": {"description": "o3 es un modelo versátil y potente que destaca en múltiples campos. Establece un nuevo estándar para tareas de matemáticas, ciencias, programación y razonamiento visual. También es hábil en redacción técnica y seguimiento de instrucciones. Los usuarios pueden utilizarlo para analizar texto, código e imágenes, resolviendo problemas complejos de múltiples pasos."}, "openai/o3-mini": {"description": "o3-mini ofrece alta inteligencia con los mismos objetivos de costo y latencia que o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini de alto nivel de razonamiento proporciona alta inteligencia con los mismos objetivos de costo y latencia que o1-mini."}, "openai/o4-mini": {"description": "o4-mini está optimizado para una inferencia rápida y efectiva, mostrando una alta eficiencia y rendimiento en tareas de codificación y visuales."}, "openai/o4-mini-high": {"description": "Versión de alto nivel de inferencia de o4-mini, optimizada para una inferencia rápida y efectiva, mostrando una alta eficiencia y rendimiento en tareas de codificación y visuales."}, "openrouter/auto": {"description": "<PERSON>g<PERSON> la longitud del contexto, el tema y la complejidad, tu solicitud se enviará a Llama 3 70B Instruct, Claude 3.5 Sonnet (autoajuste) o GPT-4o."}, "phi3": {"description": "Phi-3 es un modelo abierto ligero lanzado por Microsoft, adecuado para una integración eficiente y razonamiento de conocimiento a gran escala."}, "phi3:14b": {"description": "Phi-3 es un modelo abierto ligero lanzado por Microsoft, adecuado para una integración eficiente y razonamiento de conocimiento a gran escala."}, "pixtral-12b-2409": {"description": "El modelo Pixtral muestra una fuerte capacidad en tareas como comprensión de gráficos e imágenes, preguntas y respuestas de documentos, razonamiento multimodal y seguimiento de instrucciones, capaz de ingerir imágenes en resolución y proporción natural, y manejar una cantidad arbitraria de imágenes en una ventana de contexto larga de hasta 128K tokens."}, "pixtral-large-latest": {"description": "Pixtral Large es un modelo multimodal de código abierto con 124 mil millones de parámetros, construido sobre Mistral Large 2. Este es nuestro segundo modelo en la familia multimodal, que muestra un nivel de comprensión de imágenes de vanguardia."}, "pro-128k": {"description": "Spark Pro 128K está equipado con una capacidad de procesamiento de contexto extragrande, capaz de manejar hasta 128K de información contextual, especialmente adecuado para el análisis completo y el manejo de relaciones lógicas a largo plazo en contenido extenso, proporcionando una lógica fluida y coherente y un soporte diverso de citas en comunicaciones de texto complejas."}, "qvq-72b-preview": {"description": "El modelo QVQ es un modelo de investigación experimental desarrollado por el equipo de Qwen, enfocado en mejorar la capacidad de razonamiento visual, especialmente en el ámbito del razonamiento matemático."}, "qvq-max": {"description": "Modelo de razonamiento visual QVQ de <PERSON>, que soporta entrada visual y salida de cadena de pensamiento, mostrando capacidades superiores en matemáticas, programación, análisis visual, creación y tareas generales."}, "qvq-plus": {"description": "Modelo de razonamiento visual. Soporta entrada visual y salida en cadena de pensamiento. Versión plus lanzada tras el modelo qvq-max, con mayor velocidad de razonamiento y un equilibrio mejorado entre eficacia y coste en comparación con qvq-max."}, "qwen-coder-plus": {"description": "<PERSON><PERSON> <PERSON><PERSON>."}, "qwen-coder-turbo": {"description": "<PERSON><PERSON> <PERSON><PERSON>."}, "qwen-coder-turbo-latest": {"description": "El modelo de código <PERSON>."}, "qwen-long": {"description": "Qwen es un modelo de lenguaje a gran escala que admite contextos de texto largos y funciones de conversación basadas en documentos largos y múltiples."}, "qwen-math-plus": {"description": "Modelo matemá<PERSON>o <PERSON>wen especializado en resolución de problemas matemáticos."}, "qwen-math-plus-latest": {"description": "El modelo de matemáticas Tongyi Qwen está diseñado específicamente para resolver problemas matemáticos."}, "qwen-math-turbo": {"description": "Modelo matemá<PERSON>o <PERSON>wen especializado en resolución de problemas matemáticos."}, "qwen-math-turbo-latest": {"description": "El modelo de matemáticas Tongyi Qwen está diseñado específicamente para resolver problemas matemáticos."}, "qwen-max": {"description": "El modelo de lenguaje a gran escala Qwen Max, de billones de parámetros, admite entradas en diferentes idiomas como chino e inglés, y actualmente es el modelo API detrás de la versión del producto Qwen 2.5."}, "qwen-omni-turbo": {"description": "La serie Qwen-Omni soporta entrada de múltiples modalidades, incluyendo video, audio, imágenes y texto, y produce salida en audio y texto."}, "qwen-plus": {"description": "La versión mejorada del modelo de lenguaje a gran escala Qwen admite entradas en diferentes idiomas como chino e inglés."}, "qwen-turbo": {"description": "El modelo de lenguaje a gran escala Qwen-Turbo admite entradas en diferentes idiomas como chino e inglés."}, "qwen-vl-chat-v1": {"description": "Qwen VL admite formas de interacción flexibles, incluyendo múltiples imágenes, preguntas y respuestas en múltiples rondas, y capacidades creativas."}, "qwen-vl-max": {"description": "Modelo visual-lingüístico a gran escala Tongyi Qianwen de máxima capacidad. En comparación con la versión mejorada, incrementa aún más la capacidad de razonamiento visual y el seguimiento de instrucciones, ofreciendo un nivel superior de percepción y cognición visual."}, "qwen-vl-max-latest": {"description": "Modelo de lenguaje visual a ultra gran escala Tongyi Qianwen. En comparación con la versión mejorada, mejora aún más la capacidad de razonamiento visual y de seguimiento de instrucciones, ofreciendo un nivel más alto de percepción y cognición visual."}, "qwen-vl-ocr": {"description": "<PERSON><PERSON>wen OCR es un modelo especializado en extracción de texto, enfocado en documentos, tablas, exámenes y escritura manuscrita. Puede reconocer múltiples idiomas, incluyendo chino, ing<PERSON>s, francés, japonés, coreano, alemán, ruso, italiano, vietnamita y árabe."}, "qwen-vl-plus": {"description": "Versión mejorada del modelo visual-lingüístico a gran escala Tongyi Qianwen. Mejora considerablemente la capacidad de reconocimiento de detalles y texto, soportando imágenes con resolución superior a un millón de píxeles y proporciones de aspecto arbitrarias."}, "qwen-vl-plus-latest": {"description": "Versión mejorada del modelo de lenguaje visual a gran escala Tongyi Qianwen. Mejora significativamente la capacidad de reconocimiento de detalles y de texto, soportando imágenes con resolución de más de un millón de píxeles y proporciones de ancho y alto arbitrarias."}, "qwen-vl-v1": {"description": "Iniciado con el modelo de lenguaje Qwen-7B, se añade un modelo de imagen, un modelo preentrenado con una resolución de entrada de imagen de 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 es una nueva serie de modelos de lenguaje grande Qwen. Qwen2 7B es un modelo basado en transformador que destaca en comprensión del lenguaje, capacidades multilingües, programación, matemáticas y razonamiento."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 es una nueva serie de modelos de lenguaje de gran tamaño, con una mayor capacidad de comprensión y generación."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL es la última iteración del modelo Qwen-VL, alcanzando un rendimiento de vanguardia en pruebas de comprensión visual, incluyendo MathVista, DocVQA, RealWorldQA y MTVQA. Qwen2-VL puede entender videos de más de 20 minutos, permitiendo preguntas y respuestas, diálogos y creación de contenido de alta calidad basados en video. También posee capacidades complejas de razonamiento y toma de decisiones, pudiendo integrarse con dispositivos móviles, robots, etc., para realizar operaciones automáticas basadas en el entorno visual y las instrucciones de texto. Además del inglés y el chino, Qwen2-VL ahora también admite la comprensión de texto en diferentes idiomas dentro de imágenes, incluyendo la mayoría de los idiomas europeos, japonés, coreano, árabe y vietnamita."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct es una de las últimas series de modelos de lenguaje grande lanzadas por Alibaba Cloud. Este modelo de 72B presenta capacidades significativamente mejoradas en áreas como codificación y matemáticas. También ofrece soporte multilingüe, abarcando más de 29 idiomas, incluidos chino e inglés. El modelo ha mejorado notablemente en el seguimiento de instrucciones, la comprensión de datos estructurados y la generación de salidas estructuradas (especialmente JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct es una de las últimas series de modelos de lenguaje grande lanzadas por Alibaba Cloud. Este modelo de 32B presenta capacidades significativamente mejoradas en áreas como codificación y matemáticas. También ofrece soporte multilingüe, abarcando más de 29 idiomas, incluidos chino e inglés. El modelo ha mejorado notablemente en el seguimiento de instrucciones, la comprensión de datos estructurados y la generación de salidas estructuradas (especialmente JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM orientado a chino e inglés, enfocado en áreas como lenguaje, programación, matemáticas y razonamiento."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "LLM avanzado, que soporta generación de código, razonamiento y corrección, abarcando lenguajes de programación populares."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Poderoso modelo de código de tamaño mediano, que soporta longitudes de contexto de 32K, experto en programación multilingüe."}, "qwen/qwen3-14b": {"description": "Qwen3-14B es un modelo de lenguaje causal denso de 14.8 mil millones de parámetros en la serie Qwen3, diseñado para razonamiento complejo y diálogos eficientes. Soporta un cambio sin problemas entre un modo de 'pensamiento' para tareas de matemáticas, programación y razonamiento lógico, y un modo 'no reflexivo' para diálogos generales. Este modelo ha sido ajustado para seguir instrucciones, utilizar herramientas de agentes, escribir creativamente y realizar tareas multilingües en más de 100 idiomas y dialectos. Maneja de forma nativa un contexto de 32K tokens y se puede expandir a 131K tokens utilizando extensiones basadas en YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B es un modelo de lenguaje causal denso de 14.8 mil millones de parámetros en la serie Qwen3, diseñado para razonamiento complejo y diálogos eficientes. Soporta un cambio sin problemas entre un modo de 'pensamiento' para tareas de matemáticas, programación y razonamiento lógico, y un modo 'no reflexivo' para diálogos generales. Este modelo ha sido ajustado para seguir instrucciones, utilizar herramientas de agentes, escribir creativamente y realizar tareas multilingües en más de 100 idiomas y dialectos. Maneja de forma nativa un contexto de 32K tokens y se puede expandir a 131K tokens utilizando extensiones basadas en YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B es un modelo de mezcla de expertos (MoE) de 235B parámetros desarrollado por Qwen, que activa 22B parámetros en cada pasada hacia adelante. Soporta un cambio sin problemas entre un modo de 'pensamiento' para razonamiento complejo, matemáticas y tareas de código, y un modo 'no reflexivo' para eficiencia en diálogos generales. Este modelo demuestra una fuerte capacidad de razonamiento, soporte multilingüe (más de 100 idiomas y dialectos), y habilidades avanzadas de seguimiento de instrucciones y llamadas a herramientas de agentes. Maneja de forma nativa una ventana de contexto de 32K tokens y se puede expandir a 131K tokens utilizando extensiones basadas en YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B es un modelo de mezcla de expertos (MoE) de 235B parámetros desarrollado por Qwen, que activa 22B parámetros en cada pasada hacia adelante. Soporta un cambio sin problemas entre un modo de 'pensamiento' para razonamiento complejo, matemáticas y tareas de código, y un modo 'no reflexivo' para eficiencia en diálogos generales. Este modelo demuestra una fuerte capacidad de razonamiento, soporte multilingüe (más de 100 idiomas y dialectos), y habilidades avanzadas de seguimiento de instrucciones y llamadas a herramientas de agentes. Maneja de forma nativa una ventana de contexto de 32K tokens y se puede expandir a 131K tokens utilizando extensiones basadas en YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 es la última generación de la serie de modelos de lenguaje <PERSON>wen, con una arquitectura de mezcla densa y de expertos (MoE), que destaca en razonamiento, soporte multilingüe y tareas avanzadas de agentes. Su capacidad única para cambiar sin problemas entre un modo de pensamiento para razonamiento complejo y un modo no reflexivo para diálogos eficientes garantiza un rendimiento versátil y de alta calidad.\n\nQwen3 supera significativamente a modelos anteriores como QwQ y Qwen2.5, ofreciendo capacidades excepcionales en matemáticas, codificación, razonamiento de sentido común, escritura creativa y diálogos interactivos. La variante Qwen3-30B-A3B contiene 30.5 mil millones de parámetros (3.3 mil millones de parámetros activados), 48 capas, 128 expertos (activando 8 por tarea) y admite un contexto de hasta 131K tokens (usando YaRN), estableciendo un nuevo estándar para modelos de código abierto."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 es la última generación de la serie de modelos de lenguaje <PERSON>wen, con una arquitectura de mezcla densa y de expertos (MoE), que destaca en razonamiento, soporte multilingüe y tareas avanzadas de agentes. Su capacidad única para cambiar sin problemas entre un modo de pensamiento para razonamiento complejo y un modo no reflexivo para diálogos eficientes garantiza un rendimiento versátil y de alta calidad.\n\nQwen3 supera significativamente a modelos anteriores como QwQ y Qwen2.5, ofreciendo capacidades excepcionales en matemáticas, codificación, razonamiento de sentido común, escritura creativa y diálogos interactivos. La variante Qwen3-30B-A3B contiene 30.5 mil millones de parámetros (3.3 mil millones de parámetros activados), 48 capas, 128 expertos (activando 8 por tarea) y admite un contexto de hasta 131K tokens (usando YaRN), estableciendo un nuevo estándar para modelos de código abierto."}, "qwen/qwen3-32b": {"description": "Qwen3-32B es un modelo de lenguaje causal denso de 32.8 mil millones de parámetros en la serie Qwen3, optimizado para razonamiento complejo y diálogos eficientes. Soporta un cambio sin problemas entre un modo de 'pensamiento' para tareas de matemáticas, codificación y razonamiento lógico, y un modo 'no reflexivo' para diálogos más rápidos y generales. Este modelo muestra un rendimiento robusto en seguir instrucciones, utilizar herramientas de agentes, escribir creativamente y realizar tareas multilingües en más de 100 idiomas y dialectos. Maneja de forma nativa un contexto de 32K tokens y se puede expandir a 131K tokens utilizando extensiones basadas en YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B es un modelo de lenguaje causal denso de 32.8 mil millones de parámetros en la serie Qwen3, optimizado para razonamiento complejo y diálogos eficientes. Soporta un cambio sin problemas entre un modo de 'pensamiento' para tareas de matemáticas, codificación y razonamiento lógico, y un modo 'no reflexivo' para diálogos más rápidos y generales. Este modelo muestra un rendimiento robusto en seguir instrucciones, utilizar herramientas de agentes, escribir creativamente y realizar tareas multilingües en más de 100 idiomas y dialectos. Maneja de forma nativa un contexto de 32K tokens y se puede expandir a 131K tokens utilizando extensiones basadas en YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B es un modelo de lenguaje causal denso de 8.2 mil millones de parámetros en la serie Qwen3, diseñado para tareas intensivas en razonamiento y diálogos eficientes. Soporta un cambio sin problemas entre un modo de 'pensamiento' para matemáticas, codificación y razonamiento lógico, y un modo 'no reflexivo' para diálogos generales. Este modelo ha sido ajustado para seguir instrucciones, integrar agentes, escribir creativamente y utilizar más de 100 idiomas y dialectos. Soporta de forma nativa una ventana de contexto de 32K tokens y se puede expandir a 131K tokens a través de YaRN."}, "qwen2": {"description": "Qwen2 es el nuevo modelo de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2-72b-instruct": {"description": "Qwen2 es la nueva serie de modelos de lenguaje de gran escala presentada por el equipo de Qwen. Se basa en la arquitectura Transformer y utiliza funciones de activación SwiGLU, sesgo de atención QKV (attention QKV bias), atención de consulta grupal (group query attention), una mezcla de atención de ventana deslizante y atención completa (mixture of sliding window attention and full attention). Además, el equipo de Qwen ha mejorado el tokenizador para adaptarse a múltiples lenguajes naturales y códigos."}, "qwen2-7b-instruct": {"description": "Qwen2 es una nueva serie de modelos de lenguaje de gran escala desarrollada por el equipo de Qwen. Se basa en la arquitectura Transformer y utiliza funciones de activación SwiGLU, sesgo de atención QKV (attention QKV bias), atención de consulta grupal (group query attention), una mezcla de atención de ventana deslizante y atención completa (mixture of sliding window attention and full attention). Además, el equipo de Qwen ha mejorado el tokenizador para adaptarse a múltiples lenguajes naturales y códigos."}, "qwen2.5": {"description": "Qwen2.5 es la nueva generación de modelos de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2.5-14b-instruct": {"description": "El modelo de 14B de Tongyi Qwen 2.5, de c<PERSON>digo abierto."}, "qwen2.5-14b-instruct-1m": {"description": "El modelo de 72B de Qwen2.5 es de código abierto."}, "qwen2.5-32b-instruct": {"description": "El modelo de 32B de Tongyi Qwen 2.5, de c<PERSON>digo abierto."}, "qwen2.5-72b-instruct": {"description": "El modelo de 72B de Tongyi Qwen 2.5, de c<PERSON><PERSON> abierto."}, "qwen2.5-7b-instruct": {"description": "El modelo de 7B de Tongyi Qwen 2.5, de c<PERSON>digo abierto."}, "qwen2.5-coder-1.5b-instruct": {"description": "La versión de código abierto del modelo Qwen para codificación."}, "qwen2.5-coder-14b-instruct": {"description": "Versión de código de código abierto del modelo Tongyi Qianwen."}, "qwen2.5-coder-32b-instruct": {"description": "Versión de código abierto del modelo de código <PERSON>."}, "qwen2.5-coder-7b-instruct": {"description": "La versión de código abierto del modelo de código <PERSON>."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder es el modelo de lenguaje de gran tamaño más reciente de la serie Qwen especializado en código (anteriormente conocido como CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 es la última serie de modelos de lenguaje extenso Qwen. Para Qwen2.5, hemos lanzado varios modelos de lenguaje base y modelos de lenguaje ajustados por instrucciones, con parámetros que van desde 500 millones hasta 7.2 mil millones."}, "qwen2.5-math-1.5b-instruct": {"description": "El modelo Qwen-Math tiene habilidades poderosas para resolver problemas matemáticos."}, "qwen2.5-math-72b-instruct": {"description": "El modelo Qwen-Math tiene una poderosa capacidad para resolver problemas matemáticos."}, "qwen2.5-math-7b-instruct": {"description": "El modelo Qwen-Math tiene una poderosa capacidad para resolver problemas matemáticos."}, "qwen2.5-omni-7b": {"description": "La serie de modelos Qwen-Omni admite la entrada de datos de múltiples modalidades, incluyendo video, audio, imágenes y texto, y produce audio y texto como salida."}, "qwen2.5-vl-32b-instruct": {"description": "La serie de modelos Qwen2.5-VL ha mejorado el nivel de inteligencia, utilidad y aplicabilidad del modelo, optimizando su rendimiento en escenarios como conversaciones naturales, creación de contenido, servicios de conocimiento especializado y desarrollo de código. La versión 32B utiliza técnicas de aprendizaje por refuerzo para optimizar el modelo, ofreciendo en comparación con otros modelos de la serie Qwen2.5 VL, un estilo de salida más acorde con las preferencias humanas, capacidad de razonamiento para problemas matemáticos complejos, así como comprensión y razonamiento detallado de imágenes."}, "qwen2.5-vl-72b-instruct": {"description": "Mejora general en seguimiento de instrucciones, matemá<PERSON>s, resolución de problemas y código, con capacidades de reconocimiento de objetos mejoradas, soporta formatos diversos para localizar elementos visuales con precisión, y puede entender archivos de video largos (hasta 10 minutos) y localizar eventos en segundos, comprendiendo la secuencia y velocidad del tiempo, soportando el control de agentes en OS o móviles, con fuerte capacidad de extracción de información clave y salida en formato Json. Esta versión es la de 72B, la más potente de la serie."}, "qwen2.5-vl-7b-instruct": {"description": "Mejora general en seguimiento de instrucciones, matemá<PERSON>s, resolución de problemas y código, con capacidades de reconocimiento de objetos mejoradas, soporta formatos diversos para localizar elementos visuales con precisión, y puede entender archivos de video largos (hasta 10 minutos) y localizar eventos en segundos, comprendiendo la secuencia y velocidad del tiempo, soportando el control de agentes en OS o móviles, con fuerte capacidad de extracción de información clave y salida en formato Json. Esta versión es la de 72B, la más potente de la serie."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL es la última versión del modelo de lenguaje visual de la familia de modelos Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 es la nueva generación de modelos de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2.5:1.5b": {"description": "Qwen2.5 es la nueva generación de modelos de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2.5:72b": {"description": "Qwen2.5 es la nueva generación de modelos de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2:0.5b": {"description": "Qwen2 es el nuevo modelo de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2:1.5b": {"description": "Qwen2 es el nuevo modelo de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen2:72b": {"description": "Qwen2 es el nuevo modelo de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen3": {"description": "Qwen3 es la nueva generación de modelo de lenguaje a gran escala de Alibaba, que ofrece un rendimiento excepcional para satisfacer diversas necesidades de aplicación."}, "qwen3-0.6b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-1.7b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-14b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-235b-a22b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-235b-a22b-instruct-2507": {"description": "Modelo de código abierto basado en Qwen3 en modo no reflexivo, con mejoras leves en capacidad creativa subjetiva y seguridad del modelo respecto a la versión anterior (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Modelo de código abierto basado en Qwen3 en modo reflexivo, con mejoras significativas en capacidad lógica, general, enriquecimiento de conocimiento y creatividad respecto a la versión anterior (Tongyi Qianwen 3-235B-A22B), adecuado para escenarios de razonamiento complejo y avanzado."}, "qwen3-30b-a3b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-30b-a3b-instruct-2507": {"description": "En comparación con la versión anterior (Qwen3-30B-A3B), se ha mejorado considerablemente la capacidad general en chino, inglés y otros idiomas. Se ha optimizado especialmente para tareas subjetivas y abiertas, alineándose mucho mejor con las preferencias del usuario y proporcionando respuestas más útiles."}, "qwen3-30b-a3b-thinking-2507": {"description": "Basado en el modelo de código abierto en modo reflexivo de Qwen3, esta versión mejora significativamente la capacidad lógica, la capacidad general, el conocimiento y la creatividad en comparación con la versión anterior (Tongyi Qianwen 3-30B-A3B). Es adecuado para escenarios complejos que requieren un razonamiento avanzado."}, "qwen3-32b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-4b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-8b": {"description": "Qwen3 es un modelo de nueva generación con capacidades significativamente mejoradas, alcanzando niveles líderes en la industria en razonamiento, generalidad, agentes y multilingüismo, y soporta el cambio de modo de pensamiento."}, "qwen3-coder-480b-a35b-instruct": {"description": "Versión de código abierto del modelo de código <PERSON>. El más reciente qwen3-coder-480b-a35b-instruct está basado en Qwen3, con fuertes capacidades de agente de codificación, experto en llamadas a herramientas e interacción con entornos, capaz de programación autónoma y con habilidades sobresalientes de código y capacidades generales."}, "qwen3-coder-plus": {"description": "Modelo de código <PERSON>. La serie más reciente Qwen3-Coder-Plus está basada en Qwen3, con fuertes capacidades de agente de codificación, experto en llamadas a herramientas e interacción con entornos, capaz de programación autónoma y con habilidades sobresalientes de código y capacidades generales."}, "qwq": {"description": "QwQ es un modelo de investigación experimental que se centra en mejorar la capacidad de razonamiento de la IA."}, "qwq-32b": {"description": "El modelo de inferencia QwQ, entrenado con el modelo Qwen2.5-32B, ha mejorado significativamente su capacidad de inferencia a través del aprendizaje por refuerzo. Los indicadores clave del modelo, como el código matemático y otros indicadores centrales (AIME 24/25, LiveCodeBench), así como algunos indicadores generales (IFEval, LiveBench, etc.), han alcanzado el nivel del modelo DeepSeek-R1 en su versión completa, superando notablemente a DeepSeek-R1-Distill-Qwen-32B, que también se basa en Qwen2.5-32B."}, "qwq-32b-preview": {"description": "El modelo QwQ es un modelo de investigación experimental desarrollado por el equipo de Qwen, enfocado en mejorar la capacidad de razonamiento de la IA."}, "qwq-plus": {"description": "Modelo de razonamiento QwQ basado en el modelo Qwen2.5, que mejora significativamente la capacidad de razonamiento mediante aprendizaje reforzado. Los indicadores clave en matemáticas y código (AIME 24/25, LiveCodeBench) y algunos indicadores generales (IFEval, LiveBench, etc.) alcanzan el nivel completo de DeepSeek-R1."}, "qwq_32b": {"description": "Modelo de inferencia de tamaño mediano de la serie Qwen. En comparación con los modelos tradicionales de ajuste por instrucciones, QwQ, que posee capacidades de pensamiento y razonamiento, puede mejorar significativamente el rendimiento en tareas de resolución de problemas, especialmente en tareas difíciles."}, "r1-1776": {"description": "R1-1776 es una versión del modelo DeepSeek R1, que ha sido entrenada posteriormente para proporcionar información factual sin censura y sin sesgos."}, "solar-mini": {"description": "Solar Mini es un LLM compacto que supera a GPT-3.5, con potentes capacidades multilingües, soportando inglés y coreano, ofreciendo soluciones eficientes y compactas."}, "solar-mini-ja": {"description": "Solar Mini (Ja) amplía las capacidades de Solar Mini, enfocándose en japonés, mientras mantiene un rendimiento eficiente y excelente en el uso de inglés y coreano."}, "solar-pro": {"description": "Solar Pro es un LLM de alta inteligencia lanzado por Upstage, enfocado en la capacidad de seguimiento de instrucciones en un solo GPU, con una puntuación IFEval superior a 80. Actualmente soporta inglés, y se planea lanzar la versión oficial en noviembre de 2024, ampliando el soporte de idiomas y la longitud del contexto."}, "sonar": {"description": "Producto de búsqueda ligero basado en contexto de búsqueda, más rápido y económico que Sonar Pro."}, "sonar-deep-research": {"description": "Deep Research realiza una investigación exhaustiva a nivel de expertos y la compila en informes accesibles y prácticos."}, "sonar-pro": {"description": "Producto de búsqueda avanzada que soporta contexto de búsqueda, consultas avanzadas y seguimiento."}, "sonar-reasoning": {"description": "Nuevo producto API respaldado por el modelo de razonamiento de DeepSeek."}, "sonar-reasoning-pro": {"description": "Un nuevo producto API respaldado por el modelo de razonamiento DeepSeek."}, "stable-diffusion-3-medium": {"description": "El último gran modelo de generación de imágenes a partir de texto lanzado por Stability AI. Esta versión mejora significativamente la calidad de imagen, comprensión textual y diversidad de estilos, heredando las ventajas de generaciones anteriores. Puede interpretar con mayor precisión indicaciones complejas en lenguaje natural y generar imágenes más precisas y variadas."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large es un modelo generativo multimodal de difusión transformadora (MMDiT) con 800 millones de parámetros, que ofrece calidad de imagen sobresaliente y alta correspondencia con las indicaciones. Soporta generación de imágenes de alta resolución de hasta 1 millón de píxeles y funciona eficientemente en hardware de consumo común."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo es un modelo basado en stable-diffusion-3.5-large que utiliza tecnología de destilación de difusión adversarial (ADD) para lograr mayor velocidad."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 se inicializa con pesos del punto de control stable-diffusion-v1.2 y se ajusta finamente durante 595k pasos a resolución 512x512 sobre \"laion-aesthetics v2 5+\", reduciendo en un 10% la condicionamiento textual para mejorar el muestreo guiado sin clasificador."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl presenta mejoras significativas respecto a la versión v1.5 y ofrece resultados comparables al modelo SOTA de código abierto midjourney. Las mejoras incluyen un backbone unet tres veces mayor, un módulo de refinamiento para mejorar la calidad de las imágenes generadas y técnicas de entrenamiento más eficientes."}, "stable-diffusion-xl-base-1.0": {"description": "Modelo generativo de imágenes a partir de texto desarrollado y liberado por Stability AI, con capacidades creativas líderes en la industria. Posee excelente comprensión de instrucciones y soporta definiciones de contenido mediante prompts inversos para generación precisa."}, "step-1-128k": {"description": "Equilibrio entre rendimiento y costo, adecuado para escenarios generales."}, "step-1-256k": {"description": "Capacidad de procesamiento de contexto de longitud ultra larga, especialmente adecuada para análisis de documentos largos."}, "step-1-32k": {"description": "Soporta diálogos de longitud media, adecuado para diversas aplicaciones."}, "step-1-8k": {"description": "<PERSON><PERSON> pe<PERSON>, adecuado para tareas ligeras."}, "step-1-flash": {"description": "Modelo de alta velocidad, adecuado para diálogos en tiempo real."}, "step-1.5v-mini": {"description": "Este modelo tiene una potente capacidad de comprensión de video."}, "step-1o-turbo-vision": {"description": "Este modelo tiene una poderosa capacidad de comprensión de imágenes, superando a 1o en matemáticas y programación. El modelo es más pequeño que 1o y tiene una velocidad de salida más rápida."}, "step-1o-vision-32k": {"description": "Este modelo posee una poderosa capacidad de comprensión de imágenes. En comparación con la serie de modelos step-1v, ofrece un rendimiento visual superior."}, "step-1v-32k": {"description": "Soporta entradas visuales, mejorando la experiencia de interacción multimodal."}, "step-1v-8k": {"description": "Modelo visual pequeño, adecuado para tareas básicas de texto e imagen."}, "step-1x-edit": {"description": "Modelo especializado en tareas de edición de imágenes, capaz de modificar y mejorar imágenes según descripciones textuales e imágenes de ejemplo proporcionadas por el usuario. Entiende la intención del usuario y genera resultados de edición de imagen que cumplen con los requisitos."}, "step-1x-medium": {"description": "Modelo con fuerte capacidad de generación de imágenes, que soporta entrada mediante descripciones textuales. Posee soporte nativo para chino, comprendiendo y procesando mejor descripciones en este idioma, capturando con mayor precisión la semántica para convertirla en características visuales y lograr generación de imágenes más precisa. Puede generar imágenes de alta resolución y calidad, con cierta capacidad de transferencia de estilo."}, "step-2-16k": {"description": "Soporta interacciones de contexto a gran escala, adecuado para escenarios de diálogo complejos."}, "step-2-16k-exp": {"description": "Versión experimental del modelo step-2, que incluye las características más recientes y se actualiza continuamente. No se recomienda su uso en entornos de producción formales."}, "step-2-mini": {"description": "Un modelo de gran velocidad basado en la nueva arquitectura de atención autogestionada MFA, que logra efectos similares a los de step1 a un costo muy bajo, manteniendo al mismo tiempo un mayor rendimiento y tiempos de respuesta más rápidos. Capaz de manejar tareas generales, con habilidades destacadas en programación."}, "step-2x-large": {"description": "Nueva generación del modelo Step Star para generación de imágenes, enfocado en tareas de generación basadas en texto, capaz de crear imágenes de alta calidad según descripciones proporcionadas por el usuario. El nuevo modelo produce imágenes con texturas más realistas y mejor capacidad para generar texto en chino e inglés."}, "step-r1-v-mini": {"description": "Este modelo es un gran modelo de inferencia con una poderosa capacidad de comprensión de imágenes, capaz de procesar información de imágenes y texto, generando contenido textual tras un profundo razonamiento. Este modelo destaca en el campo del razonamiento visual, además de poseer capacidades de razonamiento matemático, de código y textual de primer nivel. La longitud del contexto es de 100k."}, "taichu_llm": {"description": "El modelo de lenguaje Taichu de Zīdōng tiene una poderosa capacidad de comprensión del lenguaje, así como habilidades en creación de textos, preguntas y respuestas, programación de código, cál<PERSON>los matemáticos, razonamiento lógico, análisis de sentimientos y resúmenes de texto. Combina de manera innovadora el preentrenamiento con grandes datos y un conocimiento rico de múltiples fuentes, perfeccionando continuamente la tecnología algorítmica y absorbiendo nuevos conocimientos en vocabulario, estructura, gramática y semántica de grandes volúmenes de datos textuales, logrando una evolución constante del modelo. Proporciona a los usuarios información y servicios más convenientes, así como una experiencia más inteligente."}, "taichu_o1": {"description": "taichu_o1 es un nuevo modelo de inferencia de gran escala, que logra un razonamiento similar al humano a través de interacciones multimodales y aprendizaje por refuerzo, apoyando la deducción de decisiones complejas, mostrando rutas de pensamiento modeladas mientras mantiene una alta precisión en la salida, adecuado para análisis de estrategias y razonamiento profundo."}, "taichu_vl": {"description": "Integra capacidades de comprensión de imágenes, transferencia de conocimiento y atribución lógica, destacándose en el campo de preguntas y respuestas basadas en texto e imagen."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct cuenta con 80 mil millones de parámetros, activando solo 13 mil millones para igualar modelos más grandes, soporta razonamiento híbrido de \"pensamiento rápido/pensamiento lento\"; comprensión estable de textos largos; validado por BFCL-v3 y τ-Bench, con capacidades avanzadas de agente; combina GQA y múltiples formatos de cuantificación para lograr inferencias eficientes."}, "text-embedding-3-large": {"description": "El modelo de vectorización más potente, adecuado para tareas en inglés y no inglés."}, "text-embedding-3-small": {"description": "Un modelo de Embedding de nueva generación, eficiente y económico, adecuado para la recuperación de conocimiento, aplicaciones RAG y más."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 es un modelo de lenguaje de pesos abiertos de 32B bilingüe (chino-inglés), optimizado para generación de código, llamadas a funciones y tareas de estilo agente. Ha sido preentrenado en 15T de datos de alta calidad y re-razonamiento, y se ha perfeccionado aún más utilizando alineación de preferencias humanas, muestreo de rechazo y aprendizaje por refuerzo. Este modelo destaca en razonamiento complejo, generación de artefactos y tareas de salida estructurada, alcanzando un rendimiento comparable al de GPT-4o y DeepSeek-V3-0324 en múltiples pruebas de referencia."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 es un modelo de lenguaje de pesos abiertos de 32B bilingüe (chino-inglés), optimizado para generación de código, llamadas a funciones y tareas de estilo agente. Ha sido preentrenado en 15T de datos de alta calidad y re-razonamiento, y se ha perfeccionado aún más utilizando alineación de preferencias humanas, muestreo de rechazo y aprendizaje por refuerzo. Este modelo destaca en razonamiento complejo, generación de artefactos y tareas de salida estructurada, alcanzando un rendimiento comparable al de GPT-4o y DeepSeek-V3-0324 en múltiples pruebas de referencia."}, "thudm/glm-4-9b-chat": {"description": "Versión de código abierto de la última generación del modelo preentrenado GLM-4 lanzado por Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 es un modelo de lenguaje de 9B parámetros en la serie GLM-4 desarrollado por THUDM. GLM-4-9B-0414 utiliza las mismas estrategias de aprendizaje por refuerzo y alineación que su modelo correspondiente de 32B, logrando un alto rendimiento en relación con su tamaño, lo que lo hace adecuado para implementaciones con recursos limitados que aún requieren una fuerte capacidad de comprensión y generación de lenguaje."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 es una variante de razonamiento mejorada de GLM-4-32B, construida para resolver problemas de matemáticas profundas, lógica y orientados al código. Aplica aprendizaje por refuerzo extendido (específico para tareas y basado en preferencias emparejadas generales) para mejorar el rendimiento en tareas complejas de múltiples pasos. En comparación con el modelo base GLM-4-32B, Z1 mejora significativamente las capacidades de razonamiento estructurado y en dominios formalizados.\n\nEste modelo admite la ejecución forzada de pasos de 'pensamiento' a través de ingeniería de indicaciones y proporciona una coherencia mejorada para salidas de formato largo. Está optimizado para flujos de trabajo de agentes y admite contextos largos (a través de YaRN), llamadas a herramientas JSON y configuraciones de muestreo de alta precisión para razonamiento estable. Es ideal para casos de uso que requieren razonamiento reflexivo, de múltiples pasos o deducción formal."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 es una variante de razonamiento mejorada de GLM-4-32B, construida para resolver problemas de matemáticas profundas, lógica y orientados al código. Aplica aprendizaje por refuerzo extendido (específico para tareas y basado en preferencias emparejadas generales) para mejorar el rendimiento en tareas complejas de múltiples pasos. En comparación con el modelo base GLM-4-32B, Z1 mejora significativamente las capacidades de razonamiento estructurado y en dominios formalizados.\n\nEste modelo admite la ejecución forzada de pasos de 'pensamiento' a través de ingeniería de indicaciones y proporciona una coherencia mejorada para salidas de formato largo. Está optimizado para flujos de trabajo de agentes y admite contextos largos (a través de YaRN), llamadas a herramientas JSON y configuraciones de muestreo de alta precisión para razonamiento estable. Es ideal para casos de uso que requieren razonamiento reflexivo, de múltiples pasos o deducción formal."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 es un modelo de lenguaje de 9B parámetros en la serie GLM-4 desarrollado por THUDM. Utiliza técnicas inicialmente aplicadas al modelo GLM-Z1 más grande, incluyendo aprendizaje por refuerzo extendido, alineación de clasificación por pares y entrenamiento para tareas intensivas en razonamiento como matemáticas, código y lógica. A pesar de su menor tamaño, muestra un rendimiento robusto en tareas de razonamiento general y supera a muchos modelos de código abierto en su nivel de pesos."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B es un modelo de razonamiento profundo de 32B parámetros en la serie GLM-4-Z1, optimizado para tareas complejas y abiertas que requieren un pensamiento prolongado. Se basa en glm-4-32b-0414, añadiendo una fase adicional de aprendizaje por refuerzo y estrategias de alineación multietapa, introduciendo una capacidad de 'reflexión' diseñada para simular el procesamiento cognitivo extendido. Esto incluye razonamiento iterativo, análisis de múltiples saltos y flujos de trabajo mejorados por herramientas, como búsqueda, recuperación y síntesis consciente de citas.\n\nEste modelo destaca en escritura de investigación, análisis comparativo y preguntas complejas. Soporta llamadas a funciones para primitivos de búsqueda y navegación (`search`, `click`, `open`, `finish`), lo que permite su uso en tuberías de agentes. El comportamiento reflexivo está moldeado por un control cíclico de múltiples rondas con mecanismos de recompensa basados en reglas y decisiones retrasadas, y se basa en marcos de investigación profunda como el stack de alineación interno de OpenAI. Esta variante es adecuada para escenarios que requieren profundidad en lugar de velocidad."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera se crea combinando DeepSeek-R1 y DeepSeek-V3 (0324), fusionando la capacidad de razonamiento de R1 con las mejoras de eficiencia de tokens de V3. Se basa en la arquitectura DeepSeek-MoE Transformer y está optimizado para tareas generales de generación de texto.\n\nEste modelo combina los pesos preentrenados de los dos modelos fuente para equilibrar el rendimiento en razonamiento, eficiencia y tareas de seguimiento de instrucciones. Se publica bajo la licencia MIT, destinado a fines de investigación y comerciales."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena <PERSON> (7B) proporciona una capacidad de cálculo mejorada a través de estrategias y arquitecturas de modelos eficientes."}, "tts-1": {"description": "El modelo más reciente de texto a voz, optimizado para velocidad en escenarios en tiempo real."}, "tts-1-hd": {"description": "El modelo más reciente de texto a voz, optimizado para calidad."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) es adecuado para tareas de instrucciones detalladas, ofreciendo una excelente capacidad de procesamiento de lenguaje."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet eleva el estándar de la industria, superando a modelos competidores y a Claude 3 Opus, destacándose en evaluaciones amplias, mientras mantiene la velocidad y costo de nuestros modelos de nivel medio."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet es el modelo de próxima generación más rápido de Anthropic. En comparación con Claude 3 Hai<PERSON>, Claude 3.7 Sonnet ha mejorado en todas las habilidades y ha superado al modelo más grande de la generación anterior, Claude 3 Opus, en muchas pruebas de referencia de inteligencia."}, "v0-1.0-md": {"description": "El modelo v0-1.0-md es una versión antigua que ofrece servicios a través de la API v0"}, "v0-1.5-lg": {"description": "El modelo v0-1.5-lg es adecuado para tareas avanzadas de pensamiento o razonamiento"}, "v0-1.5-md": {"description": "El modelo v0-1.5-md es adecuado para tareas cotidianas y generación de interfaces de usuario (UI)"}, "wan2.2-t2i-flash": {"description": "Versión ultra rápida Wanxiang 2.2, el modelo más reciente. Mejora integral en creatividad, estabilidad y realismo, con velocidad de generación rápida y alta relación calidad-precio."}, "wan2.2-t2i-plus": {"description": "Versión profesional Wanxiang 2.2, el modelo más reciente. Mejora integral en creatividad, estabilidad y realismo, con generación de detalles ricos."}, "wanx-v1": {"description": "Modelo base de generación de imágenes a partir de texto, correspondiente al modelo general 1.0 del sitio oficial <PERSON>."}, "wanx2.0-t2i-turbo": {"description": "Especializado en retratos con textura, velocidad media y bajo costo. Corresponde al modelo ultra rápido 2.0 del sitio oficial <PERSON>."}, "wanx2.1-t2i-plus": {"description": "Versión completamente mejorada. Genera imágenes con detalles más ricos, velocidad ligeramente más lenta. Corresponde al modelo profesional 2.1 del sitio oficial Tongyi <PERSON>."}, "wanx2.1-t2i-turbo": {"description": "Versión completamente mejorada. Generación rápida, resultados completos y alta relación calidad-precio. Corresponde al modelo ultra rápido 2.1 del sitio oficial Tong<PERSON>."}, "whisper-1": {"description": "Modelo universal de reconocimiento de voz que soporta reconocimiento de voz multilingüe, traducción de voz y detección de idioma."}, "wizardlm2": {"description": "WizardLM 2 es un modelo de lenguaje proporcionado por Microsoft AI, que destaca en diálogos complejos, multilingües, razonamiento y asistentes inteligentes."}, "wizardlm2:8x22b": {"description": "WizardLM 2 es un modelo de lenguaje proporcionado por Microsoft AI, que destaca en diálogos complejos, multilingües, razonamiento y asistentes inteligentes."}, "x1": {"description": "El modelo Spark X1 se actualizará aún más, logrando resultados en tareas generales como razonamiento, generación de texto y comprensión del lenguaje que se comparan con OpenAI o1 y DeepSeek R1, además de liderar en tareas matemáticas en el país."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 es una versión mejorada de Yi. Utiliza un corpus de alta calidad de 500B tokens para continuar el preentrenamiento de Yi y se微调 en 3M muestras de ajuste fino diversificadas."}, "yi-large": {"description": "Modelo de mil millones de parámetros completamente nuevo, que ofrece capacidades excepcionales de preguntas y respuestas y generación de texto."}, "yi-large-fc": {"description": "Basado en el modelo yi-large, soporta y refuerza la capacidad de llamadas a herramientas, adecuado para diversos escenarios de negocio que requieren la construcción de agentes o flujos de trabajo."}, "yi-large-preview": {"description": "Versión inicial, se recomienda usar yi-large (nueva versión)."}, "yi-large-rag": {"description": "Servicio de alto nivel basado en el modelo yi-large, combinando técnicas de recuperación y generación para proporcionar respuestas precisas y servicios de búsqueda de información en tiempo real."}, "yi-large-turbo": {"description": "Excelente relación calidad-precio y rendimiento excepcional. Ajuste de alta precisión basado en el rendimiento, velocidad de razonamiento y costo."}, "yi-lightning": {"description": "Último modelo de alto rendimiento que garantiza una salida de alta calidad y mejora significativamente la velocidad de razonamiento."}, "yi-lightning-lite": {"description": "Versión ligera, se recomienda usar yi-lightning."}, "yi-medium": {"description": "Modelo de tamaño mediano, ajustado y equilibrado, con una buena relación calidad-precio. Optimización profunda de la capacidad de seguimiento de instrucciones."}, "yi-medium-200k": {"description": "Ventana de contexto de 200K, que ofrece una profunda comprensión y generación de texto de largo formato."}, "yi-spark": {"description": "<PERSON>eque<PERSON> y ágil, modelo ligero y rápido. Ofrece capacidades mejoradas de cálculo matemático y escritura de código."}, "yi-vision": {"description": "Modelo para tareas visuales complejas, que ofrece un alto rendimiento en comprensión y análisis de imágenes."}, "yi-vision-v2": {"description": "Modelo para tareas visuales complejas, que ofrece capacidades de comprensión y análisis de alto rendimiento basadas en múltiples imágenes."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 es un modelo base diseñado para aplicaciones de agentes inteligentes, utilizando arquitectura Mixture-of-Experts (MoE). Está profundamente optimizado para llamadas a herramientas, navegación web, ingeniería de software y programación frontend, soportando integración fluida con agentes de código como Claude Code y Roo Code. GLM-4.5 emplea un modo de inferencia híbrido que se adapta a escenarios de razonamiento complejo y uso cotidiano."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air es un modelo base diseñado para aplicaciones de agentes inteligentes, utilizando arquitectura Mixture-of-Experts (MoE). Está profundamente optimizado para llamadas a herramientas, navegación web, ingeniería de software y programación frontend, soportando integración fluida con agentes de código como Claude Code y Roo Code. GLM-4.5 emplea un modo de inferencia híbrido que se adapta a escenarios de razonamiento complejo y uso cotidiano."}}