{"ai21": {"description": "AI21 Labsは企業向けに基盤モデルと人工知能システムを構築し、生成的人工知能の生産への応用を加速します。"}, "ai360": {"description": "360 AIは、360社が提供するAIモデルとサービスプラットフォームであり、360GPT2 Pro、360GPT Pro、360GPT Turbo、360GPT Turbo Responsibility 8Kなど、さまざまな先進的な自然言語処理モデルを提供しています。これらのモデルは、大規模なパラメータと多モーダル能力を組み合わせており、テキスト生成、意味理解、対話システム、コード生成などの分野で広く使用されています。柔軟な価格戦略を通じて、360 AIは多様なユーザーのニーズに応え、開発者の統合をサポートし、スマートアプリケーションの革新と発展を促進します。"}, "aihubmix": {"description": "AiHubMix は統一された API インターフェースを通じて、さまざまな AI モデルへのアクセスを提供します。"}, "anthropic": {"description": "Anthropicは、人工知能の研究と開発に特化した企業であり、Claude 3.5 Sonnet、Claude 3 Sonnet、Claude 3 Opus、Claude 3 Haikuなどの先進的な言語モデルを提供しています。これらのモデルは、知性、速度、コストの理想的なバランスを実現しており、企業向けのワークロードから迅速な応答が求められるさまざまなアプリケーションシーンに適しています。Claude 3.5 Sonnetは最新のモデルであり、複数の評価で優れたパフォーマンスを示し、高いコストパフォーマンスを維持しています。"}, "azure": {"description": "Azureは、GPT-3.5や最新のGPT-4シリーズを含む多様な先進AIモデルを提供し、さまざまなデータタイプや複雑なタスクをサポートし、安全で信頼性が高く持続可能なAIソリューションに取り組んでいます。"}, "azureai": {"description": "Azureは、GPT-3.5や最新のGPT-4シリーズを含む多様な先進的AIモデルを提供し、さまざまなデータタイプや複雑なタスクをサポートし、安全で信頼性が高く持続可能なAIソリューションに取り組んでいます。"}, "baichuan": {"description": "百川智能は、人工知能大モデルの研究開発に特化した企業であり、そのモデルは国内の知識百科、長文処理、生成創作などの中国語タスクで卓越したパフォーマンスを示し、海外の主流モデルを超えています。百川智能は、業界をリードする多モーダル能力を持ち、複数の権威ある評価で優れたパフォーマンスを示しています。そのモデルには、Baichuan 4、Baichuan 3 Turbo、Baichuan 3 Turbo 128kなどが含まれ、異なるアプリケーションシーンに最適化され、高コストパフォーマンスのソリューションを提供しています。"}, "bedrock": {"description": "Bedrockは、Amazon AWSが提供するサービスで、企業に先進的なAI言語モデルと視覚モデルを提供することに特化しています。そのモデルファミリーには、AnthropicのClaudeシリーズやMetaのLlama 3.1シリーズなどが含まれ、軽量から高性能までのさまざまな選択肢を提供し、テキスト生成、対話、画像処理などの多様なタスクをサポートし、異なる規模とニーズの企業アプリケーションに適しています。"}, "cloudflare": {"description": "Cloudflareのグローバルネットワーク上で、サーバーレスGPUによって駆動される機械学習モデルを実行します。"}, "cohere": {"description": "Cohereは、最先端の多言語モデル、高度な検索機能、そして現代企業向けにカスタマイズされたAIワークスペースを提供します。すべてが安全なプラットフォームに統合されています。"}, "deepseek": {"description": "DeepSeekは、人工知能技術の研究と応用に特化した企業であり、最新のモデルDeepSeek-V2.5は、汎用対話とコード処理能力を融合させ、人間の好みの整合、ライティングタスク、指示の遵守などの面で顕著な向上を実現しています。"}, "fal": {"description": "開発者向けの生成型メディアプラットフォーム"}, "fireworksai": {"description": "Fireworks AIは、先進的な言語モデルサービスのリーダーであり、機能呼び出しと多モーダル処理に特化しています。最新のモデルFirefunction V2はLlama-3に基づいており、関数呼び出し、対話、指示の遵守に最適化されています。視覚言語モデルFireLLaVA-13Bは、画像とテキストの混合入力をサポートしています。他の注目すべきモデルには、LlamaシリーズやMixtralシリーズがあり、高効率の多言語指示遵守と生成サポートを提供しています。"}, "giteeai": {"description": "Gitee AIのServerless APIは、AI開発者に開梱即使用の大モデル推論APIサービスを提供する。"}, "github": {"description": "GitHubモデルを使用することで、開発者はAIエンジニアになり、業界をリードするAIモデルを使って構築できます。"}, "google": {"description": "GoogleのGeminiシリーズは、Google DeepMindによって開発された最先端で汎用的なAIモデルであり、多モーダル設計に特化しており、テキスト、コード、画像、音声、動画のシームレスな理解と処理をサポートします。データセンターからモバイルデバイスまでのさまざまな環境に適しており、AIモデルの効率と適用範囲を大幅に向上させています。"}, "groq": {"description": "GroqのLPU推論エンジンは、最新の独立した大規模言語モデル（LLM）ベンチマークテストで卓越したパフォーマンスを示し、その驚異的な速度と効率でAIソリューションの基準を再定義しています。Groqは、即時推論速度の代表であり、クラウドベースの展開で良好なパフォーマンスを発揮しています。"}, "higress": {"description": "Higressは、阿里内部でTengineのリロードが長期接続のビジネスに悪影響を及ぼすことや、gRPC/Dubboの負荷分散能力が不足している問題を解決するために生まれた、クラウドネイティブなAPIゲートウェイです。"}, "huggingface": {"description": "HuggingFace Inference APIは、数千のモデルをさまざまなタスクに対して探索するための迅速かつ無料の方法を提供します。新しいアプリケーションのプロトタイプを作成している場合でも、機械学習の機能を試している場合でも、このAPIは複数の分野の高性能モデルに即座にアクセスできるようにします。"}, "hunyuan": {"description": "テンセントが開発した大規模言語モデルであり、強力な中国語の創作能力、複雑な文脈における論理的推論能力、そして信頼性の高いタスク実行能力を備えています。"}, "infiniai": {"description": "アプリケーション開発者向けに、高性能、使いやすさ、セキュリティを兼ね備えた大規模モデルサービスを提供し、大規模モデルの開発からサービス展開までの全プロセスをカバーします。"}, "internlm": {"description": "大規模モデルの研究と開発ツールチェーンに特化したオープンソース組織です。すべてのAI開発者に対して、高効率で使いやすいオープンプラットフォームを提供し、最先端の大規模モデルとアルゴリズム技術を身近に感じられるようにします。"}, "jina": {"description": "Jina AIは2020年に設立され、検索AIのリーディングカンパニーです。私たちの検索基盤プラットフォームには、ベクトルモデル、リランキングモデル、小型言語モデルが含まれており、企業が信頼性が高く高品質な生成AIおよびマルチモーダル検索アプリケーションを構築するのを支援します。"}, "lmstudio": {"description": "LM Studioは、あなたのコンピュータ上でLLMを開発し、実験するためのデスクトップアプリケーションです。"}, "minimax": {"description": "MiniMaxは2021年に設立された汎用人工知能テクノロジー企業であり、ユーザーと共に知能を共創することに取り組んでいます。MiniMaxは、さまざまなモードの汎用大モデルを独自に開発しており、トリリオンパラメータのMoEテキスト大モデル、音声大モデル、画像大モデルを含んでいます。また、海螺AIなどのアプリケーションも展開しています。"}, "mistral": {"description": "Mistralは、先進的な汎用、専門、研究型モデルを提供し、複雑な推論、多言語タスク、コード生成などの分野で広く使用されています。機能呼び出しインターフェースを通じて、ユーザーはカスタム機能を統合し、特定のアプリケーションを実現できます。"}, "modelscope": {"description": "ModelScopeはアリババクラウドが提供するモデル・アズ・ア・サービスプラットフォームで、豊富なAIモデルと推論サービスを提供しています。"}, "moonshot": {"description": "Moonshotは、北京月之暗面科技有限公司が提供するオープンプラットフォームであり、さまざまな自然言語処理モデルを提供し、コンテンツ創作、学術研究、スマート推薦、医療診断などの広範な応用分野を持ち、長文処理や複雑な生成タスクをサポートしています。"}, "novita": {"description": "Novita AIは、さまざまな大規模言語モデルとAI画像生成のAPIサービスを提供するプラットフォームであり、柔軟で信頼性が高く、コスト効率に優れています。Llama3、Mistralなどの最新のオープンソースモデルをサポートし、生成的AIアプリケーションの開発に向けた包括的でユーザーフレンドリーかつ自動スケーリングのAPIソリューションを提供し、AIスタートアップの急成長を支援します。"}, "nvidia": {"description": "NVIDIA NIM™は、自己ホスティングのGPU加速推論マイクロサービスに使用できるコンテナを提供し、クラウド、データセンター、RTX™ AIパーソナルコンピュータ、ワークステーション上で事前トレーニング済みおよびカスタムAIモデルを展開することをサポートします。"}, "ollama": {"description": "Ollamaが提供するモデルは、コード生成、数学演算、多言語処理、対話インタラクションなどの分野を広くカバーし、企業向けおよびローカライズされた展開の多様なニーズに対応しています。"}, "openai": {"description": "OpenAIは、世界をリードする人工知能研究機関であり、GPTシリーズなどのモデルを開発し、自然言語処理の最前線を推進しています。OpenAIは、革新と効率的なAIソリューションを通じて、さまざまな業界を変革することに取り組んでいます。彼らの製品は、顕著な性能と経済性を持ち、研究、ビジネス、革新アプリケーションで広く使用されています。"}, "openrouter": {"description": "OpenRouterは、OpenAI、Anthropic、LLaMAなどのさまざまな最先端の大規模モデルインターフェースを提供するサービスプラットフォームであり、多様な開発と応用のニーズに適しています。ユーザーは、自身のニーズに応じて最適なモデルと価格を柔軟に選択し、AI体験の向上を支援します。"}, "perplexity": {"description": "Perplexityは、先進的な対話生成モデルの提供者であり、さまざまなLlama 3.1モデルを提供し、オンラインおよびオフラインアプリケーションをサポートし、特に複雑な自然言語処理タスクに適しています。"}, "ppio": {"description": "PPIO パイオ云は、安定した高コストパフォーマンスのオープンソースモデル API サービスを提供し、DeepSeek の全シリーズ、Llama、Qwen などの業界をリードする大規模モデルをサポートしています。"}, "qiniu": {"description": "<PERSON>iuは、老舗のクラウドサービスプロバイダーであり、高品質で安価なリアルタイムおよびバッチAI推論サービスを提供し、シンプルな使い方を提供します。"}, "qwen": {"description": "通義千問は、アリババクラウドが独自に開発した超大規模言語モデルであり、強力な自然言語理解と生成能力を持っています。さまざまな質問に答えたり、文章を創作したり、意見を表現したり、コードを執筆したりすることができ、さまざまな分野で活躍しています。"}, "sambanova": {"description": "SambaNova Cloudは、開発者が最高のオープンソースモデルを簡単に利用でき、最速の推論速度を享受できるようにします。"}, "search1api": {"description": "Search1APIは、必要に応じて接続可能なDeepSeekシリーズモデルへのアクセスを提供します。標準版と高速版があり、さまざまなパラメータスケールのモデル選択をサポートしています。"}, "sensenova": {"description": "商湯日日新は、商湯の強力な基盤支援に基づき、高効率で使いやすい全スタックの大規模モデルサービスを提供します。"}, "siliconcloud": {"description": "SiliconFlowは、AGIを加速させ、人類に利益をもたらすことを目指し、使いやすくコスト効率の高いGenAIスタックを通じて大規模AIの効率を向上させることに取り組んでいます。"}, "spark": {"description": "科大訊飛星火大モデルは、多分野、多言語の強力なAI能力を提供し、先進的な自然言語処理技術を利用して、スマートハードウェア、スマート医療、スマート金融などのさまざまな垂直シーンに適した革新的なアプリケーションを構築します。"}, "stepfun": {"description": "階級星辰大モデルは、業界をリードする多モーダルおよび複雑な推論能力を備え、超長文の理解と強力な自律的検索エンジン機能をサポートしています。"}, "taichu": {"description": "中科院自動化研究所と武漢人工知能研究院が新世代の多モーダル大モデルを発表し、多輪問答、テキスト創作、画像生成、3D理解、信号分析などの包括的な問答タスクをサポートし、より強力な認知、理解、創作能力を持ち、新しいインタラクティブな体験を提供します。"}, "tencentcloud": {"description": "知識エンジン原子能力（LLM Knowledge Engine Atomic Power）は、知識エンジンに基づいて開発された知識問答の全体的な能力であり、企業や開発者向けに、柔軟にモデルアプリケーションを構築・開発する能力を提供します。複数の原子能力を使用して、専用のモデルサービスを構築し、文書解析、分割、埋め込み、多段階の書き換えなどのサービスを組み合わせて、企業専用のAIビジネスをカスタマイズできます。"}, "togetherai": {"description": "Together AIは、革新的なAIモデルを通じて先進的な性能を実現することに取り組んでおり、迅速なスケーリングサポートや直感的な展開プロセスを含む広範なカスタマイズ能力を提供し、企業のさまざまなニーズに応えています。"}, "upstage": {"description": "Upstageは、さまざまなビジネスニーズに応じたAIモデルの開発に特化しており、Solar LLMや文書AIを含み、人造一般知能（AGI）の実現を目指しています。Chat APIを通じてシンプルな対話エージェントを作成し、機能呼び出し、翻訳、埋め込み、特定分野のアプリケーションをサポートします。"}, "v0": {"description": "v0 はペアプログラミングアシスタントです。自然言語でアイデアを説明するだけで、プロジェクトのコードやユーザーインターフェース（UI）を生成します。"}, "vertexai": {"description": "GoogleのGeminiシリーズは、Google DeepMindによって開発された最先端の汎用AIモデルであり、マルチモーダル設計に特化しています。テキスト、コード、画像、音声、動画のシームレスな理解と処理をサポートし、データセンターからモバイルデバイスまでのさまざまな環境で使用できます。AIモデルの効率と適用範囲を大幅に向上させます。"}, "vllm": {"description": "vLLMは、LLM推論とサービスのための迅速で使いやすいライブラリです。"}, "volcengine": {"description": "バイトダンスが提供する大規模モデルサービスの開発プラットフォームで、機能が豊富で安全性が高く、価格競争力のあるモデル呼び出しサービスを提供します。また、モデルデータ、ファインチューニング、推論、評価などのエンドツーエンド機能を提供し、AIアプリケーションの開発を全面的にサポートします。"}, "wenxin": {"description": "企業向けのワンストップ大規模モデルとAIネイティブアプリケーションの開発およびサービスプラットフォームで、最も包括的で使いやすい生成的人工知能モデルの開発とアプリケーション開発の全プロセスツールチェーンを提供します。"}, "xai": {"description": "xAIは、人類の科学的発見を加速するための人工知能を構築することに専念している企業です。私たちの使命は、宇宙に対する共通の理解を促進することです。"}, "xinference": {"description": "Xorbits Inference（Xinference）は、様々なAIモデルの実行と統合を簡素化するためのオープンソースプラットフォームです。Xinferenceを利用することで、オープンソースのLLM、埋め込みモデル、マルチモーダルモデルをクラウドまたはオンプレミス環境で実行し、強力なAIアプリケーションを構築することができます。"}, "zeroone": {"description": "01.AIは、AI 2.0時代の人工知能技術に特化し、「人+人工知能」の革新と応用を推進し、超強力なモデルと先進的なAI技術を用いて人類の生産性を向上させ、技術の力を実現します。"}, "zhipu": {"description": "智谱AIは、多モーダルおよび言語モデルのオープンプラットフォームを提供し、テキスト処理、画像理解、プログラミング支援など、幅広いAIアプリケーションシーンをサポートしています。"}}