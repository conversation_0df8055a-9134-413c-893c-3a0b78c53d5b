{"01-ai/yi-1.5-34b-chat": {"description": "零一万物、最新のオープンソース微調整モデル、340億パラメータ、微調整は多様な対話シーンをサポートし、高品質なトレーニングデータで人間の好みに合わせています。"}, "01-ai/yi-1.5-9b-chat": {"description": "零一万物、最新のオープンソース微調整モデル、90億パラメータ、微調整は多様な対話シーンをサポートし、高品質なトレーニングデータで人間の好みに合わせています。"}, "360/deepseek-r1": {"description": "【360デプロイ版】DeepSeek-R1は、後訓練段階で大規模に強化学習技術を使用し、わずかなラベル付きデータでモデルの推論能力を大幅に向上させました。数学、コード、自然言語推論などのタスクで、OpenAI o1正式版に匹敵する性能を持っています。"}, "360gpt-pro": {"description": "360GPT Proは360 AIモデルシリーズの重要なメンバーであり、高効率なテキスト処理能力を持ち、多様な自然言語アプリケーションシーンに対応し、長文理解や多輪対話などの機能をサポートします。"}, "360gpt-pro-trans": {"description": "翻訳専用モデルで、深く微調整されており、翻訳効果が優れています。"}, "360gpt-turbo": {"description": "360GPT Turboは強力な計算と対話能力を提供し、優れた意味理解と生成効率を備え、企業や開発者にとって理想的なインテリジェントアシスタントソリューションです。"}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8Kは意味の安全性と責任指向を強調し、コンテンツの安全性に高い要求を持つアプリケーションシーンのために設計されており、ユーザー体験の正確性と堅牢性を確保します。"}, "360gpt2-o1": {"description": "360gpt2-o1は、ツリーサーチを使用して思考の連鎖を構築し、反省メカニズムを導入し、強化学習で訓練されたモデルであり、自己反省と誤り訂正の能力を備えています。"}, "360gpt2-pro": {"description": "360GPT2 Proは360社が発表した高級自然言語処理モデルで、卓越したテキスト生成と理解能力を備え、特に生成と創作の分野で優れたパフォーマンスを発揮し、複雑な言語変換や役割演技タスクを処理できます。"}, "360zhinao2-o1": {"description": "360zhinao2-o1は、木探索を使用して思考の連鎖を構築し、反省メカニズムを導入し、強化学習で訓練され、自己反省と誤り訂正の能力を備えています。"}, "4.0Ultra": {"description": "Spark4.0 Ultraは星火大モデルシリーズの中で最も強力なバージョンで、ネットワーク検索のリンクをアップグレードし、テキストコンテンツの理解と要約能力を向上させています。これは、オフィスの生産性を向上させ、要求に正確に応えるための全方位のソリューションであり、業界をリードするインテリジェントな製品です。"}, "AnimeSharp": {"description": "AnimeSharp（別名「4x‑AnimeSharp」）は、Kim2091がESRGANアーキテクチャを基に開発したオープンソースの超解像モデルで、アニメスタイルの画像の拡大とシャープ化に特化しています。2022年2月に「4x-TextSharpV1」から改名され、元々は文字画像にも対応していましたが、アニメコンテンツ向けに大幅に性能が最適化されています。"}, "Baichuan2-Turbo": {"description": "検索強化技術を採用し、大モデルと分野知識、全網知識の全面的なリンクを実現しています。PDF、Wordなどのさまざまな文書のアップロードやURL入力をサポートし、情報取得が迅速かつ包括的で、出力結果は正確かつ専門的です。"}, "Baichuan3-Turbo": {"description": "企業の高頻度シーンに最適化され、効果が大幅に向上し、高コストパフォーマンスを実現しています。Baichuan2モデルに対して、コンテンツ生成が20%、知識問答が17%、役割演技能力が40%向上しています。全体的な効果はGPT3.5よりも優れています。"}, "Baichuan3-Turbo-128k": {"description": "128Kの超長コンテキストウィンドウを備え、企業の高頻度シーンに最適化され、効果が大幅に向上し、高コストパフォーマンスを実現しています。Baichuan2モデルに対して、コンテンツ生成が20%、知識問答が17%、役割演技能力が40%向上しています。全体的な効果はGPT3.5よりも優れています。"}, "Baichuan4": {"description": "モデル能力は国内でトップであり、知識百科、長文、生成創作などの中国語タスクで海外の主流モデルを超えています。また、業界をリードするマルチモーダル能力を備え、複数の権威ある評価基準で優れたパフォーマンスを示しています。"}, "Baichuan4-Air": {"description": "モデル能力は国内で第一であり、知識百科、長文、生成創作などの中国語タスクで海外の主流モデルを超えています。また、業界をリードするマルチモーダル能力を持ち、多くの権威ある評価基準で優れたパフォーマンスを示しています。"}, "Baichuan4-Turbo": {"description": "モデル能力は国内で第一であり、知識百科、長文、生成創作などの中国語タスクで海外の主流モデルを超えています。また、業界をリードするマルチモーダル能力を持ち、多くの権威ある評価基準で優れたパフォーマンスを示しています。"}, "DeepSeek-R1": {"description": "最先端の効率的なLLMで、推論、数学、プログラミングに優れています。"}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1——DeepSeekスイートの中でより大きく、より賢いモデル——がLlama 70Bアーキテクチャに蒸留されました。ベンチマークテストと人間の評価に基づき、このモデルは元のLlama 70Bよりも賢く、特に数学と事実の正確性が求められるタスクで優れた性能を発揮します。"}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Qwen2.5-Math-1.5Bに基づくDeepSeek-R1蒸留モデルで、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新しました。"}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Qwen2.5-14Bに基づくDeepSeek-R1蒸留モデルで、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新しました。"}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1シリーズは、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新し、OpenAI-o1-miniのレベルを超えました。"}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Qwen2.5-Math-7Bに基づくDeepSeek-R1蒸留モデルで、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新しました。"}, "DeepSeek-V3": {"description": "DeepSeek-V3は、深度求索社が独自に開発したMoEモデルです。DeepSeek-V3は、Qwen2.5-72BやLlama-3.1-405Bなどの他のオープンソースモデルを超える評価成績を収め、性能面では世界トップクラスのクローズドソースモデルであるGPT-4oやClaude-3.5-Sonnetと肩を並べています。"}, "Doubao-lite-128k": {"description": "Doubao-liteは極めて高速な応答速度と優れたコストパフォーマンスを備え、さまざまなシナリオに柔軟な選択肢を提供します。128kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "Doubao-lite-32k": {"description": "Doubao-liteは極めて高速な応答速度と優れたコストパフォーマンスを備え、さまざまなシナリオに柔軟な選択肢を提供します。32kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "Doubao-lite-4k": {"description": "Doubao-liteは極めて高速な応答速度と優れたコストパフォーマンスを備え、さまざまなシナリオに柔軟な選択肢を提供します。4kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "Doubao-pro-128k": {"description": "最も高性能な主力モデルで、複雑なタスクの処理に適しています。参考質問応答、要約、創作、テキスト分類、ロールプレイなどのシーンで優れた効果を発揮します。128kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "Doubao-pro-32k": {"description": "最も高性能な主力モデルで、複雑なタスクの処理に適しています。参考質問応答、要約、創作、テキスト分類、ロールプレイなどのシーンで優れた効果を発揮します。32kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "Doubao-pro-4k": {"description": "最も高性能な主力モデルで、複雑なタスクの処理に適しています。参考質問応答、要約、創作、テキスト分類、ロールプレイなどのシーンで優れた効果を発揮します。4kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "DreamO": {"description": "DreamOは、ByteDanceと北京大学が共同開発したオープンソースの画像カスタマイズ生成モデルで、統一されたアーキテクチャにより多様なタスクの画像生成をサポートします。効率的な組み合わせモデリング手法を採用し、ユーザーが指定したアイデンティティ、主体、スタイル、背景など複数の条件に基づき、高度に一貫性のあるカスタマイズ画像を生成可能です。"}, "ERNIE-3.5-128K": {"description": "百度が独自に開発したフラッグシップの大規模言語モデルで、膨大な中英語のコーパスをカバーし、強力な汎用能力を持っています。ほとんどの対話型質問応答、創作生成、プラグインアプリケーションの要件を満たすことができます。また、百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ERNIE-3.5-8K": {"description": "百度が独自に開発したフラッグシップの大規模言語モデルで、膨大な中英語のコーパスをカバーし、強力な汎用能力を持っています。ほとんどの対話型質問応答、創作生成、プラグインアプリケーションの要件を満たすことができます。また、百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ERNIE-3.5-8K-Preview": {"description": "百度が独自に開発したフラッグシップの大規模言語モデルで、膨大な中英語のコーパスをカバーし、強力な汎用能力を持っています。ほとんどの対話型質問応答、創作生成、プラグインアプリケーションの要件を満たすことができます。また、百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ERNIE-4.0-8K-Latest": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、ERNIE 3.5に比べてモデル能力が全面的にアップグレードされ、さまざまな分野の複雑なタスクシナリオに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ERNIE-4.0-8K-Preview": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、ERNIE 3.5に比べてモデル能力が全面的にアップグレードされ、さまざまな分野の複雑なタスクシナリオに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "百度が自主開発したフラッグシップの超大規模な言語モデルで、総合的なパフォーマンスが優れており、各分野の複雑なタスクシナリオに広く適応します；百度検索プラグインとの自動連携をサポートし、質問応答情報のタイムリーさを保証します。ERNIE 4.0に比べてパフォーマンスが向上しています。"}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、総合的なパフォーマンスが優れており、さまざまな分野の複雑なタスクシナリオに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。ERNIE 4.0に比べてパフォーマンスがさらに優れています。"}, "ERNIE-Character-8K": {"description": "百度が独自に開発した垂直シナリオ向けの大規模言語モデルで、ゲームのNPC、カスタマーサービスの対話、対話型キャラクターの役割演技などのアプリケーションシナリオに適しており、キャラクターのスタイルがより鮮明で一貫性があり、指示に従う能力が強化され、推論性能が向上しています。"}, "ERNIE-Lite-Pro-128K": {"description": "百度が独自に開発した軽量大規模言語モデルで、優れたモデル効果と推論性能を兼ね備え、ERNIE Liteよりも効果が優れており、低計算能力のAIアクセラレータカードでの推論使用に適しています。"}, "ERNIE-Speed-128K": {"description": "百度が2024年に最新リリースした独自開発の高性能大規模言語モデルで、汎用能力が優れており、基盤モデルとして微調整に適しており、特定のシナリオの問題をより良く処理し、優れた推論性能を持っています。"}, "ERNIE-Speed-Pro-128K": {"description": "百度が2024年に最新リリースした独自開発の高性能大規模言語モデルで、汎用能力が優れており、ERNIE Speedよりも効果が優れており、基盤モデルとして微調整に適しており、特定のシナリオの問題をより良く処理し、優れた推論性能を持っています。"}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-devはBlack Forest Labsが開発した、Rectified Flow Transformerアーキテクチャに基づくマルチモーダル画像生成・編集モデルで、120億パラメータ規模を持ち、与えられたコンテキスト条件下で画像の生成、再構築、強化、編集に特化しています。本モデルは拡散モデルの制御可能な生成能力とTransformerのコンテキストモデリング能力を融合し、高品質な画像出力を実現。画像修復、画像補完、視覚シーン再構築など幅広いタスクに適用可能です。"}, "FLUX.1-dev": {"description": "FLUX.1-devはBlack Forest Labsが開発したオープンソースのマルチモーダル言語モデル（Multimodal Language Model, MLLM）で、画像と言語の理解と生成能力を融合し、画像と言語のタスクに最適化されています。先進的な大規模言語モデル（例：Mistral-7B）を基盤に、精巧に設計された視覚エンコーダーと多段階の指示微調整を通じて、画像と言語の協調処理と複雑なタスク推論能力を実現しています。"}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B)は、革新的なモデルであり、多分野のアプリケーションや複雑なタスクに適しています。"}, "HelloMeme": {"description": "HelloMemeは、提供された画像や動作に基づいて自動的にミーム画像、GIF、短い動画を生成するAIツールです。絵画やプログラミングの知識は不要で、参考画像を用意するだけで、見栄えが良く面白く、スタイルが一貫したコンテンツを作成できます。"}, "HiDream-I1-Full": {"description": "HiDream-E1-Fullは智象未来（HiDream.ai）が提供するオープンソースのマルチモーダル画像編集大規模モデルで、先進的なDiffusion Transformerアーキテクチャを基盤に、強力な言語理解能力（内蔵LLaMA 3.1-8B-Instruct）を組み合わせています。自然言語指示による画像生成、スタイル転送、局所編集、内容の再描画をサポートし、優れた画像と言語の理解と実行能力を備えています。"}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilledは軽量化されたテキストから画像生成モデルで、蒸留による最適化が施されており、高品質な画像を迅速に生成可能です。特にリソースが限られた環境やリアルタイム生成タスクに適しています。"}, "InstantCharacter": {"description": "InstantCharacterはTencent AIチームが2025年にリリースした、微調整不要（tuning-free）のパーソナライズキャラクター生成モデルで、高忠実度かつクロスシーンで一貫したキャラクター生成を目指しています。単一の参照画像のみでキャラクターをモデリングし、そのキャラクターを多様なスタイル、動作、背景に柔軟に適用可能です。"}, "InternVL2-8B": {"description": "InternVL2-8Bは、強力な視覚言語モデルで、画像とテキストのマルチモーダル処理をサポートし、画像内容を正確に認識し、関連する説明や回答を生成することができます。"}, "InternVL2.5-26B": {"description": "InternVL2.5-26Bは、強力な視覚言語モデルで、画像とテキストのマルチモーダル処理をサポートし、画像内容を正確に認識し、関連する説明や回答を生成することができます。"}, "Kolors": {"description": "KolorsはKuaishouのKolorsチームが開発したテキストから画像生成モデルで、数十億のパラメータで訓練されており、視覚品質、中国語の意味理解、テキストレンダリングにおいて顕著な優位性を持ちます。"}, "Kwai-Kolors/Kolors": {"description": "KolorsはKuaishouのKolorsチームが開発した潜在拡散に基づく大規模テキストから画像生成モデルです。数十億のテキスト・画像ペアで訓練され、視覚品質、複雑な意味の正確性、中英文字のレンダリングに優れています。中英両言語の入力をサポートし、中国語特有の内容の理解と生成においても高い性能を発揮します。"}, "Llama-3.2-11B-Vision-Instruct": {"description": "高解像度画像で優れた画像推論能力を発揮し、視覚理解アプリケーションに適しています。"}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "視覚理解エージェントアプリケーションに適した高度な画像推論能力を備えています。"}, "Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1の指示調整されたテキストモデルで、多言語対話のユースケースに最適化されており、多くの利用可能なオープンソースおよびクローズドチャットモデルの中で、一般的な業界ベンチマークで優れた性能を発揮します。"}, "Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1の指示調整されたテキストモデルで、多言語対話のユースケースに最適化されており、多くの利用可能なオープンソースおよびクローズドチャットモデルの中で、一般的な業界ベンチマークで優れた性能を発揮します。"}, "Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1の指示調整されたテキストモデルで、多言語対話のユースケースに最適化されており、多くの利用可能なオープンソースおよびクローズドチャットモデルの中で、一般的な業界ベンチマークで優れた性能を発揮します。"}, "Meta-Llama-3.2-1B-Instruct": {"description": "最先端の小型言語モデルで、言語理解、優れた推論能力、テキスト生成能力を備えています。"}, "Meta-Llama-3.2-3B-Instruct": {"description": "最先端の小型言語モデルで、言語理解、優れた推論能力、テキスト生成能力を備えています。"}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3は、Llamaシリーズの最先端の多言語オープンソース大規模言語モデルで、非常に低コストで405Bモデルに匹敵する性能を体験できます。Transformer構造に基づき、監視付き微調整（SFT）と人間のフィードバックによる強化学習（RLHF）を通じて有用性と安全性を向上させています。その指示調整バージョンは多言語対話に最適化されており、さまざまな業界のベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回る性能を発揮します。知識のカットオフ日は2023年12月です。"}, "MiniMax-M1": {"description": "新たに自社開発された推論モデル。世界最先端：80Kの思考チェーン×1Mの入力で、海外のトップモデルに匹敵する性能を実現。"}, "MiniMax-Text-01": {"description": "MiniMax-01シリーズモデルでは、大胆な革新を行いました：初めて大規模に線形注意メカニズムを実現し、従来のTransformerアーキテクチャが唯一の選択肢ではなくなりました。このモデルのパラメータ数は4560億に達し、単回のアクティベーションは459億です。モデルの総合性能は海外のトップモデルに匹敵し、世界最長の400万トークンのコンテキストを効率的に処理でき、GPT-4oの32倍、Claude-3.5-Sonnetの20倍です。"}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1はオープンソースの重みを持つ大規模混合注意力推論モデルで、4560億のパラメータを有し、各トークンで約459億のパラメータが活性化されます。モデルは100万トークンの超長文コンテキストをネイティブにサポートし、ライトニングアテンション機構により10万トークンの生成タスクでDeepSeek R1と比べて75％の浮動小数点演算量を削減します。また、MiniMax-M1はMoE（混合エキスパート）アーキテクチャを採用し、CISPOアルゴリズムと混合注意力設計による効率的な強化学習トレーニングを組み合わせ、長文入力推論および実際のソフトウェア工学シナリオで業界最高の性能を実現しています。"}, "Moonshot-Kimi-K2-Instruct": {"description": "総パラメータ数1兆、活性化パラメータ320億。非思考モデルの中で、先端知識、数学、コーディングにおいてトップレベルの性能を持ち、汎用エージェントタスクに優れています。エージェントタスクに特化して最適化されており、質問に答えるだけでなく行動も可能です。即興的で汎用的なチャットやエージェント体験に最適で、長時間の思考を必要としない反射的モデルです。"}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B)は、高精度の指示モデルであり、複雑な計算に適しています。"}, "OmniConsistency": {"description": "OmniConsistencyは大規模なDiffusion Transformers（DiTs）とペアスタイル化データを導入することで、画像から画像へのタスクにおけるスタイルの一貫性と汎化能力を向上させ、スタイルの劣化を防止します。"}, "Phi-3-medium-128k-instruct": {"description": "同じPhi-3-mediumモデルですが、RAGまたは少数ショットプロンプティング用により大きなコンテキストサイズを持っています。"}, "Phi-3-medium-4k-instruct": {"description": "14Bパラメータのモデルで、Phi-3-miniよりも高品質で、質の高い推論密度のデータに焦点を当てています。"}, "Phi-3-mini-128k-instruct": {"description": "同じPhi-3-miniモデルですが、RAGまたは少数ショットプロンプティング用により大きなコンテキストサイズを持っています。"}, "Phi-3-mini-4k-instruct": {"description": "Phi-3ファミリーの最小メンバー。品質と低遅延の両方に最適化されています。"}, "Phi-3-small-128k-instruct": {"description": "同じPhi-3-smallモデルですが、RAGまたは少数ショットプロンプティング用により大きなコンテキストサイズを持っています。"}, "Phi-3-small-8k-instruct": {"description": "7Bパラメータのモデルで、Phi-3-miniよりも高品質で、質の高い推論密度のデータに焦点を当てています。"}, "Phi-3.5-mini-instruct": {"description": "Phi-3-miniモデルの更新版です。"}, "Phi-3.5-vision-instrust": {"description": "Phi-3-visionモデルの更新版です。"}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-InstructはQwen2シリーズの指示微調整大規模言語モデルで、パラメータ規模は7Bです。このモデルはTransformerアーキテクチャに基づき、SwiGLU活性化関数、注意QKVバイアス、グループクエリ注意などの技術を採用しています。大規模な入力を処理することができます。このモデルは言語理解、生成、多言語能力、コーディング、数学、推論などの複数のベンチマークテストで優れたパフォーマンスを示し、ほとんどのオープンソースモデルを超え、特定のタスクでは専有モデルと同等の競争力を示しています。Qwen2-7B-Instructは多くの評価でQwen1.5-7B-Chatを上回り、顕著な性能向上を示しています。"}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-InstructはAlibaba Cloudが発表した最新の大規模言語モデルシリーズの一つです。この7Bモデルはコーディングや数学などの分野で顕著な能力の改善を持っています。このモデルは29以上の言語をカバーする多言語サポートも提供しており、中国語、英語などが含まれています。モデルは指示の遵守、構造化データの理解、特にJSONのような構造化出力の生成において顕著な向上を示しています。"}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-InstructはAlibaba Cloudが発表したコード特化型大規模言語モデルシリーズの最新バージョンです。このモデルはQwen2.5を基に、55兆トークンの訓練を通じて、コード生成、推論、修正能力を大幅に向上させました。コーディング能力を強化するだけでなく、数学および一般的な能力の利点も維持しています。このモデルはコードエージェントなどの実際のアプリケーションに対して、より包括的な基盤を提供します。"}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VLはQwenシリーズの新メンバーで、強力な視覚理解能力を備えています。画像内のテキスト、チャート、レイアウトを分析でき、長い動画の理解やイベントの捕捉が可能です。推論やツール操作が行え、多様な形式の物体位置特定や構造化された出力生成をサポートします。動画理解のための動的解像度とフレームレートのトレーニングが最適化され、視覚エンコーダーの効率も向上しています。"}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking は、智譜AIと清華大学KEG研究室が共同で発表したオープンソースの視覚言語モデル（VLM）であり、複雑なマルチモーダル認知タスクの処理に特化して設計されています。本モデルはGLM-4-9B-0414の基礎モデルをベースに、「思考の連鎖（Chain-of-Thought）」推論メカニズムを導入し、強化学習戦略を採用することで、マルチモーダル間の推論能力と安定性を大幅に向上させています。"}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chatは智譜AIが提供するGLM-4シリーズの事前訓練モデルのオープンバージョンです。このモデルは意味、数学、推論、コード、知識などの複数の側面で優れたパフォーマンスを示します。多輪対話をサポートするだけでなく、GLM-4-9B-Chatはウェブブラウジング、コード実行、カスタムツール呼び出し（Function Call）、長文推論などの高度な機能も備えています。モデルは中国語、英語、日本語、韓国語、ドイツ語など26の言語をサポートしています。多くのベンチマークテストで、GLM-4-9B-Chatは優れた性能を示し、AlignBench-v2、MT-Bench、MMLU、C-Evalなどでの評価が行われています。このモデルは最大128Kのコンテキスト長をサポートし、学術研究や商業アプリケーションに適しています。"}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1は、強化学習（RL）駆動の推論モデルで、モデル内の繰り返しと可読性の問題を解決します。RLの前に、DeepSeek-R1はコールドスタートデータを導入し、推論性能をさらに最適化しました。数学、コード、推論タスクにおいてOpenAI-o1と同等の性能を発揮し、精巧に設計されたトレーニング手法によって全体的な効果を向上させています。"}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B は、Qwen2.5-Math-7B を基に知識蒸留によって得られたモデルです。このモデルは、DeepSeek-R1 によって生成された80万の精選されたサンプルを使用して微調整されており、優れた推論能力を発揮します。複数のベンチマークテストで優れた性能を示し、MATH-500では92.8%の精度、AIME 2024では55.5%の合格率、CodeForcesでは1189のスコアを達成し、7B規模のモデルとして強力な数学およびプログラミング能力を実証しています。"}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3は、6710億パラメータを持つ混合専門家（MoE）言語モデルで、多頭潜在注意力（MLA）とDeepSeekMoEアーキテクチャを採用し、無補助損失の負荷バランス戦略を組み合わせて推論とトレーニングの効率を最適化しています。14.8兆の高品質トークンで事前トレーニングを行い、監視付き微調整と強化学習を経て、DeepSeek-V3は他のオープンソースモデルを超え、先進的なクローズドモデルに近づいています。"}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2は超強力なコードおよびエージェント能力を持つMoEアーキテクチャの基盤モデルで、総パラメータ数1兆、活性化パラメータ320億です。汎用知識推論、プログラミング、数学、エージェントなど主要カテゴリのベンチマーク性能で他の主流オープンソースモデルを上回っています。"}, "QwQ-32B-Preview": {"description": "QwQ-32B-Previewは、複雑な対話生成と文脈理解タスクを効率的に処理できる革新的な自然言語処理モデルです。"}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Previewは、Qwenチームによって開発された視覚推論能力に特化した研究モデルであり、複雑なシーン理解と視覚関連の数学問題を解決する上で独自の利点を持っています。"}, "Qwen/QwQ-32B": {"description": "QwQはQwenシリーズの推論モデルです。従来の指示調整モデルと比較して、QwQは思考と推論能力を備えており、特に困難な問題を解決する際に、下流タスクでのパフォーマンスを大幅に向上させることができます。QwQ-32Bは中型の推論モデルであり、最先端の推論モデル（DeepSeek-R1、o1-miniなど）との比較において競争力のあるパフォーマンスを発揮します。このモデルはRoPE、SwiGLU、RMSNorm、Attention QKVバイアスなどの技術を採用しており、64層のネットワーク構造と40のQアテンションヘッド（GQAアーキテクチャではKVは8個）を持っています。"}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-PreviewはQwenの最新の実験的研究モデルで、AIの推論能力を向上させることに特化しています。言語の混合、再帰的推論などの複雑なメカニズムを探求することで、主な利点は強力な推論分析能力、数学およびプログラミング能力です。同時に、言語切り替えの問題、推論のループ、安全性の考慮、その他の能力の違いも存在します。"}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2は、先進的な汎用言語モデルであり、さまざまな指示タイプをサポートします。"}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-InstructはQwen2シリーズの指示微調整大規模言語モデルで、パラメータ規模は72Bです。このモデルはTransformerアーキテクチャに基づき、SwiGLU活性化関数、注意QKVバイアス、グループクエリ注意などの技術を採用しています。大規模な入力を処理することができます。このモデルは言語理解、生成、多言語能力、コーディング、数学、推論などの複数のベンチマークテストで優れたパフォーマンスを示し、ほとんどのオープンソースモデルを超え、特定のタスクでは専有モデルと同等の競争力を示しています。"}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VLはQwen-VLモデルの最新のイテレーションで、視覚理解のベンチマークテストで最先端の性能を達成しました。"}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5は、新しい大型言語モデルシリーズで、指示型タスクの処理を最適化することを目的としています。"}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5は、新しい大型言語モデルシリーズで、指示型タスクの処理を最適化することを目的としています。"}, "Qwen/Qwen2.5-72B-Instruct": {"description": "アリババクラウドの通義千問チームが開発した大規模言語モデル"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5は新しい大型言語モデルシリーズで、より強力な理解と生成能力を持っています。"}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5は新しい大型言語モデルシリーズで、指示タスクの処理を最適化することを目的としています。"}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5は、新しい大型言語モデルシリーズで、指示型タスクの処理を最適化することを目的としています。"}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5は新しい大型言語モデルシリーズで、指示タスクの処理を最適化することを目的としています。"}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coderはコード作成に特化しています。"}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-InstructはAlibaba Cloudが発表したコード特化型大規模言語モデルシリーズの最新バージョンです。このモデルはQwen2.5を基に、55兆トークンの訓練を通じて、コード生成、推論、修正能力を大幅に向上させました。コーディング能力を強化するだけでなく、数学および一般的な能力の利点も維持しています。このモデルはコードエージェントなどの実際のアプリケーションに対して、より包括的な基盤を提供します。"}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instructは、通義千問チームが開発したマルチモーダル大規模言語モデルで、Qwen2.5-VLシリーズの一部です。このモデルは一般的な物体認識に優れるだけでなく、画像内のテキスト、チャート、アイコン、グラフィック、レイアウトの分析も可能です。視覚エージェントとして機能し、推論と動的なツール操作が可能で、コンピュータやスマートフォンの操作能力を備えています。さらに、画像内のオブジェクトを正確に位置特定でき、請求書や表などの構造化された出力を生成します。前世代モデルであるQwen2-VLと比較して、強化学習による数学的思考力と問題解決能力が向上し、応答スタイルも人間の嗜好により適合しています。"}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VLはQwen2.5シリーズの視覚言語モデルです。このモデルは複数の面で大幅な改善が見られます：一般的な物体の認識、テキスト・図表・レイアウトの分析能力が強化された視覚理解能力を備えています；視覚エージェントとして推論を行い、ツール使用を動的に指導できます；1時間以上の長い動画を理解し、重要なイベントを捕捉することが可能です；境界ボックスやポイントを生成することで画像内の物体を正確に位置特定できます；特に請求書や表などのスキャンデータに適した構造化出力の生成をサポートしています。"}, "Qwen/Qwen3-14B": {"description": "Qwen3は、能力が大幅に向上した新世代の通義千問大モデルであり、推論、一般、エージェント、多言語などの複数のコア能力で業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3は、能力が大幅に向上した新世代の通義千問大モデルであり、推論、一般、エージェント、多言語などの複数のコア能力で業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3シリーズのフラッグシップ混合専門家（MoE）大規模言語モデルで、Alibaba Cloud Tongyi Qianwenチームが開発。総パラメータ2350億、推論時に220億パラメータを活性化します。Qwen3-235B-A22Bの非思考モードのアップデート版で、指示遵守、論理推論、テキスト理解、数学、科学、プログラミング、ツール使用などの汎用能力が大幅に向上。多言語の長尾知識カバーを強化し、主観的かつオープンなタスクにおけるユーザーの好みにより良く整合し、より有用で高品質なテキスト生成を実現します。"}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3シリーズの大型言語モデルの一つで、Alibaba Tongyi Qianwenチームが開発。複雑な推論タスクに特化し、混合専門家（MoE）アーキテクチャを採用。総パラメータ2350億、トークンごとに約220億パラメータを活性化し、計算効率を高めつつ強力な性能を維持。論理推論、数学、科学、プログラミング、学術ベンチマークなど専門知識を要するタスクで顕著な性能向上を示し、オープンソースの思考モデルの中でトップレベル。指示遵守、ツール使用、テキスト生成などの汎用能力も強化し、256Kの長文コンテキスト理解をネイティブにサポート。深い推論や長文処理が必要なシナリオに最適です。"}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3は、能力が大幅に向上した新世代の通義千問大モデルであり、推論、一般、エージェント、多言語などの複数のコア能力で業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507は、Qwen3-30B-A3Bの非思考モードのアップデート版です。これは総パラメータ数305億、活性化パラメータ数33億の混合エキスパート（MoE）モデルです。本モデルは指示遵守、論理推論、テキスト理解、数学、科学、コーディング、ツール使用などの汎用能力を大幅に強化しました。また、多言語のロングテール知識カバレッジに実質的な進展を遂げ、主観的かつオープンなタスクにおけるユーザーの好みにより良く適合し、より有用な応答と高品質なテキストを生成できます。さらに、本モデルの長文理解能力は256Kにまで強化されています。本モデルは非思考モードのみをサポートし、出力に`<think></think>`タグは生成されません。"}, "Qwen/Qwen3-32B": {"description": "Qwen3は、能力が大幅に向上した新世代の通義千問大モデルであり、推論、一般、エージェント、多言語などの複数のコア能力で業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "Qwen/Qwen3-8B": {"description": "Qwen3は、能力が大幅に向上した新世代の通義千問大モデルであり、推論、一般、エージェント、多言語などの複数のコア能力で業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "Qwen2-72B-Instruct": {"description": "Qwen2はQwenモデルの最新シリーズで、128kのコンテキストをサポートしています。現在の最適なオープンソースモデルと比較して、Qwen2-72Bは自然言語理解、知識、コード、数学、そして多言語などの能力において、現在のリーディングモデルを大幅に上回っています。"}, "Qwen2-7B-Instruct": {"description": "Qwen2はQwenモデルの最新シリーズで、同等の規模の最適なオープンソースモデルやそれ以上の規模のモデルを超えることができ、Qwen2 7Bは複数の評価で顕著な優位性を示し、特にコードと中国語理解において優れています。"}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72Bは、強力な視覚言語モデルであり、画像とテキストのマルチモーダル処理をサポートし、画像の内容を正確に認識し、関連する説明や回答を生成できます。"}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instructは、140億パラメータの大規模言語モデルで、優れたパフォーマンスを発揮し、中国語と多言語シーンを最適化し、インテリジェントQ&A、コンテンツ生成などのアプリケーションをサポートします。"}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instructは、320億パラメータの大規模言語モデルで、パフォーマンスが均衡しており、中国語と多言語シーンを最適化し、インテリジェントQ&A、コンテンツ生成などのアプリケーションをサポートします。"}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instructは、16kのコンテキストをサポートし、8Kを超える長文を生成します。関数呼び出しと外部システムとのシームレスなインタラクションをサポートし、柔軟性と拡張性を大幅に向上させました。モデルの知識は明らかに増加し、コーディングと数学の能力が大幅に向上し、29以上の言語をサポートしています。"}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instructは、70億パラメータの大規模言語モデルで、関数呼び出しと外部システムとのシームレスなインタラクションをサポートし、柔軟性と拡張性を大幅に向上させます。中国語と多言語シーンを最適化し、インテリジェントQ&A、コンテンツ生成などのアプリケーションをサポートします。"}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instructは、大規模な事前学習に基づくプログラミング指示モデルであり、強力なコード理解と生成能力を持ち、さまざまなプログラミングタスクを効率的に処理でき、特にスマートコード作成、自動化スクリプト生成、プログラミング問題の解決に適しています。"}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instructは、コード生成、コード理解、効率的な開発シーンのために設計された大規模言語モデルで、業界をリードする32Bパラメータ規模を採用しており、多様なプログラミングニーズに応えます。"}, "Qwen3-235B": {"description": "Qwen3-235B-A22BはMoE（混合エキスパートモデル）で、「混合推論モード」を導入し、ユーザーが「思考モード」と「非思考モード」をシームレスに切り替え可能です。119言語と方言の理解・推論をサポートし、強力なツール呼び出し能力を備えています。総合能力、コード・数学、多言語能力、知識・推論などの複数のベンチマークで、DeepSeek R1、OpenAI o1、o3-mini、Grok 3、Google Gemini 2.5 Proなどの主要な大規模モデルと競合可能です。"}, "Qwen3-32B": {"description": "Qwen3-32Bは密モデル（Dense Model）で、「混合推論モード」を導入し、ユーザーが「思考モード」と「非思考モード」をシームレスに切り替え可能です。モデルアーキテクチャの改良、トレーニングデータの増加、より効率的なトレーニング手法により、全体的な性能はQwen2.5-72Bと同等の水準に達しています。"}, "SenseChat": {"description": "基本バージョンのモデル (V4)、4Kのコンテキスト長で、汎用能力が強力です。"}, "SenseChat-128K": {"description": "基本バージョンのモデル (V4)、128Kのコンテキスト長で、長文理解や生成などのタスクで優れたパフォーマンスを発揮します。"}, "SenseChat-32K": {"description": "基本バージョンのモデル (V4)、32Kのコンテキスト長で、さまざまなシーンに柔軟に適用できます。"}, "SenseChat-5": {"description": "最新バージョンのモデル (V5.5)、128Kのコンテキスト長で、数学的推論、英語の対話、指示のフォロー、長文理解などの分野での能力が大幅に向上し、GPT-4oに匹敵します。"}, "SenseChat-5-1202": {"description": "V5.5をベースにした最新バージョンで、前バージョンに比べて中英語の基礎能力、チャット、理系知識、文系知識、ライティング、数理論理、文字数制御など複数の面で顕著に向上しています。"}, "SenseChat-5-Cantonese": {"description": "32Kのコンテキスト長で、広東語の対話理解においてGPT-4を超え、知識、推論、数学、コード作成などの複数の分野でGPT-4 Turboに匹敵します。"}, "SenseChat-5-beta": {"description": "一部の性能が SenseCat-5-1202 を上回っています"}, "SenseChat-Character": {"description": "スタンダード版モデル、8Kのコンテキスト長で、高速な応答速度を持っています。"}, "SenseChat-Character-Pro": {"description": "ハイエンド版モデル、32Kのコンテキスト長で、能力が全面的に向上し、中国語/英語の対話をサポートしています。"}, "SenseChat-Turbo": {"description": "迅速な質問応答やモデルの微調整シーンに適しています。"}, "SenseChat-Turbo-1202": {"description": "最新の軽量バージョンモデルで、フルモデルの90%以上の能力を達成し、推論コストを大幅に削減しています。"}, "SenseChat-Vision": {"description": "最新バージョンモデル (V5.5) で、複数の画像入力をサポートし、モデルの基本能力の最適化を全面的に実現し、オブジェクト属性認識、空間関係、動作イベント認識、シーン理解、感情認識、論理常識推論、テキスト理解生成において大幅な向上を実現しました。"}, "SenseNova-V6-5-Pro": {"description": "多モーダル、言語、推論データの包括的な更新とトレーニング戦略の最適化により、新モデルは多モーダル推論と汎用指示遵守能力で顕著な向上を実現しました。最大128kのコンテキストウィンドウをサポートし、OCRや文化観光IP認識などの専門タスクで卓越した性能を発揮します。"}, "SenseNova-V6-5-Turbo": {"description": "多モーダル、言語、推論データの包括的な更新とトレーニング戦略の最適化により、新モデルは多モーダル推論と汎用指示遵守能力で顕著な向上を実現しました。最大128kのコンテキストウィンドウをサポートし、OCRや文化観光IP認識などの専門タスクで卓越した性能を発揮します。"}, "SenseNova-V6-Pro": {"description": "画像、テキスト、動画の能力をネイティブに統一し、従来のマルチモーダルの分立的制限を突破し、OpenCompassとSuperCLUEの評価でダブルチャンピオンを獲得しました。"}, "SenseNova-V6-Reasoner": {"description": "視覚と言語の深い推論を兼ね備え、ゆっくりとした思考と深い推論を実現し、完全な思考の連鎖過程を提示します。"}, "SenseNova-V6-Turbo": {"description": "画像、テキスト、動画の能力をネイティブに統一し、従来のマルチモーダルの分立的制限を突破し、マルチモーダルの基礎能力や言語の基礎能力などのコア次元で全面的にリードし、文理を兼ね備え、複数の評価で国内外の第一梯隊レベルに何度もランクインしています。"}, "Skylark2-lite-8k": {"description": "雲雀（Skylark）第2世代モデル、Skylark2-liteモデルは高い応答速度を持ち、リアルタイム性が求められ、コストに敏感で、モデルの精度要求がそれほど高くないシーンに適しています。コンテキストウィンドウ長は8kです。"}, "Skylark2-pro-32k": {"description": "雲雀（Skylark）第2世代モデル、Skylark2-proバージョンは高いモデル精度を持ち、専門分野の文書生成、小説創作、高品質翻訳などの複雑なテキスト生成シーンに適しています。コンテキストウィンドウ長は32kです。"}, "Skylark2-pro-4k": {"description": "雲雀（Skylark）第2世代モデル、Skylark2-proモデルは高いモデル精度を持ち、専門分野の文書生成、小説創作、高品質翻訳などの複雑なテキスト生成シーンに適しています。コンテキストウィンドウ長は4kです。"}, "Skylark2-pro-character-4k": {"description": "雲雀（Skylark）第2世代モデル、Skylark2-pro-characterモデルは、優れたロールプレイングとチャット能力を持ち、ユーザーのプロンプト要件に基づいて異なるキャラクターを演じながらチャットを行うのが得意です。キャラクターのスタイルが際立ち、対話の内容は自然で流暢です。チャットボット、仮想アシスタント、オンラインカスタマーサービスなどのシーンに適しており、高速な応答を実現します。"}, "Skylark2-pro-turbo-8k": {"description": "雲雀（Skylark）第2世代モデル、Skylark2-pro-turbo-8kは、推論がより速く、コストが低く、コンテキストウィンドウ長は8kです。"}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414はGLMシリーズの新世代オープンソースモデルで、320億パラメータを持ちます。このモデルはOpenAIのGPTシリーズやDeepSeekのV3/R1シリーズと同等の性能を持っています。"}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414はGLMシリーズの小型モデルで、90億パラメータを持ちます。このモデルはGLM-4-32Bシリーズの技術的特徴を継承しつつ、より軽量なデプロイメントオプションを提供します。規模は小さいものの、GLM-4-9B-0414はコード生成、ウェブデザイン、SVGグラフィック生成、検索ベースの執筆などのタスクで優れた能力を示しています。"}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking は、智譜AIと清華大学KEG研究室が共同で発表したオープンソースの視覚言語モデル（VLM）であり、複雑なマルチモーダル認知タスクの処理に特化して設計されています。本モデルはGLM-4-9B-0414の基礎モデルをベースに、「思考の連鎖（Chain-of-Thought）」推論メカニズムを導入し、強化学習戦略を採用することで、マルチモーダル間の推論能力と安定性を大幅に向上させています。"}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414は深い思考能力を持つ推論モデルです。このモデルはGLM-4-32B-0414に基づき、コールドスタートと拡張強化学習を通じて開発され、数学、コード、論理タスクにおいてさらに訓練されています。基礎モデルと比較して、GLM-Z1-32B-0414は数学能力と複雑なタスクの解決能力を大幅に向上させています。"}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414はGLMシリーズの小型モデルで、90億パラメータを持ち、オープンソースの伝統を維持しつつ驚くべき能力を示しています。規模は小さいものの、このモデルは数学推論や一般的なタスクで優れたパフォーマンスを発揮し、同等の規模のオープンソースモデルの中でリーダーシップを発揮しています。"}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414は深い推論能力を持つモデルで（OpenAIのDeep Researchに対抗）、典型的な深い思考モデルとは異なり、より長い時間の深い思考を用いてよりオープンで複雑な問題を解決します。"}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9Bはオープンソース版で、会話アプリケーションに最適化された対話体験を提供します。"}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32Bは強化学習で訓練された初の長文コンテキスト大型推論モデル（LRM）で、長文推論タスクに特化して最適化されています。段階的なコンテキスト拡張強化学習フレームワークにより、短文から長文への安定した移行を実現。7つの長文ドキュメントQAベンチマークでOpenAI-o3-miniやQwen3-235B-A22Bなどのフラッグシップモデルを上回り、Claude-3.7-Sonnet-Thinkingに匹敵する性能を示します。数学推論、論理推論、多段推論などの複雑なタスクに特に優れています。"}, "Yi-34B-Chat": {"description": "Yi-1.5-34Bは、元のシリーズモデルの優れた汎用言語能力を維持しつつ、5000億の高品質トークンを増分トレーニングすることで、数学的論理とコーディング能力を大幅に向上させました。"}, "abab5.5-chat": {"description": "生産性シーン向けであり、複雑なタスク処理と効率的なテキスト生成をサポートし、専門分野のアプリケーションに適しています。"}, "abab5.5s-chat": {"description": "中国語のキャラクター対話シーンに特化しており、高品質な中国語対話生成能力を提供し、さまざまなアプリケーションシーンに適しています。"}, "abab6.5g-chat": {"description": "多言語のキャラクター対話に特化しており、英語および他の多くの言語の高品質な対話生成をサポートします。"}, "abab6.5s-chat": {"description": "テキスト生成、対話システムなど、幅広い自然言語処理タスクに適しています。"}, "abab6.5t-chat": {"description": "中国語のキャラクター対話シーンに最適化されており、流暢で中国語の表現習慣に合った対話生成能力を提供します。"}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1は、強化学習とコールドスタートデータの最適化を経た最先端の大規模言語モデルで、優れた推論、数学、プログラミング性能を持っています。"}, "accounts/fireworks/models/deepseek-v3": {"description": "Deepseekが提供する強力なMixture-of-Experts (MoE)言語モデルで、総パラメータ数は671Bであり、各トークンは37Bのパラメータを活性化します。"}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Llama 3 70B指示モデルは、多言語対話と自然言語理解に最適化されており、ほとんどの競合モデルを上回る性能を持っています。"}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Llama 3 8B指示モデルは、対話や多言語タスクに最適化されており、卓越した効率を発揮します。"}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Llama 3 8B指示モデル（HFバージョン）は、公式実装結果と一致し、高い一貫性とクロスプラットフォーム互換性を持っています。"}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Llama 3.1 405B指示モデルは、超大規模なパラメータを持ち、複雑なタスクや高負荷シナリオでの指示フォローに適しています。"}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Llama 3.1 70B指示モデルは、卓越した自然言語理解と生成能力を提供し、対話や分析タスクに理想的な選択肢です。"}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Llama 3.1 8B指示モデルは、多言語対話の最適化のために設計されており、一般的な業界ベンチマークを超える性能を発揮します。"}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Metaの11Bパラメータ指示調整画像推論モデルです。このモデルは視覚認識、画像推論、画像説明、および画像に関する一般的な質問への回答に最適化されています。このモデルは、グラフや図表などの視覚データを理解し、画像の詳細をテキストで記述することで、視覚と言語の間のギャップを埋めることができます。"}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B指示モデルはMetaが発表した軽量な多言語モデルです。このモデルは効率を向上させることを目的としており、より大規模なモデルと比較して遅延とコストの面で大きな改善を提供します。このモデルの使用例には、問い合わせやプロンプトのリライト、執筆支援が含まれます。"}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Metaの90Bパラメータ指示調整画像推論モデルです。このモデルは視覚認識、画像推論、画像説明、および画像に関する一般的な質問への回答に最適化されています。このモデルは、グラフや図表などの視覚データを理解し、画像の詳細をテキストで記述することで、視覚と言語の間のギャップを埋めることができます。"}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instructは、Llama 3.1 70Bの12月の更新版です。このモデルは、2024年7月にリリースされたLlama 3.1 70Bを基に改良され、ツール呼び出し、多言語テキストサポート、数学およびプログラミング能力が強化されています。このモデルは、推論、数学、指示遵守の面で業界の最前線に達しており、3.1 405Bと同等の性能を提供しつつ、速度とコストにおいて顕著な利点を持っています。"}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "24Bパラメータモデルで、より大規模なモデルと同等の最先端の能力を備えています。"}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B指示モデルは、大規模なパラメータと多専門家アーキテクチャを持ち、複雑なタスクの高効率処理を全方位でサポートします。"}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B指示モデルは、多専門家アーキテクチャを提供し、高効率の指示フォローと実行をサポートします。"}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMax L2 13Bモデルは、新しい統合技術を組み合わせており、物語やキャラクターの役割に優れています。"}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision指示モデルは、軽量の多モーダルモデルであり、複雑な視覚とテキスト情報を処理でき、強力な推論能力を持っています。"}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "QwQモデルはQwenチームによって開発された実験的な研究モデルで、AIの推論能力を強化することに焦点を当てています。"}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Qwen-VLモデルの72Bバージョンは、アリババの最新のイテレーションの成果であり、近年の革新を代表しています。"}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5はAlibaba Cloud Qwenチームによって開発された一連のデコーダーのみを含む言語モデルです。これらのモデルは、0.5B、1.5B、3B、7B、14B、32B、72Bなど、さまざまなサイズを提供し、ベース版と指示版の2種類のバリエーションがあります。"}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B InstructはAlibaba Cloudが発表したコード特化型大規模言語モデルシリーズの最新バージョンです。このモデルはQwen2.5を基に、55兆トークンの訓練を通じて、コード生成、推論、修正能力を大幅に向上させました。コーディング能力を強化するだけでなく、数学および一般的な能力の利点も維持しています。このモデルはコードエージェントなどの実際のアプリケーションに対して、より包括的な基盤を提供します。"}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Largeモデルは、卓越した多言語処理能力を持ち、さまざまな言語生成と理解タスクに使用できます。"}, "ai21-jamba-1.5-large": {"description": "398Bパラメータ（94Bアクティブ）の多言語モデルで、256Kの長いコンテキストウィンドウ、関数呼び出し、構造化出力、基盤生成を提供します。"}, "ai21-jamba-1.5-mini": {"description": "52Bパラメータ（12Bアクティブ）の多言語モデルで、256Kの長いコンテキストウィンドウ、関数呼び出し、構造化出力、基盤生成を提供します。"}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "398Bパラメータ（うち94Bがアクティブ）の多言語モデルで、256Kの長いコンテキストウィンドウ、関数呼び出し、構造化出力、事実に基づく生成を提供します。"}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "52Bパラメータ（うち12Bがアクティブ）の多言語モデルで、256Kの長いコンテキストウィンドウ、関数呼び出し、構造化出力、事実に基づく生成を提供します。"}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnetは業界標準を向上させ、競合モデルやClaude 3 Opusを超える性能を持ち、広範な評価で優れたパフォーマンスを示し、私たちの中程度のモデルの速度とコストを兼ね備えています。"}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnetは業界標準を引き上げ、競合モデルやClaude 3 Opusを上回る性能を発揮し、広範な評価で優れた結果を示しています。また、中程度のレベルのモデルと同等の速度とコストを持っています。"}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 HaikuはAnthropicの最も速く、最もコンパクトなモデルで、ほぼ瞬時の応答速度を提供します。簡単なクエリやリクエストに迅速に回答できます。顧客は人間のインタラクションを模倣するシームレスなAI体験を構築できるようになります。Claude 3 Haikuは画像を処理し、テキスト出力を返すことができ、200Kのコンテキストウィンドウを持っています。"}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 OpusはAnthropicの最も強力なAIモデルで、高度に複雑なタスクにおいて最先端の性能を持っています。オープンエンドのプロンプトや未見のシナリオを処理でき、優れた流暢さと人間の理解能力を持っています。Claude 3 Opusは生成AIの可能性の最前線を示しています。Claude 3 Opusは画像を処理し、テキスト出力を返すことができ、200Kのコンテキストウィンドウを持っています。"}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "AnthropicのClaude 3 Sonnetは、知能と速度の理想的なバランスを実現しており、特に企業のワークロードに適しています。競合他社よりも低価格で最大の効用を提供し、信頼性が高く耐久性のある主力機として設計されており、スケール化されたAIデプロイメントに適しています。Claude 3 Sonnetは画像を処理し、テキスト出力を返すことができ、200Kのコンテキストウィンドウを持っています。"}, "anthropic.claude-instant-v1": {"description": "日常の対話、テキスト分析、要約、文書質問応答などの一連のタスクを処理できる、迅速で経済的かつ非常に能力のあるモデルです。"}, "anthropic.claude-v2": {"description": "Anthropicは、複雑な対話や創造的なコンテンツ生成から詳細な指示の遵守に至るまで、幅広いタスクで高い能力を発揮するモデルです。"}, "anthropic.claude-v2:1": {"description": "Claude 2の更新版で、コンテキストウィンドウが2倍になり、長文書やRAGコンテキストにおける信頼性、幻覚率、証拠に基づく正確性が改善されています。"}, "anthropic/claude-3-haiku": {"description": "Claude 3 HaikuはAnthropicの最も迅速でコンパクトなモデルで、ほぼ瞬時の応答を実現することを目的としています。迅速かつ正確な指向性能を備えています。"}, "anthropic/claude-3-opus": {"description": "Claude 3 Opusは、Anthropicが高度に複雑なタスクを処理するために開発した最も強力なモデルです。性能、知能、流暢さ、理解力において卓越したパフォーマンスを発揮します。"}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haikuは、Anthropicの最も高速な次世代モデルです。Claude 3 Haikuと比較して、Claude 3.5 Haikuはすべてのスキルで向上しており、多くの知能ベンチマークテストで前世代の最大モデルClaude 3 Opusを超えています。"}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 SonnetはOpusを超える能力を提供し、Sonnetよりも速い速度を持ちながら、Sonnetと同じ価格を維持します。Sonnetは特にプログラミング、データサイエンス、視覚処理、代理タスクに優れています。"}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnetは、Anthropicがこれまでに開発した最も知能の高いモデルであり、市場で初めての混合推論モデルです。Claude 3.7 Sonnetは、ほぼ瞬時の応答や段階的な思考を生成することができ、ユーザーはこれらのプロセスを明確に見ることができます。Sonnetは特にプログラミング、データサイエンス、視覚処理、代理タスクに優れています。"}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 は、Anthropic が高度に複雑なタスクを処理するために開発した最も強力なモデルです。性能、知能、流暢さ、理解力の面で卓越した能力を発揮します。"}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 はほぼ即時の応答や段階的な思考の延長を生成でき、ユーザーはこれらのプロセスを明確に確認できます。API ユーザーはモデルの思考時間を細かく制御することも可能です。"}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B は、720億パラメータ、160億アクティベーションパラメータのスパース大規模言語モデルであり、グループ化された混合エキスパート（MoGE）アーキテクチャに基づいています。エキスパート選択段階でエキスパートをグループ化し、各グループ内でトークンが均等にエキスパートをアクティベートするよう制約を設けることで、エキスパートの負荷バランスを実現し、昇騰プラットフォーム上でのモデル展開効率を大幅に向上させています。"}, "aya": {"description": "Aya 23は、Cohereが提供する多言語モデルであり、23の言語をサポートし、多様な言語アプリケーションを便利にします。"}, "aya:35b": {"description": "Aya 23は、Cohereが提供する多言語モデルであり、23の言語をサポートし、多様な言語アプリケーションを便利にします。"}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13Bは百川智能が開発した130億パラメータを持つオープンソースの商用大規模言語モデルで、権威ある中国語と英語のベンチマークで同サイズの中で最良の結果を達成しています。"}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B は、百度（Baidu）が開発した混合エキスパート（MoE）アーキテクチャに基づく大規模言語モデルです。総パラメータ数は3000億ですが、推論時には各トークンで470億パラメータのみをアクティベートし、強力な性能を維持しつつ計算効率も両立しています。ERNIE 4.5シリーズの中核モデルの一つとして、テキスト理解、生成、推論、プログラミングなどのタスクで卓越した能力を発揮します。本モデルは革新的なマルチモーダル異種MoE事前学習手法を採用し、テキストと視覚モダリティの共同学習により、特に指示遵守と世界知識の記憶において優れた効果を発揮しています。"}, "c4ai-aya-expanse-32b": {"description": "Aya Expanseは、高性能な32B多言語モデルで、指示調整、データアービトラージ、好みのトレーニング、モデル統合の革新を通じて、単一言語モデルのパフォーマンスに挑戦します。23の言語をサポートしています。"}, "c4ai-aya-expanse-8b": {"description": "Aya Expanseは、高性能な8B多言語モデルで、指示調整、データアービトラージ、好みのトレーニング、モデル統合の革新を通じて、単一言語モデルのパフォーマンスに挑戦します。23の言語をサポートしています。"}, "c4ai-aya-vision-32b": {"description": "Aya Visionは、最先端のマルチモーダルモデルで、言語、テキスト、画像能力の複数の重要なベンチマークで優れたパフォーマンスを発揮します。23の言語をサポートしています。この320億パラメータのバージョンは、最先端の多言語パフォーマンスに焦点を当てています。"}, "c4ai-aya-vision-8b": {"description": "Aya Visionは、最先端のマルチモーダルモデルで、言語、テキスト、画像能力の複数の重要なベンチマークで優れたパフォーマンスを発揮します。この80億パラメータのバージョンは、低遅延と最適なパフォーマンスに焦点を当てています。"}, "charglm-3": {"description": "CharGLM-3はキャラクター演技と感情的な伴侶のために設計されており、超長期の多段階記憶と個別化された対話をサポートし、幅広い用途に適しています。"}, "charglm-4": {"description": "CharGLM-4はキャラクター演技と感情的な伴侶のために設計されており、超長期の多回記憶と個別化された対話をサポートし、幅広い応用があります。"}, "chatglm3": {"description": "ChatGLM3は、智譜AIと清華KEGラボが公開したクローズドソースモデルで、大量の中国語と英語の識別子の事前学習と人間の好みの調整学習を経ています。1世代目のモデルと比較して、MMLU、C-Eval、GSM8Kでそれぞれ16%、36%、280%の向上を達成し、中国語タスクランキングC-Evalで1位を獲得しました。知識量、推論能力、創造性が求められる場面、例えば広告文の作成、小説の執筆、知識系の執筆、コードの生成などに適しています。"}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base は、智譜が開発した ChatGLM シリーズの最新世代の 60 億パラメータのオープンソースの基本モデルです。"}, "chatgpt-4o-latest": {"description": "ChatGPT-4oは、リアルタイムで更新される動的モデルで、常に最新のバージョンを維持します。強力な言語理解と生成能力を組み合わせており、顧客サービス、教育、技術サポートなどの大規模なアプリケーションシナリオに適しています。"}, "claude-2.0": {"description": "Claude 2は、業界をリードする200Kトークンのコンテキスト、モデルの幻覚の発生率を大幅に低下させる、システムプロンプト、および新しいテスト機能：ツール呼び出しを含む、企業にとって重要な能力の進歩を提供します。"}, "claude-2.1": {"description": "Claude 2は、業界をリードする200Kトークンのコンテキスト、モデルの幻覚の発生率を大幅に低下させる、システムプロンプト、および新しいテスト機能：ツール呼び出しを含む、企業にとって重要な能力の進歩を提供します。"}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haikuは、Anthropicの最も高速な次世代モデルです。Claude 3 Haikuと比較して、Claude 3.5 Haikuはすべてのスキルで向上しており、多くの知能ベンチマークテストで前の世代の最大モデルであるClaude 3 Opusを超えています。"}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnetは、Opusを超える能力とSonnetよりも速い速度を提供し、Sonnetと同じ価格を維持します。Sonnetは特にプログラミング、データサイエンス、視覚処理、エージェントタスクに優れています。"}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnetは、Opusを超える能力とSonnetよりも速い速度を提供しつつ、Sonnetと同じ価格を維持します。Sonnetは特にプログラミング、データサイエンス、視覚処理、代理タスクに優れています。"}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnetは、競合他社よりも低価格で最大の効用を提供し、信頼性が高く耐久性のある主力機として設計されています。スケール化されたAIデプロイメントに適しています。Claude 3.7 Sonnetは画像を処理し、テキスト出力を返すことができ、200Kのコンテキストウィンドウを持っています。"}, "claude-3-haiku-20240307": {"description": "Claude 3 Haikuは、Anthropicの最も速く、最もコンパクトなモデルであり、ほぼ瞬時の応答を実現することを目的としています。迅速かつ正確な指向性能を持っています。"}, "claude-3-opus-20240229": {"description": "Claude 3 Opusは、Anthropicが高度に複雑なタスクを処理するために開発した最も強力なモデルです。性能、知性、流暢さ、理解力において卓越したパフォーマンスを発揮します。"}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnetは、企業のワークロードに理想的なバランスを提供し、より低価格で最大の効用を提供し、信頼性が高く、大規模な展開に適しています。"}, "claude-opus-4-20250514": {"description": "Claude Opus 4は、Anthropicが高度に複雑なタスクを処理するために開発した最も強力なモデルです。性能、知性、流暢さ、理解力において卓越したパフォーマンスを発揮します。"}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnetは、ほぼ瞬時の応答や段階的な思考を生成でき、ユーザーはこれらのプロセスを明確に見ることができます。APIユーザーは、モデルの思考時間を詳細に制御することも可能です。"}, "codegeex-4": {"description": "CodeGeeX-4は強力なAIプログラミングアシスタントで、さまざまなプログラミング言語のインテリジェントな質問応答とコード補完をサポートし、開発効率を向上させます。"}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9Bは、多言語コード生成モデルで、コード補完と生成、コードインタープリター、ウェブ検索、関数呼び出し、リポジトリレベルのコードQ&Aを含む包括的な機能をサポートし、ソフトウェア開発のさまざまなシーンをカバーしています。パラメータが10B未満のトップクラスのコード生成モデルです。"}, "codegemma": {"description": "CodeGemmaは、さまざまなプログラミングタスクに特化した軽量言語モデルであり、迅速な反復と統合をサポートします。"}, "codegemma:2b": {"description": "CodeGemmaは、さまざまなプログラミングタスクに特化した軽量言語モデルであり、迅速な反復と統合をサポートします。"}, "codellama": {"description": "Code Llamaは、コード生成と議論に特化したLLMであり、広範なプログラミング言語のサポートを組み合わせて、開発者環境に適しています。"}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llamaはコード生成と議論に特化したLLMで、幅広いプログラミング言語のサポートを組み合わせて、開発者環境に適しています。"}, "codellama:13b": {"description": "Code Llamaは、コード生成と議論に特化したLLMであり、広範なプログラミング言語のサポートを組み合わせて、開発者環境に適しています。"}, "codellama:34b": {"description": "Code Llamaは、コード生成と議論に特化したLLMであり、広範なプログラミング言語のサポートを組み合わせて、開発者環境に適しています。"}, "codellama:70b": {"description": "Code Llamaは、コード生成と議論に特化したLLMであり、広範なプログラミング言語のサポートを組み合わせて、開発者環境に適しています。"}, "codeqwen": {"description": "CodeQwen1.5は、大量のコードデータでトレーニングされた大規模言語モデルであり、複雑なプログラミングタスクを解決するために特化しています。"}, "codestral": {"description": "Codestralは、Mistral AIの初のコードモデルであり、コード生成タスクに優れたサポートを提供します。"}, "codestral-latest": {"description": "Codestralは、コード生成に特化した最先端の生成モデルであり、中間埋め込みやコード補完タスクを最適化しています。"}, "codex-mini-latest": {"description": "codex-mini-latest は o4-mini の微調整バージョンで、Codex CLI 専用に設計されています。API を直接使用する場合は、gpt-4.1 から始めることを推奨します。"}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22Bは指示遵守、対話、プログラミングのために設計されたモデルです。"}, "cogview-4": {"description": "CogView-4 は智譜が初めて開発した漢字生成対応のオープンソーステキストから画像生成モデルであり、意味理解、画像生成の品質、中英文字生成能力の全方位的な向上を実現しています。任意の長さの中英バイリンガル入力に対応し、指定された範囲内で任意の解像度の画像を生成することが可能です。"}, "cohere-command-r": {"description": "Command Rは、RAGとツール使用をターゲットにしたスケーラブルな生成モデルで、企業向けの生産規模のAIを実現します。"}, "cohere-command-r-plus": {"description": "Command R+は、企業グレードのワークロードに対応するために設計された最先端のRAG最適化モデルです。"}, "cohere/Cohere-command-r": {"description": "Command RはRAGやツール使用に特化した拡張可能な生成モデルで、企業が生産レベルのAIを実現できるよう設計されています。"}, "cohere/Cohere-command-r-plus": {"description": "Command R+は最先端のRAG最適化モデルで、企業レベルのワークロードに対応することを目的としています。"}, "command": {"description": "指示に従う対話モデルで、言語タスクにおいて高品質で信頼性が高く、私たちの基本生成モデルよりも長いコンテキスト長を持っています。"}, "command-a-03-2025": {"description": "Command Aは、ツールの使用、エージェント、検索強化生成（RAG）、および多言語アプリケーションシナリオにおいて優れたパフォーマンスを発揮する、これまでで最も強力なモデルです。Command Aは256Kのコンテキスト長を持ち、2つのGPUで動作し、Command R+ 08-2024と比較してスループットが150%向上しています。"}, "command-light": {"description": "より小型で高速なCommandバージョンで、ほぼ同じ強力さを持ちながら、より速い速度を提供します。"}, "command-light-nightly": {"description": "主要なバージョンリリース間の時間間隔を短縮するために、Commandモデルのナイトリーバージョンをリリースしました。command-lightシリーズでは、このバージョンはcommand-light-nightlyと呼ばれます。command-light-nightlyは最新で最も実験的であり（おそらく）不安定なバージョンです。ナイトリーバージョンは定期的に更新され、事前通知なしにリリースされるため、プロダクション環境での使用は推奨されません。"}, "command-nightly": {"description": "主要なバージョンリリース間の時間間隔を短縮するために、Commandモデルのナイトリーバージョンをリリースしました。Commandシリーズでは、このバージョンはcommand-cightlyと呼ばれます。command-nightlyは最新で最も実験的であり（おそらく）不安定なバージョンです。ナイトリーバージョンは定期的に更新され、事前通知なしにリリースされるため、プロダクション環境での使用は推奨されません。"}, "command-r": {"description": "Command Rは、対話と長いコンテキストタスクに最適化されたLLMであり、特に動的なインタラクションと知識管理に適しています。"}, "command-r-03-2024": {"description": "Command Rは、指示に従う対話モデルで、言語タスクにおいてより高い品質と信頼性を提供し、従来のモデルよりも長いコンテキスト長を持っています。コード生成、検索強化生成（RAG）、ツール使用、エージェントなどの複雑なワークフローに使用できます。"}, "command-r-08-2024": {"description": "command-r-08-2024はCommand Rモデルの更新版で、2024年8月にリリースされました。"}, "command-r-plus": {"description": "Command R+は、リアルな企業シーンと複雑なアプリケーションのために設計された高性能な大規模言語モデルです。"}, "command-r-plus-04-2024": {"description": "Command R+は、指示に従う対話モデルで、言語タスクにおいてより高い品質と信頼性を提供し、従来のモデルよりも長いコンテキスト長を持っています。複雑なRAGワークフローや多段階ツール使用に最適です。"}, "command-r-plus-08-2024": {"description": "Command R+は指示に従う対話モデルで、言語タスクにおいてより高い品質と信頼性を示し、従来のモデルに比べてより長いコンテキスト長を持っています。複雑なRAGワークフローや多段階のツール使用に最適です。"}, "command-r7b-12-2024": {"description": "command-r7b-12-2024は、小型で効率的な更新版で、2024年12月にリリースされました。RAG、ツール使用、エージェントなど、複雑な推論と多段階処理を必要とするタスクで優れたパフォーマンスを発揮します。"}, "compound-beta": {"description": "Compound-betaは複合AIシステムで、GroqCloudでサポートされている複数のオープン利用可能なモデルによって支えられ、ユーザーのクエリに応じてツールを賢く選択的に使用します。"}, "compound-beta-mini": {"description": "Compound-beta-miniは複合AIシステムで、GroqCloudでサポートされている公開利用可能なモデルによって支えられ、ユーザーのクエリに応じてツールを賢く選択的に使用します。"}, "computer-use-preview": {"description": "computer-use-preview モデルは「コンピュータ使用ツール」専用に設計されたモデルで、コンピュータ関連のタスクを理解し実行するように訓練されています。"}, "dall-e-2": {"description": "第二世代DALL·Eモデル、よりリアルで正確な画像生成をサポートし、解像度は第一世代の4倍です"}, "dall-e-3": {"description": "最新のDALL·Eモデル、2023年11月にリリース。よりリアルで正確な画像生成をサポートし、詳細表現力が向上しています"}, "databricks/dbrx-instruct": {"description": "DBRX Instructは、高い信頼性の指示処理能力を提供し、多業界アプリケーションをサポートします。"}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1は、強化学習（RL）駆動の推論モデルであり、モデル内の繰り返しと可読性の問題を解決します。RLの前に、DeepSeek-R1はコールドスタートデータを導入し、推論性能をさらに最適化しました。数学、コード、推論タスクにおいてOpenAI-o1と同等のパフォーマンスを発揮し、精巧に設計されたトレーニング手法によって全体的な効果を向上させました。"}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1は、増強された計算資源と後訓練過程で導入されたアルゴリズム最適化機構を活用し、その推論および推断能力の深さを著しく向上させました。本モデルは数学、プログラミング、一般論理などの各種ベンチマーク評価で優れた成績を示し、全体性能はO3やGemini 2.5 Proなどの先進モデルに近づいています。"}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8BはDeepSeek-R1-0528モデルの思考連鎖をQwen3 8B Baseに蒸留して得られたモデルです。オープンソースモデル中で最先端（SOTA）の性能を達成し、AIME 2024テストでQwen3 8Bを10%上回り、Qwen3-235B-thinkingの性能レベルに達しています。数学推論、プログラミング、汎用論理など複数のベンチマークで優れた成績を示し、Qwen3-8Bと同じアーキテクチャながらDeepSeek-R1-0528のトークナイザー設定を共有しています。"}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1蒸留モデルで、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新しました。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1蒸留モデルで、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新しました。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1蒸留モデルで、強化学習とコールドスタートデータを通じて推論性能を最適化し、オープンソースモデルがマルチタスクの基準を刷新しました。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32Bは、Qwen2.5-32Bに基づいて知識蒸留によって得られたモデルです。このモデルは、DeepSeek-R1が生成した80万の選りすぐりのサンプルを使用して微調整され、数学、プログラミング、推論などの複数の分野で卓越した性能を示しています。AIME 2024、MATH-500、GPQA Diamondなどの複数のベンチマークテストで優れた成績を収めており、特にMATH-500では94.3%の正確性を達成し、強力な数学的推論能力を示しています。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7Bは、Qwen2.5-Math-7Bに基づいて知識蒸留によって得られたモデルです。このモデルは、DeepSeek-R1が生成した80万の選りすぐりのサンプルを使用して微調整され、優れた推論能力を示しています。複数のベンチマークテストで優れた成績を収めており、特にMATH-500では92.8%の正確性を達成し、AIME 2024では55.5%の合格率を達成し、CodeForcesでは1189のスコアを獲得し、7B規模のモデルとして強力な数学とプログラミング能力を示しています。"}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5は以前のバージョンの優れた特徴を集約し、汎用性とコーディング能力を強化しました。"}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3は、6710億パラメータを持つ混合専門家（MoE）言語モデルであり、多頭潜在注意（MLA）とDeepSeekMoEアーキテクチャを採用し、補助損失なしの負荷バランス戦略を組み合わせて、推論とトレーニングの効率を最適化します。14.8兆の高品質トークンで事前トレーニングを行い、監視微調整と強化学習を経て、DeepSeek-V3は他のオープンソースモデルを超え、先進的なクローズドソースモデルに近づきました。"}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67Bは、高い複雑性の対話のために訓練された先進的なモデルです。"}, "deepseek-ai/deepseek-r1": {"description": "最先端の効率的なLLMで、推論、数学、プログラミングに優れています。"}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2は、DeepSeekMoE-27Bに基づいて開発された混合専門家（MoE）視覚言語モデルであり、スパースアクティベーションのMoEアーキテクチャを採用し、わずか4.5Bパラメータを活性化することで卓越した性能を実現しています。このモデルは、視覚的質問応答、光学文字認識、文書/表/グラフ理解、視覚的定位などの複数のタスクで優れたパフォーマンスを発揮します。"}, "deepseek-chat": {"description": "一般的な対話能力と強力なコード処理能力を兼ね備えた新しいオープンソースモデルであり、元のChatモデルの対話能力とCoderモデルのコード処理能力を保持しつつ、人間の好みにより良く整合しています。さらに、DeepSeek-V2.5は、執筆タスクや指示に従う能力など、さまざまな面で大幅な向上を実現しました。"}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33Bは、2兆のデータを基にトレーニングされたコード言語モデルで、そのうち87%がコード、13%が中英語です。モデルは16Kのウィンドウサイズと穴埋めタスクを導入し、プロジェクトレベルのコード補完とスニペット埋め機能を提供します。"}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2は、オープンソースの混合エキスパートコードモデルであり、コードタスクにおいて優れた性能を発揮し、GPT4-Turboに匹敵します。"}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2は、オープンソースの混合エキスパートコードモデルであり、コードタスクにおいて優れた性能を発揮し、GPT4-Turboに匹敵します。"}, "deepseek-r1": {"description": "DeepSeek-R1は、強化学習（RL）駆動の推論モデルであり、モデル内の繰り返しと可読性の問題を解決します。RLの前に、DeepSeek-R1はコールドスタートデータを導入し、推論性能をさらに最適化しました。数学、コード、推論タスクにおいてOpenAI-o1と同等のパフォーマンスを発揮し、精巧に設計されたトレーニング手法によって全体的な効果を向上させました。"}, "deepseek-r1-0528": {"description": "685B フルスペックモデルで、2025年5月28日にリリースされました。DeepSeek-R1 は後期トレーニング段階で大規模に強化学習技術を活用し、極めて少ないラベル付きデータでモデルの推論能力を大幅に向上させました。数学、コード、自然言語推論などのタスクで高い性能と強力な能力を持ちます。"}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70Bファスト版で、リアルタイムのオンライン検索をサポートし、モデルのパフォーマンスを維持しながら、より速い応答速度を提供します。"}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70Bスタンダード版で、リアルタイムのオンライン検索をサポートし、最新情報が必要な対話やテキスト処理タスクに適しています。"}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama は、DeepSeek-R1 から Llama を蒸留したモデルです。"}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1——DeepSeekスイートの中でより大きく、より賢いモデル——がLlama 70Bアーキテクチャに蒸留されました。ベンチマークテストと人間評価に基づき、このモデルは元のLlama 70Bよりも賢く、特に数学と事実の正確性が求められるタスクで優れたパフォーマンスを示します。"}, "deepseek-r1-distill-llama-8b": {"description": "DeepSeek-R1-Distillシリーズモデルは、知識蒸留技術を通じて、DeepSeek-R1が生成したサンプルをQwen、Llamaなどのオープンソースモデルに微調整して得られたものです。"}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "2025年2月14日に初めてリリースされ、千帆大モデル開発チームがLlama3_70Bをベースモデル（Built with Meta Llama）として蒸留したもので、蒸留データには千帆のコーパスも追加されています。"}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "2025年2月14日に初めてリリースされ、千帆大モデル開発チームがLlama3_8Bをベースモデル（Built with Meta Llama）として蒸留したもので、蒸留データには千帆のコーパスも追加されています。"}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen は、Qwen をベースに DeepSeek-R1 から蒸留されたモデルです。"}, "deepseek-r1-distill-qwen-1.5b": {"description": "DeepSeek-R1-Distillシリーズモデルは、知識蒸留技術を通じて、DeepSeek-R1が生成したサンプルをQwen、Llamaなどのオープンソースモデルに微調整して得られたものです。"}, "deepseek-r1-distill-qwen-14b": {"description": "DeepSeek-R1-Distillシリーズモデルは、知識蒸留技術を通じて、DeepSeek-R1が生成したサンプルをQwen、Llamaなどのオープンソースモデルに微調整して得られたものです。"}, "deepseek-r1-distill-qwen-32b": {"description": "DeepSeek-R1-Distillシリーズモデルは、知識蒸留技術を通じて、DeepSeek-R1が生成したサンプルをQwen、Llamaなどのオープンソースモデルに微調整して得られたものです。"}, "deepseek-r1-distill-qwen-7b": {"description": "DeepSeek-R1-Distillシリーズモデルは、知識蒸留技術を通じて、DeepSeek-R1が生成したサンプルをQwen、Llamaなどのオープンソースモデルに微調整して得られたものです。"}, "deepseek-r1-fast-online": {"description": "DeepSeek R1フルファスト版で、リアルタイムのオンライン検索をサポートし、671Bパラメータの強力な能力とより速い応答速度を組み合わせています。"}, "deepseek-r1-online": {"description": "DeepSeek R1フルバージョンで、671Bパラメータを持ち、リアルタイムのオンライン検索をサポートし、より強力な理解と生成能力を備えています。"}, "deepseek-reasoner": {"description": "DeepSeekが提供する推論モデルです。最終的な回答を出力する前に、モデルは思考の連鎖を出力し、最終的な答えの正確性を高めます。"}, "deepseek-v2": {"description": "DeepSeek V2は、高効率なMixture-of-Experts言語モデルであり、経済的な処理ニーズに適しています。"}, "deepseek-v2:236b": {"description": "DeepSeek V2 236Bは、DeepSeekの設計コードモデルであり、強力なコード生成能力を提供します。"}, "deepseek-v3": {"description": "DeepSeek-V3は、杭州深度求索人工知能基礎技術研究有限公司が独自に開発したMoEモデルで、複数の評価で優れた成績を収め、主流のランキングでオープンソースモデルの首位に立っています。V3はV2.5モデルに比べて生成速度が3倍向上し、ユーザーにより迅速でスムーズな使用体験を提供します。"}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324は671BパラメータのMoEモデルであり、プログラミングと技術能力、文脈理解、長文処理において優れた性能を発揮します。"}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3は、685Bパラメータの専門的な混合モデルであり、DeepSeekチームのフラッグシップチャットモデルシリーズの最新のイテレーションです。\n\nこれは、[DeepSeek V3](/deepseek/deepseek-chat-v3)モデルを継承し、さまざまなタスクで優れたパフォーマンスを発揮します。"}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3は、685Bパラメータの専門的な混合モデルであり、DeepSeekチームのフラッグシップチャットモデルシリーズの最新のイテレーションです。\n\nこれは、[DeepSeek V3](/deepseek/deepseek-chat-v3)モデルを継承し、さまざまなタスクで優れたパフォーマンスを発揮します。"}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1は、わずかなラベル付きデータしかない状況で、モデルの推論能力を大幅に向上させました。最終的な回答を出力する前に、モデルは思考の連鎖を出力し、最終的な答えの正確性を向上させます。"}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1は極めて少ないラベル付きデータでモデルの推論能力を大幅に向上させました。最終回答を出力する前に、モデルは思考の連鎖を出力し、最終答えの正確性を高めます。"}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1は極めて少ないラベル付きデータでモデルの推論能力を大幅に向上させました。最終回答を出力する前に、モデルは思考の連鎖を出力し、最終答えの正確性を高めます。"}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70BはLlama3.3 70Bに基づく大規模言語モデルで、DeepSeek R1の出力を微調整に利用し、大規模な最前線モデルと同等の競争力のある性能を実現しています。"}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8BはLlama-3.1-8B-Instructに基づく蒸留大言語モデルで、DeepSeek R1の出力を使用してトレーニングされています。"}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14BはQwen 2.5 14Bに基づく蒸留大言語モデルで、DeepSeek R1の出力を使用してトレーニングされています。このモデルは複数のベンチマークテストでOpenAIのo1-miniを超え、密なモデル（dense models）の最新技術の成果を達成しました。以下は一部のベンチマークテストの結果です：\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nこのモデルはDeepSeek R1の出力から微調整を行い、より大規模な最前線モデルと同等の競争力のある性能を示しています。"}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32BはQwen 2.5 32Bに基づく蒸留大言語モデルで、DeepSeek R1の出力を使用してトレーニングされています。このモデルは複数のベンチマークテストでOpenAIのo1-miniを超え、密なモデル（dense models）の最新技術の成果を達成しました。以下は一部のベンチマークテストの結果です：\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nこのモデルはDeepSeek R1の出力から微調整を行い、より大規模な最前線モデルと同等の競争力のある性能を示しています。"}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1はDeepSeekチームが発表した最新のオープンソースモデルで、特に数学、プログラミング、推論タスクにおいてOpenAIのo1モデルと同等の推論性能を持っています。"}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1は、わずかなラベル付きデータしかない状況で、モデルの推論能力を大幅に向上させました。最終的な回答を出力する前に、モデルは思考の連鎖を出力し、最終的な答えの正確性を向上させます。"}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3は推論速度において前のモデルに比べて大きなブレークスルーを達成しました。オープンソースモデルの中で1位にランクインし、世界の最先端のクローズドモデルと肩を並べることができます。DeepSeek-V3はマルチヘッド潜在注意（MLA）とDeepSeekMoEアーキテクチャを採用しており、これらのアーキテクチャはDeepSeek-V2で完全に検証されています。さらに、DeepSeek-V3は負荷分散のための補助的な非損失戦略を開発し、より強力な性能を得るためにマルチラベル予測トレーニング目標を設定しました。"}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3は推論速度において前のモデルに比べて大きなブレークスルーを達成しました。オープンソースモデルの中で1位にランクインし、世界の最先端のクローズドモデルと肩を並べることができます。DeepSeek-V3はマルチヘッド潜在注意（MLA）とDeepSeekMoEアーキテクチャを採用しており、これらのアーキテクチャはDeepSeek-V2で完全に検証されています。さらに、DeepSeek-V3は負荷分散のための補助的な非損失戦略を開発し、より強力な性能を得るためにマルチラベル予測トレーニング目標を設定しました。"}, "deepseek_r1": {"description": "DeepSeek-R1は強化学習（RL）駆動の推論モデルで、モデル内の繰り返しと可読性の問題を解決しました。RLの前に、DeepSeek-R1はコールドスタートデータを導入し、推論性能をさらに最適化しました。数学、コード、推論タスクにおいてOpenAI-o1と同等のパフォーマンスを示し、精巧に設計された訓練方法によって全体的な効果を向上させました。"}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70BはLlama-3.3-70B-Instructに基づき、蒸留訓練を通じて得られたモデルです。このモデルはDeepSeek-R1シリーズの一部であり、DeepSeek-R1が生成したサンプルを使用して微調整され、数学、プログラミング、推論などの複数の分野で優れた性能を示しています。"}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14BはQwen2.5-14Bに基づき、知識蒸留を通じて得られたモデルです。このモデルはDeepSeek-R1が生成した80万の選りすぐりのサンプルを使用して微調整され、優れた推論能力を示しています。"}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32BはQwen2.5-32Bに基づき、知識蒸留を通じて得られたモデルです。このモデルはDeepSeek-R1が生成した80万の選りすぐりのサンプルを使用して微調整され、数学、プログラミング、推論などの複数の分野で卓越した性能を示しています。"}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-liteは全く新しい世代の軽量版モデルで、極限の応答速度を実現し、効果と遅延の両方で世界トップレベルに達しています。"}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256kはDoubao-1.5-Proの全面的なアップグレード版で、全体的な効果が10%大幅に向上しました。256kのコンテキストウィンドウでの推論をサポートし、出力長は最大12kトークンをサポートします。より高い性能、より大きなウィンドウ、超高コストパフォーマンスで、より広範なアプリケーションシーンに適しています。"}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-proは全く新しい世代の主力モデルで、性能が全面的にアップグレードされ、知識、コード、推論などの面で卓越したパフォーマンスを発揮します。"}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5の新しい深層思考モデルは、数学、プログラミング、科学的推論などの専門分野や、創造的な執筆などの一般的なタスクで優れたパフォーマンスを発揮し、AIME 2024、Codeforces、GPQAなどの複数の権威あるベンチマークで業界の最前線に達するか、またはそれに近いレベルを実現しています。128kのコンテキストウィンドウと16kの出力をサポートしています。"}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5の新しい深層思考モデル（mバージョンはネイティブのマルチモーダル深層推論能力を備えています）は、数学、プログラミング、科学的推論などの専門分野および創造的な執筆などの一般タスクで優れたパフォーマンスを発揮し、AIME 2024、Codeforces、GPQAなどの複数の権威あるベンチマークで業界トップクラスのレベルに達しています。128kのコンテキストウィンドウと16kの出力をサポートします。"}, "doubao-1.5-thinking-vision-pro": {"description": "新しい視覚深層思考モデルで、より強力な汎用マルチモーダル理解と推論能力を備え、59の公開ベンチマークのうち37でSOTA（最先端）を達成しています。"}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARSは、グラフィカルユーザーインターフェース（GUI）向けにネイティブ設計されたエージェントモデルです。知覚、推論、行動などの人間のような能力を通じてGUIとシームレスにインタラクションします。"}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-liteは新たにアップグレードされた多モーダル大モデルで、任意の解像度と極端なアスペクト比の画像認識をサポートし、視覚推論、文書認識、詳細情報の理解、指示の遵守能力を強化しています。128kのコンテキストウィンドウをサポートし、出力長は最大16kトークンをサポートします。"}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-proは新たにアップグレードされたマルチモーダル大規模モデルで、任意の解像度および極端なアスペクト比の画像認識をサポートし、視覚的推論、文書認識、詳細情報の理解、指示の遵守能力を強化しています。"}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-proは新たにアップグレードされたマルチモーダル大規模モデルで、任意の解像度および極端なアスペクト比の画像認識をサポートし、視覚的推論、文書認識、詳細情報の理解、指示の遵守能力を強化しています。"}, "doubao-lite-128k": {"description": "極めて高速な応答速度と優れたコストパフォーマンスを備え、さまざまなシナリオに柔軟な選択肢を提供します。128kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "doubao-lite-32k": {"description": "極めて高速な応答速度と優れたコストパフォーマンスを備え、さまざまなシナリオに柔軟な選択肢を提供します。32kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "doubao-lite-4k": {"description": "極めて高速な応答速度と優れたコストパフォーマンスを備え、さまざまなシナリオに柔軟な選択肢を提供します。4kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "doubao-pro-256k": {"description": "最も高性能な主力モデルで、複雑なタスクの処理に適しています。参考質問応答、要約、創作、テキスト分類、ロールプレイなどのシーンで優れた効果を発揮します。256kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "doubao-pro-32k": {"description": "最も高性能な主力モデルで、複雑なタスクの処理に適しています。参考質問応答、要約、創作、テキスト分類、ロールプレイなどのシーンで優れた効果を発揮します。32kのコンテキストウィンドウでの推論と微調整をサポートします。"}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 は新しいマルチモーダル深層思考モデルで、auto/thinking/non-thinking の三つの思考モードをサポートします。non-thinking モードでは、Doubao-1.5-pro/250115 と比較して大幅に性能が向上しています。256k のコンテキストウィンドウをサポートし、最大 16k トークンの出力長に対応しています。"}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash は推論速度に優れたマルチモーダル深層思考モデルで、TPOT はわずか 10ms です。テキストと視覚の理解を同時にサポートし、テキスト理解能力は前世代の lite を超え、視覚理解は競合他社の pro シリーズモデルに匹敵します。256k のコンテキストウィンドウをサポートし、最大 16k トークンの出力長に対応しています。"}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking モデルは思考能力が大幅に強化されており、Doubao-1.5-thinking-pro と比較して、コーディング、数学、論理推論などの基礎能力がさらに向上しています。視覚理解もサポートしています。256k のコンテキストウィンドウをサポートし、最大 16k トークンの出力長に対応しています。"}, "doubao-seedream-3-0-t2i-250415": {"description": "Doubao画像生成モデルはByteDanceのSeedチームが開発し、テキストと画像の入力をサポートし、高い制御性と高品質な画像生成体験を提供します。テキストプロンプトに基づいて画像を生成します。"}, "doubao-vision-lite-32k": {"description": "Doubao-visionモデルは豆包が提供するマルチモーダル大規模モデルで、強力な画像理解と推論能力、正確な指示理解能力を備えています。画像テキスト情報抽出や画像に基づく推論タスクで高い性能を示し、より複雑で幅広い視覚質問応答タスクに応用可能です。"}, "doubao-vision-pro-32k": {"description": "Doubao-visionモデルは豆包が提供するマルチモーダル大規模モデルで、強力な画像理解と推論能力、正確な指示理解能力を備えています。画像テキスト情報抽出や画像に基づく推論タスクで高い性能を示し、より複雑で幅広い視覚質問応答タスクに応用可能です。"}, "emohaa": {"description": "Emohaaは心理モデルで、専門的な相談能力を持ち、ユーザーが感情問題を理解するのを助けます。"}, "ernie-3.5-128k": {"description": "百度が独自に開発したフラッグシップの大規模言語モデルで、膨大な中英文コーパスをカバーし、強力な汎用能力を持ち、ほとんどの対話質問応答、創作生成、プラグインアプリケーションシーンの要求を満たすことができます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ernie-3.5-8k": {"description": "百度が独自に開発したフラッグシップの大規模言語モデルで、膨大な中英文コーパスをカバーし、強力な汎用能力を持ち、ほとんどの対話質問応答、創作生成、プラグインアプリケーションシーンの要求を満たすことができます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ernie-3.5-8k-preview": {"description": "百度が独自に開発したフラッグシップの大規模言語モデルで、膨大な中英文コーパスをカバーし、強力な汎用能力を持ち、ほとんどの対話質問応答、創作生成、プラグインアプリケーションシーンの要求を満たすことができます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ernie-4.0-8k-latest": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、ERNIE 3.5に比べてモデル能力が全面的にアップグレードされ、さまざまな分野の複雑なタスクシーンに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ernie-4.0-8k-preview": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、ERNIE 3.5に比べてモデル能力が全面的にアップグレードされ、さまざまな分野の複雑なタスクシーンに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。"}, "ernie-4.0-turbo-128k": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、総合的なパフォーマンスが優れており、さまざまな分野の複雑なタスクシーンに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。ERNIE 4.0に比べてパフォーマンスがさらに優れています。"}, "ernie-4.0-turbo-8k-latest": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、総合的なパフォーマンスが優れており、さまざまな分野の複雑なタスクシーンに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。ERNIE 4.0に比べてパフォーマンスがさらに優れています。"}, "ernie-4.0-turbo-8k-preview": {"description": "百度が独自に開発したフラッグシップの超大規模言語モデルで、総合的なパフォーマンスが優れており、さまざまな分野の複雑なタスクシーンに広く適用されます。百度検索プラグインとの自動接続をサポートし、質問応答情報のタイムリーさを保証します。ERNIE 4.0に比べてパフォーマンスがさらに優れています。"}, "ernie-4.5-8k-preview": {"description": "文心大モデル4.5は、百度が独自に開発した次世代のネイティブマルチモーダル基盤大モデルで、複数のモーダルを共同でモデル化することで協調最適化を実現し、優れたマルチモーダル理解能力を持っています。言語能力がさらに向上し、理解、生成、論理、記憶能力が全面的に向上し、幻覚の排除、論理推論、コード能力が顕著に向上しています。"}, "ernie-4.5-turbo-128k": {"description": "文心4.5 Turboは、幻覚の除去、論理推論、コード能力などの面で明らかな強化が見られます。文心4.5と比較して、速度が速く、価格が低くなっています。モデルの能力が全体的に向上し、複数回の長い履歴対話処理や長文書理解問答タスクにより良く対応します。"}, "ernie-4.5-turbo-32k": {"description": "文心4.5 Turboは、幻覚の除去、論理推論、コード能力などの面で明らかな強化が見られます。文心4.5と比較して、速度が速く、価格が低くなっています。テキスト創作、知識問答などの能力が顕著に向上しています。出力の長さと全体の文の遅延はERNIE 4.5に比べて増加しています。"}, "ernie-4.5-turbo-vl-32k": {"description": "文心一言大モデルの新しいバージョンで、画像理解、創作、翻訳、コードなどの能力が顕著に向上し、初めて32Kのコンテキスト長をサポートし、最初のトークンの遅延が大幅に減少しました。"}, "ernie-char-8k": {"description": "百度が独自に開発した垂直シーン向けの大規模言語モデルで、ゲームのNPC、カスタマーサービスの対話、対話キャラクターの役割演技などのアプリケーションシーンに適しており、キャラクターのスタイルがより鮮明で一貫しており、指示に従う能力が強く、推論性能が優れています。"}, "ernie-char-fiction-8k": {"description": "百度が独自に開発した垂直シーン向けの大規模言語モデルで、ゲームのNPC、カスタマーサービスの対話、対話キャラクターの役割演技などのアプリケーションシーンに適しており、キャラクターのスタイルがより鮮明で一貫しており、指示に従う能力が強く、推論性能が優れています。"}, "ernie-irag-edit": {"description": "百度が独自開発したERNIE iRAG Edit画像編集モデルは、画像に基づく消去（erase）、再描画（repaint）、バリエーション生成（variation）などの操作をサポートします。"}, "ernie-lite-8k": {"description": "ERNIE Liteは、百度が独自に開発した軽量級の大規模言語モデルで、優れたモデル効果と推論性能を兼ね備え、低計算能力のAIアクセラレータカードでの推論使用に適しています。"}, "ernie-lite-pro-128k": {"description": "百度が独自に開発した軽量級の大規模言語モデルで、優れたモデル効果と推論性能を兼ね備え、ERNIE Liteよりも優れた効果を持ち、低計算能力のAIアクセラレータカードでの推論使用に適しています。"}, "ernie-novel-8k": {"description": "百度が独自に開発した汎用大規模言語モデルで、小説の続編作成能力に明らかな優位性があり、短編劇や映画などのシーンにも使用できます。"}, "ernie-speed-128k": {"description": "百度が2024年に最新リリースした自社開発の高性能大規模言語モデルで、汎用能力が優れており、基盤モデルとして微調整に適しており、特定のシーンの問題をより良く処理し、優れた推論性能を持っています。"}, "ernie-speed-pro-128k": {"description": "百度が2024年に最新リリースした自社開発の高性能大規模言語モデルで、汎用能力が優れており、ERNIE Speedよりも優れた効果を持ち、基盤モデルとして微調整に適しており、特定のシーンの問題をより良く処理し、優れた推論性能を持っています。"}, "ernie-tiny-8k": {"description": "ERNIE Tinyは、百度が独自に開発した超高性能の大規模言語モデルで、文心シリーズモデルの中でデプロイと微調整コストが最も低いです。"}, "ernie-x1-32k": {"description": "より強力な理解、計画、反省、進化能力を備えています。より包括的な深い思考モデルとして、文心X1は正確さ、創造性、文才を兼ね備え、中国語の知識問答、文学創作、文書作成、日常会話、論理推論、複雑な計算およびツール呼び出しなどの分野で特に優れたパフォーマンスを発揮します。"}, "ernie-x1-32k-preview": {"description": "文心大モデルX1は、より強力な理解、計画、反省、進化の能力を備えています。より包括的な深い思考モデルとして、文心X1は正確さ、創造性、文才を兼ね備え、中国語の知識問答、文学創作、文書作成、日常会話、論理推論、複雑な計算、ツールの呼び出しなどの分野で特に優れたパフォーマンスを発揮します。"}, "ernie-x1-turbo-32k": {"description": "ERNIE-X1-32Kと比較して、モデルの効果と性能が向上しています。"}, "flux-1-schnell": {"description": "Black Forest Labsが開発した120億パラメータのテキストから画像生成モデルで、潜在的敵対的拡散蒸留技術を採用し、1～4ステップで高品質な画像を生成可能。閉源の代替品に匹敵する性能を持ち、Apache-2.0ライセンスの下で個人、研究、商用利用に適用可能です。"}, "flux-dev": {"description": "FLUX.1 [dev]は非商用用途向けのオープンソースの重み付き精錬モデルで、FLUXプロフェッショナル版に近い画像品質と指示遵守能力を維持しつつ、より高い実行効率を実現。標準モデルと同サイズながらリソース利用効率が向上しています。"}, "flux-kontext/dev": {"description": "フロンティアイメージ編集モデル。"}, "flux-merged": {"description": "FLUX.1-mergedモデルは、開発段階で探索された「DEV」の深層特性と「Schnell」が示す高速実行の利点を組み合わせています。この取り組みにより、FLUX.1-mergedはモデルの性能限界を押し上げ、応用範囲を拡大しました。"}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] はテキストと参照画像を入力として処理し、目的に応じた局所編集や複雑な全体シーンの変換をシームレスに実現します。"}, "flux-schnell": {"description": "FLUX.1 [schnell]は現時点で最先端の少ステップモデルであり、同種の競合モデルを凌駕し、Midjourney v6.0やDALL·E 3 (HD)などの強力な非蒸留モデルよりも優れています。専用の微調整により、事前学習段階の出力多様性を完全に保持し、市場の最先端モデルと比較して視覚品質、指示遵守、サイズ・比率変化、フォント処理、出力多様性の面で大幅に向上。ユーザーにより豊かで多様な創造的画像生成体験を提供します。"}, "flux.1-schnell": {"description": "120億パラメータを持つ修正フロートランスフォーマーで、テキスト記述に基づいて画像を生成します。"}, "flux/schnell": {"description": "FLUX.1 [schnell] は120億パラメータを持つストリーミングトランスフォーマーモデルで、1〜4ステップでテキストから高品質な画像を生成し、個人および商用利用に適しています。"}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001（チューニング）は、安定した調整可能な性能を提供し、複雑なタスクのソリューションに理想的な選択肢です。"}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002（チューニング）は、優れたマルチモーダルサポートを提供し、複雑なタスクの効果的な解決に焦点を当てています。"}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Proは、Googleの高性能AIモデルであり、幅広いタスクの拡張に特化しています。"}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001は、効率的なマルチモーダルモデルであり、幅広いアプリケーションの拡張をサポートします。"}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002は効率的なマルチモーダルモデルで、幅広いアプリケーションの拡張をサポートしています。"}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8Bは、高効率のマルチモーダルモデルで、幅広いアプリケーションの拡張をサポートしています。"}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924は最新の実験モデルで、テキストおよびマルチモーダルのユースケースにおいて顕著な性能向上を実現しています。"}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8Bは、効率的なマルチモーダルモデルで、幅広いアプリケーションの拡張をサポートしています。"}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827は、最適化されたマルチモーダル処理能力を提供し、多様な複雑なタスクシナリオに適用可能です。"}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flashは、Googleの最新のマルチモーダルAIモデルであり、高速処理能力を備え、テキスト、画像、動画の入力をサポートし、さまざまなタスクの効率的な拡張に適しています。"}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001は、拡張可能なマルチモーダルAIソリューションであり、幅広い複雑なタスクをサポートします。"}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002は最新の生産準備モデルで、特に数学、長いコンテキスト、視覚タスクにおいて質の高い出力を提供し、顕著な向上を見せています。"}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801は、優れたマルチモーダル処理能力を提供し、アプリケーション開発により大きな柔軟性をもたらします。"}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827は、最新の最適化技術を組み合わせ、より効率的なマルチモーダルデータ処理能力をもたらします。"}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Proは、最大200万トークンをサポートする中型マルチモーダルモデルの理想的な選択肢であり、複雑なタスクに対する多面的なサポートを提供します。"}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flashは、卓越した速度、ネイティブツールの使用、マルチモーダル生成、1Mトークンのコンテキストウィンドウを含む次世代の機能と改善を提供します。"}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flashは、卓越した速度、ネイティブツールの使用、マルチモーダル生成、1Mトークンのコンテキストウィンドウを含む次世代の機能と改善を提供します。"}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash モデルのバリアントで、コスト効率と低遅延などの目標に最適化されています。"}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash 実験モデル、画像生成をサポート"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flashモデルのバリアントで、コスト効率と低遅延などの目標に最適化されています。"}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flashモデルのバリアントで、コスト効率と低遅延などの目標に最適化されています。"}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash プレビュー モデル、画像生成をサポート"}, "gemini-2.5-flash": {"description": "Gemini 2.5 FlashはGoogleのコストパフォーマンスに優れたモデルで、包括的な機能を提供します。"}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite は、Google の中で最も小さく、コストパフォーマンスに優れたモデルであり、大規模な利用を目的に設計されています。"}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite PreviewはGoogleの最小かつコストパフォーマンスに優れたモデルで、大規模利用を目的に設計されています。"}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Previewは、Googleのコストパフォーマンスに優れたモデルで、包括的な機能を提供します。"}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash PreviewはGoogleのコストパフォーマンスに優れたモデルで、包括的な機能を提供します。"}, "gemini-2.5-pro": {"description": "Gemini 2.5 ProはGoogleの最先端思考モデルで、コード、数学、STEM分野の複雑な問題の推論が可能であり、長文コンテキストを用いて大規模データセット、コードベース、ドキュメントの分析を行います。"}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Previewは、Googleの最先端の思考モデルであり、コード、数学、STEM分野の複雑な問題に対して推論を行い、長いコンテキストを使用して大規模なデータセット、コードベース、文書を分析することができます。"}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Previewは、Googleの最先端思考モデルであり、コード、数学、STEM分野の複雑な問題に対して推論を行い、長いコンテキストを使用して大規模なデータセット、コードベース、文書を分析することができます。"}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview は Google の最先端思考モデルで、コード、数学、STEM 分野の複雑な問題を推論し、長いコンテキストを用いて大規模なデータセット、コードベース、ドキュメントを分析できます。"}, "gemma-7b-it": {"description": "Gemma 7Bは、中小規模のタスク処理に適しており、コスト効果を兼ね備えています。"}, "gemma2": {"description": "Gemma 2は、Googleが提供する高効率モデルであり、小型アプリケーションから複雑なデータ処理まで、さまざまなアプリケーションシーンをカバーしています。"}, "gemma2-9b-it": {"description": "Gemma 2 9Bは、特定のタスクとツール統合のために最適化されたモデルです。"}, "gemma2:27b": {"description": "Gemma 2は、Googleが提供する高効率モデルであり、小型アプリケーションから複雑なデータ処理まで、さまざまなアプリケーションシーンをカバーしています。"}, "gemma2:2b": {"description": "Gemma 2は、Googleが提供する高効率モデルであり、小型アプリケーションから複雑なデータ処理まで、さまざまなアプリケーションシーンをカバーしています。"}, "generalv3": {"description": "Spark Proは専門分野に最適化された高性能な大言語モデルで、数学、プログラミング、医療、教育などの複数の分野に特化し、ネットワーク検索や内蔵の天気、日付などのプラグインをサポートします。最適化されたモデルは、複雑な知識問答、言語理解、高度なテキスト創作において優れたパフォーマンスと高効率を示し、専門的なアプリケーションシーンに最適な選択肢です。"}, "generalv3.5": {"description": "Spark3.5 Maxは機能が最も充実したバージョンで、ネットワーク検索や多くの内蔵プラグインをサポートします。全面的に最適化されたコア能力、システムロール設定、関数呼び出し機能により、さまざまな複雑なアプリケーションシーンでのパフォーマンスが非常に優れています。"}, "glm-4": {"description": "GLM-4は2024年1月にリリースされた旧フラッグシップバージョンで、現在はより強力なGLM-4-0520に取って代わられています。"}, "glm-4-0520": {"description": "GLM-4-0520は最新のモデルバージョンで、高度に複雑で多様なタスクのために設計され、優れたパフォーマンスを発揮します。"}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chatは、意味、数学、推論、コード、知識などの多方面で高い性能を示しています。また、ウェブブラウジング、コード実行、カスタムツール呼び出し、長文推論を備えています。日本語、韓国語、ドイツ語を含む26の言語をサポートしています。"}, "glm-4-air": {"description": "GLM-4-Airはコストパフォーマンスが高いバージョンで、GLM-4に近い性能を提供し、高速かつ手頃な価格です。"}, "glm-4-air-250414": {"description": "GLM-4-Airはコストパフォーマンスの高いバージョンで、性能はGLM-4に近く、速さと手頃な価格を提供します。"}, "glm-4-airx": {"description": "GLM-4-AirXはGLM-4-Airの効率的なバージョンで、推論速度はその2.6倍に達します。"}, "glm-4-alltools": {"description": "GLM-4-AllToolsは、複雑な指示計画とツール呼び出しをサポートするために最適化された多機能エージェントモデルで、ネットサーフィン、コード解釈、テキスト生成などの多タスク実行に適しています。"}, "glm-4-flash": {"description": "GLM-4-Flashはシンプルなタスクを処理するのに理想的な選択肢で、最も速く、最も手頃な価格です。"}, "glm-4-flash-250414": {"description": "GLM-4-Flashは簡単なタスクを処理するのに理想的な選択肢で、最も速く、無料です。"}, "glm-4-flashx": {"description": "GLM-4-Flash<PERSON>はFlashの強化版で、超高速の推論速度を誇ります。"}, "glm-4-long": {"description": "GLM-4-Longは超長文入力をサポートし、記憶型タスクや大規模文書処理に適しています。"}, "glm-4-plus": {"description": "GLM-4-Plusは高い知能を持つフラッグシップモデルで、長文や複雑なタスクを処理する能力が強化され、全体的なパフォーマンスが向上しています。"}, "glm-4.1v-thinking-flash": {"description": "GLM-4.1V-Thinking シリーズモデルは、現時点で知られている10BクラスのVLMモデルの中で最も性能の高い視覚モデルであり、同クラスのSOTAの各種視覚言語タスクを統合しています。これには動画理解、画像質問応答、学科問題解決、OCR文字認識、文書およびグラフ解析、GUIエージェント、フロントエンドウェブコーディング、グラウンディングなどが含まれ、多くのタスク能力は8倍のパラメータを持つQwen2.5-VL-72Bをも上回ります。先進的な強化学習技術により、思考の連鎖推論を通じて回答の正確性と豊かさを向上させ、最終的な成果と説明可能性の両面で従来の非thinkingモデルを大きく凌駕しています。"}, "glm-4.1v-thinking-flashx": {"description": "GLM-4.1V-Thinking シリーズモデルは、現時点で知られている10BクラスのVLMモデルの中で最も性能の高い視覚モデルであり、同クラスのSOTAの各種視覚言語タスクを統合しています。これには動画理解、画像質問応答、学科問題解決、OCR文字認識、文書およびグラフ解析、GUIエージェント、フロントエンドウェブコーディング、グラウンディングなどが含まれ、多くのタスク能力は8倍のパラメータを持つQwen2.5-VL-72Bをも上回ります。先進的な強化学習技術により、思考の連鎖推論を通じて回答の正確性と豊かさを向上させ、最終的な成果と説明可能性の両面で従来の非thinkingモデルを大きく凌駕しています。"}, "glm-4.5": {"description": "智譜の最新フラッグシップモデルで、思考モードの切り替えをサポートし、総合能力はオープンソースモデルのSOTAレベルに達し、コンテキスト長は最大128Kです。"}, "glm-4.5-air": {"description": "GLM-4.5の軽量版で、性能とコストパフォーマンスのバランスを取り、混合思考モデルの柔軟な切り替えが可能です。"}, "glm-4.5-airx": {"description": "GLM-4.5-Airの高速版で、応答速度がさらに向上し、大規模かつ高速なニーズに特化しています。"}, "glm-4.5-flash": {"description": "GLM-4.5の無料版で、推論、コード生成、エージェントなどのタスクで優れた性能を発揮します。"}, "glm-4.5-x": {"description": "GLM-4.5の高速版で、強力な性能を持ちながら、生成速度は100トークン/秒に達します。"}, "glm-4v": {"description": "GLM-4Vは強力な画像理解と推論能力を提供し、さまざまな視覚タスクをサポートします。"}, "glm-4v-flash": {"description": "GLM-4V-Flashは、高効率の単一画像理解に特化しており、リアルタイム画像分析やバッチ画像処理などの迅速な画像解析のシーンに適しています。"}, "glm-4v-plus": {"description": "GLM-4V-Plusは動画コンテンツや複数の画像を理解する能力を持ち、マルチモーダルタスクに適しています。"}, "glm-4v-plus-0111": {"description": "GLM-4V-Plusは動画コンテンツや複数の画像の理解能力を持ち、多モーダルタスクに適しています。"}, "glm-z1-air": {"description": "推論モデル：強力な推論能力を持ち、深い推論が必要なタスクに適しています。"}, "glm-z1-airx": {"description": "超高速推論：非常に速い推論速度と強力な推論効果を持っています。"}, "glm-z1-flash": {"description": "GLM-Z1シリーズは強力な複雑推論能力を備え、論理推論、数学、プログラミングなどの分野で優れた性能を示します。"}, "glm-z1-flashx": {"description": "高速かつ低価格：Flash強化版で、超高速推論速度とより速い同時処理を保証します。"}, "glm-zero-preview": {"description": "GLM-Zero-Previewは、強力な複雑な推論能力を備え、論理推論、数学、プログラミングなどの分野で優れたパフォーマンスを発揮します。"}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flashは、卓越した速度、ネイティブツールの使用、マルチモーダル生成、1Mトークンのコンテキストウィンドウを含む次世代の機能と改善を提供します。"}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimentalは、Googleの最新の実験的なマルチモーダルAIモデルであり、歴史的なバージョンと比較して特に世界知識、コード、長いコンテキストにおいて品質が向上しています。"}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash は、Google の最先端の主力モデルであり、高度な推論、コーディング、数学および科学タスク向けに設計されています。内蔵の「思考」機能を備えており、より高い精度と詳細なコンテキスト処理を伴う応答を提供できます。\n\n注意：このモデルには「思考」バリアントと非「思考」バリアントの2種類があります。出力の価格は思考機能の有効化により大きく異なります。標準バリアント（「:thinking」サフィックスなし）を選択した場合、モデルは明確に思考トークンの生成を回避します。\n\n思考機能を利用し思考トークンを受け取るには、「:thinking」バリアントを選択する必要があり、これにより思考出力の価格が高くなります。\n\nさらに、Gemini 2.5 Flash はドキュメントに記載されている「推論最大トークン数」パラメータ（https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning）を通じて設定可能です。"}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flashは、Googleの最先端の主力モデルであり、高度な推論、コーディング、数学、科学タスクのために設計されています。内蔵の「思考」能力を備えており、より高い精度と詳細なコンテキスト処理で応答を提供します。\n\n注意：このモデルには、思考と非思考の2つのバリアントがあります。出力の価格は、思考能力が有効かどうかによって大きく異なります。標準バリアント（「:thinking」サフィックスなし）を選択すると、モデルは明示的に思考トークンの生成を避けます。\n\n思考能力を利用して思考トークンを受け取るには、「:thinking」バリアントを選択する必要があり、これにより思考出力の価格が高くなります。\n\nさらに、Gemini 2.5 Flashは、「推論最大トークン数」パラメータを介して構成可能であり、文書に記載されています (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flashは、Googleの最先端の主力モデルであり、高度な推論、コーディング、数学、科学タスクのために設計されています。内蔵の「思考」能力を備えており、より高い精度と詳細なコンテキスト処理で応答を提供します。\n\n注意：このモデルには、思考と非思考の2つのバリアントがあります。出力の価格は、思考能力が有効かどうかによって大きく異なります。標準バリアント（「:thinking」サフィックスなし）を選択すると、モデルは明示的に思考トークンの生成を避けます。\n\n思考能力を利用して思考トークンを受け取るには、「:thinking」バリアントを選択する必要があり、これにより思考出力の価格が高くなります。\n\nさらに、Gemini 2.5 Flashは、「推論最大トークン数」パラメータを介して構成可能であり、文書に記載されています (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro は、Google の最先端の思考モデルであり、コード、数学、STEM分野の複雑な問題に対して推論を行い、長いコンテキストを用いて大規模なデータセット、コードベース、ドキュメントを分析できます。"}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview は、Google の最先端の思考モデルであり、コード、数学、STEM 分野の複雑な問題を推論し、長いコンテキストを用いて大規模なデータセット、コードベース、ドキュメントを分析することができます。"}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flashは、最適化されたマルチモーダル処理能力を提供し、さまざまな複雑なタスクシナリオに適しています。"}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Proは、最新の最適化技術を組み合わせて、より効率的なマルチモーダルデータ処理能力を実現します。"}, "google/gemma-2-27b": {"description": "Gemma 2はGoogleが提供する効率的なモデルで、小型アプリケーションから複雑なデータ処理まで、さまざまなアプリケーションシナリオをカバーしています。"}, "google/gemma-2-27b-it": {"description": "Gemma 2は、軽量化と高効率のデザイン理念を継承しています。"}, "google/gemma-2-2b-it": {"description": "Googleの軽量指示調整モデル"}, "google/gemma-2-9b": {"description": "Gemma 2はGoogleが提供する効率的なモデルで、小型アプリケーションから複雑なデータ処理まで、さまざまなアプリケーションシナリオをカバーしています。"}, "google/gemma-2-9b-it": {"description": "Gemma 2は、Googleの軽量オープンソーステキストモデルシリーズです。"}, "google/gemma-2-9b-it:free": {"description": "Gemma 2はGoogleの軽量化されたオープンソーステキストモデルシリーズです。"}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B)は、基本的な指示処理能力を提供し、軽量アプリケーションに適しています。"}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B はGoogleのオープンソース言語モデルであり、効率と性能の面で新たな基準を打ち立てました。"}, "google/gemma-3-27b-it": {"description": "Gemma 3 27Bは、Googleのオープンソース言語モデルで、効率と性能の面で新たな基準を打ち立てました。"}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turboは、さまざまなテキスト生成と理解タスクに適しており、現在はgpt-3.5-turbo-0125を指しています。"}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turboは、さまざまなテキスト生成と理解タスクに適しており、現在はgpt-3.5-turbo-0125を指しています。"}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turboは、さまざまなテキスト生成と理解タスクに適しており、現在はgpt-3.5-turbo-0125を指しています。"}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turboは、さまざまなテキスト生成と理解タスクに適しており、現在はgpt-3.5-turbo-0125を指しています。"}, "gpt-35-turbo": {"description": "GPT 3.5 Turboは、OpenAIが提供する効率的なモデルで、チャットやテキスト生成タスクに適しており、並行関数呼び出しをサポートしています。"}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16kは、高容量のテキスト生成モデルで、複雑なタスクに適しています。"}, "gpt-4": {"description": "GPT-4は、より大きなコンテキストウィンドウを提供し、より長いテキスト入力を処理できるため、広範な情報統合やデータ分析が必要なシナリオに適しています。"}, "gpt-4-0125-preview": {"description": "最新のGPT-4 Turboモデルは視覚機能を備えています。現在、視覚リクエストはJSON形式と関数呼び出しを使用して行うことができます。GPT-4 Turboは、マルチモーダルタスクに対してコスト効率の高いサポートを提供する強化版です。正確性と効率のバランスを取り、リアルタイムのインタラクションが必要なアプリケーションシナリオに適しています。"}, "gpt-4-0613": {"description": "GPT-4は、より大きなコンテキストウィンドウを提供し、より長いテキスト入力を処理できるため、広範な情報統合やデータ分析が必要なシナリオに適しています。"}, "gpt-4-1106-preview": {"description": "最新のGPT-4 Turboモデルは視覚機能を備えています。現在、視覚リクエストはJSON形式と関数呼び出しを使用して行うことができます。GPT-4 Turboは、マルチモーダルタスクに対してコスト効率の高いサポートを提供する強化版です。正確性と効率のバランスを取り、リアルタイムのインタラクションが必要なアプリケーションシナリオに適しています。"}, "gpt-4-32k": {"description": "GPT-4は、より大きなコンテキストウィンドウを提供し、より長いテキスト入力を処理できるため、広範な情報統合やデータ分析が必要なシナリオに適しています。"}, "gpt-4-32k-0613": {"description": "GPT-4は、より大きなコンテキストウィンドウを提供し、より長いテキスト入力を処理できるため、広範な情報統合やデータ分析が必要なシナリオに適しています。"}, "gpt-4-turbo": {"description": "最新のGPT-4 Turboモデルは視覚機能を備えています。現在、視覚リクエストはJSON形式と関数呼び出しを使用して行うことができます。GPT-4 Turboは、マルチモーダルタスクに対してコスト効率の高いサポートを提供する強化版です。正確性と効率のバランスを取り、リアルタイムのインタラクションが必要なアプリケーションシナリオに適しています。"}, "gpt-4-turbo-2024-04-09": {"description": "最新のGPT-4 Turboモデルは視覚機能を備えています。現在、視覚リクエストはJSON形式と関数呼び出しを使用して行うことができます。GPT-4 Turboは、マルチモーダルタスクに対してコスト効率の高いサポートを提供する強化版です。正確性と効率のバランスを取り、リアルタイムのインタラクションが必要なアプリケーションシナリオに適しています。"}, "gpt-4-turbo-preview": {"description": "最新のGPT-4 Turboモデルは視覚機能を備えています。現在、視覚リクエストはJSON形式と関数呼び出しを使用して行うことができます。GPT-4 Turboは、マルチモーダルタスクに対してコスト効率の高いサポートを提供する強化版です。正確性と効率のバランスを取り、リアルタイムのインタラクションが必要なアプリケーションシナリオに適しています。"}, "gpt-4-vision-preview": {"description": "最新のGPT-4 Turboモデルは視覚機能を備えています。現在、視覚リクエストはJSON形式と関数呼び出しを使用して行うことができます。GPT-4 Turboは、マルチモーダルタスクに対してコスト効率の高いサポートを提供する強化版です。正確性と効率のバランスを取り、リアルタイムのインタラクションが必要なアプリケーションシナリオに適しています。"}, "gpt-4.1": {"description": "GPT-4.1は、複雑なタスクに使用するためのフラッグシップモデルです。さまざまな分野の問題を解決するのに非常に適しています。"}, "gpt-4.1-mini": {"description": "GPT-4.1 miniは、知性、速度、コストのバランスを提供し、多くのユースケースにおいて魅力的なモデルとなっています。"}, "gpt-4.1-nano": {"description": "GPT-4.1 miniは、知性、速度、コストのバランスを提供し、多くのユースケースにおいて魅力的なモデルとなっています。"}, "gpt-4.5-preview": {"description": "GPT-4.5の研究プレビュー版で、これまでで最大かつ最強のGPTモデルです。広範な世界知識を持ち、ユーザーの意図をよりよく理解することができるため、創造的なタスクや自律的な計画において優れたパフォーマンスを発揮します。GPT-4.5はテキストと画像の入力を受け付け、テキスト出力（構造化出力を含む）を生成します。関数呼び出し、バッチAPI、ストリーミング出力など、重要な開発者機能をサポートしています。創造的でオープンな思考や対話が求められるタスク（執筆、学習、新しいアイデアの探求など）において、GPT-4.5は特に優れた性能を発揮します。知識のカットオフ日は2023年10月です。"}, "gpt-4o": {"description": "ChatGPT-4oは、リアルタイムで更新される動的モデルで、常に最新のバージョンを維持します。強力な言語理解と生成能力を組み合わせており、顧客サービス、教育、技術サポートなどの大規模なアプリケーションシナリオに適しています。"}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4oは、リアルタイムで更新される動的モデルで、常に最新のバージョンを維持します。強力な言語理解と生成能力を組み合わせており、顧客サービス、教育、技術サポートなどの大規模なアプリケーションシナリオに適しています。"}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4oは、リアルタイムで更新される動的モデルで、常に最新のバージョンを維持します。強力な言語理解と生成能力を組み合わせており、顧客サービス、教育、技術サポートなどの大規模なアプリケーションシナリオに適しています。"}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4oは動的モデルで、リアルタイムで更新され、常に最新バージョンを保持します。 powerfulな言語理解と生成能力を組み合わせており、カスタマーサービス、教育、技術サポートなどの大規模なアプリケーションに適しています。"}, "gpt-4o-audio-preview": {"description": "GPT-4o Audio モデル、音声の入力と出力をサポート"}, "gpt-4o-mini": {"description": "GPT-4o miniは、OpenAIがGPT-4 Omniの後に発表した最新のモデルで、画像とテキストの入力をサポートし、テキストを出力します。最先端の小型モデルとして、最近の他の先進モデルよりもはるかに安価で、GPT-3.5 Turboよりも60%以上安価です。最先端の知能を維持しつつ、コストパフォーマンスが大幅に向上しています。GPT-4o miniはMMLUテストで82%のスコアを獲得し、現在チャットの好みではGPT-4よりも高い評価を得ています。"}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Audio モデルは音声の入力と出力をサポートします。"}, "gpt-4o-mini-realtime-preview": {"description": "GPT-4o-miniリアルタイムバージョン、音声とテキストのリアルタイム入力と出力をサポート"}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini検索プレビュー版は、ウェブ検索クエリの理解と実行に特化して訓練されたモデルで、Chat Completions APIを使用しています。トークン料金に加え、ウェブ検索クエリはツール呼び出しごとに料金が発生します。"}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini TranscribeはGPT-4oを使用した音声からテキストへの転写モデルです。元のWhisperモデルと比較して単語誤り率が改善され、言語認識と精度が向上しています。より正確な転写を得るためにご利用ください。"}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS は、GPT-4o mini に基づくテキスト音声合成モデルで、高品質な音声生成を低コストで提供します。"}, "gpt-4o-realtime-preview": {"description": "GPT-4oリアルタイムバージョン、音声とテキストのリアルタイム入力と出力をサポート"}, "gpt-4o-realtime-preview-2024-10-01": {"description": "GPT-4oリアルタイムバージョン、音声とテキストのリアルタイム入力と出力をサポート"}, "gpt-4o-realtime-preview-2025-06-03": {"description": "GPT-4oのリアルタイムバージョンで、音声とテキストのリアルタイム入出力をサポートします。"}, "gpt-4o-search-preview": {"description": "GPT-4o検索プレビュー版は、ウェブ検索クエリの理解と実行に特化して訓練されたモデルで、Chat Completions APIを使用しています。トークン料金に加え、ウェブ検索クエリはツール呼び出しごとに料金が発生します。"}, "gpt-4o-transcribe": {"description": "GPT-4o TranscribeはGPT-4oを使用した音声からテキストへの転写モデルです。元のWhisperモデルと比較して単語誤り率が改善され、言語認識と精度が向上しています。より正確な転写を得るためにご利用ください。"}, "gpt-image-1": {"description": "ChatGPT ネイティブのマルチモーダル画像生成モデル"}, "grok-2-1212": {"description": "このモデルは、精度、指示の遵守、そして多言語能力において改善されています。"}, "grok-2-image-1212": {"description": "最新の画像生成モデルで、テキストプロンプトに基づき生き生きとしたリアルな画像を生成します。マーケティング、ソーシャルメディア、エンターテインメント分野での画像生成に優れた性能を発揮します。"}, "grok-2-vision-1212": {"description": "このモデルは、精度、指示の遵守、そして多言語能力において改善されています。"}, "grok-3": {"description": "フラッグシップモデルで、データ抽出、プログラミング、テキスト要約などの企業向けアプリケーションに優れ、金融、医療、法律、科学などの分野に深い知識を持ちます。"}, "grok-3-fast": {"description": "フラッグシップモデルで、データ抽出、プログラミング、テキスト要約などの企業向けアプリケーションに優れ、金融、医療、法律、科学などの分野に深い知識を持ちます。"}, "grok-3-mini": {"description": "軽量モデルで、会話前に思考します。高速かつスマートに動作し、深い専門知識を必要としない論理タスクに適しており、元の思考過程を取得できます。"}, "grok-3-mini-fast": {"description": "軽量モデルで、会話前に思考します。高速かつスマートに動作し、深い専門知識を必要としない論理タスクに適しており、元の思考過程を取得できます。"}, "grok-4": {"description": "私たちの最新かつ最強のフラッグシップモデルであり、自然言語処理、数学計算、推論において卓越した性能を発揮します——まさに完璧な万能型プレイヤーです。"}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13Bは複数のトップモデルを統合した創造性と知性を兼ね備えた言語モデルです。"}, "hunyuan-a13b": {"description": "混元の初のハイブリッド推論モデルであるhunyuan-standard-256Kのアップグレード版で、総パラメータ80B、アクティベーション13Bを持ちます。デフォルトはスロースルーモードで、パラメータまたは指示によって高速・低速思考モードの切り替えが可能です。切り替え方法はクエリの前に「/」または「no_think」を付加します。全体的な能力は前世代に比べて大幅に向上しており、特に数学、科学、長文理解、エージェント能力が顕著に強化されています。"}, "hunyuan-code": {"description": "混元の最新のコード生成モデルで、200Bの高品質コードデータで基盤モデルを増強し、半年間の高品質SFTデータトレーニングを経て、コンテキストウィンドウの長さが8Kに増加しました。5つの主要言語のコード生成自動評価指標で上位に位置し、5つの言語における10項目の総合コードタスクの人工高品質評価で、パフォーマンスは第一梯隊にあります。"}, "hunyuan-functioncall": {"description": "混元の最新のMOEアーキテクチャFunctionCallモデルで、高品質のFunctionCallデータトレーニングを経て、コンテキストウィンドウは32Kに達し、複数の次元の評価指標でリーダーシップを発揮しています。"}, "hunyuan-large": {"description": "Hunyuan-largeモデルの総パラメータ数は約389B、活性化パラメータ数は約52Bで、現在業界で最大のパラメータ規模を持ち、最も優れた効果を持つTransformerアーキテクチャのオープンソースMoEモデル。"}, "hunyuan-large-longcontext": {"description": "文書要約や文書問答などの長文タスクを得意とし、一般的なテキスト生成タスクの処理能力も備えている。長文の分析と生成において優れたパフォーマンスを発揮し、複雑で詳細な長文内容の処理要求に効果的に対応できる。"}, "hunyuan-large-vision": {"description": "本モデルは画像と言語の理解シナリオに適しており、混元Largeを基に訓練された視覚言語大規模モデルです。任意の解像度の複数画像＋テキスト入力をサポートし、テキスト生成を行います。画像と言語の理解関連タスクに注力し、多言語の画像と言語理解能力が著しく向上しています。"}, "hunyuan-lite": {"description": "MOE構造にアップグレードされ、コンテキストウィンドウは256kで、NLP、コード、数学、業界などの多くの評価セットで多くのオープンソースモデルをリードしています。"}, "hunyuan-lite-vision": {"description": "混元最新の7Bマルチモーダルモデル、コンテキストウィンドウ32K、中英文シーンのマルチモーダル対話、画像物体認識、文書表理解、マルチモーダル数学などをサポートし、複数の次元で評価指標が7B競合モデルを上回る。"}, "hunyuan-pro": {"description": "万億規模のパラメータを持つMOE-32K長文モデルです。さまざまなベンチマークで絶対的なリーダーシップを達成し、複雑な指示や推論、複雑な数学能力を備え、functioncallをサポートし、多言語翻訳、金融、法律、医療などの分野で重点的に最適化されています。"}, "hunyuan-role": {"description": "混元の最新のロールプレイングモデルで、混元公式の精緻なトレーニングによって開発されたロールプレイングモデルで、混元モデルとロールプレイングシナリオデータセットを組み合わせて増強され、ロールプレイングシナリオにおいてより良い基本的な効果を持っています。"}, "hunyuan-standard": {"description": "より優れたルーティング戦略を採用し、負荷分散と専門家の収束の問題を緩和しました。長文に関しては、大海捞針指標が99.9%に達しています。MOE-32Kはコストパフォーマンスが相対的に高く、効果と価格のバランスを取りながら、長文入力の処理を実現します。"}, "hunyuan-standard-256K": {"description": "より優れたルーティング戦略を採用し、負荷分散と専門家の収束の問題を緩和しました。長文に関しては、大海捞針指標が99.9%に達しています。MOE-256Kは長さと効果の面でさらに突破し、入力可能な長さを大幅に拡張しました。"}, "hunyuan-standard-vision": {"description": "混元最新のマルチモーダルモデルで、多言語での応答をサポートし、中英文能力が均衡している。"}, "hunyuan-t1-20250321": {"description": "モデルの文理科能力を全面的に構築し、長文情報のキャッチ能力が高いです。さまざまな難易度の数学、論理推論、科学、コードなどの科学問題に対する推論解答をサポートします。"}, "hunyuan-t1-20250403": {"description": "プロジェクトレベルのコード生成能力を向上させる；テキスト生成の執筆品質を向上させる；テキスト理解のトピックにおける多段階対話、ToB指示の遵守および語彙理解能力を向上させる；繁体字と簡体字の混在、及び中英混在の出力問題を最適化する。"}, "hunyuan-t1-20250529": {"description": "テキスト作成や作文の最適化、コードのフロントエンド、数学、論理推論など理系能力の強化、指示遵守能力の向上を図っています。"}, "hunyuan-t1-20250711": {"description": "高難度の数学、論理、コード能力を大幅に向上させ、モデルの出力安定性を最適化し、長文処理能力を強化しました。"}, "hunyuan-t1-latest": {"description": "業界初の超大規模Hybrid-Transformer-Mamba推論モデルであり、推論能力を拡張し、超高速なデコード速度を実現し、人間の好みにさらに整合します。"}, "hunyuan-t1-vision": {"description": "混元多モーダル理解の深層思考モデルで、多モーダルのネイティブ長思考チェーンをサポートし、さまざまな画像推論シナリオに優れています。理系の難問においては速思考モデルよりも包括的に向上しています。"}, "hunyuan-t1-vision-20250619": {"description": "混元の最新バージョンt1-vision多モーダル理解深層思考モデルで、マルチモーダルのネイティブな長い思考の連鎖をサポートし、前世代のデフォルトモデルに比べて全体的に性能が向上しています。"}, "hunyuan-turbo": {"description": "混元の新世代大規模言語モデルのプレビュー版で、全く新しい混合専門家モデル（MoE）構造を採用し、hunyuan-proに比べて推論効率が向上し、パフォーマンスも強化されています。"}, "hunyuan-turbo-20241223": {"description": "このバージョンの最適化：データ指令のスケーリングにより、モデルの汎用的な一般化能力を大幅に向上；数学、コード、論理推論能力を大幅に向上；テキスト理解と語彙理解に関連する能力を最適化；テキスト作成の内容生成の質を最適化。"}, "hunyuan-turbo-latest": {"description": "汎用体験の最適化、NLP理解、テキスト作成、雑談、知識問答、翻訳、分野などを含む；擬人性を向上させ、モデルの感情知能を最適化；意図が曖昧な時のモデルの能動的な明確化能力を向上；語彙解析に関する問題の処理能力を向上；創作の質とインタラクティブ性を向上；多段階体験を向上。"}, "hunyuan-turbo-vision": {"description": "混元の次世代視覚言語フラッグシップ大モデルで、全く新しい混合専門家モデル（MoE）構造を採用し、画像とテキストの理解に関連する基礎認識、コンテンツ作成、知識問答、分析推論などの能力が前世代モデルに比べて全面的に向上。"}, "hunyuan-turbos-20250313": {"description": "数学問題解決のステップスタイルを統一し、数学の多段階問答を強化。テキスト創作において回答スタイルを最適化し、AIらしさを排除し、文采を増加。"}, "hunyuan-turbos-20250416": {"description": "事前学習基盤のアップグレードにより、基盤の指示理解および遵守能力を強化；整合フェーズで数学、コード、論理、科学などの理系能力を強化；文芸創作の執筆品質、テキスト理解、翻訳精度、知識問答などの文系能力を向上；各分野のエージェント能力を強化し、特に多段階対話理解能力を重点的に強化。"}, "hunyuan-turbos-20250604": {"description": "事前学習基盤のアップグレードにより、執筆や読解力が向上し、コードや理系能力が大幅に強化され、複雑な指示の遵守能力も継続的に向上しています。"}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS混元フラッグシップ大モデルの最新バージョンで、より強力な思考能力と優れた体験効果を備えています。"}, "hunyuan-turbos-longtext-128k-20250325": {"description": "文書要約や文書質問応答などの長文タスクを得意とし、一般的なテキスト生成タスクにも対応可能です。長文の分析と生成に優れ、複雑で詳細な長文内容の処理ニーズに効果的に対応します。"}, "hunyuan-turbos-role-plus": {"description": "混元の最新ロールプレイングモデルで、混元公式による精調整訓練を経たロールプレイングモデルです。混元モデルを基にロールプレイングシナリオのデータセットで追加訓練されており、ロールプレイングシナリオでより良い基礎性能を持ちます。"}, "hunyuan-turbos-vision": {"description": "本モデルは画像と言語の理解シーンに適しており、混元の最新turbosに基づく次世代の視覚言語フラッグシップ大規模モデルです。画像に基づく実体認識、知識質問応答、コピーライティング、写真による問題解決などのタスクに焦点を当てており、前世代モデルに比べて全体的に性能が向上しています。"}, "hunyuan-turbos-vision-20250619": {"description": "混元の最新バージョンturbos-vision視覚言語フラッグシップ大規模モデルであり、画像に基づく実体認識、知識質問応答、コピーライティング、写真による問題解決などの画像と言語の理解関連タスクにおいて、前世代のデフォルトモデルに比べて全体的に性能が向上しています。"}, "hunyuan-vision": {"description": "混元の最新のマルチモーダルモデルで、画像とテキストの入力をサポートし、テキストコンテンツを生成します。"}, "image-01": {"description": "新しい画像生成モデルで、繊細な画質を持ち、テキストから画像、画像から画像の生成をサポートします。"}, "image-01-live": {"description": "画像生成モデルで、繊細な画質を持ち、テキストから画像生成と画風設定をサポートします。"}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 第4世代テキストから画像へのモデルシリーズ"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 第4世代テキストから画像へのモデルシリーズ ウルトラバージョン"}, "imagen4/preview": {"description": "Google の最高品質の画像生成モデル"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5は多様なシーンでのインテリジェントな対話ソリューションを提供します。"}, "internlm2.5-latest": {"description": "私たちの最新のモデルシリーズで、卓越した推論性能を持ち、1Mのコンテキスト長をサポートし、より強力な指示追従とツール呼び出し能力を備えています。"}, "internlm3-latest": {"description": "私たちの最新のモデルシリーズは、卓越した推論性能を持ち、同等のオープンソースモデルの中でリーダーシップを発揮しています。デフォルトで最新のInternLM3シリーズモデルを指します。"}, "internvl2.5-latest": {"description": "私たちが引き続きメンテナンスしている InternVL2.5 バージョンは、優れた安定した性能を持っています。デフォルトでは、私たちの最新の InternVL2.5 シリーズモデルに指向されており、現在は internvl2.5-78b に指向しています。"}, "internvl3-latest": {"description": "私たちの最新のマルチモーダル大規模モデルは、より強力な画像と言語の理解能力と長期的な画像理解能力を備えており、トップクラスのクローズドソースモデルに匹敵する性能を持っています。デフォルトでは、私たちの最新の InternVL シリーズモデルに指向されており、現在は internvl3-78b に指向しています。"}, "irag-1.0": {"description": "百度が独自開発したiRAG（image based RAG）は、検索強化型のテキストから画像生成技術で、百度検索の億単位の画像リソースと強力な基盤モデル能力を組み合わせ、非常にリアルな画像を生成します。従来のテキストから画像生成システムを大きく上回る効果を持ち、AI臭さがなく、コストも低減。iRAGは幻覚がなく、超リアルで即時利用可能な特徴を備えています。"}, "jamba-large": {"description": "私たちの最も強力で先進的なモデルで、企業レベルの複雑なタスクを処理するために設計されており、卓越した性能を備えています。"}, "jamba-mini": {"description": "同クラスで最も効率的なモデルで、速度と品質のバランスが取れ、より小型です。"}, "jina-deepsearch-v1": {"description": "深層検索は、ウェブ検索、読解、推論を組み合わせて、包括的な調査を行います。これは、あなたの研究タスクを受け入れる代理人として考えることができ、広範な検索を行い、何度も反復してから答えを提供します。このプロセスには、継続的な研究、推論、さまざまな視点からの問題解決が含まれます。これは、事前に訓練されたデータから直接答えを生成する標準的な大規模モデルや、一度きりの表面的な検索に依存する従来のRAGシステムとは根本的に異なります。"}, "kimi-k2": {"description": "Kimi-K2はMoonshot AIが提供する超強力なコードおよびエージェント能力を持つMoEアーキテクチャ基盤モデルで、総パラメータ1兆、活性化パラメータ320億。汎用知識推論、プログラミング、数学、エージェントなど主要カテゴリのベンチマーク性能で他の主流オープンソースモデルを上回っています。"}, "kimi-k2-0711-preview": {"description": "kimi-k2は強力なコードおよびエージェント能力を備えたMoEアーキテクチャの基盤モデルで、総パラメータ数は1兆、活性化パラメータは320億です。一般知識推論、プログラミング、数学、エージェントなどの主要カテゴリのベンチマーク性能テストで、K2モデルは他の主流オープンソースモデルを上回る性能を示しています。"}, "kimi-latest": {"description": "Kimi スマートアシスタント製品は最新の Kimi 大モデルを使用しており、まだ安定していない機能が含まれている可能性があります。画像理解をサポートし、リクエストのコンテキストの長さに応じて 8k/32k/128k モデルを請求モデルとして自動的に選択します。"}, "kimi-thinking-preview": {"description": "kimi-thinking-preview モデルは月の裏側が提供するマルチモーダル推論能力と汎用推論能力を備えたマルチモーダル思考モデルで、深い推論に優れ、より多くの難しい課題の解決を支援します。"}, "learnlm-1.5-pro-experimental": {"description": "LearnLMは、学習科学の原則に従って訓練された実験的なタスク特化型言語モデルで、教育や学習のシーンでシステムの指示に従い、専門的なメンターとして機能します。"}, "learnlm-2.0-flash-experimental": {"description": "LearnLM は、学習科学の原則に従って訓練された、タスク特化型の実験的言語モデルであり、教育や学習のシーンでシステムの指示に従い、専門のメンターとして機能します。"}, "lite": {"description": "Spark Liteは軽量な大規模言語モデルで、非常に低い遅延と高い処理能力を備えています。完全に無料でオープンであり、リアルタイムのオンライン検索機能をサポートしています。その迅速な応答特性により、低算力デバイスでの推論アプリケーションやモデルの微調整において優れたパフォーマンスを発揮し、特に知識問答、コンテンツ生成、検索シーンにおいて優れたコストパフォーマンスとインテリジェントな体験を提供します。"}, "llama-2-7b-chat": {"description": "Llama2は、Metaによって開発され、オープンソースの大型言語モデル（LLM）シリーズで、70億から700億パラメータの異なるスケールの生成テキストモデルです。アーキテクチャの面では、LLama2は最適化されたトランスフォーマーアーキテクチャを使用した自己回帰型言語モデルです。調整されたバージョンは、監視付き微調整（SFT）と人間のフィードバックを伴う強化学習（RLHF）を使用して、人間の有用性と安全性の好みに合わせています。Llama2は、Llamaシリーズに比べて多くの学術データセットで優れたパフォーマンスを示し、多くの他のモデルに設計と開発のアイデアを提供しています。"}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70Bは、より強力なAI推論能力を提供し、複雑なアプリケーションに適しており、非常に多くの計算処理をサポートし、高効率と精度を保証します。"}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8Bは、高効率モデルであり、迅速なテキスト生成能力を提供し、大規模な効率とコスト効果が求められるアプリケーションシナリオに非常に適しています。"}, "llama-3.1-instruct": {"description": "Llama 3.1 命令チューニングモデルは対話シナリオ向けに最適化されており、一般的な業界ベンチマークテストにおいて、多くの既存のオープンソースチャットモデルを凌駕しています。"}, "llama-3.2-11b-vision-instruct": {"description": "高解像度画像で優れた画像推論能力を発揮し、視覚理解アプリケーションに適しています。"}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2は、視覚データとテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的質問応答などのタスクで優れたパフォーマンスを発揮し、言語生成と視覚推論の間のギャップを埋めます。"}, "llama-3.2-90b-vision-instruct": {"description": "視覚理解エージェントアプリケーション向けの高度な画像推論能力を提供します。"}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2は、視覚データとテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的質問応答などのタスクで優れたパフォーマンスを発揮し、言語生成と視覚推論の間のギャップを埋めます。"}, "llama-3.2-vision-instruct": {"description": "Llama 3.2-Vision 命令ファインチューニングモデルは、視覚認識、画像推論、画像説明、および画像に関連する一般的な質問への回答に最適化されています。"}, "llama-3.3-70b-instruct": {"description": "Llama 3.3は、Llamaシリーズの最先端の多言語オープンソース大規模言語モデルで、非常に低コストで405Bモデルに匹敵する性能を体験できます。Transformer構造に基づき、監視付き微調整（SFT）と人間のフィードバックによる強化学習（RLHF）を通じて有用性と安全性を向上させています。その指示調整バージョンは多言語対話に最適化されており、複数の業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回る性能を発揮します。知識のカットオフ日は2023年12月です。"}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3は、70B（テキスト入力/テキスト出力）の事前学習と指示調整による生成モデルを持つ多言語大規模言語モデル（LLM）です。Llama 3.3の指示調整済みのプレーンテキストモデルは、多言語の対話ユースケースに最適化されており、一般的な業界ベンチマークで多くの利用可能なオープンソースおよびクローズドチャットモデルを上回っています。"}, "llama-3.3-instruct": {"description": "Llama 3.3 命令チューニングモデルは対話シナリオ向けに最適化されており、一般的な業界ベンチマークテストにおいて、多くの既存のオープンソースチャットモデルを凌駕しています。"}, "llama3-70b-8192": {"description": "Meta Llama 3 70Bは、比類のない複雑性処理能力を提供し、高要求プロジェクトに特化しています。"}, "llama3-8b-8192": {"description": "Meta Llama 3 8Bは、優れた推論性能を提供し、多様なシーンのアプリケーションニーズに適しています。"}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Useは、強力なツール呼び出し能力を提供し、複雑なタスクの効率的な処理をサポートします。"}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Useは、高効率なツール使用に最適化されたモデルであり、迅速な並列計算をサポートします。"}, "llama3.1": {"description": "Llama 3.1は、Metaが提供する先進的なモデルであり、最大405Bのパラメータをサポートし、複雑な対話、多言語翻訳、データ分析の分野で応用できます。"}, "llama3.1:405b": {"description": "Llama 3.1は、Metaが提供する先進的なモデルであり、最大405Bのパラメータをサポートし、複雑な対話、多言語翻訳、データ分析の分野で応用できます。"}, "llama3.1:70b": {"description": "Llama 3.1は、Metaが提供する先進的なモデルであり、最大405Bのパラメータをサポートし、複雑な対話、多言語翻訳、データ分析の分野で応用できます。"}, "llava": {"description": "LLaVAは、視覚エンコーダーとVicunaを組み合わせたマルチモーダルモデルであり、強力な視覚と言語理解を提供します。"}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7Bは、視覚処理能力を融合させ、視覚情報入力を通じて複雑な出力を生成します。"}, "llava:13b": {"description": "LLaVAは、視覚エンコーダーとVicunaを組み合わせたマルチモーダルモデルであり、強力な視覚と言語理解を提供します。"}, "llava:34b": {"description": "LLaVAは、視覚エンコーダーとVicunaを組み合わせたマルチモーダルモデルであり、強力な視覚と言語理解を提供します。"}, "mathstral": {"description": "MathΣtralは、科学研究と数学推論のために設計されており、効果的な計算能力と結果の解釈を提供します。"}, "max-32k": {"description": "Spark Max 32Kは大規模なコンテキスト処理能力を備え、より強力なコンテキスト理解と論理推論能力を持ち、32Kトークンのテキスト入力をサポートします。長文書の読解やプライベートな知識問答などのシーンに適しています。"}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct は、無問芯穹が完全に自主的に訓練した大規模言語モデルです。Megrez-3B-Instruct は、ソフトウェアとハードウェアの協調理念に基づき、高速推論、小型で高性能、そして非常に使いやすいエッジ側のスマートソリューションを目指しています。"}, "meta-llama-3-70b-instruct": {"description": "推論、コーディング、広範な言語アプリケーションに優れた70億パラメータの強力なモデルです。"}, "meta-llama-3-8b-instruct": {"description": "対話とテキスト生成タスクに最適化された多用途の80億パラメータモデルです。"}, "meta-llama-3.1-405b-instruct": {"description": "Llama 3.1の指示調整されたテキスト専用モデルは、多言語対話のユースケースに最適化されており、一般的な業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回ります。"}, "meta-llama-3.1-70b-instruct": {"description": "Llama 3.1の指示調整されたテキスト専用モデルは、多言語対話のユースケースに最適化されており、一般的な業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回ります。"}, "meta-llama-3.1-8b-instruct": {"description": "Llama 3.1の指示調整されたテキスト専用モデルは、多言語対話のユースケースに最適化されており、一般的な業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回ります。"}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B)は、優れた言語処理能力と素晴らしいインタラクション体験を提供します。"}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2は優れた言語処理能力と素晴らしいインタラクティブ体験を提供します。"}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B)は、強力なチャットモデルであり、複雑な対話ニーズをサポートします。"}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B)は、多言語サポートを提供し、豊富な分野知識をカバーしています。"}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2は視覚データとテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的質問応答などのタスクで優れた性能を発揮し、言語生成と視覚推論の間のギャップを埋めます。"}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2は視覚データとテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的質問応答などのタスクで優れた性能を発揮し、言語生成と視覚推論の間のギャップを埋めます。"}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2は視覚データとテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的質問応答などのタスクで優れた性能を発揮し、言語生成と視覚推論の間のギャップを埋めます。"}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3の多言語大規模言語モデル（LLM）は、70B（テキスト入力/テキスト出力）の事前訓練と指示調整生成モデルです。Llama 3.3の指示調整された純粋なテキストモデルは、多言語対話のユースケースに最適化されており、一般的な業界ベンチマークで多くの利用可能なオープンソースおよびクローズドチャットモデルを上回っています。"}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2は視覚データとテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的質問応答などのタスクで優れた性能を発揮し、言語生成と視覚推論の間のギャップを埋めます。"}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Liteは、高効率と低遅延が求められる環境に適しています。"}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turboは、卓越した言語理解と生成能力を提供し、最も厳しい計算タスクに適しています。"}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Liteは、リソースが制限された環境に適しており、優れたバランス性能を提供します。"}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turboは、高効率の大規模言語モデルであり、幅広いアプリケーションシナリオをサポートします。"}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405Bは事前学習と指示調整の強力なモデルです。"}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405BのLlama 3.1 Turboモデルは、大規模データ処理のために超大容量のコンテキストサポートを提供し、超大規模な人工知能アプリケーションで優れたパフォーマンスを発揮します。"}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1はMetaが提供する先進的なモデルで、最大405Bのパラメータをサポートし、複雑な対話、多言語翻訳、データ分析の分野で利用できます。"}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70Bモデルは微調整されており、高負荷アプリケーションに適しており、FP8に量子化されてより効率的な計算能力と精度を提供し、複雑なシナリオでの卓越したパフォーマンスを保証します。"}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8BモデルはFP8量子化を採用し、最大131,072のコンテキストトークンをサポートし、オープンソースモデルの中で際立っており、複雑なタスクに適しており、多くの業界ベンチマークを上回る性能を発揮します。"}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instructは高品質な対話シーンに最適化されており、さまざまな人間の評価において優れたパフォーマンスを示します。"}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instructは高品質な対話シーンに最適化されており、多くのクローズドソースモデルよりも優れた性能を持っています。"}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instructは高品質な対話のために設計されており、人間の評価において優れたパフォーマンスを示し、高いインタラクションシーンに特に適しています。"}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B InstructはMetaが発表した最新バージョンで、高品質な対話シーンに最適化されており、多くの先進的なクローズドソースモデルを上回る性能を発揮します。"}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1は多言語サポートを提供し、業界をリードする生成モデルの一つです。"}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2は、視覚とテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的な質問応答などのタスクで優れたパフォーマンスを発揮し、言語生成と視覚推論の間のギャップを超えています。"}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2は、視覚とテキストデータを組み合わせたタスクを処理することを目的としています。画像の説明や視覚的な質問応答などのタスクで優れたパフォーマンスを発揮し、言語生成と視覚推論の間のギャップを超えています。"}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3は、Llamaシリーズの最先端の多言語オープンソース大規模言語モデルで、非常に低コストで405Bモデルに匹敵する性能を体験できます。Transformer構造に基づき、監視付き微調整（SFT）と人間のフィードバックによる強化学習（RLHF）を通じて有用性と安全性を向上させています。その指示調整バージョンは多言語対話に最適化されており、複数の業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回る性能を発揮します。知識のカットオフ日は2023年12月です。"}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3は、Llamaシリーズの最先端の多言語オープンソース大規模言語モデルで、非常に低コストで405Bモデルに匹敵する性能を体験できます。Transformer構造に基づき、監視付き微調整（SFT）と人間のフィードバックによる強化学習（RLHF）を通じて有用性と安全性を向上させています。その指示調整バージョンは多言語対話に最適化されており、複数の業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回る性能を発揮します。知識のカットオフ日は2023年12月です。"}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instructは、Llama 3.1 Instructモデルの中で最大かつ最も強力なモデルであり、高度に進化した対話推論および合成データ生成モデルです。また、特定の分野での専門的な継続的な事前トレーニングや微調整の基盤としても使用できます。Llama 3.1が提供する多言語大規模言語モデル（LLMs）は、8B、70B、405Bのサイズ（テキスト入力/出力）を含む、事前トレーニングされた指示調整された生成モデルのセットです。Llama 3.1の指示調整されたテキストモデル（8B、70B、405B）は、多言語対話のユースケースに最適化されており、一般的な業界ベンチマークテストで多くの利用可能なオープンソースチャットモデルを上回っています。Llama 3.1は、さまざまな言語の商業および研究用途に使用されることを目的としています。指示調整されたテキストモデルは、アシスタントのようなチャットに適しており、事前トレーニングモデルはさまざまな自然言語生成タスクに適応できます。Llama 3.1モデルは、他のモデルを改善するためにその出力を利用することもサポートしており、合成データ生成や洗練にも対応しています。Llama 3.1は、最適化されたトランスフォーマーアーキテクチャを使用した自己回帰型言語モデルです。調整されたバージョンは、監視付き微調整（SFT）と人間のフィードバックを伴う強化学習（RLHF）を使用して、人間の助けや安全性に対する好みに適合させています。"}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Meta Llama 3.1 70B Instructの更新版で、拡張された128Kのコンテキスト長、多言語性、改善された推論能力を含んでいます。Llama 3.1が提供する多言語大型言語モデル（LLMs）は、8B、70B、405Bのサイズ（テキスト入力/出力）を含む一連の事前トレーニングされた、指示調整された生成モデルです。Llama 3.1の指示調整されたテキストモデル（8B、70B、405B）は、多言語対話用のユースケースに最適化されており、一般的な業界ベンチマークテストで多くの利用可能なオープンソースチャットモデルを超えています。Llama 3.1は多言語の商業および研究用途に使用されることを目的としています。指示調整されたテキストモデルはアシスタントのようなチャットに適しており、事前トレーニングモデルはさまざまな自然言語生成タスクに適応できます。Llama 3.1モデルは、他のモデルを改善するためにその出力を利用することもサポートしており、合成データ生成や精製を含みます。Llama 3.1は最適化されたトランスフォーマーアーキテクチャを使用した自己回帰型言語モデルです。調整版は、監視付き微調整（SFT）と人間のフィードバックを伴う強化学習（RLHF）を使用して、人間の助けや安全性に対する好みに適合させています。"}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Meta Llama 3.1 8B Instructの更新版で、拡張された128Kのコンテキスト長、多言語性、改善された推論能力を含んでいます。Llama 3.1が提供する多言語大型言語モデル（LLMs）は、8B、70B、405Bのサイズ（テキスト入力/出力）を含む一連の事前トレーニングされた、指示調整された生成モデルです。Llama 3.1の指示調整されたテキストモデル（8B、70B、405B）は、多言語対話用のユースケースに最適化されており、一般的な業界ベンチマークテストで多くの利用可能なオープンソースチャットモデルを超えています。Llama 3.1は多言語の商業および研究用途に使用されることを目的としています。指示調整されたテキストモデルはアシスタントのようなチャットに適しており、事前トレーニングモデルはさまざまな自然言語生成タスクに適応できます。Llama 3.1モデルは、他のモデルを改善するためにその出力を利用することもサポートしており、合成データ生成や精製を含みます。Llama 3.1は最適化されたトランスフォーマーアーキテクチャを使用した自己回帰型言語モデルです。調整版は、監視付き微調整（SFT）と人間のフィードバックを伴う強化学習（RLHF）を使用して、人間の助けや安全性に対する好みに適合させています。"}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3は、開発者、研究者、企業向けのオープンな大規模言語モデル（LLM）であり、生成AIのアイデアを構築、実験、責任を持って拡張するのを支援することを目的としています。世界的なコミュニティの革新の基盤システムの一部として、コンテンツ作成、対話AI、言語理解、研究開発、企業アプリケーションに非常に適しています。"}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3は、開発者、研究者、企業向けのオープンな大規模言語モデル（LLM）であり、生成AIのアイデアを構築、実験、責任を持って拡張するのを支援することを目的としています。世界的なコミュニティの革新の基盤システムの一部として、計算能力とリソースが限られたエッジデバイスや、より迅速なトレーニング時間に非常に適しています。"}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "高解像度画像で優れた画像推論能力を発揮し、視覚理解アプリケーションに適しています。"}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "視覚理解エージェントアプリケーション向けの高度な画像推論能力を備えています。"}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3はLlamaシリーズの最先端多言語オープンソース大型言語モデルで、非常に低コストで405Bモデルに匹敵する性能を体験できます。Transformer構造に基づき、教師あり微調整（SFT）と人間のフィードバックによる強化学習（RLHF）で有用性と安全性を向上。指示調整版は多言語対話に最適化され、多くの業界ベンチマークで多くのオープンソースおよびクローズドチャットモデルを上回る性能を示します。知識カットオフは2023年12月です。"}, "meta/Meta-Llama-3-70B-Instruct": {"description": "推論、コーディング、幅広い言語アプリケーションで優れた性能を発揮する強力な700億パラメータモデルです。"}, "meta/Meta-Llama-3-8B-Instruct": {"description": "対話およびテキスト生成タスクに最適化された多用途の80億パラメータモデルです。"}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1の指示調整済みテキストモデルで、多言語対話ユースケースに最適化され、多くの利用可能なオープンソースおよびクローズドチャットモデルの中で一般的な業界ベンチマークで優れた性能を発揮します。"}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1の指示調整済みテキストモデルで、多言語対話ユースケースに最適化され、多くの利用可能なオープンソースおよびクローズドチャットモデルの中で一般的な業界ベンチマークで優れた性能を発揮します。"}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1の指示調整済みテキストモデルで、多言語対話ユースケースに最適化され、多くの利用可能なオープンソースおよびクローズドチャットモデルの中で一般的な業界ベンチマークで優れた性能を発揮します。"}, "meta/llama-3.1-405b-instruct": {"description": "高度なLLMで、合成データ生成、知識蒸留、推論をサポートし、チャットボット、プログラミング、特定の分野のタスクに適しています。"}, "meta/llama-3.1-70b-instruct": {"description": "複雑な対話を可能にし、卓越した文脈理解、推論能力、テキスト生成能力を備えています。"}, "meta/llama-3.1-8b-instruct": {"description": "高度な最先端モデルで、言語理解、卓越した推論能力、テキスト生成能力を備えています。"}, "meta/llama-3.2-11b-vision-instruct": {"description": "最先端の視覚-言語モデルで、画像から高品質な推論を行うのが得意です。"}, "meta/llama-3.2-1b-instruct": {"description": "最先端の小型言語モデルで、言語理解、卓越した推論能力、テキスト生成能力を備えています。"}, "meta/llama-3.2-3b-instruct": {"description": "最先端の小型言語モデルで、言語理解、卓越した推論能力、テキスト生成能力を備えています。"}, "meta/llama-3.2-90b-vision-instruct": {"description": "最先端の視覚-言語モデルで、画像から高品質な推論を行うのが得意です。"}, "meta/llama-3.3-70b-instruct": {"description": "高度なLLMで、推論、数学、常識、関数呼び出しに優れています。"}, "microsoft/Phi-3-medium-128k-instruct": {"description": "同じPhi-3-mediumモデルですが、より大きなコンテキストサイズを持ち、RAGや少数ショットに適しています。"}, "microsoft/Phi-3-medium-4k-instruct": {"description": "140億パラメータモデルで、Phi-3-miniよりも品質が高く、高品質で推論集約型のデータに重点を置いています。"}, "microsoft/Phi-3-mini-128k-instruct": {"description": "同じPhi-3-miniモデルですが、より大きなコンテキストサイズを持ち、RAGや少数ショットに適しています。"}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Phi-3ファミリーで最小のメンバーで、品質と低遅延に最適化されています。"}, "microsoft/Phi-3-small-128k-instruct": {"description": "同じPhi-3-smallモデルですが、より大きなコンテキストサイズを持ち、RAGや少数ショットに適しています。"}, "microsoft/Phi-3-small-8k-instruct": {"description": "70億パラメータモデルで、Phi-3-miniよりも品質が高く、高品質で推論集約型のデータに重点を置いています。"}, "microsoft/Phi-3.5-mini-instruct": {"description": "Phi-3-miniモデルのアップデート版です。"}, "microsoft/Phi-3.5-vision-instruct": {"description": "Phi-3-visionモデルのアップデート版です。"}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2はMicrosoft AIが提供する言語モデルで、複雑な対話、多言語、推論、インテリジェントアシスタントの分野で特に優れた性能を発揮します。"}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22Bは、Microsoftの最先端AI Wizardモデルであり、非常に競争力のあるパフォーマンスを示しています。"}, "minicpm-v": {"description": "MiniCPM-VはOpenBMBが発表した次世代のマルチモーダル大モデルで、優れたOCR認識能力とマルチモーダル理解能力を備え、幅広いアプリケーションシーンをサポートします。"}, "ministral-3b-latest": {"description": "Ministral 3BはMistralの世界トップクラスのエッジモデルです。"}, "ministral-8b-latest": {"description": "Ministral 8BはMistralのコストパフォーマンスに優れたエッジモデルです。"}, "mistral": {"description": "Mistralは、Mistral AIがリリースした7Bモデルであり、多様な言語処理ニーズに適しています。"}, "mistral-ai/Mistral-Large-2411": {"description": "Mistralのフラッグシップモデルで、大規模な推論能力や高度に専門化された複雑なタスク（合成テキスト生成、コード生成、RAG、エージェント）に適しています。"}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemoは最先端の言語モデル（LLM）で、そのサイズカテゴリにおいて最先端の推論、世界知識、コーディング能力を備えています。"}, "mistral-ai/mistral-small-2503": {"description": "Mistral Smallは高効率かつ低遅延を必要とするあらゆる言語ベースのタスクに利用可能です。"}, "mistral-large": {"description": "Mixtral Largeは、Mistralのフラッグシップモデルであり、コード生成、数学、推論の能力を組み合わせ、128kのコンテキストウィンドウをサポートします。"}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 は、1230億のパラメータを有する先進的な高密度大規模言語モデル（LLM）で、最先端の推論能力、知識処理能力、およびコーディング能力を備えています。"}, "mistral-large-latest": {"description": "Mistral Largeは、フラッグシップの大モデルであり、多言語タスク、複雑な推論、コード生成に優れ、高端アプリケーションに理想的な選択肢です。"}, "mistral-medium-latest": {"description": "Mistral Medium 3は、8倍のコストで最先端のパフォーマンスを提供し、企業の展開を根本的に簡素化します。"}, "mistral-nemo": {"description": "Mistral Nemoは、Mistral AIとNVIDIAが共同で開発した高効率の12Bモデルです。"}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 大規模言語モデル（LLM）は、Mistral-Nemo-Base-2407の命令微調整バージョンです。"}, "mistral-small": {"description": "Mistral Smallは、高効率と低遅延を必要とする言語ベースのタスクで使用できます。"}, "mistral-small-latest": {"description": "Mistral Smallは、コスト効率が高く、迅速かつ信頼性の高い選択肢で、翻訳、要約、感情分析などのユースケースに適しています。"}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instructは、高性能で知られ、多言語タスクに適しています。"}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7Bは、オンデマンドのファインチューニングモデルであり、タスクに最適化された解答を提供します。"}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3は、高効率の計算能力と自然言語理解を提供し、幅広いアプリケーションに適しています。"}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7Bはコンパクトで高性能なモデルで、バッチ処理や分類、テキスト生成などの簡単なタスクに優れた推論能力を持っています。"}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B)は、超大規模な言語モデルであり、非常に高い処理要求をサポートします。"}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7Bは、一般的なテキストタスクに使用される事前訓練されたスパースミックス専門家モデルです。"}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7Bはスパースエキスパートモデルで、複数のパラメータを利用して推論速度を向上させ、多言語処理やコード生成タスクに適しています。"}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instructは速度最適化と長いコンテキストサポートを兼ね備えた高性能な業界標準モデルです。"}, "mistralai/mistral-nemo": {"description": "Mistral Nemoは多言語サポートと高性能プログラミングを備えた7.3Bパラメータモデルです。"}, "mixtral": {"description": "Mixtralは、Mistral AIのエキスパートモデルであり、オープンソースの重みを持ち、コード生成と言語理解のサポートを提供します。"}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7Bは、高い耐障害性を持つ並列計算能力を提供し、複雑なタスクに適しています。"}, "mixtral:8x22b": {"description": "Mixtralは、Mistral AIのエキスパートモデルであり、オープンソースの重みを持ち、コード生成と言語理解のサポートを提供します。"}, "moonshot-v1-128k": {"description": "Moonshot V1 128Kは、超長いコンテキスト処理能力を持つモデルであり、超長文の生成に適しており、複雑な生成タスクのニーズを満たし、最大128,000トークンの内容を処理でき、研究、学術、大型文書生成などのアプリケーションシーンに非常に適しています。"}, "moonshot-v1-128k-vision-preview": {"description": "<PERSON>i視覚モデル（moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-previewなどを含む）は、画像の内容を理解でき、画像の文字、色、物体の形状などを含みます。"}, "moonshot-v1-32k": {"description": "Moonshot V1 32Kは、中程度の長さのコンテキスト処理能力を提供し、32,768トークンを処理でき、さまざまな長文や複雑な対話の生成に特に適しており、コンテンツ作成、報告書生成、対話システムなどの分野で使用されます。"}, "moonshot-v1-32k-vision-preview": {"description": "<PERSON>i視覚モデル（moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-previewなどを含む）は、画像の内容を理解でき、画像の文字、色、物体の形状などを含みます。"}, "moonshot-v1-8k": {"description": "Moonshot V1 8Kは、短文生成タスクのために設計されており、高効率な処理性能を持ち、8,192トークンを処理でき、短い対話、速記、迅速なコンテンツ生成に非常に適しています。"}, "moonshot-v1-8k-vision-preview": {"description": "<PERSON>i視覚モデル（moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-previewなどを含む）は、画像の内容を理解でき、画像の文字、色、物体の形状などを含みます。"}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto は、現在のコンテキストで使用されているトークンの数に基づいて適切なモデルを選択できます。"}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B はオープンソースの大規模コードモデルであり、大規模な強化学習によって最適化されており、堅牢で直接本番投入可能なパッチを出力できます。このモデルは SWE-bench Verified で 60.4% の新記録を達成し、欠陥修正やコードレビューなどの自動化ソフトウェア工学タスクにおけるオープンソースモデルの記録を更新しました。"}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2は超強力なコードおよびエージェント能力を持つMoEアーキテクチャ基盤モデルで、総パラメータ1兆、活性化パラメータ320億。汎用知識推論、プログラミング、数学、エージェントなど主要カテゴリのベンチマーク性能で他の主流オープンソースモデルを上回っています。"}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 は、強力なコードおよびエージェント機能を備えたMoEアーキテクチャの基盤モデルで、総パラメータ数は1兆、活性化パラメータは320億です。一般的な知識推論、プログラミング、数学、エージェントなどの主要なベンチマーク性能テストにおいて、K2モデルは他の主流のオープンソースモデルを上回る性能を示しています。"}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8BはNous Hermes 2のアップグレード版で、最新の内部開発データセットを含んでいます。"}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70BはNVIDIAによってカスタマイズされた大規模言語モデルで、LLMが生成する応答がユーザーのクエリにどれだけ役立つかを向上させることを目的としています。このモデルはArena Hard、AlpacaEval 2 LC、GPT-4-Turbo MT-Benchなどのベンチマークテストで優れたパフォーマンスを示し、2024年10月1日現在、すべての自動整合ベンチマークテストで1位にランクされています。このモデルはRLHF（特にREINFORCE）、Llama-3.1-Nemotron-70B-Reward、HelpSteer2-Preferenceプロンプトを使用してLlama-3.1-70B-Instructモデルの基盤の上で訓練されています。"}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "独自の言語モデルで、比類のない精度と効率を提供します。"}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instructは、NVIDIAがカスタマイズした大規模言語モデルで、LLMが生成する応答の有用性を向上させることを目的としています。"}, "o1": {"description": "高度な推論と複雑な問題の解決に焦点を当てており、数学や科学のタスクを含みます。深いコンテキスト理解とエージェントワークフローを必要とするアプリケーションに非常に適しています。"}, "o1-mini": {"description": "o1-miniは、プログラミング、数学、科学のアプリケーションシーンに特化して設計された迅速で経済的な推論モデルです。このモデルは128Kのコンテキストを持ち、2023年10月の知識のカットオフがあります。"}, "o1-preview": {"description": "o1はOpenAIの新しい推論モデルで、広範な一般知識を必要とする複雑なタスクに適しています。このモデルは128Kのコンテキストを持ち、2023年10月の知識のカットオフがあります。"}, "o1-pro": {"description": "o1 シリーズモデルは強化学習により訓練されており、回答前に思考を行い、複雑な推論タスクを実行できます。o1-pro モデルはより多くの計算資源を使用してより深い思考を行い、継続的に高品質な回答を提供します。"}, "o3": {"description": "o3は全能で強力なモデルで、複数の分野で優れたパフォーマンスを発揮します。数学、科学、プログラミング、視覚推論タスクの新たな基準を設定しました。また、技術的な執筆や指示の遵守にも優れています。ユーザーはこれを利用して、テキスト、コード、画像を分析し、複雑な多段階の問題を解決できます。"}, "o3-deep-research": {"description": "o3-deep-research は、複雑な多段階のリサーチタスクを処理するために設計された、当社の最先端の深層リサーチモデルです。インターネットから情報を検索・統合できるほか、MCPコネクターを通じてお客様の独自データにアクセスし活用することも可能です。"}, "o3-mini": {"description": "o3-miniは、o1-miniと同じコストと遅延目標で高い知能を提供する最新の小型推論モデルです。"}, "o3-pro": {"description": "o3-pro モデルはより多くの計算を用いてより深く思考し、常により良い回答を提供します。Responses API のみで使用可能です。"}, "o4-mini": {"description": "o4-miniは私たちの最新の小型oシリーズモデルです。迅速かつ効果的な推論のために最適化されており、コーディングや視覚タスクで非常に高い効率と性能を発揮します。"}, "o4-mini-deep-research": {"description": "o4-mini-deep-research は、より迅速かつ手頃な価格の深層リサーチモデルで、複雑な多段階のリサーチタスクに最適です。インターネットから情報を検索・統合できるほか、MCPコネクターを通じてお客様の独自データにアクセスし活用することも可能です。"}, "open-codestral-mamba": {"description": "Codestral Mambaは、コード生成に特化したMamba 2言語モデルであり、高度なコードおよび推論タスクを強力にサポートします。"}, "open-mistral-7b": {"description": "Mistral 7Bは、コンパクトでありながら高性能なモデルであり、分類やテキスト生成などのバッチ処理や簡単なタスクに優れた推論能力を持っています。"}, "open-mistral-nemo": {"description": "Mistral Nemoは、Nvidiaと共同開発された12Bモデルであり、優れた推論およびコーディング性能を提供し、統合と置き換えが容易です。"}, "open-mixtral-8x22b": {"description": "Mixtral 8x22Bは、より大きなエキスパートモデルであり、複雑なタスクに特化し、優れた推論能力とより高いスループットを提供します。"}, "open-mixtral-8x7b": {"description": "Mixtral 8x7Bは、スパースエキスパートモデルであり、複数のパラメータを利用して推論速度を向上させ、多言語およびコード生成タスクの処理に適しています。"}, "openai/gpt-4.1": {"description": "GPT-4.1は、複雑なタスクに使用するためのフラッグシップモデルです。異なる分野での問題解決に非常に適しています。"}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 miniは、知性、速度、コストのバランスを提供し、多くのユースケースにおいて魅力的なモデルとなっています。"}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nanoは、最も速く、コスト効率の高いGPT-4.1モデルです。"}, "openai/gpt-4o": {"description": "ChatGPT-4oは動的モデルで、最新のバージョンを維持するためにリアルタイムで更新されます。強力な言語理解と生成能力を組み合わせており、顧客サービス、教育、技術サポートなどの大規模なアプリケーションシナリオに適しています。"}, "openai/gpt-4o-mini": {"description": "GPT-4o miniはOpenAIがGPT-4 Omniの後に発表した最新モデルで、画像とテキストの入力をサポートし、テキストを出力します。彼らの最先端の小型モデルとして、最近の他の最前線モデルよりもはるかに安価で、GPT-3.5 Turboよりも60%以上安価です。最先端の知能を維持しつつ、顕著なコストパフォーマンスを誇ります。GPT-4o miniはMMLUテストで82%のスコアを獲得し、現在チャットの好みでGPT-4よりも高い評価を得ています。"}, "openai/o1": {"description": "o1はOpenAIの新しい推論モデルで、画像とテキストの入力をサポートし、テキストを出力します。広範な一般知識を必要とする複雑なタスクに適しています。このモデルは20万トークンのコンテキストと2023年10月の知識カットオフを備えています。"}, "openai/o1-mini": {"description": "o1-miniは、プログラミング、数学、科学のアプリケーションシーンに特化して設計された迅速で経済的な推論モデルです。このモデルは128Kのコンテキストを持ち、2023年10月の知識のカットオフがあります。"}, "openai/o1-preview": {"description": "o1はOpenAIの新しい推論モデルで、広範な一般知識を必要とする複雑なタスクに適しています。このモデルは128Kのコンテキストを持ち、2023年10月の知識のカットオフがあります。"}, "openai/o3": {"description": "o3は、さまざまな分野で優れたパフォーマンスを発揮する強力な万能モデルです。数学、科学、プログラミング、視覚的推論タスクにおいて新たな基準を打ち立てました。また、技術的な執筆や指示の遵守にも優れています。ユーザーはこれを利用して、テキスト、コード、画像を分析し、複雑な多段階の問題を解決できます。"}, "openai/o3-mini": {"description": "o3-miniは、o1-miniと同じコストと遅延目標で高い知性を提供します。"}, "openai/o3-mini-high": {"description": "o3-mini高推論レベル版は、o1-miniと同じコストと遅延目標で高い知性を提供します。"}, "openai/o4-mini": {"description": "o4-miniは迅速かつ効果的な推論のために最適化されており、コーディングや視覚タスクで非常に高い効率と性能を発揮します。"}, "openai/o4-mini-high": {"description": "o4-mini高推論レベル版で、迅速かつ効果的な推論のために最適化されており、コーディングや視覚タスクで非常に高い効率と性能を発揮します。"}, "openrouter/auto": {"description": "コンテキストの長さ、テーマ、複雑さに応じて、あなたのリクエストはLlama 3 70B Instruct、Claude 3.5 Sonnet（自己調整）、またはGPT-4oに送信されます。"}, "phi3": {"description": "Phi-3は、Microsoftが提供する軽量オープンモデルであり、高効率な統合と大規模な知識推論に適しています。"}, "phi3:14b": {"description": "Phi-3は、Microsoftが提供する軽量オープンモデルであり、高効率な統合と大規模な知識推論に適しています。"}, "pixtral-12b-2409": {"description": "Pixtralモデルは、グラフと画像理解、文書質問応答、多モーダル推論、指示遵守などのタスクで強力な能力を発揮し、自然な解像度とアスペクト比で画像を取り込み、最大128Kトークンの長いコンテキストウィンドウで任意の数の画像を処理できます。"}, "pixtral-large-latest": {"description": "Pixtral Largeは、1240億のパラメータを持つオープンソースのマルチモーダルモデルで、Mistral Large 2に基づいて構築されています。これは私たちのマルチモーダルファミリーの中で2番目のモデルであり、最先端の画像理解能力を示しています。"}, "pro-128k": {"description": "Spark Pro 128Kは特大のコンテキスト処理能力を備え、最大128Kのコンテキスト情報を処理できます。特に、全体を通じての分析や長期的な論理的関連性の処理が必要な長文コンテンツに適しており、複雑なテキストコミュニケーションにおいて滑らかで一貫した論理と多様な引用サポートを提供します。"}, "qvq-72b-preview": {"description": "QVQモデルはQwenチームによって開発された実験的研究モデルで、視覚推論能力の向上に特化しており、特に数学推論の分野で優れた性能を発揮。"}, "qvq-max": {"description": "通義千問QVQ視覚推論モデルで、視覚入力と思考連鎖出力をサポートし、数学、プログラミング、視覚分析、創作および汎用タスクにおいてより強力な能力を発揮します。"}, "qvq-plus": {"description": "視覚推論モデルです。視覚入力と思考チェーン出力をサポートし、qvq-max モデルの後継である plus バージョンです。qvq-max モデルに比べて推論速度が速く、効果とコストのバランスが優れています。"}, "qwen-coder-plus": {"description": "通義千問コードモデルです。"}, "qwen-coder-turbo": {"description": "通義千問コードモデルです。"}, "qwen-coder-turbo-latest": {"description": "通義千問のコードモデルです。"}, "qwen-long": {"description": "通義千問超大規模言語モデルで、長文コンテキストや長文書、複数文書に基づく対話機能をサポートしています。"}, "qwen-math-plus": {"description": "通義千問数学モデルは数学問題解決に特化した言語モデルです。"}, "qwen-math-plus-latest": {"description": "通義千問の数学モデルは、数学の問題解決に特化した言語モデルです。"}, "qwen-math-turbo": {"description": "通義千問数学モデルは数学問題解決に特化した言語モデルです。"}, "qwen-math-turbo-latest": {"description": "通義千問の数学モデルは、数学の問題解決に特化した言語モデルです。"}, "qwen-max": {"description": "通義千問の千億レベルの超大規模言語モデルで、中国語、英語などさまざまな言語の入力をサポートしています。現在、通義千問2.5製品バージョンの背後にあるAPIモデルです。"}, "qwen-omni-turbo": {"description": "Qwen-Omniシリーズモデルは、動画、音声、画像、テキストなど多様なモーダルの入力をサポートし、音声とテキストを出力します。"}, "qwen-plus": {"description": "通義千問の超大規模言語モデルの強化版で、中国語、英語などさまざまな言語の入力をサポートしています。"}, "qwen-turbo": {"description": "通義千問の超大規模言語モデルで、中国語、英語などさまざまな言語の入力をサポートしています。"}, "qwen-vl-chat-v1": {"description": "通義千問VLは、複数の画像、多段階の質問応答、創作などの柔軟なインタラクション方式をサポートするモデルです。"}, "qwen-vl-max": {"description": "通義千問超大規模視覚言語モデル。強化版と比較して視覚推論能力と指示遵守能力をさらに向上させ、より高い視覚認知レベルを提供します。"}, "qwen-vl-max-latest": {"description": "通義千問の超大規模視覚言語モデル。強化版に比べて、視覚推論能力と指示遵守能力をさらに向上させ、より高い視覚認識と認知レベルを提供します。"}, "qwen-vl-ocr": {"description": "通義千問OCRは文字抽出に特化した専用モデルで、文書、表、試験問題、手書き文字などの画像からの文字抽出能力に注力しています。対応言語は中国語、英語、フランス語、日本語、韓国語、ドイツ語、ロシア語、イタリア語、ベトナム語、アラビア語です。"}, "qwen-vl-plus": {"description": "通義千問大規模視覚言語モデルの強化版。細部認識能力と文字認識能力を大幅に向上させ、100万画素以上の解像度および任意の縦横比の画像をサポートします。"}, "qwen-vl-plus-latest": {"description": "通義千問の大規模視覚言語モデルの強化版。詳細認識能力と文字認識能力を大幅に向上させ、100万ピクセル以上の解像度と任意のアスペクト比の画像をサポートします。"}, "qwen-vl-v1": {"description": "Qwen-7B言語モデルを初期化し、画像モデルを追加した、画像入力解像度448の事前トレーニングモデルです。"}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2は全く新しいQwen大規模言語モデルシリーズです。Qwen2 7Bはトランスフォーマーに基づくモデルで、言語理解、多言語能力、プログラミング、数学、推論において優れた性能を示しています。"}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2は全く新しい大型言語モデルシリーズで、より強力な理解と生成能力を備えています。"}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VLはQwen-VLモデルの最新のイテレーションで、MathVista、DocVQA、RealWorldQA、MTVQAなどの視覚理解ベンチマークテストで最先端の性能を達成しました。Qwen2-VLは20分以上のビデオを理解し、高品質なビデオベースの質問応答、対話、コンテンツ作成を行うことができます。また、複雑な推論と意思決定能力を備えており、モバイルデバイスやロボットなどと統合し、視覚環境とテキスト指示に基づいて自動操作を行うことができます。英語と中国語に加えて、Qwen2-VLは現在、ほとんどのヨーロッパ言語、日本語、韓国語、アラビア語、ベトナム語など、異なる言語のテキストを画像内で理解することもサポートしています。"}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instructはアリババクラウドが発表した最新の大言語モデルシリーズの一つです。この72Bモデルはコーディングや数学などの分野で顕著な能力の向上を示しています。このモデルは29以上の言語をカバーする多言語サポートも提供しており、中国語、英語などが含まれています。モデルは指示の追従、構造化データの理解、構造化出力（特にJSON）の生成においても顕著な向上を示しています。"}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instructはアリババクラウドが発表した最新の大言語モデルシリーズの一つです。この32Bモデルはコーディングや数学などの分野で顕著な能力の向上を示しています。このモデルは29以上の言語をカバーする多言語サポートも提供しており、中国語、英語などが含まれています。モデルは指示の追従、構造化データの理解、構造化出力（特にJSON）の生成においても顕著な向上を示しています。"}, "qwen/qwen2.5-7b-instruct": {"description": "中国語と英語に対応したLLMで、言語、プログラミング、数学、推論などの分野に特化しています。"}, "qwen/qwen2.5-coder-32b-instruct": {"description": "高度なLLMで、コード生成、推論、修正をサポートし、主流のプログラミング言語をカバーしています。"}, "qwen/qwen2.5-coder-7b-instruct": {"description": "強力な中型コードモデルで、32Kのコンテキスト長をサポートし、多言語プログラミングに優れています。"}, "qwen/qwen3-14b": {"description": "Qwen3-14BはQwen3シリーズの中で、148億パラメータの密な因果言語モデルであり、複雑な推論と効率的な対話のために設計されています。数学、プログラミング、論理推論などのタスクのための「思考」モードと一般的な対話のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは微調整されており、指示の遵守、エージェントツールの使用、創造的な執筆、100以上の言語と方言にわたる多言語タスクに対応しています。32Kトークンのコンテキストをネイティブに処理し、YaRNベースの拡張を使用して131Kトークンに拡張可能です。"}, "qwen/qwen3-14b:free": {"description": "Qwen3-14BはQwen3シリーズの中で、148億パラメータの密な因果言語モデルであり、複雑な推論と効率的な対話のために設計されています。数学、プログラミング、論理推論などのタスクのための「思考」モードと一般的な対話のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは微調整されており、指示の遵守、エージェントツールの使用、創造的な執筆、100以上の言語と方言にわたる多言語タスクに対応しています。32Kトークンのコンテキストをネイティブに処理し、YaRNベースの拡張を使用して131Kトークンに拡張可能です。"}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22BはQwenによって開発された235Bパラメータの専門家混合（MoE）モデルで、各前方伝播で22Bパラメータをアクティブ化します。複雑な推論、数学、コードタスクのための「思考」モードと、一般的な対話の効率のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは強力な推論能力、100以上の言語と方言にわたる多言語サポート、高度な指示遵守、エージェントツール呼び出し能力を示しています。32Kトークンのコンテキストウィンドウをネイティブに処理し、YaRNベースの拡張を使用して131Kトークンに拡張可能です。"}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22BはQwenによって開発された235Bパラメータの専門家混合（MoE）モデルで、各前方伝播で22Bパラメータをアクティブ化します。複雑な推論、数学、コードタスクのための「思考」モードと、一般的な対話の効率のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは強力な推論能力、100以上の言語と方言にわたる多言語サポート、高度な指示遵守、エージェントツール呼び出し能力を示しています。32Kトークンのコンテキストウィンドウをネイティブに処理し、YaRNベースの拡張を使用して131Kトークンに拡張可能です。"}, "qwen/qwen3-30b-a3b": {"description": "Qwen3はQwenの大規模言語モデルシリーズの最新世代で、密な専門家混合（MoE）アーキテクチャを持ち、推論、多言語サポート、高度なエージェントタスクにおいて優れた性能を発揮します。複雑な推論の思考モードと効率的な対話の非思考モードの間をシームレスに切り替える独自の能力により、多機能で高品質なパフォーマンスが保証されています。\n\nQwen3は、QwQやQwen2.5などの以前のモデルに対して大幅に優れており、卓越した数学、コーディング、常識推論、創造的な執筆、インタラクティブな対話能力を提供します。Qwen3-30B-A3Bバリアントは、305億のパラメータ（33億のアクティブパラメータ）、48層、128の専門家（各タスクで8つをアクティブ化）を含み、最大131Kトークンのコンテキストをサポート（YaRNを使用）し、オープンソースモデルの新たな基準を確立しています。"}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3はQwenの大規模言語モデルシリーズの最新世代で、密な専門家混合（MoE）アーキテクチャを持ち、推論、多言語サポート、高度なエージェントタスクにおいて優れた性能を発揮します。複雑な推論の思考モードと効率的な対話の非思考モードの間をシームレスに切り替える独自の能力により、多機能で高品質なパフォーマンスが保証されています。\n\nQwen3は、QwQやQwen2.5などの以前のモデルに対して大幅に優れており、卓越した数学、コーディング、常識推論、創造的な執筆、インタラクティブな対話能力を提供します。Qwen3-30B-A3Bバリアントは、305億のパラメータ（33億のアクティブパラメータ）、48層、128の専門家（各タスクで8つをアクティブ化）を含み、最大131Kトークンのコンテキストをサポート（YaRNを使用）し、オープンソースモデルの新たな基準を確立しています。"}, "qwen/qwen3-32b": {"description": "Qwen3-32BはQwen3シリーズの中で、328億パラメータの密な因果言語モデルであり、複雑な推論と効率的な対話のために最適化されています。数学、コーディング、論理推論などのタスクのための「思考」モードと、より迅速で一般的な対話のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは指示の遵守、エージェントツールの使用、創造的な執筆、100以上の言語と方言にわたる多言語タスクにおいて強力な性能を発揮します。32Kトークンのコンテキストをネイティブに処理し、YaRNベースの拡張を使用して131Kトークンに拡張可能です。"}, "qwen/qwen3-32b:free": {"description": "Qwen3-32BはQwen3シリーズの中で、328億パラメータの密な因果言語モデルであり、複雑な推論と効率的な対話のために最適化されています。数学、コーディング、論理推論などのタスクのための「思考」モードと、より迅速で一般的な対話のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは指示の遵守、エージェントツールの使用、創造的な執筆、100以上の言語と方言にわたる多言語タスクにおいて強力な性能を発揮します。32Kトークンのコンテキストをネイティブに処理し、YaRNベースの拡張を使用して131Kトークンに拡張可能です。"}, "qwen/qwen3-8b:free": {"description": "Qwen3-8BはQwen3シリーズの中で、82億パラメータの密な因果言語モデルであり、推論集約型タスクと効率的な対話のために設計されています。数学、コーディング、論理推論のための「思考」モードと一般的な対話のための「非思考」モードの間をシームレスに切り替えることができます。このモデルは微調整されており、指示の遵守、エージェント統合、創造的な執筆、100以上の言語と方言にわたる多言語使用に対応しています。32Kトークンのコンテキストウィンドウをネイティブにサポートし、YaRNを使用して131Kトークンに拡張可能です。"}, "qwen2": {"description": "Qwen2は、<PERSON><PERSON><PERSON><PERSON>新世代大規模言語モデルであり、優れた性能で多様なアプリケーションニーズをサポートします。"}, "qwen2-72b-instruct": {"description": "Qwen2は、Qwenチームが発表した次世代の大型言語モデルシリーズです。これは、Transformerアーキテクチャに基づいており、SwiGLU活性化関数、注意QKVバイアス、グループクエリ注意、スライディングウィンドウ注意と全注意の混合などの技術を採用しています。さらに、Qwenチームは、さまざまな自然言語とコードに適応するトークナイザーを改善しました。"}, "qwen2-7b-instruct": {"description": "Qwen2は、Qwenチームが発表した次世代の大型言語モデルシリーズです。これは、Transformerアーキテクチャに基づいており、SwiGLU活性化関数、注意QKVバイアス、グループクエリ注意、スライディングウィンドウ注意と全注意の混合などの技術を採用しています。さらに、Qwenチームは、さまざまな自然言語とコードに適応するトークナイザーを改善しました。"}, "qwen2.5": {"description": "Qwen2.5はAlibabaの次世代大規模言語モデルで、優れた性能を持ち、多様なアプリケーションのニーズをサポートします。"}, "qwen2.5-14b-instruct": {"description": "通義千問2.5の対外オープンソースの14B規模のモデルです。"}, "qwen2.5-14b-instruct-1m": {"description": "通義千問2.5が公開した72B規模のモデルです。"}, "qwen2.5-32b-instruct": {"description": "通義千問2.5の対外オープンソースの32B規模のモデルです。"}, "qwen2.5-72b-instruct": {"description": "通義千問2.5の対外オープンソースの72B規模のモデルです。"}, "qwen2.5-7b-instruct": {"description": "通義千問2.5の対外オープンソースの7B規模のモデルです。"}, "qwen2.5-coder-1.5b-instruct": {"description": "通義千問コードモデルのオープンソース版です。"}, "qwen2.5-coder-14b-instruct": {"description": "通義千問コードモデルのオープンソース版です。"}, "qwen2.5-coder-32b-instruct": {"description": "通義千問コードモデルのオープンソース版。"}, "qwen2.5-coder-7b-instruct": {"description": "通義千問のコードモデルのオープンソース版です。"}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coderは、Qwenシリーズの最新のコード専用大規模言語モデルです（旧称：CodeQwen）。"}, "qwen2.5-instruct": {"description": "Qwen2.5はQwen大規模言語モデルの最新シリーズです。Qwen2.5では、5億から72億までのパラメータ範囲を持つ複数のベース言語モデルと命令チューニング言語モデルをリリースしました。"}, "qwen2.5-math-1.5b-instruct": {"description": "<PERSON>wen-Mathモデルは、強力な数学的問題解決能力を備えています。"}, "qwen2.5-math-72b-instruct": {"description": "<PERSON>wen-Mathモデルは、強力な数学の問題解決能力を持っています。"}, "qwen2.5-math-7b-instruct": {"description": "<PERSON>wen-Mathモデルは、強力な数学の問題解決能力を持っています。"}, "qwen2.5-omni-7b": {"description": "Qwen-Omniシリーズモデルは、動画、音声、画像、テキストなどの多様なモダリティのデータを入力としてサポートし、音声とテキストを出力します。"}, "qwen2.5-vl-32b-instruct": {"description": "Qwen2.5-VLシリーズモデルは、モデルの知能レベル、実用性、適応性を向上させ、自然な会話、コンテンツ作成、専門知識サービス、コード開発などのシナリオにおいてより優れたパフォーマンスを発揮します。32Bバージョンでは強化学習技術を用いてモデルを最適化しており、Qwen2.5 VLシリーズの他のモデルと比較して、人間の嗜好に合致した出力スタイル、複雑な数学問題の推論能力、および画像の細粒度理解と推論能力を提供します。"}, "qwen2.5-vl-72b-instruct": {"description": "指示に従い、数学、問題解決、コード全体の向上、万物認識能力の向上を実現し、多様な形式で視覚要素を直接的に正確に特定し、長い動画ファイル（最大10分）を理解し、秒単位のイベント時刻を特定でき、時間の前後や速さを理解し、解析と特定能力に基づいてOSやモバイルのエージェントを操作し、重要な情報抽出能力とJson形式出力能力が強化されています。このバージョンは72Bバージョンで、本シリーズの中で最も強力なバージョンです。"}, "qwen2.5-vl-7b-instruct": {"description": "指示に従い、数学、問題解決、コード全体の向上、万物認識能力の向上を実現し、多様な形式で視覚要素を直接的に正確に特定し、長い動画ファイル（最大10分）を理解し、秒単位のイベント時刻を特定でき、時間の前後や速さを理解し、解析と特定能力に基づいてOSやモバイルのエージェントを操作し、重要な情報抽出能力とJson形式出力能力が強化されています。このバージョンは72Bバージョンで、本シリーズの中で最も強力なバージョンです。"}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VLは、Qwenモデルファミリーにおける最新の視覚言語モデルです。"}, "qwen2.5:0.5b": {"description": "Qwen2.5はAlibabaの次世代大規模言語モデルで、優れた性能を持ち、多様なアプリケーションのニーズをサポートします。"}, "qwen2.5:1.5b": {"description": "Qwen2.5はAlibabaの次世代大規模言語モデルで、優れた性能を持ち、多様なアプリケーションのニーズをサポートします。"}, "qwen2.5:72b": {"description": "Qwen2.5はAlibabaの次世代大規模言語モデルで、優れた性能を持ち、多様なアプリケーションのニーズをサポートします。"}, "qwen2:0.5b": {"description": "Qwen2は、<PERSON><PERSON><PERSON><PERSON>新世代大規模言語モデルであり、優れた性能で多様なアプリケーションニーズをサポートします。"}, "qwen2:1.5b": {"description": "Qwen2は、<PERSON><PERSON><PERSON><PERSON>新世代大規模言語モデルであり、優れた性能で多様なアプリケーションニーズをサポートします。"}, "qwen2:72b": {"description": "Qwen2は、<PERSON><PERSON><PERSON><PERSON>新世代大規模言語モデルであり、優れた性能で多様なアプリケーションニーズをサポートします。"}, "qwen3": {"description": "Qwen3は、<PERSON><PERSON>baの次世代大規模言語モデルであり、優れた性能で多様なアプリケーションニーズをサポートします。"}, "qwen3-0.6b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-1.7b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-14b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-235b-a22b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-235b-a22b-instruct-2507": {"description": "Qwen3ベースの非思考モードオープンソースモデルで、前バージョン（通義千問3-235B-A22B）に比べ、主観的創作能力とモデルの安全性がわずかに向上しています。"}, "qwen3-235b-a22b-thinking-2507": {"description": "Qwen3ベースの思考モードオープンソースモデルで、前バージョン（通義千問3-235B-A22B）に比べ、論理能力、汎用能力、知識強化、創作能力が大幅に向上し、高難度の強推論シナリオに適しています。"}, "qwen3-30b-a3b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-30b-a3b-instruct-2507": {"description": "前バージョン（Qwen3-30B-A3B）に比べて、中国語・英語および多言語の全体的な汎用能力が大幅に向上しました。主観的かつオープンなタスクに特化した最適化により、ユーザーの好みにより適合し、より有用な応答を提供できます。"}, "qwen3-30b-a3b-thinking-2507": {"description": "Qwen3の思考モードオープンソースモデルで、前バージョン（通義千問3-30B-A3B）に比べて論理能力、汎用能力、知識強化および創作能力が大幅に向上しており、高難度の強推論シナリオに適しています。"}, "qwen3-32b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-4b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-8b": {"description": "Qwen3は能力が大幅に向上した新世代の通義千問大モデルで、推論、一般、エージェント、多言語などの複数のコア能力において業界のリーダーレベルに達し、思考モードの切り替えをサポートしています。"}, "qwen3-coder-480b-a35b-instruct": {"description": "通義千問のコードモデルオープンソース版。最新のqwen3-coder-480b-a35b-instructはQwen3ベースのコード生成モデルで、強力なコーディングエージェント能力を持ち、ツール呼び出しや環境とのインタラクションに優れ、自律的なプログラミングが可能で、コード能力と汎用能力を兼ね備えています。"}, "qwen3-coder-plus": {"description": "通義千問のコードモデル。最新のQwen3-Coder-PlusシリーズモデルはQwen3ベースのコード生成モデルで、強力なコーディングエージェント能力を持ち、ツール呼び出しや環境とのインタラクションに優れ、自律的なプログラミングが可能で、コード能力と汎用能力を兼ね備えています。"}, "qwq": {"description": "QwQはAIの推論能力を向上させることに特化した実験的研究モデルです。"}, "qwq-32b": {"description": "Qwen2.5-32Bモデルに基づいて訓練されたQwQ推論モデルは、強化学習を通じてモデルの推論能力を大幅に向上させました。モデルの数学コードなどのコア指標（AIME 24/25、LiveCodeBench）および一部の一般的な指標（IFEval、LiveBenchなど）は、DeepSeek-R1のフルバージョンに達しており、すべての指標は同じくQwen2.5-32Bに基づくDeepSeek-R1-Distill-Qwen-32Bを大幅に上回っています。"}, "qwq-32b-preview": {"description": "QwQモデルはQwenチームによって開発された実験的な研究モデルで、AIの推論能力を強化することに焦点を当てています。"}, "qwq-plus": {"description": "Qwen2.5モデルを基に訓練されたQwQ推論モデルで、強化学習によりモデルの推論能力を大幅に向上させました。数学やコードなどの主要指標（AIME 24/25、LiveCodeBench）および一部の汎用指標（IFEval、LiveBenchなど）はDeepSeek-R1フルスペック版の水準に達しています。"}, "qwq_32b": {"description": "Qwenシリーズの中規模推論モデルです。従来の指示調整モデルと比較して、思考と推論能力を持つQwQは、特に難題を解決する際に下流タスクの性能を大幅に向上させることができます。"}, "r1-1776": {"description": "R1-1776は、DeepSeek R1モデルの一つのバージョンで、後処理を経て、検閲されていない偏りのない事実情報を提供します。"}, "solar-mini": {"description": "Solar MiniはコンパクトなLLMで、GPT-3.5を上回る性能を持ち、強力な多言語能力を備え、英語と韓国語をサポートし、高効率でコンパクトなソリューションを提供します。"}, "solar-mini-ja": {"description": "Solar Mini (Ja) はSolar Miniの能力を拡張し、日本語に特化しながら、英語と韓国語の使用においても高効率で卓越した性能を維持しています。"}, "solar-pro": {"description": "Solar ProはUpstageが発表した高インテリジェンスLLMで、単一GPUの指示追従能力に特化しており、IFEvalスコアは80以上です。現在は英語をサポートしており、正式版は2024年11月にリリース予定で、言語サポートとコンテキスト長を拡張します。"}, "sonar": {"description": "検索コンテキストに基づく軽量検索製品で、Sonar Proよりも速く、安価です。"}, "sonar-deep-research": {"description": "Deep Researchは、専門家による包括的な研究を行い、それをアクセス可能で実行可能なレポートにまとめます。"}, "sonar-pro": {"description": "検索コンテキストをサポートする高度な検索製品で、高度なクエリとフォローアップをサポートします。"}, "sonar-reasoning": {"description": "DeepSeek推論モデルによってサポートされる新しいAPI製品です。"}, "sonar-reasoning-pro": {"description": "DeepSeek推論モデルによってサポートされる新しいAPI製品。"}, "stable-diffusion-3-medium": {"description": "Stability AIがリリースした最新のテキストから画像生成大規模モデルです。前世代の利点を継承しつつ、画像品質、テキスト理解、スタイル多様性の面で大幅に改善され、複雑な自然言語プロンプトをより正確に解釈し、より精密かつ多様な画像を生成可能です。"}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-largeは8億パラメータを持つマルチモーダル拡散トランスフォーマー（MMDiT）テキストから画像生成モデルで、卓越した画像品質とプロンプト適合性を備え、100万画素の高解像度画像生成をサポートし、一般的な消費者向けハードウェア上で効率的に動作します。"}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turboはstable-diffusion-3.5-largeを基に、敵対的拡散蒸留（ADD）技術を採用したモデルで、より高速な生成速度を実現しています。"}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5はstable-diffusion-v1.2のチェックポイント重みを初期化に使用し、「laion-aesthetics v2 5+」で512x512解像度にて595kステップの微調整を行い、テキスト条件付けを10%削減して無分類器ガイダンスサンプリングを改善しました。"}, "stable-diffusion-xl": {"description": "stable-diffusion-xlはv1.5に比べ大幅な改良が施され、現行のオープンソーステキストから画像生成SOTAモデルmidjourneyと同等の効果を持ちます。具体的な改良点は、unetバックボーンが従来の3倍の大きさ、生成画像の品質向上のためのリファインメントモジュール追加、効率的なトレーニング技術の導入などです。"}, "stable-diffusion-xl-base-1.0": {"description": "Stability AIが開発しオープンソース化したテキストから画像生成大規模モデルで、業界トップクラスの創造的画像生成能力を持ち、優れた指示理解能力を備え、逆プロンプト定義による精密な内容生成をサポートします。"}, "step-1-128k": {"description": "性能とコストのバランスを取り、一般的なシナリオに適しています。"}, "step-1-256k": {"description": "超長コンテキスト処理能力を持ち、特に長文書分析に適しています。"}, "step-1-32k": {"description": "中程度の長さの対話をサポートし、さまざまなアプリケーションシナリオに適しています。"}, "step-1-8k": {"description": "小型モデルであり、軽量なタスクに適しています。"}, "step-1-flash": {"description": "高速モデルであり、リアルタイムの対話に適しています。"}, "step-1.5v-mini": {"description": "このモデルは、強力なビデオ理解能力を備えています。"}, "step-1o-turbo-vision": {"description": "このモデルは強力な画像理解能力を持ち、数理、コード分野で1oより優れています。モデルは1oよりも小さく、出力速度が速くなっています。"}, "step-1o-vision-32k": {"description": "このモデルは強力な画像理解能力を持っています。step-1vシリーズモデルと比較して、より優れた視覚性能を発揮します。"}, "step-1v-32k": {"description": "視覚入力をサポートし、多モーダルインタラクション体験を強化します。"}, "step-1v-8k": {"description": "小型ビジュアルモデルで、基本的なテキストと画像のタスクに適しています。"}, "step-1x-edit": {"description": "本モデルは画像編集タスクに特化しており、ユーザーが提供した画像とテキスト記述に基づき、画像の修正や強化を行います。テキスト記述やサンプル画像など多様な入力形式をサポートし、ユーザーの意図を理解して要求に合致した画像編集結果を生成します。"}, "step-1x-medium": {"description": "本モデルは強力な画像生成能力を持ち、テキスト記述を入力としてサポートします。ネイティブの中国語対応により、中国語テキスト記述の理解と処理が向上し、テキストの意味情報をより正確に捉えて画像特徴に変換し、より精密な画像生成を実現します。入力に基づき高解像度かつ高品質な画像を生成し、一定のスタイル転送能力も備えています。"}, "step-2-16k": {"description": "大規模なコンテキストインタラクションをサポートし、複雑な対話シナリオに適しています。"}, "step-2-16k-exp": {"description": "step-2モデルの実験版で、最新の機能を含み、継続的に更新されています。正式な生産環境での使用は推奨されません。"}, "step-2-mini": {"description": "新世代の自社開発のAttentionアーキテクチャMFAに基づく超高速大モデルで、非常に低コストでstep1と同様の効果を達成しつつ、より高いスループットと迅速な応答遅延を維持しています。一般的なタスクを処理でき、コード能力において特長を持っています。"}, "step-2x-large": {"description": "階躍星辰の新世代画像生成モデルで、画像生成タスクに特化し、ユーザーが提供したテキスト記述に基づき高品質な画像を生成します。新モデルは画像の質感がよりリアルで、中英両言語の文字生成能力が強化されています。"}, "step-r1-v-mini": {"description": "このモデルは強力な画像理解能力を持つ推論大モデルで、画像とテキスト情報を処理し、深い思考の後にテキストを生成します。このモデルは視覚推論分野で優れたパフォーマンスを発揮し、数学、コード、テキスト推論能力も第一級です。コンテキスト長は100kです。"}, "taichu_llm": {"description": "紫東太初言語大モデルは、強力な言語理解能力とテキスト創作、知識問答、コードプログラミング、数学計算、論理推論、感情分析、テキスト要約などの能力を備えています。革新的に大データの事前学習と多源の豊富な知識を組み合わせ、アルゴリズム技術を継続的に磨き、膨大なテキストデータから語彙、構造、文法、意味などの新しい知識を吸収し、モデルの効果を進化させています。ユーザーにより便利な情報とサービス、よりインテリジェントな体験を提供します。"}, "taichu_o1": {"description": "taichu_o1は新世代の推論大モデルで、多モーダルインタラクションと強化学習を通じて人間の思考チェーンを実現し、複雑な意思決定推論をサポートします。高精度の出力を維持しつつ、モデル推論の思考経路を示し、戦略分析や深い思考などのシーンに適しています。"}, "taichu_vl": {"description": "画像理解、知識移転、論理帰納などの能力を融合し、画像とテキストの質問応答分野で優れたパフォーマンスを発揮します。"}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct はパラメータ数8000億で、130億のパラメータを活性化するだけでより大きなモデルに匹敵し、「速考え／遅考え」のハイブリッド推論をサポートします。長文理解が安定しており、BFCL-v3 と τ-Bench による検証でエージェント能力が先行しています。GQA と多量子化フォーマットを組み合わせ、高効率な推論を実現しています。"}, "text-embedding-3-large": {"description": "最も強力なベクトル化モデル、英語および非英語のタスクに適しています"}, "text-embedding-3-small": {"description": "効率的で経済的な次世代埋め込みモデル、知識検索やRAGアプリケーションなどのシーンに適しています"}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414は、32Bのバイリンガル（中英）オープンウェイト言語モデルであり、コード生成、関数呼び出し、エージェントタスクに最適化されています。15Tの高品質および再推論データで事前トレーニングされており、人間の好みの整合性、拒否サンプリング、強化学習を使用してさらに洗練されています。このモデルは、複雑な推論、アーティファクト生成、構造化出力タスクにおいて優れたパフォーマンスを示し、複数のベンチマークテストでGPT-4oおよびDeepSeek-V3-0324と同等のパフォーマンスを達成しています。"}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414は、32Bのバイリンガル（中英）オープンウェイト言語モデルであり、コード生成、関数呼び出し、エージェントタスクに最適化されています。15Tの高品質および再推論データで事前トレーニングされており、人間の好みの整合性、拒否サンプリング、強化学習を使用してさらに洗練されています。このモデルは、複雑な推論、アーティファクト生成、構造化出力タスクにおいて優れたパフォーマンスを示し、複数のベンチマークテストでGPT-4oおよびDeepSeek-V3-0324と同等のパフォーマンスを達成しています。"}, "thudm/glm-4-9b-chat": {"description": "智谱AIが発表したGLM-4シリーズの最新世代の事前トレーニングモデルのオープンソース版です。"}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414はTHUDMによって開発されたGLM-4シリーズの90億パラメータの言語モデルです。GLM-4-9B-0414は、より大きな32B対応モデルと同じ強化学習と整合性戦略を使用してトレーニングされており、その規模に対して高性能を実現し、依然として強力な言語理解と生成能力を必要とするリソース制約のあるデプロイメントに適しています。"}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414は、GLM-4-32Bの強化推論バリアントであり、深い数学、論理、コード指向の問題解決のために構築されています。タスク特化型および一般的なペアの好みに基づく拡張強化学習を適用して、複雑な多段階タスクのパフォーマンスを向上させます。基礎となるGLM-4-32Bモデルと比較して、Z1は構造化推論と形式的な領域の能力を大幅に向上させています。\n\nこのモデルは、プロンプトエンジニアリングを通じて「思考」ステップを強制し、長形式の出力に対して改善された一貫性を提供します。エージェントワークフローに最適化されており、長いコンテキスト（YaRNを介して）、JSONツール呼び出し、安定した推論のための細粒度サンプリング設定をサポートしています。深く考慮された多段階推論や形式的な導出が必要なユースケースに非常に適しています。"}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414は、GLM-4-32Bの強化推論バリアントであり、深い数学、論理、コード指向の問題解決のために構築されています。タスク特化型および一般的なペアの好みに基づく拡張強化学習を適用して、複雑な多段階タスクのパフォーマンスを向上させます。基礎となるGLM-4-32Bモデルと比較して、Z1は構造化推論と形式的な領域の能力を大幅に向上させています。\n\nこのモデルは、プロンプトエンジニアリングを通じて「思考」ステップを強制し、長形式の出力に対して改善された一貫性を提供します。エージェントワークフローに最適化されており、長いコンテキスト（YaRNを介して）、JSONツール呼び出し、安定した推論のための細粒度サンプリング設定をサポートしています。深く考慮された多段階推論や形式的な導出が必要なユースケースに非常に適しています。"}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414はTHUDMによって開発されたGLM-4シリーズの9Bパラメータの言語モデルです。これは、より大きなGLM-Z1モデルに最初に適用された技術を採用しており、拡張強化学習、ペアランキング整合性、数学、コーディング、論理などの推論集約型タスクのトレーニングを含みます。規模は小さいものの、一般的な推論タスクにおいて強力な性能を発揮し、その重みレベルにおいて多くのオープンソースモデルを上回っています。"}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32BはGLM-4-Z1シリーズの32Bパラメータの深い推論モデルで、長時間の思考を必要とする複雑でオープンなタスクに最適化されています。glm-4-32b-0414を基にしており、追加の強化学習段階と多段階の整合性戦略を追加し、拡張認知処理を模倣することを目的とした「反省」能力を導入しています。これには、反復推論、多段階分析、検索、取得、引用感知合成などのツール強化ワークフローが含まれます。\n\nこのモデルは研究型の執筆、比較分析、複雑な質問応答において優れた性能を発揮します。検索とナビゲーションの原語（`search`、`click`、`open`、`finish`）のための関数呼び出しをサポートし、エージェント式パイプラインで使用できるようにします。反省行動は、ルールベースの報酬形成と遅延意思決定メカニズムを持つ多ラウンドの循環制御によって形作られ、OpenAI内部の整合性スタックなどの深い研究フレームワークを基準としています。このバリアントは、速度よりも深さが必要なシナリオに適しています。"}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-ChimeraはDeepSeek-R1とDeepSeek-V3（0324）を統合して作成され、R1の推論能力とV3のトークン効率の改善を組み合わせています。DeepSeek-MoE Transformerアーキテクチャに基づいており、一般的なテキスト生成タスクに最適化されています。\n\nこのモデルは、推論、効率、指示遵守タスクのパフォーマンスをバランスさせるために、2つのソースモデルの事前学習された重みを統合しています。MITライセンスの下でリリースされ、研究および商業用途に使用されることを目的としています。"}, "togethercomputer/StripedHyena-Nous-7B": {"description": "Striped<PERSON>yena <PERSON> (7B)は、高効率の戦略とモデルアーキテクチャを通じて、強化された計算能力を提供します。"}, "tts-1": {"description": "最新のテキスト音声合成モデル、リアルタイムシーン向けに速度を最適化"}, "tts-1-hd": {"description": "最新のテキスト音声合成モデル、品質を最適化"}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B)は、精密な指示タスクに適しており、優れた言語処理能力を提供します。"}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnetは業界標準を向上させ、競合モデルやClaude 3 Opusを超える性能を持ち、広範な評価で優れた結果を示し、我々の中程度のモデルの速度とコストを兼ね備えています。"}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 Sonnetは、Anthropicの最も高速な次世代モデルです。Claude 3 Haikuと比較して、Claude 3.7 Sonnetはすべてのスキルで向上しており、多くの知能ベンチマークテストで前世代の最大モデルClaude 3 Opusを超えています。"}, "v0-1.0-md": {"description": "v0-1.0-md モデルは、v0 API を通じて提供される旧バージョンのモデルです"}, "v0-1.5-lg": {"description": "v0-1.5-lg モデルは、高度な思考や推論タスクに適しています"}, "v0-1.5-md": {"description": "v0-1.5-md モデルは、日常的なタスクやユーザーインターフェース（UI）生成に適しています"}, "wan2.2-t2i-flash": {"description": "万相2.2の高速版で、現時点で最新のモデルです。創造性、安定性、写実的質感が全面的にアップグレードされ、生成速度が速く、コストパフォーマンスに優れています。"}, "wan2.2-t2i-plus": {"description": "万相2.2のプロフェッショナル版で、現時点で最新のモデルです。創造性、安定性、写実的質感が全面的にアップグレードされ、生成される画像のディテールが豊かです。"}, "wanx-v1": {"description": "基礎的なテキストから画像生成モデルで、通義万相公式サイトの1.0汎用モデルに対応しています。"}, "wanx2.0-t2i-turbo": {"description": "質感の良い人物画像生成に優れ、速度は中程度でコストが低いモデル。通義万相公式サイトの2.0高速モデルに対応しています。"}, "wanx2.1-t2i-plus": {"description": "全面的にアップグレードされたバージョンで、生成画像のディテールがより豊かで、速度はやや遅いです。通義万相公式サイトの2.1プロフェッショナルモデルに対応しています。"}, "wanx2.1-t2i-turbo": {"description": "全面的にアップグレードされたバージョンで、生成速度が速く、効果が総合的に優れており、コストパフォーマンスが高いです。通義万相公式サイトの2.1高速モデルに対応しています。"}, "whisper-1": {"description": "汎用音声認識モデルで、多言語の音声認識、音声翻訳、言語識別をサポートします。"}, "wizardlm2": {"description": "WizardLM 2は、Microsoft AIが提供する言語モデルであり、複雑な対話、多言語、推論、インテリジェントアシスタントの分野で特に優れた性能を発揮します。"}, "wizardlm2:8x22b": {"description": "WizardLM 2は、Microsoft AIが提供する言語モデルであり、複雑な対話、多言語、推論、インテリジェントアシスタントの分野で特に優れた性能を発揮します。"}, "x1": {"description": "Spark X1 モデルはさらにアップグレードされ、元の数学タスクで国内のリーダーシップを維持しつつ、推論、テキスト生成、言語理解などの一般的なタスクで OpenAI o1 および DeepSeek R1 に匹敵する効果を実現します。"}, "yi-1.5-34b-chat": {"description": "Yi-1.5は、Yiのアップグレード版です。500Bトークンの高品質なコーパスを使用してYiの事前学習を継続し、3Mの多様なファインチューニングサンプルでファインチューニングを行います。"}, "yi-large": {"description": "新しい千億パラメータモデルであり、超強力な質問応答およびテキスト生成能力を提供します。"}, "yi-large-fc": {"description": "yi-largeモデルを基に、ツール呼び出しの能力をサポートし強化し、エージェントやワークフローを構築する必要があるさまざまなビジネスシナリオに適しています。"}, "yi-large-preview": {"description": "初期バージョンであり、yi-large（新バージョン）の使用を推奨します。"}, "yi-large-rag": {"description": "yi-largeの超強力モデルに基づく高次サービスであり、検索と生成技術を組み合わせて正確な回答を提供し、リアルタイムで全網検索情報サービスを提供します。"}, "yi-large-turbo": {"description": "超高コストパフォーマンス、卓越した性能。性能と推論速度、コストに基づいて、高精度のバランス調整を行います。"}, "yi-lightning": {"description": "最新の高性能モデルで、高品質な出力を保証しつつ、推論速度が大幅に向上しています。"}, "yi-lightning-lite": {"description": "軽量版で、yi-lightningの使用を推奨します。"}, "yi-medium": {"description": "中型サイズモデルのアップグレード微調整であり、能力が均衡しており、コストパフォーマンスが高いです。指示遵守能力を深く最適化しています。"}, "yi-medium-200k": {"description": "200Kの超長コンテキストウィンドウを持ち、長文の深い理解と生成能力を提供します。"}, "yi-spark": {"description": "小型で強力な、軽量で高速なモデルです。強化された数学演算とコード作成能力を提供します。"}, "yi-vision": {"description": "複雑な視覚タスクモデルであり、高性能な画像理解と分析能力を提供します。"}, "yi-vision-v2": {"description": "複雑な視覚タスクモデルで、複数の画像に基づく高性能な理解と分析能力を提供します。"}, "zai-org/GLM-4.5": {"description": "GLM-4.5はエージェントアプリケーション向けに設計された基盤モデルで、混合専門家（Mixture-of-Experts）アーキテクチャを採用。ツール呼び出し、ウェブブラウジング、ソフトウェア工学、フロントエンドプログラミング分野で深く最適化され、Claude CodeやRoo Codeなどのコードエージェントへのシームレスな統合をサポートします。混合推論モードを採用し、複雑な推論や日常利用など多様なシナリオに適応可能です。"}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Airはエージェントアプリケーション向けに設計された基盤モデルで、混合専門家（Mixture-of-Experts）アーキテクチャを採用。ツール呼び出し、ウェブブラウジング、ソフトウェア工学、フロントエンドプログラミング分野で深く最適化され、Claude CodeやRoo Codeなどのコードエージェントへのシームレスな統合をサポートします。混合推論モードを採用し、複雑な推論や日常利用など多様なシナリオに適応可能です。"}}