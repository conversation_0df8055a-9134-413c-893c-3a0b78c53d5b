{"01-ai/yi-1.5-34b-chat": {"description": "제로일 만물, 최신 오픈 소스 미세 조정 모델로, 340억 개의 매개변수를 가지고 있으며, 다양한 대화 시나리오를 지원하는 미세 조정, 고품질 훈련 데이터, 인간의 선호에 맞춘 조정을 제공합니다."}, "01-ai/yi-1.5-9b-chat": {"description": "제로일 만물, 최신 오픈 소스 미세 조정 모델로, 90억 개의 매개변수를 가지고 있으며, 다양한 대화 시나리오를 지원하는 미세 조정, 고품질 훈련 데이터, 인간의 선호에 맞춘 조정을 제공합니다."}, "360/deepseek-r1": {"description": "【360 배포판】DeepSeek-R1은 후 훈련 단계에서 대규모로 강화 학습 기술을 사용하여, 극히 적은 주석 데이터로도 모델 추론 능력을 크게 향상시켰습니다. 수학, 코드, 자연어 추론 등의 작업에서 OpenAI o1 정식 버전과 동등한 성능을 자랑합니다."}, "360gpt-pro": {"description": "360GPT Pro는 360 AI 모델 시리즈의 중요한 구성원으로, 다양한 자연어 응용 시나리오에 맞춘 효율적인 텍스트 처리 능력을 갖추고 있으며, 긴 텍스트 이해 및 다중 회화 기능을 지원합니다."}, "360gpt-pro-trans": {"description": "번역 전용 모델로, 깊이 있는 미세 조정 최적화가 이루어져 있으며, 번역 효과가 뛰어납니다."}, "360gpt-turbo": {"description": "360GPT Turbo는 강력한 계산 및 대화 능력을 제공하며, 뛰어난 의미 이해 및 생성 효율성을 갖추고 있어 기업 및 개발자에게 이상적인 스마트 어시스턴트 솔루션입니다."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K는 의미 안전성과 책임 지향성을 강조하며, 콘텐츠 안전에 대한 높은 요구가 있는 응용 시나리오를 위해 설계되어 사용자 경험의 정확성과 안정성을 보장합니다."}, "360gpt2-o1": {"description": "360gpt2-o1은 트리 탐색을 사용하여 사고 체인을 구축하고 반성 메커니즘을 도입하였으며, 강화 학습을 통해 훈련되어 자기 반성과 오류 수정 능력을 갖추고 있습니다."}, "360gpt2-pro": {"description": "360GPT2 Pro는 360 회사에서 출시한 고급 자연어 처리 모델로, 뛰어난 텍스트 생성 및 이해 능력을 갖추고 있으며, 특히 생성 및 창작 분야에서 뛰어난 성능을 발휘하여 복잡한 언어 변환 및 역할 연기 작업을 처리할 수 있습니다."}, "360zhinao2-o1": {"description": "360zhinao2-o1은 트리 탐색을 사용하여 사고 체인을 구축하고 반성 메커니즘을 도입하여 강화 학습으로 훈련되며, 모델은 자기 반성과 오류 수정 능력을 갖추고 있습니다."}, "4.0Ultra": {"description": "Spark4.0 Ultra는 스타크 대형 모델 시리즈 중 가장 강력한 버전으로, 업그레이드된 네트워크 검색 링크와 함께 텍스트 내용의 이해 및 요약 능력을 향상시킵니다. 사무 생산성을 높이고 정확한 요구에 응답하기 위한 종합 솔루션으로, 업계를 선도하는 스마트 제품입니다."}, "AnimeSharp": {"description": "AnimeSharp(일명 “4x‑AnimeSharp”)는 Kim2091이 ESRGAN 아키텍처를 기반으로 개발한 오픈 소스 초해상도 모델로, 애니메이션 스타일 이미지의 확대 및 선명화에 중점을 두고 있습니다. 2022년 2월에 “4x-TextSharpV1”에서 이름이 변경되었으며, 원래는 텍스트 이미지에도 적용 가능했으나 애니메이션 콘텐츠에 맞게 성능이 크게 최적화되었습니다."}, "Baichuan2-Turbo": {"description": "검색 강화 기술을 통해 대형 모델과 분야 지식, 전 세계 지식의 완전한 연결을 실현합니다. PDF, Word 등 다양한 문서 업로드 및 웹사이트 입력을 지원하며, 정보 획득이 신속하고 포괄적이며, 출력 결과가 정확하고 전문적입니다."}, "Baichuan3-Turbo": {"description": "기업의 고빈도 시나리오에 최적화되어 효과가 크게 향상되었으며, 높은 비용 효율성을 자랑합니다. Baichuan2 모델에 비해 콘텐츠 창작이 20%, 지식 질문 응답이 17%, 역할 수행 능력이 40% 향상되었습니다. 전체적인 성능은 GPT3.5보다 우수합니다."}, "Baichuan3-Turbo-128k": {"description": "128K 초장기 컨텍스트 창을 갖추고 있으며, 기업의 고빈도 시나리오에 최적화되어 효과가 크게 향상되었으며, 높은 비용 효율성을 자랑합니다. Baichuan2 모델에 비해 콘텐츠 창작이 20%, 지식 질문 응답이 17%, 역할 수행 능력이 40% 향상되었습니다. 전체적인 성능은 GPT3.5보다 우수합니다."}, "Baichuan4": {"description": "모델 능력 국내 1위로, 지식 백과, 긴 텍스트, 생성 창작 등 중국어 작업에서 해외 주류 모델을 초월합니다. 또한 업계 선도적인 다중 모달 능력을 갖추고 있으며, 여러 권위 있는 평가 기준에서 우수한 성과를 보입니다."}, "Baichuan4-Air": {"description": "모델 능력이 국내 1위이며, 지식 백과, 긴 텍스트, 생성 창작 등 중국어 작업에서 해외 주류 모델을 초월합니다. 또한 업계 선도적인 다중 모달 능력을 갖추고 있으며, 여러 권위 있는 평가 기준에서 우수한 성과를 보입니다."}, "Baichuan4-Turbo": {"description": "모델 능력이 국내 1위이며, 지식 백과, 긴 텍스트, 생성 창작 등 중국어 작업에서 해외 주류 모델을 초월합니다. 또한 업계 선도적인 다중 모달 능력을 갖추고 있으며, 여러 권위 있는 평가 기준에서 우수한 성과를 보입니다."}, "DeepSeek-R1": {"description": "최첨단 효율적인 LLM으로, 추론, 수학 및 프로그래밍에 능숙합니다."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1——DeepSeek 패키지에서 더 크고 더 스마트한 모델——이 Llama 70B 아키텍처로 증류되었습니다. 기준 테스트와 인공지능 평가에 따르면, 이 모델은 원래 Llama 70B보다 더 스마트하며, 특히 수학 및 사실 정확성이 필요한 작업에서 뛰어난 성능을 보입니다."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Qwen2.5-Math-1.5B를 기반으로 한 DeepSeek-R1 증류 모델로, 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신합니다."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Qwen2.5-14B를 기반으로 한 DeepSeek-R1 증류 모델로, 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신합니다."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1 시리즈는 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신하고 OpenAI-o1-mini 수준을 초월합니다."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Qwen2.5-Math-7B를 기반으로 한 DeepSeek-R1 증류 모델로, 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신합니다."}, "DeepSeek-V3": {"description": "DeepSeek-V3는 심층 탐색 회사에서 자체 개발한 MoE 모델입니다. DeepSeek-V3는 여러 평가에서 Qwen2.5-72B 및 Llama-3.1-405B와 같은 다른 오픈 소스 모델을 초월하며, 성능 면에서 세계 최고의 폐쇄형 모델인 GPT-4o 및 Claude-3.5-Sonnet과 동등합니다."}, "Doubao-lite-128k": {"description": "Doubao-lite는 탁월한 응답 속도와 뛰어난 가성비를 자랑하며, 고객의 다양한 시나리오에 더 유연한 선택을 제공합니다. 128k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "Doubao-lite-32k": {"description": "Doubao-lite는 탁월한 응답 속도와 뛰어난 가성비를 자랑하며, 고객의 다양한 시나리오에 더 유연한 선택을 제공합니다. 32k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "Doubao-lite-4k": {"description": "Doubao-lite는 탁월한 응답 속도와 뛰어난 가성비를 자랑하며, 고객의 다양한 시나리오에 더 유연한 선택을 제공합니다. 4k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "Doubao-pro-128k": {"description": "최고 성능의 주력 모델로 복잡한 작업 처리에 적합하며, 참고 질문 답변, 요약, 창작, 텍스트 분류, 역할극 등 다양한 시나리오에서 우수한 성과를 보입니다. 128k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "Doubao-pro-32k": {"description": "최고 성능의 주력 모델로 복잡한 작업 처리에 적합하며, 참고 질문 답변, 요약, 창작, 텍스트 분류, 역할극 등 다양한 시나리오에서 우수한 성과를 보입니다. 32k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "Doubao-pro-4k": {"description": "최고 성능의 주력 모델로 복잡한 작업 처리에 적합하며, 참고 질문 답변, 요약, 창작, 텍스트 분류, 역할극 등 다양한 시나리오에서 우수한 성과를 보입니다. 4k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "DreamO": {"description": "DreamO는 바이트댄스와 베이징대학교가 공동 개발한 오픈 소스 이미지 맞춤 생성 모델로, 통합 아키텍처를 통해 다중 작업 이미지 생성을 지원합니다. 효율적인 조합 모델링 방식을 채택하여 사용자가 지정한 신원, 주체, 스타일, 배경 등 다양한 조건에 따라 일관성 있고 맞춤화된 이미지를 생성할 수 있습니다."}, "ERNIE-3.5-128K": {"description": "바이두가 자체 개발한 플래그십 대규모 언어 모델로, 방대한 중문 및 영문 코퍼스를 포함하고 있으며, 강력한 일반 능력을 갖추고 있어 대부분의 대화형 질문 응답, 창작 생성, 플러그인 응용 시나리오 요구를 충족할 수 있습니다. 또한 바이두 검색 플러그인과의 자동 연동을 지원하여 질문 응답 정보의 시의성을 보장합니다."}, "ERNIE-3.5-8K": {"description": "바이두가 자체 개발한 플래그십 대규모 언어 모델로, 방대한 중문 및 영문 코퍼스를 포함하고 있으며, 강력한 일반 능력을 갖추고 있어 대부분의 대화형 질문 응답, 창작 생성, 플러그인 응용 시나리오 요구를 충족할 수 있습니다. 또한 바이두 검색 플러그인과의 자동 연동을 지원하여 질문 응답 정보의 시의성을 보장합니다."}, "ERNIE-3.5-8K-Preview": {"description": "바이두가 자체 개발한 플래그십 대규모 언어 모델로, 방대한 중문 및 영문 코퍼스를 포함하고 있으며, 강력한 일반 능력을 갖추고 있어 대부분의 대화형 질문 응답, 창작 생성, 플러그인 응용 시나리오 요구를 충족할 수 있습니다. 또한 바이두 검색 플러그인과의 자동 연동을 지원하여 질문 응답 정보의 시의성을 보장합니다."}, "ERNIE-4.0-8K-Latest": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, ERNIE 3.5에 비해 모델 능력이 전면적으로 업그레이드되었으며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 자동으로 바이두 검색 플러그인과 연결되어 질문 응답 정보의 시의성을 보장합니다."}, "ERNIE-4.0-8K-Preview": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, ERNIE 3.5에 비해 모델 능력이 전면적으로 업그레이드되었으며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 자동으로 바이두 검색 플러그인과 연결되어 질문 응답 정보의 시의성을 보장합니다."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "바이두가 개발한 플래그십 대규모 언어 모델로, 다양한 분야의 복잡한 작업 환경에서 뛰어난 종합 효과를 보여줍니다. 바이두 검색 플러그인 자동 연결을 지원하여 질문과 답변 정보의 시의성을 보장합니다. ERNIE 4.0에 비해 성능이 더욱 우수합니다."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, 종합적인 성능이 뛰어나며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 자동으로 바이두 검색 플러그인과 연결되어 질문 응답 정보의 시의성을 보장합니다. ERNIE 4.0에 비해 성능이 더욱 우수합니다."}, "ERNIE-Character-8K": {"description": "바이두가 자체 개발한 수직 장면 대언어 모델로, 게임 NPC, 고객 서비스 대화, 대화 역할 수행 등 다양한 응용 시나리오에 적합하며, 캐릭터 스타일이 더욱 뚜렷하고 일관되며, 지시 준수 능력이 더 강하고, 추론 성능이 우수합니다."}, "ERNIE-Lite-Pro-128K": {"description": "바이두가 자체 개발한 경량 대언어 모델로, 우수한 모델 효과와 추론 성능을 겸비하고 있으며, ERNIE Lite보다 더 나은 성능을 보여 저전력 AI 가속 카드에서의 추론 사용에 적합합니다."}, "ERNIE-Speed-128K": {"description": "바이두가 2024년에 최신 발표한 자체 개발 고성능 대언어 모델로, 일반 능력이 뛰어나며, 특정 시나리오 문제를 더 잘 처리하기 위해 기본 모델로 조정하는 데 적합하며, 뛰어난 추론 성능을 갖추고 있습니다."}, "ERNIE-Speed-Pro-128K": {"description": "바이두가 2024년에 최신 발표한 자체 개발 고성능 대언어 모델로, 일반 능력이 뛰어나며, ERNIE Speed보다 더 나은 성능을 보여 특정 시나리오 문제를 더 잘 처리하기 위해 기본 모델로 조정하는 데 적합하며, 뛰어난 추론 성능을 갖추고 있습니다."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev는 Black Forest Labs가 개발한 Rectified Flow Transformer 아키텍처 기반의 다중 모달 이미지 생성 및 편집 모델로, 120억(12B) 파라미터 규모를 갖추고 있습니다. 주어진 컨텍스트 조건 하에서 이미지 생성, 재구성, 향상 또는 편집에 특화되어 있습니다. 이 모델은 확산 모델의 제어 가능한 생성 장점과 Transformer의 컨텍스트 모델링 능력을 결합하여 고품질 이미지 출력을 지원하며, 이미지 복원, 이미지 보완, 시각적 장면 재구성 등 다양한 작업에 널리 활용됩니다."}, "FLUX.1-dev": {"description": "FLUX.1-dev는 Black Forest Labs가 개발한 오픈 소스 다중 모달 언어 모델(MLLM)로, 이미지와 텍스트 이해 및 생성 능력을 융합하여 이미지-텍스트 작업에 최적화되어 있습니다. Mistral-7B와 같은 최첨단 대형 언어 모델을 기반으로 정교하게 설계된 시각 인코더와 다단계 명령 미세 조정을 통해 이미지-텍스트 협업 처리 및 복잡한 작업 추론 능력을 구현합니다."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B)는 혁신적인 모델로, 다양한 분야의 응용과 복잡한 작업에 적합합니다."}, "HelloMeme": {"description": "HelloMeme는 사용자가 제공한 이미지나 동작을 바탕으로 자동으로 밈, GIF 또는 짧은 동영상을 생성하는 AI 도구입니다. 그림 그리기나 프로그래밍 지식이 전혀 없어도 참고 이미지만 준비하면, 보기 좋고 재미있으며 스타일이 일관된 콘텐츠를 만들어 줍니다."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full은 지상미래(HiDream.ai)에서 출시한 오픈 소스 다중 모달 이미지 편집 대형 모델로, 최첨단 Diffusion Transformer 아키텍처를 기반으로 강력한 언어 이해 능력(LLaMA 3.1-8B-Instruct 내장)을 결합하여 자연어 명령을 통해 이미지 생성, 스타일 전이, 부분 편집 및 내용 재구성을 지원하며 뛰어난 이미지-텍스트 이해 및 실행 능력을 갖추고 있습니다."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled는 경량화된 텍스트-이미지 생성 모델로, 증류 최적화를 거쳐 빠르게 고품질 이미지를 생성할 수 있어 저자원 환경과 실시간 생성 작업에 특히 적합합니다."}, "InstantCharacter": {"description": "InstantCharacter는 텐센트 AI 팀이 2025년에 발표한 튜닝 불필요(tuning-free) 개인화 캐릭터 생성 모델로, 고충실도 및 다양한 장면에서 일관된 캐릭터 생성을 목표로 합니다. 단 한 장의 참조 이미지로 캐릭터를 모델링할 수 있으며, 해당 캐릭터를 다양한 스타일, 동작, 배경에 유연하게 적용할 수 있습니다."}, "InternVL2-8B": {"description": "InternVL2-8B는 강력한 비주얼 언어 모델로, 이미지와 텍스트의 다중 모달 처리를 지원하며, 이미지 내용을 정확하게 인식하고 관련 설명이나 답변을 생성할 수 있습니다."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B는 강력한 비주얼 언어 모델로, 이미지와 텍스트의 다중 모달 처리를 지원하며, 이미지 내용을 정확하게 인식하고 관련 설명이나 답변을 생성할 수 있습니다."}, "Kolors": {"description": "Kolors는 콰이쇼우 Kolors 팀이 개발한 텍스트-이미지 생성 모델로, 수십억 개의 파라미터로 훈련되어 시각 품질, 중국어 의미 이해 및 텍스트 렌더링에서 뛰어난 성능을 보입니다."}, "Kwai-Kolors/Kolors": {"description": "Kolors는 콰이쇼우 Kolors 팀이 개발한 잠재 확산 기반 대규모 텍스트-이미지 생성 모델입니다. 수십억 개의 텍스트-이미지 쌍으로 훈련되어 시각 품질, 복잡한 의미 정확성 및 중영문 문자 렌더링에서 탁월한 성능을 발휘합니다. 중영문 입력을 모두 지원하며, 중국어 특정 콘텐츠의 이해 및 생성에서도 뛰어난 성과를 보입니다."}, "Llama-3.2-11B-Vision-Instruct": {"description": "고해상도 이미지에서 뛰어난 이미지 추론 능력을 보여주며, 시각적 이해 응용 프로그램에 적합합니다."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "시각적 이해 에이전트 응용 프로그램에 적합한 고급 이미지 추론 능력입니다."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 지시 조정 텍스트 모델로, 다국어 대화 사용 사례에 최적화되어 있으며, 많은 오픈 소스 및 폐쇄형 채팅 모델 중에서 일반 산업 기준에서 우수한 성능을 보입니다."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 지시 조정 텍스트 모델로, 다국어 대화 사용 사례에 최적화되어 있으며, 많은 오픈 소스 및 폐쇄형 채팅 모델 중에서 일반 산업 기준에서 우수한 성능을 보입니다."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 지시 조정 텍스트 모델로, 다국어 대화 사용 사례에 최적화되어 있으며, 많은 오픈 소스 및 폐쇄형 채팅 모델 중에서 일반 산업 기준에서 우수한 성능을 보입니다."}, "Meta-Llama-3.2-1B-Instruct": {"description": "언어 이해, 뛰어난 추론 능력 및 텍스트 생성 능력을 갖춘 최첨단 소형 언어 모델입니다."}, "Meta-Llama-3.2-3B-Instruct": {"description": "언어 이해, 뛰어난 추론 능력 및 텍스트 생성 능력을 갖춘 최첨단 소형 언어 모델입니다."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3은 Llama 시리즈에서 가장 진보된 다국어 오픈 소스 대형 언어 모델로, 매우 낮은 비용으로 405B 모델의 성능을 경험할 수 있습니다. Transformer 구조를 기반으로 하며, 감독 미세 조정(SFT)과 인간 피드백 강화 학습(RLHF)을 통해 유용성과 안전성을 향상시켰습니다. 그 지시 조정 버전은 다국어 대화를 위해 최적화되어 있으며, 여러 산업 기준에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다. 지식 마감일은 2023년 12월입니다."}, "MiniMax-M1": {"description": "완전히 새로 개발된 추론 모델입니다. 세계 최고 수준: 80K 사고 체인 x 1M 입력, 해외 최상위 모델과 견줄 만한 성능을 자랑합니다."}, "MiniMax-Text-01": {"description": "MiniMax-01 시리즈 모델에서는 대담한 혁신을 이루었습니다: 대규모로 선형 주의 메커니즘을 처음으로 구현하였으며, 전통적인 Transformer 아키텍처가 더 이상 유일한 선택이 아닙니다. 이 모델의 파라미터 수는 4560억에 달하며, 단일 활성화는 45.9억입니다. 모델의 종합 성능은 해외 최고의 모델과 견줄 수 있으며, 전 세계에서 가장 긴 400만 토큰의 문맥을 효율적으로 처리할 수 있습니다. 이는 GPT-4o의 32배, Claude-3.5-Sonnet의 20배에 해당합니다."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1은 오픈 소스 가중치를 가진 대규모 혼합 주의 추론 모델로, 4,560억 개의 파라미터를 보유하고 있으며, 각 토큰당 약 459억 개의 파라미터가 활성화됩니다. 모델은 100만 토큰의 초장기 문맥을 원활히 지원하며, 번개 주의 메커니즘을 통해 10만 토큰 생성 작업에서 DeepSeek R1 대비 75%의 부동 소수점 연산량을 절감합니다. 또한 MiniMax-M1은 MoE(혼합 전문가) 아키텍처를 채택하고, CISPO 알고리즘과 혼합 주의 설계가 결합된 효율적인 강화 학습 훈련을 통해 긴 입력 추론과 실제 소프트웨어 엔지니어링 환경에서 업계 선도적인 성능을 구현합니다."}, "Moonshot-Kimi-K2-Instruct": {"description": "총 파라미터 1조, 활성화 파라미터 320억. 비사고 모델 중에서 최첨단 지식, 수학, 코딩 분야에서 최고 수준을 달성했으며, 범용 에이전트 작업에 더 강합니다. 에이전트 작업에 최적화되어 질문에 답변할 뿐만 아니라 행동도 수행할 수 있습니다. 즉흥적이고 범용적인 대화 및 에이전트 경험에 가장 적합하며, 장시간 사고가 필요 없는 반사 수준 모델입니다."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B)는 고정밀 지시 모델로, 복잡한 계산에 적합합니다."}, "OmniConsistency": {"description": "OmniConsistency는 대규모 Diffusion Transformers(DiTs)와 페어드 스타일 데이터 도입을 통해 이미지-투-이미지 작업에서 스타일 일관성과 일반화 능력을 향상시켜 스타일 저하를 방지합니다."}, "Phi-3-medium-128k-instruct": {"description": "같은 Phi-3-medium 모델이지만 RAG 또는 몇 가지 샷 프롬프트를 위한 더 큰 컨텍스트 크기를 가지고 있습니다."}, "Phi-3-medium-4k-instruct": {"description": "14B 매개변수 모델로, Phi-3-mini보다 더 나은 품질을 제공하며, 고품질의 추론 밀집 데이터에 중점을 두고 있습니다."}, "Phi-3-mini-128k-instruct": {"description": "같은 Phi-3-mini 모델이지만 RAG 또는 몇 가지 샷 프롬프트를 위한 더 큰 컨텍스트 크기를 가지고 있습니다."}, "Phi-3-mini-4k-instruct": {"description": "Phi-3 가족의 가장 작은 구성원으로, 품질과 낮은 대기 시간 모두에 최적화되어 있습니다."}, "Phi-3-small-128k-instruct": {"description": "같은 Phi-3-small 모델이지만 RAG 또는 몇 가지 샷 프롬프트를 위한 더 큰 컨텍스트 크기를 가지고 있습니다."}, "Phi-3-small-8k-instruct": {"description": "7B 매개변수 모델로, Phi-3-mini보다 더 나은 품질을 제공하며, 고품질의 추론 밀집 데이터에 중점을 두고 있습니다."}, "Phi-3.5-mini-instruct": {"description": "Phi-3-mini 모델의 업데이트된 버전입니다."}, "Phi-3.5-vision-instrust": {"description": "Phi-3-vision 모델의 업데이트된 버전입니다."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct는 Qwen2 시리즈의 지침 미세 조정 대규모 언어 모델로, 파라미터 규모는 7B입니다. 이 모델은 Transformer 아키텍처를 기반으로 하며, SwiGLU 활성화 함수, 주의 QKV 편향 및 그룹 쿼리 주의와 같은 기술을 사용합니다. 이 모델은 대규모 입력을 처리할 수 있습니다. 이 모델은 언어 이해, 생성, 다국어 능력, 코딩, 수학 및 추론 등 여러 벤치마크 테스트에서 뛰어난 성능을 보이며, 대부분의 오픈 소스 모델을 초월하고 특정 작업에서 독점 모델과 동등한 경쟁력을 보여줍니다. Qwen2-7B-Instruct는 여러 평가에서 Qwen1.5-7B-Chat보다 우수하여 상당한 성능 향상을 보였습니다."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct는 Alibaba Cloud에서 발표한 최신 대규모 언어 모델 시리즈 중 하나입니다. 이 7B 모델은 코딩 및 수학 분야에서 상당한 개선된 능력을 가지고 있습니다. 이 모델은 또한 29개 이상의 언어를 포함한 다국어 지원을 제공합니다. 모델은 지침 준수, 구조화된 데이터 이해 및 구조화된 출력 생성(특히 JSON)에서 상당한 향상을 보입니다."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct는 Alibaba Cloud에서 발표한 코드 특화 대규모 언어 모델 시리즈의 최신 버전입니다. 이 모델은 Qwen2.5를 기반으로 하여 55조 개의 토큰으로 훈련되어 코드 생성, 추론 및 수정 능력을 크게 향상시켰습니다. 이 모델은 코딩 능력을 강화할 뿐만 아니라 수학 및 일반 능력의 장점도 유지합니다. 모델은 코드 에이전트와 같은 실제 응용 프로그램에 더 포괄적인 기반을 제공합니다."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL은 <PERSON>wen 시리즈의 새로운 멤버로, 강력한 시각 이해 능력을 갖추고 있습니다. 이미지 내 텍스트, 차트, 레이아웃을 분석할 수 있으며, 긴 동영상을 이해하고 이벤트를 포착할 수 있습니다. 추론을 수행하고 도구를 조작할 수 있으며, 다중 형식 객체 위치 지정과 구조화된 출력 생성을 지원합니다. 동영상 이해를 위한 동적 해상도 및 프레임 속도 훈련이 최적화되었으며, 시각 인코더 효율성이 향상되었습니다."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking은 지푸 AI와 칭화대 KEG 연구실이 공동으로 발표한 오픈소스 비주얼 언어 모델(VLM)로, 복잡한 다중 모달 인지 작업을 처리하도록 설계되었습니다. 이 모델은 GLM-4-9B-0414 기본 모델을 기반으로 하며, '사고 사슬'(Chain-of-Thought) 추론 메커니즘을 도입하고 강화 학습 전략을 채택하여 교차 모달 추론 능력과 안정성을 크게 향상시켰습니다."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat은 Zhizhu AI가 출시한 GLM-4 시리즈의 사전 훈련 모델 중 오픈 소스 버전입니다. 이 모델은 의미, 수학, 추론, 코드 및 지식 등 여러 측면에서 뛰어난 성능을 보입니다. GLM-4-9B-Chat은 다중 회전 대화를 지원할 뿐만 아니라 웹 브라우징, 코드 실행, 사용자 정의 도구 호출(Function Call) 및 긴 텍스트 추론과 같은 고급 기능도 갖추고 있습니다. 이 모델은 중국어, 영어, 일본어, 한국어 및 독일어를 포함한 26개 언어를 지원합니다. 여러 벤치마크 테스트에서 GLM-4-9B-Chat은 AlignBench-v2, MT-Bench, MMLU 및 C-Eval 등에서 뛰어난 성능을 보였습니다. 이 모델은 최대 128K의 컨텍스트 길이를 지원하며, 학술 연구 및 상업적 응용에 적합합니다."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1은 강화 학습(RL) 기반의 추론 모델로, 모델 내의 반복성과 가독성 문제를 해결합니다. RL 이전에 DeepSeek-R1은 콜드 스타트 데이터를 도입하여 추론 성능을 더욱 최적화했습니다. 수학, 코드 및 추론 작업에서 OpenAI-o1과 유사한 성능을 보이며, 정교하게 설계된 훈련 방법을 통해 전체적인 효과를 향상시켰습니다."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-7B는 Qwen2.5-Math-7B를 기반으로 지식 증류를 통해 개발된 모델입니다. 이 모델은 DeepSeek-R1에서 생성된 80만 개의 선별된 샘플을 사용하여 미세 조정되었으며, 우수한 추론 능력을 보여줍니다. 다양한 벤치마크에서 뛰어난 성능을 발휘하며, MATH-500에서 92.8%의 정확도, AIME 2024에서 55.5%의 통과율, CodeForces에서 1189점을 기록하여 7B 규모 모델로서 강력한 수학 및 프로그래밍 능력을 입증했습니다."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3는 6710억 개의 매개변수를 가진 혼합 전문가(MoE) 언어 모델로, 다중 헤드 잠재 주의(MLA) 및 DeepSeekMoE 아키텍처를 사용하여 보조 손실 없는 부하 균형 전략을 결합하여 추론 및 훈련 효율성을 최적화합니다. 14.8조 개의 고품질 토큰에서 사전 훈련을 수행하고 감독 미세 조정 및 강화 학습을 통해 DeepSeek-V3는 성능 면에서 다른 오픈 소스 모델을 초월하며, 선도적인 폐쇄형 모델에 근접합니다."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2는 초강력 코드 및 에이전트 능력을 갖춘 MoE 아키텍처 기반 모델로, 총 파라미터 1조, 활성화 파라미터 320억입니다. 범용 지식 추론, 프로그래밍, 수학, 에이전트 등 주요 분야 벤치마크에서 K2 모델은 다른 주류 오픈 소스 모델을 능가하는 성능을 보입니다."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview는 복잡한 대화 생성 및 맥락 이해 작업을 효율적으로 처리할 수 있는 혁신적인 자연어 처리 모델입니다."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview는 Qwen 팀이 개발한 시각적 추론 능력에 중점을 둔 연구 모델로, 복잡한 장면 이해 및 시각 관련 수학 문제 해결에서 독특한 장점을 가지고 있습니다."}, "Qwen/QwQ-32B": {"description": "QwQ는 Qwen 시리즈의 추론 모델입니다. 전통적인 지시 조정 모델과 비교할 때, QwQ는 사고 및 추론 능력을 갖추고 있어 하위 작업에서 특히 어려운 문제를 해결하는 데 있어 성능이 크게 향상됩니다. QwQ-32B는 중형 추론 모델로, 최신 추론 모델(예: DeepSeek-R1, o1-mini)과 비교할 때 경쟁력 있는 성능을 발휘합니다. 이 모델은 RoPE, SwiGLU, RMSNorm 및 Attention QKV bias와 같은 기술을 사용하며, 64층 네트워크 구조와 40개의 Q 주의 헤드(GQA 구조에서 KV는 8개)를 가지고 있습니다."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview는 Qwen의 최신 실험적 연구 모델로, AI 추론 능력을 향상시키는 데 중점을 두고 있습니다. 언어 혼합, 재귀 추론 등 복잡한 메커니즘을 탐구하며, 주요 장점으로는 강력한 추론 분석 능력, 수학 및 프로그래밍 능력이 포함됩니다. 동시에 언어 전환 문제, 추론 루프, 안전성 고려 및 기타 능력 차이와 같은 문제도 존재합니다."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2는 다양한 지시 유형을 지원하는 고급 범용 언어 모델입니다."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct는 Qwen2 시리즈의 지침 미세 조정 대규모 언어 모델로, 파라미터 규모는 72B입니다. 이 모델은 Transformer 아키텍처를 기반으로 하며, SwiGLU 활성화 함수, 주의 QKV 편향 및 그룹 쿼리 주의와 같은 기술을 사용합니다. 이 모델은 대규모 입력을 처리할 수 있습니다. 이 모델은 언어 이해, 생성, 다국어 능력, 코딩, 수학 및 추론 등 여러 벤치마크 테스트에서 뛰어난 성능을 보이며, 대부분의 오픈 소스 모델을 초월하고 특정 작업에서 독점 모델과 동등한 경쟁력을 보여줍니다."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL은 Qwen-VL 모델의 최신 반복 버전으로, 시각 이해 기준 테스트에서 최첨단 성능을 달성했습니다."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5는 지시형 작업 처리를 최적화하기 위해 설계된 새로운 대형 언어 모델 시리즈입니다."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5는 지시형 작업 처리를 최적화하기 위해 설계된 새로운 대형 언어 모델 시리즈입니다."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "알리바바 클라우드 통의 천문 팀이 개발한 대형 언어 모델"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5는 더 강력한 이해 및 생성 능력을 갖춘 새로운 대형 언어 모델 시리즈입니다."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5는 명령형 작업 처리를 최적화하기 위해 설계된 새로운 대형 언어 모델 시리즈입니다."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5는 지시형 작업 처리를 최적화하기 위해 설계된 새로운 대형 언어 모델 시리즈입니다."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5는 명령형 작업 처리를 최적화하기 위해 설계된 새로운 대형 언어 모델 시리즈입니다."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder는 코드 작성에 중점을 둡니다."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct는 Alibaba Cloud에서 발표한 코드 특화 대규모 언어 모델 시리즈의 최신 버전입니다. 이 모델은 Qwen2.5를 기반으로 하여 55조 개의 토큰으로 훈련되어 코드 생성, 추론 및 수정 능력을 크게 향상시켰습니다. 이 모델은 코딩 능력을 강화할 뿐만 아니라 수학 및 일반 능력의 장점도 유지합니다. 모델은 코드 에이전트와 같은 실제 응용 프로그램에 더 포괄적인 기반을 제공합니다."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct는 통의천문(<PERSON>wen) 팀이 개발한 멀티모달 대형 언어 모델로, Qwen2.5-VL 시리즈의 일부입니다. 이 모델은 일반적인 물체 인식에 능할 뿐만 아니라 이미지 내의 텍스트, 차트, 아이콘, 그래픽 및 레이아웃 분석이 가능합니다. 시각적 지능 에이전트로 작동하여 도구를 동적으로 조작하고 컴퓨터 및 스마트폰 사용 능력을 보유하고 있습니다. 또한 이 모델은 이미지 내 객체를 정밀하게 위치 지정할 수 있으며, 청구서나 표 등의 구조화된 출력을 생성할 수 있습니다. 이전 버전인 Qwen2-VL 대비 강화 학습을 통해 수학 및 문제 해결 능력이 향상되었으며, 응답 스타일도 인간의 선호에 더 부합하도록 개선되었습니다."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL은 Qwen2.5 시리즈의 시각 언어 모델입니다. 이 모델은 여러 측면에서 뛰어난 성능을 보입니다: 일반적인 물체 인식, 텍스트/차트/레이아웃 분석 등 향상된 시각 이해 능력을 갖추었으며, 시각 에이전트로서 도구 사용을 동적으로 추론하고 안내할 수 있습니다. 1시간 이상의 장편 동영상 이해가 가능하며 주요 이벤트를 포착할 수 있고, 이미지 내 객체의 정확한 위치를 경계 상자 또는 점으로 표시할 수 있습니다. 특히 인보이스, 표 등 스캔 데이터에 적합한 구조화된 출력 생성이 가능합니다."}, "Qwen/Qwen3-14B": {"description": "Qwen3는 능력이 크게 향상된 차세대 통의천문 대모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며 사고 모드 전환을 지원합니다."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3는 능력이 크게 향상된 차세대 통의천문 대모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며 사고 모드 전환을 지원합니다."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3 시리즈의 플래그십 혼합 전문가(MoE) 대형 언어 모델로, 알리바바 클라우드 통의천문 팀이 개발했습니다. 총 2350억 파라미터, 추론 시 220억 파라미터 활성화됩니다. Qwen3-235B-A22B 비사고 모드의 업데이트 버전으로, 명령 준수, 논리 추론, 텍스트 이해, 수학, 과학, 프로그래밍 및 도구 사용 등 범용 능력에서 크게 향상되었습니다. 또한 다국어 롱테일 지식 커버리지를 강화하고, 주관적 및 개방형 작업에서 사용자 선호에 더 잘 맞춰 더 유용하고 고품질의 텍스트를 생성합니다."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3 시리즈의 대형 언어 모델 중 하나로, 고난도 복잡 추론 작업에 특화되어 있습니다. 혼합 전문가(MoE) 아키텍처 기반이며, 총 파라미터 2350억, 토큰 처리 시 약 220억 파라미터만 활성화하여 강력한 성능과 계산 효율성을 동시에 달성했습니다. 전용 '사고' 모델로서 논리 추론, 수학, 과학, 프로그래밍, 학술 벤치마크 등 인간 전문 지식이 필요한 작업에서 뛰어난 성능을 보이며, 오픈 소스 사고 모델 중 최고 수준입니다. 또한 명령 준수, 도구 사용, 텍스트 생성 등 범용 능력을 강화하고, 256K 길이의 긴 문맥 이해를 기본 지원하여 심층 추론 및 장문 처리에 적합합니다."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3는 능력이 크게 향상된 차세대 통의천문 대모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며 사고 모드 전환을 지원합니다."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507은 Qwen3-30B-A3B 비사고 모드의 업데이트 버전입니다. 이 모델은 총 305억 개의 파라미터와 33억 개의 활성화 파라미터를 가진 혼합 전문가(MoE) 모델입니다. 이 모델은 지침 준수, 논리 추론, 텍스트 이해, 수학, 과학, 코딩 및 도구 사용 등 여러 측면에서 중요한 향상을 이루었습니다. 또한 다국어 장기 지식 커버리지에서 실질적인 진전을 이루었으며, 주관적이고 개방형 작업에서 사용자 선호도에 더 잘 맞춰져 더 유용한 응답과 높은 품질의 텍스트를 생성할 수 있습니다. 아울러 이 모델의 장문 이해 능력도 256K로 강화되었습니다. 이 모델은 비사고 모드만 지원하며 출력에 `<think></think>` 태그를 생성하지 않습니다."}, "Qwen/Qwen3-32B": {"description": "Qwen3는 능력이 크게 향상된 차세대 통의천문 대모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며 사고 모드 전환을 지원합니다."}, "Qwen/Qwen3-8B": {"description": "Qwen3는 능력이 크게 향상된 차세대 통의천문 대모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며 사고 모드 전환을 지원합니다."}, "Qwen2-72B-Instruct": {"description": "Qwen2는 Qwen 모델의 최신 시리즈로, 128k 컨텍스트를 지원합니다. 현재 최상의 오픈 소스 모델과 비교할 때, Qwen2-72B는 자연어 이해, 지식, 코드, 수학 및 다국어 등 여러 능력에서 현재 선도하는 모델을 현저히 초월합니다."}, "Qwen2-7B-Instruct": {"description": "Qwen2는 Qwen 모델의 최신 시리즈로, 동등한 규모의 최적 오픈 소스 모델은 물론 더 큰 규모의 모델을 초월할 수 있습니다. Qwen2 7B는 여러 평가에서 현저한 우위를 차지하였으며, 특히 코드 및 중국어 이해에서 두드러진 성과를 보였습니다."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B는 강력한 시각 언어 모델로, 이미지와 텍스트의 다중 모드 처리를 지원하며, 이미지 내용을 정확하게 인식하고 관련 설명이나 답변을 생성할 수 있습니다."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct는 140억 매개변수를 가진 대형 언어 모델로, 성능이 우수하며 중국어 및 다국어 시나리오를 최적화하여 스마트 Q&A, 콘텐츠 생성 등의 응용을 지원합니다."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct는 320억 매개변수를 가진 대형 언어 모델로, 성능이 균형 잡혀 있으며 중국어 및 다국어 시나리오를 최적화하여 스마트 Q&A, 콘텐츠 생성 등의 응용을 지원합니다."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct는 16k 컨텍스트를 지원하며, 8K를 초과하는 긴 텍스트를 생성할 수 있습니다. 함수 호출 및 외부 시스템과의 원활한 상호작용을 지원하여 유연성과 확장성을 크게 향상시킵니다. 모델의 지식이 현저히 증가하였고, 인코딩 및 수학 능력이 크게 향상되었으며, 29개 이상의 언어를 지원합니다."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct는 70억 매개변수를 가진 대형 언어 모델로, 함수 호출 및 외부 시스템과의 원활한 상호작용을 지원하여 유연성과 확장성을 크게 향상시킵니다. 중국어 및 다국어 시나리오를 최적화하여 스마트 Q&A, 콘텐츠 생성 등의 응용을 지원합니다."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct는 대규모 사전 훈련된 프로그래밍 지침 모델로, 강력한 코드 이해 및 생성 능력을 갖추고 있으며, 다양한 프로그래밍 작업을 효율적으로 처리할 수 있습니다. 특히 스마트 코드 작성, 자동화 스크립트 생성 및 프로그래밍 문제 해결에 적합합니다."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct는 코드 생성, 코드 이해 및 효율적인 개발 시나리오를 위해 설계된 대형 언어 모델로, 업계 최고의 32B 매개변수 규모를 채택하여 다양한 프로그래밍 요구를 충족합니다."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B는 MoE(혼합 전문가 모델)로, '혼합 추론 모드'를 도입하여 사용자가 '사고 모드'와 '비사고 모드' 사이를 원활하게 전환할 수 있습니다. 119개 언어 및 방언의 이해와 추론을 지원하며 강력한 도구 호출 능력을 갖추고 있습니다. 종합 능력, 코드 및 수학, 다국어 능력, 지식 및 추론 등 여러 벤치마크 테스트에서 DeepSeek R1, OpenAI o1, o3-mini, Grok 3, 구글 Gemini 2.5 Pro 등 현재 시장의 주요 대형 모델들과 경쟁할 수 있습니다."}, "Qwen3-32B": {"description": "Qwen3-32B는 밀집 모델(<PERSON><PERSON> Model)로, '혼합 추론 모드'를 도입하여 사용자가 '사고 모드'와 '비사고 모드' 사이를 원활하게 전환할 수 있습니다. 모델 아키텍처 개선, 학습 데이터 증가 및 더 효율적인 학습 방법 덕분에 전체 성능이 Qwen2.5-72B와 유사한 수준입니다."}, "SenseChat": {"description": "기본 버전 모델(V4), 4K 컨텍스트 길이, 일반적인 능력이 강력합니다."}, "SenseChat-128K": {"description": "기본 버전 모델(V4), 128K 컨텍스트 길이, 긴 텍스트 이해 및 생성 작업에서 뛰어난 성능을 발휘합니다."}, "SenseChat-32K": {"description": "기본 버전 모델(V4), 32K 컨텍스트 길이, 다양한 시나리오에 유연하게 적용됩니다."}, "SenseChat-5": {"description": "최신 버전 모델(V5.5), 128K 컨텍스트 길이, 수학적 추론, 영어 대화, 지시 따르기 및 긴 텍스트 이해 등 분야에서 능력이 크게 향상되어 GPT-4o와 견줄 수 있습니다."}, "SenseChat-5-1202": {"description": "V5.5 기반 최신 버전으로, 이전 버전 대비 중영문 기본 능력, 대화, 이과 지식, 문과 지식, 작문, 수리 논리, 글자 수 조절 등 여러 측면에서 현저한 향상을 이루었습니다."}, "SenseChat-5-Cantonese": {"description": "32K 컨텍스트 길이, 광둥어 대화 이해에서 GPT-4를 초월하며, 지식, 추론, 수학 및 코드 작성 등 여러 분야에서 GPT-4 Turbo와 견줄 수 있습니다."}, "SenseChat-5-beta": {"description": "일부 성능이 SenseCat-5-1202보다 우수합니다."}, "SenseChat-Character": {"description": "표준 버전 모델, 8K 컨텍스트 길이, 높은 응답 속도를 자랑합니다."}, "SenseChat-Character-Pro": {"description": "고급 버전 모델, 32K 컨텍스트 길이, 능력이 전반적으로 향상되었으며, 중/영어 대화를 지원합니다."}, "SenseChat-Turbo": {"description": "빠른 질문 응답 및 모델 미세 조정 시나리오에 적합합니다."}, "SenseChat-Turbo-1202": {"description": "최신 경량 버전 모델로, 전체 모델의 90% 이상의 능력을 달성하며, 추론 비용을 크게 줄였습니다."}, "SenseChat-Vision": {"description": "최신 버전 모델(V5.5)로, 다중 이미지 입력을 지원하며, 모델의 기본 능력 최적화를 전면적으로 구현하여 객체 속성 인식, 공간 관계, 동작 사건 인식, 장면 이해, 감정 인식, 논리 상식 추론 및 텍스트 이해 생성에서 큰 향상을 이루었습니다."}, "SenseNova-V6-5-Pro": {"description": "다중 모달, 언어 및 추론 데이터의 전면적인 업데이트와 학습 전략 최적화를 통해, 새로운 모델은 다중 모달 추론 및 일반화된 지침 준수 능력에서 현저한 향상을 이루었으며, 최대 128k의 컨텍스트 윈도우를 지원합니다. 또한 OCR 및 문화관광 IP 인식 등 특수 과제에서 뛰어난 성능을 보입니다."}, "SenseNova-V6-5-Turbo": {"description": "다중 모달, 언어 및 추론 데이터의 전면적인 업데이트와 학습 전략 최적화를 통해, 새로운 모델은 다중 모달 추론 및 일반화된 지침 준수 능력에서 현저한 향상을 이루었으며, 최대 128k의 컨텍스트 윈도우를 지원합니다. 또한 OCR 및 문화관광 IP 인식 등 특수 과제에서 뛰어난 성능을 보입니다."}, "SenseNova-V6-Pro": {"description": "이미지, 텍스트, 비디오 기능의 원주율 통합을 실현하여 전통적인 다중 모드의 분리 한계를 극복하고, OpenCompass와 SuperCLUE 평가에서 두 개의 챔피언을 차지했습니다."}, "SenseNova-V6-Reasoner": {"description": "시각과 언어의 깊이 있는 추론을 동시에 고려하여, 느린 사고와 깊이 있는 추론을 실현하고, 완전한 사고 과정의 연쇄를 제시합니다."}, "SenseNova-V6-Turbo": {"description": "이미지, 텍스트, 비디오 기능의 원주율 통합을 실현하여 전통적인 다중 모드의 분리 한계를 극복하고, 다중 모드 기본 능력, 언어 기본 능력 등 핵심 차원에서 전면적으로 앞서며, 문리 겸수하여 여러 평가에서 국내외 1위 수준에 여러 차례 올라섰습니다."}, "Skylark2-lite-8k": {"description": "구름제비(Skylark) 2세대 모델로, Skylark2-lite 모델은 높은 응답 속도를 자랑하며, 실시간 요구가 높은, 비용에 민감하고, 모델 정확도에 대한 요구가 낮은 장면에 적합하며, 컨텍스트 윈도우 길이는 8k입니다."}, "Skylark2-pro-32k": {"description": "구름제비(Skylark) 2세대 모델로, Skylark2-pro 버전은 높은 모델 정확도를 자랑하며, 전문 분야 문서 생성, 소설 창작, 고품질 번역 등 복잡한 텍스트 생성 장면에 적합하며, 컨텍스트 윈도우 길이는 32k입니다."}, "Skylark2-pro-4k": {"description": "구름제비(Skylark) 2세대 모델로, Skylark2-pro 모델은 높은 모델 정확도를 자랑하며, 전문 분야 문서 생성, 소설 창작, 고품질 번역 등 복잡한 텍스트 생성 장면에 적합하며, 컨텍스트 윈도우 길이는 4k입니다."}, "Skylark2-pro-character-4k": {"description": "구름제비(Skylark) 2세대 모델로, Skylark2-pro-character 모델은 우수한 역할 수행 및 채팅 능력을 갖추고 있으며, 사용자 프롬프트 요구에 따라 다양한 역할을 수행하고 자연스러운 대화를 이어갈 수 있습니다. 채팅봇, 가상 비서 및 온라인 고객 서비스 등을 구축하는 데 적합하며 높은 응답 속도를 자랑합니다."}, "Skylark2-pro-turbo-8k": {"description": "구름제비(Skylark) 2세대 모델로, Skylark2-pro-turbo-8k는 더 빠른 추론과 낮은 비용을 자랑하며, 컨텍스트 윈도우 길이는 8k입니다."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414는 GLM 시리즈의 차세대 오픈 소스 모델로, 320억 개의 매개변수를 가지고 있습니다. 이 모델은 OpenAI의 GPT 시리즈 및 DeepSeek의 V3/R1 시리즈와 성능이 비슷합니다."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414는 GLM 시리즈의 소형 모델로, 90억 개의 매개변수를 가지고 있습니다. 이 모델은 GLM-4-32B 시리즈의 기술적 특징을 계승하면서도 더 경량화된 배포 옵션을 제공합니다. 규모가 작음에도 불구하고, GLM-4-9B-0414는 코드 생성, 웹 디자인, SVG 그래픽 생성 및 검색 기반 작문 등 작업에서 뛰어난 능력을 보여줍니다."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking은 지푸 AI와 칭화대 KEG 연구실이 공동으로 발표한 오픈소스 비주얼 언어 모델(VLM)로, 복잡한 다중 모달 인지 작업을 처리하도록 설계되었습니다. 이 모델은 GLM-4-9B-0414 기본 모델을 기반으로 하며, '사고 사슬'(Chain-of-Thought) 추론 메커니즘을 도입하고 강화 학습 전략을 채택하여 교차 모달 추론 능력과 안정성을 크게 향상시켰습니다."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414는 깊은 사고 능력을 갖춘 추론 모델로, GLM-4-32B-0414를 기반으로 냉각 시작 및 확장 강화 학습을 통해 개발되었으며, 수학, 코드 및 논리 작업에서 추가 훈련을 받았습니다. 기본 모델에 비해 GLM-Z1-32B-0414는 수학 능력과 복잡한 작업 해결 능력이 크게 향상되었습니다."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414는 GLM 시리즈의 소형 모델로, 90억 개의 매개변수를 가지고 있지만 오픈 소스 전통을 유지하면서도 놀라운 능력을 보여줍니다. 규모가 작음에도 불구하고, 이 모델은 수학 추론 및 일반 작업에서 여전히 뛰어난 성능을 발휘하며, 동등한 규모의 오픈 소스 모델 중에서 선두 수준에 있습니다."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414는 깊은 추론 능력을 갖춘 모델로(OpenAI의 Deep Research와 비교됨), 전형적인 깊은 사고 모델과는 달리, 더 긴 시간 동안 깊은 사고를 통해 더 개방적이고 복잡한 문제를 해결합니다."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B 오픈 소스 버전으로, 대화 응용을 위한 최적화된 대화 경험을 제공합니다."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B는 강화 학습으로 훈련된 최초의 장문 컨텍스트 대형 추론 모델(LRM)로, 장문 텍스트 추론 작업에 최적화되어 있습니다. 점진적 컨텍스트 확장 강화 학습 프레임워크를 통해 짧은 컨텍스트에서 긴 컨텍스트로 안정적인 전이를 실현했습니다. 7개의 장문 문서 질의응답 벤치마크에서 OpenAI-o3-mini, Qwen3-235B-A22B 등 플래그십 모델을 능가하며 Claude-3.7-Sonnet-Thinking과 견줄 만한 성능을 보입니다. 특히 수학 추론, 논리 추론, 다중 점프 추론 등 복잡한 작업에 뛰어납니다."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B는 원래 시리즈 모델의 뛰어난 일반 언어 능력을 유지하면서, 5000억 개의 고품질 토큰을 통해 점진적으로 훈련하여 수학적 논리 및 코드 능력을 크게 향상시켰습니다."}, "abab5.5-chat": {"description": "생산성 시나리오를 위해 설계되었으며, 복잡한 작업 처리 및 효율적인 텍스트 생성을 지원하여 전문 분야 응용에 적합합니다."}, "abab5.5s-chat": {"description": "중국어 캐릭터 대화 시나리오를 위해 설계되었으며, 고품질의 중국어 대화 생성 능력을 제공하여 다양한 응용 시나리오에 적합합니다."}, "abab6.5g-chat": {"description": "다국어 캐릭터 대화를 위해 설계되었으며, 영어 및 기타 여러 언어의 고품질 대화 생성을 지원합니다."}, "abab6.5s-chat": {"description": "텍스트 생성, 대화 시스템 등 다양한 자연어 처리 작업에 적합합니다."}, "abab6.5t-chat": {"description": "중국어 캐릭터 대화 시나리오에 최적화되어 있으며, 유창하고 중국어 표현 습관에 맞는 대화 생성 능력을 제공합니다."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1은 최첨단 대형 언어 모델로, 강화 학습과 콜드 스타트 데이터를 최적화하여 뛰어난 추론, 수학 및 프로그래밍 성능을 제공합니다."}, "accounts/fireworks/models/deepseek-v3": {"description": "Deepseek에서 제공하는 강력한 Mixture-of-Experts (MoE) 언어 모델로, 총 매개변수 수는 671B이며, 각 토큰은 37B 매개변수를 활성화합니다."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Llama 3 70B 지시 모델은 다국어 대화 및 자연어 이해를 위해 최적화되어 있으며, 대부분의 경쟁 모델보다 성능이 우수합니다."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Llama 3 8B 지시 모델은 대화 및 다국어 작업을 위해 최적화되어 있으며, 뛰어난 성능과 효율성을 제공합니다."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Llama 3 8B 지시 모델(HF 버전)은 공식 구현 결과와 일치하며, 높은 일관성과 크로스 플랫폼 호환성을 갖추고 있습니다."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Llama 3.1 405B 지시 모델은 초대규모 매개변수를 갖추고 있어 복잡한 작업과 고부하 환경에서의 지시 따르기에 적합합니다."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Llama 3.1 70B 지시 모델은 뛰어난 자연어 이해 및 생성 능력을 제공하며, 대화 및 분석 작업에 이상적인 선택입니다."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Llama 3.1 8B 지시 모델은 다국어 대화를 위해 최적화되어 있으며, 일반 산업 기준에서 대부분의 오픈 소스 및 폐쇄 소스 모델을 초월합니다."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Meta의 11B 파라미터 지시 조정 이미지 추론 모델입니다. 이 모델은 시각 인식, 이미지 추론, 이미지 설명 및 이미지에 대한 일반적인 질문에 답변하기 위해 최적화되었습니다. 이 모델은 차트 및 그래프와 같은 시각 데이터를 이해할 수 있으며, 이미지 세부 사항을 설명하는 텍스트를 생성하여 시각과 언어 간의 격차를 메웁니다."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B 지시 모델은 Meta가 출시한 경량 다국어 모델입니다. 이 모델은 효율성을 높이기 위해 설계되었으며, 더 큰 모델에 비해 지연 시간과 비용에서 상당한 개선을 제공합니다. 이 모델의 예시 사용 사례에는 쿼리 및 프롬프트 재작성, 작문 지원이 포함됩니다."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Meta의 90B 파라미터 지시 조정 이미지 추론 모델입니다. 이 모델은 시각 인식, 이미지 추론, 이미지 설명 및 이미지에 대한 일반적인 질문에 답변하기 위해 최적화되었습니다. 이 모델은 차트 및 그래프와 같은 시각 데이터를 이해할 수 있으며, 이미지 세부 사항을 설명하는 텍스트를 생성하여 시각과 언어 간의 격차를 메웁니다."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct는 Llama 3.1 70B의 12월 업데이트 버전입니다. 이 모델은 Llama 3.1 70B(2024년 7월 출시)를 기반으로 개선되어 도구 호출, 다국어 텍스트 지원, 수학 및 프로그래밍 능력을 강화했습니다. 이 모델은 추론, 수학 및 지시 준수에서 업계 최고 수준에 도달했으며, 3.1 405B와 유사한 성능을 제공하면서 속도와 비용에서 상당한 이점을 가지고 있습니다."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "24B 매개변수 모델로, 더 큰 모델과 동등한 최첨단 능력을 갖추고 있습니다."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B 지시 모델은 대규모 매개변수와 다수의 전문가 아키텍처를 통해 복잡한 작업의 효율적인 처리를 전방위적으로 지원합니다."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B 지시 모델은 다수의 전문가 아키텍처를 통해 효율적인 지시 따르기 및 실행을 제공합니다."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMax L2 13B 모델은 혁신적인 통합 기술을 결합하여 서사 및 역할 수행에 강점을 보입니다."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision 지시 모델은 경량 다중 모달 모델로, 복잡한 시각 및 텍스트 정보를 처리할 수 있으며, 강력한 추론 능력을 갖추고 있습니다."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "QwQ 모델은 Qwen 팀이 개발한 실험적 연구 모델로, AI 추론 능력을 향상시키는 데 중점을 두고 있습니다."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Qwen-VL 모델의 72B 버전은 알리바바의 최신 반복 결과로, 거의 1년간의 혁신을 대표합니다."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5는 Alibaba Cloud Qwen 팀이 개발한 일련의 디코더 전용 언어 모델입니다. 이러한 모델은 0.5B, 1.5B, 3B, 7B, 14B, 32B 및 72B와 같은 다양한 크기를 제공하며, 기본 버전과 지시 버전 두 가지 변형이 있습니다."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct는 Alibaba Cloud에서 발표한 코드 특화 대규모 언어 모델 시리즈의 최신 버전입니다. 이 모델은 Qwen2.5를 기반으로 하여 55조 개의 토큰으로 훈련되어 코드 생성, 추론 및 수정 능력을 크게 향상시켰습니다. 이 모델은 코딩 능력을 강화할 뿐만 아니라 수학 및 일반 능력의 장점도 유지합니다. 모델은 코드 에이전트와 같은 실제 응용 프로그램에 더 포괄적인 기반을 제공합니다."}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Large 모델은 뛰어난 다국어 처리 능력을 갖추고 있으며, 다양한 언어 생성 및 이해 작업에 사용될 수 있습니다."}, "ai21-jamba-1.5-large": {"description": "398B 매개변수(94B 활성)의 다국어 모델로, 256K 긴 컨텍스트 창, 함수 호출, 구조화된 출력 및 기반 생성 기능을 제공합니다."}, "ai21-jamba-1.5-mini": {"description": "52B 매개변수(12B 활성)의 다국어 모델로, 256K 긴 컨텍스트 창, 함수 호출, 구조화된 출력 및 기반 생성 기능을 제공합니다."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "398B 매개변수(활성 94B)를 가진 다국어 모델로, 256K 길이의 컨텍스트 창, 함수 호출, 구조화된 출력 및 사실 기반 생성을 제공합니다."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "52B 매개변수(활성 12B)를 가진 다국어 모델로, 256K 길이의 컨텍스트 창, 함수 호출, 구조화된 출력 및 사실 기반 생성을 제공합니다."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet는 업계 표준을 향상시켜 경쟁 모델 및 Claude 3 Opus를 초월하며, 광범위한 평가에서 뛰어난 성능을 보이고, 중간 수준 모델의 속도와 비용을 갖추고 있습니다."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet는 업계 표준을 향상시켰으며, 경쟁 모델과 Claude 3 Opus를 초월하는 성능을 보여주고, 광범위한 평가에서 뛰어난 성과를 보였습니다. 또한 중간 수준 모델의 속도와 비용을 갖추고 있습니다."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku는 Anthropic의 가장 빠르고 간결한 모델로, 거의 즉각적인 응답 속도를 제공합니다. 간단한 질문과 요청에 신속하게 답변할 수 있습니다. 고객은 인간 상호작용을 모방하는 원활한 AI 경험을 구축할 수 있습니다. Claude 3 Haiku는 이미지를 처리하고 텍스트 출력을 반환할 수 있으며, 200K의 컨텍스트 창을 갖추고 있습니다."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus는 Anthropic의 가장 강력한 AI 모델로, 매우 복잡한 작업에서 최첨단 성능을 발휘합니다. 개방형 프롬프트와 이전에 보지 못한 장면을 처리할 수 있으며, 뛰어난 유창성과 인간과 유사한 이해 능력을 갖추고 있습니다. Claude 3 Opus는 생성 AI의 가능성을 보여줍니다. Claude 3 Opus는 이미지를 처리하고 텍스트 출력을 반환할 수 있으며, 200K의 컨텍스트 창을 갖추고 있습니다."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Anthropic의 Claude 3 Sonnet는 지능과 속도 간의 이상적인 균형을 이루어 기업 작업 부하에 특히 적합합니다. 경쟁 모델보다 낮은 가격으로 최대의 효용을 제공하며, 신뢰할 수 있고 내구성이 뛰어난 주력 모델로 설계되어 대규모 AI 배포에 적합합니다. Claude 3 Sonnet는 이미지를 처리하고 텍스트 출력을 반환할 수 있으며, 200K의 컨텍스트 창을 갖추고 있습니다."}, "anthropic.claude-instant-v1": {"description": "일상 대화, 텍스트 분석, 요약 및 문서 질문 응답을 포함한 다양한 작업을 처리할 수 있는 빠르고 경제적이며 여전히 매우 유능한 모델입니다."}, "anthropic.claude-v2": {"description": "Anthropic은 복잡한 대화 및 창의적 콘텐츠 생성에서부터 세부 지시 준수에 이르기까지 광범위한 작업에서 높은 능력을 발휘하는 모델입니다."}, "anthropic.claude-v2:1": {"description": "Claude 2의 업데이트 버전으로, 두 배의 컨텍스트 창을 갖추고 있으며, 긴 문서 및 RAG 컨텍스트에서의 신뢰성, 환각률 및 증거 기반 정확성이 개선되었습니다."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku는 Anthropic의 가장 빠르고 컴팩트한 모델로, 거의 즉각적인 응답을 목표로 합니다. 빠르고 정확한 방향성 성능을 제공합니다."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus는 Anthropic이 복잡한 작업을 처리하기 위해 개발한 가장 강력한 모델입니다. 성능, 지능, 유창성 및 이해력에서 뛰어난 성과를 보입니다."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku는 Anthropic의 가장 빠른 차세대 모델입니다. <PERSON> 3 Haiku와 비교하여 Claude 3.5 Haiku는 모든 기술에서 향상되었으며, 많은 지능 벤치마크 테스트에서 이전 세대의 가장 큰 모델인 Claude 3 Opus를 초월했습니다."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet은 Opus를 초월하는 능력과 Sonnet보다 더 빠른 속도를 제공하며, Sonnet과 동일한 가격을 유지합니다. Sonnet은 프로그래밍, 데이터 과학, 비주얼 처리 및 에이전트 작업에 특히 강합니다."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet은 Anthropic이 지금까지 개발한 가장 지능적인 모델로, 시장에서 최초의 혼합 추론 모델입니다. Claude 3.7 Sonnet은 거의 즉각적인 응답이나 연장된 단계적 사고를 생성할 수 있으며, 사용자는 이러한 과정을 명확하게 볼 수 있습니다. Sonnet은 프로그래밍, 데이터 과학, 시각 처리, 대리 작업에 특히 뛰어납니다."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4는 Anthropic에서 고도로 복잡한 작업을 처리하기 위해 개발한 가장 강력한 모델입니다. 성능, 지능, 유창성 및 이해력 면에서 뛰어난 성과를 보입니다."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4는 거의 즉각적인 응답이나 단계별 심층 사고를 생성할 수 있으며, 사용자는 이러한 과정을 명확하게 볼 수 있습니다. API 사용자는 모델의 사고 시간을 세밀하게 제어할 수도 있습니다."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B는 720억 개의 파라미터와 160억 활성 파라미터를 가진 희소 대형 언어 모델로, 그룹 혼합 전문가(MoGE) 아키텍처를 기반으로 합니다. 전문가 선택 단계에서 전문가를 그룹화하고 각 그룹 내에서 토큰이 동일 수의 전문가를 활성화하도록 제한하여 전문가 부하 균형을 달성함으로써 Ascend 플랫폼에서의 모델 배포 효율성을 크게 향상시켰습니다."}, "aya": {"description": "Aya 23은 Cohere에서 출시한 다국어 모델로, 23개 언어를 지원하여 다양한 언어 응용에 편리함을 제공합니다."}, "aya:35b": {"description": "Aya 23은 Cohere에서 출시한 다국어 모델로, 23개 언어를 지원하여 다양한 언어 응용에 편리함을 제공합니다."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B는 백천 인공지능이 개발한 130억 개의 매개변수를 가진 오픈 소스 상용 대형 언어 모델로, 권위 있는 중국어 및 영어 벤치마크에서 동일한 크기에서 최고의 성과를 달성했습니다."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B는 바이두에서 개발한 혼합 전문가(MoE) 아키텍처 기반의 대형 언어 모델입니다. 총 3천억 개의 파라미터를 보유하지만 추론 시 각 토큰당 470억 파라미터만 활성화하여 강력한 성능과 계산 효율성을 동시에 달성합니다. ERNIE 4.5 시리즈의 핵심 모델 중 하나로, 텍스트 이해, 생성, 추론 및 프로그래밍 작업에서 뛰어난 능력을 보여줍니다. 이 모델은 텍스트와 시각 모달리티의 공동 학습을 통한 혁신적인 다중 모달 이기종 MoE 사전학습 방식을 채택하여, 특히 명령 준수와 세계 지식 기억 측면에서 탁월한 성능을 발휘합니다."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse는 지시 조정, 데이터 차익 거래, 선호 훈련 및 모델 통합의 혁신을 통해 단일 언어 모델의 성능에 도전하는 고성능 32B 다국어 모델입니다. 23개 언어를 지원합니다."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse는 지시 조정, 데이터 차익 거래, 선호 훈련 및 모델 통합의 혁신을 통해 단일 언어 모델의 성능에 도전하는 고성능 8B 다국어 모델입니다. 23개 언어를 지원합니다."}, "c4ai-aya-vision-32b": {"description": "Aya Vision은 언어, 텍스트 및 이미지 능력의 여러 주요 기준에서 뛰어난 성능을 발휘하는 최첨단 다중 모달 모델입니다. 23개 언어를 지원합니다. 이 320억 매개변수 버전은 최첨단 다국어 성능에 중점을 두고 있습니다."}, "c4ai-aya-vision-8b": {"description": "Aya Vision은 언어, 텍스트 및 이미지 능력의 여러 주요 기준에서 뛰어난 성능을 발휘하는 최첨단 다중 모달 모델입니다. 이 80억 매개변수 버전은 낮은 지연 시간과 최상의 성능에 중점을 두고 있습니다."}, "charglm-3": {"description": "CharGLM-3는 역할 수행 및 감정 동반을 위해 설계된 모델로, 초장 다회 기억 및 개인화된 대화를 지원하여 광범위하게 사용됩니다."}, "charglm-4": {"description": "CharGLM-4는 역할 놀이 및 감정 동반을 위해 설계되었으며, 초장기 다중 회상 및 개인화된 대화를 지원하여 광범위하게 활용됩니다."}, "chatglm3": {"description": "ChatGLM3는 지품 AI와 청화 KEG 연구실에서 발표한 폐원 모델로, 대량의 중영 식별자 사전 학습과 인간 선호도 맞춤 학습을 거쳤습니다. 1세대 모델에 비해 MMLU, C-Eval, GSM8K에서 각각 16%, 36%, 280%의 향상을 이루었으며, 중국어 작업 차트 C-Eval에서 1위를 차지했습니다. 이 모델은 지식량, 추론 능력, 창의력이 요구되는 상황, 예를 들어 광고 문안, 소설 작성, 지식 기반 작문, 코드 생성 등에 적합합니다."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base는 지푸에서 개발한 ChatGLM 시리즈의 최신 세대 60억 개 매개변수 규모의 오픈소스 기반 모델입니다."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o는 동적 모델로, 최신 버전을 유지하기 위해 실시간으로 업데이트됩니다. 강력한 언어 이해 및 생성 능력을 결합하여 고객 서비스, 교육 및 기술 지원을 포함한 대규모 응용 프로그램에 적합합니다."}, "claude-2.0": {"description": "Claude 2는 기업에 중요한 능력의 발전을 제공하며, 업계 최고의 200K 토큰 컨텍스트, 모델 환각 발생률 대폭 감소, 시스템 프롬프트 및 새로운 테스트 기능인 도구 호출을 포함합니다."}, "claude-2.1": {"description": "Claude 2는 기업에 중요한 능력의 발전을 제공하며, 업계 최고의 200K 토큰 컨텍스트, 모델 환각 발생률 대폭 감소, 시스템 프롬프트 및 새로운 테스트 기능인 도구 호출을 포함합니다."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku는 Anthropic의 가장 빠른 차세대 모델입니다. <PERSON> 3 Haiku와 비교할 때, <PERSON> 3.5 Haiku는 모든 기술에서 향상되었으며, 많은 지능 기준 테스트에서 이전 세대의 가장 큰 모델인 <PERSON> 3 Opus를 초월했습니다."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet은 Opus를 초월하는 능력과 Sonnet보다 더 빠른 속도를 제공하며, Sonnet과 동일한 가격을 유지합니다. Sonnet은 프로그래밍, 데이터 과학, 시각 처리 및 대리 작업에 특히 강합니다."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet은 Opus를 초월하는 능력과 Sonnet보다 빠른 속도를 제공하면서도 Sonnet과 동일한 가격을 유지합니다. Sonnet은 프로그래밍, 데이터 과학, 비주얼 처리 및 대리 작업에 특히 뛰어납니다."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet은 Opus를 초월하는 능력과 Sonnet보다 더 빠른 속도를 제공하며, Sonnet과 동일한 가격을 유지합니다. Sonnet은 프로그래밍, 데이터 과학, 비주얼 처리 및 에이전트 작업에 특히 강합니다."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku는 Anthropic의 가장 빠르고 컴팩트한 모델로, 거의 즉각적인 응답을 목표로 합니다. 빠르고 정확한 방향성 성능을 갖추고 있습니다."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus는 Anthropic이 고도로 복잡한 작업을 처리하기 위해 개발한 가장 강력한 모델입니다. 성능, 지능, 유창성 및 이해력에서 뛰어난 성능을 보입니다."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet은 기업 작업 부하에 이상적인 균형을 제공하며, 더 낮은 가격으로 최대 효용을 제공합니다. 신뢰성이 높고 대규모 배포에 적합합니다."}, "claude-opus-4-20250514": {"description": "<PERSON> Opus 4는 Anthropic이 매우 복잡한 작업을 처리하기 위해 개발한 가장 강력한 모델입니다. 성능, 지능, 유창성 및 이해력 면에서 뛰어난 성과를 보입니다."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet은 거의 즉각적인 응답 또는 점진적인 사고 과정을 생성할 수 있으며, 사용자는 이러한 과정을 명확하게 볼 수 있습니다. API 사용자는 모델의 사고 시간을 세밀하게 제어할 수 있습니다."}, "codegeex-4": {"description": "CodeGeeX-4는 강력한 AI 프로그래밍 도우미로, 다양한 프로그래밍 언어에 대한 스마트 Q&A 및 코드 완성을 지원하여 개발 효율성을 높입니다."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B는 다국어 코드 생성 모델로, 코드 완성 및 생성, 코드 해석기, 웹 검색, 함수 호출, 저장소 수준의 코드 질문 응답 등 다양한 기능을 지원하여 소프트웨어 개발의 여러 시나리오를 포괄합니다. 10B 미만의 매개변수를 가진 최고의 코드 생성 모델입니다."}, "codegemma": {"description": "CodeGemma는 다양한 프로그래밍 작업을 위한 경량 언어 모델로, 빠른 반복 및 통합을 지원합니다."}, "codegemma:2b": {"description": "CodeGemma는 다양한 프로그래밍 작업을 위한 경량 언어 모델로, 빠른 반복 및 통합을 지원합니다."}, "codellama": {"description": "Code Llama는 코드 생성 및 논의에 중점을 둔 LLM으로, 광범위한 프로그래밍 언어 지원을 결합하여 개발자 환경에 적합합니다."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama는 코드 생성 및 논의에 중점을 둔 LLM으로, 광범위한 프로그래밍 언어 지원을 결합하여 개발자 환경에 적합합니다."}, "codellama:13b": {"description": "Code Llama는 코드 생성 및 논의에 중점을 둔 LLM으로, 광범위한 프로그래밍 언어 지원을 결합하여 개발자 환경에 적합합니다."}, "codellama:34b": {"description": "Code Llama는 코드 생성 및 논의에 중점을 둔 LLM으로, 광범위한 프로그래밍 언어 지원을 결합하여 개발자 환경에 적합합니다."}, "codellama:70b": {"description": "Code Llama는 코드 생성 및 논의에 중점을 둔 LLM으로, 광범위한 프로그래밍 언어 지원을 결합하여 개발자 환경에 적합합니다."}, "codeqwen": {"description": "CodeQwen1.5는 대량의 코드 데이터로 훈련된 대형 언어 모델로, 복잡한 프로그래밍 작업을 해결하기 위해 설계되었습니다."}, "codestral": {"description": "Codestral은 Mistral AI의 첫 번째 코드 모델로, 코드 생성 작업에 뛰어난 지원을 제공합니다."}, "codestral-latest": {"description": "Codestral은 코드 생성을 전문으로 하는 최첨단 생성 모델로, 중간 채우기 및 코드 완성 작업을 최적화했습니다."}, "codex-mini-latest": {"description": "codex-mini-latest는 o4-mini의 미세 조정 버전으로, Codex CLI 전용입니다. API를 통해 직접 사용하려면 gpt-4.1부터 시작하는 것을 권장합니다."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B는 지시 준수, 대화 및 프로그래밍을 위해 설계된 모델입니다."}, "cogview-4": {"description": "CogView-4는 지푸가 처음으로 한자 생성을 지원하는 오픈 소스 텍스트-이미지 생성 모델로, 의미 이해, 이미지 생성 품질, 중영 문자 생성 능력 등 여러 측면에서 전면적으로 향상되었으며, 임의 길이의 중영 이중 언어 입력을 지원하고 주어진 범위 내에서 임의 해상도의 이미지를 생성할 수 있습니다."}, "cohere-command-r": {"description": "Command R은 RAG 및 도구 사용을 목표로 하는 확장 가능한 생성 모델로, 기업을 위한 생산 규모 AI를 가능하게 합니다."}, "cohere-command-r-plus": {"description": "Command R+는 기업급 작업을 처리하기 위해 설계된 최첨단 RAG 최적화 모델입니다."}, "cohere/Cohere-command-r": {"description": "Command R은 RAG 및 도구 사용에 최적화된 확장 가능한 생성 모델로, 기업이 생산 수준의 AI를 구현할 수 있도록 설계되었습니다."}, "cohere/Cohere-command-r-plus": {"description": "Command R+는 최첨단 RAG 최적화 모델로, 기업용 워크로드에 대응하도록 설계되었습니다."}, "command": {"description": "지시를 따르는 대화 모델로, 언어 작업에서 높은 품질과 신뢰성을 제공하며, 우리의 기본 생성 모델에 비해 더 긴 컨텍스트 길이를 가지고 있습니다."}, "command-a-03-2025": {"description": "Command A는 지금까지 성능이 가장 뛰어난 모델로, 도구 사용, 에이전트, 검색 강화 생성(RAG) 및 다국어 응용 시나리오에서 뛰어난 성능을 발휘합니다. Command A는 256K의 컨텍스트 길이를 가지고 있으며, 두 개의 GPU만으로 실행할 수 있으며, Command R+ 08-2024에 비해 처리량이 150% 향상되었습니다."}, "command-light": {"description": "더 작고 빠른 Command 버전으로, 거의 동일한 강력함을 가지고 있지만 더 빠릅니다."}, "command-light-nightly": {"description": "주요 버전 출시 간의 시간 간격을 단축하기 위해 Command 모델의 매일 버전을 출시했습니다. command-light 시리즈의 경우 이 버전은 command-light-nightly로 불립니다. command-light-nightly는 최신이며 가장 실험적이고(아마도) 불안정한 버전입니다. 매일 버전은 정기적으로 업데이트되며 사전 통지 없이 제공되므로 생산 환경에서 사용하지 않는 것이 좋습니다."}, "command-nightly": {"description": "주요 버전 출시 간의 시간 간격을 단축하기 위해 Command 모델의 매일 버전을 출시했습니다. Command 시리즈의 경우 이 버전은 command-cightly로 불립니다. command-nightly는 최신이며 가장 실험적이고(아마도) 불안정한 버전입니다. 매일 버전은 정기적으로 업데이트되며 사전 통지 없이 제공되므로 생산 환경에서 사용하지 않는 것이 좋습니다."}, "command-r": {"description": "Command R은 대화 및 긴 컨텍스트 작업에 최적화된 LLM으로, 동적 상호작용 및 지식 관리에 특히 적합합니다."}, "command-r-03-2024": {"description": "Command R은 지시를 따르는 대화 모델로, 언어 작업에서 더 높은 품질과 신뢰성을 제공하며, 이전 모델에 비해 더 긴 컨텍스트 길이를 가지고 있습니다. 코드 생성, 검색 강화 생성(RAG), 도구 사용 및 에이전트와 같은 복잡한 워크플로우에 사용할 수 있습니다."}, "command-r-08-2024": {"description": "command-r-08-2024는 Command R 모델의 업데이트 버전으로, 2024년 8월에 출시되었습니다."}, "command-r-plus": {"description": "Command R+는 실제 기업 환경 및 복잡한 응용을 위해 설계된 고성능 대형 언어 모델입니다."}, "command-r-plus-04-2024": {"description": "Command R+는 지시를 따르는 대화 모델로, 언어 작업에서 더 높은 품질과 신뢰성을 제공하며, 이전 모델에 비해 더 긴 컨텍스트 길이를 가지고 있습니다. 복잡한 RAG 워크플로우와 다단계 도구 사용에 가장 적합합니다."}, "command-r-plus-08-2024": {"description": "Command R+는 지시를 따르는 대화 모델로, 언어 작업에서 더 높은 품질과 신뢰성을 제공하며, 이전 모델에 비해 더 긴 문맥 길이를 지원합니다. 복잡한 RAG 워크플로우와 다단계 도구 사용에 가장 적합합니다."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024는 소형이면서도 효율적인 업데이트 버전으로, 2024년 12월에 출시되었습니다. RAG, 도구 사용, 에이전트 등 복잡한 추론과 다단계 처리가 필요한 작업에서 뛰어난 성능을 발휘합니다."}, "compound-beta": {"description": "Compound-beta는 GroqCloud에서 지원하는 여러 개방형 모델로 구성된 복합 AI 시스템으로, 사용자의 쿼리에 답변하기 위해 도구를 지능적으로 선택적으로 사용할 수 있습니다."}, "compound-beta-mini": {"description": "Compound-beta-mini는 GroqCloud에서 지원하는 공개 가능한 모델로 구성된 복합 AI 시스템으로, 사용자의 쿼리에 답변하기 위해 도구를 지능적으로 선택적으로 사용할 수 있습니다."}, "computer-use-preview": {"description": "computer-use-preview 모델은 '컴퓨터 사용 도구'를 위해 특별히 설계된 전용 모델로, 컴퓨터 관련 작업을 이해하고 수행하도록 훈련되었습니다."}, "dall-e-2": {"description": "2세대 DALL·E 모델로, 더 사실적이고 정확한 이미지 생성을 지원하며, 해상도는 1세대의 4배입니다."}, "dall-e-3": {"description": "최신 DALL·E 모델로, 2023년 11월에 출시되었습니다. 더 사실적이고 정확한 이미지 생성을 지원하며, 세부 표현력이 강화되었습니다."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct는 높은 신뢰성을 가진 지시 처리 능력을 제공하며, 다양한 산업 응용을 지원합니다."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1은 강화 학습(RL) 기반의 추론 모델로, 모델 내의 반복성과 가독성 문제를 해결합니다. RL 이전에 DeepSeek-R1은 콜드 스타트 데이터를 도입하여 추론 성능을 더욱 최적화했습니다. 수학, 코드 및 추론 작업에서 OpenAI-o1과 유사한 성능을 보이며, 정교하게 설계된 훈련 방법을 통해 전체적인 효과를 향상시켰습니다."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1은 증가된 계산 자원과 후속 훈련 과정에서 도입된 알고리즘 최적화 메커니즘을 활용하여 추론 및 추론 능력의 깊이를 크게 향상시켰습니다. 이 모델은 수학, 프로그래밍, 일반 논리 등 다양한 벤치마크 평가에서 뛰어난 성능을 보이며, 전체 성능은 O3 및 Gemini 2.5 Pro와 같은 선도 모델에 근접합니다."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B는 DeepSeek-R1-0528 모델에서 사고 과정(chain-of-thought)을 증류하여 Qwen3 8B Base에 적용한 모델입니다. 오픈소스 모델 중 최첨단(SOTA) 성능을 달성했으며, AIME 2024 테스트에서 Qwen3 8B를 10% 능가하고 Qwen3-235B-thinking 수준의 성능을 보입니다. 수학 추론, 프로그래밍, 일반 논리 등 여러 벤치마크에서 뛰어난 성능을 보이며, Qwen3-8B와 동일한 아키텍처를 사용하지만 DeepSeek-R1-0528의 토크나이저 구성을 공유합니다."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1 증류 모델로, 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신합니다."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 증류 모델로, 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신합니다."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 증류 모델로, 강화 학습과 콜드 스타트 데이터를 통해 추론 성능을 최적화하며, 오픈 소스 모델로 다중 작업 기준을 갱신합니다."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B는 Qwen2.5-32B를 기반으로 지식 증류를 통해 얻은 모델입니다. 이 모델은 DeepSeek-R1이 생성한 80만 개의 선별된 샘플을 사용하여 미세 조정되었으며, 수학, 프로그래밍 및 추론 등 여러 분야에서 뛰어난 성능을 보여줍니다. AIME 2024, MATH-500, GPQA Diamond 등 여러 기준 테스트에서 우수한 성적을 거두었으며, MATH-500에서 94.3%의 정확도를 달성하여 강력한 수학 추론 능력을 보여줍니다."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-7B는 Qwen2.5-Math-7B를 기반으로 지식 증류를 통해 얻은 모델입니다. 이 모델은 DeepSeek-R1이 생성한 80만 개의 선별된 샘플을 사용하여 미세 조정되었으며, 뛰어난 추론 능력을 보여줍니다. 여러 기준 테스트에서 우수한 성적을 거두었으며, MATH-500에서 92.8%의 정확도를 달성하고, AIME 2024에서 55.5%의 통과율을 기록했으며, CodeForces에서 1189의 점수를 얻어 7B 규모의 모델로서 강력한 수학 및 프로그래밍 능력을 보여줍니다."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5는 이전 버전의 우수한 기능을 집약하여 일반 및 인코딩 능력을 강화했습니다."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3는 6710억 개의 매개변수를 가진 혼합 전문가(MoE) 언어 모델로, 다중 헤드 잠재 주의(MLA) 및 DeepSeekMoE 아키텍처를 채택하여 보조 손실 없는 부하 균형 전략을 결합하여 추론 및 훈련 효율성을 최적화합니다. 14.8조 개의 고품질 토큰에서 사전 훈련을 수행하고 감독 미세 조정 및 강화 학습을 통해 DeepSeek-V3는 성능 면에서 다른 오픈 소스 모델을 초월하며, 선도적인 폐쇄형 모델에 근접합니다."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B는 고복잡성 대화를 위해 훈련된 고급 모델입니다."}, "deepseek-ai/deepseek-r1": {"description": "추론, 수학 및 프로그래밍에 능숙한 최첨단 효율 LLM입니다."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2는 DeepSeekMoE-27B를 기반으로 개발된 혼합 전문가(MoE) 비주얼 언어 모델로, 희소 활성화 MoE 아키텍처를 사용하여 4.5B 매개변수만 활성화된 상태에서 뛰어난 성능을 발휘합니다. 이 모델은 비주얼 질문 응답, 광학 문자 인식, 문서/표/차트 이해 및 비주얼 위치 지정 등 여러 작업에서 우수한 성과를 보입니다."}, "deepseek-chat": {"description": "일반 및 코드 능력을 융합한 새로운 오픈 소스 모델로, 기존 Chat 모델의 일반 대화 능력과 Coder 모델의 강력한 코드 처리 능력을 유지하면서 인간의 선호에 더 잘 맞춰졌습니다. 또한, DeepSeek-V2.5는 작문 작업, 지시 따르기 등 여러 측면에서 큰 향상을 이루었습니다."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B는 코드 언어 모델로, 20조 개의 데이터로 훈련되었으며, 그 중 87%는 코드, 13%는 중문 및 영문입니다. 모델은 16K 창 크기와 빈칸 채우기 작업을 도입하여 프로젝트 수준의 코드 완성과 코드 조각 채우기 기능을 제공합니다."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2는 오픈 소스 혼합 전문가 코드 모델로, 코드 작업에서 뛰어난 성능을 발휘하며, GPT4-Turbo와 경쟁할 수 있습니다."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2는 오픈 소스 혼합 전문가 코드 모델로, 코드 작업에서 뛰어난 성능을 발휘하며, GPT4-Turbo와 경쟁할 수 있습니다."}, "deepseek-r1": {"description": "DeepSeek-R1은 강화 학습(RL) 기반의 추론 모델로, 모델 내의 반복성과 가독성 문제를 해결합니다. RL 이전에 DeepSeek-R1은 콜드 스타트 데이터를 도입하여 추론 성능을 더욱 최적화했습니다. 수학, 코드 및 추론 작업에서 OpenAI-o1과 유사한 성능을 보이며, 정교하게 설계된 훈련 방법을 통해 전체적인 효과를 향상시켰습니다."}, "deepseek-r1-0528": {"description": "685B 풀스펙 모델로, 2025년 5월 28일에 출시되었습니다. DeepSeek-R1은 후학습 단계에서 대규모 강화 학습 기술을 활용하여 극소수의 라벨 데이터만으로도 모델의 추론 능력을 크게 향상시켰습니다. 수학, 코드, 자연어 추론 등 과제에서 높은 성능과 강력한 능력을 자랑합니다."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B 빠른 버전으로, 실시간 온라인 검색을 지원하며 모델 성능을 유지하면서 더 빠른 응답 속도를 제공합니다."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B 표준 버전으로, 실시간 온라인 검색을 지원하며 최신 정보가 필요한 대화 및 텍스트 처리 작업에 적합합니다."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama는 DeepSeek-R1에서 추출한 Llama 기반 모델입니다."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - DeepSeek 패키지에서 더 크고 더 스마트한 모델이 Llama 70B 아키텍처로 증류되었습니다. 기준 테스트와 인공지능 평가에 따르면, 이 모델은 원래 Llama 70B보다 더 스마트하며, 특히 수학 및 사실 정확성이 필요한 작업에서 뛰어난 성능을 보입니다."}, "deepseek-r1-distill-llama-8b": {"description": "DeepSeek-R1-Distill 시리즈 모델은 지식 증류 기술을 통해 DeepSeek-R1이 생성한 샘플을 <PERSON><PERSON>, Llama 등 오픈 소스 모델에 미세 조정하여 얻은 것입니다."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "2025년 2월 14일 최초 출시된 이 모델은 천범 대모델 연구팀이 Llama3_70B를 기반 모델로 하여(메타 라마로 구축) 증류한 것입니다. 증류 데이터에는 천범의 말뭉치도 동기화되어 추가되었습니다."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "2025년 2월 14일 최초 출시된 이 모델은 천범 대모델 연구팀이 Llama3_8B를 기반 모델로 하여(메타 라마로 구축) 증류한 것입니다. 증류 데이터에는 천범의 말뭉치도 동기화되어 추가되었습니다."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen은 DeepSeek-R1에서 Qwen을 기반으로 증류된 모델입니다."}, "deepseek-r1-distill-qwen-1.5b": {"description": "DeepSeek-R1-Distill 시리즈 모델은 지식 증류 기술을 통해 DeepSeek-R1이 생성한 샘플을 <PERSON><PERSON>, Llama 등 오픈 소스 모델에 미세 조정하여 얻은 것입니다."}, "deepseek-r1-distill-qwen-14b": {"description": "DeepSeek-R1-Distill 시리즈 모델은 지식 증류 기술을 통해 DeepSeek-R1이 생성한 샘플을 <PERSON><PERSON>, Llama 등 오픈 소스 모델에 미세 조정하여 얻은 것입니다."}, "deepseek-r1-distill-qwen-32b": {"description": "DeepSeek-R1-Distill 시리즈 모델은 지식 증류 기술을 통해 DeepSeek-R1이 생성한 샘플을 <PERSON><PERSON>, Llama 등 오픈 소스 모델에 미세 조정하여 얻은 것입니다."}, "deepseek-r1-distill-qwen-7b": {"description": "DeepSeek-R1-Distill 시리즈 모델은 지식 증류 기술을 통해 DeepSeek-R1이 생성한 샘플을 <PERSON><PERSON>, Llama 등 오픈 소스 모델에 미세 조정하여 얻은 것입니다."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 풀 빠른 버전으로, 실시간 온라인 검색을 지원하며 671B 매개변수의 강력한 능력과 더 빠른 응답 속도를 결합합니다."}, "deepseek-r1-online": {"description": "DeepSeek R1 풀 버전으로, 671B 매개변수를 가지고 있으며 실시간 온라인 검색을 지원하여 더 강력한 이해 및 생성 능력을 제공합니다."}, "deepseek-reasoner": {"description": "DeepSeek에서 제공하는 추론 모델입니다. 최종 답변을 출력하기 전에 모델은 먼저 사고 과정을 출력하여 최종 답변의 정확성을 높입니다."}, "deepseek-v2": {"description": "DeepSeek V2는 경제적이고 효율적인 처리 요구에 적합한 Mixture-of-Experts 언어 모델입니다."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B는 DeepSeek의 설계 코드 모델로, 강력한 코드 생성 능력을 제공합니다."}, "deepseek-v3": {"description": "DeepSeek-V3는 항저우 심도 탐색 인공지능 기초 기술 연구 회사에서 자체 개발한 MoE 모델로, 여러 평가에서 뛰어난 성적을 거두며, 주류 순위에서 오픈 소스 모델 1위를 차지하고 있습니다. V3는 V2.5 모델에 비해 생성 속도가 3배 향상되어 사용자에게 더 빠르고 원활한 사용 경험을 제공합니다."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324는 671B 매개변수를 가진 MoE 모델로, 프로그래밍 및 기술 능력, 맥락 이해 및 긴 텍스트 처리 등에서 두드러진 장점을 보입니다."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3는 685B 매개변수를 가진 전문가 혼합 모델로, DeepSeek 팀의 플래그십 채팅 모델 시리즈의 최신 반복입니다.\n\n이 모델은 [DeepSeek V3](/deepseek/deepseek-chat-v3) 모델을 계승하며 다양한 작업에서 뛰어난 성능을 보입니다."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3는 685B 매개변수를 가진 전문가 혼합 모델로, DeepSeek 팀의 플래그십 채팅 모델 시리즈의 최신 반복입니다.\n\n이 모델은 [DeepSeek V3](/deepseek/deepseek-chat-v3) 모델을 계승하며 다양한 작업에서 뛰어난 성능을 보입니다."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1은 극히 적은 주석 데이터로 모델의 추론 능력을 크게 향상시킵니다. 최종 답변을 출력하기 전에 모델은 먼저 사고의 연쇄 내용을 출력하여 최종 답변의 정확성을 높입니다."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1은 매우 적은 라벨 데이터만으로도 모델 추론 능력을 크게 향상시켰습니다. 최종 답변 출력 전에 모델이 사고 과정(chain-of-thought)을 먼저 출력하여 최종 답변의 정확도를 높입니다."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1은 매우 적은 라벨 데이터만으로도 모델 추론 능력을 크게 향상시켰습니다. 최종 답변 출력 전에 모델이 사고 과정(chain-of-thought)을 먼저 출력하여 최종 답변의 정확도를 높입니다."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B는 Llama3.3 70B를 기반으로 한 대형 언어 모델로, DeepSeek R1의 출력을 활용하여 대형 최첨단 모델과 동등한 경쟁 성능을 달성했습니다."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B는 Llama-3.1-8B-Instruct를 기반으로 한 증류 대형 언어 모델로, DeepSeek R1의 출력을 사용하여 훈련되었습니다."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B는 Qwen 2.5 14B를 기반으로 한 증류 대형 언어 모델로, DeepSeek R1의 출력을 사용하여 훈련되었습니다. 이 모델은 여러 벤치마크 테스트에서 OpenAI의 o1-mini를 초월하며, 밀집 모델(dense models)에서 최신 기술 선도 성과(state-of-the-art)를 달성했습니다. 다음은 몇 가지 벤치마크 테스트 결과입니다:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\n이 모델은 DeepSeek R1의 출력을 미세 조정하여 더 큰 규모의 최첨단 모델과 동등한 경쟁 성능을 보여주었습니다."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B는 Qwen 2.5 32B를 기반으로 한 증류 대형 언어 모델로, DeepSeek R1의 출력을 사용하여 훈련되었습니다. 이 모델은 여러 벤치마크 테스트에서 OpenAI의 o1-mini를 초월하며, 밀집 모델(dense models)에서 최신 기술 선도 성과(state-of-the-art)를 달성했습니다. 다음은 몇 가지 벤치마크 테스트 결과입니다:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\n이 모델은 DeepSeek R1의 출력을 미세 조정하여 더 큰 규모의 최첨단 모델과 동등한 경쟁 성능을 보여주었습니다."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1은 DeepSeek 팀이 발표한 최신 오픈 소스 모델로, 특히 수학, 프로그래밍 및 추론 작업에서 OpenAI의 o1 모델과 동등한 수준의 강력한 추론 성능을 갖추고 있습니다."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1은 극히 적은 주석 데이터로 모델의 추론 능력을 크게 향상시킵니다. 최종 답변을 출력하기 전에 모델은 먼저 사고의 연쇄 내용을 출력하여 최종 답변의 정확성을 높입니다."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3는 추론 속도에서 이전 모델에 비해 중대한 돌파구를 이루었습니다. 오픈 소스 모델 중 1위를 차지하며, 세계에서 가장 진보된 폐쇄형 모델과 견줄 수 있습니다. DeepSeek-V3는 다중 헤드 잠재 주의(Multi-Head Latent Attention, MLA)와 DeepSeekMoE 아키텍처를 채택하였으며, 이 아키텍처는 DeepSeek-V2에서 철저히 검증되었습니다. 또한, DeepSeek-V3는 부하 균형을 위한 보조 무손실 전략을 개척하고, 더 강력한 성능을 위해 다중 레이블 예측 훈련 목표를 설정했습니다."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3는 추론 속도에서 이전 모델에 비해 중대한 돌파구를 이루었습니다. 오픈 소스 모델 중 1위를 차지하며, 세계에서 가장 진보된 폐쇄형 모델과 견줄 수 있습니다. DeepSeek-V3는 다중 헤드 잠재 주의(Multi-Head Latent Attention, MLA)와 DeepSeekMoE 아키텍처를 채택하였으며, 이 아키텍처는 DeepSeek-V2에서 철저히 검증되었습니다. 또한, DeepSeek-V3는 부하 균형을 위한 보조 무손실 전략을 개척하고, 더 강력한 성능을 위해 다중 레이블 예측 훈련 목표를 설정했습니다."}, "deepseek_r1": {"description": "DeepSeek-R1은 강화 학습(RL) 기반의 추론 모델로, 모델 내의 반복성과 가독성 문제를 해결합니다. RL 이전에 DeepSeek-R1은 냉각 시작 데이터를 도입하여 추론 성능을 더욱 최적화했습니다. 수학, 코드 및 추론 작업에서 OpenAI-o1과 동등한 성능을 보이며, 정교하게 설계된 훈련 방법을 통해 전체적인 효과를 향상시켰습니다."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B는 Llama-3.3-70B-Instruct를 기반으로 증류 훈련을 통해 얻어진 모델입니다. 이 모델은 DeepSeek-R1 시리즈의 일부로, DeepSeek-R1이 생성한 샘플을 사용하여 미세 조정되어 수학, 프로그래밍 및 추론 등 여러 분야에서 뛰어난 성능을 보여줍니다."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-14B는 Qwen2.5-14B를 기반으로 지식 증류를 통해 얻어진 모델입니다. 이 모델은 DeepSeek-R1이 생성한 80만 개의 선별된 샘플을 사용하여 미세 조정되어 뛰어난 추론 능력을 보여줍니다."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-<PERSON>still-Qwen-32B는 Qwen2.5-32B를 기반으로 지식 증류를 통해 얻어진 모델입니다. 이 모델은 DeepSeek-R1이 생성한 80만 개의 선별된 샘플을 사용하여 미세 조정되어, 수학, 프로그래밍 및 추론 등 여러 분야에서 뛰어난 성능을 발휘합니다."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite는 전혀 새로운 세대의 경량 모델로, 극한의 응답 속도를 자랑하며, 효과와 지연 모두 세계 최고 수준에 도달했습니다."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k는 Doubao-1.5-Pro의 전면 업그레이드 버전으로, 전체적인 효과가 10% 향상되었습니다. 256k의 컨텍스트 윈도우를 지원하며, 출력 길이는 최대 12k 토큰을 지원합니다. 더 높은 성능, 더 큰 윈도우, 뛰어난 가성비로 더 넓은 응용 분야에 적합합니다."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro는 전혀 새로운 세대의 주력 모델로, 성능이 전면적으로 업그레이드되어 지식, 코드, 추론 등 여러 분야에서 뛰어난 성능을 발휘합니다."}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5는 수학, 프로그래밍, 과학적 추론 등 전문 분야와 창의적 글쓰기 등 일반 작업에서 뛰어난 성능을 발휘하는 새로운 심층 사고 모델입니다. AIME 2024, Codeforces, GPQA 등 여러 권위 있는 기준에서 업계 최상위 수준에 도달하거나 근접했습니다. 128k의 컨텍스트 윈도우와 16k 출력을 지원합니다."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5는 완전히 새로워진 심층 사고 모델(m 버전은 본래 다중 모달 심층 추론 기능 탑재)로, 수학, 프로그래밍, 과학적 추론 등 전문 분야와 창의적 글쓰기 등 일반 작업에서 뛰어난 성능을 보이며, AIME 2024, Codeforces, GPQA 등 여러 권위 있는 벤치마크에서 업계 최상위권 수준에 도달하거나 근접했습니다. 128k 컨텍스트 윈도우와 16k 출력 지원."}, "doubao-1.5-thinking-vision-pro": {"description": "완전히 새로워진 시각 심층 사고 모델로, 강력한 범용 다중 모달 이해 및 추론 능력을 갖추었으며, 59개의 공개 평가 벤치마크 중 37개에서 최첨단(SOTA) 성과를 달성했습니다."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS는 그래픽 사용자 인터페이스(GUI) 상호작용에 본래 최적화된 에이전트 모델입니다. 인지, 추론, 행동 등 인간과 유사한 능력을 통해 GUI와 원활하게 상호작용합니다."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite는 새롭게 업그레이드된 다중 모드 대모델로, 임의의 해상도와 극단적인 가로 세로 비율의 이미지 인식을 지원하며, 시각적 추론, 문서 인식, 세부 정보 이해 및 지시 준수 능력을 강화합니다. 128k 문맥 창을 지원하며, 최대 16k 토큰의 출력 길이를 지원합니다."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro는 완전히 업그레이드된 다중 모달 대형 모델로, 임의 해상도 및 극단적인 종횡비 이미지 인식을 지원하며, 시각 추론, 문서 인식, 세부 정보 이해 및 명령 준수 능력이 강화되었습니다."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro는 완전히 업그레이드된 다중 모달 대형 모델로, 임의 해상도 및 극단적인 종횡비 이미지 인식을 지원하며, 시각 추론, 문서 인식, 세부 정보 이해 및 명령 준수 능력이 강화되었습니다."}, "doubao-lite-128k": {"description": "탁월한 응답 속도와 뛰어난 가성비를 자랑하며, 고객의 다양한 시나리오에 더 유연한 선택을 제공합니다. 128k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "doubao-lite-32k": {"description": "탁월한 응답 속도와 뛰어난 가성비를 자랑하며, 고객의 다양한 시나리오에 더 유연한 선택을 제공합니다. 32k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "doubao-lite-4k": {"description": "탁월한 응답 속도와 뛰어난 가성비를 자랑하며, 고객의 다양한 시나리오에 더 유연한 선택을 제공합니다. 4k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "doubao-pro-256k": {"description": "최고 성능의 주력 모델로 복잡한 작업 처리에 적합하며, 참고 질문 답변, 요약, 창작, 텍스트 분류, 역할극 등 다양한 시나리오에서 우수한 성과를 보입니다. 256k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "doubao-pro-32k": {"description": "최고 성능의 주력 모델로 복잡한 작업 처리에 적합하며, 참고 질문 답변, 요약, 창작, 텍스트 분류, 역할극 등 다양한 시나리오에서 우수한 성과를 보입니다. 32k 컨텍스트 윈도우 추론 및 미세 조정을 지원합니다."}, "doubao-seed-1.6": {"description": "Do<PERSON>o-Seed-1.6은 완전히 새로워진 다중 모달 심층 사고 모델로, auto/thinking/non-thinking 세 가지 사고 모드를 모두 지원합니다. non-thinking 모드에서는 Doubao-1.5-pro/250115에 비해 모델 성능이 크게 향상되었습니다. 256k 컨텍스트 창을 지원하며, 출력 길이는 최대 16k 토큰까지 가능합니다."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash는 추론 속도가 극대화된 다중 모달 심층 사고 모델로, TPOT가 단 10ms에 불과합니다. 텍스트와 시각 이해를 모두 지원하며, 텍스트 이해 능력은 이전 세대 lite를 능가하고, 시각 이해는 경쟁사 pro 시리즈 모델과 견줄 만합니다. 256k 컨텍스트 창을 지원하며, 출력 길이는 최대 16k 토큰까지 가능합니다."}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking 모델은 사고 능력이 크게 강화되어 Doubao-1.5-thinking-pro에 비해 코딩, 수학, 논리 추론 등 기본 능력이 더욱 향상되었으며, 시각 이해도 지원합니다. 256k 컨텍스트 창을 지원하며, 출력 길이는 최대 16k 토큰까지 가능합니다."}, "doubao-seedream-3-0-t2i-250415": {"description": "Doubao 이미지 생성 모델은 바이트댄스 Seed 팀이 개발했으며, 텍스트와 이미지 입력을 지원하여 높은 제어력과 고품질 이미지 생성 경험을 제공합니다. 텍스트 프롬프트를 기반으로 이미지를 생성합니다."}, "doubao-vision-lite-32k": {"description": "Doubao-vision 모델은 Doubao에서 출시한 다중 모달 대형 모델로, 강력한 이미지 이해 및 추론 능력과 정밀한 명령 이해 능력을 갖추고 있습니다. 이미지 텍스트 정보 추출 및 이미지 기반 추론 작업에서 뛰어난 성능을 보여, 더 복잡하고 광범위한 시각 질문 응답 작업에 적용할 수 있습니다."}, "doubao-vision-pro-32k": {"description": "Doubao-vision 모델은 Doubao에서 출시한 다중 모달 대형 모델로, 강력한 이미지 이해 및 추론 능력과 정밀한 명령 이해 능력을 갖추고 있습니다. 이미지 텍스트 정보 추출 및 이미지 기반 추론 작업에서 뛰어난 성능을 보여, 더 복잡하고 광범위한 시각 질문 응답 작업에 적용할 수 있습니다."}, "emohaa": {"description": "Emohaa는 심리 모델로, 전문 상담 능력을 갖추고 있어 사용자가 감정 문제를 이해하는 데 도움을 줍니다."}, "ernie-3.5-128k": {"description": "바이두가 자체 개발한 플래그십 대규모 언어 모델로, 방대한 중영문 자료를 포함하고 있으며, 강력한 일반 능력을 가지고 있어 대부분의 대화 질문 답변, 창작 생성, 플러그인 응용 시나리오 요구를 충족할 수 있습니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다."}, "ernie-3.5-8k": {"description": "바이두가 자체 개발한 플래그십 대규모 언어 모델로, 방대한 중영문 자료를 포함하고 있으며, 강력한 일반 능력을 가지고 있어 대부분의 대화 질문 답변, 창작 생성, 플러그인 응용 시나리오 요구를 충족할 수 있습니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다."}, "ernie-3.5-8k-preview": {"description": "바이두가 자체 개발한 플래그십 대규모 언어 모델로, 방대한 중영문 자료를 포함하고 있으며, 강력한 일반 능력을 가지고 있어 대부분의 대화 질문 답변, 창작 생성, 플러그인 응용 시나리오 요구를 충족할 수 있습니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다."}, "ernie-4.0-8k-latest": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, ERNIE 3.5에 비해 모델 능력이 전면 업그레이드되었으며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다."}, "ernie-4.0-8k-preview": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, ERNIE 3.5에 비해 모델 능력이 전면 업그레이드되었으며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다."}, "ernie-4.0-turbo-128k": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, 종합적인 성능이 뛰어나며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다. ERNIE 4.0에 비해 성능이 더 우수합니다."}, "ernie-4.0-turbo-8k-latest": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, 종합적인 성능이 뛰어나며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다. ERNIE 4.0에 비해 성능이 더 우수합니다."}, "ernie-4.0-turbo-8k-preview": {"description": "바이두가 자체 개발한 플래그십 초대규모 언어 모델로, 종합적인 성능이 뛰어나며, 다양한 분야의 복잡한 작업 시나리오에 널리 적용됩니다. 바이두 검색 플러그인과 자동으로 연결되어 질문 답변 정보의 시의성을 보장합니다. ERNIE 4.0에 비해 성능이 더 우수합니다."}, "ernie-4.5-8k-preview": {"description": "문심 대모델 4.5는 바이두가 독자적으로 개발한 차세대 원주율 다중 모달 기본 대모델로, 여러 모달의 공동 모델링을 통해 협동 최적화를 실현하며, 다중 모달 이해 능력이 뛰어납니다. 언어 능력이 더욱 향상되어 이해, 생성, 논리, 기억 능력이 전반적으로 향상되었으며, 환각 제거, 논리 추론, 코드 능력이 현저히 향상되었습니다."}, "ernie-4.5-turbo-128k": {"description": "문신 4.5 Turbo는 환각 제거, 논리적 추론 및 코드 능력 등에서 뚜렷한 향상을 보였습니다. 문신 4.5와 비교할 때, 속도가 더 빠르고 가격이 더 저렴합니다. 모델의 능력이 전반적으로 향상되어 다중 회차 긴 역사 대화 처리 및 긴 문서 이해 질문 응답 작업을 더 잘 충족합니다."}, "ernie-4.5-turbo-32k": {"description": "문신 4.5 Turbo는 환각 제거, 논리적 추론 및 코드 능력 등에서 뚜렷한 향상을 보였습니다. 문신 4.5와 비교할 때, 속도가 더 빠르고 가격이 더 저렴합니다. 텍스트 창작, 지식 질문 응답 등의 능력이 크게 향상되었습니다. 출력 길이 및 전체 문장 지연은 ERNIE 4.5에 비해 증가했습니다."}, "ernie-4.5-turbo-vl-32k": {"description": "문신 일언 대모델의 새로운 버전으로, 이미지 이해, 창작, 번역, 코드 등의 능력이 크게 향상되었으며, 처음으로 32K의 맥락 길이를 지원하고 첫 번째 토큰 지연이 현저히 감소했습니다."}, "ernie-char-8k": {"description": "바이두가 자체 개발한 수직 장면 대형 언어 모델로, 게임 NPC, 고객 서비스 대화, 대화 역할극 등 응용 시나리오에 적합하며, 캐릭터 스타일이 더 뚜렷하고 일관되며, 지시 따르기 능력이 더 강하고 추론 성능이 우수합니다."}, "ernie-char-fiction-8k": {"description": "바이두가 자체 개발한 수직 장면 대형 언어 모델로, 게임 NPC, 고객 서비스 대화, 대화 역할극 등 응용 시나리오에 적합하며, 캐릭터 스타일이 더 뚜렷하고 일관되며, 지시 따르기 능력이 더 강하고 추론 성능이 우수합니다."}, "ernie-irag-edit": {"description": "바이두가 자체 개발한 ERNIE iRAG Edit 이미지 편집 모델로, 이미지 기반으로 객체 제거(erase), 재도색(repaint), 변형(variation) 생성 등의 작업을 지원합니다."}, "ernie-lite-8k": {"description": "ERNIE Lite는 바이두가 자체 개발한 경량 대형 언어 모델로, 우수한 모델 효과와 추론 성능을 겸비하여 저전력 AI 가속 카드 추론에 적합합니다."}, "ernie-lite-pro-128k": {"description": "바이두가 자체 개발한 경량 대형 언어 모델로, 우수한 모델 효과와 추론 성능을 겸비하여 ERNIE Lite보다 더 우수하며, 저전력 AI 가속 카드 추론에 적합합니다."}, "ernie-novel-8k": {"description": "바이두가 자체 개발한 일반 대형 언어 모델로, 소설 연속 작성 능력에서 뚜렷한 장점을 가지고 있으며, 단편극, 영화 등 시나리오에서도 사용할 수 있습니다."}, "ernie-speed-128k": {"description": "바이두가 2024년에 최신 출시한 고성능 대형 언어 모델로, 일반 능력이 우수하여 특정 시나리오 문제를 더 잘 처리하기 위해 기초 모델로 미세 조정하는 데 적합하며, 뛰어난 추론 성능을 가지고 있습니다."}, "ernie-speed-pro-128k": {"description": "바이두가 2024년에 최신 출시한 고성능 대형 언어 모델로, 일반 능력이 우수하여 ERNIE Speed보다 더 우수하며, 특정 시나리오 문제를 더 잘 처리하기 위해 기초 모델로 미세 조정하는 데 적합하며, 뛰어난 추론 성능을 가지고 있습니다."}, "ernie-tiny-8k": {"description": "ERNIE Tiny는 바이두가 자체 개발한 초고성능 대형 언어 모델로, 문신 시리즈 모델 중 배포 및 미세 조정 비용이 가장 낮습니다."}, "ernie-x1-32k": {"description": "더 강력한 이해, 계획, 반성, 진화 능력을 갖추고 있습니다. 보다 포괄적인 심층 사고 모델로서, 문신 X1은 정확성, 창의성 및 문체를 겸비하여 중국어 지식 질문 응답, 문학 창작, 문서 작성, 일상 대화, 논리적 추론, 복잡한 계산 및 도구 호출 등에서 특히 뛰어난 성능을 발휘합니다."}, "ernie-x1-32k-preview": {"description": "문심 대모델 X1은 더 강력한 이해, 계획, 반성 및 진화 능력을 갖추고 있습니다. 보다 포괄적인 심층 사고 모델로서, 문심 X1은 정확성, 창의성 및 문체를 겸비하여 중국어 지식 질문 응답, 문학 창작, 문서 작성, 일상 대화, 논리 추론, 복잡한 계산 및 도구 호출 등에서 특히 뛰어난 성과를 보입니다."}, "ernie-x1-turbo-32k": {"description": "ERNIE-X1-32K에 비해 모델의 효과와 성능이 더 우수합니다."}, "flux-1-schnell": {"description": "Black Forest Labs가 개발한 120억 파라미터 텍스트-이미지 생성 모델로, 잠재적 적대적 확산 증류 기술을 사용하여 1~4단계 내에 고품질 이미지를 생성할 수 있습니다. 이 모델은 폐쇄형 대체품과 견줄 만한 성능을 보이며, Apache-2.0 라이선스 하에 개인, 연구 및 상업적 용도로 공개되어 있습니다."}, "flux-dev": {"description": "FLUX.1 [dev]는 비상업적 용도를 위한 오픈 소스 가중치 및 정제 모델입니다. FLUX.1 [dev]는 FLUX 전문판과 유사한 이미지 품질과 명령 준수 능력을 유지하면서도 더 높은 실행 효율성을 갖추고 있습니다. 동일 크기 표준 모델 대비 자원 활용이 더 효율적입니다."}, "flux-kontext/dev": {"description": "프론티어 이미지 편집 모델."}, "flux-merged": {"description": "FLUX.1-merged 모델은 개발 단계에서 탐색된 \"DEV\"의 심층 특성과 \"Schnell\"이 대표하는 고속 실행 장점을 결합했습니다. 이를 통해 FLUX.1-merged는 모델 성능 한계를 높이고 적용 범위를 확장했습니다."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro]는 텍스트와 참조 이미지를 입력으로 처리하여 목표 지향적인 부분 편집과 복잡한 전체 장면 변환을 원활하게 수행할 수 있습니다."}, "flux-schnell": {"description": "FLUX.1 [schnell]은 현재 공개된 가장 진보된 소단계 모델로, 동종 경쟁 모델을 능가할 뿐만 아니라 Midjourney v6.0, DALL·E 3 (HD) 같은 강력한 비증류 모델보다도 우수합니다. 이 모델은 사전 학습 단계의 모든 출력 다양성을 유지하도록 특별히 미세 조정되었으며, 시각 품질, 명령 준수, 크기/비율 변화, 글꼴 처리 및 출력 다양성 등에서 현존 최고 모델 대비 현저한 향상을 이루어 사용자에게 더욱 풍부하고 다양한 창의적 이미지 생성 경험을 제공합니다."}, "flux.1-schnell": {"description": "120억 파라미터의 수정 흐름 변환기로, 텍스트 설명에 따라 이미지를 생성할 수 있습니다."}, "flux/schnell": {"description": "FLUX.1 [schnell]은 120억 개의 매개변수를 가진 스트림 변환기 모델로, 1~4단계 내에 텍스트로부터 고품질 이미지를 생성하며 개인 및 상업적 용도에 적합합니다."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning)은 안정적이고 조정 가능한 성능을 제공하며, 복잡한 작업 솔루션의 이상적인 선택입니다."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning)은 뛰어난 다중 모달 지원을 제공하며, 복잡한 작업의 효과적인 해결에 중점을 둡니다."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro는 Google의 고성능 AI 모델로, 광범위한 작업 확장을 위해 설계되었습니다."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001은 효율적인 다중 모달 모델로, 광범위한 응용 프로그램 확장을 지원합니다."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002는 효율적인 다중 모달 모델로, 광범위한 응용 프로그램의 확장을 지원합니다."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B는 효율적인 다중 모달 모델로, 광범위한 응용 프로그램의 확장을 지원합니다."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924는 최신 실험 모델로, 텍스트 및 다중 모달 사용 사례에서 상당한 성능 향상을 보여줍니다."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B는 광범위한 애플리케이션을 지원하는 효율적인 멀티모달 모델입니다."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827은 다양한 복잡한 작업에 적합한 최적화된 다중 모달 처리 능력을 제공합니다."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash는 Google의 최신 다중 모달 AI 모델로, 빠른 처리 능력을 갖추고 있으며 텍스트, 이미지 및 비디오 입력을 지원하여 다양한 작업에 효율적으로 확장할 수 있습니다."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001은 확장 가능한 다중 모달 AI 솔루션으로, 광범위한 복잡한 작업을 지원합니다."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002는 최신 생산 준비 모델로, 특히 수학, 긴 문맥 및 시각적 작업에서 더 높은 품질의 출력을 제공합니다."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801은 뛰어난 다중 모달 처리 능력을 제공하여 애플리케이션 개발에 더 큰 유연성을 제공합니다."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827은 최신 최적화 기술을 결합하여 보다 효율적인 다중 모달 데이터 처리 능력을 제공합니다."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro는 최대 200만 개의 토큰을 지원하며, 중형 다중 모달 모델의 이상적인 선택으로 복잡한 작업에 대한 다각적인 지원을 제공합니다."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash는 뛰어난 속도, 원주율 도구 사용, 다중 모달 생성 및 1M 토큰 문맥 창을 포함한 차세대 기능과 개선 사항을 제공합니다."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash는 뛰어난 속도, 원주율 도구 사용, 다중 모달 생성 및 1M 토큰 문맥 창을 포함한 차세대 기능과 개선 사항을 제공합니다."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash 모델 변형으로, 비용 효율성과 저지연 등의 목표를 위해 최적화되었습니다."}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash 실험 모델, 이미지 생성을 지원합니다."}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 플래시 모델 변형으로, 비용 효율성과 낮은 지연 시간 등의 목표를 위해 최적화되었습니다."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 플래시 모델 변형으로, 비용 효율성과 낮은 지연 시간 등의 목표를 위해 최적화되었습니다."}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash 미리보기 모델로, 이미지 생성을 지원합니다."}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash는 구글에서 가장 가성비가 뛰어난 모델로, 포괄적인 기능을 제공합니다."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite는 Google의 가장 작고 가성비가 뛰어난 모델로, 대규모 사용을 위해 설계되었습니다."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview는 구글의 가장 작고 가성비가 뛰어난 모델로, 대규모 사용을 위해 설계되었습니다."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview는 Google의 가장 가성비 높은 모델로, 포괄적인 기능을 제공합니다."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview는 Google의 최고의 가성비 모델로, 포괄적인 기능을 제공합니다."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro는 구글의 최첨단 사고 모델로, 코드, 수학 및 STEM 분야의 복잡한 문제를 추론할 수 있으며, 긴 문맥을 활용해 대규모 데이터셋, 코드베이스 및 문서를 분석합니다."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview는 Google의 최첨단 사고 모델로, 코드, 수학 및 STEM 분야의 복잡한 문제를 추론하고 긴 맥락을 사용하여 대규모 데이터 세트, 코드베이스 및 문서를 분석할 수 있습니다."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview는 Google의 최첨단 사고 모델로, 코드, 수학 및 STEM 분야의 복잡한 문제를 추론하고 긴 맥락을 사용하여 대규모 데이터 세트, 코드베이스 및 문서를 분석할 수 있습니다."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview는 구글의 최첨단 사고 모델로, 코드, 수학 및 STEM 분야의 복잡한 문제를 추론할 수 있으며, 긴 문맥을 활용해 대규모 데이터셋, 코드베이스 및 문서를 분석합니다."}, "gemma-7b-it": {"description": "Gemma 7B는 중소 규모 작업 처리에 적합하며, 비용 효과성을 갖추고 있습니다."}, "gemma2": {"description": "Gemma 2는 Google에서 출시한 효율적인 모델로, 소형 응용 프로그램부터 복잡한 데이터 처리까지 다양한 응용 시나리오를 포함합니다."}, "gemma2-9b-it": {"description": "Gemma 2 9B는 특정 작업 및 도구 통합을 위해 최적화된 모델입니다."}, "gemma2:27b": {"description": "Gemma 2는 Google에서 출시한 효율적인 모델로, 소형 응용 프로그램부터 복잡한 데이터 처리까지 다양한 응용 시나리오를 포함합니다."}, "gemma2:2b": {"description": "Gemma 2는 Google에서 출시한 효율적인 모델로, 소형 응용 프로그램부터 복잡한 데이터 처리까지 다양한 응용 시나리오를 포함합니다."}, "generalv3": {"description": "Spark Pro는 전문 분야에 최적화된 고성능 대형 언어 모델로, 수학, 프로그래밍, 의료, 교육 등 여러 분야에 중점을 두고 있으며, 네트워크 검색 및 내장된 날씨, 날짜 등의 플러그인을 지원합니다. 최적화된 모델은 복잡한 지식 질문 응답, 언어 이해 및 고급 텍스트 창작에서 뛰어난 성능과 효율성을 보여주며, 전문 응용 시나리오에 적합한 이상적인 선택입니다."}, "generalv3.5": {"description": "Spark3.5 Max는 기능이 가장 포괄적인 버전으로, 네트워크 검색 및 다양한 내장 플러그인을 지원합니다. 전면적으로 최적화된 핵심 능력과 시스템 역할 설정 및 함수 호출 기능 덕분에 다양한 복잡한 응용 시나리오에서 매우 우수한 성능을 발휘합니다."}, "glm-4": {"description": "GLM-4는 2024년 1월에 출시된 구형 플래그십 버전으로, 현재 더 강력한 GLM-4-0520으로 대체되었습니다."}, "glm-4-0520": {"description": "GLM-4-0520은 최신 모델 버전으로, 매우 복잡하고 다양한 작업을 위해 설계되어 뛰어난 성능을 발휘합니다."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat은 의미, 수학, 추론, 코드 및 지식 등 여러 분야에서 높은 성능을 보입니다. 웹 브라우징, 코드 실행, 사용자 정의 도구 호출 및 긴 텍스트 추론 기능도 갖추고 있습니다. 일본어, 한국어, 독일어를 포함한 26개 언어를 지원합니다."}, "glm-4-air": {"description": "GLM-4-Air는 가성비가 높은 버전으로, GLM-4에 가까운 성능을 제공하며 빠른 속도와 저렴한 가격을 자랑합니다."}, "glm-4-air-250414": {"description": "GLM-4-Air는 가성비가 높은 버전으로, GLM-4에 가까운 성능을 제공하며, 빠른 속도와 저렴한 가격을 자랑합니다."}, "glm-4-airx": {"description": "GLM-4-AirX는 GLM-4-Air의 효율적인 버전으로, 추론 속도가 최대 2.6배에 달합니다."}, "glm-4-alltools": {"description": "GLM-4-AllTools는 복잡한 지시 계획 및 도구 호출을 지원하도록 최적화된 다기능 지능형 모델로, 웹 브라우징, 코드 해석 및 텍스트 생성을 포함한 다중 작업 실행에 적합합니다."}, "glm-4-flash": {"description": "GLM-4-Flash는 간단한 작업을 처리하는 데 이상적인 선택으로, 가장 빠른 속도와 가장 저렴한 가격을 자랑합니다."}, "glm-4-flash-250414": {"description": "GLM-4-Flash는 간단한 작업을 처리하는 데 이상적인 선택으로, 가장 빠르고 무료입니다."}, "glm-4-flashx": {"description": "GLM-4-FlashX는 Flash의 향상된 버전으로, 초고속 추론 속도를 자랑합니다."}, "glm-4-long": {"description": "GLM-4-Long는 초장 텍스트 입력을 지원하여 기억형 작업 및 대규모 문서 처리에 적합합니다."}, "glm-4-plus": {"description": "GLM-4-Plus는 고지능 플래그십 모델로, 긴 텍스트 및 복잡한 작업 처리 능력이 뛰어나며 성능이 전반적으로 향상되었습니다."}, "glm-4.1v-thinking-flash": {"description": "GLM-4.1V-Thinking 시리즈 모델은 현재 알려진 10B급 VLM 모델 중 가장 성능이 뛰어난 비주얼 모델로, 동급 SOTA의 다양한 비주얼 언어 작업을 통합합니다. 여기에는 비디오 이해, 이미지 질문응답, 학과 문제 해결, OCR 문자 인식, 문서 및 차트 해석, GUI 에이전트, 프론트엔드 웹 코딩, 그라운딩 등이 포함되며, 여러 작업 능력은 8배 이상의 파라미터를 가진 Qwen2.5-VL-72B를 능가합니다. 선도적인 강화 학습 기술을 통해 사고 사슬 추론 방식을 습득하여 답변의 정확성과 풍부함을 향상시키며, 최종 결과와 해석 가능성 측면에서 전통적인 비사고 모델을 현저히 능가합니다."}, "glm-4.1v-thinking-flashx": {"description": "GLM-4.1V-Thinking 시리즈 모델은 현재 알려진 10B급 VLM 모델 중 가장 성능이 뛰어난 비주얼 모델로, 동급 SOTA의 다양한 비주얼 언어 작업을 통합합니다. 여기에는 비디오 이해, 이미지 질문응답, 학과 문제 해결, OCR 문자 인식, 문서 및 차트 해석, GUI 에이전트, 프론트엔드 웹 코딩, 그라운딩 등이 포함되며, 여러 작업 능력은 8배 이상의 파라미터를 가진 Qwen2.5-VL-72B를 능가합니다. 선도적인 강화 학습 기술을 통해 사고 사슬 추론 방식을 습득하여 답변의 정확성과 풍부함을 향상시키며, 최종 결과와 해석 가능성 측면에서 전통적인 비사고 모델을 현저히 능가합니다."}, "glm-4.5": {"description": "지능형 최신 플래그십 모델로, 사고 모드 전환을 지원하며 종합 능력이 오픈 소스 모델 중 최고 수준(SOTA)에 도달했습니다. 문맥 길이는 최대 128K까지 지원합니다."}, "glm-4.5-air": {"description": "GLM-4.5의 경량 버전으로, 성능과 비용 효율성을 균형 있게 갖추었으며 혼합 사고 모델을 유연하게 전환할 수 있습니다."}, "glm-4.5-airx": {"description": "GLM-4.5-Air의 초고속 버전으로, 반응 속도가 더 빠르며 대규모 고속 요구에 최적화되었습니다."}, "glm-4.5-flash": {"description": "GLM-4.5의 무료 버전으로, 추론, 코딩, 에이전트 등 작업에서 뛰어난 성능을 보입니다."}, "glm-4.5-x": {"description": "GLM-4.5의 초고속 버전으로, 강력한 성능과 함께 최대 100 tokens/초의 생성 속도를 자랑합니다."}, "glm-4v": {"description": "GLM-4V는 강력한 이미지 이해 및 추론 능력을 제공하며, 다양한 시각적 작업을 지원합니다."}, "glm-4v-flash": {"description": "GLM-4V-Flash는 효율적인 단일 이미지 이해에 중점을 두며, 실시간 이미지 분석이나 대량 이미지 처리와 같은 빠른 이미지 분석 환경에 적합합니다."}, "glm-4v-plus": {"description": "GLM-4V-Plus는 비디오 콘텐츠 및 다수의 이미지에 대한 이해 능력을 갖추고 있어 다중 모드 작업에 적합합니다."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus는 비디오 콘텐츠 및 여러 이미지에 대한 이해 능력을 갖추고 있어 다중 모드 작업에 적합합니다."}, "glm-z1-air": {"description": "추론 모델: 강력한 추론 능력을 갖추고 있으며, 깊은 추론이 필요한 작업에 적합합니다."}, "glm-z1-airx": {"description": "초고속 추론: 매우 빠른 추론 속도와 강력한 추론 효과를 제공합니다."}, "glm-z1-flash": {"description": "GLM-Z1 시리즈는 강력한 복잡 추론 능력을 갖추었으며, 논리 추론, 수학, 코딩 등 분야에서 우수한 성과를 보입니다."}, "glm-z1-flashx": {"description": "고속 저가: Flash 강화 버전으로, 매우 빠른 추론 속도와 더 빠른 동시성 보장을 제공합니다."}, "glm-zero-preview": {"description": "GLM-Zero-Preview는 강력한 복잡한 추론 능력을 갖추고 있으며, 논리 추론, 수학, 프로그래밍 등 분야에서 우수한 성능을 발휘합니다."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash는 뛰어난 속도, 원주율 도구 사용, 다중 모달 생성 및 1M 토큰 문맥 창을 포함한 차세대 기능과 개선 사항을 제공합니다."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental은 Google의 최신 실험적 다중 모달 AI 모델로, 역사적 버전과 비교하여 품질이 향상되었으며, 특히 세계 지식, 코드 및 긴 맥락에 대해 개선되었습니다."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash는 Google의 최첨단 주력 모델로, 고급 추론, 코딩, 수학 및 과학 작업을 위해 설계되었습니다. 내장된 '사고' 능력을 포함하여 더 높은 정확도와 세밀한 문맥 처리가 가능한 응답을 제공합니다.\n\n참고: 이 모델에는 사고형과 비사고형 두 가지 변형이 있습니다. 출력 가격은 사고 능력 활성화 여부에 따라 크게 다릅니다. 표준 변형(‘:thinking’ 접미사 없음)을 선택하면 모델이 명확히 사고 토큰 생성을 피합니다.\n\n사고 능력을 활용하고 사고 토큰을 받으려면 ‘:thinking’ 변형을 선택해야 하며, 이 경우 더 높은 사고 출력 가격이 적용됩니다.\n\n또한, Gemini 2.5 Flash는 문서(https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)에 설명된 대로 '최대 추론 토큰 수' 매개변수를 통해 구성할 수 있습니다."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash는 Google의 최첨단 주력 모델로, 고급 추론, 코딩, 수학 및 과학 작업을 위해 설계되었습니다. 내장된 '사고' 능력을 포함하고 있어 더 높은 정확성과 세밀한 맥락 처리를 통해 응답을 제공합니다.\n\n주의: 이 모델에는 두 가지 변형이 있습니다: 사고 및 비사고. 출력 가격은 사고 능력이 활성화되었는지 여부에 따라 크게 다릅니다. 표준 변형(‘:thinking’ 접미사가 없는)을 선택하면 모델이 사고 토큰 생성을 명확히 피합니다.\n\n사고 능력을 활용하고 사고 토큰을 수신하려면 ‘:thinking’ 변형을 선택해야 하며, 이는 더 높은 사고 출력 가격을 발생시킵니다.\n\n또한, Gemini 2.5 Flash는 문서에 설명된 대로 '추론 최대 토큰 수' 매개변수를 통해 구성할 수 있습니다 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash는 Google의 최첨단 주력 모델로, 고급 추론, 코딩, 수학 및 과학 작업을 위해 설계되었습니다. 내장된 '사고' 능력을 포함하고 있어 더 높은 정확성과 세밀한 맥락 처리를 통해 응답을 제공합니다.\n\n주의: 이 모델에는 두 가지 변형이 있습니다: 사고 및 비사고. 출력 가격은 사고 능력이 활성화되었는지 여부에 따라 크게 다릅니다. 표준 변형(‘:thinking’ 접미사가 없는)을 선택하면 모델이 사고 토큰 생성을 명확히 피합니다.\n\n사고 능력을 활용하고 사고 토큰을 수신하려면 ‘:thinking’ 변형을 선택해야 하며, 이는 더 높은 사고 출력 가격을 발생시킵니다.\n\n또한, Gemini 2.5 Flash는 문서에 설명된 대로 '추론 최대 토큰 수' 매개변수를 통해 구성할 수 있습니다 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro는 Google의 최첨단 사고 모델로, 코드, 수학 및 STEM 분야의 복잡한 문제를 추론할 수 있으며, 긴 문맥을 사용해 대규모 데이터 세트, 코드베이스 및 문서를 분석합니다."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview는 Google의 최첨단 사고 모델로, 코드, 수학 및 STEM 분야의 복잡한 문제를 추론할 수 있으며, 긴 문맥을 사용하여 대규모 데이터 세트, 코드베이스 및 문서를 분석할 수 있습니다."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash는 최적화된 다중 모달 처리 능력을 제공하며, 다양한 복잡한 작업 시나리오에 적합합니다."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro는 최신 최적화 기술을 결합하여 더 효율적인 다중 모달 데이터 처리 능력을 제공합니다."}, "google/gemma-2-27b": {"description": "Gemma 2는 Google에서 출시한 효율적인 모델로, 소형 애플리케이션부터 복잡한 데이터 처리까지 다양한 응용 시나리오를 포함합니다."}, "google/gemma-2-27b-it": {"description": "Gemma 2는 경량화와 효율적인 설계를 이어갑니다."}, "google/gemma-2-2b-it": {"description": "Google의 경량 지시 조정 모델"}, "google/gemma-2-9b": {"description": "Gemma 2는 Google에서 출시한 효율적인 모델로, 소형 애플리케이션부터 복잡한 데이터 처리까지 다양한 응용 시나리오를 포함합니다."}, "google/gemma-2-9b-it": {"description": "Gemma 2는 Google의 경량화된 오픈 소스 텍스트 모델 시리즈입니다."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2는 Google의 경량화된 오픈 소스 텍스트 모델 시리즈입니다."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B)는 기본적인 지시 처리 능력을 제공하며, 경량 애플리케이션에 적합합니다."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B는 구글의 오픈소스 언어 모델로, 효율성과 성능 면에서 새로운 기준을 세웠습니다."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B는 구글의 오픈 소스 언어 모델로, 효율성과 성능 면에서 새로운 기준을 세웠습니다."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo는 다양한 텍스트 생성 및 이해 작업에 적합하며, 현재 gpt-3.5-turbo-0125를 가리킵니다."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo는 다양한 텍스트 생성 및 이해 작업에 적합하며, 현재 gpt-3.5-turbo-0125를 가리킵니다."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo는 다양한 텍스트 생성 및 이해 작업에 적합하며, 현재 gpt-3.5-turbo-0125를 가리킵니다."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo는 다양한 텍스트 생성 및 이해 작업에 적합하며, 현재 gpt-3.5-turbo-0125를 가리킵니다."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo는 OpenAI에서 제공하는 효율적인 모델로, 채팅 및 텍스트 생성 작업에 적합하며, 병렬 함수 호출을 지원합니다."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k는 복잡한 작업에 적합한 고용량 텍스트 생성 모델입니다."}, "gpt-4": {"description": "GPT-4는 더 큰 컨텍스트 창을 제공하여 더 긴 텍스트 입력을 처리할 수 있으며, 광범위한 정보 통합 및 데이터 분석이 필요한 상황에 적합합니다."}, "gpt-4-0125-preview": {"description": "최신 GPT-4 Turbo 모델은 시각적 기능을 갖추고 있습니다. 이제 시각적 요청은 JSON 형식과 함수 호출을 사용하여 처리할 수 있습니다. GPT-4 Turbo는 다중 모드 작업을 위한 비용 효율적인 지원을 제공하는 향상된 버전입니다. 정확성과 효율성 간의 균형을 찾아 실시간 상호작용이 필요한 응용 프로그램에 적합합니다."}, "gpt-4-0613": {"description": "GPT-4는 더 큰 컨텍스트 창을 제공하여 더 긴 텍스트 입력을 처리할 수 있으며, 광범위한 정보 통합 및 데이터 분석이 필요한 상황에 적합합니다."}, "gpt-4-1106-preview": {"description": "최신 GPT-4 Turbo 모델은 시각적 기능을 갖추고 있습니다. 이제 시각적 요청은 JSON 형식과 함수 호출을 사용하여 처리할 수 있습니다. GPT-4 Turbo는 다중 모드 작업을 위한 비용 효율적인 지원을 제공하는 향상된 버전입니다. 정확성과 효율성 간의 균형을 찾아 실시간 상호작용이 필요한 응용 프로그램에 적합합니다."}, "gpt-4-32k": {"description": "GPT-4는 더 큰 컨텍스트 창을 제공하여 더 긴 텍스트 입력을 처리할 수 있으며, 광범위한 정보 통합 및 데이터 분석이 필요한 상황에 적합합니다."}, "gpt-4-32k-0613": {"description": "GPT-4는 더 큰 컨텍스트 창을 제공하여 더 긴 텍스트 입력을 처리할 수 있으며, 광범위한 정보 통합 및 데이터 분석이 필요한 상황에 적합합니다."}, "gpt-4-turbo": {"description": "최신 GPT-4 Turbo 모델은 시각적 기능을 갖추고 있습니다. 이제 시각적 요청은 JSON 형식과 함수 호출을 사용하여 처리할 수 있습니다. GPT-4 Turbo는 다중 모드 작업을 위한 비용 효율적인 지원을 제공하는 향상된 버전입니다. 정확성과 효율성 간의 균형을 찾아 실시간 상호작용이 필요한 응용 프로그램에 적합합니다."}, "gpt-4-turbo-2024-04-09": {"description": "최신 GPT-4 Turbo 모델은 시각적 기능을 갖추고 있습니다. 이제 시각적 요청은 JSON 형식과 함수 호출을 사용하여 처리할 수 있습니다. GPT-4 Turbo는 다중 모드 작업을 위한 비용 효율적인 지원을 제공하는 향상된 버전입니다. 정확성과 효율성 간의 균형을 찾아 실시간 상호작용이 필요한 응용 프로그램에 적합합니다."}, "gpt-4-turbo-preview": {"description": "최신 GPT-4 Turbo 모델은 시각적 기능을 갖추고 있습니다. 이제 시각적 요청은 JSON 형식과 함수 호출을 사용하여 처리할 수 있습니다. GPT-4 Turbo는 다중 모드 작업을 위한 비용 효율적인 지원을 제공하는 향상된 버전입니다. 정확성과 효율성 간의 균형을 찾아 실시간 상호작용이 필요한 응용 프로그램에 적합합니다."}, "gpt-4-vision-preview": {"description": "최신 GPT-4 Turbo 모델은 시각적 기능을 갖추고 있습니다. 이제 시각적 요청은 JSON 형식과 함수 호출을 사용하여 처리할 수 있습니다. GPT-4 Turbo는 다중 모드 작업을 위한 비용 효율적인 지원을 제공하는 향상된 버전입니다. 정확성과 효율성 간의 균형을 찾아 실시간 상호작용이 필요한 응용 프로그램에 적합합니다."}, "gpt-4.1": {"description": "GPT-4.1은 복잡한 작업을 위한 우리의 플래그십 모델입니다. 다양한 분야의 문제를 해결하는 데 매우 적합합니다."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini는 지능, 속도 및 비용 간의 균형을 제공하여 많은 사용 사례에서 매력적인 모델이 됩니다."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini는 지능, 속도 및 비용 간의 균형을 제공하여 많은 사용 사례에서 매력적인 모델이 됩니다."}, "gpt-4.5-preview": {"description": "GPT-4.5 연구 미리보기 버전으로, 지금까지 우리가 만든 가장 크고 강력한 GPT 모델입니다. 광범위한 세계 지식을 보유하고 있으며 사용자 의도를 더 잘 이해하여 창의적인 작업과 자율 계획에서 뛰어난 성능을 발휘합니다. GPT-4.5는 텍스트와 이미지 입력을 수용하고 텍스트 출력을 생성합니다(구조화된 출력 포함). 함수 호출, 배치 API 및 스트리밍 출력을 포함한 주요 개발자 기능을 지원합니다. 창의적이고 개방적인 사고 및 대화가 필요한 작업(예: 글쓰기, 학습 또는 새로운 아이디어 탐색)에서 특히 뛰어난 성능을 보입니다. 지식 기준일은 2023년 10월입니다."}, "gpt-4o": {"description": "ChatGPT-4o는 동적 모델로, 최신 버전을 유지하기 위해 실시간으로 업데이트됩니다. 강력한 언어 이해 및 생성 능력을 결합하여 고객 서비스, 교육 및 기술 지원을 포함한 대규모 응용 프로그램에 적합합니다."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o는 동적 모델로, 최신 버전을 유지하기 위해 실시간으로 업데이트됩니다. 강력한 언어 이해 및 생성 능력을 결합하여 고객 서비스, 교육 및 기술 지원을 포함한 대규모 응용 프로그램에 적합합니다."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o는 동적 모델로, 최신 버전을 유지하기 위해 실시간으로 업데이트됩니다. 강력한 언어 이해 및 생성 능력을 결합하여 고객 서비스, 교육 및 기술 지원을 포함한 대규모 응용 프로그램에 적합합니다."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o는 동적 모델로, 최신 버전을 유지하기 위해 실시간으로 업데이트됩니다. 강력한 언어 이해 및 생성 능력을 결합하여 고객 서비스, 교육 및 기술 지원을 포함한 대규모 애플리케이션에 적합합니다."}, "gpt-4o-audio-preview": {"description": "GPT-4o 오디오 모델로, 오디오 입력 및 출력을 지원합니다."}, "gpt-4o-mini": {"description": "GPT-4o mini는 OpenAI가 GPT-4 Omni 이후에 출시한 최신 모델로, 텍스트와 이미지를 입력받아 텍스트를 출력합니다. 이 모델은 최신의 소형 모델로, 최근의 다른 최첨단 모델보다 훨씬 저렴하며, GPT-3.5 Turbo보다 60% 이상 저렴합니다. 최첨단의 지능을 유지하면서도 뛰어난 가성비를 자랑합니다. GPT-4o mini는 MMLU 테스트에서 82%의 점수를 기록했으며, 현재 채팅 선호도에서 GPT-4보다 높은 순위를 차지하고 있습니다."}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Audio 모델로, 오디오 입력과 출력을 지원합니다."}, "gpt-4o-mini-realtime-preview": {"description": "GPT-4o-mini 실시간 버전으로, 오디오 및 텍스트의 실시간 입력 및 출력을 지원합니다."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini 검색 미리보기 버전은 웹 검색 쿼리 이해 및 실행을 위해 특별히 훈련된 모델로, Chat Completions API를 사용합니다. 토큰 비용 외에 웹 검색 쿼리는 도구 호출당 별도의 비용이 부과됩니다."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe는 GPT-4o를 사용하여 오디오를 텍스트로 전사하는 음성 인식 모델입니다. 원래 Whisper 모델에 비해 단어 오류율이 개선되었고, 언어 인식 및 정확도가 향상되었습니다. 보다 정확한 전사를 위해 사용하세요."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS 는 GPT-4o mini 에 기반한 텍스트 음성 변환 모델로, 높은 품질의 음성 생성을 저렴한 가격으로 제공합니다."}, "gpt-4o-realtime-preview": {"description": "GPT-4o 실시간 버전으로, 오디오 및 텍스트의 실시간 입력 및 출력을 지원합니다."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "GPT-4o 실시간 버전으로, 오디오 및 텍스트의 실시간 입력 및 출력을 지원합니다."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "GPT-4o 실시간 버전으로, 오디오와 텍스트의 실시간 입출력을 지원합니다."}, "gpt-4o-search-preview": {"description": "GPT-4o 검색 미리보기 버전은 웹 검색 쿼리 이해 및 실행을 위해 특별히 훈련된 모델로, Chat Completions API를 사용합니다. 토큰 비용 외에 웹 검색 쿼리는 도구 호출당 별도의 비용이 부과됩니다."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe는 GPT-4o를 사용하여 오디오를 텍스트로 전사하는 음성 인식 모델입니다. 원래 Whisper 모델에 비해 단어 오류율이 개선되었고, 언어 인식 및 정확도가 향상되었습니다. 보다 정확한 전사를 위해 사용하세요."}, "gpt-image-1": {"description": "ChatGPT 네이티브 멀티모달 이미지 생성 모델"}, "grok-2-1212": {"description": "이 모델은 정확성, 지시 준수 및 다국어 능력에서 개선되었습니다."}, "grok-2-image-1212": {"description": "최신 이미지 생성 모델로, 텍스트 프롬프트에 따라 생생하고 사실적인 이미지를 생성할 수 있습니다. 마케팅, 소셜 미디어, 엔터테인먼트 등 분야에서 뛰어난 이미지 생성 성능을 발휘합니다."}, "grok-2-vision-1212": {"description": "이 모델은 정확성, 지시 준수 및 다국어 능력에서 개선되었습니다."}, "grok-3": {"description": "플래그십 모델로, 데이터 추출, 프로그래밍, 텍스트 요약 등 기업용 애플리케이션에 능하며 금융, 의료, 법률, 과학 분야에 대한 깊은 지식을 보유하고 있습니다."}, "grok-3-fast": {"description": "플래그십 모델로, 데이터 추출, 프로그래밍, 텍스트 요약 등 기업용 애플리케이션에 능하며 금융, 의료, 법률, 과학 분야에 대한 깊은 지식을 보유하고 있습니다."}, "grok-3-mini": {"description": "경량 모델로, 대화 전에 먼저 사고합니다. 빠르고 지능적으로 작동하며, 깊은 도메인 지식이 필요 없는 논리 작업에 적합하고 원시 사고 경로를 획득할 수 있습니다."}, "grok-3-mini-fast": {"description": "경량 모델로, 대화 전에 먼저 사고합니다. 빠르고 지능적으로 작동하며, 깊은 도메인 지식이 필요 없는 논리 작업에 적합하고 원시 사고 경로를 획득할 수 있습니다."}, "grok-4": {"description": "저희의 최신이자 가장 강력한 플래그십 모델로, 자연어 처리, 수학 계산 및 추론에서 뛰어난 성능을 자랑합니다 — 완벽한 만능형 선수입니다."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B는 여러 최상위 모델을 통합한 창의성과 지능이 결합된 언어 모델입니다."}, "hunyuan-a13b": {"description": "혼위안의 첫 혼합 추론 모델인 hunyuan-standard-256K의 업그레이드 버전으로, 총 800억 파라미터, 활성화 130억 파라미터를 갖추고 있습니다. 기본적으로 느린 사고 모드이며, 파라미터나 명령어를 통해 빠른/느린 사고 모드 전환을 지원합니다. 빠른/느린 사고 전환 방식은 쿼리 앞에 /no_think를 추가하는 방식입니다. 전반적인 능력은 이전 세대에 비해 전면적으로 향상되었으며, 특히 수학, 과학, 긴 문서 이해 및 에이전트 능력이 크게 개선되었습니다."}, "hunyuan-code": {"description": "혼원 최신 코드 생성 모델로, 200B 고품질 코드 데이터로 증훈된 기초 모델을 기반으로 하며, 6개월간 고품질 SFT 데이터 훈련을 거쳤습니다. 컨텍스트 길이는 8K로 증가하였으며, 다섯 가지 언어의 코드 생성 자동 평가 지표에서 상위에 위치하고 있습니다. 다섯 가지 언어의 10개 항목에서 종합 코드 작업의 인공지능 고품질 평가에서 성능이 1위입니다."}, "hunyuan-functioncall": {"description": "혼원 최신 MOE 구조의 FunctionCall 모델로, 고품질 FunctionCall 데이터 훈련을 거쳤으며, 컨텍스트 윈도우는 32K에 도달하고 여러 차원의 평가 지표에서 선두에 있습니다."}, "hunyuan-large": {"description": "Hunyuan-large 모델의 총 매개변수 수는 약 389B, 활성화 매개변수 수는 약 52B로, 현재 업계에서 매개변수 규모가 가장 크고 성능이 가장 우수한 Transformer 구조의 오픈 소스 MoE 모델입니다."}, "hunyuan-large-longcontext": {"description": "문서 요약 및 문서 질문 응답과 같은 긴 문서 작업을 잘 처리하며, 일반 텍스트 생성 작업도 수행할 수 있는 능력을 갖추고 있습니다. 긴 텍스트의 분석 및 생성에서 뛰어난 성능을 보이며, 복잡하고 상세한 긴 문서 내용 처리 요구에 효과적으로 대응할 수 있습니다."}, "hunyuan-large-vision": {"description": "이 모델은 이미지-텍스트 이해 시나리오에 적합하며, 혼원 Large를 기반으로 훈련된 비전-언어 대형 모델입니다. 임의 해상도의 다중 이미지+텍스트 입력을 지원하며, 텍스트 생성에 특화되어 이미지-텍스트 이해 관련 작업에 집중합니다. 다국어 이미지-텍스트 이해 능력이 크게 향상되었습니다."}, "hunyuan-lite": {"description": "MOE 구조로 업그레이드되었으며, 컨텍스트 윈도우는 256k로 설정되어 NLP, 코드, 수학, 산업 등 여러 평가 집합에서 많은 오픈 소스 모델을 선도하고 있습니다."}, "hunyuan-lite-vision": {"description": "혼원 최신 7B 다중 모달 모델, 컨텍스트 윈도우 32K, 중문 및 영문 환경에서의 다중 모달 대화, 이미지 객체 인식, 문서 표 이해, 다중 모달 수학 등을 지원하며, 여러 차원에서 평가 지표가 7B 경쟁 모델보다 우수합니다."}, "hunyuan-pro": {"description": "조 단위 매개변수 규모의 MOE-32K 긴 문서 모델입니다. 다양한 벤치마크에서 절대적인 선두 수준에 도달하며, 복잡한 지시 및 추론, 복잡한 수학 능력을 갖추고 있으며, functioncall을 지원하고 다국어 번역, 금융, 법률, 의료 등 분야에서 최적화된 응용을 제공합니다."}, "hunyuan-role": {"description": "혼원 최신 버전의 역할 수행 모델로, 혼원 공식적으로 세밀하게 조정된 훈련을 통해 출시된 역할 수행 모델입니다. 혼원 모델과 역할 수행 시나리오 데이터 세트를 결합하여 증훈하였으며, 역할 수행 시나리오에서 더 나은 기본 성능을 제공합니다."}, "hunyuan-standard": {"description": "더 나은 라우팅 전략을 채택하여 부하 균형 및 전문가 수렴 문제를 완화했습니다. 긴 문서의 경우, 대해잡기 지표가 99.9%에 도달했습니다. MOE-32K는 상대적으로 더 높은 가성비를 제공하며, 효과와 가격의 균형을 맞추면서 긴 텍스트 입력 처리를 가능하게 합니다."}, "hunyuan-standard-256K": {"description": "더 나은 라우팅 전략을 채택하여 부하 균형 및 전문가 수렴 문제를 완화했습니다. 긴 문서의 경우, 대해잡기 지표가 99.9%에 도달했습니다. MOE-256K는 길이와 효과에서 더욱 발전하여 입력 길이를 크게 확장했습니다."}, "hunyuan-standard-vision": {"description": "혼원 최신 다중 모달 모델, 다국어 응답 지원, 중문 및 영문 능력이 균형 잡혀 있습니다."}, "hunyuan-t1-20250321": {"description": "모델의 문리과 능력을 종합적으로 구축하며, 긴 텍스트 정보 포착 능력이 뛰어납니다. 다양한 난이도의 수학/논리 추론/과학/코드 등 과학 문제에 대한 추론 답변을 지원합니다."}, "hunyuan-t1-20250403": {"description": "프로젝트 수준의 코드 생성 능력 향상; 텍스트 생성 및 작문 품질 향상; 텍스트 이해 주제의 다중 라운드, B2B 명령 준수 및 단어 이해 능력 향상; 번체와 간체 혼용 및 중영 혼용 출력 문제 최적화."}, "hunyuan-t1-20250529": {"description": "텍스트 창작과 작문을 최적화하고, 코드 프론트엔드, 수학, 논리 추론 등 이공계 능력을 향상시키며, 명령어 준수 능력을 강화합니다."}, "hunyuan-t1-20250711": {"description": "고난도 수학, 논리, 코딩 능력을 대폭 향상시키고 모델 출력 안정성을 최적화했으며, 장문 처리 능력을 강화했습니다."}, "hunyuan-t1-latest": {"description": "업계 최초의 초대형 Hybrid-Transformer-<PERSON>mba 추론 모델로, 추론 능력을 확장하고, 뛰어난 디코딩 속도를 자랑하며, 인간의 선호에 더욱 부합합니다."}, "hunyuan-t1-vision": {"description": "혼원 다중모달 이해 심층 사고 모델로, 다중모달 원천 사고 체인을 지원하며 다양한 이미지 추론 시나리오에 능숙합니다. 이과 문제에서 빠른 사고 모델 대비 전반적인 성능 향상을 보입니다."}, "hunyuan-t1-vision-20250619": {"description": "혼위안 최신 버전 t1-vision 다중 모달 이해 심층 사고 모델로, 다중 모달 원생 사고 사슬을 지원하며 이전 세대 기본 모델에 비해 전면적으로 향상되었습니다."}, "hunyuan-turbo": {"description": "혼원 최신 세대 대형 언어 모델의 미리보기 버전으로, 새로운 혼합 전문가 모델(MoE) 구조를 채택하여 hunyuan-pro보다 추론 효율이 더 빠르고 성능이 더 뛰어납니다."}, "hunyuan-turbo-20241223": {"description": "이번 버전 최적화: 데이터 지시 스케일링, 모델의 일반화 능력 대폭 향상; 수학, 코드, 논리 추론 능력 대폭 향상; 텍스트 이해 및 단어 이해 관련 능력 최적화; 텍스트 창작 내용 생성 질 최적화."}, "hunyuan-turbo-latest": {"description": "일반적인 경험 최적화, NLP 이해, 텍스트 창작, 대화, 지식 질문 응답, 번역, 분야 등; 인간화 향상, 모델의 감정 지능 최적화; 의도가 모호할 때 모델의 능동적인 명확화 능력 향상; 단어 및 구문 분석 관련 문제 처리 능력 향상; 창작의 질과 상호작용성 향상; 다중 회차 경험 향상."}, "hunyuan-turbo-vision": {"description": "혼원 차세대 비주얼 언어 플래그십 대형 모델, 새로운 혼합 전문가 모델(MoE) 구조를 채택하여, 이미지 및 텍스트 이해 관련 기본 인식, 콘텐츠 창작, 지식 질문 응답, 분석 추론 등의 능력이 이전 세대 모델에 비해 전반적으로 향상되었습니다."}, "hunyuan-turbos-20250313": {"description": "수학 문제 해결 단계 스타일 통일, 수학 다중 라운드 질의응답 강화. 텍스트 창작에서 답변 스타일 최적화, AI 느낌 제거, 문학적 표현 강화."}, "hunyuan-turbos-20250416": {"description": "사전 학습 기반 업그레이드로 명령 이해 및 준수 능력 강화; 정렬 단계에서 수학, 코드, 논리, 과학 등 이공계 능력 강화; 문예 창작 품질, 텍스트 이해, 번역 정확도, 지식 질의응답 등 인문계 능력 향상; 각 분야 에이전트 능력 강화, 특히 다중 라운드 대화 이해 능력 중점 강화."}, "hunyuan-turbos-20250604": {"description": "사전 학습 기반 업그레이드로 작문 및 독해 능력이 향상되었으며, 코드 및 이공계 능력이 크게 향상되고 복잡한 명령어 준수 능력도 지속적으로 개선됩니다."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS 혼원 플래그십 대모델 최신 버전으로, 더 강력한 사고 능력과 더 나은 경험 효과를 제공합니다."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "문서 요약 및 문서 질문 응답과 같은 긴 문서 작업을 잘 처리하며, 일반 텍스트 생성 작업도 수행할 수 있는 능력을 갖추고 있습니다. 긴 텍스트 분석 및 생성에서 뛰어난 성능을 발휘하며, 복잡하고 상세한 긴 문서 내용 처리 요구에 효과적으로 대응할 수 있습니다."}, "hunyuan-turbos-role-plus": {"description": "혼원 최신 버전 역할극 모델로, 혼원 공식 미세 조정 훈련을 거친 역할극 모델입니다. 혼원 모델과 역할극 시나리오 데이터셋을 결합해 추가 훈련하여 역할극 시나리오에서 더 우수한 기본 성능을 제공합니다."}, "hunyuan-turbos-vision": {"description": "이 모델은 이미지-텍스트 이해 시나리오에 적합하며, 혼위안 최신 turbos 기반의 차세대 비주얼 언어 플래그십 대형 모델입니다. 이미지 기반 엔티티 인식, 지식 질문응답, 문안 작성, 사진 촬영 문제 해결 등 이미지-텍스트 이해 관련 작업에 집중하며, 이전 세대 모델에 비해 전면적으로 향상되었습니다."}, "hunyuan-turbos-vision-20250619": {"description": "혼위안 최신 버전 turbos-vision 비주얼 언어 플래그십 대형 모델로, 이미지 기반 엔티티 인식, 지식 질문응답, 문안 작성, 사진 촬영 문제 해결 등 이미지-텍스트 이해 관련 작업에서 이전 세대 기본 모델에 비해 전면적으로 향상되었습니다."}, "hunyuan-vision": {"description": "혼원 최신 다중 모달 모델로, 이미지와 텍스트 입력을 지원하여 텍스트 콘텐츠를 생성합니다."}, "image-01": {"description": "새로운 이미지 생성 모델로, 섬세한 화질을 자랑하며 텍스트-이미지 및 이미지-이미지 생성을 지원합니다."}, "image-01-live": {"description": "이미지 생성 모델로, 섬세한 화질을 제공하며 텍스트-이미지 생성과 화풍 설정을 지원합니다."}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 4세대 텍스트-이미지 모델 시리즈"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 4세대 텍스트-이미지 모델 시리즈 울트라 버전"}, "imagen4/preview": {"description": "Google의 최고 품질 이미지 생성 모델"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5는 다양한 시나리오에서 스마트 대화 솔루션을 제공합니다."}, "internlm2.5-latest": {"description": "우리가 최신으로 선보이는 모델 시리즈로, 뛰어난 추론 성능을 자랑하며 1M의 컨텍스트 길이와 더 강력한 지시 따르기 및 도구 호출 기능을 지원합니다."}, "internlm3-latest": {"description": "우리의 최신 모델 시리즈는 뛰어난 추론 성능을 가지고 있으며, 동급 오픈 소스 모델 중에서 선두를 달리고 있습니다. 기본적으로 최신 출시된 InternLM3 시리즈 모델을 가리킵니다."}, "internvl2.5-latest": {"description": "우리가 여전히 유지 관리하는 InternVL2.5 버전으로, 우수하고 안정적인 성능을 제공합니다. 기본적으로 최신 발표된 InternVL2.5 시리즈 모델을 가리키며, 현재 internvl2.5-78b를 가리킵니다."}, "internvl3-latest": {"description": "우리가 최근 발표한 다중 모달 대형 모델로, 더 강력한 이미지 및 텍스트 이해 능력과 장기 이미지 이해 능력을 갖추고 있으며, 성능은 최상급 폐쇄형 모델에 필적합니다. 기본적으로 최신 발표된 InternVL 시리즈 모델을 가리키며, 현재 internvl3-78b를 가리킵니다."}, "irag-1.0": {"description": "바이두가 자체 개발한 iRAG(image based RAG)로, 검색 강화 텍스트-이미지 생성 기술입니다. 바이두 검색의 수억 장 이미지 자원과 강력한 기본 모델 능력을 결합하여 매우 사실적인 이미지를 생성하며, 기존 텍스트-이미지 시스템을 훨씬 능가합니다. AI 느낌이 없고 비용도 매우 낮습니다. iRAG는 환각이 없고, 초현실적이며 즉시 사용 가능한 특징을 갖추고 있습니다."}, "jamba-large": {"description": "가장 강력하고 진보된 모델로, 기업급 복잡한 작업을 처리하도록 설계되었으며, 뛰어난 성능을 제공합니다."}, "jamba-mini": {"description": "동급에서 가장 효율적인 모델로, 속도와 품질을 모두 갖추고 있으며, 더 작은 크기를 자랑합니다."}, "jina-deepsearch-v1": {"description": "딥 서치는 웹 검색, 독서 및 추론을 결합하여 포괄적인 조사를 수행합니다. 연구 작업을 수용하는 에이전트로 생각할 수 있으며, 광범위한 검색을 수행하고 여러 번 반복한 후에야 답변을 제공합니다. 이 과정은 지속적인 연구, 추론 및 다양한 각도에서 문제를 해결하는 것을 포함합니다. 이는 사전 훈련된 데이터에서 직접 답변을 생성하는 표준 대형 모델 및 일회성 표면 검색에 의존하는 전통적인 RAG 시스템과 근본적으로 다릅니다."}, "kimi-k2": {"description": "Kimi-K2는 Moonshot AI가 출시한 초강력 코드 및 에이전트 능력을 갖춘 MoE 아키텍처 기반 모델로, 총 파라미터 1조, 활성화 파라미터 320억입니다. 범용 지식 추론, 프로그래밍, 수학, 에이전트 등 주요 분야 벤치마크에서 K2 모델은 다른 주류 오픈 소스 모델을 능가하는 성능을 보입니다."}, "kimi-k2-0711-preview": {"description": "kimi-k2는 강력한 코드 및 에이전트 기능을 갖춘 MoE 아키텍처 기반 모델로, 총 파라미터 1조, 활성화 파라미터 320억을 보유하고 있습니다. 일반 지식 추론, 프로그래밍, 수학, 에이전트 등 주요 분야 벤치마크 성능 테스트에서 K2 모델은 다른 주요 오픈소스 모델을 능가하는 성능을 보여줍니다."}, "kimi-latest": {"description": "Kimi 스마트 어시스턴트 제품은 최신 Kimi 대형 모델을 사용하며, 아직 안정되지 않은 기능이 포함될 수 있습니다. 이미지 이해를 지원하며, 요청의 맥락 길이에 따라 8k/32k/128k 모델을 청구 모델로 자동 선택합니다."}, "kimi-thinking-preview": {"description": "kimi-thinking-preview 모델은 월면의 어두운 면에서 제공하는 다중 모달 추론 능력과 범용 추론 능력을 갖춘 다중 모달 사고 모델로, 심층 추론에 능하며 더 어렵고 복잡한 문제 해결을 돕습니다."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM은 학습 과학 원칙에 맞춰 훈련된 실험적이고 특정 작업에 특화된 언어 모델로, 교육 및 학습 환경에서 시스템 지침을 따르며 전문가 멘토 역할을 수행합니다."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM은 실험적인 특정 작업 언어 모델로, 학습 과학 원칙에 맞게 훈련되어 교육 및 학습 환경에서 시스템 지침을 따르고 전문가 멘토 역할을 수행합니다."}, "lite": {"description": "Spark Lite는 경량 대형 언어 모델로, 매우 낮은 지연 시간과 효율적인 처리 능력을 갖추고 있으며, 완전히 무료로 제공되고 실시간 온라인 검색 기능을 지원합니다. 빠른 응답 특성 덕분에 저전력 장치에서의 추론 응용 및 모델 미세 조정에서 뛰어난 성능을 발휘하며, 사용자에게 뛰어난 비용 효율성과 스마트한 경험을 제공합니다. 특히 지식 질문 응답, 콘텐츠 생성 및 검색 시나리오에서 두각을 나타냅니다."}, "llama-2-7b-chat": {"description": "Llama2는 Meta에서 개발하고 오픈소스로 공개한 대형 언어 모델(LLM) 시리즈로, 70억에서 700억 개의 매개변수를 가진 다양한 규모의 사전 학습 및 미세 조정된 생성형 텍스트 모델입니다. 구조적으로 Llama2는 최적화된 트랜스포머 아키텍처를 사용하는 자동 회귀 언어 모델입니다. 조정된 버전은 감독된 미세 조정(SFT)과 인간 피드백을 활용한 강화 학습(RLHF)을 사용하여 인간의 유용성과 안전성 선호도에 맞춥니다. Llama2는 Llama 시리즈보다 다양한 학술 데이터셋에서 더욱 뛰어난 성능을 보여주며, 많은 다른 모델의 설계와 개발에 영감을 주고 있습니다."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B는 더 강력한 AI 추론 능력을 제공하며, 복잡한 응용 프로그램에 적합하고, 많은 계산 처리를 지원하며 효율성과 정확성을 보장합니다."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B는 효율적인 모델로, 빠른 텍스트 생성 능력을 제공하며, 대규모 효율성과 비용 효과성이 필요한 응용 프로그램에 매우 적합합니다."}, "llama-3.1-instruct": {"description": "Llama 3.1 지침 미세 조정 모델은 대화 시나리오에 최적화되어 있으며, 일반적인 업계 벤치마크 테스트에서 기존의 많은 오픈소스 채팅 모델을 능가합니다."}, "llama-3.2-11b-vision-instruct": {"description": "고해상도 이미지에서 탁월한 이미지 추론 능력을 발휘하며, 시각 이해 응용 프로그램에 적합합니다."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하기 위해 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 보이며, 언어 생성과 시각적 추론 간의 간극을 넘습니다."}, "llama-3.2-90b-vision-instruct": {"description": "시각 이해 에이전트 응용 프로그램에 적합한 고급 이미지 추론 능력입니다."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하기 위해 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 보이며, 언어 생성과 시각적 추론 간의 간극을 넘습니다."}, "llama-3.2-vision-instruct": {"description": "Llama 3.2-Vision 지시 미세 조정 모델은 시각 인식, 이미지 추론, 이미지 설명 및 이미지 관련 일반 질문 답변에 최적화되었습니다."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3은 Llama 시리즈에서 가장 진보된 다국어 오픈 소스 대형 언어 모델로, 매우 낮은 비용으로 405B 모델의 성능을 경험할 수 있습니다. Transformer 구조를 기반으로 하며, 감독 미세 조정(SFT)과 인간 피드백 강화 학습(RLHF)을 통해 유용성과 안전성을 향상시켰습니다. 이 지시 조정 버전은 다국어 대화를 위해 최적화되어 있으며, 여러 산업 벤치마크에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다. 지식 마감일은 2023년 12월입니다."}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 다국어 대형 언어 모델(LLM)은 70B(텍스트 입력/텍스트 출력)에서 사전 학습 및 지침 조정 생성 모델입니다. Llama 3.3의 지침 조정 순수 텍스트 모델은 다국어 대화 사용 사례에 최적화되어 있으며, 많은 오픈 소스 및 폐쇄형 채팅 모델보다 일반 산업 기준에서 우수한 성능을 보입니다."}, "llama-3.3-instruct": {"description": "Llama 3.3 지침 미세 조정 모델은 대화 시나리오에 최적화되어 있으며, 일반적인 업계 벤치마크 테스트에서 기존의 많은 오픈소스 채팅 모델을 능가합니다."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B는 비할 데 없는 복잡성 처리 능력을 제공하며, 높은 요구 사항을 가진 프로젝트에 맞춤형으로 설계되었습니다."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B는 우수한 추론 성능을 제공하며, 다양한 응용 프로그램 요구에 적합합니다."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use는 강력한 도구 호출 능력을 제공하며, 복잡한 작업의 효율적인 처리를 지원합니다."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use는 효율적인 도구 사용을 위해 최적화된 모델로, 빠른 병렬 계산을 지원합니다."}, "llama3.1": {"description": "Llama 3.1은 Meta에서 출시한 선도적인 모델로, 최대 405B 매개변수를 지원하며, 복잡한 대화, 다국어 번역 및 데이터 분석 분야에 적용될 수 있습니다."}, "llama3.1:405b": {"description": "Llama 3.1은 Meta에서 출시한 선도적인 모델로, 최대 405B 매개변수를 지원하며, 복잡한 대화, 다국어 번역 및 데이터 분석 분야에 적용될 수 있습니다."}, "llama3.1:70b": {"description": "Llama 3.1은 Meta에서 출시한 선도적인 모델로, 최대 405B 매개변수를 지원하며, 복잡한 대화, 다국어 번역 및 데이터 분석 분야에 적용될 수 있습니다."}, "llava": {"description": "LLaVA는 시각 인코더와 Vicuna를 결합한 다중 모달 모델로, 강력한 시각 및 언어 이해를 제공합니다."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B는 시각 처리 능력을 융합하여, 시각 정보 입력을 통해 복잡한 출력을 생성합니다."}, "llava:13b": {"description": "LLaVA는 시각 인코더와 Vicuna를 결합한 다중 모달 모델로, 강력한 시각 및 언어 이해를 제공합니다."}, "llava:34b": {"description": "LLaVA는 시각 인코더와 Vicuna를 결합한 다중 모달 모델로, 강력한 시각 및 언어 이해를 제공합니다."}, "mathstral": {"description": "MathΣtral은 과학 연구 및 수학 추론을 위해 설계되었으며, 효과적인 계산 능력과 결과 해석을 제공합니다."}, "max-32k": {"description": "Spark Max 32K는 큰 컨텍스트 처리 능력을 갖추고 있으며, 더 강력한 컨텍스트 이해 및 논리 추론 능력을 지원합니다. 32K 토큰의 텍스트 입력을 지원하며, 긴 문서 읽기, 개인 지식 질문 응답 등 다양한 시나리오에 적합합니다."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct는 무문심궁이 완전히 독립적으로 훈련한 대형 언어 모델입니다. Megrez-3B-Instruct는 소프트웨어와 하드웨어의 협동 개념을 통해 빠른 추론, 컴팩트하고 강력하며 사용하기 쉬운 엣지 측 인텔리전스 솔루션을 제공하는 것을 목표로 합니다."}, "meta-llama-3-70b-instruct": {"description": "추론, 코딩 및 광범위한 언어 응용 프로그램에서 뛰어난 성능을 발휘하는 강력한 70억 매개변수 모델입니다."}, "meta-llama-3-8b-instruct": {"description": "대화 및 텍스트 생성 작업에 최적화된 다재다능한 8억 매개변수 모델입니다."}, "meta-llama-3.1-405b-instruct": {"description": "Llama 3.1 지침 조정된 텍스트 전용 모델은 다국어 대화 사용 사례에 최적화되어 있으며, 일반 산업 벤치마크에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다."}, "meta-llama-3.1-70b-instruct": {"description": "Llama 3.1 지침 조정된 텍스트 전용 모델은 다국어 대화 사용 사례에 최적화되어 있으며, 일반 산업 벤치마크에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다."}, "meta-llama-3.1-8b-instruct": {"description": "Llama 3.1 지침 조정된 텍스트 전용 모델은 다국어 대화 사용 사례에 최적화되어 있으며, 일반 산업 벤치마크에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B)는 뛰어난 언어 처리 능력과 우수한 상호작용 경험을 제공합니다."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2는 뛰어난 언어 처리 능력과 뛰어난 상호작용 경험을 제공합니다."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B)는 강력한 채팅 모델로, 복잡한 대화 요구를 지원합니다."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B)는 다국어 지원을 제공하며, 풍부한 분야 지식을 포함합니다."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하도록 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 발휘하며, 언어 생성과 시각 추론 간의 간극을 메웁니다."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하도록 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 발휘하며, 언어 생성과 시각 추론 간의 간극을 메웁니다."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하도록 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 발휘하며, 언어 생성과 시각 추론 간의 간극을 메웁니다."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 다국어 대형 언어 모델(LLM)은 70B(텍스트 입력/텍스트 출력)에서 사전 훈련 및 지시 조정 생성 모델입니다. Llama 3.3 지시 조정의 순수 텍스트 모델은 다국어 대화 사용 사례에 최적화되어 있으며, 일반 산업 기준에서 많은 사용 가능한 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하도록 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 발휘하며, 언어 생성과 시각 추론 간의 간극을 메웁니다."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite는 효율성과 낮은 지연 시간이 필요한 환경에 적합합니다."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo는 뛰어난 언어 이해 및 생성 능력을 제공하며, 가장 까다로운 계산 작업에 적합합니다."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite는 자원이 제한된 환경에 적합하며, 뛰어난 균형 성능을 제공합니다."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo는 효율적인 대형 언어 모델로, 광범위한 응용 분야를 지원합니다."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B는 사전 훈련 및 지시 조정의 강력한 모델입니다."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405B Llama 3.1 Turbo 모델은 대규모 데이터 처리를 위한 초대용량의 컨텍스트 지원을 제공하며, 초대규모 인공지능 애플리케이션에서 뛰어난 성능을 발휘합니다."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1은 Meta에서 출시한 선도적인 모델로, 최대 405B 매개변수를 지원하며 복잡한 대화, 다국어 번역 및 데이터 분석 분야에 적용됩니다."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70B 모델은 정밀 조정되어 고부하 애플리케이션에 적합하며, FP8로 양자화되어 더 높은 효율의 계산 능력과 정확성을 제공합니다."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8B 모델은 FP8 양자화를 사용하여 최대 131,072개의 컨텍스트 토큰을 지원하며, 오픈 소스 모델 중에서 뛰어난 성능을 발휘하여 복잡한 작업에 적합합니다."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct는 고품질 대화 시나리오에 최적화되어 있으며, 다양한 인간 평가에서 뛰어난 성능을 보여줍니다."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct는 고품질 대화 시나리오에 최적화되어 있으며, 많은 폐쇄형 모델보다 우수한 성능을 보입니다."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct는 고품질 대화를 위해 설계되었으며, 인간 평가에서 뛰어난 성능을 보여주고, 특히 높은 상호작용 시나리오에 적합합니다."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct는 Meta에서 출시한 최신 버전으로, 고품질 대화 시나리오에 최적화되어 있으며, 많은 선도적인 폐쇄형 모델보다 우수한 성능을 보입니다."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1은 다국어 지원을 제공하며, 업계 최고의 생성 모델 중 하나입니다."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하기 위해 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 보이며, 언어 생성과 시각적 추론 간의 간극을 넘습니다."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2는 시각 및 텍스트 데이터를 결합한 작업을 처리하기 위해 설계되었습니다. 이미지 설명 및 시각적 질문 응답과 같은 작업에서 뛰어난 성능을 보이며, 언어 생성과 시각적 추론 간의 간극을 넘습니다."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3은 Llama 시리즈에서 가장 진보된 다국어 오픈 소스 대형 언어 모델로, 매우 낮은 비용으로 405B 모델의 성능을 경험할 수 있습니다. Transformer 구조를 기반으로 하며, 감독 미세 조정(SFT)과 인간 피드백 강화 학습(RLHF)을 통해 유용성과 안전성을 향상시켰습니다. 이 지시 조정 버전은 다국어 대화를 위해 최적화되어 있으며, 여러 산업 벤치마크에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다. 지식 마감일은 2023년 12월입니다."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3은 Llama 시리즈에서 가장 진보된 다국어 오픈 소스 대형 언어 모델로, 매우 낮은 비용으로 405B 모델의 성능을 경험할 수 있습니다. Transformer 구조를 기반으로 하며, 감독 미세 조정(SFT)과 인간 피드백 강화 학습(RLHF)을 통해 유용성과 안전성을 향상시켰습니다. 이 지시 조정 버전은 다국어 대화를 위해 최적화되어 있으며, 여러 산업 벤치마크에서 많은 오픈 소스 및 폐쇄형 채팅 모델보다 우수한 성능을 보입니다. 지식 마감일은 2023년 12월입니다."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct는 Llama 3.1 Instruct 모델 중 가장 크고 강력한 모델로, 고도로 발전된 대화 추론 및 합성 데이터 생성 모델입니다. 특정 분야에서 전문적인 지속적 사전 훈련 또는 미세 조정의 기초로도 사용될 수 있습니다. Llama 3.1이 제공하는 다국어 대형 언어 모델(LLMs)은 8B, 70B 및 405B 크기의 사전 훈련된 지시 조정 생성 모델로 구성되어 있습니다(텍스트 입력/출력). Llama 3.1 지시 조정 텍스트 모델(8B, 70B, 405B)은 다국어 대화 사용 사례에 최적화되어 있으며, 일반 산업 벤치마크 테스트에서 많은 오픈 소스 채팅 모델을 초과했습니다. Llama 3.1은 다양한 언어의 상업적 및 연구 용도로 설계되었습니다. 지시 조정 텍스트 모델은 비서와 유사한 채팅에 적합하며, 사전 훈련 모델은 다양한 자연어 생성 작업에 적응할 수 있습니다. Llama 3.1 모델은 또한 모델의 출력을 활용하여 다른 모델을 개선하는 것을 지원하며, 합성 데이터 생성 및 정제에 사용될 수 있습니다. Llama 3.1은 최적화된 변압기 아키텍처를 사용한 자기 회귀 언어 모델입니다. 조정된 버전은 감독 미세 조정(SFT) 및 인간 피드백이 포함된 강화 학습(RLHF)을 사용하여 인간의 도움 및 안전성 선호에 부합하도록 설계되었습니다."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Meta Llama 3.1 70B Instruct의 업데이트 버전으로, 확장된 128K 컨텍스트 길이, 다국어 지원 및 개선된 추론 능력을 포함합니다. Llama 3.1이 제공하는 다국어 대형 언어 모델(LLMs)은 사전 훈련된 지침 조정 생성 모델의 집합으로, 8B, 70B 및 405B 크기(텍스트 입력/출력)를 포함합니다. Llama 3.1 지침 조정 텍스트 모델(8B, 70B, 405B)은 다국어 대화 사용 사례에 최적화되어 있으며, 일반적인 산업 벤치마크 테스트에서 많은 사용 가능한 오픈 소스 채팅 모델을 초월했습니다. Llama 3.1은 다양한 언어의 상업적 및 연구 용도로 사용되도록 설계되었습니다. 지침 조정 텍스트 모델은 비서와 유사한 채팅에 적합하며, 사전 훈련된 모델은 다양한 자연어 생성 작업에 적응할 수 있습니다. Llama 3.1 모델은 또한 모델의 출력을 활용하여 다른 모델을 개선하는 데 지원하며, 합성 데이터 생성 및 정제 작업을 포함합니다. Llama 3.1은 최적화된 변압기 아키텍처를 사용한 자기 회귀 언어 모델입니다. 조정된 버전은 감독 미세 조정(SFT) 및 인간 피드백을 통한 강화 학습(RLHF)을 사용하여 인간의 도움 및 안전성 선호에 부합하도록 설계되었습니다."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Meta Llama 3.1 8B Instruct의 업데이트 버전으로, 확장된 128K 컨텍스트 길이, 다국어 지원 및 개선된 추론 능력을 포함합니다. Llama 3.1이 제공하는 다국어 대형 언어 모델(LLMs)은 사전 훈련된 지침 조정 생성 모델의 집합으로, 8B, 70B 및 405B 크기(텍스트 입력/출력)를 포함합니다. Llama 3.1 지침 조정 텍스트 모델(8B, 70B, 405B)은 다국어 대화 사용 사례에 최적화되어 있으며, 일반적인 산업 벤치마크 테스트에서 많은 사용 가능한 오픈 소스 채팅 모델을 초월했습니다. Llama 3.1은 다양한 언어의 상업적 및 연구 용도로 사용되도록 설계되었습니다. 지침 조정 텍스트 모델은 비서와 유사한 채팅에 적합하며, 사전 훈련된 모델은 다양한 자연어 생성 작업에 적응할 수 있습니다. Llama 3.1 모델은 또한 모델의 출력을 활용하여 다른 모델을 개선하는 데 지원하며, 합성 데이터 생성 및 정제 작업을 포함합니다. Llama 3.1은 최적화된 변압기 아키텍처를 사용한 자기 회귀 언어 모델입니다. 조정된 버전은 감독 미세 조정(SFT) 및 인간 피드백을 통한 강화 학습(RLHF)을 사용하여 인간의 도움 및 안전성 선호에 부합하도록 설계되었습니다."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3은 개발자, 연구자 및 기업을 위한 오픈 대형 언어 모델(LLM)로, 생성 AI 아이디어를 구축하고 실험하며 책임감 있게 확장하는 데 도움을 주기 위해 설계되었습니다. 전 세계 커뮤니티 혁신의 기초 시스템의 일환으로, 콘텐츠 생성, 대화 AI, 언어 이해, 연구 개발 및 기업 응용에 매우 적합합니다."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3은 개발자, 연구자 및 기업을 위한 오픈 대형 언어 모델(LLM)로, 생성 AI 아이디어를 구축하고 실험하며 책임감 있게 확장하는 데 도움을 주기 위해 설계되었습니다. 전 세계 커뮤니티 혁신의 기초 시스템의 일환으로, 계산 능력과 자원이 제한된 환경, 엣지 장치 및 더 빠른 훈련 시간에 매우 적합합니다."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "고해상도 이미지에서 뛰어난 이미지 추론 능력을 발휘하며, 시각적 이해 애플리케이션에 적합합니다."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "시각적 이해 에이전트 애플리케이션에 적합한 고급 이미지 추론 능력입니다."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3은 Llama 시리즈의 최첨단 다국어 오픈소스 대형 언어 모델로, 매우 낮은 비용으로 405B 모델에 필적하는 성능을 경험할 수 있습니다. Transformer 구조를 기반으로 하며, 감독 미세조정(SFT)과 인간 피드백 강화 학습(RLHF)을 통해 유용성과 안전성을 향상시켰습니다. 지침 조정 버전은 다국어 대화에 최적화되어 있으며, 여러 산업 벤치마크에서 다수의 오픈소스 및 폐쇄형 챗 모델을 능가합니다. 지식 기준일은 2023년 12월입니다."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "추론, 코딩 및 광범위한 언어 응용 분야에서 뛰어난 성능을 보이는 강력한 700억 매개변수 모델입니다."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "대화 및 텍스트 생성 작업에 최적화된 다목적 80억 매개변수 모델입니다."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 지침 조정 텍스트 모델로, 다국어 대화 사례에 최적화되어 있으며, 다수의 오픈소스 및 폐쇄형 챗 모델 중에서 일반 산업 벤치마크에서 우수한 성능을 보입니다."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 지침 조정 텍스트 모델로, 다국어 대화 사례에 최적화되어 있으며, 다수의 오픈소스 및 폐쇄형 챗 모델 중에서 일반 산업 벤치마크에서 우수한 성능을 보입니다."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 지침 조정 텍스트 모델로, 다국어 대화 사례에 최적화되어 있으며, 다수의 오픈소스 및 폐쇄형 챗 모델 중에서 일반 산업 벤치마크에서 우수한 성능을 보입니다."}, "meta/llama-3.1-405b-instruct": {"description": "합성 데이터 생성, 지식 증류 및 추론을 지원하는 고급 LLM으로, 챗봇, 프로그래밍 및 특정 분야 작업에 적합합니다."}, "meta/llama-3.1-70b-instruct": {"description": "복잡한 대화를 가능하게 하며, 뛰어난 맥락 이해, 추론 능력 및 텍스트 생성 능력을 갖추고 있습니다."}, "meta/llama-3.1-8b-instruct": {"description": "언어 이해, 뛰어난 추론 능력 및 텍스트 생성 능력을 갖춘 고급 최첨단 모델입니다."}, "meta/llama-3.2-11b-vision-instruct": {"description": "이미지에서 고품질 추론을 수행하는 최첨단 비주얼-언어 모델입니다."}, "meta/llama-3.2-1b-instruct": {"description": "언어 이해, 뛰어난 추론 능력 및 텍스트 생성 능력을 갖춘 최첨단 소형 언어 모델입니다."}, "meta/llama-3.2-3b-instruct": {"description": "언어 이해, 뛰어난 추론 능력 및 텍스트 생성 능력을 갖춘 최첨단 소형 언어 모델입니다."}, "meta/llama-3.2-90b-vision-instruct": {"description": "이미지에서 고품질 추론을 수행하는 최첨단 비주얼-언어 모델입니다."}, "meta/llama-3.3-70b-instruct": {"description": "추론, 수학, 상식 및 함수 호출에 능숙한 고급 LLM입니다."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "동일한 Phi-3-medium 모델이지만 더 큰 컨텍스트 크기를 제공하여 RAG 또는 소량 프롬프트에 적합합니다."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "140억 매개변수 모델로, Phi-3-mini보다 품질이 우수하며 고품질 추론 집약적 데이터에 중점을 둡니다."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "동일한 Phi-3-mini 모델이지만 더 큰 컨텍스트 크기를 제공하여 RAG 또는 소량 프롬프트에 적합합니다."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Phi-3 시리즈 중 가장 작은 모델로, 품질과 저지연에 최적화되어 있습니다."}, "microsoft/Phi-3-small-128k-instruct": {"description": "동일한 Phi-3-small 모델이지만 더 큰 컨텍스트 크기를 제공하여 RAG 또는 소량 프롬프트에 적합합니다."}, "microsoft/Phi-3-small-8k-instruct": {"description": "70억 매개변수 모델로, Phi-3-mini보다 품질이 우수하며 고품질 추론 집약적 데이터에 중점을 둡니다."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Phi-3-mini 모델의 업데이트 버전입니다."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Phi-3-vision 모델의 업데이트 버전입니다."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2는 Microsoft AI가 제공하는 언어 모델로, 복잡한 대화, 다국어, 추론 및 스마트 어시스턴트 분야에서 특히 뛰어난 성능을 보입니다."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B는 마이크로소프트 AI의 최첨단 Wizard 모델로, 매우 경쟁력 있는 성능을 보여줍니다."}, "minicpm-v": {"description": "MiniCPM-V는 OpenBMB에서 출시한 차세대 다중 모달 대형 모델로, 뛰어난 OCR 인식 및 다중 모달 이해 능력을 갖추고 있으며, 다양한 응용 프로그램을 지원합니다."}, "ministral-3b-latest": {"description": "Ministral 3B는 Mistral의 세계적 수준의 엣지 모델입니다."}, "ministral-8b-latest": {"description": "Ministral 8B는 Mistral의 뛰어난 가성비를 자랑하는 엣지 모델입니다."}, "mistral": {"description": "Mistral은 Mistral AI에서 출시한 7B 모델로, 변화하는 언어 처리 요구에 적합합니다."}, "mistral-ai/Mistral-Large-2411": {"description": "Mistral의 플래그십 모델로, 대규모 추론 능력이나 고도로 전문화된 복잡한 작업(합성 텍스트 생성, 코드 생성, RAG 또는 에이전트)에 적합합니다."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo는 최첨단 언어 모델(LLM)로, 해당 크기 범주에서 최상의 추론, 세계 지식 및 코딩 능력을 갖추고 있습니다."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small은 고효율 및 저지연이 필요한 모든 언어 기반 작업에 사용할 수 있습니다."}, "mistral-large": {"description": "Mixtral Large는 Mistral의 플래그십 모델로, 코드 생성, 수학 및 추론 능력을 결합하여 128k 컨텍스트 창을 지원합니다."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407은 1230억 개의 매개변수를 가진 첨단 고밀도 대형 언어 모델(LLM)로, 최첨단 추론 능력, 지식 및 코딩 능력을 갖추고 있습니다."}, "mistral-large-latest": {"description": "Mistral Large는 플래그십 대형 모델로, 다국어 작업, 복잡한 추론 및 코드 생성에 능숙하여 고급 응용 프로그램에 이상적인 선택입니다."}, "mistral-medium-latest": {"description": "Mistral Medium 3는 8배의 비용으로 최첨단 성능을 제공하며, 기업 배포를 근본적으로 단순화합니다."}, "mistral-nemo": {"description": "Mistral Nemo는 Mistral AI와 NVIDIA가 협력하여 출시한 고효율 12B 모델입니다."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 대형 언어 모델(LLM)은 Mistral-Nemo-Base-2407의 지시 미세 조정 버전입니다."}, "mistral-small": {"description": "Mistral Small은 높은 효율성과 낮은 대기 시간이 필요한 모든 언어 기반 작업에 사용할 수 있습니다."}, "mistral-small-latest": {"description": "Mistral Small은 번역, 요약 및 감정 분석과 같은 사용 사례에 적합한 비용 효율적이고 빠르며 신뢰할 수 있는 옵션입니다."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct는 높은 성능으로 유명하며, 다양한 언어 작업에 적합합니다."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B는 필요에 따라 미세 조정된 모델로, 작업에 최적화된 해답을 제공합니다."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3는 효율적인 계산 능력과 자연어 이해를 제공하며, 광범위한 응용에 적합합니다."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B는 컴팩트하지만 높은 성능의 모델로, 분류 및 텍스트 생성과 같은 간단한 작업을 잘 처리하며, 좋은 추론 능력을 갖추고 있습니다."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B)는 슈퍼 대형 언어 모델로, 극도의 처리 요구를 지원합니다."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B는 일반 텍스트 작업을 위한 사전 훈련된 희소 혼합 전문가 모델입니다."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B는 여러 파라미터를 활용하여 추론 속도를 높이는 희소 전문가 모델입니다. 다국어 및 코드 생성 작업 처리에 적합합니다."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct는 속도 최적화와 긴 컨텍스트 지원을 갖춘 고성능 산업 표준 모델입니다."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo는 다국어 지원과 고성능 프로그래밍을 위한 7.3B 파라미터 모델입니다."}, "mixtral": {"description": "Mixtral은 Mistral AI의 전문가 모델로, 오픈 소스 가중치를 가지고 있으며, 코드 생성 및 언어 이해를 지원합니다."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B는 높은 내결함성을 가진 병렬 계산 능력을 제공하며, 복잡한 작업에 적합합니다."}, "mixtral:8x22b": {"description": "Mixtral은 Mistral AI의 전문가 모델로, 오픈 소스 가중치를 가지고 있으며, 코드 생성 및 언어 이해를 지원합니다."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K는 초장기 컨텍스트 처리 능력을 갖춘 모델로, 초장문 생성을 위해 설계되었으며, 복잡한 생성 작업 요구를 충족하고 최대 128,000개의 토큰을 처리할 수 있어, 연구, 학술 및 대형 문서 생성 등 응용 시나리오에 매우 적합합니다."}, "moonshot-v1-128k-vision-preview": {"description": "<PERSON><PERSON> 시각 모델(예: moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 등)은 이미지 내용을 이해할 수 있으며, 이미지 텍스트, 이미지 색상 및 물체 형태 등을 포함합니다."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K는 중간 길이의 컨텍스트 처리 능력을 제공하며, 32,768개의 토큰을 처리할 수 있어, 다양한 장문 및 복잡한 대화 생성을 위해 특히 적합하며, 콘텐츠 생성, 보고서 작성 및 대화 시스템 등 분야에 활용됩니다."}, "moonshot-v1-32k-vision-preview": {"description": "<PERSON><PERSON> 시각 모델(예: moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 등)은 이미지 내용을 이해할 수 있으며, 이미지 텍스트, 이미지 색상 및 물체 형태 등을 포함합니다."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K는 짧은 텍스트 작업 생성을 위해 설계되었으며, 효율적인 처리 성능을 갖추고 있어 8,192개의 토큰을 처리할 수 있으며, 짧은 대화, 속기 및 빠른 콘텐츠 생성에 매우 적합합니다."}, "moonshot-v1-8k-vision-preview": {"description": "<PERSON><PERSON> 시각 모델(예: moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 등)은 이미지 내용을 이해할 수 있으며, 이미지 텍스트, 이미지 색상 및 물체 형태 등을 포함합니다."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto는 현재 맥락에서 사용되는 토큰 수에 따라 적합한 모델을 선택할 수 있습니다."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B는 대규모 강화 학습 최적화를 거친 오픈소스 코드 대형 모델로, 안정적이고 바로 생산에 투입 가능한 패치를 출력할 수 있습니다. 이 모델은 SWE-bench Verified에서 60.4%의 신기록을 세우며, 결함 수정, 코드 리뷰 등 자동화 소프트웨어 엔지니어링 작업에서 오픈소스 모델의 기록을 경신했습니다."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2는 초강력 코드 및 에이전트 능력을 갖춘 MoE 아키텍처 기반 모델로, 총 파라미터 1조, 활성화 파라미터 320억입니다. 범용 지식 추론, 프로그래밍, 수학, 에이전트 등 주요 분야 벤치마크에서 K2 모델은 다른 주류 오픈 소스 모델을 능가하는 성능을 보입니다."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2는 강력한 코드 및 에이전트 기능을 갖춘 MoE 아키텍처 기반 모델로, 총 파라미터 1조, 활성화 파라미터 320억입니다. 일반 지식 추론, 프로그래밍, 수학, 에이전트 등 주요 분야의 벤치마크 성능 테스트에서 K2 모델은 다른 주요 오픈소스 모델을 능가하는 성능을 보입니다."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B는 Nous Hermes 2의 업그레이드 버전으로, 최신 내부 개발 데이터 세트를 포함하고 있습니다."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B는 NVIDIA가 맞춤 제작한 대규모 언어 모델로, LLM이 생성한 응답이 사용자 쿼리에 얼마나 도움이 되는지를 향상시키기 위해 설계되었습니다. 이 모델은 Arena Hard, AlpacaEval 2 LC 및 GPT-4-Turbo MT-Bench와 같은 벤치마크 테스트에서 뛰어난 성능을 보였으며, 2024년 10월 1일 기준으로 모든 자동 정렬 벤치마크 테스트에서 1위를 차지했습니다. 이 모델은 RLHF(특히 REINFORCE), Llama-3.1-Nemotron-70B-Reward 및 HelpSteer2-Preference 프롬프트를 사용하여 Llama-3.1-70B-Instruct 모델을 기반으로 훈련되었습니다."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "비교할 수 없는 정확성과 효율성을 제공하는 독특한 언어 모델입니다."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct는 NVIDIA가 맞춤 제작한 대형 언어 모델로, LLM이 생성한 응답의 유용성을 향상시키기 위해 설계되었습니다."}, "o1": {"description": "고급 추론 및 복잡한 문제 해결에 중점을 두며, 수학 및 과학 작업을 포함합니다. 깊이 있는 컨텍스트 이해와 에이전트 작업 흐름이 필요한 애플리케이션에 매우 적합합니다."}, "o1-mini": {"description": "o1-mini는 프로그래밍, 수학 및 과학 응용 프로그램을 위해 설계된 빠르고 경제적인 추론 모델입니다. 이 모델은 128K의 컨텍스트와 2023년 10월의 지식 기준일을 가지고 있습니다."}, "o1-preview": {"description": "o1은 OpenAI의 새로운 추론 모델로, 광범위한 일반 지식이 필요한 복잡한 작업에 적합합니다. 이 모델은 128K의 컨텍스트와 2023년 10월의 지식 기준일을 가지고 있습니다."}, "o1-pro": {"description": "o1 시리즈 모델은 강화 학습을 통해 훈련되어 답변 전에 사고를 진행하고 복잡한 추론 작업을 수행할 수 있습니다. o1-pro 모델은 더 많은 계산 자원을 사용하여 더 깊이 사고함으로써 지속적으로 더 우수한 답변을 제공합니다."}, "o3": {"description": "o3는 다재다능한 강력한 모델로, 여러 분야에서 뛰어난 성능을 발휘합니다. 수학, 과학, 프로그래밍 및 시각적 추론 작업에서 새로운 기준을 세웠습니다. 기술 작문 및 지시 준수에도 능숙합니다. 사용자는 이를 통해 텍스트, 코드 및 이미지를 분석하고, 다단계 복잡한 문제를 해결할 수 있습니다."}, "o3-deep-research": {"description": "o3-deep-research는 복잡한 다단계 연구 작업을 처리하도록 설계된 당사의 최첨단 심층 연구 모델입니다. 인터넷에서 정보를 검색하고 종합할 수 있으며, MCP 커넥터를 통해 귀하의 자체 데이터를 액세스하고 활용할 수도 있습니다."}, "o3-mini": {"description": "o3-mini는 최신 소형 추론 모델로, o1-mini와 동일한 비용과 지연 목표에서 높은 지능을 제공합니다."}, "o3-pro": {"description": "o3-pro 모델은 더 많은 계산을 사용하여 더 깊이 사고하고 항상 더 나은 답변을 제공하며, Responses API에서만 사용 가능합니다."}, "o4-mini": {"description": "o4-mini는 최신 소형 o 시리즈 모델로, 빠르고 효율적인 추론을 위해 최적화되어 있으며, 코딩 및 시각적 작업에서 매우 높은 효율성과 성능을 자랑합니다."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research는 더 빠르고 경제적인 심층 연구 모델로, 복잡한 다단계 연구 작업에 매우 적합합니다. 인터넷에서 정보를 검색하고 종합할 수 있으며, MCP 커넥터를 통해 귀하의 자체 데이터를 액세스하고 활용할 수도 있습니다."}, "open-codestral-mamba": {"description": "Codestral Mamba는 코드 생성을 전문으로 하는 Mamba 2 언어 모델로, 고급 코드 및 추론 작업에 강력한 지원을 제공합니다."}, "open-mistral-7b": {"description": "Mistral 7B는 컴팩트하지만 고성능 모델로, 분류 및 텍스트 생성과 같은 간단한 작업 및 배치 처리에 능숙하며, 우수한 추론 능력을 갖추고 있습니다."}, "open-mistral-nemo": {"description": "Mistral Nemo는 NVIDIA와 협력하여 개발된 12B 모델로, 뛰어난 추론 및 인코딩 성능을 제공하며, 통합 및 교체가 용이합니다."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B는 더 큰 전문가 모델로, 복잡한 작업에 중점을 두고 뛰어난 추론 능력과 더 높은 처리량을 제공합니다."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B는 희소 전문가 모델로, 여러 매개변수를 활용하여 추론 속도를 높이며, 다국어 및 코드 생성 작업 처리에 적합합니다."}, "openai/gpt-4.1": {"description": "GPT-4.1은 복잡한 작업을 위한 우리의 플래그십 모델입니다. 다양한 분야의 문제를 해결하는 데 매우 적합합니다."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini는 지능, 속도 및 비용 간의 균형을 제공하여 많은 사용 사례에서 매력적인 모델이 됩니다."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano는 가장 빠르고 비용 효율적인 GPT-4.1 모델입니다."}, "openai/gpt-4o": {"description": "ChatGPT-4o는 동적 모델로, 최신 버전을 유지하기 위해 실시간으로 업데이트됩니다. 강력한 언어 이해 및 생성 능력을 결합하여 고객 서비스, 교육 및 기술 지원을 포함한 대규모 응용 프로그램에 적합합니다."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini는 OpenAI가 GPT-4 Omni 이후에 출시한 최신 모델로, 이미지와 텍스트 입력을 지원하며 텍스트를 출력합니다. 가장 진보된 소형 모델로, 최근의 다른 최첨단 모델보다 훨씬 저렴하며, GPT-3.5 Turbo보다 60% 이상 저렴합니다. 최첨단 지능을 유지하면서도 뛰어난 가성비를 자랑합니다. GPT-4o mini는 MMLU 테스트에서 82%의 점수를 기록했으며, 현재 채팅 선호도에서 GPT-4보다 높은 순위를 차지하고 있습니다."}, "openai/o1": {"description": "o1은 OpenAI의 새로운 추론 모델로, 이미지와 텍스트 입력을 지원하며 텍스트를 출력합니다. 광범위한 일반 지식이 필요한 복잡한 작업에 적합합니다. 이 모델은 20만 토큰의 컨텍스트와 2023년 10월 기준 지식을 보유하고 있습니다."}, "openai/o1-mini": {"description": "o1-mini는 프로그래밍, 수학 및 과학 응용 프로그램을 위해 설계된 빠르고 경제적인 추론 모델입니다. 이 모델은 128K의 컨텍스트와 2023년 10월의 지식 기준일을 가지고 있습니다."}, "openai/o1-preview": {"description": "o1은 OpenAI의 새로운 추론 모델로, 광범위한 일반 지식이 필요한 복잡한 작업에 적합합니다. 이 모델은 128K의 컨텍스트와 2023년 10월의 지식 기준일을 가지고 있습니다."}, "openai/o3": {"description": "o3는 다재다능하고 강력한 모델로, 여러 분야에서 뛰어난 성능을 발휘합니다. 수학, 과학, 프로그래밍 및 시각적 추론 작업에 대한 새로운 기준을 설정했습니다. 기술 작문 및 지시 준수에도 능숙합니다. 사용자는 이를 통해 텍스트, 코드 및 이미지를 분석하고, 다단계 복잡한 문제를 해결할 수 있습니다."}, "openai/o3-mini": {"description": "o3-mini는 o1-mini와 동일한 비용 및 지연 목표에서 높은 지능을 제공합니다."}, "openai/o3-mini-high": {"description": "o3-mini 고급 추론 버전은 o1-mini와 동일한 비용 및 지연 목표에서 높은 지능을 제공합니다."}, "openai/o4-mini": {"description": "o4-mini는 빠르고 효율적인 추론을 위해 최적화되어 있으며, 코딩 및 시각적 작업에서 매우 높은 효율성과 성능을 자랑합니다."}, "openai/o4-mini-high": {"description": "o4-mini 고급 추론 버전으로, 빠르고 효율적인 추론을 위해 최적화되어 있으며, 코딩 및 시각적 작업에서 매우 높은 효율성과 성능을 자랑합니다."}, "openrouter/auto": {"description": "요청은 컨텍스트 길이, 주제 및 복잡성에 따라 Llama 3 70B Instruct, Claude 3.5 Sonnet(자기 조정) 또는 GPT-4o로 전송됩니다."}, "phi3": {"description": "Phi-3는 Microsoft에서 출시한 경량 오픈 모델로, 효율적인 통합 및 대규모 지식 추론에 적합합니다."}, "phi3:14b": {"description": "Phi-3는 Microsoft에서 출시한 경량 오픈 모델로, 효율적인 통합 및 대규모 지식 추론에 적합합니다."}, "pixtral-12b-2409": {"description": "Pixtral 모델은 차트 및 이미지 이해, 문서 질문 응답, 다중 모드 추론 및 지시 준수와 같은 작업에서 강력한 능력을 발휘하며, 자연 해상도와 가로 세로 비율로 이미지를 입력할 수 있고, 최대 128K 토큰의 긴 컨텍스트 창에서 임의의 수의 이미지를 처리할 수 있습니다."}, "pixtral-large-latest": {"description": "Pixtral Large는 1240억 개의 매개변수를 가진 오픈 소스 다중 모달 모델로, Mistral Large 2를 기반으로 구축되었습니다. 이는 우리의 다중 모달 가족 중 두 번째 모델로, 최첨단 수준의 이미지 이해 능력을 보여줍니다."}, "pro-128k": {"description": "Spark Pro 128K는 매우 큰 컨텍스트 처리 능력을 갖추고 있으며, 최대 128K의 컨텍스트 정보를 처리할 수 있습니다. 특히 전체 분석 및 장기 논리 연관 처리가 필요한 긴 문서 콘텐츠에 적합하며, 복잡한 텍스트 커뮤니케이션에서 매끄럽고 일관된 논리와 다양한 인용 지원을 제공합니다."}, "qvq-72b-preview": {"description": "QVQ 모델은 Qwen 팀이 개발한 실험적 연구 모델로, 시각적 추론 능력 향상에 중점을 두고 있으며, 특히 수학적 추론 분야에서 두드러진 성과를 보입니다."}, "qvq-max": {"description": "통의천문 QVQ 비전 추론 모델로, 시각 입력과 사고 과정(chain-of-thought) 출력을 지원하며, 수학, 프로그래밍, 시각 분석, 창작 및 일반 작업에서 뛰어난 능력을 발휘합니다."}, "qvq-plus": {"description": "시각 추론 모델입니다. 시각 입력과 사고 체인 출력을 지원하며, qvq-max 모델에 이어 출시된 플러스 버전으로, qvq-max 모델에 비해 추론 속도가 더 빠르고 성능과 비용의 균형이 우수합니다."}, "qwen-coder-plus": {"description": "통의천문 코드 모델입니다."}, "qwen-coder-turbo": {"description": "통의천문 코드 모델입니다."}, "qwen-coder-turbo-latest": {"description": "통의 천문 코드 모델입니다."}, "qwen-long": {"description": "통의천문 초대규모 언어 모델로, 긴 텍스트 컨텍스트를 지원하며, 긴 문서 및 다수의 문서에 기반한 대화 기능을 제공합니다."}, "qwen-math-plus": {"description": "통의천문 수학 모델로, 수학 문제 해결에 특화된 언어 모델입니다."}, "qwen-math-plus-latest": {"description": "통의 천문 수학 모델은 수학 문제 해결을 위해 특별히 설계된 언어 모델입니다."}, "qwen-math-turbo": {"description": "통의천문 수학 모델로, 수학 문제 해결에 특화된 언어 모델입니다."}, "qwen-math-turbo-latest": {"description": "통의 천문 수학 모델은 수학 문제 해결을 위해 특별히 설계된 언어 모델입니다."}, "qwen-max": {"description": "통의천문 천억 수준 초대형 언어 모델로, 중국어, 영어 등 다양한 언어 입력을 지원하며, 현재 통의천문 2.5 제품 버전 뒤의 API 모델입니다."}, "qwen-omni-turbo": {"description": "Qwen-Omni 시리즈 모델은 비디오, 오디오, 이미지, 텍스트 등 다양한 모달리티 입력을 지원하며, 오디오와 텍스트 출력을 제공합니다."}, "qwen-plus": {"description": "통의천문 초대형 언어 모델의 강화 버전으로, 중국어, 영어 등 다양한 언어 입력을 지원합니다."}, "qwen-turbo": {"description": "통의천문 초대형 언어 모델로, 중국어, 영어 등 다양한 언어 입력을 지원합니다."}, "qwen-vl-chat-v1": {"description": "통의천문 VL은 다중 이미지, 다중 회차 질문 응답, 창작 등 유연한 상호작용 방식을 지원하는 모델입니다."}, "qwen-vl-max": {"description": "통의천문 초대규모 비전-언어 모델로, 강화판에 비해 시각 추론 능력과 명령 준수 능력을 다시 한 번 향상시켜 더 높은 시각 인지 및 인식 수준을 제공합니다."}, "qwen-vl-max-latest": {"description": "통의천문 초대규모 비주얼 언어 모델. 강화판에 비해 시각적 추론 능력과 지시 준수 능력을 다시 한 번 향상시켜, 더 높은 시각적 인식과 인지 수준을 제공합니다."}, "qwen-vl-ocr": {"description": "통의천문 OCR은 문서, 표, 시험 문제, 손글씨 등 이미지 내 문자 추출에 특화된 전용 모델입니다. 중국어, 영어, 프랑스어, 일본어, 한국어, 독일어, 러시아어, 이탈리아어, 베트남어, 아랍어 등 다양한 언어를 인식할 수 있습니다."}, "qwen-vl-plus": {"description": "통의천문 대규모 비전-언어 모델 강화판으로, 세부 인식 능력과 문자 인식 능력을 크게 향상시켰으며, 백만 화소 이상의 해상도와 임의 비율의 이미지를 지원합니다."}, "qwen-vl-plus-latest": {"description": "통의천문 대규모 비주얼 언어 모델 강화판. 세부 사항 인식 능력과 문자 인식 능력을 크게 향상시켰으며, 백만 화소 이상의 해상도와 임의의 가로 세로 비율의 이미지를 지원합니다."}, "qwen-vl-v1": {"description": "Qwen-7B 언어 모델로 초기화된 모델로, 이미지 모델을 추가하여 이미지 입력 해상도가 448인 사전 훈련 모델입니다."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2는 새로운 Qwen 대형 언어 모델 시리즈입니다. Qwen2 7B는 트랜스포머 기반 모델로, 언어 이해, 다국어 능력, 프로그래밍, 수학 및 추론에서 뛰어난 성능을 보여줍니다."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2는 더 강력한 이해 및 생성 능력을 갖춘 새로운 대형 언어 모델 시리즈입니다."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL은 Qwen-VL 모델의 최신 반복 버전으로, MathVista, DocVQA, RealWorldQA 및 MTVQA와 같은 시각적 이해 벤치마크 테스트에서 최첨단 성능을 달성했습니다. Qwen2-VL은 20분 이상의 비디오를 이해할 수 있으며, 고품질의 비디오 기반 질문 응답, 대화 및 콘텐츠 생성에 사용됩니다. 또한 복잡한 추론 및 의사 결정 능력을 갖추고 있어, 모바일 장치, 로봇 등과 통합되어 시각적 환경 및 텍스트 지침에 따라 자동으로 작업을 수행할 수 있습니다. 영어와 중국어 외에도 Qwen2-VL은 이제 대부분의 유럽 언어, 일본어, 한국어, 아랍어 및 베트남어 등 다양한 언어의 텍스트를 이해할 수 있습니다."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct는 알리바바 클라우드에서 발표한 최신 대형 언어 모델 시리즈 중 하나입니다. 이 72B 모델은 코딩 및 수학 등 분야에서 현저한 개선된 능력을 가지고 있습니다. 이 모델은 또한 29개 이상의 언어를 포함한 다국어 지원을 제공하며, 지침 준수, 구조화된 데이터 이해 및 구조화된 출력 생성(특히 JSON)에서 현저한 향상을 보였습니다."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct는 알리바바 클라우드에서 발표한 최신 대형 언어 모델 시리즈 중 하나입니다. 이 32B 모델은 코딩 및 수학 등 분야에서 현저한 개선된 능력을 가지고 있습니다. 이 모델은 29개 이상의 언어를 포함한 다국어 지원을 제공하며, 지침 준수, 구조화된 데이터 이해 및 구조화된 출력 생성(특히 JSON)에서 현저한 향상을 보였습니다."}, "qwen/qwen2.5-7b-instruct": {"description": "중국어와 영어를 위한 LLM으로, 언어, 프로그래밍, 수학, 추론 등 다양한 분야를 다룹니다."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "코드 생성, 추론 및 수정 지원을 위한 고급 LLM으로, 주요 프로그래밍 언어를 포함합니다."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "32K 컨텍스트 길이를 지원하는 강력한 중형 코드 모델로, 다국어 프로그래밍에 능숙합니다."}, "qwen/qwen3-14b": {"description": "Qwen3-14B는 Qwen3 시리즈의 밀집형 148억 매개변수 인과 언어 모델로, 복잡한 추론과 효율적인 대화를 위해 설계되었습니다. 수학, 프로그래밍 및 논리 추론과 같은 작업을 위한 '사고' 모드와 일반 대화를 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 지침 준수, 에이전트 도구 사용, 창의적 글쓰기 및 100개 이상의 언어와 방언에서의 다국어 작업을 위해 미세 조정되었습니다. 기본적으로 32K 토큰 컨텍스트를 처리하며, YaRN 기반 확장을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B는 Qwen3 시리즈의 밀집형 148억 매개변수 인과 언어 모델로, 복잡한 추론과 효율적인 대화를 위해 설계되었습니다. 수학, 프로그래밍 및 논리 추론과 같은 작업을 위한 '사고' 모드와 일반 대화를 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 지침 준수, 에이전트 도구 사용, 창의적 글쓰기 및 100개 이상의 언어와 방언에서의 다국어 작업을 위해 미세 조정되었습니다. 기본적으로 32K 토큰 컨텍스트를 처리하며, YaRN 기반 확장을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B는 Qwen이 개발한 235B 매개변수 전문가 혼합(MoE) 모델로, 매번 전방 전달 시 22B 매개변수를 활성화합니다. 복잡한 추론, 수학 및 코드 작업을 위한 '사고' 모드와 일반 대화 효율을 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 강력한 추론 능력, 다국어 지원(100개 이상의 언어와 방언), 고급 지침 준수 및 에이전트 도구 호출 능력을 보여줍니다. 기본적으로 32K 토큰 컨텍스트 창을 처리하며, YaRN 기반 확장을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B는 Qwen이 개발한 235B 매개변수 전문가 혼합(MoE) 모델로, 매번 전방 전달 시 22B 매개변수를 활성화합니다. 복잡한 추론, 수학 및 코드 작업을 위한 '사고' 모드와 일반 대화 효율을 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 강력한 추론 능력, 다국어 지원(100개 이상의 언어와 방언), 고급 지침 준수 및 에이전트 도구 호출 능력을 보여줍니다. 기본적으로 32K 토큰 컨텍스트 창을 처리하며, YaRN 기반 확장을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3는 Qwen 대형 언어 모델 시리즈의 최신 세대로, 밀집 및 전문가 혼합(MoE) 아키텍처를 갖추고 있으며, 추론, 다국어 지원 및 고급 에이전트 작업에서 뛰어난 성능을 발휘합니다. 복잡한 추론의 사고 모드와 효율적인 대화의 비사고 모드 간의 원활한 전환 능력은 다기능적이고 고품질의 성능을 보장합니다.\n\nQwen3는 QwQ 및 Qwen2.5와 같은 이전 모델에 비해 현저하게 우수하며, 뛰어난 수학, 코딩, 상식 추론, 창의적 글쓰기 및 상호작용 대화 능력을 제공합니다. Qwen3-30B-A3B 변형은 305억 개의 매개변수(33억 개의 활성화 매개변수), 48층, 128명의 전문가(각 작업에 대해 8명 활성화)를 포함하며, 최대 131K 토큰 컨텍스트(YaRN 사용)를 지원하여 오픈 소스 모델의 새로운 기준을 설정합니다."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3는 Qwen 대형 언어 모델 시리즈의 최신 세대로, 밀집 및 전문가 혼합(MoE) 아키텍처를 갖추고 있으며, 추론, 다국어 지원 및 고급 에이전트 작업에서 뛰어난 성능을 발휘합니다. 복잡한 추론의 사고 모드와 효율적인 대화의 비사고 모드 간의 원활한 전환 능력은 다기능적이고 고품질의 성능을 보장합니다.\n\nQwen3는 QwQ 및 Qwen2.5와 같은 이전 모델에 비해 현저하게 우수하며, 뛰어난 수학, 코딩, 상식 추론, 창의적 글쓰기 및 상호작용 대화 능력을 제공합니다. Qwen3-30B-A3B 변형은 305억 개의 매개변수(33억 개의 활성화 매개변수), 48층, 128명의 전문가(각 작업에 대해 8명 활성화)를 포함하며, 최대 131K 토큰 컨텍스트(YaRN 사용)를 지원하여 오픈 소스 모델의 새로운 기준을 설정합니다."}, "qwen/qwen3-32b": {"description": "Qwen3-32B는 Qwen3 시리즈의 밀집형 328억 매개변수 인과 언어 모델로, 복잡한 추론과 효율적인 대화를 위해 최적화되었습니다. 수학, 코딩 및 논리 추론과 같은 작업을 위한 '사고' 모드와 더 빠르고 일반적인 대화를 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 지침 준수, 에이전트 도구 사용, 창의적 글쓰기 및 100개 이상의 언어와 방언에서의 다국어 작업에서 강력한 성능을 발휘합니다. 기본적으로 32K 토큰 컨텍스트를 처리하며, YaRN 기반 확장을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B는 Qwen3 시리즈의 밀집형 328억 매개변수 인과 언어 모델로, 복잡한 추론과 효율적인 대화를 위해 최적화되었습니다. 수학, 코딩 및 논리 추론과 같은 작업을 위한 '사고' 모드와 더 빠르고 일반적인 대화를 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 지침 준수, 에이전트 도구 사용, 창의적 글쓰기 및 100개 이상의 언어와 방언에서의 다국어 작업에서 강력한 성능을 발휘합니다. 기본적으로 32K 토큰 컨텍스트를 처리하며, YaRN 기반 확장을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B는 Qwen3 시리즈의 밀집형 82억 매개변수 인과 언어 모델로, 추론 집약적인 작업과 효율적인 대화를 위해 설계되었습니다. 수학, 코딩 및 논리 추론을 위한 '사고' 모드와 일반 대화를 위한 '비사고' 모드 간의 원활한 전환을 지원합니다. 이 모델은 지침 준수, 에이전트 통합, 창의적 글쓰기 및 100개 이상의 언어와 방언에서의 다국어 사용을 위해 미세 조정되었습니다. 기본적으로 32K 토큰 컨텍스트 창을 지원하며, YaRN을 통해 131K 토큰으로 확장할 수 있습니다."}, "qwen2": {"description": "Qwen2는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2-72b-instruct": {"description": "Qwen2는 Qwen 팀이 출시한 새로운 대형 언어 모델 시리즈입니다. 이 모델은 Transformer 아키텍처를 기반으로 하며, SwiGLU 활성화 함수, 주의 QKV 편향(attention QKV bias), 그룹 쿼리 주의(group query attention), 슬라이딩 윈도우 주의와 전체 주의의 혼합(mixture of sliding window attention and full attention) 등의 기술을 채택하고 있습니다. 또한, <PERSON><PERSON> 팀은 다양한 자연어와 코드에 적합한 토크나이저를 개선했습니다."}, "qwen2-7b-instruct": {"description": "Qwen2는 Qwen 팀이 출시한 새로운 대형 언어 모델 시리즈입니다. 이 모델은 Transformer 아키텍처를 기반으로 하며, SwiGLU 활성화 함수, 주의 QKV 편향(attention QKV bias), 그룹 쿼리 주의(group query attention), 슬라이딩 윈도우 주의와 전체 주의의 혼합(mixture of sliding window attention and full attention) 등의 기술을 채택하고 있습니다. 또한, <PERSON><PERSON> 팀은 다양한 자연어와 코드에 적합한 토크나이저를 개선했습니다."}, "qwen2.5": {"description": "Qwen2.5는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2.5-14b-instruct": {"description": "통의 천문 2.5 외부 오픈 소스 14B 규모 모델입니다."}, "qwen2.5-14b-instruct-1m": {"description": "통의천문2.5의 외부 오픈 소스 72B 규모 모델입니다."}, "qwen2.5-32b-instruct": {"description": "통의 천문 2.5 외부 오픈 소스 32B 규모 모델입니다."}, "qwen2.5-72b-instruct": {"description": "통의 천문 2.5 외부 오픈 소스 72B 규모 모델입니다."}, "qwen2.5-7b-instruct": {"description": "통의 천문 2.5 외부 오픈 소스 7B 규모 모델입니다."}, "qwen2.5-coder-1.5b-instruct": {"description": "통의천문 코드 모델 오픈 소스 버전입니다."}, "qwen2.5-coder-14b-instruct": {"description": "통의천문 코드 모델 오픈소스 버전입니다."}, "qwen2.5-coder-32b-instruct": {"description": "통의 천문 코드 모델 오픈 소스 버전입니다."}, "qwen2.5-coder-7b-instruct": {"description": "통의 천문 코드 모델 오픈 소스 버전입니다."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder는 Qwen 시리즈의 최신 코드 전용 대규모 언어 모델입니다(이전 명칭: Code<PERSON>wen)."}, "qwen2.5-instruct": {"description": "Qwen2.5은 Qwen 대형 언어 모델의 최신 시리즈입니다. Qwen2.5을 위해 우리는 5억에서 72억에 이르는 다양한 파라미터 범위의 기본 언어 모델과 지침 미세 조정 언어 모델을 출시했습니다."}, "qwen2.5-math-1.5b-instruct": {"description": "<PERSON><PERSON>-Math 모델은 강력한 수학 문제 해결 능력을 갖추고 있습니다."}, "qwen2.5-math-72b-instruct": {"description": "<PERSON><PERSON>-Math 모델은 강력한 수학 문제 해결 능력을 가지고 있습니다."}, "qwen2.5-math-7b-instruct": {"description": "<PERSON><PERSON>-Math 모델은 강력한 수학 문제 해결 능력을 가지고 있습니다."}, "qwen2.5-omni-7b": {"description": "Qwen-Omni 시리즈 모델은 비디오, 오디오, 이미지, 텍스트 등 다양한 모드의 데이터를 입력으로 지원하며, 오디오와 텍스트를 출력합니다."}, "qwen2.5-vl-32b-instruct": {"description": "Qwen2.5-VL 시리즈 모델은 모델의 지능 수준, 실용성 및 적용성을 향상시켜 자연스러운 대화, 콘텐츠 제작, 전문 지식 서비스 및 코드 개발 등 다양한 시나리오에서 더 나은 성능을 발휘합니다. 32B 버전은 강화 학습 기술을 활용하여 최적화되었으며, Qwen2.5 VL 시리즈의 다른 모델들과 비교하여 인간의 선호도에 더 부합하는 출력 스타일, 복잡한 수학 문제의 추론 능력, 그리고 이미지의 세밀한 이해와 추론 능력을 제공합니다."}, "qwen2.5-vl-72b-instruct": {"description": "지시 따르기, 수학, 문제 해결, 코드 전반적인 향상, 모든 사물 인식 능력 향상, 다양한 형식의 시각적 요소를 직접 정확하게 위치 지정할 수 있으며, 최대 10분 길이의 긴 비디오 파일을 이해하고 초 단위의 사건 시점을 위치 지정할 수 있습니다. 시간의 선후와 속도를 이해할 수 있으며, 분석 및 위치 지정 능력을 기반으로 OS 또는 모바일 에이전트를 조작할 수 있습니다. 주요 정보 추출 능력과 Json 형식 출력 능력이 뛰어나며, 이 버전은 72B 버전으로, 이 시리즈에서 가장 강력한 버전입니다."}, "qwen2.5-vl-7b-instruct": {"description": "지시 따르기, 수학, 문제 해결, 코드 전반적인 향상, 모든 사물 인식 능력 향상, 다양한 형식의 시각적 요소를 직접 정확하게 위치 지정할 수 있으며, 최대 10분 길이의 긴 비디오 파일을 이해하고 초 단위의 사건 시점을 위치 지정할 수 있습니다. 시간의 선후와 속도를 이해할 수 있으며, 분석 및 위치 지정 능력을 기반으로 OS 또는 모바일 에이전트를 조작할 수 있습니다. 주요 정보 추출 능력과 Json 형식 출력 능력이 뛰어나며, 이 버전은 72B 버전으로, 이 시리즈에서 가장 강력한 버전입니다."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL은 Qwen 모델 패밀리의 최신 버전 시각 언어 모델입니다."}, "qwen2.5:0.5b": {"description": "Qwen2.5는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2.5:1.5b": {"description": "Qwen2.5는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2.5:72b": {"description": "Qwen2.5는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2:0.5b": {"description": "Qwen2는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2:1.5b": {"description": "Qwen2는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen2:72b": {"description": "Qwen2는 Alibaba의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen3": {"description": "Qwen3는 알리바바의 차세대 대규모 언어 모델로, 뛰어난 성능으로 다양한 응용 요구를 지원합니다."}, "qwen3-0.6b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-1.7b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-14b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-235b-a22b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-235b-a22b-instruct-2507": {"description": "Qwen3 기반 비사고 모드 오픈 소스 모델로, 이전 버전(통의천문3-235B-A22B) 대비 주관적 창작 능력과 모델 안전성이 소폭 향상되었습니다."}, "qwen3-235b-a22b-thinking-2507": {"description": "Qwen3 기반 사고 모드 오픈 소스 모델로, 이전 버전(통의천문3-235B-A22B) 대비 논리 능력, 범용 능력, 지식 강화 및 창작 능력이 크게 향상되어 고난도 강추론 시나리오에 적합합니다."}, "qwen3-30b-a3b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-30b-a3b-instruct-2507": {"description": "이전 버전(Qwen3-30B-A3B) 대비 중영 및 다국어 전반적인 일반 능력이 크게 향상되었습니다. 주관적이고 개방형 작업에 특화된 최적화로 사용자 선호에 훨씬 더 부합하며, 보다 유용한 응답을 제공할 수 있습니다."}, "qwen3-30b-a3b-thinking-2507": {"description": "Qwen3 기반 사고 모드 오픈소스 모델로, 이전 버전(通义千问3-30B-A3B) 대비 논리 능력, 일반 능력, 지식 강화 및 창작 능력이 크게 향상되어 고난도 강력 추론 시나리오에 적합합니다."}, "qwen3-32b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-4b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-8b": {"description": "Qwen3는 능력이 대폭 향상된 새로운 세대의 통합 지식 모델로, 추론, 일반, 에이전트 및 다국어 등 여러 핵심 능력에서 업계 선두 수준에 도달하며, 사고 모드 전환을 지원합니다."}, "qwen3-coder-480b-a35b-instruct": {"description": "통의천문 코드 모델 오픈 소스 버전입니다. 최신 qwen3-coder-480b-a35b-instruct는 Qwen3 기반 코드 생성 모델로, 강력한 코딩 에이전트 능력을 갖추고 도구 호출 및 환경 상호작용에 능하며, 자율 프로그래밍과 뛰어난 코드 능력 및 범용 능력을 동시에 구현합니다."}, "qwen3-coder-plus": {"description": "통의천문 코드 모델입니다. 최신 Qwen3-Coder-Plus 시리즈 모델은 Qwen3 기반 코드 생성 모델로, 강력한 코딩 에이전트 능력을 갖추고 도구 호출 및 환경 상호작용에 능하며, 자율 프로그래밍과 뛰어난 코드 능력 및 범용 능력을 동시에 구현합니다."}, "qwq": {"description": "QwQ는 AI 추론 능력을 향상시키는 데 중점을 둔 실험 연구 모델입니다."}, "qwq-32b": {"description": "Qwen2.5-32B 모델을 기반으로 훈련된 QwQ 추론 모델로, 강화 학습을 통해 모델의 추론 능력을 크게 향상시켰습니다. 모델의 수학 코드 등 핵심 지표(AIME 24/25, LiveCodeBench) 및 일부 일반 지표(IFEval, LiveBench 등)는 DeepSeek-R1 풀 버전 수준에 도달했으며, 모든 지표는 동일하게 Qwen2.5-32B를 기반으로 한 DeepSeek-R1-Distill-Qwen-32B를 크게 초과합니다."}, "qwq-32b-preview": {"description": "QwQ 모델은 Qwen 팀이 개발한 실험적 연구 모델로, AI 추론 능력을 향상시키는 데 중점을 두고 있습니다."}, "qwq-plus": {"description": "Qwen2.5 모델을 기반으로 훈련된 QwQ 추론 모델로, 강화 학습을 통해 모델 추론 능력을 대폭 향상시켰습니다. 수학, 코드 등 핵심 지표(AIME 24/25, LiveCodeBench)와 일부 일반 지표(IFEval, LiveBench 등)에서 DeepSeek-R1 풀스펙 수준에 도달했습니다."}, "qwq_32b": {"description": "<PERSON>wen 시리즈의 중간 규모 추론 모델입니다. 전통적인 지시 조정 모델에 비해 사고 및 추론 능력을 갖춘 QwQ는 하위 작업에서, 특히 문제 해결 시 성능을 크게 향상시킬 수 있습니다."}, "r1-1776": {"description": "R1-1776은 DeepSeek R1 모델의 한 버전으로, 후속 훈련을 거쳐 검토되지 않은 편향 없는 사실 정보를 제공합니다."}, "solar-mini": {"description": "Solar Mini는 컴팩트한 LLM으로, GPT-3.5보다 성능이 우수하며, 강력한 다국어 능력을 갖추고 있어 영어와 한국어를 지원하며, 효율적이고 소형의 솔루션을 제공합니다."}, "solar-mini-ja": {"description": "Solar Mini (Ja)는 Solar Mini의 능력을 확장하여 일본어에 집중하며, 영어와 한국어 사용에서도 효율적이고 뛰어난 성능을 유지합니다."}, "solar-pro": {"description": "Solar Pro는 Upstage에서 출시한 고지능 LLM으로, 단일 GPU의 지시 추적 능력에 중점을 두고 있으며, IFEval 점수가 80 이상입니다. 현재 영어를 지원하며, 정식 버전은 2024년 11월에 출시될 예정이며, 언어 지원 및 컨텍스트 길이를 확장할 계획입니다."}, "sonar": {"description": "검색 맥락 기반의 경량 검색 제품으로, Sonar Pro보다 더 빠르고 저렴합니다."}, "sonar-deep-research": {"description": "Deep Research는 포괄적인 전문가 수준의 연구를 수행하고 이를 접근 가능하고 실행 가능한 보고서로 통합합니다."}, "sonar-pro": {"description": "고급 쿼리 및 후속 작업을 지원하는 검색 맥락 기반의 고급 검색 제품입니다."}, "sonar-reasoning": {"description": "DeepSeek 추론 모델이 지원하는 새로운 API 제품입니다."}, "sonar-reasoning-pro": {"description": "DeepSeek 추론 모델이 지원하는 새로운 API 제품입니다."}, "stable-diffusion-3-medium": {"description": "Stability AI가 출시한 최신 텍스트-이미지 대형 모델입니다. 이전 버전의 장점을 계승하면서 이미지 품질, 텍스트 이해 및 스타일 다양성 측면에서 크게 개선되어 복잡한 자연어 프롬프트를 더 정확히 해석하고 더욱 정밀하고 다양한 이미지를 생성할 수 있습니다."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large는 8억 파라미터를 가진 다중 모달 확산 변환기(MMDiT) 텍스트-이미지 생성 모델로, 뛰어난 이미지 품질과 프롬프트 일치도를 갖추고 있습니다. 최대 100만 픽셀의 고해상도 이미지 생성을 지원하며, 일반 소비자용 하드웨어에서도 효율적으로 작동합니다."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo는 stable-diffusion-3.5-large를 기반으로 적대적 확산 증류(ADD) 기술을 적용한 모델로, 더 빠른 속도를 자랑합니다."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5는 stable-diffusion-v1.2 체크포인트 가중치를 초기화하고 \"laion-aesthetics v2 5+\" 데이터셋에서 512x512 해상도로 595k 스텝 미세 조정을 거쳤으며, 텍스트 조건화를 10% 줄여 분류기 없는 가이드 샘플링을 향상시켰습니다."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl은 v1.5 대비 대대적인 개선이 이루어졌으며, 현재 공개된 텍스트-이미지 SOTA 모델인 midjourney와 유사한 성능을 보입니다. 주요 개선점은 더 큰 unet 백본(기존 대비 3배), 생성 이미지 품질 향상을 위한 정제 모듈 추가, 더 효율적인 훈련 기법 등입니다."}, "stable-diffusion-xl-base-1.0": {"description": "Stability AI가 개발하고 오픈 소스로 공개한 텍스트-이미지 대형 모델로, 업계 선두 수준의 창의적 이미지 생성 능력을 갖추고 있습니다. 뛰어난 명령 이해 능력을 보유하며, 역방향 프롬프트 정의를 지원해 정확한 콘텐츠 생성을 가능하게 합니다."}, "step-1-128k": {"description": "성능과 비용의 균형을 맞추어 일반적인 시나리오에 적합합니다."}, "step-1-256k": {"description": "초장기 컨텍스트 처리 능력을 갖추고 있으며, 특히 긴 문서 분석에 적합합니다."}, "step-1-32k": {"description": "중간 길이의 대화를 지원하며, 다양한 응용 시나리오에 적합합니다."}, "step-1-8k": {"description": "소형 모델로, 경량 작업에 적합합니다."}, "step-1-flash": {"description": "고속 모델로, 실시간 대화에 적합합니다."}, "step-1.5v-mini": {"description": "이 모델은 강력한 비디오 이해 능력을 가지고 있습니다."}, "step-1o-turbo-vision": {"description": "이 모델은 강력한 이미지 이해 능력을 가지고 있으며, 수리 및 코드 분야에서 1o보다 우수합니다. 모델은 1o보다 더 작고, 출력 속도가 더 빠릅니다."}, "step-1o-vision-32k": {"description": "이 모델은 강력한 이미지 이해 능력을 가지고 있습니다. step-1v 시리즈 모델에 비해 더 강력한 시각 성능을 자랑합니다."}, "step-1v-32k": {"description": "시각 입력을 지원하여 다중 모달 상호작용 경험을 강화합니다."}, "step-1v-8k": {"description": "소형 비주얼 모델로, 기본적인 텍스트 및 이미지 작업에 적합합니다."}, "step-1x-edit": {"description": "이 모델은 이미지 편집 작업에 특화되어 있으며, 사용자가 제공한 이미지와 텍스트 설명에 따라 이미지를 수정 및 향상시킬 수 있습니다. 텍스트 설명과 예시 이미지 등 다양한 입력 형식을 지원하며, 사용자의 의도를 이해하고 요구에 부합하는 이미지 편집 결과를 생성합니다."}, "step-1x-medium": {"description": "이 모델은 강력한 이미지 생성 능력을 갖추고 있으며, 텍스트 설명을 입력으로 지원합니다. 기본적으로 중국어를 지원하여 중국어 텍스트 설명을 더 잘 이해하고 처리할 수 있으며, 텍스트 설명의 의미를 정확히 포착해 이미지 특징으로 변환하여 보다 정밀한 이미지 생성을 실현합니다. 입력에 따라 고해상도, 고품질 이미지를 생성하며, 일정 수준의 스타일 전이 능력도 갖추고 있습니다."}, "step-2-16k": {"description": "대규모 컨텍스트 상호작용을 지원하며, 복잡한 대화 시나리오에 적합합니다."}, "step-2-16k-exp": {"description": "step-2 모델의 실험 버전으로, 최신 기능이 포함되어 있으며, 지속적으로 업데이트됩니다. 공식 생산 환경에서 사용을 권장하지 않습니다."}, "step-2-mini": {"description": "신세대 자체 개발 Attention 아키텍처인 MFA를 기반으로 한 초고속 대형 모델로, 매우 낮은 비용으로 step1과 유사한 효과를 달성하면서도 더 높은 처리량과 더 빠른 응답 지연을 유지합니다. 일반적인 작업을 처리할 수 있으며, 코드 능력에 있어 특장점을 가지고 있습니다."}, "step-2x-large": {"description": "계단별 신성(阶跃星辰) 차세대 이미지 생성 모델로, 텍스트 설명에 따라 고품질 이미지를 생성하는 데 특화되어 있습니다. 새 모델은 이미지 질감이 더욱 사실적이며, 중영문 텍스트 생성 능력이 강화되었습니다."}, "step-r1-v-mini": {"description": "이 모델은 강력한 이미지 이해 능력을 갖춘 추론 대모델로, 이미지와 텍스트 정보를 처리하며, 깊은 사고 후 텍스트를 생성합니다. 이 모델은 시각적 추론 분야에서 두드러진 성능을 보이며, 1차 대열의 수학, 코드, 텍스트 추론 능력을 갖추고 있습니다. 문맥 길이는 100k입니다."}, "taichu_llm": {"description": "자이동 태초 언어 대모델은 뛰어난 언어 이해 능력과 텍스트 창작, 지식 질문 응답, 코드 프로그래밍, 수학 계산, 논리 추론, 감정 분석, 텍스트 요약 등의 능력을 갖추고 있습니다. 혁신적으로 대규모 데이터 사전 훈련과 다원적 풍부한 지식을 결합하여 알고리즘 기술을 지속적으로 다듬고, 방대한 텍스트 데이터에서 어휘, 구조, 문법, 의미 등의 새로운 지식을 지속적으로 흡수하여 모델 성능을 지속적으로 진화시킵니다. 사용자에게 보다 편리한 정보와 서비스, 그리고 더 지능적인 경험을 제공합니다."}, "taichu_o1": {"description": "taichu_o1은 차세대 추론 대모델로, 다중 모드 상호작용과 강화 학습을 통해 인간과 유사한 사고 체계를 구현하며, 복잡한 의사결정 시나리오를 지원합니다. 높은 정확도의 출력을 유지하면서도 모델 추론의 사고 경로를 보여주며, 전략 분석 및 깊은 사고와 같은 시나리오에 적합합니다."}, "taichu_vl": {"description": "이미지 이해, 지식 이전, 논리 귀속 등의 능력을 통합하여, 이미지-텍스트 질문 응답 분야에서 뛰어난 성능을 보입니다."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct는 800억 개의 매개변수를 보유하며, 130억 개의 활성화 매개변수만으로 더 큰 모델과 견줄 수 있습니다. '빠른 사고/느린 사고' 혼합 추론을 지원하며, 긴 문서 이해가 안정적입니다. BFCL-v3와 τ-Bench 검증을 통해 에이전트 능력이 선도적임을 입증했으며, GQA와 다중 양자화 포맷을 결합해 효율적인 추론을 실현합니다."}, "text-embedding-3-large": {"description": "가장 강력한 벡터화 모델로, 영어 및 비영어 작업에 적합합니다."}, "text-embedding-3-small": {"description": "효율적이고 경제적인 차세대 임베딩 모델로, 지식 검색, RAG 애플리케이션 등 다양한 상황에 적합합니다."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414는 32B 이중 언어(중국어 및 영어) 오픈 가중치 언어 모델로, 코드 생성, 함수 호출 및 에이전트 기반 작업에 최적화되어 있습니다. 15T의 고품질 및 재추론 데이터로 사전 훈련되었으며, 인간 선호 정렬, 거부 샘플링 및 강화 학습을 통해 추가적으로 개선되었습니다. 이 모델은 복잡한 추론, 아티팩트 생성 및 구조적 출력 작업에서 뛰어난 성능을 보이며, 여러 벤치마크 테스트에서 GPT-4o 및 DeepSeek-V3-0324와 동등한 성능을 달성했습니다."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414는 32B 이중 언어(중국어 및 영어) 오픈 가중치 언어 모델로, 코드 생성, 함수 호출 및 에이전트 기반 작업에 최적화되어 있습니다. 15T의 고품질 및 재추론 데이터로 사전 훈련되었으며, 인간 선호 정렬, 거부 샘플링 및 강화 학습을 통해 추가적으로 개선되었습니다. 이 모델은 복잡한 추론, 아티팩트 생성 및 구조적 출력 작업에서 뛰어난 성능을 보이며, 여러 벤치마크 테스트에서 GPT-4o 및 DeepSeek-V3-0324와 동등한 성능을 달성했습니다."}, "thudm/glm-4-9b-chat": {"description": "지프 AI가 발표한 GLM-4 시리즈 최신 세대의 사전 훈련 모델의 오픈 소스 버전입니다."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414는 THUDM이 개발한 GLM-4 시리즈의 90억 매개변수 언어 모델입니다. GLM-4-9B-0414는 더 큰 32B 대응 모델과 동일한 강화 학습 및 정렬 전략을 사용하여 훈련되었으며, 그 규모에 비해 높은 성능을 달성하여 여전히 강력한 언어 이해 및 생성 능력이 필요한 자원 제한 배포에 적합합니다."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414는 GLM-4-32B의 향상된 추론 변형으로, 깊은 수학, 논리 및 코드 중심 문제 해결을 위해 설계되었습니다. 이 모델은 복잡한 다단계 작업의 성능을 향상시키기 위해 확장 강화 학습(작업 특정 및 일반 쌍 선호 기반)을 적용합니다. 기본 GLM-4-32B 모델에 비해 Z1은 구조적 추론 및 형식적 분야의 능력을 크게 향상시킵니다.\n\n이 모델은 프롬프트 엔지니어링을 통해 '사고' 단계를 강제 실행할 수 있으며, 긴 형식 출력에 대한 개선된 일관성을 제공합니다. 에이전트 워크플로우에 최적화되어 있으며, 긴 맥락(YaRN을 통해), JSON 도구 호출 및 안정적인 추론을 위한 세분화된 샘플링 구성을 지원합니다. 깊이 있는 사고, 다단계 추론 또는 형식적 유도가 필요한 사용 사례에 매우 적합합니다."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414는 GLM-4-32B의 향상된 추론 변형으로, 깊은 수학, 논리 및 코드 중심 문제 해결을 위해 설계되었습니다. 이 모델은 복잡한 다단계 작업의 성능을 향상시키기 위해 확장 강화 학습(작업 특정 및 일반 쌍 선호 기반)을 적용합니다. 기본 GLM-4-32B 모델에 비해 Z1은 구조적 추론 및 형식적 분야의 능력을 크게 향상시킵니다.\n\n이 모델은 프롬프트 엔지니어링을 통해 '사고' 단계를 강제 실행할 수 있으며, 긴 형식 출력에 대한 개선된 일관성을 제공합니다. 에이전트 워크플로우에 최적화되어 있으며, 긴 맥락(YaRN을 통해), JSON 도구 호출 및 안정적인 추론을 위한 세분화된 샘플링 구성을 지원합니다. 깊이 있는 사고, 다단계 추론 또는 형식적 유도가 필요한 사용 사례에 매우 적합합니다."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414는 THUDM이 개발한 GLM-4 시리즈의 9B 매개변수 언어 모델입니다. 이 모델은 더 큰 GLM-Z1 모델에 처음 적용된 기술을 포함하여, 확장된 강화 학습, 쌍 순위 정렬 및 수학, 코드 및 논리와 같은 추론 집약적인 작업에 대한 훈련을 포함합니다. 비록 규모는 작지만, 일반 추론 작업에서 강력한 성능을 발휘하며, 많은 오픈 소스 모델보다 우수한 성능을 보입니다."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B는 GLM-4-Z1 시리즈의 32B 매개변수 심층 추론 모델로, 오랜 시간 동안 사고가 필요한 복잡하고 개방적인 작업을 위해 최적화되었습니다. 이 모델은 glm-4-32b-0414를 기반으로 하며, 추가적인 강화 학습 단계와 다단계 정렬 전략을 도입하여 확장된 인지 처리를 모방하는 '반성' 능력을 도입합니다. 여기에는 반복 추론, 다중 점 분석 및 검색, 검색 및 인용 인식 합성을 포함한 도구 강화 워크플로우가 포함됩니다.\n\n이 모델은 연구 기반 글쓰기, 비교 분석 및 복잡한 질문 응답에서 뛰어난 성능을 발휘합니다. 검색 및 탐색 원시(`search`, `click`, `open`, `finish`)를 위한 함수 호출을 지원하여 에이전트 기반 파이프라인에서 사용할 수 있습니다. 반성 행동은 규칙 기반 보상 및 지연 결정 메커니즘을 갖춘 다중 회전 제어에 의해 형성되며, OpenAI 내부 정렬 스택과 같은 심층 연구 프레임워크를 기준으로 합니다. 이 변형은 깊이가 필요하고 속도가 필요하지 않은 시나리오에 적합합니다."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera는 DeepSeek-R1과 DeepSeek-V3(0324)를 결합하여 생성된 모델로, R1의 추론 능력과 V3의 토큰 효율성 개선을 통합합니다. 이 모델은 DeepSeek-MoE Transformer 아키텍처를 기반으로 하며, 일반 텍스트 생성 작업을 위해 최적화되었습니다.\n\n이 모델은 두 개의 소스 모델의 사전 훈련된 가중치를 결합하여 추론, 효율성 및 지침 준수 작업의 성능을 균형 있게 조정합니다. MIT 라이센스에 따라 배포되며, 연구 및 상업적 용도로 사용될 수 있습니다."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "Striped<PERSON>yena <PERSON> (7B)는 효율적인 전략과 모델 아키텍처를 통해 향상된 계산 능력을 제공합니다."}, "tts-1": {"description": "최신 텍스트 음성 변환 모델로, 실시간 상황에 최적화된 속도를 제공합니다."}, "tts-1-hd": {"description": "최신 텍스트 음성 변환 모델로, 품질을 최적화했습니다."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B)는 세밀한 지시 작업에 적합하며, 뛰어난 언어 처리 능력을 제공합니다."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet는 업계 표준을 향상시켰으며, 경쟁 모델과 Claude 3 Opus를 초월하는 성능을 보여주고, 광범위한 평가에서 뛰어난 성과를 보이며, 중간 수준 모델의 속도와 비용을 갖추고 있습니다."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 소네트는 Anthropic의 가장 빠른 차세대 모델입니다. <PERSON> 3 하이쿠와 비교할 때, <PERSON> 3.7 소네트는 모든 기술에서 향상되었으며, 많은 지능 기준 테스트에서 이전 세대의 가장 큰 모델인 <PERSON> 3 오푸스를 초월했습니다."}, "v0-1.0-md": {"description": "v0-1.0-md 모델은 v0 API를 통해 제공되는 구버전 모델입니다"}, "v0-1.5-lg": {"description": "v0-1.5-lg 모델은 고급 사고 또는 추론 작업에 적합합니다"}, "v0-1.5-md": {"description": "v0-1.5-md 모델은 일상 작업 및 사용자 인터페이스(UI) 생성에 적합합니다"}, "wan2.2-t2i-flash": {"description": "만상2.2 초고속 버전으로, 현재 최신 모델입니다. 창의성, 안정성, 사실적 질감이 전면 업그레이드되었으며, 생성 속도가 빠르고 비용 효율성이 높습니다."}, "wan2.2-t2i-plus": {"description": "만상2.2 전문 버전으로, 현재 최신 모델입니다. 창의성, 안정성, 사실적 질감이 전면 업그레이드되었으며, 생성 세부 사항이 풍부합니다."}, "wanx-v1": {"description": "기본 텍스트-이미지 생성 모델로, 통의 만상 공식 웹사이트 1.0 범용 모델에 해당합니다."}, "wanx2.0-t2i-turbo": {"description": "질감 인물 생성에 능하며, 속도는 중간, 비용은 낮은 편입니다. 통의 만상 공식 웹사이트 2.0 초고속 모델에 해당합니다."}, "wanx2.1-t2i-plus": {"description": "전면 업그레이드 버전으로, 생성 이미지 세부 사항이 더욱 풍부하며 속도는 다소 느립니다. 통의 만상 공식 웹사이트 2.1 전문 모델에 해당합니다."}, "wanx2.1-t2i-turbo": {"description": "전면 업그레이드 버전으로, 생성 속도가 빠르고 효과가 전반적으로 우수하며 종합 비용 효율성이 높습니다. 통의 만상 공식 웹사이트 2.1 초고속 모델에 해당합니다."}, "whisper-1": {"description": "범용 음성 인식 모델로, 다국어 음성 인식, 음성 번역 및 언어 인식을 지원합니다."}, "wizardlm2": {"description": "WizardLM 2는 Microsoft AI에서 제공하는 언어 모델로, 복잡한 대화, 다국어, 추론 및 스마트 어시스턴트 분야에서 특히 뛰어난 성능을 발휘합니다."}, "wizardlm2:8x22b": {"description": "WizardLM 2는 Microsoft AI에서 제공하는 언어 모델로, 복잡한 대화, 다국어, 추론 및 스마트 어시스턴트 분야에서 특히 뛰어난 성능을 발휘합니다."}, "x1": {"description": "Spark X1 모델은 추가 업그레이드를 통해 기존의 수학 과제에서 국내 선두를 유지하며, 추론, 텍스트 생성, 언어 이해 등 일반 과제에서 OpenAI o1 및 DeepSeek R1과 동등한 성과를 달성합니다."}, "yi-1.5-34b-chat": {"description": "Yi-1.5는 Yi의 업그레이드 버전입니다. 500B 토큰의 고품질 데이터셋을 사용하여 Yi를 추가로 사전 학습시키고, 3M개의 다양한 미세 조정 샘플을 사용하여 미세 조정되었습니다."}, "yi-large": {"description": "새로운 1000억 매개변수 모델로, 강력한 질문 응답 및 텍스트 생성 능력을 제공합니다."}, "yi-large-fc": {"description": "yi-large 모델을 기반으로 도구 호출 능력을 지원하고 강화하여 다양한 에이전트 또는 워크플로우 구축이 필요한 비즈니스 시나리오에 적합합니다."}, "yi-large-preview": {"description": "초기 버전으로, yi-large(신버전) 사용을 권장합니다."}, "yi-large-rag": {"description": "yi-large 초강력 모델을 기반으로 한 고급 서비스로, 검색 및 생성 기술을 결합하여 정확한 답변을 제공하며, 실시간으로 전 세계 정보를 검색하는 서비스를 제공합니다."}, "yi-large-turbo": {"description": "초고성능, 뛰어난 성능. 성능과 추론 속도, 비용을 기준으로 균형 잡힌 고정밀 조정을 수행합니다."}, "yi-lightning": {"description": "최신 고성능 모델로, 고품질 출력을 보장하며, 추론 속도를 크게 향상시킵니다."}, "yi-lightning-lite": {"description": "경량 버전으로, yi-lightning 사용을 권장합니다."}, "yi-medium": {"description": "중형 모델 업그레이드 및 미세 조정으로, 능력이 균형 잡히고 가성비가 높습니다. 지시 따르기 능력을 깊이 최적화하였습니다."}, "yi-medium-200k": {"description": "200K 초장기 컨텍스트 창을 지원하여 긴 텍스트의 깊은 이해 및 생성 능력을 제공합니다."}, "yi-spark": {"description": "작고 강력한 경량 모델로, 강화된 수학 연산 및 코드 작성 능력을 제공합니다."}, "yi-vision": {"description": "복잡한 시각 작업 모델로, 고성능 이미지 이해 및 분석 능력을 제공합니다."}, "yi-vision-v2": {"description": "복잡한 시각적 작업 모델로, 여러 이미지를 기반으로 한 고성능 이해 및 분석 능력을 제공합니다."}, "zai-org/GLM-4.5": {"description": "GLM-4.5는 에이전트 애플리케이션을 위해 설계된 기본 모델로, 혼합 전문가(Mixture-of-Experts) 아키텍처를 사용합니다. 도구 호출, 웹 브라우징, 소프트웨어 엔지니어링, 프론트엔드 프로그래밍 분야에서 깊이 최적화되었으며, <PERSON>, Roo Code 등 코드 에이전트에 원활히 통합될 수 있습니다. GLM-4.5는 혼합 추론 모드를 채택하여 복잡한 추론과 일상 사용 등 다양한 응용 시나리오에 적응할 수 있습니다."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air는 에이전트 애플리케이션을 위해 설계된 기본 모델로, 혼합 전문가(Mixture-of-Experts) 아키텍처를 사용합니다. 도구 호출, 웹 브라우징, 소프트웨어 엔지니어링, 프론트엔드 프로그래밍 분야에서 깊이 최적화되었으며, <PERSON>, Roo Code 등 코드 에이전트에 원활히 통합될 수 있습니다. GLM-4.5는 혼합 추론 모드를 채택하여 복잡한 추론과 일상 사용 등 다양한 응용 시나리오에 적응할 수 있습니다."}}