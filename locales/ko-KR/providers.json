{"ai21": {"description": "AI21 Labs는 기업을 위해 기본 모델과 인공지능 시스템을 구축하여 생성적 인공지능의 생산적 활용을 가속화합니다."}, "ai360": {"description": "360 AI는 360 회사가 출시한 AI 모델 및 서비스 플랫폼으로, 360GPT2 Pro, 360GPT Pro, 360GPT Turbo 및 360GPT Turbo Responsibility 8K를 포함한 다양한 고급 자연어 처리 모델을 제공합니다. 이러한 모델은 대규모 매개변수와 다중 모드 능력을 결합하여 텍스트 생성, 의미 이해, 대화 시스템 및 코드 생성 등 다양한 분야에 널리 사용됩니다. 유연한 가격 전략을 통해 360 AI는 다양한 사용자 요구를 충족하고 개발자가 통합할 수 있도록 지원하여 스마트화 응용 프로그램의 혁신과 발전을 촉진합니다."}, "aihubmix": {"description": "AiHubMix는 통합 API 인터페이스를 통해 다양한 AI 모델에 대한 접근을 제공합니다."}, "anthropic": {"description": "Anthropic은 인공지능 연구 및 개발에 집중하는 회사로, <PERSON> 3.5 <PERSON><PERSON>, <PERSON> 3 Sonnet, <PERSON> 3 Opus 및 Claude 3 Haiku와 같은 고급 언어 모델을 제공합니다. 이러한 모델은 지능, 속도 및 비용 간의 이상적인 균형을 이루며, 기업급 작업 부하에서부터 빠른 응답이 필요한 다양한 응용 프로그램에 적합합니다. Claude 3.5 Sonnet은 최신 모델로, 여러 평가에서 우수한 성능을 보이며 높은 비용 효율성을 유지하고 있습니다."}, "azure": {"description": "Azure는 GPT-3.5 및 최신 GPT-4 시리즈를 포함한 다양한 고급 AI 모델을 제공하며, 다양한 데이터 유형과 복잡한 작업을 지원하고 안전하고 신뢰할 수 있으며 지속 가능한 AI 솔루션을 목표로 하고 있습니다."}, "azureai": {"description": "Azure는 GPT-3.5 및 최신 GPT-4 시리즈를 포함한 다양한 고급 AI 모델을 제공하며, 다양한 데이터 유형과 복잡한 작업을 지원하고 안전하고 신뢰할 수 있으며 지속 가능한 AI 솔루션을 위해 노력합니다."}, "baichuan": {"description": "百川智能은 인공지능 대형 모델 연구 개발에 집중하는 회사로, 그 모델은 국내 지식 백과, 긴 텍스트 처리 및 생성 창작 등 중국어 작업에서 뛰어난 성능을 보이며, 해외 주류 모델을 초월합니다. 百川智能은 업계 선도적인 다중 모드 능력을 갖추고 있으며, 여러 권위 있는 평가에서 우수한 성능을 보였습니다. 그 모델에는 Baichuan 4, Baichuan 3 Turbo 및 Baichuan 3 Turbo 128k 등이 포함되어 있으며, 각각 다른 응용 시나리오에 최적화되어 비용 효율적인 솔루션을 제공합니다."}, "bedrock": {"description": "Bedrock은 아마존 AWS가 제공하는 서비스로, 기업에 고급 AI 언어 모델과 비주얼 모델을 제공합니다. 그 모델 가족에는 Anthropic의 Claude 시리즈, Meta의 Llama 3.1 시리즈 등이 포함되어 있으며, 경량형부터 고성능까지 다양한 선택지를 제공하고 텍스트 생성, 대화, 이미지 처리 등 여러 작업을 지원하여 다양한 규모와 요구의 기업 응용 프로그램에 적합합니다."}, "cloudflare": {"description": "Cloudflare의 글로벌 네트워크에서 서버리스 GPU로 구동되는 머신러닝 모델을 실행합니다."}, "cohere": {"description": "Cohere는 최첨단 다국어 모델, 고급 검색 기능 및 현대 기업을 위해 맞춤 설계된 AI 작업 공간을 제공합니다 — 모든 것이 안전한 플랫폼에 통합되어 있습니다."}, "deepseek": {"description": "DeepSeek는 인공지능 기술 연구 및 응용에 집중하는 회사로, 최신 모델인 DeepSeek-V2.5는 일반 대화 및 코드 처리 능력을 통합하고 인간의 선호 정렬, 작문 작업 및 지시 따르기 등에서 상당한 향상을 이루었습니다."}, "fal": {"description": "개발자를 위한 생성형 미디어 플랫폼"}, "fireworksai": {"description": "Fireworks AI는 기능 호출 및 다중 모드 처리를 전문으로 하는 선도적인 고급 언어 모델 서비스 제공업체입니다. 최신 모델인 Firefunction V2는 Llama-3를 기반으로 하며, 함수 호출, 대화 및 지시 따르기에 최적화되어 있습니다. 비주얼 언어 모델인 FireLLaVA-13B는 이미지와 텍스트 혼합 입력을 지원합니다. 기타 주목할 만한 모델로는 Llama 시리즈와 Mixtral 시리즈가 있으며, 효율적인 다국어 지시 따르기 및 생성 지원을 제공합니다."}, "giteeai": {"description": "Gitee AI의 Serverless API는 AI 개발자에게 즉시 사용할 수 있는 대형 모델 추론 API 서비스를 제공한다."}, "github": {"description": "GitHub 모델을 통해 개발자는 AI 엔지니어가 되어 업계 최고의 AI 모델로 구축할 수 있습니다."}, "google": {"description": "Google의 Gemini 시리즈는 Google DeepMind가 개발한 가장 진보된 범용 AI 모델로, 다중 모드 설계를 통해 텍스트, 코드, 이미지, 오디오 및 비디오의 원활한 이해 및 처리를 지원합니다. 데이터 센터에서 모바일 장치에 이르기까지 다양한 환경에 적합하며 AI 모델의 효율성과 응용 범위를 크게 향상시킵니다."}, "groq": {"description": "Groq의 LPU 추론 엔진은 최신 독립 대형 언어 모델(LLM) 벤치마크 테스트에서 뛰어난 성능을 보이며, 놀라운 속도와 효율성으로 AI 솔루션의 기준을 재정의하고 있습니다. Groq는 즉각적인 추론 속도의 대표주자로, 클라우드 기반 배포에서 우수한 성능을 보여줍니다."}, "higress": {"description": "Higress는 클라우드 네이티브 API 게이트웨이로, 알리 내부에서 Tengine reload가 장기 연결 비즈니스에 미치는 영향을 해결하고 gRPC/Dubbo의 로드 밸런싱 능력이 부족한 문제를 해결하기 위해 탄생했습니다."}, "huggingface": {"description": "HuggingFace Inference API는 수천 개의 모델을 탐색할 수 있는 빠르고 무료의 방법을 제공합니다. 새로운 애플리케이션을 프로토타입 하거나 머신러닝의 기능을 시도하는 경우, 이 API는 여러 분야의 고성능 모델에 즉시 접근할 수 있게 해줍니다."}, "hunyuan": {"description": "텐센트가 개발한 대형 언어 모델로, 강력한 한국어 창작 능력과 복잡한 맥락에서의 논리적 추론 능력, 그리고 신뢰할 수 있는 작업 수행 능력을 갖추고 있습니다."}, "infiniai": {"description": "애플리케이션 개발자에게 고성능, 사용하기 쉬운, 안전하고 신뢰할 수 있는 대형 모델 서비스를 제공하며, 대형 모델 개발부터 서비스 배포까지의 전체 프로세스를 지원합니다."}, "internlm": {"description": "대규모 모델 연구 및 개발 도구 체인에 전념하는 오픈 소스 조직입니다. 모든 AI 개발자에게 효율적이고 사용하기 쉬운 오픈 소스 플랫폼을 제공하여 최첨단 대규모 모델 및 알고리즘 기술을 손쉽게 이용할 수 있도록 합니다."}, "jina": {"description": "Jina AI는 2020년에 설립된 선도적인 검색 AI 회사입니다. 우리의 검색 기반 플랫폼은 기업이 신뢰할 수 있고 고품질의 생성적 AI 및 다중 모드 검색 애플리케이션을 구축할 수 있도록 돕는 벡터 모델, 재배치기 및 소형 언어 모델을 포함하고 있습니다."}, "lmstudio": {"description": "LM Studio는 귀하의 컴퓨터에서 LLM을 개발하고 실험하기 위한 데스크탑 애플리케이션입니다."}, "minimax": {"description": "MiniMax는 2021년에 설립된 일반 인공지능 기술 회사로, 사용자와 함께 지능을 공동 창출하는 데 전념하고 있습니다. MiniMax는 다양한 모드의 일반 대형 모델을 독자적으로 개발하였으며, 여기에는 조 단위의 MoE 텍스트 대형 모델, 음성 대형 모델 및 이미지 대형 모델이 포함됩니다. 또한 해마 AI와 같은 응용 프로그램을 출시하였습니다."}, "mistral": {"description": "Mistral은 고급 일반, 전문 및 연구형 모델을 제공하며, 복잡한 추론, 다국어 작업, 코드 생성 등 다양한 분야에 널리 사용됩니다. 기능 호출 인터페이스를 통해 사용자는 사용자 정의 기능을 통합하여 특정 응용 프로그램을 구현할 수 있습니다."}, "modelscope": {"description": "ModelScope는 알리바바 클라우드에서 출시한 모델 서비스 플랫폼으로, 풍부한 AI 모델과 추론 서비스를 제공합니다."}, "moonshot": {"description": "Moonshot은 베이징 월의 어두운 면 기술 회사가 출시한 오픈 소스 플랫폼으로, 다양한 자연어 처리 모델을 제공하며, 콘텐츠 창작, 학술 연구, 스마트 추천, 의료 진단 등 다양한 분야에 적용됩니다. 긴 텍스트 처리 및 복잡한 생성 작업을 지원합니다."}, "novita": {"description": "Novita AI는 다양한 대형 언어 모델과 AI 이미지 생성을 제공하는 API 서비스 플랫폼으로, 유연하고 신뢰할 수 있으며 비용 효율적입니다. Llama3, Mistral 등 최신 오픈 소스 모델을 지원하며, 생성적 AI 응용 프로그램 개발을 위한 포괄적이고 사용자 친화적이며 자동 확장 가능한 API 솔루션을 제공하여 AI 스타트업의 빠른 발전에 적합합니다."}, "nvidia": {"description": "NVIDIA NIM™은 클라우드, 데이터 센터, RTX™ AI 개인용 컴퓨터 및 워크스테이션에서 사전 훈련된 AI 모델과 사용자 정의 AI 모델을 배포할 수 있도록 지원하는 컨테이너를 제공합니다."}, "ollama": {"description": "Ollama가 제공하는 모델은 코드 생성, 수학 연산, 다국어 처리 및 대화 상호작용 등 다양한 분야를 포괄하며, 기업급 및 로컬 배포의 다양한 요구를 지원합니다."}, "openai": {"description": "OpenAI는 세계 최고의 인공지능 연구 기관으로, 개발한 모델인 GPT 시리즈는 자연어 처리의 최전선에서 혁신을 이끌고 있습니다. OpenAI는 혁신적이고 효율적인 AI 솔루션을 통해 여러 산업을 변화시키는 데 전념하고 있습니다. 그들의 제품은 뛰어난 성능과 경제성을 갖추고 있어 연구, 비즈니스 및 혁신적인 응용 프로그램에서 널리 사용됩니다."}, "openrouter": {"description": "OpenRouter는 OpenAI, Anthropic, LLaMA 등 다양한 최첨단 대형 모델 인터페이스를 제공하는 서비스 플랫폼으로, 다양한 개발 및 응용 요구에 적합합니다. 사용자는 자신의 필요에 따라 최적의 모델과 가격을 유연하게 선택하여 AI 경험을 향상시킬 수 있습니다."}, "perplexity": {"description": "Perplexity는 선도적인 대화 생성 모델 제공업체로, 다양한 고급 Llama 3.1 모델을 제공하며, 온라인 및 오프라인 응용 프로그램을 지원하고 복잡한 자연어 처리 작업에 특히 적합합니다."}, "ppio": {"description": "PPIO 파이오 클라우드는 안정적이고 비용 효율적인 오픈 소스 모델 API 서비스를 제공하며, DeepSeek 전 시리즈, <PERSON><PERSON><PERSON>, <PERSON>wen 등 업계 선도 대모델을 지원합니다."}, "qiniu": {"description": "Qiniu는 대형 모델 서비스를 제공하는 대형 모델 플랫폼으로, 안정적이고 비용 효율적인 오픈 소스 모델 API 서비스를 제공하며, DeepSeek 전 시리즈, <PERSON><PERSON><PERSON>, Qwen 등 업계 선도 대모델을 지원합니다."}, "qwen": {"description": "통의천문은 알리바바 클라우드가 자주 개발한 초대형 언어 모델로, 강력한 자연어 이해 및 생성 능력을 갖추고 있습니다. 다양한 질문에 답변하고, 텍스트 콘텐츠를 창작하며, 의견을 표현하고, 코드를 작성하는 등 여러 분야에서 활용됩니다."}, "sambanova": {"description": "SambaNova Cloud는 개발자가 최고의 오픈 소스 모델을 쉽게 사용하고 가장 빠른 추론 속도를 즐길 수 있도록 합니다."}, "search1api": {"description": "Search1API는 필요에 따라 연결할 수 있는 DeepSeek 시리즈 모델에 대한 액세스를 제공하며, 표준 버전과 빠른 버전을 포함하고 다양한 매개변수 규모의 모델 선택을 지원합니다."}, "sensenova": {"description": "상탕의 일일 혁신은 상탕의 강력한 기반 지원을 바탕으로 효율적이고 사용하기 쉬운 전체 스택 대모델 서비스를 제공합니다."}, "siliconcloud": {"description": "SiliconFlow는 AGI를 가속화하여 인류에 혜택을 주기 위해 사용하기 쉽고 비용이 저렴한 GenAI 스택을 통해 대규모 AI 효율성을 향상시키는 데 전념하고 있습니다."}, "spark": {"description": "科大讯飞星火 대모델은 다중 분야 및 다국어의 강력한 AI 능력을 제공하며, 고급 자연어 처리 기술을 활용하여 스마트 하드웨어, 스마트 의료, 스마트 금융 등 다양한 수직 분야에 적합한 혁신적인 응용 프로그램을 구축합니다."}, "stepfun": {"description": "阶级星辰 대모델은 업계 선도적인 다중 모드 및 복잡한 추론 능력을 갖추고 있으며, 초장 텍스트 이해 및 강력한 자율 스케줄링 검색 엔진 기능을 지원합니다."}, "taichu": {"description": "중국과학원 자동화 연구소와 우한 인공지능 연구원이 출시한 차세대 다중 모드 대형 모델은 다중 회차 질문 응답, 텍스트 창작, 이미지 생성, 3D 이해, 신호 분석 등 포괄적인 질문 응답 작업을 지원하며, 더 강력한 인지, 이해 및 창작 능력을 갖추고 있어 새로운 상호작용 경험을 제공합니다."}, "tencentcloud": {"description": "지식 엔진 원자 능력(LLM Knowledge Engine Atomic Power)은 지식 엔진을 기반으로 개발된 지식 질문 응답의 전체 링크 능력으로, 기업 및 개발자를 대상으로 하여 유연한 모델 응용 프로그램 구성 및 개발 능력을 제공합니다. 여러 원자 능력을 통해 귀하만의 모델 서비스를 구성하고, 문서 분석, 분할, 임베딩, 다중 회차 수정 등의 서비스를 호출하여 조합하여 기업 전용 AI 비즈니스를 맞춤화할 수 있습니다."}, "togetherai": {"description": "Together AI는 혁신적인 AI 모델을 통해 선도적인 성능을 달성하는 데 전념하며, 빠른 확장 지원 및 직관적인 배포 프로세스를 포함한 광범위한 사용자 정의 기능을 제공하여 기업의 다양한 요구를 충족합니다."}, "upstage": {"description": "Upstage는 Solar LLM 및 문서 AI를 포함하여 다양한 비즈니스 요구를 위한 AI 모델 개발에 집중하고 있으며, 인공지능 일반 지능(AGI)을 실현하는 것을 목표로 하고 있습니다. Chat API를 통해 간단한 대화 에이전트를 생성하고 기능 호출, 번역, 임베딩 및 특정 분야 응용 프로그램을 지원합니다."}, "v0": {"description": "v0는 페어 프로그래밍 도우미로, 자연어로 아이디어를 설명하기만 하면 프로젝트에 필요한 코드와 사용자 인터페이스(UI)를 생성해 줍니다."}, "vertexai": {"description": "구글의 제미니 시리즈는 구글 딥마인드가 개발한 최첨단 범용 AI 모델로, 다중 모드에 맞춰 설계되어 텍스트, 코드, 이미지, 오디오 및 비디오의 원활한 이해와 처리를 지원합니다. 데이터 센터에서 모바일 장치에 이르기까지 다양한 환경에 적합하며, AI 모델의 효율성과 응용 범위를 크게 향상시킵니다."}, "vllm": {"description": "vLLM은 LLM 추론 및 서비스를 위한 빠르고 사용하기 쉬운 라이브러리입니다."}, "volcengine": {"description": "바이트댄스가 출시한 대형 모델 서비스 개발 플랫폼으로, 기능이 풍부하고 안전하며 가격 경쟁력이 있는 모델 호출 서비스를 제공합니다. 또한 모델 데이터, 세밀 조정, 추론, 평가 등 엔드 투 엔드 기능을 제공하여 귀하의 AI 애플리케이션 개발을 전방위적으로 지원합니다."}, "wenxin": {"description": "기업용 원스톱 대형 모델 및 AI 네이티브 애플리케이션 개발 및 서비스 플랫폼으로, 가장 포괄적이고 사용하기 쉬운 생성적 인공지능 모델 개발 및 애플리케이션 개발 전체 프로세스 도구 체인을 제공합니다."}, "xai": {"description": "xAI는 인류의 과학적 발견을 가속화하기 위해 인공지능을 구축하는 데 전념하는 회사입니다. 우리의 사명은 우주에 대한 공동의 이해를 증진하는 것입니다."}, "xinference": {"description": "Xorbits 추론(Xinference)은 다양한 AI 모델의 실행 및 통합을 단순화하기 위한 오픈소스 플랫폼입니다. Xinference를 사용하면 클라우드 또는 로컬 환경에서 오픈소스 LLM, 임베딩 모델 및 멀티모달 모델을 활용하여 추론을 실행하고 강력한 AI 애플리케이션을 구축할 수 있습니다."}, "zeroone": {"description": "01.AI는 AI 2.0 시대의 인공지능 기술에 집중하며, '인간 + 인공지능'의 혁신과 응용을 적극적으로 추진하고, 초강력 모델과 고급 AI 기술을 활용하여 인간의 생산성을 향상시키고 기술의 힘을 실현합니다."}, "zhipu": {"description": "智谱 AI는 다중 모드 및 언어 모델의 개방형 플랫폼을 제공하며, 텍스트 처리, 이미지 이해 및 프로그래밍 지원 등 광범위한 AI 응용 프로그램 시나리오를 지원합니다."}}