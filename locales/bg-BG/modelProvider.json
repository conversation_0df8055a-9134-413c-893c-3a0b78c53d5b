{"azure": {"azureApiVersion": {"desc": "Версия на Azure API, в формат YYYY-MM-DD, вижте [най-новата версия](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "Извличане на списък", "title": "Версия на Azure API"}, "empty": "Моля, въведете идентификатор на модела, за да добавите първия модел", "endpoint": {"desc": "Тази стойност може да бъде намерена в раздела „Ключове и крайни точки“ при проверка на ресурсите от портала на Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Адрес на Azure API"}, "modelListPlaceholder": "Изберете или добавете моделите на OpenAI, които сте разгърнали", "title": "Azure OpenAI", "token": {"desc": "Тази стойност може да бъде намерена в раздела „Ключове и крайни точки“ при проверка на ресурсите от портала на Azure. Можете да използвате KEY1 или KEY2", "placeholder": "Azure API Key", "title": "<PERSON> ключ"}}, "azureai": {"azureApiVersion": {"desc": "Версия на API на Azure, следваща формата YYYY-MM-DD, вижте [най-новата версия](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "Вземи списък", "title": "Версия на API на Azure"}, "endpoint": {"desc": "Намерете крайна точка за инференция на моделите на Azure AI в прегледа на проекта на Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Крайна точка на Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Намерете API ключа в прегледа на проекта на Azure AI", "placeholder": "Ключ на Azure", "title": "<PERSON><PERSON><PERSON><PERSON>"}}, "bedrock": {"accessKeyId": {"desc": "Въведете AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "Тестване дали AccessKeyId / SecretAccessKey са попълнени правилно"}, "region": {"desc": "Въведете AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Въведете AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Ако използвате AWS SSO/STS, моля, въведете вашия AWS Session Token", "placeholder": "AWS Session Token", "title": "AWS Session Token (по избор)"}, "title": "Bedrock", "unlock": {"customRegion": "Персонализиран регион за услуги", "customSessionToken": "Персонализиран токен за сесия", "description": "Въведете вашия AWS AccessKeyId / SecretAccessKey, за да започнете сесия. Приложението няма да запази вашата удостоверителна конфигурация", "imageGenerationDescription": "Въведете вашия AWS AccessKeyId / SecretAccessKey, за да започнете генерирането. Приложението няма да записва вашите данни за удостоверяване", "title": "Използване на персонализирана информация за удостоверяване на Bedrock"}}, "cloudflare": {"apiKey": {"desc": "Моля, въведете Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Въведете ID на Cloudflare или личен API адрес", "placeholder": "ID на Cloudflare / личен API адрес", "title": "ID на Cloudflare / API адрес"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Моля, въведете вашия API ключ", "title": "<PERSON> ключ"}, "basicTitle": "Основна информация", "configTitle": "Конфигурационна информация", "confirm": "Създаване", "createSuccess": "Създаването е успешно", "description": {"placeholder": "Описание на доставчика (по избор)", "title": "Описание на доставчика"}, "id": {"desc": "Уникален идентификатор за доставчика на услуги, който не може да бъде променян след създаването му", "format": "Може да съдържа само цифри, малки букви, тирета (-) и долни черти (_) ", "placeholder": "Препоръчително изцяло с малки букви, например openai, след създаването не може да се промени", "required": "Моля, въведете ID на доставчика", "title": "ID на доставчика"}, "logo": {"required": "Моля, качете правилното лого на доставчика", "title": "Лого на доставчика"}, "name": {"placeholder": "Моля, въведете показваното име на доставчика", "required": "Моля, въведете името на доставчика", "title": "Име на доставчика"}, "proxyUrl": {"required": "Моля, въведете адреса на проксито", "title": "Адрес на прокси"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Моля, изберете тип SDK", "title": "Формат на запитването"}, "title": "Създаване на персонализиран AI доставчик"}, "github": {"personalAccessToken": {"desc": "Въведете вашия GitHub PAT, кликнете [тук](https://github.com/settings/tokens), за да създадете", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Въведете вашия HuggingFace токен, кликнете [тук](https://huggingface.co/settings/tokens), за да създадете", "placeholder": "hf_xxxxxxxxx", "title": "HuggingFace токен"}}, "list": {"title": {"disabled": "Неактивен доставчик", "enabled": "Активен доставчик"}}, "menu": {"addCustomProvider": "Добавяне на персонализиран доставчик", "all": "Всички", "list": {"disabled": "Неактиви<PERSON><PERSON>н", "enabled": "А<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notFound": "Не са намерени резултати от търсенето", "searchProviders": "Търсене на доставчици...", "sort": "Персонализирано сортиране"}, "ollama": {"checker": {"desc": "Тестване дали адресът на прокси е попълнен правилно", "title": "Проверка на свързаност"}, "customModelName": {"desc": "Добавяне на персонализирани модели, използвайте запетая (,) за разделяне на множество модели", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Имена на персонализирани модели"}, "download": {"desc": "Ollama изтегля този модел, мол<PERSON>, не затваряйте тази страница. При повторно изтегляне ще продължи от мястото, на което е прекъснато", "failed": "Изтеглянето на модела не успя, моля проверете мрежата или настройките на Ollama и опитайте отново", "remainingTime": "Оставащо време", "speed": "Скорост на изтегляне", "title": "Изтегляне на модел {{model}} "}, "endpoint": {"desc": "Трябва да съдържа http(s)://, местният адрес може да остане празен, ако не е зададен допълнително", "title": "Адрес на прокси интерфейс"}, "title": "Ollama", "unlock": {"cancel": "Отмяна на изтеглянето", "confirm": "Изтегляне", "description": "Въведете етикета на вашия Ollama модел, за да продължите сесията", "downloaded": "{{completed}} / {{total}}", "starting": "Започва изтеглянето...", "title": "Изтегляне на зададения Ollama модел"}}, "providerModels": {"config": {"aesGcm": "Ваши<PERSON>т ключ и адреса на прокси ще бъдат криптирани с <1>AES-GCM</1> алгоритъм", "apiKey": {"desc": "Моля, въведете вашия {{name}} API ключ", "descWithUrl": "Моля, въведете вашия {{name}} API ключ, <3>кликнете тук, за да го получите</3>", "placeholder": "{{name}} <PERSON> ключ", "title": "<PERSON> ключ"}, "baseURL": {"desc": "Трябва да съдържа http(s)://", "invalid": "Моля, въведете валиден URL", "placeholder": "https://your-proxy-url.com/v1", "title": "API адрес на прокси"}, "checker": {"button": "Проверка", "desc": "Тест на API ключа и адреса на прокси за правилно попълване", "pass": "Проверката е успешна", "title": "Проверка на свързаност"}, "fetchOnClient": {"desc": "Режимът на клиентски запитвания ще инициира сесийни запитвания директно от браузъра, което може да ускори времето за отговор", "title": "Използване на клиентски режим на запитвания"}, "helpDoc": "Ръководство за конфигуриране", "responsesApi": {"desc": "Използва новия формат на заявките на OpenAI, отключващ функции като вериги на мислене и други усъвършенствани възможности", "title": "Използване на Responses API стандарта"}, "waitingForMore": "Още модели са в <1>планиране</1>, моля, очаквайте"}, "createNew": {"title": "Създаване на персонализиран AI модел"}, "item": {"config": "Конфигуриране на модела", "customModelCards": {"addNew": "Създаване и добавяне на модел {{id}}", "confirmDelete": "Ще изтриете този персонализи<PERSON><PERSON><PERSON> модел, след изтриването няма да може да бъде възстановен, моля, действайте внимателно."}, "delete": {"confirm": "Потвърдете ли, че искате да изтриете модела {{displayName}}?", "success": "Изтриването е успешно", "title": "Изтриване на модел"}, "modelConfig": {"azureDeployName": {"extra": "Полето, което действително се изисква в Azure OpenAI", "placeholder": "Моля, въведете името на модела за разполагане в Azure", "title": "Име на разполагане на модела"}, "deployName": {"extra": "Това поле ще бъде използвано като ID на модела при изпращане на заявката", "placeholder": "Моля, въведете действителното име или ID на разположението на модела", "title": "Име на разположение на модела"}, "displayName": {"placeholder": "Моля, въведете показваното име на модела, например ChatGPT, GPT-4 и др.", "title": "Показвано име на модела"}, "files": {"extra": "Текущата функция за качване на файлове е само един хак, само за опити. Пълната функционалност за качване на файлове ще бъде реализирана по-късно.", "title": "Поддръжка на качване на файлове"}, "functionCall": {"extra": "Тази конфигурация ще активира само способността на модела да използва инструменти, което позволява добавянето на плъгини от клас инструменти. Но дали наистина ще се поддържа използването на инструменти зависи изцяло от самия модел, моля, тествайте неговата наличност", "title": "Поддръжка на използването на инструменти"}, "id": {"extra": "След създаването не може да бъде променян, ще се използва като идентификатор на модела при извикване на AI", "placeholder": "Моля, въведете идентификатор на модела, например gpt-4o или claude-3.5-sonnet", "title": "ID на модела"}, "modalTitle": "Конфигурация на персонализиран модел", "reasoning": {"extra": "Тази конфигурация ще активира само способността на модела за дълбоко мислене, конкретният ефект зависи изцяло от самия модел, моля, тествайте сами дали моделът притежава налична способност за дълбоко мислене", "title": "Поддръжка на дълбоко мислене"}, "tokens": {"extra": "Настройте максималния брой токени, поддържани от модела", "title": "Максима<PERSON><PERSON>н контекстуален прозорец", "unlimited": "Без ограничения"}, "vision": {"extra": "Тази конфигурация ще активира само конфигурацията за качване на изображения в приложението, дали поддържа разпознаване зависи изцяло от самия модел, моля, тествайте наличността на визуалната разпознаваемост на този модел.", "title": "Поддръжка на визуално разпознаване"}}, "pricing": {"image": "${{amount}}/изображение", "inputCharts": "${{amount}}/M символи", "inputMinutes": "${{amount}}/минути", "inputTokens": "Входящи ${{amount}}/М", "outputTokens": "Изходящи ${{amount}}/М"}, "releasedAt": "Пуснато на {{releasedAt}}"}, "list": {"addNew": "Добавяне на модел", "disabled": "Неактивен", "disabledActions": {"showMore": "Покажи всичко"}, "empty": {"desc": "Моля, създайте персонализиран модел или изтеглете модел, за да започнете да го използвате", "title": "Няма налични модели"}, "enabled": "Акти<PERSON><PERSON>н", "enabledActions": {"disableAll": "Деактиви<PERSON><PERSON>й всичко", "enableAll": "Актив<PERSON><PERSON><PERSON><PERSON> всичко", "sort": "Персонализиране на подредбата на моделите"}, "enabledEmpty": "Няма активни модели, моля активирайте желаните модели от списъка по-долу~", "fetcher": {"clear": "Изчисти получените модели", "fetch": "Получаване на списък с модели", "fetching": "Получаване на списък с модели...", "latestTime": "Последно обновление: {{time}}", "noLatestTime": "Все още не е получен списък"}, "resetAll": {"conform": "Потвърдете ли, че искате да нулирате всички промени в текущия модел? След нулирането списъкът с текущи модели ще се върне в първоначалното си състояние", "success": "Успешно нулирано", "title": "Нулиране на всички промени"}, "search": "Търсене на модели...", "searchResult": "Намерени са {{count}} модела", "title": "Списък с модели", "total": "Общо {{count}} налични модела"}, "searchNotFound": "Не са намерени резултати от търсенето"}, "sortModal": {"success": "Сортирането е успешно обновено", "title": "Персонализирано сортиране", "update": "Актуализи<PERSON>а<PERSON>е"}, "updateAiProvider": {"confirmDelete": "Ще изтриете този AI доставчик, след изтриването няма да може да бъде възстановен, потвърдете ли, че искате да изтриете?", "deleteSuccess": "Изтриването е успешно", "tooltip": "Актуализиране на основната конфигурация на доставчика", "updateSuccess": "Актуализацията е успешна"}, "updateCustomAiProvider": {"title": "Актуализиране на конфигурацията на доставчика на персонализирани AI услуги"}, "vertexai": {"apiKey": {"desc": "Въведете вашите ключове за Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Ключове за Vertex AI"}}, "zeroone": {"title": "01.AI Зероуан Всичко"}, "zhipu": {"title": "Интелигентен албум"}}