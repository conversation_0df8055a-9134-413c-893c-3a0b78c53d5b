{"01-ai/yi-1.5-34b-chat": {"description": "零一万物, най-новият отворен модел с фина настройка, с 34 милиарда параметри, който поддържа множество диалогови сценарии, с висококачествени обучителни данни, съобразени с човешките предпочитания."}, "01-ai/yi-1.5-9b-chat": {"description": "零一万物, най-новият отворен модел с фина настройка, с 9 милиарда параметри, който поддържа множество диалогови сценарии, с висококачествени обучителни данни, съобразени с човешките предпочитания."}, "360/deepseek-r1": {"description": "【360 версия】DeepSeek-R1 използва мащабно обучение с подсилване в етапа на следобучение, значително подобрявайки способността на модела за извеждане при наличието на много малко етикетирани данни. В задачите по математика, код и разсъждения на естествен език, производителността му е наравно с официалната версия на OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro, като важен член на серията AI модели на 360, отговаря на разнообразни приложения на естествения език с ефективни способности за обработка на текст, поддържайки разбиране на дълги текстове и многостепенни диалози."}, "360gpt-pro-trans": {"description": "Модел, специално проектиран за превод, дълбоко оптимизиран за постигане на водещи резултати."}, "360gpt-turbo": {"description": "360GPT Turbo предлага мощни изчислителни и диалогови способности, с отлична семантична разбираемост и ефективност на генериране, идеално решение за интелигентни асистенти за предприятия и разработчици."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K акцентира на семантичната безопасност и отговорността, проектиран специално за приложения с високи изисквания за безопасност на съдържанието, осигурявайки точност и стабилност на потребителското изживяване."}, "360gpt2-o1": {"description": "360gpt2-o1 използва дървесно търсене за изграждане на вериги от мисли и въвежда механизъм за размисъл, обучен чрез подсилено учене, моделът притежава способността за саморазмисъл и корекция на грешки."}, "360gpt2-pro": {"description": "360GPT2 Pro е усъвършенстван модел за обработка на естествен език, пуснат от компания 360, с изключителни способности за генериране и разбиране на текст, особено в областта на генерирането и творчеството, способен да обработва сложни езикови трансформации и ролеви игри."}, "360zhinao2-o1": {"description": "360zhinao2-o1 използва дървесно търсене за изграждане на мисловни вериги и въвежда механизъм за саморазмисъл, обучавайки се чрез подсилено учене, моделът притежава способността за саморазмисъл и корекция на грешки."}, "4.0Ultra": {"description": "Spark4.0 Ultra е най-мощната версия в серията Starfire, която подобрява разбирането и обобщаването на текстовото съдържание, докато надгражда свързаните търсения. Това е всестранно решение за повишаване на производителността в офиса и точно отговаряне на нуждите, водещо в индустрията интелигентно решение."}, "AnimeSharp": {"description": "AnimeSharp (известен още като “4x‑AnimeSharp”) е отворен модел за свръхрезолюция, разработен от Kim2091 на базата на архитектурата ESRGAN, фокусиран върху увеличаване и изостряне на изображения в аниме стил. През февруари 2022 г. моделът е преименуван от “4x-TextSharpV1” и първоначално е бил подходящ и за текстови изображения, но е оптимизиран значително за аниме съдържание."}, "Baichuan2-Turbo": {"description": "Използва технологии за подобряване на търсенето, за да свърже голям модел с областни знания и знания от интернет. Поддържа качване на различни документи като PDF, Word и вход на уебсайтове, с бърз и цялостен достъп до информация, предоставяйки точни и професионални резултати."}, "Baichuan3-Turbo": {"description": "Оптимизиран за често срещани корпоративни сценарии, с значително подобрени резултати и висока цена-качество. В сравнение с модела Baichuan2, генерирането на съдържание е увеличено с 20%, отговорите на знания с 17%, а способността за ролеви игри с 40%. Общите резултати са по-добри от тези на GPT3.5."}, "Baichuan3-Turbo-128k": {"description": "С 128K свръхдълъг контекстен прозорец, оптимизиран за често срещани корпоративни сценарии, с значително подобрени резултати и висока цена-качество. В сравнение с модела Baichuan2, генерирането на съдържание е увеличено с 20%, отговорите на знания с 17%, а способността за ролеви игри с 40%. Общите резултати са по-добри от тези на GPT3.5."}, "Baichuan4": {"description": "Моделът е с най-добри способности в страната, надминаващ чуждестранните водещи модели в задачи като енциклопедични знания, дълги текстове и генериране на съдържание. Също така притежава водещи в индустрията мултимодални способности и отлични резултати в множество авторитетни тестови стандарти."}, "Baichuan4-Air": {"description": "Моделът е лидер в страната по способности, надминавайки чуждестранните основни модели в задачи на китайски език, като знания, дълги текстове и генериране на творби. Също така притежава водещи в индустрията мултимодални способности и отлични резултати в множество авторитетни оценки."}, "Baichuan4-Turbo": {"description": "Моделът е лидер в страната по способности, надминавайки чуждестранните основни модели в задачи на китайски език, като знания, дълги текстове и генериране на творби. Също така притежава водещи в индустрията мултимодални способности и отлични резултати в множество авторитетни оценки."}, "DeepSeek-R1": {"description": "Най-напредналият ефективен LLM, специализиран в разсъждения, математика и програмиране."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 - по-голям и по-умен модел в комплекта DeepSeek - е дестилиран в архитектурата Llama 70B. На базата на бенчмаркове и човешка оценка, този модел е по-умен от оригиналния Llama 70B, особено в задачи, изискващи математическа и фактическа точност."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 дестили<PERSON><PERSON><PERSON> модел, ба<PERSON><PERSON><PERSON><PERSON><PERSON> на Qwen2.5-Math-1.5B, оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 дестили<PERSON><PERSON><PERSON> модел, баз<PERSON><PERSON><PERSON><PERSON> на Qwen2.5-14B, оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "Серията DeepSeek-R1 оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт, надминавайки нивото на OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1 дестили<PERSON><PERSON><PERSON> модел, ба<PERSON><PERSON><PERSON><PERSON><PERSON> на Qwen2.5-Math-7B, оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт."}, "DeepSeek-V3": {"description": "DeepSeek-V3 е Mo<PERSON> модел, разработен от компанията DeepSeek. DeepSeek-V3 постига резултати в множество оценки, които надминават други отворени модели като Qwen2.5-72B и Llama-3.1-405B, като по отношение на производителност е наравно с водещите затворени модели в света като GPT-4o и Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite предлага изключително бърза реакция и по-добро съотношение цена-качество, осигурявайки по-гъвкави опции за различни сценарии на клиентите. Поддържа разсъждения и финна настройка с контекстен прозорец от 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite предлага изключително бърза реакция и по-добро съотношение цена-качество, осигурявайки по-гъвкави опции за различни сценарии на клиентите. Поддържа разсъждения и финна настройка с контекстен прозорец от 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite предлага изключително бърза реакция и по-добро съотношение цена-качество, осигурявайки по-гъвкави опции за различни сценарии на клиентите. Поддържа разсъждения и финна настройка с контекстен прозорец от 4k."}, "Doubao-pro-128k": {"description": "Най-ефективният основен модел, подходящ за обработка на сложни задачи, с отлични резултати в справки, обобщения, творчество, текстова класификация и ролеви игри. Поддържа разсъждения и финна настройка с контекстен прозорец от 128k."}, "Doubao-pro-32k": {"description": "Най-ефективният основен модел, подходящ за обработка на сложни задачи, с отлични резултати в справки, обобщения, творчество, текстова класификация и ролеви игри. Поддържа разсъждения и финна настройка с контекстен прозорец от 32k."}, "Doubao-pro-4k": {"description": "Най-ефективният основен модел, подходящ за обработка на сложни задачи, с отлични резултати в справки, обобщения, творчество, текстова класификация и ролеви игри. Поддържа разсъждения и финна настройка с контекстен прозорец от 4k."}, "DreamO": {"description": "DreamO е отворен модел за персонализирано генериране на изображения, съвместно разработен от ByteDance и Пекинския университет, с цел поддържане на мултизадачно генериране на изображения чрез унифицирана архитектура. Той използва ефективен комбиниран модел, който може да генерира високо съгласувани и персонализирани изображения според множество условия, зададени от потребителя, като идентичност, обект, стил и фон."}, "ERNIE-3.5-128K": {"description": "Флагманският модел на Baidu, разработен самостоятелно, е мащабен езиков модел, който обхваща огромно количество китайски и английски текстове. Той притежава мощни общи способности и може да отговори на почти всички изисквания за диалогови въпроси и отговори, генериране на съдържание и приложения с плъгини; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията за отговорите."}, "ERNIE-3.5-8K": {"description": "Флагманският модел на Baidu, разработен самостоятелно, е мащабен езиков модел, който обхваща огромно количество китайски и английски текстове. Той притежава мощни общи способности и може да отговори на почти всички изисквания за диалогови въпроси и отговори, генериране на съдържание и приложения с плъгини; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията за отговорите."}, "ERNIE-3.5-8K-Preview": {"description": "Флагманският модел на Baidu, разработен самостоятелно, е мащабен езиков модел, който обхваща огромно количество китайски и английски текстове. Той притежава мощни общи способности и може да отговори на почти всички изисквания за диалогови въпроси и отговори, генериране на съдържание и приложения с плъгини; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията за отговорите."}, "ERNIE-4.0-8K-Latest": {"description": "Флагманският модел на Baidu за изключително големи езикови модели, разработен самостоятелно, е напълно обновен в сравнение с ERNIE 3.5 и е широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията за отговори."}, "ERNIE-4.0-8K-Preview": {"description": "Флагманският модел на Baidu за изключително големи езикови модели, разработен самостоятелно, е напълно обновен в сравнение с ERNIE 3.5 и е широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията за отговори."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Патентованият флагмански модул на Baidu, изключително мащабен езиков модел, показващ отлични резултати и широко приложение в сложни сценарии. Поддържа автоматично свързване с плъгини на Baidu Search, гарантирайки актуалността на информацията. В сравнение с ERNIE 4.0, той представя по-добри резултати."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Флагманският модел на Baidu за изключително големи езикови модели, разработен самостоятелно, показва отлични резултати и е широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията за отговори. В сравнение с ERNIE 4.0, представянето му е по-добро."}, "ERNIE-Character-8K": {"description": "Специализиран модел на Baidu за големи езикови модели, разработен самостоятелно, подходящ за приложения като NPC в игри, клиентски разговори и ролеви игри, с по-изразителен и последователен стил на персонажите, по-силна способност за следване на инструкции и по-добра производителност при извеждане."}, "ERNIE-Lite-Pro-128K": {"description": "Лек модел на Baidu за големи езикови модели, разработен самостоятелно, който съчетава отлични резултати с производителност при извеждане, с по-добри резултати в сравнение с ERNIE Lite, подходящ за използване с AI ускорителни карти с ниска изчислителна мощ."}, "ERNIE-Speed-128K": {"description": "Най-новият модел на Baidu за големи езикови модели с висока производителност, разработен самостоятелно, с отлични общи способности, подходящ за основен модел за фина настройка, за по-добро справяне с конкретни проблеми, като същевременно предлага отлична производителност при извеждане."}, "ERNIE-Speed-Pro-128K": {"description": "Най-новият модел на Baidu за големи езикови модели с висока производителност, разработен самостоятелно, с отлични общи способности, по-добри резултати в сравнение с ERNIE Speed, подходящ за основен модел за фина настройка, за по-добро справяне с конкретни проблеми, като същевременно предлага отлична производителност при извеждане."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev е мултимоделен модел за генериране и редактиране на изображения, разработен от Black Forest Labs, базиран на архитектурата Rectified Flow Transformer с 12 милиарда параметри. Моделът е специализиран в генериране, реконструкция, подобряване и редактиране на изображения при зададени контекстуални условия. Той съчетава предимствата на контролираното генериране на дифузионни модели с контекстуалното моделиране на Transformer, поддържайки висококачествен изход и широко приложение в задачи като възстановяване, допълване и реконструкция на визуални сцени."}, "FLUX.1-dev": {"description": "FLUX.1-dev е отворен мултимодален езиков модел (Multimodal Language Model, MLLM), разработен от Black Forest Labs, оптимизиран за задачи с текст и изображения. Той интегрира разбиране и генериране на изображения и текст, базиран на напреднали големи езикови модели като Mistral-7B, с внимателно проектиран визуален енкодер и многостепенно фино настройване с инструкции, което позволява съвместна обработка на текст и изображения и сложни задачи за разсъждение."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) е иновативен модел, подходящ за приложения в множество области и сложни задачи."}, "HelloMeme": {"description": "HelloMeme е AI инструмент, който автоматично генерира мемета, анимирани GIF файлове или кратки видеоклипове въз основа на предоставени от вас изображения или действия. Не е необходимо да имате умения за рисуване или програмиране – просто подгответе референтни изображения и инструментът ще създаде красиви, забавни и стилово съгласувани съдържания."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full е отворен мултимодален голям модел за редактиране на изображения, разработен от HiDream.ai, базиран на напредналата архитектура Diffusion Transformer и съчетаващ мощни езикови способности (вграден LLaMA 3.1-8B-Instruct). Поддържа генериране на изображения, трансфер на стил, локално редактиране и прерисуване чрез естествени езикови команди, с изключителни умения за разбиране и изпълнение на текстово-изобразителни задачи."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled е лек модел за генериране на изображения от текст, оптимизиран чрез дистилация, който може бързо да създава висококачествени изображения, особено подходящ за среди с ограничени ресурси и задачи за реално време."}, "InstantCharacter": {"description": "InstantCharacter е персонализиран модел за генериране на персонажи без нужда от фино настройване, пуснат от AI екипа на Tencent през 2025 г. Целта му е да осигури висококачествено и консистентно генериране на персонажи в различни сцени. Моделът поддържа моделиране на персонаж само на базата на една референтна снимка и позволява гъвкаво пренасяне на персонажа в различни стилове, пози и фонове."}, "InternVL2-8B": {"description": "InternVL2-8B е мощен визуален езиков модел, който поддържа многомодално обработване на изображения и текст, способен да разпознава точно съдържанието на изображения и да генерира свързани описания или отговори."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B е мощен визуален езиков модел, който поддържа многомодално обработване на изображения и текст, способен да разпознава точно съдържанието на изображения и да генерира свързани описания или отговори."}, "Kolors": {"description": "Kolors е модел за генериране на изображения от текст, разработен от екипа Kolors на Kuaishou. Той е обучен с милиарди параметри и има значителни предимства в качеството на визуализация, разбирането на китайски семантичен контекст и рендирането на текст."}, "Kwai-Kolors/Kolors": {"description": "Kolors е голям модел за генериране на изображения от текст, базиран на латентна дифузия, разработен от екипа Kolors на Kuaishou. Обучен с милиарди двойки текст-изображение, моделът демонстрира значителни предимства в качеството на визуализация, точността на сложната семантика и рендирането на китайски и английски символи. Той поддържа вход на китайски и английски език и се представя отлично в разбирането и генерирането на специфично китайско съдържание."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Изключителни способности за визуално разсъждение върху изображения с висока резолюция, подходящи за приложения за визуално разбиране."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Напреднали способности за визуално разсъждение, подходящи за приложения на агенти за визуално разбиране."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Текстов модел с оптимизация за инструкции на Llama 3.1, проектиран за многоезични диалогови случаи, който показва отлични резултати на много налични отворени и затворени чат модели на общи индустриални бенчмаркове."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Текстов модел с оптимизация за инструкции на Llama 3.1, проектиран за многоезични диалогови случаи, който показва отлични резултати на много налични отворени и затворени чат модели на общи индустриални бенчмаркове."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Текстов модел с оптимизация за инструкции на Llama 3.1, проектиран за многоезични диалогови случаи, който показва отлични резултати на много налични отворени и затворени чат модели на общи индустриални бенчмаркове."}, "Meta-Llama-3.2-1B-Instruct": {"description": "Напреднал, водещ малък езиков модел с разбиране на езика, изключителни способности за разсъждение и генериране на текст."}, "Meta-Llama-3.2-3B-Instruct": {"description": "Напреднал, водещ малък езиков модел с разбиране на езика, изключителни способности за разсъждение и генериране на текст."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 е най-напредналият многоезичен отворен голям езиков модел от серията Llama, който предлага производителност, сравнима с 405B моделите, на изключително ниска цена. Базиран на структурата Transformer и подобрен чрез супервизирано фино настройване (SFT) и обучение с човешка обратна връзка (RLHF) за повишаване на полезността и безопасността. Неговата версия с оптимизация за инструкции е специално проектирана за многоезични диалози и показва по-добри резултати от много от наличните отворени и затворени чат модели на множество индустриални бенчмаркове. Краен срок за знанията е декември 2023 г."}, "MiniMax-M1": {"description": "Изцяло ново самостоятелно разработено модел за разсъждение. Световен лидер: 80K вериги на мислене x 1M вход, с резултати, сравними с водещите модели в чужбина."}, "MiniMax-Text-01": {"description": "В серията модели MiniMax-01 направихме смели иновации: за първи път реализирахме мащабно линейно внимание, традиционната архитектура на Transformer вече не е единственият избор. Параметрите на този модел достигат 4560 милиарда, с единична активация от 45.9 милиарда. Общата производителност на модела е на нивото на водещите модели в чужбина, като същевременно ефективно обработва глобалния контекст от 4 милиона токена, което е 32 пъти повече от GPT-4o и 20 пъти повече от Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 е мащабен модел за разсъждение с отворени тегла и смесено внимание, с 456 милиарда параметри, като всеки токен активира около 45.9 милиарда параметри. Моделът поддържа естествено контекст с дължина до 1 милион токена и чрез механизма за светкавично внимание спестява 75% от изчисленията при задачи с генериране на 100 хиляди токена в сравнение с DeepSeek R1. Освен това MiniMax-M1 използва MoE (смесен експертен) архитектура, комбинирайки CISPO алгоритъм и ефективно обучение с подсилване с дизайн на смесено внимание, постигащи водещи в индустрията резултати при дълги входни разсъждения и реални софтуерни инженерни сценарии."}, "Moonshot-Kimi-K2-Instruct": {"description": "Общ брой параметри 1 трилион, активирани параметри 32 милиарда. Сред немисловните модели постига водещи резултати в областта на актуални знания, математика и кодиране, с по-добри възможности за универсални агентски задачи. Специално оптимизиран за агентски задачи, не само отговаря на въпроси, но и може да предприема действия. Най-подходящ за импровизирани, универсални разговори и агентски преживявания, модел с рефлексна скорост без нужда от дълго мислене."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) е модел с висока точност за инструкции, подходящ за сложни изчисления."}, "OmniConsistency": {"description": "OmniConsistency подобрява консистентността на стил и генерализацията в задачи за преобразуване на изображения чрез въвеждане на големи дифузионни трансформъри (DiTs) и двойни стилизирани данни, като предотвратява деградация на стила."}, "Phi-3-medium-128k-instruct": {"description": "Същият модел Phi-3-medium, но с по-голям размер на контекста за RAG или малко подканване."}, "Phi-3-medium-4k-instruct": {"description": "Модел с 14B параметри, предлагащ по-добро качество от Phi-3-mini, с акцент върху висококачествени, плътни на разсъждения данни."}, "Phi-3-mini-128k-instruct": {"description": "Същият модел Phi-3-mini, но с по-голям размер на контекста за RAG или малко подканване."}, "Phi-3-mini-4k-instruct": {"description": "Най-малкият член на семейството Phi-3. Оптимизи<PERSON><PERSON>н както за качество, така и за ниска латентност."}, "Phi-3-small-128k-instruct": {"description": "Същият модел Phi-3-small, но с по-голям размер на контекста за RAG или малко подканване."}, "Phi-3-small-8k-instruct": {"description": "Модел с 7B параметри, предлагащ по-добро качество от Phi-3-mini, с акцент върху висококачествени, плътни на разсъждения данни."}, "Phi-3.5-mini-instruct": {"description": "Актуализи<PERSON><PERSON>на версия на модела Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "Актуа<PERSON><PERSON>з<PERSON><PERSON><PERSON>на версия на модела Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct е голям езиков модел с параметри 7B от серията Qwen2, специално настроен за инструкции. Моделът е базиран на архитектурата Transformer и използва технологии като SwiGLU активационна функция, QKV отклонение за внимание и групова внимание. Той може да обработва големи входни данни. Моделът показва отлични резултати в множество бенчмаркове за разбиране на езика, генериране, многоезични способности, кодиране, математика и разсъждения, надминавайки повечето отворени модели и показвайки конкурентоспособност на определени задачи в сравнение с патентовани модели. Qwen2-7B-Instruct показва значителни подобрения в множество оценки в сравнение с Qwen1.5-7B-Chat."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct е един от най-новите големи езикови модели, публикувани от Alibaba Cloud. Този 7B модел показва значителни подобрения в областите на кодирането и математиката. Моделът предлага многоезична поддръжка, обхващаща над 29 езика, включително китайски, английски и др. Моделът показва значителни подобрения в следването на инструкции, разбирането на структурирани данни и генерирането на структурирани изходи (особено JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct е най-новата версия на серията големи езикови модели, специфични за код, публикувана от Alibaba Cloud. Моделът значително подобрява способностите за генериране на код, разсъждения и корекции, след като е обучен с 55 трилиона токена на базата на Qwen2.5. Той не само подобрява кодовите умения, но и запазва предимствата в математиката и общите способности. Моделът предоставя по-пълна основа за практическите приложения като кодови интелигентни агенти."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL е нов член от серията Qwen, който разполага с мощни възможности за визуално разбиране. Той може да анализира текст, диаграми и оформление в изображения, да разбира дълги видеоклипове и да улавя събития. Може да извършва логически изводи, да работи с инструменти, поддържа локализиране на обекти в различни формати и генериране на структуриран изход. Оптимизиран е с динамична резолюция и честота на кадрите за разбиране на видео и подобрена ефективност на визуалния кодиращ модул."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking е отворен визуално-езиков модел (VLM), съвместно разработен от Zhizhu AI и KEG лабораторията на Университета Цинхуа, специално проектиран за обработка на сложни мултимодални когнитивни задачи. Моделът е базиран на основния модел GLM-4-9B-0414 и значително подобрява способностите си за кросмодално разсъждение и стабилност чрез въвеждането на механизма за разсъждение „верига на мисълта“ (Chain-of-Thought) и използването на стратегии за подсилено обучение."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat е отворената версия на предварително обучен модел от серията GLM-4, пусната от Zhizhu AI. Моделът показва отлични резултати в семантика, математика, разсъждения, код и знания. Освен че поддържа многократни разговори, GLM-4-9B-Chat предлага и напреднали функции като уеб браузинг, изпълнение на код, извикване на персонализирани инструменти (Function Call) и разсъждения с дълги текстове. Моделът поддържа 26 езика, включително китайски, английски, японски, корейски и немски. В множество бенчмаркове, GLM-4-9B-Chat показва отлична производителност, като AlignBench-v2, MT-Bench, MMLU и C-Eval. Моделът поддържа максимална контекстна дължина от 128K, подходящ за академични изследвания и търговски приложения."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 е модел за инференция, управляван от обучение с подсилване (RL), който решава проблемите с повторяемостта и четимостта в моделите. Преди RL, DeepSeek-R1 въвежда данни за студен старт, за да оптимизира допълнително производителността на инференцията. Той показва сравними резултати с OpenAI-o1 в математически, кодови и инференционни задачи и подобрява общата ефективност чрез внимателно проектирани методи на обучение."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B е модел, получен чрез дистилация на знания от Qwen2.5-Math-7B. Този модел е фино настроен с 800 000 избрани проби, генерирани от DeepSeek-R1, и демонстрира изключителни способности за разсъждение. Той се представя отлично в множество тестове, постигайки 92,8% точност в MATH-500, 55,5% успеваемост в AIME 2024 и рейтинг от 1189 в CodeForces, показвайки силни математически и програмистки способности за модел с мащаб 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 е модел на езика с 6710 милиарда параметри, който използва архитектура на смесени експерти (MoE) с много глави на потенциално внимание (MLA) и стратегия за баланс на натоварването без помощни загуби, оптимизираща производителността на инференцията и обучението. Чрез предварително обучение на 14.8 трилиона висококачествени токени и последващо супервизирано фино настройване и обучение с подсилване, DeepSeek-V3 надминава производителността на други отворени модели и е близо до водещите затворени модели."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 е базов модел с MoE архитектура с изключителни кодови и агентски способности, с общо 1 трилион параметри и 32 милиарда активирани параметри. В бенчмаркове за общо знание, програмиране, математика и агентски задачи моделът K2 превъзхожда други водещи отворени модели."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview е иновативен модел за обработка на естествен език, способен да обработва ефективно сложни задачи за генериране на диалог и разбиране на контекста."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview е изследователски модел, разработен от екипа на Qwen, който се фокусира върху визуалните способности за извеждане и притежава уникални предимства в разбирането на сложни сцени и решаването на визуално свързани математически проблеми."}, "Qwen/QwQ-32B": {"description": "QwQ е моделът за изводи от серията Qwen. В сравнение с традиционните модели за оптимизация на инструкции, QwQ притежава способности за разсъждение и извод, което позволява значително подобряване на производителността в задачи от по-ниско ниво, особено при решаване на трудни проблеми. QwQ-32B е среден модел за изводи, който постига конкурентоспособна производителност в сравнение с най-съвременните модели за изводи (като DeepSeek-R1, o1-mini). Този модел използва технологии като RoPE, SwiGLU, RMSNorm и Attention QKV bias, с 64 слоя в мрежовата структура и 40 Q внимание глави (в архитектурата GQA KV е 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview е най-новият експериментален изследователски модел на Qwen, който се фокусира върху подобряване на AI разсъдъчните способности. Чрез изследване на сложни механизми като езикови смеси и рекурсивно разсъждение, основните предимства включват мощни аналитични способности, математически и програмистки умения. В същото време съществуват проблеми с езиковото превключване, цикли на разсъждение, съображения за безопасност и разлики в други способности."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 е напреднал универсален езиков модел, поддържащ множество типове инструкции."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct е голям езиков модел с параметри 72B от серията Qwen2, специално настроен за инструкции. Моделът е базиран на архитектурата Transformer и използва технологии като SwiGLU активационна функция, QKV отклонение за внимание и групова внимание. Той може да обработва големи входни данни. Моделът показва отлични резултати в множество бенчмаркове за разбиране на езика, генериране, многоезични способности, кодиране, математика и разсъждения, надминавайки повечето отворени модели и показвайки конкурентоспособност на определени задачи в сравнение с патентовани модели."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL е най-новата итерация на модела Qwen-VL, който е постигнал водещи резултати в тестовете за визуално разбиране."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 е нова серия от големи езикови модели, проектирана да оптимизира обработката на инструкции."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 е нова серия от големи езикови модели, проектирана да оптимизира обработката на инструкции."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Голям езиков модел, разработен от екипа на Alibaba Cloud Tongyi Qianwen"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 е нова серия от големи езикови модели с по-силни способности за разбиране и генериране."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 е нова серия от големи езикови модели, проектирана да оптимизира обработката на инструкти."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 е нова серия от големи езикови модели, проектирана да оптимизира обработката на инструкции."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 е нова серия от големи езикови модели, проектирана да оптимизира обработката на инструкти."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder се фокусира върху писането на код."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct е най-новата версия на серията големи езикови модели, специфични за код, публикувана от Alibaba Cloud. Моделът значително подобрява способностите за генериране на код, разсъждения и корекции, след като е обучен с 55 трилиона токена на базата на Qwen2.5. Той не само подобрява кодовите умения, но и запазва предимствата в математиката и общите способности. Моделът предоставя по-пълна основа за практическите приложения като кодови интелигентни агенти."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct е многомодален голям модел, разработен от екипа на Tongyi Qianwen, част от серията Qwen2.5-VL. Този модел не само разпознава отлично обичайни обекти, но също така анализира текст, диаграми, икони, графики и оформление в изображения. Той може да функционира като визуален агент, способен да разсъждава и динамично да управлява инструменти, с възможности за работа с компютри и мобилни устройства. Освен това, моделът може точно да локализира обекти в изображения и да генерира структурирани изходи за фактури, таблици и други. В сравнение с предходния модел Qwen2-VL, тази версия е подобрена чрез усилено обучение в областта на математиката и способностите за решаване на проблеми, като стилът на отговорите е по-съобразен с човешките предпочитания."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL е визуален езиков модел от серията Qwen2.5. Този модел има значителни подобрения в различни аспекти: разполага с по-добри възможности за визуално разбиране, може да разпознава обикновени обекти, да анализира текст, диаграми и оформление; като визуален агент може да разсъждава и динамично да насочва използването на инструменти; поддържа разбиране на дълги видеоклипове с продължителност над 1 час и улавяне на ключови събития; може да локализира точно обекти в изображения чрез генериране на ограничителни кутии или точки; поддържа генериране на структуриран изход, особено подходящ за сканирани данни като фактури и таблици."}, "Qwen/Qwen3-14B": {"description": "Qwen3 е ново поколение модел на Tongyi Qianwen с значително подобрени способности, достигащи водещо ниво в индустрията в разсъждения, общи, агенти и многоезични основни способности, и поддържа превключване на режим на мислене."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 е ново поколение модел на Tongyi Qianwen с значително подобрени способности, достигащи водещо ниво в индустрията в разсъждения, общи, агенти и многоезични основни способности, и поддържа превключване на режим на мислене."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 е флагмански голям езиков модел с хибридни експерти (MoE) от серията Qwen3, разработен от екипа на Alibaba Cloud Tongyi Qianwen. Моделът има общо 235 милиарда параметри, като при всяко извеждане се активират 22 милиарда. Той е обновена версия на Qwen3-235B-A22B в не-мисловен режим, със значителни подобрения в следването на инструкции, логическо разсъждение, разбиране на текст, математика, наука, програмиране и използване на инструменти. Моделът също така разширява покритието на многоезикови дългоопашати знания и по-добре се адаптира към потребителските предпочитания в субективни и отворени задачи, за да генерира по-полезен и качествен текст."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 е член на серията големи езикови модели Qwen3, разработен от екипа на Alibaba Tongyi Qianwen, фокусиран върху сложни задачи за разсъждение. Моделът използва MoE архитектура с общо 235 милиарда параметри, като при обработка на всеки токен се активират около 22 милиарда, което повишава изчислителната ефективност без да се губи мощност. Като специализиран „мисловен“ модел, той постига значителни подобрения в логическо разсъждение, математика, наука, програмиране и академични бенчмаркове, достигайки водещи нива сред отворените мисловни модели. Освен това подобрява общите способности като следване на инструкции, използване на инструменти и генериране на текст, и поддържа нативно разбиране на дълги контексти до 256K токена, подходящ за дълбоко разсъждение и обработка на дълги документи."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 е ново поколение модел на Tongyi Qianwen с значително подобрени способности, достигащи водещо ниво в индустрията в разсъждения, общи, агенти и многоезични основни способности, и поддържа превключване на режим на мислене."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 е обновена версия на Qwen3-30B-A3B в режим без мислене. Това е хибриден експертен (MoE) модел с общо 30,5 милиарда параметри и 3,3 милиарда активни параметри. Моделът е получил ключови подобрения в множество аспекти, включително значително подобрена способност за следване на инструкции, логическо разсъждение, разбиране на текст, математика, наука, кодиране и използване на инструменти. Освен това, той постига съществен напредък в покритието на дългоопашатите знания на многоезично ниво и по-добре се съгласува с предпочитанията на потребителите при субективни и отворени задачи, което позволява генериране на по-полезни отговори и по-висококачествен текст. Освен това, способността му за разбиране на дълги текстове е увеличена до 256K. Този модел поддържа само режим без мислене и в изхода му не се генерират тагове `<think></think>`."}, "Qwen/Qwen3-32B": {"description": "Qwen3 е ново поколение модел на Tongyi Qianwen с значително подобрени способности, достигащи водещо ниво в индустрията в разсъждения, общи, агенти и многоезични основни способности, и поддържа превключване на режим на мислене."}, "Qwen/Qwen3-8B": {"description": "Qwen3 е ново поколение модел на Tongyi Qianwen с значително подобрени способности, достигащи водещо ниво в индустрията в разсъждения, общи, агенти и многоезични основни способности, и поддържа превключване на режим на мислене."}, "Qwen2-72B-Instruct": {"description": "Qwen2 е най-новата серия на модела <PERSON>wen, поддържаща 128k контекст. В сравнение с текущите най-добри отворени модели, Qwen2-72B значително надминава водещите модели в области като разбиране на естествен език, знания, код, математика и многоезичност."}, "Qwen2-7B-Instruct": {"description": "Qwen2 е най-новата серия на модела <PERSON>wen, способен да надмине оптималните отворени модели с равен размер или дори по-големи модели. Qwen2 7B постига значителни предимства в множество тестове, особено в разбирането на код и китайския език."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B е мощен визуален езиков модел, който поддържа многомодално обработване на изображения и текст, способен точно да разпознава съдържанието на изображения и да генерира свързани описания или отговори."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct е голям езиков модел с 14 милиарда параметри, с отлично представяне, оптимизиран за китайски и многоезични сценарии, поддържа интелигентни въпроси и отговори, генериране на съдържание и други приложения."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct е голям езиков модел с 32 милиарда параметри, с балансирано представяне, оптимизиран за китайски и многоезични сценарии, поддържа интелигентни въпроси и отговори, генериране на съдържание и други приложения."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct поддържа 16k контекст, генерира дълги текстове над 8K. Поддържа функция за извикване и безпроблемна интеграция с външни системи, значително увеличаваща гъвкавостта и разширяемостта. Моделът има значително увеличени знания и значително подобрени способности в кодиране и математика, с поддръжка на над 29 езика."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct е голям езиков модел с 7 милиарда параметри, който поддържа безпроблемно взаимодействие с функции и външни системи, значително увеличавайки гъвкавостта и разширяемостта. Оптимизиран за китайски и многоезични сценарии, поддържа интелигентни въпроси и отговори, генериране на съдържание и други приложения."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct е модел за програмиране, базиран на мащабно предварително обучение, с мощни способности за разбиране и генериране на код, способен ефективно да обработва различни програмни задачи, особено подходящ за интелигентно писане на код, автоматично генериране на скриптове и отговори на програмни въпроси."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct е голям езиков модел, проектиран специално за генериране на код, разбиране на код и ефективни сценарии за разработка, с водеща в индустрията параметрична стойност от 32B, способен да отговори на разнообразни програмни нужди."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B е MoE (хибриден експертен модел), който въвежда „хибриден режим на разсъждение“, позволяващ на потребителите безпроблемно превключване между „режим мислене“ и „режим без мислене“. Поддържа разбиране и разсъждение на 119 езика и диалекта и разполага с мощни възможности за извикване на инструменти. В множество базови тестове за общи способности, кодиране, математика, многоезичност, знания и разсъждение, той може да се конкурира с водещите големи модели на пазара като DeepSeek R1, OpenAI o1, o3-mini, Grok 3 и Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B е плътен модел (Dense Model), който въвежда „хибриден режим на разсъждение“, позволяващ на потребителите безпроблемно превключване между „режим мислене“ и „режим без мислене“. Благодарение на подобрения в архитектурата на модела, увеличени тренировъчни данни и по-ефективни методи за обучение, общата производителност е сравнима с тази на Qwen2.5-72B."}, "SenseChat": {"description": "Основна версия на модела (V4), с контекстна дължина 4K, с мощни общи способности."}, "SenseChat-128K": {"description": "Основна версия на модела (V4), с контекстна дължина 128K, показваща отлични резултати в задачи за разбиране и генериране на дълги текстове."}, "SenseChat-32K": {"description": "Основна версия на модела (V4), с контекстна дължина 32K, гъвкаво приложима в различни сцени."}, "SenseChat-5": {"description": "Най-новата версия на модела (V5.5), с контекстна дължина 128K, значително подобрена способност в области като математическо разсъждение, английски разговори, следване на инструкции и разбиране на дълги текстове, сравнима с GPT-4o."}, "SenseChat-5-1202": {"description": "Базирана на версия V5.5, последната версия показва значително подобрение в основните умения на китайски и английски, чат, научни знания, хуманитарни знания, писане, математическа логика и контрол на броя думи."}, "SenseChat-5-Cantonese": {"description": "С контекстна дължина 32K, надминава GPT-4 в разбирането на разговори на кантонски, сравним с GPT-4 Turbo в множество области като знания, разсъждение, математика и писане на код."}, "SenseChat-5-beta": {"description": "Част от производителността е надминала SenseCat-5-1202"}, "SenseChat-Character": {"description": "Стандартна версия на модела, с контекстна дължина 8K, с висока скорост на отговор."}, "SenseChat-Character-Pro": {"description": "Премиум версия на модела, с контекстна дължина 32K, с напълно подобрени способности, поддържаща разговори на китайски/английски."}, "SenseChat-Turbo": {"description": "Подходящ за бързи въпроси и отговори, сцени на фино настройване на модела."}, "SenseChat-Turbo-1202": {"description": "Това е най-новият лек модел, който достига над 90% от способностите на пълния модел, значително намалявайки разходите за изчисление."}, "SenseChat-Vision": {"description": "Най-новата версия на модела (V5.5) поддържа вход с множество изображения и напълно реализира оптимизация на основните способности на модела, с голямо подобрение в разпознаването на свойства на обекти, пространствени отношения, разпознаване на действия и събития, разбиране на сцени, разпознаване на емоции, логическо разсъждение и генериране на текст."}, "SenseNova-V6-5-Pro": {"description": "Чрез цялостно обновяване на мултимодалните, езиковите и разсъждаващите данни и оптимизация на тренировъчните стратегии, новият модел постига значително подобрение в мултимодалното разсъждение и способността за следване на общи инструкции. Поддържа контекстен прозорец до 128k и показва отлични резултати в специализирани задачи като OCR и разпознаване на културно-туристически IP."}, "SenseNova-V6-5-Turbo": {"description": "Чрез цялостно обновяване на мултимодалните, езиковите и разсъждаващите данни и оптимизация на тренировъчните стратегии, новият модел постига значително подобрение в мултимодалното разсъждение и способността за следване на общи инструкции. Поддържа контекстен прозорец до 128k и показва отлични резултати в специализирани задачи като OCR и разпознаване на културно-туристически IP."}, "SenseNova-V6-Pro": {"description": "Постигане на родно обединение на възможностите за изображения, текст и видео, преодолявайки ограниченията на традиционните мултимодални разделения, спечелвайки двойна титла в оценките OpenCompass и SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "Комбинира визуално и езиково дълбоко разсъждение, осъществявайки бавно мислене и задълбочен анализ, представяйки пълния процес на мисловната верига."}, "SenseNova-V6-Turbo": {"description": "Постигане на родно обединение на възможностите за изображения, текст и видео, преодолявайки ограниченията на традиционните мултимодални разделения, с водещи постижения в основни измерения като мултимодални базови способности и езикови базови способности, съчетавайки хуманитарни и технически умения, многократно класиран на първото ниво както в национални, така и в международни оценки."}, "Skylark2-lite-8k": {"description": "Cloud Lark (Skylark) второ поколение модел, Skylark2-lite предлага висока скорост на отговор, подходяща за сценарии с високи изисквания за реално време, чувствителни към разходите и с по-ниски изисквания за прецизност, с дължина на контекстовия прозорец 8k."}, "Skylark2-pro-32k": {"description": "Cloud Lark (Skylark) второ поколение модел, версията Skylark2-pro предлага висока прецизност на модела, подходяща за по-сложни текстови генерации, като например генериране на текстове за специализирани области, писане на романи и висококачествени преводи, с дължина на контекстовия прозорец 32k."}, "Skylark2-pro-4k": {"description": "Cloud Lark (Skylark) второ поколение модел, версията Skylark2-pro предлага висока прецизност на модела, подходяща за по-сложни текстови генерации, като например генериране на текстове за специализирани области, писане на романи и висококачествени преводи, с дължина на контекстовия прозорец 4k."}, "Skylark2-pro-character-4k": {"description": "Cloud Lark (Skylark) второ поколение модел, Skylark2-pro-character предоставя отлични способности за ролеви игри и чат, специализирани в адаптиране на стилове на персонажи, които естествено взаимодействат с потребителите, идеален за изграждане на чат-ботове, виртуални асистенти и онлайн обслужване с висока скорост на отговор."}, "Skylark2-pro-turbo-8k": {"description": "Cloud Lark (Skylark) второ поколение модел, Skylark2-pro-turbo-8k предлага по-бърза обработка и по-ниски разходи, с дължина на контекстовия прозорец 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 е новото поколение отворен модел от серията GLM, с 32 милиарда параметри. Производителността на този модел е сравнима с GPT серията на OpenAI и V3/R1 серията на DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 е малкият модел от серията GLM, с 9 милиарда параметри. Този модел наследява техническите характеристики на GLM-4-32B серията, но предлага по-леко решение за внедряване. Въпреки по-малкия си размер, GLM-4-9B-0414 все още показва отлични способности в генерирането на код, уеб дизайн, генериране на SVG графики и писане на базата на търсене."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking е отворен визуално-езиков модел (VLM), съвместно разработен от Zhizhu AI и KEG лабораторията на Университета Цинхуа, специално проектиран за обработка на сложни мултимодални когнитивни задачи. Моделът е базиран на основния модел GLM-4-9B-0414 и значително подобрява способностите си за кросмодално разсъждение и стабилност чрез въвеждането на механизма за разсъждение „верига на мисълта“ (Chain-of-Thought) и използването на стратегии за подсилено обучение."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 е модел за разсъждение с дълбоки способности за разсъждение. Този модел е разработен на базата на GLM-4-32B-0414 чрез студен старт и разширено обучение с подсилване и е допълнително обучен в задачи по математика, код и логика. В сравнение с основния модел, GLM-Z1-32B-0414 значително подобрява математическите способности и способността за решаване на сложни задачи."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 е малкият модел от серията GLM, с 9 милиарда параметри, но запазва удивителни способности, докато следва традицията на отворен код. Въпреки по-малкия си размер, моделът все още показва отлични резултати в математическите разсъждения и общите задачи, като общата му производителност е на водещо ниво сред модели с подобен размер."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 е модел за дълбочинно разсъждение с дълбоки способности за разсъждение (сравним с Deep Research на OpenAI). За разлика от типичните модели за дълбочинно разсъждение, моделът за разсъждение използва по-дълго време за дълбочинно разсъждение, за да решава по-отворени и сложни проблеми."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B е отворен код версия, предоставяща оптимизирано изживяване в разговорните приложения."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B е първият голям модел за разсъждение с дълъг контекст, обучен чрез усилено обучение (LRM), специално оптимизиран за задачи с дълги текстове. Моделът използва прогресивна рамка за разширяване на контекста чрез усилено обучение, осигурявайки стабилен преход от кратък към дълъг контекст. В седем базови теста за въпроси и отговори с дълъг контекст QwenLong-L1-32B превъзхожда водещи модели като OpenAI-o3-mini и Qwen3-235B-A22B, с производителност, сравнима с Claude-3.7-Sonnet-Thinking. Моделът е особено силен в математическо, логическо и многократно разсъждение."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B значително подобрява математическата логика и способностите в кодирането, като запазва отличните общи езикови способности на оригиналната серия модели, чрез инкрементално обучение с 500 милиарда висококачествени токени."}, "abab5.5-chat": {"description": "Насочена към производствени сценарии, поддържаща обработка на сложни задачи и ефективно генериране на текст, подходяща за професионални приложения."}, "abab5.5s-chat": {"description": "Специално проектирана за диалогови сценарии на китайски, предлагаща висококачествено генериране на диалози на китайски, подходяща за множество приложения."}, "abab6.5g-chat": {"description": "Специално проектирана за многоезични диалогови системи, поддържаща висококачествено генериране на диалози на английски и много други езици."}, "abab6.5s-chat": {"description": "Подходяща за широк спектър от задачи за обработка на естествен език, включително генериране на текст, диалогови системи и др."}, "abab6.5t-chat": {"description": "Оптимизирана за диалогови сценарии на китайски, предлагаща плавно и съответстващо на китайските изразни навици генериране на диалози."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 е авангарден голям езиков модел, оптимизиран чрез подсилено обучение и данни за студен старт, с отлични способности в разсъжденията, математиката и програмирането."}, "accounts/fireworks/models/deepseek-v3": {"description": "Мо<PERSON>ен езиков модел Mixture-of-Experts (MoE) от Deepseek, с общ брой параметри 671B, активиращи 37B параметри на всеки токен."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Llama 3 70B модел за инструкции, специално оптимизиран за многоезични диалози и разбиране на естествен език, с производителност, превъзхождаща повечето конкурентни модели."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Llama 3 8B модел за инструкции, оптимизиран за диалози и многоезични задачи, с изключителна производителност и ефективност."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Llama 3 8B модел за инструкции (HF версия), с резултати, съвпадащи с официалната реализация, предлагаща висока последователност и съвместимост между платформите."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Llama 3.1 405B модел за инструкции, с огромен брой параметри, подходящ за сложни задачи и следване на инструкции в сценарии с високо натоварване."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Llama 3.1 70B модел за инструкции, предлагащ изключителни способности за разбиране и генериране на естествен език, идеален за диалогови и аналитични задачи."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Llama 3.1 8B модел за инструкции, оптимизиран за многоезични диалози, способен да надмине повечето отворени и затворени модели на общи индустриални стандарти."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Моделът за разсъждение по изображения с 11B параметри на Meta е оптимизиран за визуално разпознаване, разсъждение по изображения, описание на изображения и отговаряне на общи въпроси относно изображения. Моделът може да разбира визуални данни, като графики и таблици, и свързва визуалните данни с текстовите описания на детайлите на изображенията."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Моделът Llama 3.2 3B е лека многоезична разработка от Meta. Този модел е проектиран да подобри ефективността, предоставяйки значителни подобрения в забавянето и разходите в сравнение с по-големи модели. Примерни случаи на ползване включват заявки, пренаписване на подканвания и подпомагане на писането."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Моделът за разсъждение по изображения с 90B параметри на Meta е оптимизиран за визуално разпознаване, разсъждение по изображения, описание на изображения и отговаряне на общи въпроси относно изображения. Моделът може да разбира визуални данни, като графики и таблици, и свързва визуалните данни с текстовите описания на детайлите на изображенията."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct е актуализир<PERSON>на версия на Llama 3.1 70B от декември. Този модел е подобрен на базата на Llama 3.1 70B (пуснат през юли 2024 г.), с подобрени възможности за извикване на инструменти, поддръжка на многоезичен текст, математика и програмиране. Моделът постига водещи в индустрията резултати в области като разсъждение, математика и следване на инструкции, и предлага производителност, подобна на 3.1 405B, с значителни предимства в скоростта и разходите."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Модел с 24B параметри, предлага<PERSON> водещи в индустрията способности, сравними с по-големите модели."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B модел за инструкции, с голям брой параметри и архитектура с множество експерти, осигуряваща всестранна поддръжка за ефективна обработка на сложни задачи."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B модел за инструкции, архитектура с множество експерти, предлагаща ефективно следване и изпълнение на инструкции."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMax L2 13B модел, комбинир<PERSON>щ новаторски технологии за интеграция, специализиран в разказване на истории и ролеви игри."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision модел за инструкции, лек мултимодален модел, способен да обработва сложна визуална и текстова информация, с високи способности за разсъждение."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "QwQ моделът е експериментален изследователски модел, разработен от екипа на Qwen, който се фокусира върху подобряване на AI разсъдъчните способности."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "72B версия на модела Qwen-VL е последната итерация на Alibaba, представляваща иновации от последната година."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 е серия от езикови модели, разработени от екипа на Alibaba Cloud Qwen, които съдържат само декодери. Тези модели предлагат различни размери, включително 0.5B, 1.5B, 3B, 7B, 14B, 32B и 72B, и разполагат с базови (base) и инструкти (instruct) варианти."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct е най-новата версия на серията големи езикови модели, специфични за код, публикувана от Alibaba Cloud. Моделът значително подобрява способностите за генериране на код, разсъждения и корекции, след като е обучен с 55 трилиона токена на базата на Qwen2.5. Той не само подобрява кодовите умения, но и запазва предимствата в математиката и общите способности. Моделът предоставя по-пълна основа за практическите приложения като кодови интелигентни агенти."}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Large модел, предлагащ изключителни способности за многоезична обработка, подходящ за различни задачи по генериране и разбиране на език."}, "ai21-jamba-1.5-large": {"description": "Многоезичен модел с 398B параметри (94B активни), предлага<PERSON> контекстен прозорец с дължина 256K, извикване на функции, структурирани изходи и генериране на основа."}, "ai21-jamba-1.5-mini": {"description": "Многоезичен модел с 52B параметри (12B активни), предлага<PERSON> контекстен прозорец с дължина 256K, извикване на функции, структурирани изходи и генериране на основа."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Многоезичен модел с 398 милиарда параметри (94 милиарда активни), предлагащ прозорец за дълъг контекст от 256K, извикване на функции, структурирани изходи и генериране, базирано на факти."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Многоезичен модел с 52 милиарда параметри (12 милиарда активни), предлагащ прозорец за дълъг контекст от 256K, извикване на функции, структурирани изходи и генериране, базирано на факти."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet повишава индустриалните стандарти, с производителност, надвишаваща конкурентните модели и Claude 3 Opus, с отлични резултати в широки оценки, като същевременно предлага скорост и разходи на нашите модели от средно ниво."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet повишава индустриалните стандарти, с производителност, надминаваща конкурентните модели и Claude 3 Opus, показвайки отлични резултати в широки оценки, като същевременно предлага скорост и разходи, характерни за нашите модели от среден клас."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku е най-бързият и компактен модел на Anthropic, предлагащ почти мигновена скорост на отговор. Той може бързо да отговаря на прости запитвания и заявки. Клиентите ще могат да изградят безпроблемно AI изживяване, имитиращо човешко взаимодействие. Claude 3 Haiku може да обработва изображения и да връща текстови изходи, с контекстуален прозорец от 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus е най-мощният AI модел на Anthropic, с най-съвременна производителност при високо сложни задачи. Той може да обработва отворени подсказки и непознати сценарии, с отлична плавност и човешко разбиране. Claude 3 Opus демонстрира предимствата на генериращия AI. Claude 3 Opus може да обработва изображения и да връща текстови изходи, с контекстуален прозорец от 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet на Anthropic постига идеален баланс между интелигентност и скорост - особено подходящ за корпоративни работни натоварвания. Той предлага максимална полезност на цена под конкурентите и е проектиран да бъде надежден и издръжлив основен модел, подходящ за мащабируеми AI внедрения. Claude 3 Sonnet може да обработва изображения и да връща текстови изходи, с контекстуален прозорец от 200K."}, "anthropic.claude-instant-v1": {"description": "Бърз, икономичен и все пак много способен модел, който може да обработва редица задачи, включително ежедневни разговори, текстов анализ, обобщение и въпроси и отговори на документи."}, "anthropic.claude-v2": {"description": "Anthropic демонстрира висока способност в широк спектър от задачи, от сложни разговори и генериране на креативно съдържание до следване на подробни инструкции."}, "anthropic.claude-v2:1": {"description": "Актуализирана версия на Claude 2, с двойно по-голям контекстуален прозорец и подобрения в надеждността, процента на халюцинации и точността, основана на доказателства, в контексти с дълги документи и RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku е най-бързият и компактен модел на Anthropic, проектиран за почти мигновени отговори. Той предлага бърза и точна насочена производителност."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus е най-мощният модел на Anthropic, предназначен за обработка на изключително сложни задачи. Той се отличава с изключителна производителност, интелигентност, гладкост и разбиране."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku е най-бързият следващ модел на Anthropic. В сравнение с Claude 3 Haiku, Claude 3.5 Haiku показва подобрения в различни умения и надминава предишното поколение най-голям модел Claude 3 Opus в много интелектуални бенчмаркове."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet предлага способности, надхвърлящи Opus, и по-бърза скорост в сравнение с Sonnet, като същевременно запазва същата цена. Sonnet е особено силен в програмирането, науката за данни, визуалната обработка и агентските задачи."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet е най-интелигентният модел на Anthropic до момента и е първият хибриден модел за разсъждение на пазара. Claude 3.7 Sonnet може да генерира почти мигновени отговори или удължено стъпково мислене, което позволява на потребителите ясно да видят тези процеси. Sonnet е особено добър в програмирането, науката за данни, визуалната обработка и агентските задачи."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 е най-мощният модел на Anthropic за справяне с изключително сложни задачи. Той се отличава с изключителна производителност, интелигентност, плавност и разбиране."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 може да генерира почти мигновени отговори или удължено стъпково мислене, което потребителите могат ясно да проследят. Потребителите на API също така имат прецизен контрол върху времето за мислене на модела."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B е голям езиков модел с 72 милиарда параметри и 16 милиарда активирани параметри, базиран на архитектурата с групирани смесени експерти (MoGE). Той групира експертите по време на избора им и ограничава активацията на токените да активират равен брой експерти във всяка група, което осигурява балансирано натоварване на експертите и значително подобрява ефективността на разгръщане на модела на платформата Ascend."}, "aya": {"description": "Aya 23 е многозначен модел, предста<PERSON><PERSON><PERSON> от Cohere, поддържащ 23 езика, предоставяйки удобство за многоезични приложения."}, "aya:35b": {"description": "Aya 23 е многозначен модел, предста<PERSON><PERSON><PERSON> от Cohere, поддържащ 23 езика, предоставяйки удобство за многоезични приложения."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B е отворен, комерсиа<PERSON>ен голям езиков модел, разработен от Baichuan Intelligence, с 13 милиарда параметри, който постига най-добрите резултати в своя размер на авторитетни бенчмаркове на китайски и английски."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B е голям езиков модел, разработен от Baidu, базиран на архитектурата с хибридни експерти (MoE). Моделът има общо 300 милиарда параметри, но при инференция активира само 47 милиарда параметри на токен, което осигурява висока производителност и изчислителна ефективност. Като един от основните модели в серията ERNIE 4.5, той демонстрира изключителни способности в задачи като разбиране на текст, генериране, разсъждение и програмиране. Моделът използва иновативен мултимодален хетерогенен MoE метод за предварително обучение, който чрез съвместно обучение на текстови и визуални модалности значително подобрява цялостните му възможности, особено в следването на инструкции и запаметяването на световни знания."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse е високопроизводителен многоезичен модел с 32B, проектиран да предизвика представянето на едноезични модели чрез иновации в настройката на инструкции, арбитраж на данни, обучение на предпочитания и комбиниране на модели. Той поддържа 23 езика."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse е високопроизводителен многоезичен модел с 8B, проектиран да предизвика представянето на едноезични модели чрез иновации в настройката на инструкции, арбитраж на данни, обучение на предпочитания и комбиниране на модели. Той поддържа 23 езика."}, "c4ai-aya-vision-32b": {"description": "Aya Vision е авангарден много модален модел, който показва отлични резултати в множество ключови бенчмаркове за езикови, текстови и визуални способности. Той поддържа 23 езика. Тази версия с 32 милиарда параметри се фокусира върху авангарден многоезичен представител."}, "c4ai-aya-vision-8b": {"description": "Aya Vision е авангарден много модален модел, който показва отлични резултати в множество ключови бенчмаркове за езикови, текстови и визуални способности. Тази версия с 8 милиарда параметри се фокусира върху ниска латентност и оптимална производителност."}, "charglm-3": {"description": "CharGLM-3 е проектиран за ролеви игри и емоционално придружаване, поддържаща дълга многократна памет и персонализиран диалог, с широко приложение."}, "charglm-4": {"description": "CharGLM-4 е проектиран за ролеви игри и емоционално придружаване, поддържащ дългосрочна памет и персонализирани диалози, с широко приложение."}, "chatglm3": {"description": "ChatGLM3 е закритоизточен модел, обявен от интелигентната платформа AI и лабораторията KEG на Университета в Тайхуа. Той е претрениран с голям обем на китайски и английски идентификатори и е подложен на тренировка за съответствие с хуманите предпочитания. Сравнено с първата версия на модела, ChatGLM3 постига подобрения от 16%, 36% и 280% в MMLU, C-Eval и GSM8K съответно, и е класифициран на първо място в китайския рейтинг C-Eval. Този модел е подходящ за сценарии, които изискват високи стандарти за знания, умения за разсъждаване и креативност, като например създаване на рекламни текстове, писане на романи, научно-популярно писане и генериране на код."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base е последната генерация на редицата ChatGLM, разработена от компанията Zhipu, с 6 милиарда параметри и е открит източник."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o е динамичен модел, който се актуализира в реално време, за да поддържа най-новата версия. Той комбинира мощно разбиране на езика и генериране на текст, подходящ за мащабни приложения, включително обслужване на клиенти, образование и техническа поддръжка."}, "claude-2.0": {"description": "Claude 2 предлага напредък в ключовите способности за бизнеса, включително водещи в индустрията 200K токена контекст, значително намаляване на честотата на илюзии на модела, системни подсказки и нова тестова функция: извикване на инструменти."}, "claude-2.1": {"description": "Claude 2 предлага напредък в ключовите способности за бизнеса, включително водещи в индустрията 200K токена контекст, значително намаляване на честотата на илюзии на модела, системни подсказки и нова тестова функция: извикване на инструменти."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku е най-бързият следващ модел на Anthropic. В сравнение с Claude 3 Haiku, Claude 3.5 Haiku е подобрен във всички умения и надминава предишния най-голям модел Claude 3 Opus в много интелектуални тестове."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet предлага способности, надминаващи Opus и по-бърза скорост от Sonnet, като същевременно поддържа същата цена. Sonnet е особено силен в програмирането, науката за данни, визуалната обработка и задачи с агенти."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet предлага възможности, които надминават Opus и скорости, които са по-бързи от Sonnet, като същевременно поддържа същата цена като Sonnet. Sonnet е специално силен в програмирането, науката за данни, визуалната обработка и задачи, свързани с代理."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet предлага индустриални стандарти, с производителност, надвишаваща конкурентните модели и Claude 3 Opus, с отлични резултати в широки оценки, като същевременно предлага скорост и разходи, характерни за нашите модели от среден клас."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku е най-бързият и компактен модел на Anthropic, проектиран за почти мигновени отговори. Той предлага бърза и точна насочена производителност."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus е най-мощният модел на Anthropic за обработка на високо сложни задачи. Той показва изключителна производителност, интелигентност, гладкост и разбиране."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet предлага идеален баланс между интелигентност и скорост за корпоративни работни натоварвания. Той предлага максимална полезност на по-ниска цена, надежден и подходящ за мащабно внедряване."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 е най-мощният модел на Anthrop<PERSON>, предназначен за обработка на изключително сложни задачи. Той се отличава с изключителна производителност, интелигентност, плавност и разбиране."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet може да генерира почти мигновени отговори или удължено постепенно мислене, като потребителите могат ясно да видят тези процеси. Потребителите на API също могат да упражняват прецизен контрол върху времето за мислене на модела."}, "codegeex-4": {"description": "CodeGeeX-4 е мощен AI помощник за програмиране, който поддържа интелигентни въпроси и отговори и автоматично допълване на код за различни програмни езици, повишавайки ефективността на разработката."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B е многоезичен модел за генериране на код, който предлага пълни функции, включително попълване и генериране на код, интерпретатор на код, уеб търсене, извикване на функции и въпроси и отговори на ниво хранилище, обхващащ различни сценарии на софтуерна разработка. Това е водещ модел за генериране на код с по-малко от 10B параметри."}, "codegemma": {"description": "CodeGemma е лек езиков модел, специализиран в различни програмни задачи, поддържащ бърза итерация и интеграция."}, "codegemma:2b": {"description": "CodeGemma е лек езиков модел, специализиран в различни програмни задачи, поддържащ бърза итерация и интеграция."}, "codellama": {"description": "Code Llama е LLM, фокусиран върху генерирането и обсъждането на код, комбиниращ широк спектър от поддръжка на програмни езици, подходящ за среда на разработчици."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama е LLM, фокусиран върху генерирането и обсъждането на код, с широка поддръжка на програмни езици, подходящ за среда на разработчици."}, "codellama:13b": {"description": "Code Llama е LLM, фокусиран върху генерирането и обсъждането на код, комбиниращ широк спектър от поддръжка на програмни езици, подходящ за среда на разработчици."}, "codellama:34b": {"description": "Code Llama е LLM, фокусиран върху генерирането и обсъждането на код, комбиниращ широк спектър от поддръжка на програмни езици, подходящ за среда на разработчици."}, "codellama:70b": {"description": "Code Llama е LLM, фокусиран върху генерирането и обсъждането на код, комбиниращ широк спектър от поддръжка на програмни езици, подходящ за среда на разработчици."}, "codeqwen": {"description": "CodeQwen1.5 е голям езиков модел, обучен на основата на обширни кодови данни, специално проектиран за решаване на сложни програмни задачи."}, "codestral": {"description": "Codestral е първият кодов модел на Mistral AI, предоставящ отлична поддръжка за задачи по генериране на код."}, "codestral-latest": {"description": "Codestral е авангарден генеративен модел, фокусиран върху генерирането на код, оптимизиран за междинно попълване и задачи за допълване на код."}, "codex-mini-latest": {"description": "codex-mini-latest е фина настройка на o4-mini, специално предназначена за Codex CLI. За директна употреба чрез API препоръчваме да започнете с gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B е модел, проектиран за следване на инструкции, диалози и програмиране."}, "cogview-4": {"description": "CogView-4 е първият отворен модел за генериране на изображения с текст на китайски, разработен от Zhipu, който значително подобрява разбирането на семантиката, качеството на генериране на изображения и способността за генериране на текст на китайски и английски език. Поддържа двуезичен вход на произволна дължина на китайски и английски и може да генерира изображения с произволна резолюция в зададения диапазон."}, "cohere-command-r": {"description": "Command R е мащабируем генеративен модел, насочен към RAG и използване на инструменти, за да позволи AI на производствено ниво за предприятия."}, "cohere-command-r-plus": {"description": "Command R+ е модел, оптимизиран за RAG, проектиран да се справя с натоварвания на ниво предприятие."}, "cohere/Cohere-command-r": {"description": "Command R е мащабируем генеративен модел, предназначен за RAG и използване на инструменти, който позволява на предприятията да внедрят AI на производствено ниво."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ е усъвършенстван оптимизиран модел за RAG, предназначен да се справя с натоварвания на корпоративно ниво."}, "command": {"description": "Диалогов модел, следващ инструкции, който показва високо качество и надеждност в езиковите задачи, с по-дълга контекстна дължина в сравнение с нашия основен генеративен модел."}, "command-a-03-2025": {"description": "Команда A е нашият най-мощен модел до момента, който показва отлични резултати в използването на инструменти, агенти, подобрено генериране на информация (RAG) и многоезични приложения. Команда A разполага с контекстна дължина от 256K и може да работи само с две GPU, а производителността е увеличена с 150% в сравнение с Команда R+ 08-2024."}, "command-light": {"description": "По-малка и по-бърза версия на Команда, почти толкова мощна, но с по-бърза скорост."}, "command-light-nightly": {"description": "За да съкратим времевия интервал между основните версии, пуснахме нощна версия на модела Команда. За серията command-light, тази версия се нарича command-light-nightly. Обърнете внимание, че command-light-nightly е най-новата, най-експериментална и (възможно) нестабилна версия. Нощните версии се актуализират редовно и без предварително уведомление, затова не се препоръчва използването им в производствени среди."}, "command-nightly": {"description": "За да съкратим времевия интервал между основните версии, пуснахме нощна версия на модела Команда. За серията Команда, тази версия се нарича command-cightly. Обърнете внимание, че command-nightly е най-новата, най-експериментална и (възможно) нестабилна версия. Нощните версии се актуализират редовно и без предварително уведомление, затова не се препоръчва използването им в производствени среди."}, "command-r": {"description": "Command R е LLM, оптимизиран за диалогови и дълги контекстуални задачи, особено подходящ за динамично взаимодействие и управление на знания."}, "command-r-03-2024": {"description": "Команда R е диалогов модел, следващ инструкции, който показва по-високо качество и надеждност в езиковите задачи, с по-дълга контекстна дължина в сравнение с предишните модели. Той може да се използва за сложни работни потоци, като генериране на код, подобрено генериране на информация (RAG), използване на инструменти и агенти."}, "command-r-08-2024": {"description": "command-r-08-2024 е актуализирана версия на модела Команда R, пусната през август 2024 г."}, "command-r-plus": {"description": "Command R+ е високопроизводителен голям езиков модел, проектиран за реални бизнес сценарии и сложни приложения."}, "command-r-plus-04-2024": {"description": "Команда R+ е диалогов модел, следващ инструкции, който показва по-високо качество и надеждност в езиковите задачи, с по-дълга контекстна дължина в сравнение с предишните модели. Той е най-подходящ за сложни RAG работни потоци и многократна употреба на инструменти."}, "command-r-plus-08-2024": {"description": "Command R+ е диалогов модел, който следва инструкции, показващ по-високо качество и надеждност в езиковите задачи, с по-дълга контекстуална дължина в сравнение с предишните модели. Най-подходящ е за сложни RAG работни потоци и многостепенна употреба на инструменти."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 е малка и ефективна актуализирана версия, пусната през декември 2024 г. Тя показва отлични резултати в задачи, изискващи сложни разсъждения и многократна обработка, като RAG, използване на инструменти и агенти."}, "compound-beta": {"description": "Compound-beta е композитна AI система, подкрепена от множество отворени модели, налични в GroqCloud, която интелигентно и селективно използва инструменти за отговор на запитвания на потребителите."}, "compound-beta-mini": {"description": "Compound-beta-mini е композитна AI система, подкрепена от публично достъпни модели в GroqCloud, която интелигентно и селективно използва инструменти за отговор на запитвания на потребителите."}, "computer-use-preview": {"description": "Моделът computer-use-preview е специално разработен за „инструменти за използване на компютър“, обучен да разбира и изпълнява задачи, свързани с компютри."}, "dall-e-2": {"description": "Второ поколение модел DALL·E, поддържащ по-реалистично и точно генериране на изображения, с резолюция 4 пъти по-висока от първото поколение."}, "dall-e-3": {"description": "Най-новият модел DALL·E, пуснат през ноември 2023 г. Поддържа по-реалистично и точно генериране на изображения с по-силна детайлност."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct предлага висока надеждност в обработката на инструкции, поддържаща приложения в множество индустрии."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 е модел за извеждане, управляван от подсилено обучение (RL), който решава проблемите с повторяемостта и четимостта в модела. Преди RL, DeepSeek-R1 въвежда данни за студен старт, за да оптимизира допълнително производителността на извеждане. Той показва сравнима производителност с OpenAI-o1 в математически, кодови и извеждащи задачи и подобрява общите резултати чрез внимателно проектирани методи на обучение."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 значително подобрява дълбочината на разсъждения и изводи чрез използване на увеличени изчислителни ресурси и въвеждане на алгоритмични оптимизации по време на последващото обучение. Моделът постига отлични резултати в различни базови оценки, включително математика, програмиране и обща логика. Общата му производителност вече е близка до водещи модели като O3 и Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B е модел, получен чрез дистилация на мисловни вериги от DeepSeek-R1-0528 към Qwen3 8B Base. Този модел постига най-съвременна (SOTA) производителност сред отворените модели, превъзхождайки Qwen3 8B с 10% в теста AIME 2024 и достига нивото на Qwen3-235B-thinking. Моделът показва отлични резултати в математическо разсъждение, програмиране и обща логика, с архитектура, идентична на Qwen3-8B, но споделяща конфигурацията на токенизатора на DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1 дестили<PERSON><PERSON><PERSON> модел, оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 дестили<PERSON><PERSON><PERSON> модел, оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 дестили<PERSON><PERSON><PERSON> модел, оптимизира производителността на разсъжденията чрез подсилено учене и данни за студен старт, отворен модел, който обновява многозадачния стандарт."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B е модел, получен чрез знание дестилация на Qwen2.5-32B. Този модел е финализиран с 800 000 избрани примера, генерирани от DeepSeek-R1, и показва изключителна производителност в множество области, включително математика, програмиране и разсъждения. Той постига отлични резултати в множество бенчмаркове, включително 94.3% точност в MATH-500, демонстрирайки силни способности за математическо разсъждение."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B е модел, получен чрез знание дестилация на Qwen2.5-Math-7B. Този модел е финализиран с 800 000 избрани примера, генерирани от DeepSeek-R1, и показва отлична производителност на разсъжденията. Той постига отлични резултати в множество бенчмаркове, включително 92.8% точност в MATH-500, 55.5% успеваемост в AIME 2024 и 1189 точки в CodeForces, демонстрирайки силни способности за математика и програмиране."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 обединява отличителните характеристики на предишните версии, подобрявайки общите и кодиращите способности."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 е езиков модел с 6710 милиарда параметри, базиран на смесени експерти (MoE), който използва многоглаво потенциално внимание (MLA) и архитектурата DeepSeekMoE, комбинирайки стратегии за баланс на натоварването без помощни загуби, за да оптимизира производителността на извеждане и обучение. Чрез предварително обучение на 14.8 трилиона висококачествени токени и последващо наблюдавано фино настройване и подсилено обучение, DeepSeek-V3 надминава производителността на други отворени модели и се приближава до водещите затворени модели."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B е напреднал модел, обучен за диалози с висока сложност."}, "deepseek-ai/deepseek-r1": {"description": "Най-съвременен ефективен LLM, специализиран в разсъждения, математика и програмиране."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 е визуален езиков модел, разработен на базата на DeepSeekMoE-27B, който използва архитектура на смесени експерти (MoE) с рядка активация, постигайки изключителна производителност с активирани само 4.5B параметри. Моделът показва отлични резултати в множество задачи, включително визуални въпроси и отговори, оптично разпознаване на символи, разбиране на документи/таблици/графики и визуална локализация."}, "deepseek-chat": {"description": "Новооткритият отворен модел, който съчетава общи и кодови способности, не само запазва общата диалогова способност на оригиналния Chat модел и мощната способност за обработка на код на Coder модела, но също така по-добре се съгласува с човешките предпочитания. Освен това, DeepSeek-V2.5 постигна значителни подобрения в писателските задачи, следването на инструкции и много други области."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B е модел за кодови езици, обучен на 20 трилиона данни, от които 87% са код и 13% са на китайски и английски. Моделът въвежда размер на прозореца от 16K и задачи за попълване, предоставяйки функции за попълване на код на проектно ниво и попълване на фрагменти."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 е отворен хибриден експертен кодов модел, който се представя отлично в кодовите задачи, сравним с GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 е отворен хибриден експертен кодов модел, който се представя отлично в кодовите задачи, сравним с GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 е модел за извеждане, управляван от подсилено обучение (RL), който решава проблемите с повторяемостта и четимостта в модела. Преди RL, DeepSeek-R1 въвежда данни за студен старт, за да оптимизира допълнително производителността на извеждане. Той показва сравнима производителност с OpenAI-o1 в математически, кодови и извеждащи задачи и подобрява общите резултати чрез внимателно проектирани методи на обучение."}, "deepseek-r1-0528": {"description": "Пълноценен модел с 685 милиарда параметри, пуснат на 28 май 2025 г. DeepSeek-R1 използва мащабно обучение с подсилване в последващия етап на обучение, значително подобрявайки способността за разсъждение с минимални анотирани данни. Отличава се с висока производителност и способности в задачи по математика, кодиране и естествен езиков разсъждения."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B бърза версия, поддържаща търсене в реално време, предлагаща по-бърза скорост на отговор, без да компрометира производителността на модела."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B стандартна версия, поддържаща търсене в реално време, подходяща за диалози и текстови задачи, изискващи най-новата информация."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama е модел, дестилиран от DeepSeek-R1 на базата на Llama."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - по-голям и по-интелигентен модел в комплекта DeepSeek - е дестилиран в архитектурата Llama 70B. На базата на бенчмаркове и човешка оценка, този модел е по-интелигентен от оригиналния Llama 70B, особено в задачи, изискващи математическа и фактическа точност."}, "deepseek-r1-distill-llama-8b": {"description": "Моделите от серията DeepSeek-R1-<PERSON><PERSON>ill са получени чрез техника на знание дестилация, като се фино настройват образците, генерирани от DeepSeek-R1, спрямо отворени модели като Qwen и Llama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Първоначално пуснат на 14 февруари 2025 г., дестилиран от екипа за разработка на модела Qianfan с базов модел Llama3_70B (създаден с Meta Llama), в дестилираните данни също е добавен корпус от Qianfan."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Първоначално пуснат на 14 февруари 2025 г., дестилиран от екипа за разработка на модела Qianfan с базов модел Llama3_8B (създаден с Meta Llama), в дестилираните данни също е добавен корпус от Qianfan."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen е модел, базиран на Qwen, дестилиран от DeepSeek-R1."}, "deepseek-r1-distill-qwen-1.5b": {"description": "Моделите от серията DeepSeek-R1-<PERSON><PERSON>ill са получени чрез техника на знание дестилация, като се фино настройват образците, генерирани от DeepSeek-R1, спрямо отворени модели като Qwen и Llama."}, "deepseek-r1-distill-qwen-14b": {"description": "Моделите от серията DeepSeek-R1-<PERSON><PERSON>ill са получени чрез техника на знание дестилация, като се фино настройват образците, генерирани от DeepSeek-R1, спрямо отворени модели като Qwen и Llama."}, "deepseek-r1-distill-qwen-32b": {"description": "Моделите от серията DeepSeek-R1-<PERSON><PERSON>ill са получени чрез техника на знание дестилация, като се фино настройват образците, генерирани от DeepSeek-R1, спрямо отворени модели като Qwen и Llama."}, "deepseek-r1-distill-qwen-7b": {"description": "Моделите от серията DeepSeek-R1-<PERSON><PERSON>ill са получени чрез техника на знание дестилация, като се фино настройват образците, генерирани от DeepSeek-R1, спрямо отворени модели като Qwen и Llama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 пълна бърза версия, поддържаща търсене в реално време, комбинираща мощността на 671B параметри с по-бърза скорост на отговор."}, "deepseek-r1-online": {"description": "DeepSeek R1 пълна версия, с 671B параметри, поддържаща търсене в реално време, с по-силни способности за разбиране и генериране."}, "deepseek-reasoner": {"description": "Модел за извеждане, разработен от DeepSeek. Преди да предостави окончателния отговор, моделът първо извежда част от веригата на мислене, за да повиши точността на крайния отговор."}, "deepseek-v2": {"description": "DeepSeek V2 е ефективен модел на Mixture-of-Experts, подходящ за икономически ефективни нужди от обработка."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B е кодовият модел на DeepSeek, предоставящ мощни способности за генериране на код."}, "deepseek-v3": {"description": "DeepSeek-V3 е Mo<PERSON> модел, разработен от Hangzhou DeepSeek AI Technology Research Co., Ltd., с отлични резултати в множество тестове, заемащ първото място в основните класации на отворените модели. V3 постига 3-кратно увеличение на скоростта на генериране в сравнение с V2.5, предоставяйки на потребителите по-бързо и гладко изживяване."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 е MoE модел с 671B параметри, който се отличава с предимства в програмирането и техническите способности, разбирането на контекста и обработката на дълги текстове."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 е експертен смесен модел с 685B параметри, последната итерация на флагманската серия чат модели на екипа DeepSeek.\n\nТой наследява модела [DeepSeek V3](/deepseek/deepseek-chat-v3) и показва отлични резултати в различни задачи."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 е експертен смесен модел с 685B параметри, последната итерация на флагманската серия чат модели на екипа DeepSeek.\n\nТой наследява модела [DeepSeek V3](/deepseek/deepseek-chat-v3) и показва отлични резултати в различни задачи."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 значително подобри способността на модела за разсъждение при наличието на много малко маркирани данни. Преди да предостави окончателния отговор, моделът първо ще изведе част от съдържанието на веригата на мислене, за да повиши точността на окончателния отговор."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 значително подобрява способността за разсъждение на модела дори с много малко анотирани данни. Преди да изведе окончателния отговор, моделът първо генерира мисловна верига, за да повиши точността на крайния отговор."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 значително подобрява способността за разсъждение на модела дори с много малко анотирани данни. Преди да изведе окончателния отговор, моделът първо генерира мисловна верига, за да повиши точността на крайния отговор."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B е голям езиков модел, базиран на Llama3.3 70B, който използва фина настройка на изхода на DeepSeek R1, за да постигне конкурентна производителност, сравнима с големите водещи модели."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B е дестилиран голям езиков модел, базиран на Llama-3.1-8B-Instruct, обучен с изхода на DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B е дестилиран голям езиков модел, баз<PERSON><PERSON><PERSON><PERSON> на Qwen 2.5 14B, обучен с изхода на DeepSeek R1. Този модел надминава o1-mini на OpenAI в множество бенчмарков, постигащи най-съвременни резултати за плътни модели. Ето някои от резултатите от бенчмарковете:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nТози модел демонстрира конкурентна производителност, сравнима с по-големи водещи модели, благодарение на фина настройка на изхода на DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B е дестилиран голям езиков модел, баз<PERSON><PERSON><PERSON><PERSON> на Qwen 2.5 32B, обучен с изхода на DeepSeek R1. Този модел надминава o1-mini на OpenAI в множество бенчмарков, постигащи най-съвременни резултати за плътни модели. Ето някои от резултатите от бенчмарковете:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nТози модел демонстрира конкурентна производителност, сравнима с по-големи водещи модели, благодарение на фина настройка на изхода на DeepSeek R1."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 е най-новият отворен модел, публикуван от екипа на DeepSeek, който предлага изключителна производителност при извеждане, особено в математически, програмистки и логически задачи, достигайки ниво, сравнимо с модела o1 на OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 значително подобри способността на модела за разсъждение при наличието на много малко маркирани данни. Преди да предостави окончателния отговор, моделът първо ще изведе част от съдържанието на веригата на мислене, за да повиши точността на окончателния отговор."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 постига значителен напредък в скоростта на извеждане в сравнение с предишните модели. Той е на първо място сред отворените модели и може да се сравнява с най-съвременните затворени модели в света. DeepSeek-V3 използва архитектури с многоглаво внимание (MLA) и DeepSeekMoE, които бяха напълно валидирани в DeepSeek-V2. Освен това, DeepSeek-V3 въвежда помощна беззагубна стратегия за баланс на натоварването и задава цели за обучение с множество етикети, за да постигне по-силна производителност."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 постига значителен напредък в скоростта на извеждане в сравнение с предишните модели. Той е на първо място сред отворените модели и може да се сравнява с най-съвременните затворени модели в света. DeepSeek-V3 използва архитектури с многоглаво внимание (MLA) и DeepSeekMoE, които бяха напълно валидирани в DeepSeek-V2. Освен това, DeepSeek-V3 въвежда помощна беззагубна стратегия за баланс на натоварването и задава цели за обучение с множество етикети, за да постигне по-силна производителност."}, "deepseek_r1": {"description": "DeepSeek-R1 е модел за разсъждение, управляван от обучение с подсилване (RL), който решава проблемите с повторяемостта и четимостта в модела. Преди RL, DeepSeek-R1 въвежда студени данни, за да оптимизира допълнително производителността на разсъжденията. Той показва производителност, сравнима с OpenAI-o1 в задачи по математика, код и разсъждения, и чрез внимателно проектирани методи на обучение, подобрява общите резултати."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B е модел, получен чрез дестилация на Llama-3.3-70B-Instruct. Този модел е част от серията DeepSeek-R1 и е финализиран с примери, генерирани от DeepSeek-R1, показвайки отлична производителност в области като математика, програмиране и разсъждения."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B е модел, получен чрез знание на дестилация на Qwen2.5-14B. Този модел е финализиран с 800 000 избрани примера, генерирани от DeepSeek-R1, показвайки отлични способности за разсъждение."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B е модел, получен чрез знание на дестилация на Qwen2.5-32B. Този модел е финализиран с 800 000 избрани примера, генерирани от DeepSeek-R1, показвайки изключителна производителност в множество области, включително математика, програмиране и разсъждения."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite е ново поколение лек модел, с изключителна скорост на отговор, който постига световно ниво както по отношение на ефективността, така и на времето за реакция."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k е напълно обновен вариант на Doubao-1.5-Pro, с общо подобрение на ефективността с 10%. Поддържа разсъждения с контекстен прозорец от 256k, а дължината на изхода поддържа максимум 12k токена. По-висока производителност, по-голям прозорец и изключителна цена-качество, подходящ за по-широк спектър от приложения."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro е ново поколение основен модел, с напълно обновени характеристики, който показва отлични резултати в области като знания, код, разсъждения и др."}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5 е нов модел за дълбоко мислене, който се отличава в специализирани области като математика, програмиране и научно разсъждение, както и в общи задачи като креативно писане. Той достига или е близо до нивото на водещите в индустрията в множество авторитетни бенчмаркове, включително AIME 2024, Codeforces и GPQA. Поддържа контекстен прозорец от 128k и 16k изход."}, "doubao-1.5-thinking-pro-m": {"description": "Новият дълбок мисловен модел Doubao-1.5 (версия m с вградена мултимодална дълбока разсъдителна способност), отличаващ се в математика, програмиране, научно разсъждение и творческо писане, постигащ или приближаващ се до водещите нива в индустрията на авторитетни тестове като AIME 2024, Codeforces и GPQA. Поддържа контекстен прозорец от 128k и изход до 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Нов визуален дълбок мисловен модел с по-силни универсални мултимодални разбиране и разсъждения, постигнал SOTA резултати в 37 от 59 публични тестови бази."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS е агентен модел, специално създаден за графичен потребителски интерфейс (GUI). Чрез човешки подобни способности за възприятие, разсъждение и действие, осигурява безпроблемно взаимодействие с GUI."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite е ново обновление на мултимодалния модел, поддържащ разпознаване на изображения с произволна резолюция и екстремни съотношения на дължина и ширина, подобряващ способностите за визуални разсъждения, разпознаване на документи, разбиране на детайлна информация и следване на инструкции. Поддържа контекстуален прозорец от 128k, с максимална дължина на изхода от 16k токена."}, "doubao-1.5-vision-pro": {"description": "Новоподобреният мултимодален голям модел Doubao-1.5-vision-pro поддържа разпознаване на изображения с всякаква резолюция и екстремни съотношения на страните, подобрявайки визуалното разсъждение, разпознаване на документи, разбиране на детайлна информация и следване на инструкции."}, "doubao-1.5-vision-pro-32k": {"description": "Новоподобреният мултимодален голям модел Doubao-1.5-vision-pro поддържа разпознаване на изображения с всякаква резолюция и екстремни съотношения на страните, подобрявайки визуалното разсъждение, разпознаване на документи, разбиране на детайлна информация и следване на инструкции."}, "doubao-lite-128k": {"description": "Предлага изключително бърза реакция и по-добро съотношение цена-качество, осигурявайки по-гъвкави опции за различни сценарии на клиентите. Поддържа разсъждения и финна настройка с контекстен прозорец от 128k."}, "doubao-lite-32k": {"description": "Предлага изключително бърза реакция и по-добро съотношение цена-качество, осигурявайки по-гъвкави опции за различни сценарии на клиентите. Поддържа разсъждения и финна настройка с контекстен прозорец от 32k."}, "doubao-lite-4k": {"description": "Предлага изключително бърза реакция и по-добро съотношение цена-качество, осигурявайки по-гъвкави опции за различни сценарии на клиентите. Поддържа разсъждения и финна настройка с контекстен прозорец от 4k."}, "doubao-pro-256k": {"description": "Най-ефективният основен модел, подходящ за обработка на сложни задачи, с отлични резултати в справки, обобщения, творчество, текстова класификация и ролеви игри. Поддържа разсъждения и финна настройка с контекстен прозорец от 256k."}, "doubao-pro-32k": {"description": "Най-ефективният основен модел, подходящ за обработка на сложни задачи, с отлични резултати в справки, обобщения, творчество, текстова класификация и ролеви игри. Поддържа разсъждения и финна настройка с контекстен прозорец от 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 е нов много модален модел за дълбоко мислене, който поддържа три режима на мислене: auto, thinking и non-thinking. В non-thinking режим моделът значително превъзхожда Doubao-1.5-pro/250115. Поддържа контекстен прозорец от 256k и максимална дължина на изхода до 16k токена."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash е изключително бърз много модален модел за дълбоко мислене с TPOT само 10ms; поддържа както текстово, така и визуално разбиране, като текстовите му възможности надминават предишното поколение lite, а визуалното разбиране е на нивото на професионалните модели на конкурентите. Поддържа контекстен прозорец от 256k и максимална дължина на изхода до 16k токена."}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking моделът значително подобрява способностите за мислене в сравнение с Doubao-1.5-thinking-pro, с допълнителни подобрения в кодиране, математика и логическо разсъждение, като поддържа и визуално разбиране. Поддържа контекстен прозорец от 256k и максимална дължина на изхода до 16k токена."}, "doubao-seedream-3-0-t2i-250415": {"description": "Моделът за генериране на изображения Doubao е разработен от екипа Seed на ByteDance, поддържа вход както от текст, така и от изображения, и предлага високо контролирано и качествено генериране на изображения. Генерира изображения въз основа на текстови подсказки."}, "doubao-vision-lite-32k": {"description": "Моделът Doubao-vision е мултимодален голям модел, разработен от Doubao, с мощни способности за разбиране и разсъждение върху изображения, както и прецизно разбиране на инструкции. Моделът показва силна производителност при извличане на информация от изображения и текст, както и при задачи за разсъждение, базирани на изображения, подходящ за по-сложни и широки визуални въпроси."}, "doubao-vision-pro-32k": {"description": "Моделът Doubao-vision е мултимодален голям модел, разработен от Doubao, с мощни способности за разбиране и разсъждение върху изображения, както и прецизно разбиране на инструкции. Моделът показва силна производителност при извличане на информация от изображения и текст, както и при задачи за разсъждение, базирани на изображения, подходящ за по-сложни и широки визуални въпроси."}, "emohaa": {"description": "Emohaa е психологически модел с професионални консултантски способности, помагащ на потребителите да разберат емоционалните проблеми."}, "ernie-3.5-128k": {"description": "Флагманският голям езиков модел, разработен от Baidu, обхваща огромно количество китайски и английски текстове, притежаващ силни общи способности, способен да отговори на повечето изисквания за диалогови въпроси и отговори, генериране на съдържание и приложения на плъгини; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията."}, "ernie-3.5-8k": {"description": "Флагманският голям езиков модел, разработен от Baidu, обхваща огромно количество китайски и английски текстове, притежаващ силни общи способности, способен да отговори на повечето изисквания за диалогови въпроси и отговори, генериране на съдържание и приложения на плъгини; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията."}, "ernie-3.5-8k-preview": {"description": "Флагманският голям езиков модел, разработен от Baidu, обхваща огромно количество китайски и английски текстове, притежаващ силни общи способности, способен да отговори на повечето изисквания за диалогови въпроси и отговори, генериране на съдържание и приложения на плъгини; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията."}, "ernie-4.0-8k-latest": {"description": "Флагманският голям езиков модел, разработен от Baidu, с изключителни подобрения в сравнение с ERNIE 3.5, широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията."}, "ernie-4.0-8k-preview": {"description": "Флагманският голям езиков модел, разработен от Baidu, с изключителни подобрения в сравнение с ERNIE 3.5, широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията."}, "ernie-4.0-turbo-128k": {"description": "Флагманският голям езиков модел, разработен от Baidu, с отлични общи резултати, широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията. В сравнение с ERNIE 4.0, показва по-добри резултати."}, "ernie-4.0-turbo-8k-latest": {"description": "Флагманският голям езиков модел, разработен от Baidu, с отлични общи резултати, широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията. В сравнение с ERNIE 4.0, показва по-добри резултати."}, "ernie-4.0-turbo-8k-preview": {"description": "Флагманският голям езиков модел, разработен от Baidu, с отлични общи резултати, широко приложим в сложни задачи в различни области; поддържа автоматично свързване с плъгина за търсене на Baidu, осигурявайки актуалност на информацията. В сравнение с ERNIE 4.0, показва по-добри резултати."}, "ernie-4.5-8k-preview": {"description": "Моделът Ernie 4.5 е ново поколение оригинален много модален основен модел, разработен от Baidu, който постига съвместна оптимизация чрез многомодално моделиране, с отлични способности за разбиране на много модалности; предлага усъвършенствани езикови способности, с подобрено разбиране, генериране, логика и памет, значително подобрени способности за избягване на халюцинации, логическо разсъждение и код."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo показва значителни подобрения в областите на елиминиране на илюзии, логическо разсъждение и кодиране. В сравнение с Wenxin 4.5, е по-бърз и по-евтин. Моделът е с цялостно подобрени способности, по-добре отговарящи на задачите за обработка на многократни дълги исторически разговори и разбиране на дълги документи."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo показва значителни подобрения в областите на елиминиране на илюзии, логическо разсъждение и кодиране. В сравнение с Wenxin 4.5, е по-бърз и по-евтин. Способностите за текстово творчество и знания са значително подобрени. Дължината на изхода и времето за забавяне на цялото изречение са увеличени в сравнение с ERNIE 4.5."}, "ernie-4.5-turbo-vl-32k": {"description": "Нова версия на големия мод<PERSON><PERSON>, с значително подобрени способности за разбиране на изображения, творчество, превод и кодиране, за първи път поддържа контекстна дължина от 32K, значително намалено забавяне при първия токен."}, "ernie-char-8k": {"description": "Специализиран голям езиков модел, разработен от Baidu, подходящ за приложения като NPC в игри, диалози на клиентска поддръжка и ролеви игри, с по-изразителен и последователен стил на персонажите, по-силна способност за следване на инструкции и по-добра производителност на разсъжденията."}, "ernie-char-fiction-8k": {"description": "Специализиран голям езиков модел, разработен от Baidu, подходящ за приложения като NPC в игри, диалози на клиентска поддръжка и ролеви игри, с по-изразителен и последователен стил на персонажите, по-силна способност за следване на инструкции и по-добра производителност на разсъжденията."}, "ernie-irag-edit": {"description": "Собствен модел за редактиране на изображения ERNIE iRAG на Baidu поддържа операции като изтриване (erase), прерисуване (repaint) и вариации (variation) върху изображения."}, "ernie-lite-8k": {"description": "ERNIE Lite е лек голям езиков модел, разработен от Baidu, който съчетава отлични резултати с производителност на разсъжденията, подходящ за използване с AI ускорителни карти с ниска изчислителна мощ."}, "ernie-lite-pro-128k": {"description": "Лек голям езиков модел, разработ<PERSON><PERSON> от Baidu, който съчетава отлични резултати с производителност на разсъжденията, с по-добри резултати в сравнение с ERNIE Lite, подходящ за използване с AI ускорителни карти с ниска изчислителна мощ."}, "ernie-novel-8k": {"description": "Общ голям езиков модел, разработен от Baidu, с очевидни предимства в продължаването на разкази, подходящ и за кратки пиеси и филми."}, "ernie-speed-128k": {"description": "Най-новият високопроизводителен голям езиков модел, разработен от Baidu през 2024 г., с отлични общи способности, подходящ за финализиране на специфични проблеми, с отлична производителност на разсъжденията."}, "ernie-speed-pro-128k": {"description": "Най-новият високопроизводителен голям езиков модел, разработен от Baidu през 2024 г., с отлични общи способности, с по-добри резултати в сравнение с ERNIE Speed, подходящ за финализиране на специфични проблеми, с отлична производителност на разсъжденията."}, "ernie-tiny-8k": {"description": "ERNIE Tiny е модел с изключителна производителност, разработен от Baidu, с най-ниски разходи за внедряване и фина настройка сред моделите от серията Wenxin."}, "ernie-x1-32k": {"description": "Разполага с по-силни способности за разбиране, планиране, размисъл и еволюция. Като модел за дълбоко мислене с по-пълни способности, Wenxin X1 съчетава точност, креативност и изящество, и се представя особено добре в области като китайски знания, литературно творчество, писане на документи, ежедневни разговори, логическо разсъждение, сложни изчисления и извикване на инструменти."}, "ernie-x1-32k-preview": {"description": "Моделът Wenxin X1 притежава по-силни способности за разбиране, планиране, размисъл и еволюция. Като модел за дълбоко мислене с по-широки възможности, Wenxin X1 съчетава точност, креативност и изящество, особено в области като китайски знания и отговори, литературно творчество, писане на документи, ежедневни разговори, логическо разсъждение, сложни изчисления и извикване на инструменти."}, "ernie-x1-turbo-32k": {"description": "В сравнение с ERNIE-X1-32K, моделът предлага по-добри резултати и производителност."}, "flux-1-schnell": {"description": "Модел за генериране на изображения от текст с 12 милиарда параметри, разработен от Black Forest Labs, използващ латентна противоречива дифузионна дистилация, способен да генерира висококачествени изображения за 1 до 4 стъпки. Моделът постига производителност, сравнима с проприетарни алтернативи, и е пуснат под лиценз Apache-2.0, подходящ за лична, научна и търговска употреба."}, "flux-dev": {"description": "FLUX.1 [dev] е отворен и пречистен модел, предназначен за нетърговска употреба. Той запазва качество на изображенията и способността за следване на инструкции, близки до професионалната версия на FLUX, като същевременно предлага по-висока ефективност на работа и по-добро използване на ресурсите в сравнение със стандартни модели със същия размер."}, "flux-kontext/dev": {"description": "Модел за редактиране на изображения Frontier."}, "flux-merged": {"description": "FLUX.1-merged комбинира дълбоките характеристики, изследвани в разработката на \"DEV\" версията, с високоскоростните предимства на \"Schnell\". Тази комбинация не само разширява границите на производителността на модела, но и увеличава обхвата на неговото приложение."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] може да обработва текст и референтни изображения като вход, осигурявайки безпроблемно целенасочено локално редактиране и сложни трансформации на цялостната сцена."}, "flux-schnell": {"description": "FLUX.1 [schnell] е най-напредналият отворен модел с малък брой стъпки, който надминава конкурентите си и дори превъзхожда мощни нефино настроени модели като Midjourney v6.0 и DALL·E 3 (HD). Моделът е специално фино настроен, за да запази пълното разнообразие на изхода от предварителното обучение и значително подобрява визуалното качество, следването на инструкции, промяната на размери/пропорции, обработката на шрифтове и разнообразието на изхода в сравнение с най-съвременните модели на пазара, предоставяйки по-богато и разнообразно творческо генериране на изображения."}, "flux.1-schnell": {"description": "Коригиран потоков трансформър с 12 милиарда параметри, способен да генерира изображения въз основа на текстово описание."}, "flux/schnell": {"description": "FLUX.1 [schnell] е потоков трансформаторен модел с 12 милиарда параметри, способен да генерира висококачествени изображения от текст в 1 до 4 стъпки, подходящ за лична и търговска употреба."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) предлага стабилна и настройваема производителност, идеален избор за решения на сложни задачи."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) предлага отлична поддръжка на многомодални данни, фокусирайки се върху ефективното решаване на сложни задачи."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro е високопроизводителен AI модел на Google, проектиран за разширяване на широк спектър от задачи."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 е ефективен многомодален модел, който поддържа разширяване на широк спектър от приложения."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 е ефективен мултимодален модел, който поддържа разширения за широко приложение."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B е ефективен многомодален модел, който поддържа разширения за широко приложение."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 е най-новият експериментален модел, който показва значителни подобрения в производителността както в текстови, така и в мултимодални приложения."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B е високоефективен мултимодален модел, който поддържа разширени приложения."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 предлага оптимизирани мултимодални способности, подходящи за различни сложни задачи."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash е най-новият многомодален AI модел на Google, който предлага бърза обработка и поддържа текстови, изображенчески и видео входове, подходящ за ефективно разширяване на множество задачи."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 е разширяемо многомодално AI решение, което поддържа широк спектър от сложни задачи."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 е най-новият модел, готов за производство, който предлага по-високо качество на изхода, особено в математически, дълги контексти и визуални задачи."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 предоставя отлична мултимодална обработка, давайки по-голяма гъвкавост при разработката на приложения."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 комбинира най-новите оптимизационни технологии, предоставяйки по-ефективни мултимодални способности за обработка на данни."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro поддържа до 2 милиона токена и е идеален избор за среден многомодален модел, подходящ за многостранна поддръжка на сложни задачи."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash предлага следващо поколение функции и подобрения, включително изключителна скорост, нативна употреба на инструменти, многомодално генериране и контекстен прозорец от 1M токена."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash предлага следващо поколение функции и подобрения, включително изключителна скорост, нативна употреба на инструменти, многомодално генериране и контекстен прозорец от 1M токена."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash моделна вариация, оптимизирана за икономичност и ниска латентност."}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash експериментален модел, който поддържа генериране на изображения"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash е вариант на модела, оптимизиран за икономичност и ниска латентност."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash е вариант на модела, оптимизиран за икономичност и ниска латентност."}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash предварителен модел, поддържащ генериране на изображения"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash е най-ефективният модел на Google, предлагащ пълна функционалност."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite е най-малкият и най-ефективен модел на Google, създаден специално за масово използване."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview е най-малкият и най-ефективен модел на Google, проектиран за мащабна употреба."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview е моделът с най-добро съотношение цена-качество на Google, предлагащ пълна функционалност."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview е най-ефективният модел на Google, предлагащ пълна функционалност."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro е най-напредналият мисловен модел на Google, способен да разсъждава върху сложни проблеми в областта на кода, математиката и STEM, както и да анализира големи набори от данни, кодови бази и документи с дълъг контекст."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview е най-напредналият модел на Google за мислене, способен да разсъждава по сложни проблеми в кодиране, математика и STEM области, както и да анализира големи набори от данни, кодови библиотеки и документи с дълъг контекст."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview е най-напредналият модел на Google за мислене, способен да разсъждава по сложни проблеми в кодиране, математика и STEM области, както и да анализира големи набори от данни, кодови библиотеки и документи с дълъг контекст."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview е най-напредналият мисловен модел на Google, способен да разсъждава върху сложни проблеми в областта на кодирането, математиката и STEM, както и да анализира големи набори от данни, кодови бази и документи с дълъг контекст."}, "gemma-7b-it": {"description": "Gemma 7B е подходяща за обработка на средни и малки задачи, съчетаваща икономичност."}, "gemma2": {"description": "Gemma 2 е ефективен модел, предста<PERSON><PERSON><PERSON> от Google, обхващащ множество приложения от малки до сложни обработки на данни."}, "gemma2-9b-it": {"description": "Gemma 2 9B е модел, оптимизиран за специфични задачи и интеграция на инструменти."}, "gemma2:27b": {"description": "Gemma 2 е ефективен модел, предста<PERSON><PERSON><PERSON> от Google, обхващащ множество приложения от малки до сложни обработки на данни."}, "gemma2:2b": {"description": "Gemma 2 е ефективен модел, предста<PERSON><PERSON><PERSON> от Google, обхващащ множество приложения от малки до сложни обработки на данни."}, "generalv3": {"description": "Spark Pro е високопроизводителен голям езиков модел, оптимизиран за професионални области, фокусирайки се върху математика, програмиране, медицина, образование и др., и поддържа свързано търсене и вградени плъгини за времето, датата и др. Оптимизираният модел показва отлични резултати и висока производителност в сложни отговори на знания, разбиране на езика и високо ниво на текстово генериране, което го прави идеален избор за професионални приложения."}, "generalv3.5": {"description": "Spark3.5 Max е най-пълната версия, поддържаща свързано търсене и множество вградени плъгини. Неговите напълно оптимизирани основни способности, системни роли и функции за извикване на функции осигуряват изключителни резултати в различни сложни приложения."}, "glm-4": {"description": "GLM-4 е старата флагманска версия, пусната през януари 2024 г., която в момента е заменена от по-силната GLM-4-0520."}, "glm-4-0520": {"description": "GLM-4-0520 е най-новата версия на модела, проектирана за високо сложни и разнообразни задачи, с отлични резултати."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat показва висока производителност в множество области, включително семантика, математика, логическо разсъждение, код и знания. Също така предлага уеб браузинг, изпълнение на код, извикване на персонализирани инструменти и разсъждение върху дълги текстове. Поддържа 26 езика, включително японски, корейски и немски."}, "glm-4-air": {"description": "GLM-4-Air е икономичен вариант, с производителност близка до GLM-4, предлагаща бързина и достъпна цена."}, "glm-4-air-250414": {"description": "GLM-4-Air е версия с висока стойност, с производителност, близка до GLM-4, предлагаща бързина и достъпна цена."}, "glm-4-airx": {"description": "GLM-4-AirX предлага ефективна версия на GLM-4-Air, с скорост на извеждане до 2.6 пъти."}, "glm-4-alltools": {"description": "GLM-4-AllTools е многофункционален интелигентен модел, оптимизиран за поддръжка на сложни инструкции и извиквания на инструменти, като уеб браузинг, обяснение на код и генериране на текст, подходящ за изпълнение на множество задачи."}, "glm-4-flash": {"description": "GLM-4-Flash е идеалният избор за обработка на прости задачи, с най-бърза скорост и най-добра цена."}, "glm-4-flash-250414": {"description": "GLM-4-Flash е идеалният избор за обработка на прости задачи, с най-бърза скорост и безплатен."}, "glm-4-flashx": {"description": "GLM-4-FlashX е подобрена версия на Flash с изключително бърза скорост на извеждане."}, "glm-4-long": {"description": "GLM-4-Long поддържа извеждане на много дълги текстове, подходящ за задачи, свързани с памет и обработка на големи документи."}, "glm-4-plus": {"description": "GLM-4-Plus, като флагман с висока интелигентност, разполага с мощни способности за обработка на дълги текстове и сложни задачи, с цялостно подобрена производителност."}, "glm-4.1v-thinking-flash": {"description": "Серията модели GLM-4.1V-Thinking е най-мощният визуален модел сред известните VLM модели с размер около 10 милиарда параметри, обединяващ водещи в класа си задачи за визуално-езиково разбиране, включително видео разбиране, въпроси и отговори върху изображения, решаване на предметни задачи, OCR разпознаване на текст, интерпретация на документи и графики, GUI агент, кодиране на уеб страници, Grounding и други. Някои от задачите дори превъзхождат модели с 8 пъти повече параметри като Qwen2.5-VL-72B. Чрез водещи техники за подсилено обучение моделът овладява разсъждения чрез вериги на мисълта, което значително подобрява точността и богатството на отговорите, превъзхождайки традиционните модели без мисловен процес по отношение на крайния резултат и обяснимостта."}, "glm-4.1v-thinking-flashx": {"description": "Серията модели GLM-4.1V-Thinking е най-мощният визуален модел сред известните VLM модели с размер около 10 милиарда параметри, обединяващ водещи в класа си задачи за визуално-езиково разбиране, включително видео разбиране, въпроси и отговори върху изображения, решаване на предметни задачи, OCR разпознаване на текст, интерпретация на документи и графики, GUI агент, кодиране на уеб страници, Grounding и други. Някои от задачите дори превъзхождат модели с 8 пъти повече параметри като Qwen2.5-VL-72B. Чрез водещи техники за подсилено обучение моделът овладява разсъждения чрез вериги на мисълта, което значително подобрява точността и богатството на отговорите, превъзхождайки традиционните модели без мисловен процес по отношение на крайния резултат и обяснимостта."}, "glm-4.5": {"description": "Най-новият флагмански модел на Zhizhu, поддържащ режим на мислене, с общи способности на ниво SOTA сред отворените модели и контекстова дължина до 128K."}, "glm-4.5-air": {"description": "Леката версия на GLM-4.5, балансираща между производителност и цена, с възможност за гъвкаво превключване на смесен мисловен режим."}, "glm-4.5-airx": {"description": "Експресната версия на GLM-4.5-Air с по-бърза реакция, специално създадена за големи мащаби и високи скорости."}, "glm-4.5-flash": {"description": "Безплатната версия на GLM-4.5, с отлични резултати в задачи за разсъждение, кодиране и интелигентни агенти."}, "glm-4.5-x": {"description": "Експресната версия на GLM-4.5, която съчетава силна производителност с генериране на скорост до 100 токена в секунда."}, "glm-4v": {"description": "GLM-4V предлага мощни способности за разбиране и разсъждение на изображения, поддържаща множество визуални задачи."}, "glm-4v-flash": {"description": "GLM-4V-Flash се фокусира върху ефективното разбиране на единични изображения, подходящо за сцени с бърз анализ на изображения, като например анализ в реално време или обработка на партидни изображения."}, "glm-4v-plus": {"description": "GLM-4V-Plus разполага с разбиране на видео съдържание и множество изображения, подходящ за мултимодални задачи."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus притежава способността да разбира видео съдържание и множество изображения, подходящ за мултимодални задачи."}, "glm-z1-air": {"description": "Модел за разсъждение: притежава силни способности за разсъждение, подходящ за задачи, изискващи дълбочинно разсъждение."}, "glm-z1-airx": {"description": "Супер бързо разсъждение: с изключително бърза скорост на разсъждение и силни резултати."}, "glm-z1-flash": {"description": "Серията GLM-Z1 притежава мощни способности за сложни разсъждения и се представя отлично в логическо мислене, математика и програмиране."}, "glm-z1-flashx": {"description": "Висока скорост и ниска цена: Flash подобрена версия с изключително бърза скорост на инференция и по-добра гаранция за паралелна обработка."}, "glm-zero-preview": {"description": "GLM-Zero-Preview притежава мощни способности за сложни разсъждения, показвайки отлични резултати в логическото разсъждение, математиката и програмирането."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash предлага следващо поколение функции и подобрения, включително изключителна скорост, нативна употреба на инструменти, многомодално генериране и контекстен прозорец от 1M токена."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental е най-новият експериментален мултимодален AI модел на Google, с определено подобрение в качеството в сравнение с предишните версии, особено по отношение на световни знания, код и дълъг контекст."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash е най-усъвършенстваният основен модел на Google, специално проектиран за напреднали задачи по разсъждение, кодиране, математика и наука. Той включва вградена способност за „мислене“, която му позволява да предоставя отговори с по-висока точност и по-детайлна обработка на контекста.\n\nЗабележка: Този модел има два варианта: с мислене и без мислене. Ценообразуването на изхода се различава значително в зависимост от това дали способността за мислене е активирана. Ако изберете стандартния вариант (без суфикса „:thinking“), моделът ясно избягва генерирането на мисловни токени.\n\nЗа да използвате способността за мислене и да получавате мисловни токени, трябва да изберете варианта „:thinking“, което ще доведе до по-висока цена за изход с мислене.\n\nОсвен това, Gemini 2.5 Flash може да бъде конфигуриран чрез параметъра „максимален брой токени за разсъждение“, както е описано в документацията (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash е най-напредналият основен модел на Google, проектиран за напреднали разсъждения, кодиране, математика и научни задачи. Той включва вградена способност за \"мислене\", което му позволява да предоставя отговори с по-висока точност и детайлна обработка на контекста.\n\nЗабележка: Този модел има два варианта: с мислене и без мислене. Цените на изхода значително варират в зависимост от активирането на способността за мислене. Ако изберете стандартния вариант (без суфикс \":thinking\"), моделът ще избягва генерирането на токени за мислене.\n\nЗа да се възползвате от способността за мислене и да получите токени за мислене, трябва да изберете варианта \":thinking\", което ще доведе до по-високи цени на изхода за мислене.\n\nОсвен това, Gemini 2.5 Flash може да бъде конфигуриран чрез параметъра \"максимален брой токени за разсъждение\", както е описано в документацията (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash е най-напредналият основен модел на Google, проектиран за напреднали разсъждения, кодиране, математика и научни задачи. Той включва вградена способност за \"мислене\", което му позволява да предоставя отговори с по-висока точност и детайлна обработка на контекста.\n\nЗабележка: Този модел има два варианта: с мислене и без мислене. Цените на изхода значително варират в зависимост от активирането на способността за мислене. Ако изберете стандартния вариант (без суфикс \":thinking\"), моделът ще избягва генерирането на токени за мислене.\n\nЗа да се възползвате от способността за мислене и да получите токени за мислене, трябва да изберете варианта \":thinking\", което ще доведе до по-високи цени на изхода за мислене.\n\nОсвен това, Gemini 2.5 Flash може да бъде конфигуриран чрез параметъра \"максимален брой токени за разсъждение\", както е описано в документацията (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro е най-усъвършенстваният мисловен модел на Google, способен да извършва разсъждения върху сложни проблеми в областта на кодирането, математиката и STEM, както и да анализира големи набори от данни, кодови бази и документи с дълъг контекст."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview е най-усъвършенстваният мисловен модел на Google, способен да извършва разсъждения върху сложни проблеми в областта на кодирането, математиката и STEM, както и да анализира големи набори от данни, кодови бази и документи с дълъг контекст."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash предлага оптимизирани мултимодални обработващи способности, подходящи за различни сложни задачи."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro комбинира най-новите оптимизационни технологии, предоставяйки по-ефективна обработка на мултимодални данни."}, "google/gemma-2-27b": {"description": "Gemma 2 е ефективен модел, предста<PERSON><PERSON><PERSON> от Google, обхващащ множество приложения от малки приложения до сложна обработка на данни."}, "google/gemma-2-27b-it": {"description": "Gemma 2 продължава концепцията за лекота и ефективност."}, "google/gemma-2-2b-it": {"description": "Лек модел за настройка на инструкции от Google."}, "google/gemma-2-9b": {"description": "Gemma 2 е ефективен модел, предста<PERSON><PERSON><PERSON> от Google, обхващащ множество приложения от малки приложения до сложна обработка на данни."}, "google/gemma-2-9b-it": {"description": "Gemma 2 е серия от леки отворени текстови модели на Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 е лека отворена текстова моделна серия на Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) предлага основни способности за обработка на инструкции, подходящи за леки приложения."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B е отворен езиков модел на Google, който поставя нови стандарти за ефективност и производителност."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B е отворен езиков модел на Google, който поставя нови стандарти за ефективност и производителност."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo, подходящ за различни задачи по генериране и разбиране на текст, в момента сочи към gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo, подходящ за различни задачи по генериране и разбиране на текст, в момента сочи към gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo, подходящ за различни задачи по генериране и разбиране на текст, в момента сочи към gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo, подходящ за различни задачи по генериране и разбиране на текст, в момента сочи към gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo е ефективен модел, предоставен от OpenAI, подходящ за чат и генериране на текст, поддържащ паралелни извиквания на функции."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k е модел с висока капацитет за генериране на текст, подходящ за сложни задачи."}, "gpt-4": {"description": "GPT-4 предлага по-голям контекстуален прозорец, способен да обработва по-дълги текстови входове, подходящ за сценарии, изискващи интеграция на обширна информация и анализ на данни."}, "gpt-4-0125-preview": {"description": "Най-новият модел GPT-4 Turbo разполага с визуални функции. Сега визуалните заявки могат да се използват с JSON формат и извиквания на функции. GPT-4 Turbo е подобрена версия, която предлага икономически ефективна поддръжка за мултимодални задачи. Той намира баланс между точност и ефективност, подходящ за приложения, изискващи взаимодействие в реално време."}, "gpt-4-0613": {"description": "GPT-4 предлага по-голям контекстуален прозорец, способен да обработва по-дълги текстови входове, подходящ за сценарии, изискващи интеграция на обширна информация и анализ на данни."}, "gpt-4-1106-preview": {"description": "Най-новият модел GPT-4 Turbo разполага с визуални функции. Сега визуалните заявки могат да се използват с JSON формат и извиквания на функции. GPT-4 Turbo е подобрена версия, която предлага икономически ефективна поддръжка за мултимодални задачи. Той намира баланс между точност и ефективност, подходящ за приложения, изискващи взаимодействие в реално време."}, "gpt-4-32k": {"description": "GPT-4 предлага по-голям контекстуален прозорец, способен да обработва по-дълги текстови входове, подходящ за сценарии, изискващи интеграция на обширна информация и анализ на данни."}, "gpt-4-32k-0613": {"description": "GPT-4 предлага по-голям контекстуален прозорец, способен да обработва по-дълги текстови входове, подходящ за сценарии, изискващи интеграция на обширна информация и анализ на данни."}, "gpt-4-turbo": {"description": "Най-новият модел GPT-4 Turbo разполага с визуални функции. Сега визуалните заявки могат да се използват с JSON формат и извиквания на функции. GPT-4 Turbo е подобрена версия, която предлага икономически ефективна поддръжка за мултимодални задачи. Той намира баланс между точност и ефективност, подходящ за приложения, изискващи взаимодействие в реално време."}, "gpt-4-turbo-2024-04-09": {"description": "Най-новият модел GPT-4 Turbo разполага с визуални функции. Сега визуалните заявки могат да се използват с JSON формат и извиквания на функции. GPT-4 Turbo е подобрена версия, която предлага икономически ефективна поддръжка за мултимодални задачи. Той намира баланс между точност и ефективност, подходящ за приложения, изискващи взаимодействие в реално време."}, "gpt-4-turbo-preview": {"description": "Най-новият модел GPT-4 Turbo разполага с визуални функции. Сега визуалните заявки могат да се използват с JSON формат и извиквания на функции. GPT-4 Turbo е подобрена версия, която предлага икономически ефективна поддръжка за мултимодални задачи. Той намира баланс между точност и ефективност, подходящ за приложения, изискващи взаимодействие в реално време."}, "gpt-4-vision-preview": {"description": "Най-новият модел GPT-4 Turbo разполага с визуални функции. Сега визуалните заявки могат да се използват с JSON формат и извиквания на функции. GPT-4 Turbo е подобрена версия, която предлага икономически ефективна поддръжка за мултимодални задачи. Той намира баланс между точност и ефективност, подходящ за приложения, изискващи взаимодействие в реално време."}, "gpt-4.1": {"description": "GPT-4.1 е нашият флагмански модел за сложни задачи. Той е изключително подходящ за решаване на проблеми в различни области."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini предлага баланс между интелигентност, скорост и разходи, което го прави привлекателен модел за много случаи на употреба."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini предлага баланс между интелигентност, скорост и разходи, което го прави привлекателен модел за много случаи на употреба."}, "gpt-4.5-preview": {"description": "Изследователската предварителна версия на GPT-4.5, която е нашият най-голям и мощен GPT модел до момента. Тя притежава обширни знания за света и може по-добре да разбира намеренията на потребителите, което я прави изключително ефективна в креативни задачи и автономно планиране. GPT-4.5 приема текстови и изображен вход и генерира текстови изход (включително структурирани изходи). Поддържа ключови функции за разработчици, като извикване на функции, пакетно API и потоков изход. В задачи, изискващи креативно, открито мислене и диалог (като писане, учене или изследване на нови идеи), GPT-4.5 показва особени способности. Крайната дата на знанията е октомври 2023."}, "gpt-4o": {"description": "ChatGPT-4o е динамичен модел, който се актуализира в реално време, за да поддържа най-новата версия. Той комбинира мощно разбиране на езика и генериране на текст, подходящ за мащабни приложения, включително обслужване на клиенти, образование и техническа поддръжка."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o е динамичен модел, който се актуализира в реално време, за да поддържа най-новата версия. Той комбинира мощно разбиране на езика и генериране на текст, подходящ за мащабни приложения, включително обслужване на клиенти, образование и техническа поддръжка."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o е динамичен модел, който се актуализира в реално време, за да поддържа най-новата версия. Той комбинира мощно разбиране на езика и генериране на текст, подходящ за мащабни приложения, включително обслужване на клиенти, образование и техническа поддръжка."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o е динамичен модел, който се актуализира в реално време, за да поддържа най-новата версия. Той съчетава мощно разбиране и генериране на език и е подходящ за мащабни приложения, включително обслужване на клиенти, образование и техническа поддръжка."}, "gpt-4o-audio-preview": {"description": "Модел GPT-4o Audio, поддържащ вход и изход на аудио."}, "gpt-4o-mini": {"description": "GPT-4o mini е най-новият модел на OpenAI, след GPT-4 Omni, който поддържа текстово и визуално въвеждане и генерира текст. Като най-напредналият им малък модел, той е значително по-евтин от другите нови модели и е с над 60% по-евтин от GPT-3.5 Turbo. Запазва най-съвременната интелигентност, като същевременно предлага значителна стойност за парите. GPT-4o mini получи 82% на теста MMLU и в момента е с по-висок рейтинг от GPT-4 по предпочитания за чат."}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini аудио модел, поддържа вход и изход на аудио."}, "gpt-4o-mini-realtime-preview": {"description": "Реален вариант на GPT-4o-mini, поддържащ вход и изход на аудио и текст в реално време."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini предварителна версия за търсене е модел, специално обучен за разбиране и изпълнение на заявки за уеб търсене, използващ Chat Completions API. Освен таксите за токени, заявките за уеб търсене се таксуват и на всяко извикване на инструмента."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe е модел за преобразуване на реч в текст, използващ GPT-4o за транскрибиране на аудио. В сравнение с оригиналния модел Whisper, той намалява процента на грешки в думите и подобрява разпознаването на езика и точността. Използвайте го за по-точни транскрипции."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS е модел за преобразуване на текст в реч, базиран на GPT-4o mini, предлагащ висококачествено генериране на реч при по-ниска цена."}, "gpt-4o-realtime-preview": {"description": "Реален вариант на GPT-4o, поддържа<PERSON> вход и изход на аудио и текст в реално време."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Реален вариант на GPT-4o, поддържа<PERSON> вход и изход на аудио и текст в реално време."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Реално време версия на GPT-4o, поддържаща едновременно аудио и текстов вход и изход."}, "gpt-4o-search-preview": {"description": "GPT-4o предварителна версия за търсене е модел, специално обучен за разбиране и изпълнение на заявки за уеб търсене, използващ Chat Completions API. Освен таксите за токени, заявките за уеб търсене се таксуват и на всяко извикване на инструмента."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe е модел за преобразуване на реч в текст, използващ GPT-4o за транскрибиране на аудио. В сравнение с оригиналния модел Whisper, той намалява процента на грешки в думите и подобрява разпознаването на езика и точността. Използвайте го за по-точни транскрипции."}, "gpt-image-1": {"description": "Роден мултимодален модел за генериране на изображения ChatGPT."}, "grok-2-1212": {"description": "Този модел е подобрен по отношение на точност, спазване на инструкции и многоезични способности."}, "grok-2-image-1212": {"description": "Нашият най-нов модел за генериране на изображения може да създава живи и реалистични изображения въз основа на текстови подсказки. Той се представя отлично в маркетинг, социални медии и развлекателни области."}, "grok-2-vision-1212": {"description": "Този модел е подобрен по отношение на точност, спазване на инструкции и многоезични способности."}, "grok-3": {"description": "Флагмански модел, експертен в извличане на данни, програмиране и обобщаване на текст за корпоративни приложения, с дълбоки знания в областите финанси, медицина, право и наука."}, "grok-3-fast": {"description": "Флагмански модел, експертен в извличане на данни, програмиране и обобщаване на текст за корпоративни приложения, с дълбоки знания в областите финанси, медицина, право и наука."}, "grok-3-mini": {"description": "Лек модел, който мисли преди разговор. Работи бързо и интелигентно, подходящ за логически задачи без нужда от дълбоки специализирани знания и позволява проследяване на оригиналния мисловен процес."}, "grok-3-mini-fast": {"description": "Лек модел, който мисли преди разговор. Работи бързо и интелигентно, подходящ за логически задачи без нужда от дълбоки специализирани знания и позволява проследяване на оригиналния мисловен процес."}, "grok-4": {"description": "Нашият най-нов и най-мощен флагмански модел, който се отличава с изключителни резултати в обработката на естествен език, математическите изчисления и разсъжденията — перфектен универсален играч."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B е езиков модел, който комбинира креативност и интелигентност, обединявайки множество водещи модели."}, "hunyuan-a13b": {"description": "Hunyuan е първият хибриден разсъждаващ модел, ъпгрейд на hunyuan-standard-256K, с общо 80 милиарда параметри и 13 милиарда активирани. По подразбиране работи в режим на бавно мислене, като поддържа превключване между бърз и бавен режим чрез параметри или инструкции, като превключването се осъществява чрез добавяне на query префикс / no_think. Общите способности са значително подобрени спрямо предишното поколение, особено в областите математика, наука, разбиране на дълги текстове и агентски функции."}, "hunyuan-code": {"description": "Най-новият модел за генериране на код на HunYuan, обучен с 200B висококачествени данни за код, с шестмесечно обучение на данни за SFT с високо качество, увеличен контекстен прозорец до 8K, и водещи резултати в автоматичните оценъчни показатели за генериране на код на пет основни езика; в комплексната оценка на кодови задачи на пет основни езика, представянето е в първата група."}, "hunyuan-functioncall": {"description": "Най-новият модел на HunYuan с MOE архитектура за извикване на функции, обучен с висококачествени данни за извикване на функции, с контекстен прозорец от 32K, водещ в множество измерения на оценъчните показатели."}, "hunyuan-large": {"description": "Моделът Hunyuan-large има общ брой параметри около 389B, активни параметри около 52B, и е най-голямият и най-добър в индустрията отворен MoE модел с архитектура Transformer."}, "hunyuan-large-longcontext": {"description": "Специализира в обработката на дълги текстови задачи, като резюмета на документи и отговори на въпроси, и също така притежава способността да обработва общи текстови генериращи задачи. Показва отлични резултати в анализа и генерирането на дълги текстове, ефективно справяйки се с комплексни и подробни изисквания за обработка на дълги текстове."}, "hunyuan-large-vision": {"description": "Този модел е подходящ за сцени с разбиране на изображения и текст, базиран на Hunyuan Large, голям визуално-езиков модел, който поддържа вход с множество изображения с произволна резолюция и текст, генерира текстово съдържание, фокусиран върху задачи, свързани с разбиране на изображения и текст, с значително подобрени мултиезикови способности за разбиране на изображения и текст."}, "hunyuan-lite": {"description": "Актуализиран до MOE структура, контекстният прозорец е 256k, водещ в множество оценъчни набори в NLP, код, математика и индустрия, пред много от отворените модели."}, "hunyuan-lite-vision": {"description": "Най-новият 7B мултимодален модел на Hunyuan, с контекстен прозорец от 32K, поддържа мултимодални разговори на китайски и английски, разпознаване на обекти в изображения, разбиране на документи и таблици, мултимодална математика и др., с показатели, които надвишават 7B конкурентни модели в множество измерения."}, "hunyuan-pro": {"description": "Модел с параметри от триллион MOE-32K за дълги текстове. Постига абсолютни водещи нива в различни бенчмаркове, с комплексни инструкции и разсъждения, притежаващи сложни математически способности, поддържа функция за извикване, с акцент върху оптимизацията в области като многоезичен превод, финанси, право и медицина."}, "hunyuan-role": {"description": "Най-новият модел за ролеви игри на HunYuan, официално настроен и обучен от HunYuan, базиран на модела HunYuan и данни от набори за ролеви игри, с по-добри основни резултати в ролевите игри."}, "hunyuan-standard": {"description": "Използва по-добра стратегия за маршрутизиране, като същевременно облекчава проблемите с балансирането на натоварването и сближаването на експертите. За дълги текстове, показателят за откритие достига 99.9%. MOE-32K предлага по-добра цена-качество, балансирайки ефективността и цената, и позволява обработка на дълги текстови входове."}, "hunyuan-standard-256K": {"description": "Използва по-добра стратегия за маршрутизиране, като същевременно облекчава проблемите с балансирането на натоварването и сближаването на експертите. За дълги текстове, показателят за откритие достига 99.9%. MOE-256K прави допълнителен пробив в дължината и ефективността, значително разширявайки допустимата дължина на входа."}, "hunyuan-standard-vision": {"description": "Най-новият мултимодален модел на Hunyuan, поддържащ отговори на множество езици, с балансирани способности на китайски и английски."}, "hunyuan-t1-20250321": {"description": "Цялостно изграждане на моделни способности в хуманитарни и точни науки, с висока способност за улавяне на дълги текстови информации. Поддържа разсъждения и отговори на научни въпроси от всякаква трудност, включително математика, логика, наука и код."}, "hunyuan-t1-20250403": {"description": "Подобряване на възможностите за генериране на код на проектно ниво; повишаване качеството на текстовото писане; подобряване на разбирането на теми, многократното следване на инструкции и разбирането на думи и изрази; оптимизиране на проблемите с изход, смесващ опростен и традиционен китайски, както и китайски и английски."}, "hunyuan-t1-20250529": {"description": "Оптимизиран за текстово творчество и писане на есета, подобрява уменията в кодирането, математиката и логическото разсъждение, както и способността за следване на инструкции."}, "hunyuan-t1-20250711": {"description": "Значително подобрени способности в сложна математика, логика и кодиране, оптимизирана стабилност на изхода и подобрена работа с дълги текстове."}, "hunyuan-t1-latest": {"description": "Първият в индустрията свръхголям хибриден трансформаторен модел за инференция, който разширява инференционните способности, предлага изключителна скорост на декодиране и допълнително съгласува човешките предпочитания."}, "hunyuan-t1-vision": {"description": "Модел за дълбоко мултимодално разбиране Hunyuan, поддържащ естествени мултимодални вериги на мислене, експертен в различни сценарии за разсъждение върху изображения, с цялостно подобрение спрямо бързите мисловни модели при научни задачи."}, "hunyuan-t1-vision-20250619": {"description": "Най-новият мултимодален дълбок мислещ модел t1-vision на Hunyuan, който поддържа оригинални мултимодални вериги на мисълта и предлага цялостно подобрение спрямо предишната версия по подразбиране."}, "hunyuan-turbo": {"description": "Предварителна версия на новото поколение голям езиков модел на HunYuan, използваща нова структура на смесен експертен модел (MoE), с по-бърза скорост на извеждане и по-силни резултати в сравнение с hunyuan-pro."}, "hunyuan-turbo-20241223": {"description": "Оптимизация в тази версия: скалиране на данни и инструкции, значително повишаване на общата генерализационна способност на модела; значително повишаване на математическите, кодовите и логическите способности; оптимизиране на свързаните с разбирането на текста и думите способности; оптимизиране на качеството на генерираното съдържание при създаване на текст."}, "hunyuan-turbo-latest": {"description": "Оптимизация на общото изживяване, включително разбиране на NLP, създаване на текст, разговори, отговори на въпроси, превод и специфични области; повишаване на хуманността, оптимизиране на емоционалната интелигентност на модела; подобряване на способността на модела да изяснява при неясни намерения; повишаване на способността за обработка на въпроси, свързани с анализ на думи; подобряване на качеството и интерактивността на създаването; подобряване на многократното изживяване."}, "hunyuan-turbo-vision": {"description": "Новото поколение визуално езиково флагманско голямо модел на Hunyuan, използващо нова структура на смесен експертен модел (MoE), с цялостно подобрение на способностите за основно разпознаване, създаване на съдържание, отговори на въпроси и анализ и разсъждение в сравнение с предишното поколение модели."}, "hunyuan-turbos-20250313": {"description": "Уеднаквяване на стила на стъпките за решаване на математически задачи, засилване на многократните въпроси и отговори по математика. Оптимизация на стила на отговорите при текстово творчество, премахване на изкуствения интелектуален оттенък и добавяне на литературна изразителност."}, "hunyuan-turbos-20250416": {"description": "Актуализация на предварително обучената основа, засилване на разбирането и следването на инструкции; подобряване на научните способности в математика, кодиране, логика и наука по време на фазата на съгласуване; повишаване качеството на творческото писане, разбирането на текстове, точността на преводите и знанията в хуманитарните науки; засилване на възможностите на агенти в различни области, с особен акцент върху разбирането на многократни диалози."}, "hunyuan-turbos-20250604": {"description": "Актуализирана предварително обучена основа, подобрени умения за писане и разбиране на текст, значително подобрени способности в кодирането и точните науки, както и непрекъснато усъвършенстване в следването на сложни инструкции."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS е последната версия на флагманския модел Hunyuan, с по-силни способности за разсъждение и по-добро потребителско изживяване."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Специализирана в обработката на дълги текстови задачи като резюмета на документи и въпроси и отговори, също така притежава способността да се справя с общи задачи по генериране на текст. Показва отлични резултати в анализа и генерирането на дълги текстове, ефективно справяйки се с комплексни и детайлни изисквания за обработка на дълги текстове."}, "hunyuan-turbos-role-plus": {"description": "Най-новият модел за ролеви игри на Hunyuan, официално фино настроен и обучен от Hunyuan, базиран на Hunyuan модел с допълнително обучение върху набор от данни за ролеви игри, осигуряващ по-добри основни резултати в ролеви игрови сцени."}, "hunyuan-turbos-vision": {"description": "Този модел е предназначен за задачи по разбиране на изображения и текст, базиран на най-новия turbos модел на Hunyuan, ново поколение водещ визуално-езиков модел, фокусиран върху задачи като разпознаване на обекти в изображения, въпроси и отговори, създаване на текстове и решаване на задачи чрез снимки, с цялостно подобрение спрямо предишното поколение."}, "hunyuan-turbos-vision-20250619": {"description": "Най-новият водещ визуално-езиков модел turbos-vision на Hunyuan, който предлага цялостно подобрение спрямо предишната версия по подразбиране в задачи, свързани с разбиране на изображения и текст, включително разпознаване на обекти, въпроси и отговори, създаване на текстове и решаване на задачи чрез снимки."}, "hunyuan-vision": {"description": "Най-новият мултимодален модел на HunYuan, поддържащ генериране на текстово съдържание от изображения и текстови входове."}, "image-01": {"description": "Нов модел за генериране на изображения с фини детайли, поддържащ генериране от текст и изображения."}, "image-01-live": {"description": "Модел за генериране на изображения с фини детайли, поддържащ генериране от текст и настройка на стил."}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 4-то поколение текст-към-изображение модел серия"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 4-то поколение текст-към-изображение модел серия Ултра версия"}, "imagen4/preview": {"description": "Най-висококачественият модел за генериране на изображения на Google."}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 предлага интелигентни решения за диалог в множество сценарии."}, "internlm2.5-latest": {"description": "Нашата най-нова серия модели с изключителни способности за извеждане, поддържаща контекстна дължина от 1M и по-силни способности за следване на инструкции и извикване на инструменти."}, "internlm3-latest": {"description": "Нашата най-нова серия модели с изключителна производителност на разсъжденията, водеща в категорията на отворените модели. По подразбиране сочи към най-ново публикуваната серия модели InternLM3."}, "internvl2.5-latest": {"description": "Версията InternVL2.5, която все още поддържаме, предлага отлична и стабилна производителност. По подразбиране сочи към нашата най-нова версия на серията InternVL2.5, текущо сочи към internvl2.5-78b."}, "internvl3-latest": {"description": "Нашият най-нов мултимодален голям модел, с по-силни способности за разбиране на текст и изображения, дългосрочно разбиране на изображения, производителност, сравнима с водещи затворени модели. По подразбиране сочи към нашата най-нова версия на серията InternVL, текущо сочи към internvl3-78b."}, "irag-1.0": {"description": "Собствената технология iRAG (image based RAG) на Baidu за генериране на изображения с подсилено търсене, комбинираща милиарди изображения от търсачката на Baidu с мощни основни модели, позволява създаването на изключително реалистични изображения, далеч надминаващи родните системи за генериране на изображения от текст, без изкуствен вид и с ниски разходи. iRAG се характеризира с липса на халюцинации, изключителна реалистичност и незабавна готовност."}, "jamba-large": {"description": "Нашият най-мощен и напреднал модел, проектиран за справяне с комплексни задачи на корпоративно ниво, с изключителна производителност."}, "jamba-mini": {"description": "Най-ефективният модел в своя клас, съчетаващ скорост и качество, с по-малък обем."}, "jina-deepsearch-v1": {"description": "Дълбокото търсене комбинира интернет търсене, четене и разсъждение, за да извърши обширно разследване. Можете да го разглеждате като агент, който приема вашата изследователска задача - той ще извърши широко търсене и ще премине през множество итерации, преди да предостави отговор. Този процес включва непрекъснато изследване, разсъждение и решаване на проблеми от различни ъгли. Това е коренно различно от стандартните големи модели, които генерират отговори директно от предварително обучени данни, и от традиционните RAG системи, които разчитат на еднократни повърхностни търсения."}, "kimi-k2": {"description": "Kimi-K2 е базов модел с MoE архитектура, пуснат от Moonshot AI, с изключителни кодови и агентски способности, общо 1 трилион параметри и 32 милиарда активирани параметри. В бенчмаркове за общо знание, програмиране, математика и агентски задачи моделът K2 превъзхожда други водещи отворени модели."}, "kimi-k2-0711-preview": {"description": "kimi-k2 е базов модел с MoE архитектура с изключителни способности за кодиране и агентски функции, с общо 1 трилион параметри и 32 милиарда активни параметри. В тестове за общо знание, програмиране, математика и агентски задачи, моделът K2 превъзхожда други водещи отворени модели."}, "kimi-latest": {"description": "Kimi интелигентен асистент използва най-новия Kimi голям модел, който може да съдържа нестабилни функции. Поддържа разбиране на изображения и автоматично избира 8k/32k/128k модел за таксуване в зависимост от дължината на контекста на заявката."}, "kimi-thinking-preview": {"description": "Моделът kimi-thinking-preview, предоставен от „Тъмната страна на Луната“, е мултимодален мисловен модел с възможности за мултимодално и общо разсъждение, който е експерт в дълбокото разсъждение и помага за решаването на по-сложни задачи."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM е експериментален езиков модел, специфичен за задачи, обучен да отговаря на принципите на научното обучение, способен да следва системни инструкции в учебни и обучителни сценарии, да действа като експертен ментор и др."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM е експериментален езиков модел, специфичен за задачи, обучен да отговаря на принципите на научното обучение, способен да следва системни инструкции в учебни и обучителни сценарии, да действа като експертен ментор и др."}, "lite": {"description": "Spark Lite е лек модел на голям език, с изключително ниска латентност и ефективна обработка, напълно безплатен и отворен, поддържащ функции за онлайн търсене в реално време. Неговите бързи отговори го правят отличен за приложения на нискомощни устройства и фина настройка на модели, предоставяйки на потребителите отлична рентабилност и интелигентно изживяване, особено в контекста на въпроси и отговори, генериране на съдържание и търсене."}, "llama-2-7b-chat": {"description": "Llama2 е серия от големи модели за език (LLM), разработени и с отворен код от Meta. Това е набор от генеративни текстови модели с различен размер, от 7 милиарда до 70 милиарда параметри, които са претренирани и майсторски оптимизирани. Архитектурно, Llama2 е автоматично регресивен езиков модел, използващ оптимизирана трансформаторна архитектура. Подобренията включват супервизирано майсторско трениране (SFT) и подкрепено с учене с награди (RLHF) за подреждане на предпочтенията на хората за полезност и безопасност. Llama2 демонстрира значително подобрени резултати върху множество академични набори от данни, което предоставя възможности за дизайн и развитие на много други модели."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B предлага по-мощни способности за разсъждение на AI, подходящи за сложни приложения, поддържащи множество изчислителни обработки и осигуряващи ефективност и точност."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B е модел с висока производителност, предлагащ бързи способности за генериране на текст, особено подходящ за приложения, изискващи мащабна ефективност и икономичност."}, "llama-3.1-instruct": {"description": "Моделата Llama 3.1 с фина настройка за инструкции е оптимизирана за диалогови сценарии и надминава много съществуващи модели с отворен код в общи отраслови бенчмарк тестове."}, "llama-3.2-11b-vision-instruct": {"description": "Изключителни способности за визуално разсъждение върху изображения с висока разделителна способност, подходящи за приложения за визуално разбиране."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 е проектиран да обработва задачи, свързващи визуални и текстови данни. Той показва отлични резултати в задачи като описание на изображения и визуални въпроси и отговори, преодолявайки пропастта между генерирането на език и визуалното разсъждение."}, "llama-3.2-90b-vision-instruct": {"description": "Разширени способности за визуално разсъждение, подходящи за приложения на визуални агенти."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 е проектиран да обработва задачи, свързващи визуални и текстови данни. Той показва отлични резултати в задачи като описание на изображения и визуални въпроси и отговори, преодолявайки пропастта между генерирането на език и визуалното разсъждение."}, "llama-3.2-vision-instruct": {"description": "Моделът Llama 3.2-Vision с инструкции е оптимизиран за визуално разпознаване, изводи от изображения, описание на изображения и отговаряне на общи въпроси, свързани с изображения."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 е най-напредналият многоезичен отворен езиков модел от серията Llama, който предлага производителност, сравнима с 405B моделите, на изключително ниска цена. Базиран на структурата Transformer и подобрен чрез супервизирано фино настройване (SFT) и обучение с човешка обратна връзка (RLHF) за повишаване на полезността и безопасността. Неговата версия, оптимизирана за инструкции, е специално проектирана за многоезични диалози и показва по-добри резултати от много от отворените и затворените чат модели в множество индустриални бенчмаркове. Краен срок за знания: декември 2023."}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 е многоезичен модел за генерация на език (LLM) с 70B (вход/изход на текст), който е предварително обучен и е пригоден за указания. Чистият текстов модел на Llama 3.3 е оптимизиран за многоезични диалогови случаи и надминава много налични отворени и затворени чат модели на стандартни индустриални тестове."}, "llama-3.3-instruct": {"description": "Моделата Llama 3.3 с фина настройка за инструкции е оптимизирана за диалогови сценарии и надминава много съществуващи модели с отворен код в общи отраслови бенчмарк тестове."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B предлага ненадмината способност за обработка на сложност, проектирана за високи изисквания."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B предлага качествени способности за разсъждение, подходящи за множество приложения."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use предлага мощни способности за извикване на инструменти, поддържащи ефективна обработка на сложни задачи."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use е модел, оптимизиран за ефективна употреба на инструменти, поддържащ бързо паралелно изчисление."}, "llama3.1": {"description": "Llama 3.1 е водещ модел, представен от Meta, поддържащ до 405B параметри, приложим в области като сложни диалози, многоезичен превод и анализ на данни."}, "llama3.1:405b": {"description": "Llama 3.1 е водещ модел, представен от Meta, поддържащ до 405B параметри, приложим в области като сложни диалози, многоезичен превод и анализ на данни."}, "llama3.1:70b": {"description": "Llama 3.1 е водещ модел, представен от Meta, поддържащ до 405B параметри, приложим в области като сложни диалози, многоезичен превод и анализ на данни."}, "llava": {"description": "LLaVA е многомодален модел, комбиниращ визуален кодер и Vicuna, предназначен за мощно визуално и езиково разбиране."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B предлага интегрирани способности за визуална обработка, генерирайки сложни изходи чрез визуална информация."}, "llava:13b": {"description": "LLaVA е многомодален модел, комбиниращ визуален кодер и Vicuna, предназначен за мощно визуално и езиково разбиране."}, "llava:34b": {"description": "LLaVA е многомодален модел, комбиниращ визуален кодер и Vicuna, предназначен за мощно визуално и езиково разбиране."}, "mathstral": {"description": "MathΣtral е проектиран за научни изследвания и математически разсъждения, предоставяйки ефективни изчислителни способности и интерпретация на резултати."}, "max-32k": {"description": "Spark Max 32K е конфигуриран с голяма способност за обработка на контекст, с по-силно разбиране на контекста и логическо разсъждение, поддържащ текстови входове до 32K токена, подходящ за четене на дълги документи, частни въпроси и отговори и други сценарии."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct е голям езиков модел, напълно обучен от безкрайната връху чиповете. Megrez-3B-Instruct се стреми чрез концепцията за съвместно хардуерно-софтуерно взаимодействие да създаде решение за крайните устройства, което е бързо за извършване, компактно и лесно за използване."}, "meta-llama-3-70b-instruct": {"description": "Мо<PERSON>ен модел с 70 милиарда параметри, отличаващ се в разсъждения, кодиране и широки езикови приложения."}, "meta-llama-3-8b-instruct": {"description": "Универсален модел с 8 милиарда параметри, оптимизиран за диалогови и текстови генериращи задачи."}, "meta-llama-3.1-405b-instruct": {"description": "Моделите на Llama 3.1, настроени за инструкции, са оптимизирани за многоезични диалогови случаи на употреба и надминават много от наличните модели с отворен код и затворени чат модели на общи индустриални стандарти."}, "meta-llama-3.1-70b-instruct": {"description": "Моделите на Llama 3.1, настроени за инструкции, са оптимизирани за многоезични диалогови случаи на употреба и надминават много от наличните модели с отворен код и затворени чат модели на общи индустриални стандарти."}, "meta-llama-3.1-8b-instruct": {"description": "Моделите на Llama 3.1, настроени за инструкции, са оптимизирани за многоезични диалогови случаи на употреба и надминават много от наличните модели с отворен код и затворени чат модели на общи индустриални стандарти."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) предлага отлични способности за обработка на език и изключителен интерактивен опит."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 предлага отлични способности за обработка на език и невероятно потребителско изживяване."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) е мощен чат модел, поддържащ сложни изисквания за диалог."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) предлага многоезична поддръжка, обхващаща богати области на знание."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 е проектирана да обработва задачи, комбиниращи визуални и текстови данни. Тя демонстрира отлични резултати в задачи като описание на изображения и визуални въпроси и отговори, преодолявайки пропастта между генерирането на езици и визуалното разсъждение."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 е проектирана да обработва задачи, комбиниращи визуални и текстови данни. Тя демонстрира отлични резултати в задачи като описание на изображения и визуални въпроси и отговори, преодолявайки пропастта между генерирането на езици и визуалното разсъждение."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 е проектирана да обработва задачи, комбиниращи визуални и текстови данни. Тя демонстрира отлични резултати в задачи като описание на изображения и визуални въпроси и отговори, преодолявайки пропастта между генерирането на езици и визуалното разсъждение."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 многоезичен голям езиков модел (LLM) е предварително обучен и коригиран за инструкции в 70B (текстов вход/текстов изход). Моделът Llama 3.3, коригиран за инструкции, е оптимизиран за многоезични диалогови случаи и превъзхожда много налични отворени и затворени чат модели на общи индустриални бенчмаркове."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 е проектирана да обработва задачи, комбиниращи визуални и текстови данни. Тя демонстрира отлични резултати в задачи като описание на изображения и визуални въпроси и отговори, преодолявайки пропастта между генерирането на езици и визуалното разсъждение."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite е подходящ за среди, изискващи висока производителност и ниска латентност."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo предлага изключителни способности за разбиране и генериране на език, подходящи за най-строги изчислителни задачи."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite е подходящ за среди с ограничени ресурси, предлагащи отличен баланс на производителност."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo е високоефективен голям езиков модел, поддържащ широк спектър от приложения."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B е мощен модел за предварително обучение и настройка на инструкции."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405B Llama 3.1 Turbo моделът предлага огромна контекстова поддръжка за обработка на големи данни, с изключителна производителност в приложения с изкуствен интелект с много голям мащаб."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 е водещ модел, представен от Meta, поддържащ до 405B параметри, подходящ за сложни разговори, многоезичен превод и анализ на данни."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70B моделът е прецизно настроен за приложения с високо натоварване, квантован до FP8, осигурявайки по-ефективна изчислителна мощ и точност, гарантиращи изключителна производителност в сложни сценарии."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8B моделът използва FP8 квантоване, поддържа до 131,072 контекстови маркера и е сред най-добрите отворени модели, подходящи за сложни задачи, с производителност, превъзхождаща много индустриални стандарти."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct е оптимизирана за висококачествени диалогови сценарии и показва отлични резултати в различни човешки оценки."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct е оптимизирана за висококачествени диалогови сценарии, с представяне, надминаващо много затворени модели."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct е проектиран за висококачествени диалози и показва отлични резултати в човешките оценки, особено подходящ за сценарии с висока интерактивност."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct е най-новата версия, пусната от Meta, оптимизирана за висококачествени диалогови сценарии, с представяне, надминаващо много водещи затворени модели."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 предлага поддръжка на множество езици и е един от водещите генеративни модели в индустрията."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 е проектиран да обработва задачи, свързващи визуални и текстови данни. Той показва отлични резултати в задачи като описание на изображения и визуални въпроси, преодолявайки пропастта между генерирането на език и визуалното разсъждение."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 е проектиран да обработва задачи, свързващи визуални и текстови данни. Той показва отлични резултати в задачи като описание на изображения и визуални въпроси, преодолявайки пропастта между генерирането на език и визуалното разсъждение."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 е най-напредналият многоезичен отворен езиков модел от серията Llama, който предлага производителност, сравнима с 405B моделите, на изключително ниска цена. Базиран на структурата Transformer и подобрен чрез супервизирано фино настройване (SFT) и обучение с човешка обратна връзка (RLHF) за повишаване на полезността и безопасността. Неговата версия, оптимизирана за инструкции, е специално проектирана за многоезични диалози и показва по-добри резултати от много от отворените и затворените чат модели в множество индустриални бенчмаркове. Краен срок за знания: декември 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 е най-напредналият многоезичен отворен езиков модел от серията Llama, който предлага производителност, сравнима с 405B моделите, на изключително ниска цена. Базиран на структурата Transformer и подобрен чрез супервизирано фино настройване (SFT) и обучение с човешка обратна връзка (RLHF) за повишаване на полезността и безопасността. Неговата версия, оптимизирана за инструкции, е специално проектирана за многоезични диалози и показва по-добри резултати от много от отворените и затворените чат модели в множество индустриални бенчмаркове. Краен срок за знания: декември 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct е най-голямата и най-мощната версия на модела Llama 3.1 Instruct. Това е високо напреднал модел за диалогово разсъждение и генериране на синтетични данни, който може да се използва и като основа за професионално продължително предварително обучение или фино настройване в специфични области. Многоезичният голям езиков модел (LLMs), предоставен от Llama 3.1, е набор от предварително обучени, коригирани по инструкции генеративни модели, включително размери 8B, 70B и 405B (текстов вход/изход). Текстовите модели, коригирани по инструкции (8B, 70B, 405B), са оптимизирани за многоезични диалогови случаи и надминават много налични отворени чат модели в общи индустриални бенчмаркове. Llama 3.1 е проектиран за търговски и изследователски цели на множество езици. Моделите, коригирани по инструкции, са подходящи за чатове, подобни на асистенти, докато предварително обучените модели могат да се адаптират към различни задачи за генериране на естествен език. Моделите на Llama 3.1 също поддържат използването на изхода на модела за подобряване на други модели, включително генериране на синтетични данни и рафиниране. Llama 3.1 е саморегресивен езиков модел, използващ оптимизирана трансформаторна архитектура. Коригираните версии използват супервизирано фино настройване (SFT) и обучение с човешка обратна връзка (RLHF), за да отговорят на предпочитанията на хората за полезност и безопасност."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Актуализирана версия на Meta Llama 3.1 70B Instruct, включваща разширен контекстуален прозорец от 128K, многоезичност и подобрени способности за разсъждение. Многоезичният голям езиков модел (LLMs) на Llama 3.1 е набор от предварително обучени, коригирани за инструкции генериращи модели, включващи размери 8B, 70B и 405B (текстово въвеждане/изход). Текстовите модели, коригирани за инструкции (8B, 70B, 405B), са оптимизирани за многоезични диалогови случаи и надминават много налични отворени чат модели в общи индустриални бенчмаркове. Llama 3.1 е проектиран за търговски и изследователски цели на множество езици. Текстовите модели, коригирани за инструкции, са подходящи за чат, подобен на асистент, докато предварително обучените модели могат да се адаптират за различни задачи по генериране на естествен език. Моделите на Llama 3.1 също поддържат използването на изхода на модела за подобряване на други модели, включително генериране на синтетични данни и рафиниране. Llama 3.1 е саморегресивен езиков модел, използващ оптимизирана архитектура на трансформатор. Коригираните версии използват наблюдавано фино настройване (SFT) и обучение с подсилване с човешка обратна връзка (RLHF), за да отговорят на предпочитанията на хората за полезност и безопасност."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Актуализирана версия на Meta Llama 3.1 8B Instruct, включваща разширен контекстуален прозорец от 128K, многоезичност и подобрени способности за разсъждение. Многоезичният голям езиков модел (LLMs) на Llama 3.1 е набор от предварително обучени, коригирани за инструкции генериращи модели, включващи размери 8B, 70B и 405B (текстово въвеждане/изход). Текстовите модели, коригирани за инструкции (8B, 70B, 405B), са оптимизирани за многоезични диалогови случаи и надминават много налични отворени чат модели в общи индустриални бенчмаркове. Llama 3.1 е проектиран за търговски и изследователски цели на множество езици. Текстовите модели, коригирани за инструкции, са подходящи за чат, подобен на асистент, докато предварително обучените модели могат да се адаптират за различни задачи по генериране на естествен език. Моделите на Llama 3.1 също поддържат използването на изхода на модела за подобряване на други модели, включително генериране на синтетични данни и рафиниране. Llama 3.1 е саморегресивен езиков модел, използващ оптимизирана архитектура на трансформатор. Коригираните версии използват наблюдавано фино настройване (SFT) и обучение с подсилване с човешка обратна връзка (RLHF), за да отговорят на предпочитанията на хората за полезност и безопасност."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 е отворен голям езиков модел (LLM), насочен към разработчици, изследователи и предприятия, предназначен да им помогне да изградят, експериментират и отговорно разширят своите идеи за генеративен ИИ. Като част от основната система на глобалната общност за иновации, той е особено подходящ за създаване на съдържание, диалогов ИИ, разбиране на езика, научноизследователска и развойна дейност и бизнес приложения."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 е отворен голям езиков модел (LLM), насочен към разработчици, изследователи и предприятия, предназначен да им помогне да изградят, експериментират и отговорно разширят своите идеи за генеративен ИИ. Като част от основната система на глобалната общност за иновации, той е особено подходящ за устройства с ограничени изчислителни ресурси и по-бързо време за обучение."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Отлични способности за разсъждение върху изображения с висока резолюция, подходящи за приложения за визуално разбиране."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Разширени способности за разсъждение върху изображения, подходящи за визуални агенти за разбиране."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 е най-напредналият многоезичен отворен езиков модел от серията Llama, предлагащ производителност, сравнима с 405B модел, при изключително ниски разходи. Базиран на архитектура Transformer и подобрен чрез надзорно фино настройване (SFT) и обучение с човешка обратна връзка (RLHF) за повишаване на полезността и безопасността. Версията с инструкции е оптимизирана за многоезични диалози и превъзхожда множество отворени и затворени чат модели в различни индустриални бенчмаркове. Дата на знание: декември 2023 г."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Мо<PERSON>ен модел с 70 милиарда параметри, отличаващ се в разсъждение, кодиране и широк спектър от езикови приложения."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Многофункционален модел с 8 милиарда параметри, оптимизиран за задачи с диалог и генериране на текст."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Текстов модел Llama 3.1 с фино настройване за инструкции, оптимизиран за многоезични диалогови случаи, с отлични резултати в множество налични отворени и затворени чат модели при стандартни индустриални бенчмаркове."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Текстов модел Llama 3.1 с фино настройване за инструкции, оптимизиран за многоезични диалогови случаи, с отлични резултати в множество налични отворени и затворени чат модели при стандартни индустриални бенчмаркове."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Текстов модел Llama 3.1 с фино настройване за инструкции, оптимизиран за многоезични диалогови случаи, с отлични резултати в множество налични отворени и затворени чат модели при стандартни индустриални бенчмаркове."}, "meta/llama-3.1-405b-instruct": {"description": "Напред<PERSON><PERSON> LLM, поддържащ генериране на синтетични данни, дестилация на знания и разсъждение, подходящ за чатботове, програмиране и специфични задачи."}, "meta/llama-3.1-70b-instruct": {"description": "Улеснява сложни разговори, с изключителни способности за разбиране на контекста, разсъждение и генериране на текст."}, "meta/llama-3.1-8b-instruct": {"description": "Напреднал, водещ модел с разбиране на езика, изключителни способности за разсъждение и генериране на текст."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Водещ визуално-езиков модел, специализиран в извършване на висококачествени разсъждения от изображения."}, "meta/llama-3.2-1b-instruct": {"description": "Напреднал, водещ малък езиков модел с разбиране на езика, изключителни способности за разсъждение и генериране на текст."}, "meta/llama-3.2-3b-instruct": {"description": "Напреднал, водещ малък езиков модел с разбиране на езика, изключителни способности за разсъждение и генериране на текст."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Водещ визуално-езиков модел, специализиран в извършване на висококачествени разсъждения от изображения."}, "meta/llama-3.3-70b-instruct": {"description": "Напред<PERSON>л LLM, специализиран в разсъждения, математика, общи познания и извикване на функции."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "Същият модел Phi-3-medium, но с по-голям размер на контекста, подходящ за RAG или малко количество подсказки."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Модел с 14 милиарда параметри, с по-добро качество от Phi-3-mini, фокусиран върху висококачествени, разсъждаващи данни."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Същият модел Phi-3-mini, но с по-голям размер на контекста, подходящ за RAG или малко количество подсказки."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Най-малки<PERSON>т член на семейството Phi-3, оптимизиран за качество и ниска латентност."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Същият модел Phi-3-small, но с по-голям размер на контекста, подходящ за RAG или малко количество подсказки."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Модел с 7 милиарда параметри, с по-добро качество от Phi-3-mini, фокусиран върху висококачествени, разсъждаващи данни."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Актуализи<PERSON><PERSON>на версия на модела Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Актуа<PERSON><PERSON>з<PERSON><PERSON><PERSON>на версия на модела Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 е езиков модел, предоставен от Microsoft AI, който показва особени способности в сложни разговори, многоезичност, разсъждения и интелигентни асистенти."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B е най-напредналият Wizard модел на Microsoft AI, показващ изключителна конкурентоспособност."}, "minicpm-v": {"description": "MiniCPM-V е новото поколение мултимодален голям модел, представен от OpenBMB, който притежава изключителни способности за OCR разпознаване и мултимодално разбиране, поддържащ широк спектър от приложения."}, "ministral-3b-latest": {"description": "Ministral 3B е световен лидер сред моделите на Mistral."}, "ministral-8b-latest": {"description": "Ministral 8B е модел на Mistral с отлична цена-качество."}, "mistral": {"description": "Mistral е 7B модел, представен от Mistral AI, подходящ за променливи нужди в обработката на език."}, "mistral-ai/Mistral-Large-2411": {"description": "Флагманският модел на Mistral, подходящ за сложни задачи, изискващи мащабни способности за разсъждение или висока специализация (синтетично генериране на текст, кодиране, RAG или агенти)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo е водещ езиков модел (LLM), който предлага най-съвременни способности за разсъждение, световни знания и кодиране в своя размерен клас."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small е подходящ за всякакви езикови задачи, изискващи висока ефективност и ниска латентност."}, "mistral-large": {"description": "Mixtral Large е флагманският модел на Mistral, комбиниращ способности за генериране на код, математика и разсъждение, поддържащ контекстен прозорец от 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 е усъвършенстван плътен голям езиков модел (LLM) с 123 милиарда параметъра, който притежава водещи във времето си способности за разсъждение, познания и кодиране."}, "mistral-large-latest": {"description": "Mistral Large е флагманският модел, специализиран в многоезични задачи, сложни разсъждения и генериране на код, идеален за висококачествени приложения."}, "mistral-medium-latest": {"description": "Mistral Medium 3 предлага най-напреднала производителност на цена 8 пъти по-ниска и основно опростява внедряването в предприятия."}, "mistral-nemo": {"description": "Mistral Nemo е 12B модел, разработен в сътрудничество между Mistral AI и NVIDIA, предлагащ ефективна производителност."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 е голям езиков модел (LLM), който представлява фино настроена за инструкции версия на Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small може да се използва за всяка езикова задача, която изисква висока ефективност и ниска латентност."}, "mistral-small-latest": {"description": "Mistral Small е икономически ефективен, бърз и надежден вариант, подходящ за случаи на употреба като превод, резюме и анализ на настроението."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct е известен с високата си производителност, подходящ за множество езикови задачи."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B е модел с фино настройване по заявка, предлагащ оптимизирани отговори за задачи."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 предлага ефективна изчислителна мощ и разбиране на естествения език, подходяща за широк спектър от приложения."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B е компактен, но високопроизводителен модел, специализиран в обработка на партиди и основни задачи, като класификация и генериране на текст с добри способности за разсъждение."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) е супер голям езиков модел, поддържащ изключително високи изисквания за обработка."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B е предварително обучен модел на разредени смесени експерти, предназначен за универсални текстови задачи."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B е модел с рядък експерт, който използва множество параметри, за да подобри скоростта на разсъждение, подходящ за обработка на многоезични и генериращи код задачи."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct е високопроизводителен индустриален стандартен модел, оптимизиран за бързина и поддръжка на дълги контексти."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo е модел с 7.3B параметри, предлагащ многоезична поддръжка и висока производителност."}, "mixtral": {"description": "Mixtral е експертен модел на Mistral AI, с отворени тегла, предоставящ поддръжка в генерирането на код и разбиране на езика."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B предлага висока толерантност на грешки при паралелно изчисление, подходяща за сложни задачи."}, "mixtral:8x22b": {"description": "Mixtral е експертен модел на Mistral AI, с отворени тегла, предоставящ поддръжка в генерирането на код и разбиране на езика."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K е модел с изключителна способност за обработка на дълги контексти, подходящ за генериране на много дълги текстове, отговарящи на сложни изисквания за генериране, способен да обработва до 128,000 токена, особено подходящ за научни изследвания, академични и генериране на големи документи."}, "moonshot-v1-128k-vision-preview": {"description": "Визуалният моде<PERSON> (включител<PERSON><PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview и др.) може да разбира съдържанието на изображения, включително текст в изображения, цветове и форми на обекти."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K предлага средна дължина на контекста, способен да обработва 32,768 токена, особено подходящ за генериране на различни дълги документи и сложни диалози, използван в области като създаване на съдържание, генериране на отчети и диалогови системи."}, "moonshot-v1-32k-vision-preview": {"description": "Визуалният моде<PERSON> (включител<PERSON><PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview и др.) може да разбира съдържанието на изображения, включително текст в изображения, цветове и форми на обекти."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K е проектиран за генериране на кратки текстови задачи, с ефективна производителност, способен да обработва 8,192 токена, особено подходящ за кратки диалози, бележки и бързо генериране на съдържание."}, "moonshot-v1-8k-vision-preview": {"description": "Визуалният моде<PERSON> (включител<PERSON><PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview и др.) може да разбира съдържанието на изображения, включително текст в изображения, цветове и форми на обекти."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto може да избере подходящ модел в зависимост от количеството токени, заето от текущия контекст."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B е голям отворен модел за код, оптимизиран чрез мащабно подсилено обучение, способен да генерира стабилни и директно приложими пачове. Този модел постига нов рекорд от 60,4 % на SWE-bench Verified, подобрявайки резултатите на отворени модели в автоматизирани задачи за софтуерно инженерство като поправка на дефекти и преглед на код."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 е базов модел с MoE архитектура, с изключителни кодови и агентски способности, общо 1 трилион параметри и 32 милиарда активирани параметри. В бенчмаркове за общо знание, програмиране, математика и агентски задачи моделът K2 превъзхожда други водещи отворени модели."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 е базов модел с MoE архитектура с изключителни способности за кодиране и агент, с общо 1 трилион параметри и 32 милиарда активни параметри. В бенчмаркови тестове за общи знания, програмиране, математика и агенти, моделът K2 превъзхожда други водещи отворени модели."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B е обновена версия на Nous Hermes 2, включваща най-новите вътрешно разработени набори от данни."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B е голям езиков модел, персонализир<PERSON>н от NVIDIA, предназначен да увеличи полезността на отговорите, генерирани от LLM на потребителските запитвания. Моделът показва отлични резултати в бенчмаркове като Arena Hard, AlpacaEval 2 LC и GPT-4-Turbo MT-Bench, като заема първо място в трите автоматизирани теста за подравняване към 1 октомври 2024 г. Моделът е обучен с RLHF (по-специално REINFORCE), Llama-3.1-Nemotron-70B-Reward и HelpSteer2-Preference подсказки на базата на Llama-3.1-70B-Instruct модела."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Уникален езиков модел, предлагащ ненадмината точност и ефективност."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct е персонализиран голям езиков модел на NVIDIA, предназначен да подобри полезността на отговорите, генерирани от LLM."}, "o1": {"description": "Фокусиран върху напреднали изводи и решаване на сложни проблеми, включително математически и научни задачи. Изключително подходящ за приложения, изискващи дълбочинно разбиране на контекста и управление на работни потоци."}, "o1-mini": {"description": "o1-mini е бърз и икономичен модел за изводи, проектиран за приложения в програмирането, математиката и науката. Моделът разполага с контекст от 128K и дата на знание до октомври 2023."}, "o1-preview": {"description": "o1 е новият модел за изводи на OpenAI, подходящ за сложни задачи, изискващи обширни общи знания. Моделът разполага с контекст от 128K и дата на знание до октомври 2023."}, "o1-pro": {"description": "Моделите от серията o1 са обучени чрез подсилващо обучение, което им позволява да мислят преди да отговорят и да изпълняват сложни задачи за разсъждение. Моделът o1-pro използва повече изчислителни ресурси за по-задълбочено мислене, осигурявайки постоянно по-високо качество на отговорите."}, "o3": {"description": "o3 е универсален и мощен модел, който показва отлични резултати в множество области. Той задава нови стандарти за задачи по математика, наука, програмиране и визуални разсъждения. Също така е добър в техническото писане и следването на инструкции. Потребителите могат да го използват за анализ на текст, код и изображения, за решаване на сложни проблеми с множество стъпки."}, "o3-deep-research": {"description": "o3-deep-research е нашият най-напреднал модел за дълбоко изследване, специално проектиран за обработка на сложни многократни изследователски задачи. Той може да търси и обобщава информация от интернет, както и да осъществява достъп и използва вашите собствени данни чрез MCP конектор."}, "o3-mini": {"description": "o3-mini е нашият най-нов малък модел за инференция, който предлага висока интелигентност при същите разходи и цели за закъснение като o1-mini."}, "o3-pro": {"description": "Моделът o3-pro използва повече изчислителна мощ за по-задълбочено мислене и винаги предоставя по-добри отговори, като се поддържа само чрез Responses API."}, "o4-mini": {"description": "o4-mini е нашият най-нов малък модел от серията o. Той е оптимизиран за бързо и ефективно извеждане, показвайки изключителна ефективност и производителност в задачи по кодиране и визуализация."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research е нашият по-бърз и по-достъпен модел за дълбоко изследване — идеален за обработка на сложни многократни изследователски задачи. Той може да търси и обобщава информация от интернет, както и да осъществява достъп и използва вашите собствени данни чрез MCP конектор."}, "open-codestral-mamba": {"description": "Codestral Mamba е модел на езика Mamba 2, специализиран в генерирането на код, предоставящ мощна поддръжка за напреднали кодови и разсъждателни задачи."}, "open-mistral-7b": {"description": "Mistral 7B е компактен, но високопроизводителен модел, специализиран в обработка на партиди и прости задачи, като класификация и генериране на текст, с добра способност за разсъждение."}, "open-mistral-nemo": {"description": "Mistral Nemo е 12B модел, разработен в сътрудничество с Nvidia, предлагащ отлични способности за разсъждение и кодиране, лесен за интеграция и замяна."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B е по-голям експертен модел, фокусиран върху сложни задачи, предлагащ отлични способности за разсъждение и по-висока производителност."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B е рядък експертен модел, който използва множество параметри за увеличаване на скоростта на разсъждение, подходящ за обработка на многоезични и кодови генериращи задачи."}, "openai/gpt-4.1": {"description": "GPT-4.1 е нашият флагмански модел за сложни задачи. Той е изключително подходящ за решаване на проблеми в различни области."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini предлага баланс между интелигентност, скорост и разходи, което го прави привлекателен модел за много случаи на употреба."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano е най-бързият и най-икономичният модел на GPT-4.1."}, "openai/gpt-4o": {"description": "ChatGPT-4o е динамичен модел, който се актуализира в реално време, за да поддържа най-новата версия. Той комбинира мощно разбиране на езика и способности за генериране, подходящ за мащабни приложения, включително обслужване на клиенти, образование и техническа поддръжка."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini е най-новият модел на OpenAI, пуснат след GPT-4 Omni, който поддържа вход и изход на текст и изображения. Като най-напредналият им малък модел, той е значително по-евтин от другите нови модели и е с над 60% по-евтин от GPT-3.5 Turbo. Запазва най-съвременната интелигентност, като предлага значителна стойност за парите. GPT-4o mini получи 82% на теста MMLU и в момента е с по-висок рейтинг от GPT-4 в предпочитанията за чат."}, "openai/o1": {"description": "o1 е новият модел за разсъждение на OpenAI, който поддържа вход с изображения и текст и генерира текст, подходящ за сложни задачи, изискващи широкообхватни общи знания. Моделът разполага с контекст от 200K и дата на знание до октомври 2023 г."}, "openai/o1-mini": {"description": "o1-mini е бърз и икономичен модел за изводи, проектиран за приложения в програмирането, математиката и науката. Моделът разполага с контекст от 128K и дата на знание до октомври 2023."}, "openai/o1-preview": {"description": "o1 е новият модел за изводи на OpenAI, подходящ за сложни задачи, изискващи обширни общи знания. Моделът разполага с контекст от 128K и дата на знание до октомври 2023."}, "openai/o3": {"description": "o3 е мощен универсален модел, който показва отлични резултати в множество области. Той поставя нови стандарти за математически, научни, програмистки и визуални задачи за разсъждение. Също така е добър в техническото писане и следването на инструкции. Потребителите могат да го използват за анализ на текст, код и изображения, за решаване на сложни проблеми с множество стъпки."}, "openai/o3-mini": {"description": "o3-mini предлага висока интелигентност при същите разходи и цели за закъснение като o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini high е версия с високо ниво на разсъждение, която предлага висока интелигентност при същите разходи и цели за закъснение като o1-mini."}, "openai/o4-mini": {"description": "o4-mini е оптимизиран за бързо и ефективно извеждане, показвайки изключителна ефективност и производителност в задачи по кодиране и визуализация."}, "openai/o4-mini-high": {"description": "o4-mini версия с високо ниво на извеждане, оптимизирана за бързо и ефективно извеждане, показваща изключителна ефективност и производителност в задачи по кодиране и визуализация."}, "openrouter/auto": {"description": "В зависимост от дължината на контекста, темата и сложността, вашето запитване ще бъде изпратено до Llama 3 70B Instruct, Claude 3.5 Sonnet (саморегулиращ) или GPT-4o."}, "phi3": {"description": "Phi-3 е лек отворен модел, представен от Microsoft, подходящ за ефективна интеграция и мащабно знание разсъждение."}, "phi3:14b": {"description": "Phi-3 е лек отворен модел, представен от Microsoft, подходящ за ефективна интеграция и мащабно знание разсъждение."}, "pixtral-12b-2409": {"description": "Моделът Pixtral демонстрира силни способности в задачи като разбиране на графики и изображения, отговори на документи, многомодално разсъждение и следване на инструкции, способен да приема изображения с естествено разрешение и съотношение на страните, както и да обработва произволен брой изображения в контекстен прозорец с дължина до 128K токена."}, "pixtral-large-latest": {"description": "Pixtral Large е отворен многомодален модел с 1240 милиарда параметри, базиран на Mistral Large 2. Това е вторият модел в нашето многомодално семейство, който демонстрира авангардни способности за разбиране на изображения."}, "pro-128k": {"description": "Spark Pro 128K е конфигуриран с изключителна способност за обработка на контекст, способен да обработва до 128K контекстна информация, особено подходящ за дълги текстове, изискващи цялостен анализ и дългосрочна логическа свързаност, предоставяйки гладка и последователна логика и разнообразна поддръжка на цитати в сложни текстови комуникации."}, "qvq-72b-preview": {"description": "QVQ моделът е експериментален изследователски модел, разработен от екипа на Qwen, фокусиран върху повишаване на визуалните способности за разсъждение, особено в областта на математическото разсъждение."}, "qvq-max": {"description": "Tongyi Qianwen QVQ визуален разсъждаващ модел, поддържащ визуален вход и изход на мисловни вериги, показващ по-силни способности в математика, програмиране, визуален анализ, творчество и общи задачи."}, "qvq-plus": {"description": "Модел за визуално разсъждение. Поддържа визуален вход и изход на мисловни вериги. Версия plus, пусната след модела qvq-max, предлага по-бързо разсъждение и по-добър баланс между ефективност и разходи в сравнение с qvq-max."}, "qwen-coder-plus": {"description": "Tongyi Qianwen модел за кодиране."}, "qwen-coder-turbo": {"description": "Tongyi Qianwen модел за кодиране."}, "qwen-coder-turbo-latest": {"description": "Моделът на кода Qwen."}, "qwen-long": {"description": "Qwen е мащабен езиков модел, който поддържа дълги текстови контексти и диалогови функции, базирани на дълги документи и множество документи."}, "qwen-math-plus": {"description": "Tongyi Qianwen математически модел, специално предназначен за решаване на математически задачи."}, "qwen-math-plus-latest": {"description": "Математическият модел Qwen е специално проектиран за решаване на математически задачи."}, "qwen-math-turbo": {"description": "Tongyi Qianwen математически модел, специално предназначен за решаване на математически задачи."}, "qwen-math-turbo-latest": {"description": "Математическият модел Qwen е специално проектиран за решаване на математически задачи."}, "qwen-max": {"description": "通义千问（<PERSON>wen） е моделиран на база багатограмния езиков модел с хипотетично ниво на милярд, поддържащ различни езици, включително китайски и английски, и в момента служи като API на продукта версия 2.5 на 通义千问."}, "qwen-omni-turbo": {"description": "Серията модели Qwen-Omni поддържа входни данни от различни модалности, включително видео, аудио, изображения и текст, и изходи аудио и текст."}, "qwen-plus": {"description": "通义千问（<PERSON><PERSON>） е подобрена версия на мащабен езиков модел, който поддържа вход на различни езици, включително китайски и английски."}, "qwen-turbo": {"description": "通义千问（<PERSON><PERSON>） е мащабен езиков модел, който поддържа вход на различни езици, включително китайски и английски."}, "qwen-vl-chat-v1": {"description": "Qwen VL поддържа гъвкави интерактивни методи, включително множество изображения, многократни въпроси и отговори, творчество и др."}, "qwen-vl-max": {"description": "Супер голям визуално-езиков модел Tongyi Qianwen. В сравнение с подсилената версия, допълнително подобрява визуалното разсъждение и следване на инструкции, предоставяйки по-високо ниво на визуално възприятие и познание."}, "qwen-vl-max-latest": {"description": "Qwen-VL Max е модел за визуален език с изключително голям мащаб. В сравнение с подобрената версия, той отново подобрява способността за визуално разсъждение и следване на инструкции, предоставяйки по-високо ниво на визуално възприятие и познание."}, "qwen-vl-ocr": {"description": "Tongyi Qianwen OCR е специализиран модел за извличане на текст, фокусиран върху документи, таблици, тестови задачи, ръкописен текст и други видове изображения. Моделът може да разпознава множество езици, включително китайски, английски, френски, японски, корейски, немски, руски, италиански, виетнамски и арабски."}, "qwen-vl-plus": {"description": "Подсилена версия на големия визуално-езиков модел Tongyi Qianwen. Значително подобрена способност за разпознаване на детайли и текст, поддържа изображения с резолюция над милион пиксела и произволни пропорции."}, "qwen-vl-plus-latest": {"description": "Моделят за визуален език Qwen-VL Plus е подобрена версия с голям мащаб. Значително подобрява способността за разпознаване на детайли и текст, поддържа резолюция над милион пиксела и изображения с произволно съотношение на страните."}, "qwen-vl-v1": {"description": "Инициализиран с езиковия модел Qwen-7B, добавя модел за изображения, предтренировъчен модел с резолюция на входа от 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 е новата серия големи езикови модели Qwen. Qwen2 7B е модел, базиран на трансформатор, който показва отлични резултати в разбирането на езика, многоезичните способности, програмирането, математиката и разсъжденията."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 е нова серия от големи езикови модели с по-силни способности за разбиране и генериране."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL е най-новата итерация на модела Qwen-VL, постигайки най-съвременни резултати в бенчмарковете за визуално разбиране, включително MathVista, DocVQA, RealWorldQA и MTVQA. Qwen2-VL може да разбира видеа с продължителност над 20 минути, за висококачествени въпроси и отговори, диалози и създаване на съдържание, базирани на видео. Той също така притежава сложни способности за разсъждение и вземане на решения, които могат да се интегрират с мобилни устройства, роботи и др., за автоматични операции на базата на визуална среда и текстови инструкции. Освен английски и китайски, Qwen2-VL сега поддържа и разбиране на текст на различни езици в изображения, включително повечето европейски езици, японски, корейски, арабски и виетнамски."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct е една от най-новите серии големи езикови модели, публикувани от Alibaba Cloud. Този 72B модел има значителни подобрения в области като кодиране и математика. Моделът предлага и многоезична поддръжка, обхващаща над 29 езика, включително китайски и английски. Моделът показва значителни подобрения в следването на инструкции, разбирането на структурирани данни и генерирането на структурирани изходи (особено JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct е една от най-новите серии големи езикови модели, публикувани от Alibaba Cloud. Този 32B модел има значителни подобрения в области като кодиране и математика. Моделът предлага и многоезична поддръжка, обхващаща над 29 езика, включително китайски и английски. Моделът показва значителни подобрения в следването на инструкции, разбирането на структурирани данни и генерирането на структурирани изходи (особено JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM, насочен към китайски и английски, за области като език, програмиране, математика и разсъждение."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "Напред<PERSON><PERSON> LLM, поддържащ генериране на код, разсъждение и корекции, обхващащ основните програмни езици."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Мощен среден модел за код, поддържащ 32K дължина на контекста, специализиран в многоезично програмиране."}, "qwen/qwen3-14b": {"description": "Qwen3-14B е плътен езиков модел с 14.8 милиарда параметри от серията Qwen3, проектиран за сложни разсъждения и ефективен диалог. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за математика, програмиране и логическо разсъждение и режим на \"неразсъждение\" за общи разговори. Моделът е фино настроен за следване на инструкции, използване на инструменти за агенти, креативно писане и многоезични задачи на над 100 езика и диалекта. Той нативно обработва контекст от 32K токена и може да бъде разширен до 131K токена с помощта на разширение, базирано на YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B е плътен езиков модел с 14.8 милиарда параметри от серията Qwen3, проектиран за сложни разсъждения и ефективен диалог. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за математика, програмиране и логическо разсъждение и режим на \"неразсъждение\" за общи разговори. Моделът е фино настроен за следване на инструкции, използване на инструменти за агенти, креативно писане и многоезични задачи на над 100 езика и диалекта. Той нативно обработва контекст от 32K токена и може да бъде разширен до 131K токена с помощта на разширение, базирано на YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B е модел с 235B параметри, разработен от Qwen, с експертна смесена (MoE) архитектура, активираща 22B параметри при всяко напредване. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за сложни разсъждения, математика и кодиране и режим на \"неразсъждение\" за ефективен общ диалог. Моделът демонстрира силни способности за разсъждение, многоезична поддръжка (над 100 езика и диалекта), напреднало следване на инструкции и способности за извикване на инструменти за агенти. Той нативно обработва контекстен прозорец от 32K токена и може да бъде разширен до 131K токена с помощта на разширение, базирано на YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B е модел с 235B параметри, разработен от Qwen, с експертна смесена (MoE) архитектура, активираща 22B параметри при всяко напредване. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за сложни разсъждения, математика и кодиране и режим на \"неразсъждение\" за ефективен общ диалог. Моделът демонстрира силни способности за разсъждение, многоезична поддръжка (над 100 езика и диалекта), напреднало следване на инструкции и способности за извикване на инструменти за агенти. Той нативно обработва контекстен прозорец от 32K токена и може да бъде разширен до 131K токена с помощта на разширение, базирано на YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 е най-новото поколение от серията големи езикови модели Qwen, с плътна и експертна смесена (MoE) архитектура, която показва отлични резултати в области като разсъждение, многоезична поддръжка и сложни задачи за агенти. Уникалната му способност да преминава безпроблемно между режим на разсъждение за сложни логически задачи и режим на неразсъждение за ефективен диалог осигурява многофункционална и висококачествена производителност.\n\nQwen3 значително надминава предишни модели като QwQ и Qwen2.5, предоставяйки изключителни способности в математиката, програмирането, общото разсъждение, креативното писане и интерактивния диалог. Вариантът Qwen3-30B-A3B съдържа 30.5 милиарда параметри (3.3 милиарда активни параметри), 48 слоя, 128 експерти (активирани по 8 за всяка задача) и поддържа контекст до 131K токена (с използване на YaRN), задавайки нов стандарт за отворени модели."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 е най-новото поколение от серията големи езикови модели Qwen, с плътна и експертна смесена (MoE) архитектура, която показва отлични резултати в области като разсъждение, многоезична поддръжка и сложни задачи за агенти. Уникалната му способност да преминава безпроблемно между режим на разсъждение за сложни логически задачи и режим на неразсъждение за ефективен диалог осигурява многофункционална и висококачествена производителност.\n\nQwen3 значително надминава предишни модели като QwQ и Qwen2.5, предоставяйки изключителни способности в математиката, програмирането, общото разсъждение, креативното писане и интерактивния диалог. Вариантът Qwen3-30B-A3B съдържа 30.5 милиарда параметри (3.3 милиарда активни параметри), 48 слоя, 128 експерти (активирани по 8 за всяка задача) и поддържа контекст до 131K токена (с използване на YaRN), задавайки нов стандарт за отворени модели."}, "qwen/qwen3-32b": {"description": "Qwen3-32B е плътен езиков модел с 32.8 милиарда параметри от серията Qwen3, оптимизиран за сложни разсъждения и ефективен диалог. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за математика, програмиране и логическо разсъждение и режим на \"неразсъждение\" за по-бързи и общи разговори. Моделът показва силна производителност в следването на инструкции, използването на инструменти за агенти, креативно писане и многоезични задачи на над 100 езика и диалекта. Той нативно обработва контекст от 32K токена и може да бъде разширен до 131K токена с помощта на разширение, базирано на YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B е плътен езиков модел с 32.8 милиарда параметри от серията Qwen3, оптимизиран за сложни разсъждения и ефективен диалог. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за математика, програмиране и логическо разсъждение и режим на \"неразсъждение\" за по-бързи и общи разговори. Моделът показва силна производителност в следването на инструкции, използването на инструменти за агенти, креативно писане и многоезични задачи на над 100 езика и диалекта. Той нативно обработва контекст от 32K токена и може да бъде разширен до 131K токена с помощта на разширение, базирано на YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B е плътен езиков модел с 8.2 милиарда параметри от серията Qwen3, проектиран за задачи с интензивно разсъждение и ефективен диалог. Той поддържа безпроблемно преминаване между режим на \"разсъждение\" за математика, програмиране и логическо разсъждение и режим на \"неразсъждение\" за общи разговори. Моделът е фино настроен за следване на инструкции, интеграция на агенти, креативно писане и многоезично използване на над 100 езика и диалекта. Той нативно поддържа контекстен прозорец от 32K токена и може да бъде разширен до 131K токена чрез YaRN."}, "qwen2": {"description": "Qwen2 е новото поколение голям езиков модел на Alibaba, предлагащ отлична производителност за разнообразни приложения."}, "qwen2-72b-instruct": {"description": "Qwen2 е новият серий на големи модели за език, предложен от екипа Qwen. Той се основава на архитектурата Transformer и използва SwiGLU активационна функция, внимание QKV смещение (attention QKV bias), групово запитване на внимание (group query attention), смесени техники за внимание с превъртващи се прозорци (mixture of sliding window attention) и пълно внимание. Освен това, екипът Qwen също е подобрал токенизатора, който поддържа множество езици и код."}, "qwen2-7b-instruct": {"description": "Qwen2 е новият серийен модел за големи езици, представен от екипа Qwen. Той се основава на архитектурата Transformer и използва SwiGLU активационна функция, внимание с QKV смещение (attention QKV bias), групово внимание за заявки (group query attention), смесени техники за обръщане на внимание с превъртващи се прозорци (mixture of sliding window attention) и пълно внимание. Освен това, екипът Qwen е подобрил токенизатора, който поддържа множество езици и код."}, "qwen2.5": {"description": "Qwen2.5 е новото поколение мащабен езиков модел на Alibaba, който предлага отлична производителност, за да отговори на разнообразни приложни нужди."}, "qwen2.5-14b-instruct": {"description": "Модел с мащаб 14B, отворен за обществеността от Qwen 2.5."}, "qwen2.5-14b-instruct-1m": {"description": "Qwen2.5 е отворен модел с мащаб 72B."}, "qwen2.5-32b-instruct": {"description": "Модел с мащаб 32B, отворен за обществеността от Qwen 2.5."}, "qwen2.5-72b-instruct": {"description": "Модел с мащаб 72B, отворен за обществеността от Qwen 2.5."}, "qwen2.5-7b-instruct": {"description": "Модел с мащаб 7B, отворен за обществеността от Qwen 2.5."}, "qwen2.5-coder-1.5b-instruct": {"description": "通义千问（<PERSON><PERSON>） е отворен код модел за програмиране."}, "qwen2.5-coder-14b-instruct": {"description": "Отворена версия на Tongyi Qianwen модел за кодиране."}, "qwen2.5-coder-32b-instruct": {"description": "Отворена версия на модела за кодиране Qwen с общо предназначение."}, "qwen2.5-coder-7b-instruct": {"description": "Отворената версия на модела на кода Qwen."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder е най-новият специализиран голям езиков модел за код от серията Qwen (предишно име CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 е най-новата серия от големи езикови модели на Qwen. За Qwen2.5 публикувахме няколко основни езикови модели и модели с фино настройване на инструкции, с параметри в диапазона от 500 милиона до 7,2 милиарда."}, "qwen2.5-math-1.5b-instruct": {"description": "Qwen-Math моделът разполага със силни умения за решаване на математически задачи."}, "qwen2.5-math-72b-instruct": {"description": "Моде<PERSON><PERSON>т Qwen-Math притежава силни способности за решаване на математически задачи."}, "qwen2.5-math-7b-instruct": {"description": "Моде<PERSON><PERSON>т Qwen-Math притежава силни способности за решаване на математически задачи."}, "qwen2.5-omni-7b": {"description": "Моделите от серията Qwen-Omni поддържат входни данни от множество модалности, включително видео, аудио, изображения и текст, и изходят аудио и текст."}, "qwen2.5-vl-32b-instruct": {"description": "Моделите от серията Qwen2.5-VL подобряват интелигентността, практичността и приложимостта на модела, като ги правят по-ефективни в сценарии като естествени разговори, създаване на съдържание, професионални услуги и разработка на код. Версията 32B използва технологии за обучение с подсилване за оптимизиране на модела, предлагайки в сравнение с другите модели от серията Qwen2.5 VL по-съответстващ на човешките предпочитания стил на изход, способност за разсъждение върху сложни математически проблеми, както и фино разбиране и разсъждение на изображения."}, "qwen2.5-vl-72b-instruct": {"description": "Подобрение на следването на инструкции, математика, решаване на проблеми и код, повишаване на способността за разпознаване на обекти, поддържа директно точно локализиране на визуални елементи в различни формати, поддържа разбиране на дълги видео файлове (до 10 минути) и локализиране на събития в секунда, може да разбира времеви последователности и скорости, базирано на способности за анализ и локализация, поддържа управление на OS или Mobile агенти, силна способност за извличане на ключова информация и изход в JSON формат, тази версия е 72B, най-силната версия в серията."}, "qwen2.5-vl-7b-instruct": {"description": "Подобрение на следването на инструкции, математика, решаване на проблеми и код, повишаване на способността за разпознаване на обекти, поддържа директно точно локализиране на визуални елементи в различни формати, поддържа разбиране на дълги видео файлове (до 10 минути) и локализиране на събития в секунда, може да разбира времеви последователности и скорости, базирано на способности за анализ и локализация, поддържа управление на OS или Mobile агенти, силна способност за извличане на ключова информация и изход в JSON формат, тази версия е 72B, най-силната версия в серията."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL е най-новата версия на визуално-езиковия модел от семейството Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 е новото поколение мащабен езиков модел на Alibaba, който предлага отлична производителност, за да отговори на разнообразни приложни нужди."}, "qwen2.5:1.5b": {"description": "Qwen2.5 е новото поколение мащабен езиков модел на Alibaba, който предлага отлична производителност, за да отговори на разнообразни приложни нужди."}, "qwen2.5:72b": {"description": "Qwen2.5 е новото поколение мащабен езиков модел на Alibaba, който предлага отлична производителност, за да отговори на разнообразни приложни нужди."}, "qwen2:0.5b": {"description": "Qwen2 е новото поколение голям езиков модел на Alibaba, предлагащ отлична производителност за разнообразни приложения."}, "qwen2:1.5b": {"description": "Qwen2 е новото поколение голям езиков модел на Alibaba, предлагащ отлична производителност за разнообразни приложения."}, "qwen2:72b": {"description": "Qwen2 е новото поколение голям езиков модел на Alibaba, предлагащ отлична производителност за разнообразни приложения."}, "qwen3": {"description": "Qwen3 е новото поколение на Alibaba голям езиков модел, който предлага отлична производителност, за да отговори на разнообразни приложения."}, "qwen3-0.6b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-1.7b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-14b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-235b-a22b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-235b-a22b-instruct-2507": {"description": "Отворен модел в не-мисловен режим, базиран на Qwen3, с леки подобрения в субективните творчески способности и безопасността на модела спрямо предишната версия (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Отворен модел в мисловен режим, базиран на Qwen3, с големи подобрения в логическите способности, общите умения, обогатяването на знания и творческите способности спрямо предишната версия (Tongyi Qianwen 3-235B-A22B), подходящ за сложни задачи с високи изисквания за разсъждение."}, "qwen3-30b-a3b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-30b-a3b-instruct-2507": {"description": "В сравнение с предишната версия (Qwen3-30B-A3B), общите способности на английски, китайски и многоезични задачи са значително подобрени. Специализирана оптимизация за субективни и отворени задачи, значително по-добре съобразена с предпочитанията на потребителите, което позволява предоставяне на по-полезни отговори."}, "qwen3-30b-a3b-thinking-2507": {"description": "Базиран на отворения модел в режим мислене на Qwen3, в сравнение с предишната версия (Tongyi Qianwen 3-30B-A3B) логическите способности, общите умения, знанията и творческите способности са значително подобрени, подходящ за сложни сценарии с интензивно разсъждение."}, "qwen3-32b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-4b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-8b": {"description": "Qwen3 е ново поколение модел с значително подобрени способности, който достига водещо ниво в индустрията в области като разсъждение, общо използване, агенти и многоезичност, и поддържа превключване на режимите на разсъждение."}, "qwen3-coder-480b-a35b-instruct": {"description": "Отворена версия на кодовия модел Tongyi Qianwen. Най-новият qwen3-coder-480b-a35b-instruct е кодов модел, базиран на Qwen3, с мощни Coding Agent способности, умения за използване на инструменти и взаимодействие с околната среда, способен на автономно програмиране с отлични кодови и общи умения."}, "qwen3-coder-plus": {"description": "Кодов модел Tongyi Qianwen. Най-новата серия Qwen3-Coder-Plus е базирана на Qwen3, с мощни Coding Agent способности, умения за използване на инструменти и взаимодействие с околната среда, способна на автономно програмиране с отлични кодови и общи умения."}, "qwq": {"description": "QwQ е експериментален изследователски модел, който се фокусира върху подобряване на AI разсъдъчните способности."}, "qwq-32b": {"description": "QwQ моделът за изводи, обучен на базата на модела Qwen2.5-32B, значително подобрява способностите си за изводи чрез усилено обучение. Основните показатели на модела, като математически код и други ключови индикатори (AIME 24/25, LiveCodeBench), както и някои общи индикатори (IFEval, LiveBench и др.), достигат нивото на DeepSeek-R1 в пълна версия, като всички показатели значително надвишават тези на DeepSeek-R1-Distill-Qwen-32B, също базиран на Qwen2.5-32B."}, "qwq-32b-preview": {"description": "QwQ моделът е експериментален изследователски модел, разработен от екипа на Qwen, който се фокусира върху подобряване на AI разсъдъчните способности."}, "qwq-plus": {"description": "QwQ е модел за разсъждение, обучен на базата на Qwen2.5, който значително подобрява способностите за разсъждение чрез усилено обучение. Основните показатели на модела в математика и кодиране (AIME 24/25, LiveCodeBench), както и някои общи показатели (IFEval, LiveBench и др.) достигат нивото на пълната версия на DeepSeek-R1."}, "qwq_32b": {"description": "Модел за разсъждение със среден размер от серията Qwen. В сравнение с традиционните модели за настройка на инструкции, QwQ, който притежава способности за разсъждение и разсъждение, може значително да подобри производителността в задачи с по-висока сложност."}, "r1-1776": {"description": "R1-1776 е версия на модела DeepSeek R1, след обучението, която предоставя непроверена и безпристрастна фактическа информация."}, "solar-mini": {"description": "Solar Mini е компактен LLM, който превъзхожда GPT-3.5, с мощни многоезични способности, поддържа английски и корейски, предоставяйки ефективно и компактно решение."}, "solar-mini-ja": {"description": "Solar Mini (Ja) разширява възможностите на Solar Mini, фокусирайки се върху японския език, като същевременно поддържа висока ефективност и отлично представяне в английския и корейския."}, "solar-pro": {"description": "Solar Pro е високоинтелигентен LLM, пуснат от Upstage, фокусиран върху способността за следване на инструкции с един GPU, с IFEval оценка над 80. В момента поддържа английски, а официалната версия е планирана за пускане през ноември 2024 г., с разширена поддръжка на езици и дължина на контекста."}, "sonar": {"description": "Лек продукт за търсене, базиран на контекст на търсене, по-бърз и по-евтин от Sonar Pro."}, "sonar-deep-research": {"description": "Deep Research извършва задълбочени експертни изследвания и ги обобщава в достъпни и приложими доклади."}, "sonar-pro": {"description": "Разширен продукт за търсене, който поддържа контекст на търсене, напреднали запитвания и проследяване."}, "sonar-reasoning": {"description": "Нови API продукти, поддържани от модела за разсъждение на DeepSeek."}, "sonar-reasoning-pro": {"description": "Нов API продукт, поддържан от модела за разсъждение DeepSeek."}, "stable-diffusion-3-medium": {"description": "Най-новият голям модел за генериране на изображения от текст, пуснат от Stability AI. Тази версия запазва предимствата на предишните поколения и значително подобрява качеството на изображенията, разбирането на текст и разнообразието на стилове, позволявайки по-точно интерпретиране на сложни естествени езикови подсказки и генериране на по-прецизни и разнообразни изображения."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large е мултимоделен дифузионен трансформър (MMDiT) модел за генериране на изображения от текст с 800 милиона параметри, предлагащ изключително качество на изображенията и съвпадение с подсказките, поддържащ генериране на изображения с резолюция до 1 милион пиксела и ефективна работа на обикновен хардуер за потребители."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo е модел, базиран на stable-diffusion-3.5-large, използващ технологията за противоречива дифузионна дистилация (ADD) за по-висока скорост."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 е инициализиран с теглата на stable-diffusion-v1.2 checkpoint и е фино настроен за 595k стъпки при резолюция 512x512 върху \"laion-aesthetics v2 5+\", с намалена текстова кондиционираност с 10% за подобряване на безкласовото насочено семплиране."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl представлява значително подобрение спрямо v1.5 и постига качество, сравнимо с водещия отворен модел midjourney. Основните подобрения включват: по-голям unet гръбнак, три пъти по-голям от предишния; добавен refinement модул за подобряване на качеството на генерираните изображения; по-ефективни техники за обучение и други."}, "stable-diffusion-xl-base-1.0": {"description": "Голям модел за генериране на изображения от текст, разработен и отворен от Stability AI, с водещи в индустрията способности за творческо генериране на изображения. Отличава се с изключителна способност за разбиране на инструкции и поддържа обратни промпти за прецизно дефиниране на съдържанието."}, "step-1-128k": {"description": "Баланс между производителност и разходи, подходящ за общи сценарии."}, "step-1-256k": {"description": "Супер дълга контекстова обработка, особено подходяща за анализ на дълги документи."}, "step-1-32k": {"description": "Поддържа диалози със средна дължина, подходящи за множество приложения."}, "step-1-8k": {"description": "Малък модел, подходящ за леки задачи."}, "step-1-flash": {"description": "Бърз модел, подходящ за реални диалози."}, "step-1.5v-mini": {"description": "Този модел разполага с мощни способности за разбиране на видео."}, "step-1o-turbo-vision": {"description": "Този модел разполага с мощни способности за разбиране на изображения и е по-добър от 1o в областта на математиката и кода. Моделът е по-малък от 1o и предлага по-бърза скорост на изход."}, "step-1o-vision-32k": {"description": "Този модел разполага с мощни способности за разбиране на изображения. В сравнение с моделите от серията step-1v, предлага по-силна визуална производителност."}, "step-1v-32k": {"description": "Поддържа визуални входове, подобряваща мултимодалното взаимодействие."}, "step-1v-8k": {"description": "Малък визуален модел, подходящ за основни текстово-визуални задачи."}, "step-1x-edit": {"description": "Този модел е специализиран за задачи по редактиране на изображения, способен да модифицира и подобрява изображения според предоставени от потребителя снимки и текстови описания. Поддържа различни входни формати, включително текстови описания и примерни изображения. Моделът разбира намеренията на потребителя и генерира редактирани изображения, отговарящи на изискванията."}, "step-1x-medium": {"description": "Този модел притежава мощни способности за генериране на изображения, поддържа вход от текстови описания. Има вградена поддръжка на китайски език, което позволява по-добро разбиране и обработка на китайски текстови описания, по-точно улавяне на семантиката и превръщането ѝ в визуални характеристики за по-прецизно генериране на изображения. Моделът може да генерира висококачествени и високоразделителни изображения и притежава известни способности за трансфер на стил."}, "step-2-16k": {"description": "Поддържа взаимодействия с голям мащаб на контекста, подходящи за сложни диалогови сценарии."}, "step-2-16k-exp": {"description": "Експерименталната версия на модела step-2, съдържаща най-новите функции, в процес на актуализация. Не се препоръчва за използване в официална производствена среда."}, "step-2-mini": {"description": "Модел с бърза производителност, базиран на новото поколение собствена архитектура Attention MFA, който постига резултати, подобни на step1 с много ниски разходи, като същевременно поддържа по-висока производителност и по-бързо време за отговор. Може да обработва общи задачи и притежава специализирани умения в кодирането."}, "step-2x-large": {"description": "Новото поколение модел за генериране на изображения Step Star, специализиран в генериране на висококачествени изображения според текстови описания от потребителя. Новият модел създава по-реалистични текстури и има по-силни способности за генериране на китайски и английски текст."}, "step-r1-v-mini": {"description": "Този модел е мощен модел за разсъждение с отлични способности за разбиране на изображения, способен да обработва информация от изображения и текст, и след дълбочинно разсъждение да генерира текстово съдържание. Моделът показва изключителни резултати в областта на визуалните разсъждения, като същевременно притежава първокласни способности в математиката, кода и текстовите разсъждения. Дължината на контекста е 100k."}, "taichu_llm": {"description": "Моделът на езика TaiChu е с изключителни способности за разбиране на езика, текстово генериране, отговори на знания, програмиране, математически изчисления, логическо разсъждение, анализ на емоции, резюмиране на текст и др. Иновативно комбинира предварително обучение с големи данни и разнообразни източници на знания, чрез непрекъснато усъвършенстване на алгоритмичните технологии и усвояване на нови знания от масивни текстови данни, за да осигури на потребителите по-удобна информация и услуги, както и по-интелигентно изживяване."}, "taichu_o1": {"description": "taichu_o1 е новото поколение модел за разсъждение, реализиращ човешки мисловни вериги чрез мултимодално взаимодействие и обучение с подсилване, поддържащ сложни решения и демонстриращ мисловни пътища, които могат да бъдат моделирани, с висока точност на изхода, подходящ за анализ на стратегии и дълбочинни разсъждения."}, "taichu_vl": {"description": "Съчетава способности за разбиране на изображения, прехвърляне на знания и логическо обяснение, като показва отлични резултати в областта на въпросите и отговорите с текст и изображения."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct има 80 милиарда параметри, като активиране на 13 милиарда параметри е достатъчно за съпоставяне с по-големи модели, поддържа хибридно разсъждение „бързо мислене/бавно мислене“; стабилно разбиране на дълги текстове; потвърдено с BFCL-v3 и τ-Bench, с водещи възможности на агент; комбинира GQA и множество формати за квантоване за ефективно разсъждение."}, "text-embedding-3-large": {"description": "Най-мощният модел за векторизация, подходящ за английски и неанглийски задачи."}, "text-embedding-3-small": {"description": "Ефективен и икономичен ново поколение модел за вграждане, подходящ за извличане на знания, RAG приложения и други сценарии."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 е 32B двуезичен (китайски и английски) модел на отворени тегла, оптимизиран за генериране на код, извиквания на функции и задачи с агенти. Той е предварително обучен на 15T висококачествени и повторно разсъждаващи данни и е допълнително усъвършенстван с човешка съвместимост, отхвърляне на проби и обучение с подсилване. Моделът показва отлични резултати в сложни разсъждения, генериране на артефакти и структурирани изходни задачи, постигащи производителност, сравнима с GPT-4o и DeepSeek-V3-0324 в множество бенчмаркове."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 е 32B двуезичен (китайски и английски) модел на отворени тегла, оптимизиран за генериране на код, извиквания на функции и задачи с агенти. Той е предварително обучен на 15T висококачествени и повторно разсъждаващи данни и е допълнително усъвършенстван с човешка съвместимост, отхвърляне на проби и обучение с подсилване. Моделът показва отлични резултати в сложни разсъждения, генериране на артефакти и структурирани изходни задачи, постигащи производителност, сравнима с GPT-4o и DeepSeek-V3-0324 в множество бенчмаркове."}, "thudm/glm-4-9b-chat": {"description": "GLM-4 е последната версия на предварително обучен модел от серията, публикувана от Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 е езиков модел с 9 милиарда параметри от серията GLM-4, разработен от THUDM. GLM-4-9B-0414 използва същите стратегии за усилено обучение и подравняване, които се прилагат за по-голямата му 32B версия, за да постигне висока производителност в съотношение с размера си, което го прави подходящ за внедряване с ограничени ресурси, което все пак изисква силни способности за разбиране и генериране на език."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 е подобрена версия на GLM-4-32B, проектирана за дълбока математика, логика и решаване на проблеми, свързани с код. Той прилага разширено обучение с подсилване (за специфични задачи и на базата на общи предпочитания) за подобряване на производителността при сложни многостепенни задачи. В сравнение с основния модел GLM-4-32B, Z1 значително подобрява способностите в структурираното разсъждение и формалните области.\n\nМоделът поддържа прилагане на стъпки за \"мислене\" чрез инженеринг на подсказки и предлага подобрена последователност за дълги формати на изхода. Той е оптимизиран за работни потоци на агенти и поддържа дълъг контекст (чрез YaRN), извиквания на JSON инструменти и конфигурации за фино проби за стабилно разсъждение. Идеален е за случаи, изискващи дълбочинно разсъждение, многостепенни разсъждения или формализирани изводи."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 е подобрена версия на GLM-4-32B, проектирана за дълбока математика, логика и решаване на проблеми, свързани с код. Той прилага разширено обучение с подсилване (за специфични задачи и на базата на общи предпочитания) за подобряване на производителността при сложни многостепенни задачи. В сравнение с основния модел GLM-4-32B, Z1 значително подобрява способностите в структурираното разсъждение и формалните области.\n\nМоделът поддържа прилагане на стъпки за \"мислене\" чрез инженеринг на подсказки и предлага подобрена последователност за дълги формати на изхода. Той е оптимизиран за работни потоци на агенти и поддържа дълъг контекст (чрез YaRN), извиквания на JSON инструменти и конфигурации за фино проби за стабилно разсъждение. Идеален е за случаи, изискващи дълбочинно разсъждение, многостепенни разсъждения или формализирани изводи."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 е езиков модел с 9B параметри от серията GLM-4, разработен от THUDM. Той прилага технологии, първоначално използвани в по-големия GLM-Z1 модел, включително разширено усилено обучение, подравняване на двойки и обучение за интензивни разсъждения в области като математика, кодиране и логика. Въпреки по-малкия си размер, той показва силна производителност в общите задачи за разсъждение и надминава много от отворените модели на нивото на теглата."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B е дълбок разсъдъчен модел с 32B параметри от серията GLM-4-Z1, оптимизиран за сложни, отворени задачи, изискващи дълго разсъждение. Той е построен на основата на glm-4-32b-0414, с добавени допълнителни етапи на усилено обучение и многостепенни стратегии за подравняване, въвеждайки \"разсъждателни\" способности, предназначени да симулират разширена когнитивна обработка. Това включва итеративно разсъждение, многократен анализ и работни потоци, подобрени с инструменти, като търсене, извличане и синтез с осведоменост за цитати.\n\nМоделът показва отлични резултати в изследователското писане, сравнителния анализ и сложните въпроси и отговори. Той поддържа извиквания на функции за търсене и навигация (\"search\", \"click\", \"open\", \"finish\"), което позволява използването му в агенти. Разсъждателното поведение се контролира от многократни цикли с базирани на правила награди и механизми за забавено вземане на решения, с референтни рамки за дълбоки изследвания, като вътрешния стек за подравняване на OpenAI. Този вариант е подходящ за сценарии, изискващи дълбочина, а не скорост."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera е създаден чрез комбиниране на DeepSeek-R1 и DeepSeek-V3 (0324), съчетавайки разсъдъчните способности на R1 и подобренията в ефективността на токените на V3. Той е базиран на архитектурата DeepSeek-MoE Transformer и е оптимизиран за общи задачи по генериране на текст.\n\nМоделът комбинира предварително обучените тегла на двата източника, за да балансира производителността в разсъжденията, ефективността и задачите за следване на инструкции. Той е публикуван под MIT лиценз и е предназначен за изследователски и търговски цели."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) предлага подобрена изчислителна мощ чрез ефективни стратегии и архитектура на модела."}, "tts-1": {"description": "Най-новият модел за текст в реч, оптимизиран за скорост в реални сценарии."}, "tts-1-hd": {"description": "Най-новият модел за текст в реч, оптимизиран за качество."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) е подходящ за прецизни задачи с инструкции, предлагащи отлични способности за обработка на език."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet повишава индустриалните стандарти, с производителност, надминаваща конкурентните модели и Claude 3 Opus, показвайки отлични резултати в широк спектър от оценки, като същевременно предлага скорост и разходи, сравними с нашите модели от средно ниво."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 сонет е най-бързият модел от следващото поколение на Anthropic. В сравнение с Claude 3 Haiku, Claude 3.7 Сонет е подобрен във всички умения и надминава най-големия модел от предишното поколение Claude 3 Opus в много интелектуални тестове."}, "v0-1.0-md": {"description": "Моделът v0-1.0-md е стара версия, която се обслужва чрез v0 API"}, "v0-1.5-lg": {"description": "Моделът v0-1.5-lg е подходящ за задачи, изискващи напреднало мислене или разсъждение"}, "v0-1.5-md": {"description": "Моделът v0-1.5-md е подходящ за ежедневни задачи и генериране на потребителски интерфейс (UI)"}, "wan2.2-t2i-flash": {"description": "Wanxiang 2.2 експресна версия, най-новият модел към момента. Комплексно подобрение в креативност, стабилност и реализъм, с бърза скорост на генериране и висока цена-ефективност."}, "wan2.2-t2i-plus": {"description": "Wanxiang 2.2 професионална версия, най-новият модел към момента. Комплексно подобрение в креативност, стабилност и реализъм, с богати детайли в генерираните изображения."}, "wanx-v1": {"description": "Основен модел за генериране на изображения от текст. Съответства на универсалния модел 1.0 на официалния сайт на Tongyi Wanxiang."}, "wanx2.0-t2i-turbo": {"description": "Специализиран в генериране на портрети с реалистична текстура, със средна скорост и ниски разходи. Съответства на експресния модел 2.0 на официалния сайт на Tongyi Wanxiang."}, "wanx2.1-t2i-plus": {"description": "Пълноценна ъпгрейд версия. Генерираните изображения са с по-богати детайли, скоростта е леко по-ниска. Съответства на професионалния модел 2.1 на официалния сайт на Tongyi Wanxiang."}, "wanx2.1-t2i-turbo": {"description": "Пълноценна ъпгрейд версия. Бърза скорост на генериране, цялостно качество и висока цена-ефективност. Съответства на експресния модел 2.1 на официалния сайт на Tongyi Wanxiang."}, "whisper-1": {"description": "Универсален модел за разпознаване на реч, поддържащ многоезично разпознаване на реч, превод на реч и разпознаване на език."}, "wizardlm2": {"description": "WizardLM 2 е езиков модел, предоставен от Microsoft AI, който се отличава в сложни диалози, многоезичност, разсъждение и интелигентни асистенти."}, "wizardlm2:8x22b": {"description": "WizardLM 2 е езиков модел, предоставен от Microsoft AI, който се отличава в сложни диалози, многоезичност, разсъждение и интелигентни асистенти."}, "x1": {"description": "Моделът Spark X1 ще бъде допълнително обновен, като на базата на водещите в страната резултати в математически задачи, ще постигне ефекти в общи задачи като разсъждение, генериране на текст и разбиране на език, сравними с OpenAI o1 и DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 е обновена версия на Yi. Тя използва висококачествен корпус от 500B токена за продължителна предварителна обучение на Yi и е майсторски подобрявана с 3M разнообразни примера за fino-tuning."}, "yi-large": {"description": "Новият модел с хиляда милиарда параметри предлага изключителни способности за отговори и генериране на текст."}, "yi-large-fc": {"description": "Поддържа и усилва способностите за извикване на инструменти на базата на модела yi-large, подходящ за различни бизнес сценарии, изискващи изграждане на агенти или работни потоци."}, "yi-large-preview": {"description": "Начална версия, препоръчва се да се използва yi-large (новата версия)."}, "yi-large-rag": {"description": "Висококачествена услуга, базирана на мощния модел yi-large, комбинираща технологии за извличане и генериране, предлагаща точни отговори и услуги за търсене на информация в реално време."}, "yi-large-turbo": {"description": "Изключителна производителност на висока цена. Балансирано прецизно настройване на производителността и скоростта на разсъжденията."}, "yi-lightning": {"description": "Най-новият високо производителен модел, който гарантира висококачествени изходи, докато значително ускорява времето за разсъждение."}, "yi-lightning-lite": {"description": "Лека версия, препоръчително е да се използва yi-lightning."}, "yi-medium": {"description": "Модел с среден размер, обновен и прецизно настроен, с балансирани способности и висока цена на производителност."}, "yi-medium-200k": {"description": "200K свръхдълъг контекстов прозорец, предлагащ дълбочинно разбиране и генериране на дълги текстове."}, "yi-spark": {"description": "Малък и мощен, лек и бърз модел. Предлага подобрени способности за математически операции и писане на код."}, "yi-vision": {"description": "Модел за сложни визуални задачи, предлагащ висока производителност за разбиране и анализ на изображения."}, "yi-vision-v2": {"description": "Модел за сложни визуални задачи, предлагащ висока производителност в разбирането и анализа на базата на множество изображения."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 е базов модел, специално създаден за интелигентни агенти, използващ архитектура с микс от експерти (Mixture-of-Experts). Той е дълбоко оптимизиран за използване на инструменти, уеб браузване, софтуерно инженерство и фронтенд програмиране, и поддържа безпроблемна интеграция с кодови агенти като Claude Code и Roo Code. GLM-4.5 използва смесен режим на разсъждение, подходящ за сложни и ежедневни приложения."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air е базов модел, специално създаден за интелигентни агенти, използващ архитектура с микс от експерти (Mixture-of-Experts). Той е дълбоко оптимизиран за използване на инструменти, уеб браузване, софтуерно инженерство и фронтенд програмиране, и поддържа безпроблемна интеграция с кодови агенти като Claude Code и Roo Code. GLM-4.5 използва смесен режим на разсъждение, подходящ за сложни и ежедневни приложения."}}