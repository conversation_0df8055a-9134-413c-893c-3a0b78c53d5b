{"ai21": {"description": "AI21 Labs изгражда основни модели и системи за изкуствен интелект за предприятия, ускорявайки приложението на генеративния изкуствен интелект в производството."}, "ai360": {"description": "360 AI е платформа за AI модели и услуги, предлагана от компания 360, предлагаща множество напреднали модели за обработка на естествен език, включително 360GPT2 Pro, 360GPT Pro, 360GPT Turbo и 360GPT Turbo Responsibility 8K. Тези модели комбинират голям брой параметри и мултимодални способности, широко използвани в текстово генериране, семантично разбиране, диалогови системи и генериране на код. Чрез гъвкава ценова стратегия, 360 AI отговаря на разнообразни потребителски нужди, поддържайки интеграция за разработчици и насърчавайки иновации и развитие на интелигентни приложения."}, "aihubmix": {"description": "AiHubMix предоставя достъп до множество AI модели чрез единен API интерфейс."}, "anthropic": {"description": "Anthropic е компания, специализирана в изследвания и разработка на изкуствен интелект, предлагаща набор от напреднали езикови модели, кат<PERSON> Claude 3.5 Sonnet, Claude 3 Sonnet, Claude 3 Opus и Claude 3 Haiku. Тези модели постигат идеален баланс между интелигентност, скорост и разходи, подходящи за различни приложения, от корпоративни натоварвания до бързи отговори. Claude 3.5 Sonnet, като най-новия им модел, показва отлични резултати в множество оценки, като същевременно поддържа висока цена-качество."}, "azure": {"description": "Azure предлага разнообразие от напреднали AI модели, включително GPT-3.5 и най-новата серия GPT-4, поддържащи различни типове данни и сложни задачи, с акцент върху безопасни, надеждни и устойчиви AI решения."}, "azureai": {"description": "Azure предлага множество напреднали AI модели, включително GPT-3.5 и най-новата серия GPT-4, които поддържат различни типове данни и сложни задачи, ангажирани с безопасни, надеждни и устойчиви AI решения."}, "baichuan": {"description": "Baichuan Intelligence е компания, специализирана в разработката на големи модели за изкуствен интелект, чийто модели показват отлични резултати в китайски задачи, свързани с енциклопедии, обработка на дълги текстове и генериране на съдържание, надминавайки основните чуждестранни модели. Baichuan Intelligence също така притежава индустриално водещи мултимодални способности, показвайки отлични резултати в множество авторитетни оценки. Моделите им включват Baichuan 4, Baichuan 3 Turbo и Baichuan 3 Turbo 128k, оптимизирани за различни приложения, предлагащи решения с висока цена-качество."}, "bedrock": {"description": "Bedrock е услуга, предоставяна от Amazon AWS, фокусирана върху предоставянето на напреднали AI езикови и визуални модели за предприятия. Семейството на моделите включва серията Claude на Anthropic, серията Llama 3.1 на Meta и други, обхващащи разнообразие от опции от леки до високо производителни, поддържащи текстово генериране, диалог, обработка на изображения и много други задачи, подходящи за различни мащаби и нужди на бизнес приложения."}, "cloudflare": {"description": "Работа с модели на машинно обучение, задвижвани от безсървърни GPU, в глобалната мрежа на Cloudflare."}, "cohere": {"description": "Cohere ви предлага най-съвременни многоезични модели, напреднали функции за търсене и AI работно пространство, проектирано специално за съвременните предприятия — всичко интегрирано в една сигурна платформа."}, "deepseek": {"description": "DeepSeek е компания, специализирана в изследвания и приложения на технологии за изкуствен интелект, чийто най-нов модел DeepSeek-V2.5 комбинира способности за общи диалози и обработка на код, постигайки значителни подобрения в съответствието с човешките предпочитания, писателските задачи и следването на инструкции."}, "fal": {"description": "Генера<PERSON>ивна медийна платформа за разработчици"}, "fireworksai": {"description": "Fireworks AI е водещ доставчик на напреднали езикови модели, фокусирайки се върху извикване на функции и мултимодална обработка. Най-новият им модел Firefunction V2, базиран на Llama-3, е оптимизиран за извикване на функции, диалози и следване на инструкции. Визуалният езиков модел FireLLaVA-13B поддържа смесени входове от изображения и текст. Други забележителни модели включват серията Llama и серията Mixtral, предлагащи ефективна поддръжка за многоезично следване на инструкции и генериране."}, "giteeai": {"description": "Безсървърният API на Гити ИИ предоставя на разработчиците ИИ услугата за извеждане на голям модел."}, "github": {"description": "С моделите на GitHub разработчиците могат да станат AI инженери и да изграждат с водещите AI модели в индустрията."}, "google": {"description": "Серият<PERSON> Gemini на Google е най-напредналият и универсален AI модел, разработен от Google DeepMind, проектиран за мултимодално разбиране и обработка на текст, код, изображения, аудио и видео. Подходящ за различни среди, от центрове за данни до мобилни устройства, значително увеличава ефективността и приложимостта на AI моделите."}, "groq": {"description": "Инженерният двигател LPU на Groq показва изключителни резултати в последните независими тестове на големи езикови модели (LLM), преосмисляйки стандартите за AI решения с невероятната си скорост и ефективност. Groq е представител на мигновен скорост на изводите, демонстрирайки добро представяне в облачни внедрения."}, "higress": {"description": "Higress е облачно роден API шлюз, създаден в Alibaba, за да реши проблемите с Tengine reload, които вредят на дългосрочните връзки, и недостатъчните възможности за баланс на натоварването на gRPC/Dubbo."}, "huggingface": {"description": "HuggingFace Inference API предлагава бърз и безплатен начин да изследвате хиляди модели, подходящи за различни задачи. Независимо дали проектирате прототип за ново приложение, или опитвате функционалността на машинното обучение, този API ви предоставя незабавен достъп до високопроизводителни модели в множество области."}, "hunyuan": {"description": "Модел на голям език, разрабо<PERSON><PERSON><PERSON> от Ten<PERSON>, който притежава мощни способности за създаване на текст на китайски, логическо разсъждение в сложни контексти и надеждни способности за изпълнение на задачи."}, "infiniai": {"description": "Предоставя високопроизводителни, лесни за използване и сигурни услуги с големи модели за приложението разработчици, покриващи целия процес от разработка на големи модели до техното услугово разгъване."}, "internlm": {"description": "Отворена организация, посветена на изследването и разработването на инструменти за големи модели. Предоставя на всички AI разработчици ефективна и лесна за използване отворена платформа, която прави най-съвременните технологии и алгоритми за големи модели достъпни."}, "jina": {"description": "Jina AI е основана през 2020 г. и е водеща компания в областта на търсенето с AI. Нашата платформа за търсене включва векторни модели, реорганизатори и малки езикови модели, които помагат на предприятията да изградят надеждни и висококачествени генеративни AI и мултимодални приложения за търсене."}, "lmstudio": {"description": "LM Studio е настолно приложение за разработка и експериментиране с LLMs на вашия компютър."}, "minimax": {"description": "MiniMax е компания за универсален изкуствен интелект, основана през 2021 г., която се стреми да създаде интелигентност заедно с потребителите. MiniMax е разработила различни универсални големи модели, включително текстови модели с трилйон параметри, модели за глас и модели за изображения. Също така е пуснала приложения като Conch AI."}, "mistral": {"description": "Mistral предлага напреднали универсални, професионални и изследователски модели, широко използвани в сложни разсъждения, многоезични задачи, генериране на код и др. Чрез интерфейси за извикване на функции, потребителите могат да интегрират персонализирани функции за специфични приложения."}, "modelscope": {"description": "ModelScope е платформа за модели като услуга, пусната от Alibaba Cloud, която предлага богато разнообразие от AI модели и услуги за извод."}, "moonshot": {"description": "Moonshot е отворена платформа, представена от Beijing Dark Side Technology Co., Ltd., предлагаща множество модели за обработка на естествен език, с широко приложение, включително, но не само, създаване на съдържание, академични изследвания, интелигентни препоръки, медицинска диагностика и др., поддържаща обработка на дълги текстове и сложни генериращи задачи."}, "novita": {"description": "Novita AI е платформа, предлагаща API услуги за множество големи езикови модели и генериране на AI изображения, гъвкава, надеждна и икономически ефективна. Поддържа най-новите отворени модели, като Llama3 и Mistral, и предлага цялостни, потребителски приятелски и автоматично разширяеми API решения за разработка на генеративни AI приложения, подходящи за бързото развитие на AI стартъпи."}, "nvidia": {"description": "NVIDIA NIM™ предлага контейнери, които могат да се използват за самостоятелно хоствани GPU ускорени инференционни микросервизи, поддържащи разгръщането на предварително обучени и персонализирани AI модели в облака, центрове за данни, RTX™ AI персонални компютри и работни станции."}, "ollama": {"description": "Моделите, предоставени от <PERSON>, обхващат широк спектър от области, включително генериране на код, математически операции, многоезично обработване и диалогова интеракция, отговарящи на разнообразните нужди на предприятията и локализирани внедрявания."}, "openai": {"description": "OpenAI е водеща световна изследователска институция в областта на изкуствения интелект, чийто модели, като серията GPT, напредват в границите на обработката на естествен език. OpenAI се стреми да трансформира множество индустрии чрез иновации и ефективни AI решения. Продуктите им предлагат значителна производителност и икономичност, широко използвани в изследвания, бизнес и иновационни приложения."}, "openrouter": {"description": "OpenRouter е платформа за услуги, предлагаща интерфейси за множество авангардни големи модели, поддържащи OpenAI, Anthropic, LLaMA и много други, подходяща за разнообразни нужди от разработка и приложение. Потребителите могат гъвкаво да избират оптималния модел и цена в зависимост от собствените си нужди, подобрявайки AI опита."}, "perplexity": {"description": "Perplexity е водещ доставчик на модели за генериране на диалози, предлагащ множество напреднали модели Llama 3.1, поддържащи онлайн и офлайн приложения, особено подходящи за сложни задачи по обработка на естествен език."}, "ppio": {"description": "PPIO ПайОу облак предлага стабилни и икономически изгодни API услуги за отворени модели, поддържащи цялата серия DeepSeek, Llama, Qwen и други водещи модели в индустрията."}, "qiniu": {"description": "Qiniu е водещ доставчик на облачни услуги, предлагащ бързи и ефективни API за извикване на големи модели, включително и тези на阿里巴巴, с гъвкави възможности за изграждане и развитие на AI приложения."}, "qwen": {"description": "Qwen е самостоятелно разработен свръхголям езиков модел на Alibaba Cloud, с мощни способности за разбиране и генериране на естествен език. Може да отговаря на различни въпроси, да създава текстово съдържание, да изразява мнения и да пише код, играейки роля в множество области."}, "sambanova": {"description": "SambaNova Cloud позволява на разработчиците лесно да използват най-добрите отворени модели и да се наслаждават на най-бързата скорост на извеждане."}, "search1api": {"description": "Search1API предоставя достъп до серията модели DeepSeek, които могат да се свързват в мрежа при нужда, включително стандартна и бърза версия, с поддръжка за избор на модели с различни параметри."}, "sensenova": {"description": "SenseNova, с мощната основа на SenseTime, предлага ефективни и лесни за използване услуги за големи модели с пълен стек."}, "siliconcloud": {"description": "SiliconFlow се стреми да ускори AGI, за да бъде от полза за човечеството, повишавайки ефективността на мащабния AI чрез лесен за използване и икономически изгоден GenAI стек."}, "spark": {"description": "Spark на iFlytek предлага мощни AI способности в множество области и езици, използвайки напреднали технологии за обработка на естествен език, за изграждане на иновационни приложения, подходящи за интелигентни устройства, интелигентно здравеопазване, интелигентни финанси и други вертикални сцени."}, "stepfun": {"description": "StepFun предлага индустриално водещи мултимодални и сложни разсъждения, поддържащи разбиране на свръхдълги текстове и мощни функции за самостоятелно планиране на търсене."}, "taichu": {"description": "Институтът по автоматизация на Китайската академия на науките и Институтът по изкуствен интелект в Ухан представят ново поколение мултимодални големи модели, поддържащи многократни въпроси и отговори, текстово създаване, генериране на изображения, 3D разбиране, анализ на сигнали и др., с по-силни способности за познание, разбиране и създаване, предоставяйки ново взаимодействие."}, "tencentcloud": {"description": "Атомни способности на знаниевия двигател (LLM Knowledge Engine Atomic Power) са базирани на разработката на знаниевия двигател и предлагат пълна верига от способности за въпроси и отговори, насочени към предприятия и разработчици, предоставяйки гъвкави възможности за изграждане и разработка на моделни приложения. Можете да изградите собствена моделна услуга чрез множество атомни способности, като използвате услуги за анализ на документи, разделяне, вграждане, многократни пренаписвания и др., за да персонализирате AI бизнеса, специфичен за вашето предприятие."}, "togetherai": {"description": "Together AI се стреми да постигне водеща производителност чрез иновационни AI модели, предлагащи широки възможности за персонализация, включително бърза поддръжка за разширяване и интуитивни процеси на внедряване, отговарящи на разнообразните нужди на предприятията."}, "upstage": {"description": "Upstage се фокусира върху разработването на AI модели за различни бизнес нужди, включително Solar LLM и документен AI, с цел постигане на човешки универсален интелект (AGI). Създава прости диалогови агенти чрез Chat API и поддържа извикване на функции, превод, вграждане и специфични приложения."}, "v0": {"description": "v0 е асистент за програмиране в екип, който ви позволява да описвате идеите си с естествен език и автоматично генерира код и потребителски интерфейс (UI) за вашия проект"}, "vertexai": {"description": "Серията Gemini на Google е най-напредналият и универсален AI модел, създаден от Google DeepMind, проектиран за мултимодалност, който поддържа безпроблемно разбиране и обработка на текст, код, изображения, аудио и видео. Подходящ за различни среди, от центрове за данни до мобилни устройства, значително увеличава ефективността и приложимостта на AI моделите."}, "vllm": {"description": "vLLM е бърза и лесна за използване библиотека за LLM инференция и услуги."}, "volcengine": {"description": "Платформа за разработка на услуги с големи модели, пусната от ByteDance, предлагаща богати на функции, безопасни и конкурентни по цена услуги за извикване на модели. Освен това предоставя край до край функции като данни за модели, фина настройка, инференция и оценка, за да осигури всестранна подкрепа за разработката на вашите AI приложения."}, "wenxin": {"description": "Платформа за разработка и услуги на корпоративно ниво, предлагаща цялостно решение за разработка на генеративни модели на изкуствен интелект и приложения, с най-пълния и лесен за използване инструментариум за целия процес на разработка на модели и приложения."}, "xai": {"description": "xAI е компания, която се стреми да изгражда изкуствен интелект за ускоряване на човешките научни открития. Нашата мисия е да насърчаваме общото ни разбиране за вселената."}, "xinference": {"description": "Xorbits Inference (Xinference) е платформа с отворен код, предназначена да опрости изпълнението и интегрирането на различни AI модели. С Xinference можете да използвате всякакви LLM с отворен код, модели за вграждане и мултимодални модели за извършване на изводи в облак или локална среда, както и да създавате мощни AI приложения."}, "zeroone": {"description": "01.AI се фокусира върху технологии за изкуствен интелект от ерата на AI 2.0, активно насърчавайки иновации и приложения на \"човек + изкуствен интелект\", използвайки мощни модели и напреднали AI технологии за повишаване на производителността на човека и реализиране на технологично овластяване."}, "zhipu": {"description": "Zhipu AI предлага отворена платформа за мултимодални и езикови модели, поддържащи широк спектър от AI приложения, включително обработка на текст, разбиране на изображения и помощ при програмиране."}}