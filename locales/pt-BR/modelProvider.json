{"azure": {"azureApiVersion": {"desc": "A versão da API da Azure, seguindo o formato AAAA-MM-DD, consulte a [versão mais recente](https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#chat-completions)", "fetch": "Obter lista", "title": "Versão da API da Azure"}, "empty": "Por favor, insira o ID do modelo para adicionar o primeiro modelo", "endpoint": {"desc": "Você pode encontrar este valor na seção 'Chaves e Endpoints' ao verificar os recursos no portal Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Endereço da API Azure"}, "modelListPlaceholder": "Selecione ou adicione o modelo OpenAI que você implantou", "title": "Azure OpenAI", "token": {"desc": "Você pode encontrar este valor na seção 'Chaves e Endpoints' ao verificar os recursos no portal Azure. Você pode usar KEY1 ou KEY2", "placeholder": "Azure API Key", "title": "API Key"}}, "azureai": {"azureApiVersion": {"desc": "Versão da API do Azure, seguindo o formato AAAA-MM-DD. Consulte a [versão mais recente](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "Obter lista", "title": "Versão da API do Azure"}, "endpoint": {"desc": "Encontre o ponto de extremidade de inferência do modelo do Azure AI na visão geral do projeto Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Ponto de extremidade do Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Encontre a chave da API na visão geral do projeto Azure AI", "placeholder": "Chave do <PERSON>zure", "title": "Chave"}}, "bedrock": {"accessKeyId": {"desc": "Insira o AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "Teste se o AccessKeyId / SecretAccessKey foi preenchido corretamente"}, "region": {"desc": "Insira o AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Insira o AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Se você estiver usando AWS SSO/STS, insira seu Token de Sessão da AWS", "placeholder": "Token de Sessão da AWS", "title": "Token de Sessão da AWS (opcional)"}, "title": "Bedrock", "unlock": {"customRegion": "Região de serviço personalizada", "customSessionToken": "Token de Sessão Personalizado", "description": "Digite sua AWS AccessKeyId / SecretAccessKey para iniciar a sessão. O aplicativo não irá armazenar suas configurações de autenticação", "imageGenerationDescription": "Digite seu AWS AccessKeyId / SecretAccessKey para começar a gerar. O aplicativo não armazenará suas credenciais de autenticação", "title": "Usar informações de autenticação Bedrock personalizadas"}}, "cloudflare": {"apiKey": {"desc": "Insira o Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Insira o ID da conta do Cloudflare ou o endereço da API personalizado", "placeholder": "ID da conta do Cloudflare / URL da API personalizada", "title": "ID da conta do Cloudflare / Endereço da API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Por favor, insira sua API Key", "title": "API Key"}, "basicTitle": "Informações Básicas", "configTitle": "Informações de Configuração", "confirm": "Criar <PERSON>", "createSuccess": "Criação bem-sucedida", "description": {"placeholder": "Descrição do provedor (opcional)", "title": "Descrição do Provedor"}, "id": {"desc": "Identificador único do provedor de serviços, não pode ser modificado após a criação", "format": "<PERSON><PERSON> pode conter n<PERSON>, letras minús<PERSON>, hí<PERSON><PERSON> (-) e sublinhados (_) ", "placeholder": "Sugestão: tudo em minús<PERSON>, por exemplo, openai, não poderá ser modificado após a criação", "required": "Por favor, insira o ID do provedor", "title": "ID do Provedor"}, "logo": {"required": "Por favor, envie um logo correto do provedor", "title": "Logo do Provedor"}, "name": {"placeholder": "Por favor, insira o nome de exibição do provedor", "required": "Por favor, insira o nome do provedor", "title": "Nome do Provedor"}, "proxyUrl": {"required": "Por favor, insira o endereço do proxy", "title": "Endereço do Proxy"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Por favor, selecione o tipo de SDK", "title": "Formato da Requisição"}, "title": "<PERSON><PERSON><PERSON> Pro<PERSON>or de AI Personalizado"}, "github": {"personalAccessToken": {"desc": "Insira seu PAT do Github, clique [aqui](https://github.com/settings/tokens) para criar", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Insira seu Token do HuggingFace, clique [aqui](https://huggingface.co/settings/tokens) para criar", "placeholder": "hf_xxxxxxxxx", "title": "Token do HuggingFace"}}, "list": {"title": {"disabled": "Fornecedor não habilitado", "enabled": "Fornecedor habilitado"}}, "menu": {"addCustomProvider": "<PERSON><PERSON><PERSON><PERSON>", "all": "Todos", "list": {"disabled": "Desativado", "enabled": "<PERSON><PERSON>do"}, "notFound": "Nenhum resultado encontrado", "searchProviders": "<PERSON><PERSON><PERSON><PERSON>...", "sort": "Ordenação Personalizada"}, "ollama": {"checker": {"desc": "Teste se o endereço do proxy está corretamente preenchido", "title": "Verificação de Conectividade"}, "customModelName": {"desc": "Adicione modelos personalizados, separe múltiplos modelos com vírgulas (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Nomes dos Modelos Personalizados"}, "download": {"desc": "Ollama está baixando este modelo, por favor, evite fechar esta página. O download será retomado do ponto em que parou.", "failed": "Falha ao baixar o modelo, por favor verifique a rede ou as configurações do Ollama e tente novamente", "remainingTime": "Tempo restante", "speed": "Velocidade de download", "title": "<PERSON><PERSON>ndo o modelo {{model}} "}, "endpoint": {"desc": "Deve incluir http(s)://, pode deixar em branco se não houver especificação local adicional", "title": "Endereço do Proxy de Interface"}, "title": "Ollama", "unlock": {"cancel": "Cancel Download", "confirm": "Download", "description": "Enter your Ollama model tag to continue the session", "downloaded": "{{completed}} / {{total}}", "starting": "Starting download...", "title": "Download specified Ollama model"}}, "providerModels": {"config": {"aesGcm": "Sua chave e o endereço do proxy serão criptografados usando o algoritmo de criptografia <1>AES-GCM</1>", "apiKey": {"desc": "Por favor, insira sua {{name}} API Key", "descWithUrl": "Por favor, insira sua chave API do {{name}}, <3>clique aqui para obter</3>", "placeholder": "{{name}} API Key", "title": "API Key"}, "baseURL": {"desc": "Deve incluir http(s)://", "invalid": "Por favor, insira uma URL válida", "placeholder": "https://seu-endereco-proxy.com/v1", "title": "Endereço do Proxy API"}, "checker": {"button": "Verificar", "desc": "Teste se a API Key e o endereço do proxy estão preenchidos corretamente", "pass": "Verificação bem-sucedida", "title": "Verificação de Conectividade"}, "fetchOnClient": {"desc": "O modo de requisição do cliente iniciará a requisição de sessão diretamente do navegador, podendo aumentar a velocidade de resposta", "title": "Usar Modo de Requisição do Cliente"}, "helpDoc": "Tutorial de Configuração", "responsesApi": {"desc": "Adota o novo padrão de formato de requisição da OpenAI, desbloqueando recursos avançados como cadeias de raciocínio", "title": "Usar o padrão Responses API"}, "waitingForMore": "Mais modelos estão <1>planejados para integração</1>, fique atento"}, "createNew": {"title": "Criar Modelo de AI Personalizado"}, "item": {"config": "Con<PERSON><PERSON>rar <PERSON>", "customModelCards": {"addNew": "Criar e adicionar modelo {{id}}", "confirmDelete": "Você está prestes a excluir este modelo personalizado, após a exclusão não poderá ser recuperado, por favor, proceda com cautela."}, "delete": {"confirm": "Confirmar exclusão do modelo {{displayName}}?", "success": "Exclusão bem-sucedida", "title": "Excluir Modelo"}, "modelConfig": {"azureDeployName": {"extra": "Campo solicitado na Azure OpenAI", "placeholder": "Por favor, insira o nome de implantação do modelo na Azure", "title": "Nome de Implantação do Modelo"}, "deployName": {"extra": "Este campo será usado como ID do modelo ao enviar a solicitação", "placeholder": "Insira o nome ou ID real do modelo implantado", "title": "Nome da implantação do modelo"}, "displayName": {"placeholder": "Por favor, insira o nome de exibição do modelo, por exemplo, ChatGPT, GPT-4, etc.", "title": "Nome de Exibição do Modelo"}, "files": {"extra": "A implementação atual de upload de arquivos é apenas uma solução temporária, limitada a tentativas pessoais. A capacidade completa de upload de arquivos será implementada posteriormente.", "title": "Suporte a Upload de Arquivos"}, "functionCall": {"extra": "Esta configuração ativará apenas a capacidade do modelo de usar ferramentas, permitindo assim a adição de plugins do tipo ferramenta. No entanto, se o uso real das ferramentas é suportado depende inteiramente do modelo em si, teste a usabilidade por conta própria.", "title": "Suporte ao uso de ferramentas"}, "id": {"extra": "Não pode ser modificado após a criação, será usado como ID do modelo ao chamar a IA", "placeholder": "Insira o ID do modelo, por exemplo, gpt-4o ou claude-3.5-sonnet", "title": "ID do Modelo"}, "modalTitle": "Configuração do Modelo Personalizado", "reasoning": {"extra": "Esta configuração ativará apenas a capacidade de pensamento profundo do modelo, e o efeito específico depende totalmente do próprio modelo. Por favor, teste se este modelo possui a capacidade de pensamento profundo utilizável.", "title": "Suporte a Pensamento Profundo"}, "tokens": {"extra": "Configurar o número máximo de tokens suportados pelo modelo", "title": "<PERSON><PERSON> de contexto máxima", "unlimited": "<PERSON><PERSON><PERSON><PERSON>"}, "vision": {"extra": "Esta configuração apenas habilitará a configuração de upload de imagens no aplicativo, se o reconhecimento for suportado depende do modelo em si, teste a capacidade de reconhecimento visual desse modelo.", "title": "Suporte a Reconhecimento Visual"}}, "pricing": {"image": "${{amount}}/imagem", "inputCharts": "${{amount}}/M caracteres", "inputMinutes": "${{amount}}/minuto", "inputTokens": "Entrada ${{amount}}/M", "outputTokens": "Saída ${{amount}}/M"}, "releasedAt": "Lan<PERSON><PERSON> em {{releasedAt}}"}, "list": {"addNew": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "Não habilitado", "disabledActions": {"showMore": "Mostrar tudo"}, "empty": {"desc": "Por favor, crie um modelo personalizado ou importe um modelo para começar a usar.", "title": "Nenhum modelo disponível"}, "enabled": "Habilitado", "enabledActions": {"disableAll": "Desabilitar todos", "enableAll": "Habilitar todos", "sort": "Ordenar modelos personalizados"}, "enabledEmpty": "Nenhum modelo habilitado no momento, por favor habilite os modelos desejados na lista abaixo~", "fetcher": {"clear": "Limpar modelos obtidos", "fetch": "Obter lista de modelos", "fetching": "Obtendo lista de modelos...", "latestTime": "Última atualização: {{time}}", "noLatestTime": "Lista ainda não obtida"}, "resetAll": {"conform": "Você tem certeza de que deseja redefinir todas as modificações do modelo atual? Após a redefinição, a lista de modelos atuais voltará ao estado padrão", "success": "Redefinição bem-sucedida", "title": "<PERSON>ef<PERSON><PERSON> as modificaç<PERSON><PERSON>"}, "search": "Pesquisar modelos...", "searchResult": "Encontrados {{count}} modelos", "title": "Lista de Modelos", "total": "Um total de {{count}} modelos disponíveis"}, "searchNotFound": "Nenhum resultado encontrado"}, "sortModal": {"success": "Ordenação atualizada com sucesso", "title": "Ordenação Personalizada", "update": "<PERSON><PERSON><PERSON><PERSON>"}, "updateAiProvider": {"confirmDelete": "Você está prestes a excluir este provedor de AI, após a exclusão não poderá ser recuperado, deseja confirmar a exclusão?", "deleteSuccess": "Exclusão bem-sucedida", "tooltip": "Atualizar configurações básicas do provedor", "updateSuccess": "Atualização bem-sucedida"}, "updateCustomAiProvider": {"title": "Atualizar configuração do provedor de IA personalizado"}, "vertexai": {"apiKey": {"desc": "Insira suas Chaves do Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Chaves do Vertex AI"}}, "zeroone": {"title": "01.<PERSON> Zero e Um"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}