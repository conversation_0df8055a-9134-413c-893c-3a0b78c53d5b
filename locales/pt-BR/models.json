{"01-ai/yi-1.5-34b-chat": {"description": "Zero Um, o mais recente modelo de ajuste fino de código aberto, com 34 bilhões de parâmetros, suporta múltiplos cenários de diálogo, com dados de treinamento de alta qualidade, alinhados às preferências humanas."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero Um, o mais recente modelo de ajuste fino de código aberto, com 9 bilhões de parâmetros, suporta múltiplos cenários de diálogo, com dados de treinamento de alta qualidade, alinhados às preferências humanas."}, "360/deepseek-r1": {"description": "【Versão implantada 360】DeepSeek-R1 utilizou amplamente técnicas de aprendizado por reforço na fase de pós-treinamento, melhorando significativamente a capacidade de inferência do modelo com apenas poucos dados rotulados. Em tarefas de matemática, código e raciocínio em linguagem natural, seu desempenho é comparável à versão oficial OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro, como um membro importante da série de modelos de IA da 360, atende a diversas aplicações de linguagem natural com sua capacidade eficiente de processamento de texto, suportando compreensão de longos textos e diálogos em múltiplas rodadas."}, "360gpt-pro-trans": {"description": "Modelo dedicado à tradução, otimizado com ajuste fino profundo, com resultados de tradução líderes."}, "360gpt-turbo": {"description": "360GPT Turbo oferece poderosas capacidades de computação e diálogo, com excelente compreensão semântica e eficiência de geração, sendo a solução ideal de assistente inteligente para empresas e desenvolvedores."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K enfatiza segurança semântica e responsabilidade, projetado especificamente para cenários de aplicação com altas exigências de segurança de conteúdo, garantindo precisão e robustez na experiência do usuário."}, "360gpt2-o1": {"description": "O 360gpt2-o1 utiliza busca em árvore para construir cadeias de pensamento e introduz um mecanismo de reflexão, sendo treinado com aprendizado por reforço, o modelo possui a capacidade de auto-reflexão e correção de erros."}, "360gpt2-pro": {"description": "360GPT2 Pro é um modelo avançado de processamento de linguagem natural lançado pela 360, com excelente capacidade de geração e compreensão de texto, destacando-se especialmente na geração e criação de conteúdo, capaz de lidar com tarefas complexas de conversão de linguagem e interpretação de papéis."}, "360zhinao2-o1": {"description": "O 360zhinao2-o1 utiliza busca em árvore para construir cadeias de pensamento e introduz um mecanismo de reflexão, utilizando aprendizado por reforço para treinar, permitindo que o modelo tenha a capacidade de auto-reflexão e correção de erros."}, "4.0Ultra": {"description": "Spark4.0 Ultra é a versão mais poderosa da série de grandes modelos Xinghuo, que, ao atualizar a conexão de busca online, melhora a capacidade de compreensão e resumo de conteúdo textual. É uma solução abrangente para aumentar a produtividade no trabalho e responder com precisão às demandas, sendo um produto inteligente líder na indústria."}, "AnimeSharp": {"description": "AnimeSharp (também conhecido como “4x‑AnimeSharp”) é um modelo de super-resolução open source desenvolvido por Kim2091 baseado na arquitetura ESRGAN, focado em ampliação e nitidez de imagens no estilo anime. Renomeado em fevereiro de 2022 a partir de “4x-TextSharpV1”, originalmente também aplicável a imagens de texto, mas com desempenho significativamente otimizado para conteúdo de anime."}, "Baichuan2-Turbo": {"description": "Utiliza tecnologia de busca aprimorada para conectar completamente o grande modelo com conhecimento de domínio e conhecimento da web. Suporta upload de vários documentos, como PDF e Word, e entrada de URLs, garantindo acesso a informações de forma rápida e abrangente, com resultados precisos e profissionais."}, "Baichuan3-Turbo": {"description": "Otimizado para cenários de alta frequência empresarial, com melhorias significativas de desempenho e excelente custo-benefício. Em comparação com o modelo Baichuan2, a criação de conteúdo aumentou em 20%, a resposta a perguntas de conhecimento em 17% e a capacidade de interpretação de papéis em 40%. O desempenho geral é superior ao do GPT-3.5."}, "Baichuan3-Turbo-128k": {"description": "Possui uma janela de contexto ultra longa de 128K, otimizada para cenários de alta frequência empresarial, com melhorias significativas de desempenho e excelente custo-benefício. Em comparação com o modelo Baichuan2, a criação de conteúdo aumentou em 20%, a resposta a perguntas de conhecimento em 17% e a capacidade de interpretação de papéis em 40%. O desempenho geral é superior ao do GPT-3.5."}, "Baichuan4": {"description": "O modelo é o melhor do país, superando modelos estrangeiros em tarefas em chinês, como enciclopédias, textos longos e criação de conteúdo. Também possui capacidades multimodais líderes na indústria, com desempenho excepcional em várias avaliações de referência."}, "Baichuan4-Air": {"description": "Modelo com a melhor capacidade do país, superando modelos estrangeiros em tarefas em chinês como enciclopédia, textos longos e criação de conteúdo. Também possui capacidades multimodais líderes da indústria, com excelente desempenho em várias avaliações de referência."}, "Baichuan4-Turbo": {"description": "Modelo com a melhor capacidade do país, superando modelos estrangeiros em tarefas em chinês como enciclopédia, textos longos e criação de conteúdo. Também possui capacidades multimodais líderes da indústria, com excelente desempenho em várias avaliações de referência."}, "DeepSeek-R1": {"description": "LLM eficiente de ponta, especializado em raciocínio, matemática e programação."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 — o modelo maior e mais inteligente do conjunto DeepSeek — foi destilado para a arquitetura Llama 70B. Com base em testes de benchmark e avaliações humanas, este modelo é mais inteligente do que o Llama 70B original, destacando-se especialmente em tarefas que exigem precisão matemática e factual."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Modelo de destilação DeepSeek-R1 baseado no Qwen2.5-Math-1.5B, otimizado para desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Modelo de destilação DeepSeek-R1 baseado no Qwen2.5-14B, otimizado para desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "A série DeepSeek-R1 otimiza o desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas, superando o nível do OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Modelo de destilação DeepSeek-R1 baseado no Qwen2.5-Math-7B, otimizado para desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas."}, "DeepSeek-V3": {"description": "DeepSeek-V3 é um modelo MoE desenvolvido internamente pela DeepSeek. Os resultados de várias avaliações do DeepSeek-V3 superaram outros modelos de código aberto, como Qwen2.5-72B e Llama-3.1-405B, e seu desempenho é comparável aos melhores modelos fechados do mundo, como GPT-4o e Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite oferece velocidade de resposta extrema e melhor custo-benef<PERSON><PERSON>, proporcionando opções mais flexíveis para diferentes cenários dos clientes. Suporta inferência e fine-tuning com janela de contexto de 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite oferece velocidade de resposta extrema e melhor custo-benef<PERSON><PERSON>, proporcionando opções mais flexíveis para diferentes cenários dos clientes. Suporta inferência e fine-tuning com janela de contexto de 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite oferece velocidade de resposta extrema e melhor custo-benef<PERSON><PERSON>, proporcionando opções mais flexíveis para diferentes cenários dos clientes. Suporta inferência e fine-tuning com janela de contexto de 4k."}, "Doubao-pro-128k": {"description": "Modelo principal com melhor desempenho, adequado para tarefas complexas, apresentando ótimos resultados em perguntas de referência, resumos, criação, classificação de texto, interpretação de papéis e outros cenários. Suporta inferência e fine-tuning com janela de contexto de 128k."}, "Doubao-pro-32k": {"description": "Modelo principal com melhor desempenho, adequado para tarefas complexas, apresentando ótimos resultados em perguntas de referência, resumos, criação, classificação de texto, interpretação de papéis e outros cenários. Suporta inferência e fine-tuning com janela de contexto de 32k."}, "Doubao-pro-4k": {"description": "Modelo principal com melhor desempenho, adequado para tarefas complexas, apresentando ótimos resultados em perguntas de referência, resumos, criação, classificação de texto, interpretação de papéis e outros cenários. Suporta inferência e fine-tuning com janela de contexto de 4k."}, "DreamO": {"description": "DreamO é um modelo open source de geração de imagens customizadas desenvolvido em parceria pela ByteDance e pela Universidade de Pequim, projetado para suportar geração multitarefa de imagens através de uma arquitetura unificada. Utiliza um método eficiente de modelagem combinada para gerar imagens altamente consistentes e personalizadas com base em múltiplas condições especificadas pelo usuário, como identidade, sujeito, estilo e fundo."}, "ERNIE-3.5-128K": {"description": "Modelo de linguagem de grande escala desenvolvido pela <PERSON>, cobrindo uma vasta quantidade de dados em chinês e inglês, com poderosas capacidades gerais, capaz de atender à maioria das demandas de perguntas e respostas em diálogos, geração de conteúdo e aplicações de plugins; suporta integração automática com o plugin de busca da Baidu, garantindo a atualidade das informações nas respostas."}, "ERNIE-3.5-8K": {"description": "Modelo de linguagem de grande escala desenvolvido pela <PERSON>, cobrindo uma vasta quantidade de dados em chinês e inglês, com poderosas capacidades gerais, capaz de atender à maioria das demandas de perguntas e respostas em diálogos, geração de conteúdo e aplicações de plugins; suporta integração automática com o plugin de busca da Baidu, garantindo a atualidade das informações nas respostas."}, "ERNIE-3.5-8K-Preview": {"description": "Modelo de linguagem de grande escala desenvolvido pela <PERSON>, cobrindo uma vasta quantidade de dados em chinês e inglês, com poderosas capacidades gerais, capaz de atender à maioria das demandas de perguntas e respostas em diálogos, geração de conteúdo e aplicações de plugins; suporta integração automática com o plugin de busca da Baidu, garantindo a atualidade das informações nas respostas."}, "ERNIE-4.0-8K-Latest": {"description": "Modelo de linguagem ultra grande escala desenvolvido pela <PERSON>du, que em comparação com o ERNIE 3.5, apresenta uma atualização completa nas capacidades do modelo, amplamente aplicável em cenários de tarefas complexas em diversas áreas; suporta integração automática com o plugin de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ERNIE-4.0-8K-Preview": {"description": "Modelo de linguagem ultra grande escala desenvolvido pela <PERSON>du, que em comparação com o ERNIE 3.5, apresenta uma atualização completa nas capacidades do modelo, amplamente aplicável em cenários de tarefas complexas em diversas áreas; suporta integração automática com o plugin de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Modelo de linguagem de última geração desenvolvido pela Baidu, com desempenho excepcional em uma ampla gama de cenários de tarefas complexas; suporta integração automática com plugins de busca da Baidu, garantindo a relevância da informação nas respostas. Supera o desempenho do ERNIE 4.0."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Modelo de linguagem ultra grande escala desenvolvido pela <PERSON>du, com desempenho excepcional em resultados gerais, amplamente aplicável em cenários de tarefas complexas em diversas áreas; suporta integração automática com o plugin de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas. Em comparação com o ERNIE 4.0, apresenta desempenho superior."}, "ERNIE-Character-8K": {"description": "Modelo de linguagem vertical desenvolvido p<PERSON>, adequado para aplicações como NPCs em jogos, diálogos de atendimento ao cliente e interpretação de personagens em diálogos, com estilos de personagem mais distintos e consistentes, maior capacidade de seguir instruções e desempenho de inferência superior."}, "ERNIE-Lite-Pro-128K": {"description": "Modelo de linguagem leve desenvolvido pela <PERSON>, que combina excelente desempenho do modelo com eficiência de inferência, apresentando resultados superiores ao ERNIE Lite, adequado para uso em inferência com placas de aceleração de IA de baixo poder computacional."}, "ERNIE-Speed-128K": {"description": "Modelo de linguagem de alto desempenho desenvolvido pela <PERSON>, lançado em 2024, com capacidades gerais excepcionais, adequado como modelo base para ajuste fino, melhorando o tratamento de problemas em cenários específicos, enquanto mantém excelente desempenho de inferência."}, "ERNIE-Speed-Pro-128K": {"description": "Modelo de linguagem de alto desempenho desenvolvido pela <PERSON>, lançado em 2024, com capacidades gerais excepcionais, apresentando resultados superiores ao ERNIE Speed, adequado como modelo base para ajuste fino, melhorando o tratamento de problemas em cenários específicos, enquanto mantém excelente desempenho de inferência."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev é um modelo multimodal de geração e edição de imagens desenvolvido pela Black Forest Labs, baseado na arquitetura Rectified Flow Transformer, com 12 bilhões de parâmetros, focado em gerar, reconstruir, aprimorar ou editar imagens sob condições contextuais fornecidas. Combina as vantagens da geração controlada de modelos de difusão com a capacidade de modelagem contextual dos Transformers, suportando saída de imagens de alta qualidade e aplicável a tarefas como restauração, preenchimento e reconstrução visual de cenas."}, "FLUX.1-dev": {"description": "FLUX.1-dev é um modelo multimodal de linguagem open source desenvolvido pela Black Forest Labs, otimizado para tarefas de texto e imagem, integrando capacidades de compreensão e geração de imagens e texto. Baseado em avançados modelos de linguagem como Mistral-7B, utiliza codificadores visuais cuidadosamente projetados e ajuste fino em múltiplas etapas para alcançar processamento colaborativo de texto e imagem e raciocínio complexo."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) é um modelo inovador, adequado para aplicações em múltiplas áreas e tarefas complexas."}, "HelloMeme": {"description": "HelloMeme é uma ferramenta de IA que gera automaticamente memes, GIFs ou vídeos curtos a partir de imagens ou ações fornecidas por você. Não requer habilidades de desenho ou programação; basta fornecer imagens de referência, e ela cria conteúdos visualmente atraentes, divertidos e com estilo consistente."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full é um grande modelo open source de edição multimodal de imagens lançado pela HiDream.ai, baseado na avançada arquitetura Diffusion Transformer e integrado com forte capacidade de compreensão linguística (incorporando LLaMA 3.1-8B-Instruct). Suporta geração de imagens, transferência de estilo, edição local e repintura de conteúdo via comandos em linguagem natural, com excelente compreensão e execução texto-imagem."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled é um modelo leve de geração de imagens a partir de texto, otimizado por destilação para gerar imagens de alta qualidade rapidamente, especialmente adequado para ambientes com recursos limitados e tarefas de geração em tempo real."}, "InstantCharacter": {"description": "InstantCharacter é um modelo de geração personalizada de personagens lançado pela equipe de IA da Tencent em 2025, que não requer ajuste fino (tuning-free), visando gerar personagens consistentes e de alta fidelidade em múltiplos cenários. Suporta modelagem de personagens a partir de uma única imagem de referência e permite transferir esses personagens para diversos estilos, ações e fundos de forma flexível."}, "InternVL2-8B": {"description": "InternVL2-8B é um poderoso modelo de linguagem visual, que suporta processamento multimodal de imagens e textos, capaz de identificar com precisão o conteúdo da imagem e gerar descrições ou respostas relevantes."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B é um poderoso modelo de linguagem visual, que suporta processamento multimodal de imagens e textos, capaz de identificar com precisão o conteúdo da imagem e gerar descrições ou respostas relevantes."}, "Kolors": {"description": "Kolors é um modelo de geração de imagens a partir de texto desenvolvido pela equipe <PERSON>s da Kuaishou. Treinado com bilhões de parâmetros, apresenta vantagens significativas em qualidade visual, compreensão semântica do chinês e renderização de texto."}, "Kwai-Kolors/Kolors": {"description": "Kolors é um modelo de geração de imagens a partir de texto em larga escala baseado em difusão latente, desenvolvido pela equipe <PERSON> da Kuaishou. Treinado com bilhões de pares texto-imagem, destaca-se na qualidade visual, precisão semântica complexa e renderização de caracteres em chinês e inglês. Suporta entrada em chinês e inglês, com desempenho excepcional na compreensão e geração de conteúdos específicos em chinês."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Capacidade de raciocínio de imagem excepcional em imagens de alta resolução, adequada para aplicações de compreensão visual."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Capacidade avançada de raciocínio de imagem para aplicações de agentes de compreensão visual."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Modelo de texto ajustado para instruções Llama 3.1, otimizado para casos de uso de diálogos multilíngues, apresentando desempenho superior em muitos modelos de chat de código aberto e fechados em benchmarks da indústria."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Modelo de texto ajustado para instruções Llama 3.1, otimizado para casos de uso de diálogos multilíngues, apresentando desempenho superior em muitos modelos de chat de código aberto e fechados em benchmarks da indústria."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Modelo de texto ajustado para instruções Llama 3.1, otimizado para casos de uso de diálogos multilíngues, apresentando desempenho superior em muitos modelos de chat de código aberto e fechados em benchmarks da indústria."}, "Meta-Llama-3.2-1B-Instruct": {"description": "Modelo de linguagem pequeno de ponta, com compreensão de linguagem, excelente capacidade de raciocínio e geração de texto."}, "Meta-Llama-3.2-3B-Instruct": {"description": "Modelo de linguagem pequeno de ponta, com compreensão de linguagem, excelente capacidade de raciocínio e geração de texto."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 é o modelo de linguagem de código aberto multilíngue mais avançado da série Llama, oferecendo desempenho comparável ao modelo de 405B a um custo extremamente baixo. Baseado na estrutura Transformer, e aprimorado por meio de ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para aumentar a utilidade e a segurança. Sua versão ajustada para instruções é otimizada para diálogos multilíngues, superando muitos modelos de chat de código aberto e fechados em vários benchmarks da indústria. A data limite de conhecimento é dezembro de 2023."}, "MiniMax-M1": {"description": "Modelo de inferência totalmente desenvolvido internamente. <PERSON>íder mund<PERSON>: 80K cadeias de pensamento x 1M de entradas, desempenho comparável aos melhores modelos internacionais."}, "MiniMax-Text-01": {"description": "Na série de modelos MiniMax-01, fizemos inovações ousadas: pela primeira vez, implementamos em larga escala um mecanismo de atenção linear, tornando a arquitetura Transformer tradicional não mais a única opção. Este modelo possui um total de 456 bilhões de parâmetros, com 45,9 bilhões ativados em uma única vez. O desempenho geral do modelo é comparável aos melhores modelos internacionais, enquanto lida eficientemente com contextos de até 4 milhões de tokens, 32 vezes mais que o GPT-4o e 20 vezes mais que o Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 é um modelo de inferência de atenção mista em grande escala com pesos abertos, possuindo 456 bilhões de parâmetros, com cerca de 45,9 bilhões de parâmetros ativados por token. O modelo suporta nativamente contextos ultra longos de 1 milhão de tokens e, graças ao mecanismo de atenção relâmpago, economiza 75% do custo computacional em operações de ponto flutuante em tarefas de geração com 100 mil tokens, em comparação com o DeepSeek R1. Além disso, MiniMax-M1 utiliza a arquitetura MoE (Mistura de Especialistas), combinando o algoritmo CISPO e um design eficiente de atenção mista para treinamento reforçado, alcançando desempenho líder na indústria em inferência de entradas longas e cenários reais de engenharia de software."}, "Moonshot-Kimi-K2-Instruct": {"description": "Com 1 trilhão de parâmetros totais e 32 bilhões de parâmetros ativados, este modelo não reflexivo alcança níveis de ponta em conhecimento avançado, matemática e codificação, sendo especialmente apto para tarefas gerais de agentes. Otimizado para tarefas de agentes, não apenas responde perguntas, mas também pode agir. Ideal para conversas improvisadas, experiências gerais de chat e agentes, funcionando como um modelo reflexivo sem necessidade de longos processos de pensamento."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) é um modelo de instrução de alta precisão, adequado para cálculos complexos."}, "OmniConsistency": {"description": "OmniConsistency melhora a consistência de estilo e a capacidade de generalização em tarefas de imagem para imagem (Image-to-Image) ao introduzir grandes Diffusion Transformers (DiTs) e dados estilizados pareados, evitando a degradação do estilo."}, "Phi-3-medium-128k-instruct": {"description": "Mesmo modelo Phi-3-medium, mas com um tamanho de contexto maior para RAG ou prompting de poucos exemplos."}, "Phi-3-medium-4k-instruct": {"description": "Um modelo de 14B parâmetros, que apresenta melhor qualidade do que o Phi-3-mini, com foco em dados densos de raciocínio de alta qualidade."}, "Phi-3-mini-128k-instruct": {"description": "Mesmo modelo Phi-3-mini, mas com um tamanho de contexto maior para RAG ou prompting de poucos exemplos."}, "Phi-3-mini-4k-instruct": {"description": "O menor membro da família Phi-3. <PERSON><PERSON><PERSON><PERSON><PERSON> tanto para qualidade quanto para baixa latência."}, "Phi-3-small-128k-instruct": {"description": "Mesmo modelo Phi-3-small, mas com um tamanho de contexto maior para RAG ou prompting de poucos exemplos."}, "Phi-3-small-8k-instruct": {"description": "Um modelo de 7B parâmetros, que apresenta melhor qualidade do que o Phi-3-mini, com foco em dados densos de raciocínio de alta qualidade."}, "Phi-3.5-mini-instruct": {"description": "Versão atualizada do modelo Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "Versão atualizada do modelo Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct é um modelo de linguagem de grande escala com ajuste fino para instruções na série Qwen2, com um tamanho de parâmetro de 7B. Este modelo é baseado na arquitetura Transformer, utilizando funções de ativação SwiGLU, viés de atenção QKV e atenção de consulta em grupo. Ele é capaz de lidar com entradas em larga escala. O modelo se destaca em compreensão de linguagem, geração, capacidade multilíngue, codificação, matemática e raciocínio em vários benchmarks, superando a maioria dos modelos de código aberto e demonstrando competitividade comparável a modelos proprietários em algumas tarefas. O Qwen2-7B-Instruct superou o Qwen1.5-7B-Chat em várias avaliações, mostrando melhorias significativas de desempenho."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct é um dos mais recentes modelos de linguagem de grande escala lançados pela Alibaba Cloud. Este modelo de 7B apresenta melhorias significativas em áreas como codificação e matemática. O modelo também oferece suporte multilíngue, abrangendo mais de 29 idiomas, incluindo chinês e inglês. O modelo teve melhorias significativas em seguir instruções, entender dados estruturados e gerar saídas estruturadas (especialmente JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct é a versão mais recente da série de modelos de linguagem de grande escala específicos para código lançada pela Alibaba Cloud. Este modelo, baseado no Qwen2.5, foi treinado com 55 trilhões de tokens, melhorando significativamente a capacidade de geração, raciocínio e correção de código. Ele não apenas aprimora a capacidade de codificação, mas também mantém as vantagens em matemática e habilidades gerais. O modelo fornece uma base mais abrangente para aplicações práticas, como agentes de código."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL é o novo membro da série Qwen, com capacidades avançadas de compreensão visual. Ele pode analisar textos, gráficos e layouts em imagens, compreender vídeos longos e capturar eventos. Capaz de realizar racioc<PERSON>ios, manipular ferramentas, suporta localização de objetos em múltiplos formatos e geração de saídas estruturadas. Otimiza a compreensão de vídeos através de treinamento com resolução dinâmica e taxa de quadros, além de melhorar a eficiência do codificador visual."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking é um modelo de linguagem visual (VLM) de código aberto lançado em conjunto pela Zhipu AI e pelo Laboratório KEG da Universidade de Tsinghua, projetado para lidar com tarefas cognitivas multimodais complexas. Este modelo é baseado no modelo base GLM-4-9B-0414 e melhora significativamente sua capacidade e estabilidade de raciocínio multimodal ao introduzir o mecanismo de raciocínio \"Chain-of-Thought\" (Cadeia de Pensamento) e adotar estratégias de aprendizado por reforço."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat é a versão de código aberto da série de modelos pré-treinados GLM-4 lançada pela Zhipu AI. Este modelo se destaca em semântica, matemática, raciocínio, código e conhecimento. Além de suportar diálogos de múltiplas rodadas, o GLM-4-9B-Chat também possui recursos avançados como navegação na web, execução de código, chamadas de ferramentas personalizadas (Function Call) e raciocínio de longo texto. O modelo suporta 26 idiomas, incluindo chinês, ingl<PERSON>s, japonês, coreano e alemão. Em vários benchmarks, o GLM-4-9B-Chat demonstrou desempenho excepcional, como AlignBench-v2, MT-Bench, MMLU e C-Eval. O modelo suporta um comprimento de contexto máximo de 128K, adequado para pesquisa acadêmica e aplicações comerciais."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 é um modelo de inferência impulsionado por aprendizado por reforço (RL), que resolve problemas de repetitividade e legibilidade no modelo. Antes do RL, o DeepSeek-R1 introduziu dados de inicialização a frio, otimizando ainda mais o desempenho de inferência. Ele se compara ao OpenAI-o1 em tarefas matemáticas, de código e de inferência, e melhora o desempenho geral por meio de métodos de treinamento cuidadosamente projetados."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-7B é um modelo obtido por destilação de conhecimento baseado no Qwen2.5-Math-7B. Este modelo foi refinado usando 800 mil amostras selecionadas geradas pelo DeepSeek-R1, demonstrando excelente capacidade de raciocínio. Apresenta desempenho destacado em diversos benchmarks, alcançando 92,8% de precisão no MATH-500, 55,5% de taxa de aprovação no AIME 2024 e uma pontuação de 1189 no CodeForces, mostrando forte competência em matemática e programação para um modelo de escala 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 é um modelo de linguagem com 671 bilhões de parâmetros, utilizando uma arquitetura de especialistas mistos (MoE) com atenção potencial de múltiplas cabeças (MLA) e uma estratégia de balanceamento de carga sem perda auxiliar, otimizando a eficiência de inferência e treinamento. Pré-treinado em 14,8 trilhões de tokens de alta qualidade, e ajustado por supervisão e aprendizado por reforço, o DeepSeek-V3 supera outros modelos de código aberto, aproximando-se de modelos fechados líderes."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 é um modelo base com arquitetura MoE e capacidades avançadas de código e agente, com 1 trilhão de parâmetros totais e 32 bilhões ativados. Em testes de desempenho em raciocínio geral, programação, matemática e agentes, o modelo K2 supera outros modelos open source populares."}, "QwQ-32B-Preview": {"description": "O QwQ-32B-Preview é um modelo de processamento de linguagem natural inovador, capaz de lidar eficientemente com tarefas complexas de geração de diálogos e compreensão de contexto."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview é um modelo de pesquisa desenvolvido pela equipe <PERSON>, focado em capacidades de raciocínio visual, apresentando vantagens únicas na compreensão de cenários complexos e na resolução de problemas matemáticos relacionados à visão."}, "Qwen/QwQ-32B": {"description": "QwQ é o modelo de inferência da série Qwen. Em comparação com modelos tradicionais de ajuste de instruções, o QwQ possui habilidades de raciocínio e inferência, permitindo um desempenho significativamente melhorado em tarefas de downstream, especialmente na resolução de problemas difíceis. O QwQ-32B é um modelo de inferência de médio porte, capaz de obter um desempenho competitivo em comparação com modelos de inferência de ponta, como DeepSeek-R1 e o1-mini. Este modelo utiliza tecnologias como RoPE, SwiGLU, RMSNorm e viés de atenção QKV, apresentando uma estrutura de rede de 64 camadas e 40 cabeças de atenção Q (sendo KV 8 no GQA)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview é o mais recente modelo de pesquisa experimental da Qwen, focado em melhorar a capacidade de raciocínio da IA. Ao explorar mecanismos complexos como mistura de linguagem e raciocínio recursivo, suas principais vantagens incluem forte capacidade de análise de raciocínio, habilidades matemáticas e de programação. Ao mesmo tempo, existem questões de troca de linguagem, ciclos de raciocínio, considerações de segurança e diferenças em outras capacidades."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 é um modelo de linguagem universal avançado, suportando diversos tipos de instruções."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct é um modelo de linguagem de grande escala com ajuste fino para instruções na série Qwen2, com um tamanho de parâmetro de 72B. Este modelo é baseado na arquitetura Transformer, utilizando funções de ativação SwiGLU, viés de atenção QKV e atenção de consulta em grupo. Ele é capaz de lidar com entradas em larga escala. O modelo se destaca em compreensão de linguagem, geração, capacidade multilíngue, codificação, matemática e raciocínio em vários benchmarks, superando a maioria dos modelos de código aberto e demonstrando competitividade comparável a modelos proprietários em algumas tarefas."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL é a versão mais recente do modelo Qwen-VL, alcançando desempenho de ponta em testes de compreensão visual."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 é uma nova série de modelos de linguagem em larga escala, projetada para otimizar o processamento de tarefas instrucionais."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 é uma nova série de modelos de linguagem em larga escala, projetada para otimizar o processamento de tarefas instrucionais."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Modelo de linguagem de grande escala desenvolvido pela equipe <PERSON> Alibaba <PERSON>."}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 é uma nova série de grandes modelos de linguagem, com capacidades de compreensão e geração aprimoradas."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 é uma nova série de grandes modelos de linguagem, projetada para otimizar o processamento de tarefas instrucionais."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 é uma nova série de modelos de linguagem em larga escala, projetada para otimizar o processamento de tarefas instrucionais."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 é uma nova série de grandes modelos de linguagem, projetada para otimizar o processamento de tarefas instrucionais."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder foca na escrita de código."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct é a versão mais recente da série de modelos de linguagem de grande escala específicos para código lançada pela Alibaba Cloud. Este modelo, baseado no Qwen2.5, foi treinado com 55 trilhões de tokens, melhorando significativamente a capacidade de geração, raciocínio e correção de código. Ele não apenas aprimora a capacidade de codificação, mas também mantém as vantagens em matemática e habilidades gerais. O modelo fornece uma base mais abrangente para aplicações práticas, como agentes de código."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct é um modelo multimodal de grande escala desenvolvido pela equipe <PERSON>, parte da série Qwen2.5-VL. Este modelo não apenas domina o reconhecimento de objetos comuns, mas também pode analisar textos, gr<PERSON><PERSON><PERSON>, ícon<PERSON>, diagramas e layouts em imagens. Ele funciona como um agente visual inteligente, capaz de raciocinar e manipular ferramentas dinamicamente, com habilidades para operar computadores e smartphones. Além disso, o modelo pode localizar objetos em imagens com precisão e gerar saídas estruturadas para documentos como faturas e tabelas. Em comparação com a versão anterior Qwen2-VL, esta versão apresenta melhorias significativas em habilidades matemáticas e de resolução de problemas através de aprendizado por reforço, com um estilo de resposta mais alinhado às preferências humanas."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL é o modelo de linguagem visual da série Qwen2.5. Este modelo apresenta melhorias significativas em vários aspectos: possui capacidade aprimorada de compreensão visual, podendo reconhecer objetos comuns, analisar textos, gráficos e layouts; atua como um agente visual capaz de raciocinar e orientar dinamicamente o uso de ferramentas; suporta a compreensão de vídeos longos com mais de 1 hora de duração, capturando eventos-chave; pode localizar objetos em imagens com precisão através da geração de caixas delimitadoras ou pontos; suporta a geração de saídas estruturadas, sendo especialmente útil para dados digitalizados como faturas e tabelas."}, "Qwen/Qwen3-14B": {"description": "O Qwen3 é um novo modelo de grande escala da Tongyi Qianwen com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, tare<PERSON><PERSON> gera<PERSON>, agentes e multilinguismo, e suporta a alternância de modos de pensamento."}, "Qwen/Qwen3-235B-A22B": {"description": "O Qwen3 é um novo modelo de grande escala da Tongyi Qianwen com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, tare<PERSON><PERSON> gera<PERSON>, agentes e multilinguismo, e suporta a alternância de modos de pensamento."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 é um modelo de linguagem grande híbrido especialista (MoE) flagship da série Qwen3, desenvolvido pela equipe Tongyi Qianwen da Alibaba Cloud. Com 235 bilhões de parâmetros totais e 22 bilhões ativados por inferência, é uma versão atualizada do modo não reflexivo Qwen3-235B-A22B, focada em melhorias significativas em seguimento de instruções, raciocínio lógico, compreensão textual, matemática, ciência, programação e uso de ferramentas. Além disso, amplia a cobertura de conhecimento multilíngue e alinha melhor as preferências do usuário em tarefas subjetivas e abertas para gerar textos mais úteis e de alta qualidade."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 é um modelo de linguagem grande da série Qwen3, desenvolvido pela equipe <PERSON>, focado em tarefas complexas de raciocínio avançado. Baseado em arquitetura MoE, possui 235 bilhões de parâmetros totais, ativando cerca de 22 bilhões por token, equilibrando alta performance e eficiência computacional. Como modelo dedicado ao “pensamento”, apresenta melhorias notáveis em raciocínio lógico, matemática, ciência, programação e benchmarks acadêmicos, alcançando o topo entre modelos open source reflexivos. Também aprimora capacidades gerais como seguimento de instruções, uso de ferramentas e geração de texto, com suporte nativo para contexto longo de 256K tokens, ideal para cenários que exigem raciocínio profundo e processamento de documentos extensos."}, "Qwen/Qwen3-30B-A3B": {"description": "O Qwen3 é um novo modelo de grande escala da Tongyi Qianwen com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, tare<PERSON><PERSON> gera<PERSON>, agentes e multilinguismo, e suporta a alternância de modos de pensamento."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 é uma versão atualizada do Qwen3-30B-A3B no modo não reflexivo. Este é um modelo de especialista misto (MoE) com um total de 30,5 bilhões de parâmetros e 3,3 bilhões de parâmetros ativados. O modelo apresenta melhorias significativas em vários aspectos, incluindo um aumento notável na capacidade de seguir instruções, raciocínio lógico, compreensão de texto, matemática, ciências, codificação e uso de ferramentas. Além disso, alcança avanços substanciais na cobertura de conhecimento em múltiplos idiomas e melhor alinhamento com as preferências dos usuários em tarefas subjetivas e abertas, permitindo gerar respostas mais úteis e textos de maior qualidade. A capacidade de compreensão de textos longos também foi ampliada para 256K. Este modelo suporta apenas o modo não reflexivo e não gera tags `<think></think>` em sua saída."}, "Qwen/Qwen3-32B": {"description": "O Qwen3 é um novo modelo de grande escala da Tongyi Qianwen com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, tare<PERSON><PERSON> gera<PERSON>, agentes e multilinguismo, e suporta a alternância de modos de pensamento."}, "Qwen/Qwen3-8B": {"description": "O Qwen3 é um novo modelo de grande escala da Tongyi Qianwen com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, tare<PERSON><PERSON> gera<PERSON>, agentes e multilinguismo, e suporta a alternância de modos de pensamento."}, "Qwen2-72B-Instruct": {"description": "Qwen2 é a mais recente série do modelo Qwen, suportando 128k de contexto. Em comparação com os melhores modelos de código aberto atua<PERSON>, o Qwen2-72B supera significativamente os modelos líderes em várias capacidades, incluindo compreensão de linguagem natural, conhecimento, código, matemática e multilinguismo."}, "Qwen2-7B-Instruct": {"description": "Qwen2 é a mais recente série do modelo Qwen, capaz de superar modelos de código aberto de tamanho equivalente e até mesmo modelos de maior escala. O Qwen2 7B obteve vantagens significativas em várias avaliações, especialmente em compreensão de código e chinês."}, "Qwen2-VL-72B": {"description": "O Qwen2-VL-72B é um poderoso modelo de linguagem visual, que suporta processamento multimodal de imagens e texto, capaz de reconhecer com precisão o conteúdo das imagens e gerar descrições ou respostas relacionadas."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct é um grande modelo de linguagem com 14 bilhões de parâmetros, com desempenho excelente, otimizado para cenários em chinês e multilíngues, suportando aplicações como perguntas e respostas inteligentes e geração de conteúdo."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct é um grande modelo de linguagem com 32 bilhões de parâmetros, com desempenho equilibrado, otimizado para cenários em chinês e multilíngues, suportando aplicações como perguntas e respostas inteligentes e geração de conteúdo."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct suporta 16k de contexto, gerando textos longos com mais de 8K. Suporta chamadas de função e interação sem costura com sistemas externos, aumentando significativamente a flexibilidade e escalabilidade. O conhecimento do modelo aumentou consideravelmente, e suas habilidades em codificação e matemática melhoraram muito, com suporte a mais de 29 idiomas."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct é um grande modelo de linguagem com 7 bilhões de parâmetros, que suporta chamadas de função e interação sem costura com sistemas externos, aumentando significativamente a flexibilidade e escalabilidade. Otimizado para cenários em chinês e multilíngues, suporta aplicações como perguntas e respostas inteligentes e geração de conteúdo."}, "Qwen2.5-Coder-14B-Instruct": {"description": "O Qwen2.5-Coder-14B-Instruct é um modelo de instrução de programação baseado em pré-treinamento em larga escala, com forte capacidade de compreensão e geração de código, capaz de lidar eficientemente com diversas tarefas de programação, especialmente adequado para escrita inteligente de código, geração de scripts automatizados e resolução de problemas de programação."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct é um grande modelo de linguagem projetado para geração de código, compreensão de código e cenários de desenvolvimento eficiente, com uma escala de 32 bilhões de parâmetros, atendendo a diversas necessidades de programação."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B, model<PERSON> (especialista misto), introduz o “modo de raciocínio hí<PERSON>, permitindo aos usuários alternar perfeitamente entre os modos “reflexivo” e “não reflexivo”. Suporta compreensão e raciocínio em 119 idiomas e dialetos, além de possuir forte capacidade de chamada de ferramentas. Em testes de benchmark abrangentes, incluindo habilidades gerais, código, matemática, multilinguismo, conhecimento e raciocínio, compete com os principais grandes modelos do mercado, como DeepSeek R1, OpenAI o1, o3-mini, Grok 3 e Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B, <PERSON><PERSON> <PERSON> (Dense Model), introduz o “modo de raciocín<PERSON>, permitindo aos usuários alternar perfeitamente entre os modos “reflexivo” e “não reflexivo”. Graças a melhorias na arquitetura do modelo, aumento dos dados de treinamento e métodos de treinamento mais eficazes, seu desempenho geral é comparável ao do Qwen2.5-72B."}, "SenseChat": {"description": "Modelo da versão básica (V4), com comprimento de contexto de 4K, com capacidades gerais poderosas."}, "SenseChat-128K": {"description": "Modelo da versão básica (V4), com comprimento de contexto de 128K, se destaca em tarefas de compreensão e geração de textos longos."}, "SenseChat-32K": {"description": "Modelo da versão básica (V4), com comprimento de contexto de 32K, aplicável de forma flexível em diversos cenários."}, "SenseChat-5": {"description": "<PERSON>o da versão mais recente (V5.5), com comprimento de contexto de 128K, com capacidades significativamente aprimoradas em racioc<PERSON>io <PERSON>, diálogos em inglês, seguimento de instruções e compreensão de textos longos, rivalizando com o GPT-4o."}, "SenseChat-5-1202": {"description": "Baseado na versão mais recente V5.5, apresenta melhorias significativas em várias dimensões, incluindo habilidades básicas em chinês e inglês, conversação, conhecimento científico, conhecimento humanístico, escrita, lógica matemática e controle de contagem de palavras."}, "SenseChat-5-Cantonese": {"description": "Comprimento de contexto de 32K, superando o GPT-4 na compreensão de diálogos em cantonês, competindo com o GPT-4 Turbo em várias áreas, incluindo conhecimento, raciocínio, matemática e programação."}, "SenseChat-5-beta": {"description": "Desempenho superior em alguns aspectos em relação ao SenseCat-5-1202"}, "SenseChat-Character": {"description": "<PERSON><PERSON>, com comprimento de contexto de 8K, alta velocidade de resposta."}, "SenseChat-Character-Pro": {"description": "<PERSON><PERSON> a<PERSON>, com comprimento de contexto de 32K, com capacidades amplamente aprimoradas, suportando diálogos em chinês e inglês."}, "SenseChat-Turbo": {"description": "Adequado para perguntas rápidas e cenários de ajuste fino do modelo."}, "SenseChat-Turbo-1202": {"description": "É a versão mais recente do modelo leve, alcançando mais de 90% da capacidade do modelo completo, reduzindo significativamente o custo de inferência."}, "SenseChat-Vision": {"description": "<PERSON>o da versão mais recente (V5.5), suporta entrada de múltiplas imagens, o<PERSON><PERSON><PERSON><PERSON> completam<PERSON> as capacidades básicas do modelo, com grandes melhorias em reconhecimento de atributos de objetos, relações espaciais, reconhecimento de eventos, compreensão de cenários, reconhecimento de emoções, raciocínio lógico e compreensão e geração de texto."}, "SenseNova-V6-5-Pro": {"description": "Com atualizações abrangentes em dados multimodais, linguísticos e de raciocínio, além da otimização das estratégias de treinamento, o novo modelo alcança melhorias significativas em raciocínio multimodal e capacidade de seguir instruções generalizadas. Suporta janelas de contexto de até 128k e apresenta desempenho excepcional em tarefas especializadas como OCR e reconhecimento de IPs culturais e turísticos."}, "SenseNova-V6-5-Turbo": {"description": "Com atualizações abrangentes em dados multimodais, linguísticos e de raciocínio, além da otimização das estratégias de treinamento, o novo modelo alcança melhorias significativas em raciocínio multimodal e capacidade de seguir instruções generalizadas. Suporta janelas de contexto de até 128k e apresenta desempenho excepcional em tarefas especializadas como OCR e reconhecimento de IPs culturais e turísticos."}, "SenseNova-V6-Pro": {"description": "Realizar a unificação nativa de capacidades de imagem, texto e vídeo, superando as limitações tradicionais da multimodalidade discreta, conquistando o título duplo nas avaliações OpenCompass e SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "Equilibrar raciocínio visual e linguístico profundo, realizando um pensamento lento e uma inferência profunda, apresentando um processo completo de cadeia de pensamento."}, "SenseNova-V6-Turbo": {"description": "Realizar a unificação nativa de capacidades de imagem, texto e vídeo, superando as limitações tradicionais da multimodalidade discreta, liderando amplamente em dimensões centrais como capacidades básicas multimodais e linguísticas, combinando rigor acadêmico e prático, e alcançando repetidamente o nível da primeira divisão em várias avaliações nacionais e internacionais."}, "Skylark2-lite-8k": {"description": "Modelo de segunda geração Skylark, o modelo Skylark2-lite possui alta velocidade de resposta, adequado para cenários que exigem alta capacidade de resposta, sensíveis ao custo e com baixa exigência de precisão do modelo, com uma janela de contexto de 8k."}, "Skylark2-pro-32k": {"description": "Modelo de segunda geração Skylark, a versão Skylark2-pro possui alta precisão, adequada para cenários de geração de texto mais complexos, como geração de textos em campos especializados, criação de romances e traduções de alta qualidade, com uma janela de contexto de 32k."}, "Skylark2-pro-4k": {"description": "Modelo de segunda gera<PERSON> Skylark, o modelo Skylark2-pro possui alta precisão, adequado para cenários de geração de texto mais complexos, como geração de textos em campos especializados, criação de romances e traduções de alta qualidade, com uma janela de contexto de 4k."}, "Skylark2-pro-character-4k": {"description": "Modelo de segunda gera<PERSON>rk, o modelo Skylark2-pro-character possui excelentes habilidades de interpretação de papéis e chat, especializado em interpretar diferentes papéis com base nas solicitações do usuário e engajar em conversas, apresentando um estilo de personagem distinto e um conteúdo de diálogo natural e fluído, adequado para construir chatbots, assistentes virtuais e atendimento ao cliente online, com alta velocidade de resposta."}, "Skylark2-pro-turbo-8k": {"description": "Modelo de segunda geração <PERSON>rk, o Skylark2-pro-turbo-8k proporciona raciocínio mais rápido e menor custo, com uma janela de contexto de 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 é a nova geração de modelo de código aberto da série GLM, com 32 bilhões de parâmetros. O desempenho deste modelo é comparável ao da série GPT da OpenAI e da série V3/R1 da DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 é um modelo compacto da série GLM, com 9 bilhões de parâmetros. Este modelo herda as características técnicas da série GLM-4-32B, mas oferece uma opção de implantação mais leve. Apesar de seu tamanho menor, o GLM-4-9B-0414 ainda demonstra habilidades excepcionais em tarefas de geração de código, design de páginas da web, geração de gráficos SVG e redação baseada em pesquisa."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking é um modelo de linguagem visual (VLM) de código aberto lançado em conjunto pela Zhipu AI e pelo Laboratório KEG da Universidade de Tsinghua, projetado para lidar com tarefas cognitivas multimodais complexas. Este modelo é baseado no modelo base GLM-4-9B-0414 e melhora significativamente sua capacidade e estabilidade de raciocínio multimodal ao introduzir o mecanismo de raciocínio \"Chain-of-Thought\" (Cadeia de Pensamento) e adotar estratégias de aprendizado por reforço."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 é um modelo de inferência com capacidade de pensamento profundo. Este modelo é baseado no GLM-4-32B-0414, desenvolvido através de inicialização a frio e aprendizado por reforço expandido, e foi treinado adicionalmente em tarefas de matemática, código e lógica. Em comparação com o modelo base, o GLM-Z1-32B-0414 melhorou significativamente suas habilidades matemáticas e capacidade de resolver tarefas complexas."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 é um modelo compacto da série GLM, com apenas 9 bilhões de parâmetros, mas mantendo a tradição de código aberto enquanto exibe habilidades impressionantes. Apesar de seu tamanho menor, este modelo ainda se destaca em raciocínio matemático e tarefas gerais, com desempenho geral em nível de liderança entre modelos de código aberto de tamanho semelhante."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 é um modelo de inferência profunda com capacidade de reflexão (comparável ao Deep Research da OpenAI). Diferente dos modelos típicos de pensamento profundo, o modelo de reflexão utiliza um tempo mais longo de pensamento profundo para resolver problemas mais abertos e complexos."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B é uma versão de código aberto, oferecendo uma experiência de diálogo otimizada para aplicações de conversa."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B é o primeiro modelo de raciocínio de grande escala com contexto longo treinado por aprendizado por reforço (LRM), otimizado para tarefas de raciocínio em textos longos. O modelo utiliza um framework de aprendizado por reforço com expansão progressiva de contexto, permitindo uma transição estável de contextos curtos para longos. Em sete benchmarks de perguntas e respostas com documentos de contexto longo, QwenLong-L1-32B supera modelos líderes como OpenAI-o3-mini e Qwen3-235B-A22B, com desempenho comparável ao Claude-3.7-Sonnet-Thinking. É especialmente eficaz em raciocínio matemático, lógico e raciocínio de múltiplos saltos."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, man<PERSON><PERSON> as excelentes habilidades linguísticas do modelo original, aumentou significativamente suas capacidades de lógica matemática e codificação através de treinamento incremental com 500 bilhões de tokens de alta qualidade."}, "abab5.5-chat": {"description": "Voltado para cenários de produtividade, suportando o processamento de tarefas complexas e geração de texto eficiente, adequado para aplicações em áreas profissionais."}, "abab5.5s-chat": {"description": "Projetado para cenários de diálogo de personagens em chinês, oferecendo capacidade de geração de diálogos de alta qualidade em chinês, adequado para várias aplicações."}, "abab6.5g-chat": {"description": "Projetado para diálogos de personagens multilíngues, suportando geração de diálogos de alta qualidade em inglês e várias outras línguas."}, "abab6.5s-chat": {"description": "Adequado para uma ampla gama de tarefas de processamento de linguagem natural, incluindo geração de texto, sistemas de diálogo, etc."}, "abab6.5t-chat": {"description": "Otimizado para cenários de diálogo de personagens em chinês, oferecendo capacidade de geração de diálogos fluentes e que respeitam os hábitos de expressão em chinês."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 é um modelo de linguagem grande de última geração, otimizado com aprendizado por reforço e dados de inicialização a frio, apresentando desempenho excepcional em raciocínio, matemática e programação."}, "accounts/fireworks/models/deepseek-v3": {"description": "Modelo de linguagem poderoso da Deepseek, baseado em Mixture-of-Experts (MoE), com um total de 671B de parâmetros, ativando 37B de parâmetros por token."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "O modelo Llama 3 70B Instruct é otimizado para diálogos multilíngues e compreensão de linguagem natural, superando a maioria dos modelos concorrentes."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "O modelo Llama 3 8B Instruct é otimizado para diálogos e tarefas multilíngues, apresentando desempenho excepcional e eficiência."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "O modelo Llama 3 8B Instruct (versão HF) é consistente com os resultados da implementação oficial, apresentando alta consistência e compatibilidade entre plataformas."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "O modelo Llama 3.1 405B Instruct possui parâmetros em escala extremamente grande, adequado para seguimento de instruções em tarefas complexas e cenários de alta carga."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "O modelo Llama 3.1 70B Instruct oferece excelente compreensão e geração de linguagem natural, sendo a escolha ideal para tarefas de diálogo e análise."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "O modelo Llama 3.1 8B Instruct é otimizado para diálogos multilíngues, superando a maioria dos modelos de código aberto e fechado em benchmarks do setor."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Modelo de raciocínio visual de 11B parâmetros da Meta, otimizado para reconhecimento visual, raciocínio visual, descrição de imagens e resposta a perguntas gerais sobre imagens. Este modelo é capaz de entender dados visuais, como gráficos e diagramas, e preencher a lacuna entre visão e linguagem gerando descrições textuais dos detalhes das imagens."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "O modelo de instrução Llama 3.2 3B é um modelo multilíngue leve lançado pela Meta. Este modelo visa aumentar a eficiência, oferecendo melhorias significativas em latência e custo em comparação com modelos maiores. Exemplos de uso incluem consultas, reescrita de prompts e auxílio na redação."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Modelo de raciocínio visual de 90B parâmetros da Meta, otimizado para reconhecimento visual, raciocínio visual, descrição de imagens e resposta a perguntas gerais sobre imagens. Este modelo é capaz de entender dados visuais, como gráficos e diagramas, e preencher a lacuna entre visão e linguagem gerando descrições textuais dos detalhes das imagens."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct é a versão atualizada de dezembro do Llama 3.1 70B. Este modelo foi aprimorado com base no Llama 3.1 70B (lançado em julho de 2024), melhorando a chamada de ferramentas, suporte a textos multilíngues, habilidades matemáticas e de programação. O modelo alcançou níveis de liderança da indústria em raciocínio, matemática e seguimento de instruções, e é capaz de oferecer desempenho semelhante ao 3.1 405B, ao mesmo tempo em que apresenta vantagens significativas em velocidade e custo."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Modelo com 24B de parâmetros, com capacidades de ponta comparáveis a modelos maiores."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "O modelo Mixtral MoE 8x22B Instruct, com parâmetros em grande escala e arquitetura de múltiplos especialistas, suporta o processamento eficiente de tarefas complexas."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "O modelo Mixtral MoE 8x7B Instruct, com uma arquitetura de múltiplos especialistas, oferece seguimento e execução de instruções de forma eficiente."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "O modelo MythoMax L2 13B combina novas técnicas de fusão, sendo especializado em narrativas e interpretação de personagens."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "O modelo Phi 3 Vision Instruct é um modelo multimodal leve, capaz de processar informações visuais e textuais complexas, com forte capacidade de raciocínio."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "O modelo QwQ é um modelo de pesquisa experimental desenvolvido pela equipe <PERSON>, focado em aprimorar a capacidade de raciocínio da IA."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "A versão 72B do modelo Qwen-VL é o resultado da mais recente iteração da Alibaba, representando quase um ano de inovações."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 é uma série de modelos de linguagem com apenas decodificadores, desenvolvida pela equipe Qwen da Alibaba Cloud. Estes modelos têm tamanhos variados, incluindo 0.5B, 1.5B, 3B, 7B, 14B, 32B e 72B, com variantes base (base) e de instrução (instruct)."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct é a versão mais recente da série de modelos de linguagem de grande escala específicos para código lançada pela Alibaba Cloud. Este modelo, baseado no Qwen2.5, foi treinado com 55 trilhões de tokens, melhorando significativamente a capacidade de geração, raciocínio e correção de código. Ele não apenas aprimora a capacidade de codificação, mas também mantém as vantagens em matemática e habilidades gerais. O modelo fornece uma base mais abrangente para aplicações práticas, como agentes de código."}, "accounts/yi-01-ai/models/yi-large": {"description": "O modelo Yi-Large oferece excelente capacidade de processamento multilíngue, adequado para diversas tarefas de geração e compreensão de linguagem."}, "ai21-jamba-1.5-large": {"description": "Um modelo multilíngue com 398B de parâmetros (94B ativos), oferecendo uma janela de contexto longa de 256K, chamada de função, saída estruturada e geração fundamentada."}, "ai21-jamba-1.5-mini": {"description": "Um modelo multilíngue com 52B de parâmetros (12B ativos), oferecendo uma janela de contexto longa de 256K, chamada de função, saída estruturada e geração fundamentada."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Um modelo multilíngue com 398 bilhões de parâmetros (94 bilhões ativos), oferecendo janela de contexto longa de 256K, chamadas de função, saída estruturada e geração baseada em fatos."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Um modelo multilíngue com 52 bilhões de parâmetros (12 bilhões ativos), oferecendo janela de contexto longa de 256K, chamadas de função, saída estruturada e geração baseada em fatos."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "O Claude 3.5 Sonnet eleva o padrão da indústria, superando modelos concorrentes e o Claude 3 Opus, apresentando um desempenho excepcional em avaliações amplas, ao mesmo tempo que mantém a velocidade e o custo de nossos modelos de nível médio."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet elevou o padrão da indústria, superando modelos concorrentes e o Claude 3 Opus, apresentando um desempenho excepcional em avaliações amplas, enquanto mantém a velocidade e o custo de nossos modelos de nível médio."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "O Claude 3 Haiku é o modelo mais rápido e compacto da Anthropic, oferecendo uma velocidade de resposta quase instantânea. Ele pode responder rapidamente a consultas e solicitações simples. Os clientes poderão construir uma experiência de IA sem costura que imita a interação humana. O Claude 3 Haiku pode processar imagens e retornar saídas de texto, com uma janela de contexto de 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "O Claude 3 Opus é o modelo de IA mais poderoso da Anthropic, com desempenho de ponta em tarefas altamente complexas. Ele pode lidar com prompts abertos e cenários não vistos, apresentando fluência excepcional e compreensão semelhante à humana. O Claude 3 Opus demonstra as possibilidades de geração de IA na vanguarda. O Claude 3 Opus pode processar imagens e retornar saídas de texto, com uma janela de contexto de 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "O Claude 3 Sonnet da Anthropic alcança um equilíbrio ideal entre inteligência e velocidade — especialmente adequado para cargas de trabalho empresariais. Ele oferece a máxima utilidade a um custo inferior ao dos concorrentes e foi projetado para ser um modelo confiável e durável, adequado para implantações de IA em larga escala. O Claude 3 Sonnet pode processar imagens e retornar saídas de texto, com uma janela de contexto de 200K."}, "anthropic.claude-instant-v1": {"description": "Um modelo rápido, econômico e ainda muito capaz, capaz de lidar com uma variedade de tarefas, incluindo diálogos cotidianos, aná<PERSON>e de texto, resumos e perguntas e respostas de documentos."}, "anthropic.claude-v2": {"description": "O modelo da Anthropic demonstra alta capacidade em uma ampla gama de tarefas, desde diálogos complexos e geração de conteúdo criativo até o seguimento detalhado de instruções."}, "anthropic.claude-v2:1": {"description": "A versão atualizada do Claude 2, com o dobro da janela de contexto, além de melhorias na confiabilidade, taxa de alucinação e precisão baseada em evidências em documentos longos e contextos RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku é o modelo mais rápido e compacto da Anthropic, projetado para oferecer respostas quase instantâneas. Ele possui desempenho direcionado rápido e preciso."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus é o modelo mais poderoso da Anthropic para lidar com tarefas altamente complexas. Ele se destaca em desempenho, inteligência, fluência e compreensão."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku é o modelo de próxima geração mais rápido da Anthropic. Em comparação com Claude 3 Hai<PERSON>, Claude 3.5 Haiku apresenta melhorias em várias habilidades e supera o maior modelo da geração anterior, Claude 3 Opus, em muitos testes de inteligência."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet oferece capacidades que vão além do Opus e uma velocidade superior ao Sonnet, mantendo o mesmo preço do Sonnet. O Sonnet é especialmente habilidoso em programação, ciência de dados, processamento visual e tarefas de agente."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet é o modelo mais inteligente da Anthropic até agora e é o primeiro modelo de raciocínio misto do mercado. Claude 3.7 Sonnet pode gerar respostas quase instantâneas ou um pensamento gradual prolongado, permitindo que os usuários vejam claramente esses processos. Sonnet é especialmente habilidoso em programação, ciência de dados, processamento visual e tarefas de agente."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 é o modelo mais poderoso da Anthropic para lidar com tarefas altamente complexas. Ele se destaca em desempenho, inteligência, fluidez e capacidade de compreensão."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 pode gerar respostas quase instantâneas ou um pensamento gradual prolongado, permitindo que os usuários vejam claramente esses processos. Usuários da API também podem controlar detalhadamente o tempo de raciocínio do modelo."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B é um modelo de linguagem grande esparso com 72 bilhões de parâmetros e 16 bilhões de parâmetros ativados, baseado na arquitetura Mixture of Experts em grupos (MoGE). Ele agrupa especialistas na fase de seleção e restringe a ativação de um número igual de especialistas dentro de cada grupo para cada token, alcançando equilíbrio na carga dos especialistas e melhorando significativamente a eficiência de implantação do modelo na plataforma Ascend."}, "aya": {"description": "Aya 23 é um modelo multilíngue lançado pela Cohere, suportando 23 idiomas, facilitando aplicações linguísticas diversificadas."}, "aya:35b": {"description": "Aya 23 é um modelo multilíngue lançado pela Cohere, suportando 23 idiomas, facilitando aplicações linguísticas diversificadas."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B é um modelo de linguagem de código aberto e comercializável desenvolvido pela Baichuan Intelligence, contendo 13 bilhões de parâmetros, alcançando os melhores resultados em benchmarks de chinês e inglês na mesma dimensão."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B é um modelo de linguagem grande baseado na arquitetura Mixture of Experts (MoE), desenvolvido pela <PERSON>du. Com um total de 300 bilhões de parâmetros, ativa apenas 47 bilhões por token durante a inferência, equilibrando desempenho robusto e eficiência computacional. Como um dos modelos centrais da série ERNIE 4.5, demonstra capacidades excepcionais em compreensão, geração, raciocínio textual e programação. O modelo utiliza um método inovador de pré-treinamento multimodal heterogêneo MoE, treinando conjuntamente texto e visão, o que melhora significativamente suas habilidades gerais, especialmente em seguir instruções e memória de conhecimento mundial."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse é um modelo multilíngue de alto desempenho com 32B, projetado para desafiar o desempenho de modelos monolíngues por meio de inovações em ajuste por instrução, arbitragem de dados, treinamento de preferências e fusão de modelos. Ele suporta 23 idiomas."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse é um modelo multilíngue de alto desempenho com 8B, projetado para desafiar o desempenho de modelos monolíngues por meio de inovações em ajuste por instrução, arbitragem de dados, treinamento de preferências e fusão de modelos. Ele suporta 23 idiomas."}, "c4ai-aya-vision-32b": {"description": "Aya Vision é um modelo multimodal de ponta, apresentando desempenho excepcional em múltiplos benchmarks críticos de linguagem, texto e imagem. Esta versão de 32 bilhões de parâmetros foca no desempenho multilíngue de ponta."}, "c4ai-aya-vision-8b": {"description": "Aya Vision é um modelo multimodal de ponta, apresentando desempenho excepcional em múltiplos benchmarks críticos de linguagem, texto e imagem. Esta versão de 8 bilhões de parâmetros foca em baixa latência e desempenho ideal."}, "charglm-3": {"description": "O CharGLM-3 é projetado para interpretação de personagens e companhia emocional, suportando memória de múltiplas rodadas e diálogos personalizados, com ampla aplicação."}, "charglm-4": {"description": "CharGLM-4 é projetado para interpretação de personagens e companhia emocional, suportando memória de múltiplas rodadas de longa duração e diálogos personalizados, com ampla aplicação."}, "chatglm3": {"description": "ChatGLM3 é um modelo de código fechado desenvolvido pela AI Zhipu em colaboração com o laboratório KEG da Tsinghua. Após um pré-treinamento extenso com identificadores em chinês e inglês, e um alinhamento com as preferências humanas, o modelo apresenta melhorias de 16%, 36% e 280% em MMLU, C-Eval e GSM8K, respectivamente, em comparação com a primeira geração. Ele lidera o ranking de tarefas em chinês C-Eval. É ideal para cenários que exigem alto nível de conhecimento, capacidade de raciocínio e criatividade, como redação de textos publicitários, escrita de romances, redação de conteúdo informativo e geração de código."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base é o modelo base de 6 bilhões de parâmetros da mais recente geração da série ChatGLM, desenvolvida pela Zhípǔ."}, "chatgpt-4o-latest": {"description": "O ChatGPT-4o é um modelo dinâmico, atualizado em tempo real para manter a versão mais atual. Ele combina uma poderosa capacidade de compreensão e geração de linguagem, adequado para cenários de aplicação em larga escala, incluindo atendimento ao cliente, educação e suporte técnico."}, "claude-2.0": {"description": "Claude 2 oferece avanços em capacidades críticas para empresas, incluindo um contexto líder do setor de 200K tokens, uma redução significativa na taxa de alucinação do modelo, prompts de sistema e uma nova funcionalidade de teste: chamadas de ferramentas."}, "claude-2.1": {"description": "Claude 2 oferece avanços em capacidades críticas para empresas, incluindo um contexto líder do setor de 200K tokens, uma redução significativa na taxa de alucinação do modelo, prompts de sistema e uma nova funcionalidade de teste: chamadas de ferramentas."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku é o modelo de próxima geração mais rápido da Anthropic. Em comparação com o Claude 3 Haiku, o Claude 3.5 Haiku apresenta melhorias em várias habilidades e superou o maior modelo da geração anterior, o Claude 3 Opus, em muitos testes de referência de inteligência."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet oferece capacidades que superam o Opus e uma velocidade mais rápida que o Sonnet, mantendo o mesmo preço. O Sonnet é especialmente bom em programação, ciência de dados, processamento visual e tarefas de agente."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet oferece capacidades que vão além do Opus e uma velocidade mais rápida do que o Sonnet, mantendo o mesmo preço do Sonnet. O Sonnet é especialmente bom em programação, ciência de dados, processamento visual e tarefas de agente."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet é o modelo de IA mais poderoso da Anthropic, com desempenho de ponta em tarefas altamente complexas. Ele pode lidar com prompts abertos e cenários não vistos, apresentando fluência excepcional e compreensão semelhante à humana. O Claude 3.7 Sonnet demonstra as possibilidades de geração de IA na vanguarda."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku é o modelo mais rápido e compacto da Anthropic, projetado para respostas quase instantâneas. Ele possui desempenho direcionado rápido e preciso."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus é o modelo mais poderoso da Anthropic para lidar com tarefas altamente complexas. Ele se destaca em desempenho, inteligência, fluência e compreensão."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet oferece um equilíbrio ideal entre inteligência e velocidade para cargas de trabalho empresariais. Ele fornece máxima utilidade a um custo mais baixo, sendo confiável e adequado para implantação em larga escala."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 é o modelo mais poderoso da Anthropic para lidar com tarefas altamente complexas. Ele se destaca em desempenho, inteligência, fluência e compreensão."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet pode gerar respostas quase instantâneas ou um pensamento gradual prolongado, permitindo que os usuários vejam claramente esses processos. Os usuários da API também podem ter um controle detalhado sobre o tempo de reflexão do modelo."}, "codegeex-4": {"description": "O CodeGeeX-4 é um poderoso assistente de programação AI, suportando perguntas e respostas inteligentes e autocompletar em várias linguagens de programação, aumentando a eficiência do desenvolvimento."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B é um modelo de geração de código multilíngue, suportando funcionalidades abrangentes, incluindo completude e geração de código, interpretador de código, busca na web, chamadas de função e perguntas e respostas em nível de repositório, cobrindo diversos cenários de desenvolvimento de software. É um modelo de geração de código de ponta com menos de 10B de parâmetros."}, "codegemma": {"description": "CodeGemma é um modelo de linguagem leve especializado em diferentes tarefas de programação, suportando iterações rápidas e integração."}, "codegemma:2b": {"description": "CodeGemma é um modelo de linguagem leve especializado em diferentes tarefas de programação, suportando iterações rápidas e integração."}, "codellama": {"description": "Code Llama é um LLM focado em geração e discussão de código, combinando suporte a uma ampla gama de linguagens de programação, adequado para ambientes de desenvolvedores."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama é um LLM focado em geração e discussão de código, combinando amplo suporte a linguagens de programação, adequado para ambientes de desenvolvedores."}, "codellama:13b": {"description": "Code Llama é um LLM focado em geração e discussão de código, combinando suporte a uma ampla gama de linguagens de programação, adequado para ambientes de desenvolvedores."}, "codellama:34b": {"description": "Code Llama é um LLM focado em geração e discussão de código, combinando suporte a uma ampla gama de linguagens de programação, adequado para ambientes de desenvolvedores."}, "codellama:70b": {"description": "Code Llama é um LLM focado em geração e discussão de código, combinando suporte a uma ampla gama de linguagens de programação, adequado para ambientes de desenvolvedores."}, "codeqwen": {"description": "CodeQwen1.5 é um modelo de linguagem de grande escala treinado com uma vasta quantidade de dados de código, projetado para resolver tarefas de programação complexas."}, "codestral": {"description": "Codestral é o primeiro modelo de código da Mistral AI, oferecendo suporte excepcional para tarefas de geração de código."}, "codestral-latest": {"description": "Codestral é um modelo gerador de ponta focado em geração de código, otimizado para preenchimento intermediário e tarefas de conclusão de código."}, "codex-mini-latest": {"description": "codex-mini-latest é uma versão ajustada do o4-mini, especialmente para Codex CLI. Para uso direto via API, recomendamos começar pelo gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B é um modelo projetado para seguir instruções, diálogos e programação."}, "cogview-4": {"description": "CogView-4 é o primeiro modelo de geração de imagens a partir de texto open source da Zhipu que suporta a geração de caracteres chineses. Ele apresenta melhorias abrangentes em compreensão semântica, qualidade de geração de imagens e capacidade de geração de textos em chinês e inglês, suportando entradas bilíngues de qualquer comprimento e podendo gerar imagens em qualquer resolução dentro do intervalo especificado."}, "cohere-command-r": {"description": "Command R é um modelo generativo escalável voltado para RAG e uso de ferramentas, permitindo IA em escala de produção para empresas."}, "cohere-command-r-plus": {"description": "Command R+ é um modelo otimizado para RAG de última geração, projetado para lidar com cargas de trabalho de nível empresarial."}, "cohere/Cohere-command-r": {"description": "Command R é um modelo generativo escalável projetado para uso com RAG e ferramentas, permitindo que empresas implementem IA em nível de produção."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ é um modelo otimizado de ponta para RAG, projetado para cargas de trabalho empresariais."}, "command": {"description": "Um modelo de diálogo que segue instruções, apresentando alta qualidade e confiabilidade em tarefas linguísticas, além de um comprimento de contexto mais longo em comparação com nosso modelo de geração básico."}, "command-a-03-2025": {"description": "O Command A é o nosso modelo mais poderoso até agora, apresentando um desempenho excepcional em uso de ferramentas, agentes, geração aumentada por recuperação (RAG) e cenários de aplicação multilíngue. O Command A possui um comprimento de contexto de 256K, pode ser executado com apenas duas GPUs e, em comparação com o Command R+ 08-2024, teve um aumento de 150% na taxa de transferência."}, "command-light": {"description": "Uma versão do Command que é menor e mais r<PERSON>, quase tão poderosa, mas com maior velocidade."}, "command-light-nightly": {"description": "Para reduzir o intervalo entre os lançamentos de versões principais, lançamos versões noturnas do modelo Command. Para a série command-light, essa versão é chamada de command-light-nightly. Observe que o command-light-nightly é a versão mais recente, experimental e (possivelmente) instável. As versões noturnas são atualizadas regularmente e sem aviso prévio, portanto, não são recomendadas para uso em ambientes de produção."}, "command-nightly": {"description": "Para reduzir o intervalo entre os lançamentos de versões principais, lançamos versões noturnas do modelo Command. Para a série Command, essa versão é chamada de command-cightly. Observe que o command-nightly é a versão mais recente, experimental e (possivelmente) instável. As versões noturnas são atualizadas regularmente e sem aviso prévio, portanto, não são recomendadas para uso em ambientes de produção."}, "command-r": {"description": "Command R é um LLM otimizado para tarefas de diálogo e longos contextos, especialmente adequado para interações dinâmicas e gerenciamento de conhecimento."}, "command-r-03-2024": {"description": "O Command R é um modelo de diálogo que segue instruções, apresentando maior qualidade e confiabilidade em tarefas linguísticas, além de um comprimento de contexto mais longo em comparação com modelos anteriores. Ele pode ser utilizado em fluxos de trabalho complexos, como geração de código, geração aumentada por recuperação (RAG), uso de ferramentas e agentes."}, "command-r-08-2024": {"description": "O command-r-08-2024 é uma versão atualizada do modelo Command R, lançada em agosto de 2024."}, "command-r-plus": {"description": "Command R+ é um modelo de linguagem de grande porte de alto desempenho, projetado para cenários empresariais reais e aplicações complexas."}, "command-r-plus-04-2024": {"description": "O Command R+ é um modelo de diálogo que segue instruções, apresentando maior qualidade e confiabilidade em tarefas linguísticas, além de um comprimento de contexto mais longo em comparação com modelos anteriores. É mais adequado para fluxos de trabalho complexos de RAG e uso de ferramentas em múltiplas etapas."}, "command-r-plus-08-2024": {"description": "Command R+ é um modelo de diálogo que segue instruções, apresentando maior qualidade e confiabilidade em tarefas de linguagem, além de ter um comprimento de contexto mais longo em comparação com modelos anteriores. É mais adequado para fluxos de trabalho RAG complexos e uso de ferramentas em múltiplas etapas."}, "command-r7b-12-2024": {"description": "O command-r7b-12-2024 é uma versão compacta e eficiente, lançada em dezembro de 2024. Ele se destaca em tarefas que exigem raciocínio complexo e processamento em múltiplas etapas, como RAG, uso de ferramentas e agentes."}, "compound-beta": {"description": "Compound-beta é um sistema de IA composto, suportado por vários modelos abertos disponíveis no GroqCloud, que pode usar ferramentas de forma inteligente e seletiva para responder a consultas dos usuários."}, "compound-beta-mini": {"description": "Compound-beta-mini é um sistema de IA composto, suportado por modelos abertos disponíveis no GroqCloud, que pode usar ferramentas de forma inteligente e seletiva para responder a consultas dos usuários."}, "computer-use-preview": {"description": "O modelo computer-use-preview é um modelo dedicado projetado para \"ferramentas de uso de computador\", treinado para entender e executar tarefas relacionadas a computadores."}, "dall-e-2": {"description": "O segundo modelo DALL·E, suporta geração de imagens mais realistas e precisas, com resolução quatro vezes maior que a da primeira geração."}, "dall-e-3": {"description": "O mais recente modelo DALL·E, lançado em novembro de 2023. Suporta geração de imagens mais realistas e precisas, com maior capacidade de detalhamento."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct oferece capacidade de processamento de instruções altamente confiável, suportando aplicações em diversos setores."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 é um modelo de inferência impulsionado por aprendizado por reforço (RL), que resolve problemas de repetitividade e legibilidade no modelo. Antes do RL, o DeepSeek-R1 introduziu dados de inicialização a frio, otimizando ainda mais o desempenho da inferência. Ele apresenta desempenho comparável ao OpenAI-o1 em tarefas matemáticas, de código e de inferência, e melhora o resultado geral por meio de métodos de treinamento cuidadosamente projetados."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1, ao utilizar recursos computacionais ampliados e introduzir mecanismos de otimização algorítmica durante o pós-treinamento, aumentou significativamente a profundidade de suas capacidades de raciocínio e inferência. Este modelo apresenta desempenho excelente em diversos benchmarks, incluindo matemática, programação e lógica geral. Seu desempenho geral está próximo de modelos líderes, como O3 e Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B é um modelo obtido pela destilação da cadeia de pensamento do DeepSeek-R1-0528 para o Qwen3 8B Base. Este modelo alcança desempenho de ponta (SOTA) entre modelos open source, superando o Qwen3 8B em 10% no teste AIME 2024 e atingindo o nível do Qwen3-235B-thinking. Apresenta excelente desempenho em raciocínio matemático, programação e lógica geral, compartilhando a arquitetura do Qwen3-8B, mas utilizando a configuração de tokenização do DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "Modelo de destilação DeepSeek-R1, otimizado para desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Modelo de destilação DeepSeek-R1, otimizado para desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "Modelo de destilação DeepSeek-R1, otimizado para desempenho de inferência através de aprendizado por reforço e dados de inicialização fria, modelo de código aberto que redefine os padrões de múltiplas tarefas."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B é um modelo obtido através da destilação do Qwen2.5-32B. Este modelo foi ajustado com 800 mil amostras selecionadas geradas pelo DeepSeek-R1, demonstrando desempenho excepcional em várias <PERSON>, como matemática, programação e raciocínio. Obteve resultados notáveis em vários testes de referência, alcançando uma precisão de 94,3% no MATH-500, demonstrando forte capacidade de raciocínio matemático."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-7B é um modelo obtido através da destilação do Qwen2.5-Math-7B. Este modelo foi ajustado com 800 mil amostras selecionadas geradas pelo DeepSeek-R1, demonstrando excelente capacidade de inferência. Apresentou desempenho notável em vários testes de referência, alcançando uma precisão de 92,8% no MATH-500, uma taxa de aprovação de 55,5% no AIME 2024 e uma pontuação de 1189 no CodeForces, demonstrando forte capacidade matemática e de programação para um modelo de 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 combina as excelentes características das versões anteriores, aprimorando a capacidade geral e de codificação."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 é um modelo de linguagem de especialistas mistos (MoE) com 671 bilhões de parâmetros, utilizando atenção latente de múltiplas cabeças (MLA) e a arquitetura DeepSeekMoE, combinando uma estratégia de balanceamento de carga sem perda auxiliar para otimizar a eficiência de inferência e treinamento. Após ser pré-treinado em 14,8 trilhões de tokens de alta qualidade e passar por ajuste fino supervisionado e aprendizado por reforço, o DeepSeek-V3 supera outros modelos de código aberto em desempenho, aproximando-se de modelos fechados líderes."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B é um modelo avançado treinado para diálogos de alta complexidade."}, "deepseek-ai/deepseek-r1": {"description": "LLM avançado e eficiente, especializado em raciocínio, matemática e programação."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 é um modelo de linguagem visual baseado no DeepSeekMoE-27B, desenvolvido como um especialista misto (MoE), utilizando uma arquitetura de MoE com ativação esparsa, alcançando desempenho excepcional com apenas 4,5 bilhões de parâmetros ativados. Este modelo se destaca em várias tarefas, incluindo perguntas visuais, reconhecimento óptico de caracteres, compreensão de documentos/tabelas/gráficos e localização visual."}, "deepseek-chat": {"description": "Um novo modelo de código aberto que combina capacidades gerais e de codificação, não apenas preservando a capacidade de diálogo geral do modelo Chat original e a poderosa capacidade de processamento de código do modelo Coder, mas também alinhando-se melhor às preferências humanas. Além disso, o DeepSeek-V2.5 também alcançou melhorias significativas em várias áreas, como tarefas de escrita e seguimento de instruções."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B é um modelo de linguagem de código, treinado com 20 trilhões de dados, dos quais 87% são código e 13% são em chinês e inglês. O modelo introduz uma janela de 16K e tarefas de preenchimento, oferecendo funcionalidades de completude de código e preenchimento de fragmentos em nível de projeto."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 é um modelo de código de especialistas abertos, destacando-se em tarefas de codificação, comparável ao GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 é um modelo de código de especialistas abertos, destacando-se em tarefas de codificação, comparável ao GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 é um modelo de inferência impulsionado por aprendizado por reforço (RL), que resolve problemas de repetitividade e legibilidade no modelo. Antes do RL, o DeepSeek-R1 introduziu dados de inicialização a frio, otimizando ainda mais o desempenho da inferência. Ele apresenta desempenho comparável ao OpenAI-o1 em tarefas matemáticas, de código e de inferência, e melhora o resultado geral por meio de métodos de treinamento cuidadosamente projetados."}, "deepseek-r1-0528": {"description": "Modelo completo de 685B, lançado em 28 de maio de 2025. O DeepSeek-R1 utilizou amplamente técnicas de aprendizado por reforço na fase pós-treinamento, aumentando significativamente a capacidade de raciocínio do modelo mesmo com poucos dados anotados. Apresenta alto desempenho e forte capacidade em tarefas de matemática, código e raciocínio em linguagem natural."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B vers<PERSON> rá<PERSON>, suporta busca em tempo real, oferecendo maior velocidade de resposta enquanto mantém o desempenho do modelo."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B vers<PERSON> padrão, suporta busca em tempo real, adequado para diálogos e tarefas de processamento de texto que requerem informações atualizadas."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama é um modelo baseado no Llama, destilado a partir do DeepSeek-R1."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 — um modelo maior e mais inteligente dentro do pacote DeepSeek — foi destilado para a arquitetura Llama 70B. Com base em testes de referência e avaliações humanas, este modelo é mais inteligente que o Llama 70B original, destacando-se especialmente em tarefas que exigem precisão matemática e factual."}, "deepseek-r1-distill-llama-8b": {"description": "O modelo da série DeepSeek-R1-Distill é obtido através da técnica de destilação de conhecimento, ajustando amostras geradas pelo DeepSeek-R1 em modelos de código aberto como Qwen e Llama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Lançado pela primeira vez em 14 de fevereiro de 2025, destilado pela equipe de desenvolvimento do modelo Qianfan a partir do modelo base Llama3_70B (Construído com Meta Llama), com dados de destilação que também incluem o corpus do Qianfan."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Lançado pela primeira vez em 14 de fevereiro de 2025, destilado pela equipe de desenvolvimento do modelo Qianfan a partir do modelo base Llama3_8B (Construído com Meta Llama), com dados de destilação que também incluem o corpus do Qianfan."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen é um modelo derivado do Qwen, destilado a partir do DeepSeek-R1."}, "deepseek-r1-distill-qwen-1.5b": {"description": "O modelo da série DeepSeek-R1-Distill é obtido através da técnica de destilação de conhecimento, ajustando amostras geradas pelo DeepSeek-R1 em modelos de código aberto como Qwen e Llama."}, "deepseek-r1-distill-qwen-14b": {"description": "O modelo da série DeepSeek-R1-Distill é obtido através da técnica de destilação de conhecimento, ajustando amostras geradas pelo DeepSeek-R1 em modelos de código aberto como Qwen e Llama."}, "deepseek-r1-distill-qwen-32b": {"description": "O modelo da série DeepSeek-R1-Distill é obtido através da técnica de destilação de conhecimento, ajustando amostras geradas pelo DeepSeek-R1 em modelos de código aberto como Qwen e Llama."}, "deepseek-r1-distill-qwen-7b": {"description": "O modelo da série DeepSeek-R1-Distill é obtido através da técnica de destilação de conhecimento, ajustando amostras geradas pelo DeepSeek-R1 em modelos de código aberto como Qwen e Llama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 versão completa rápida, suporta busca em tempo real, combinando a poderosa capacidade de 671B de parâmetros com maior velocidade de resposta."}, "deepseek-r1-online": {"description": "DeepSeek R1 versão completa, com 671B de parâmetros, suporta busca em tempo real, apresentando capacidades de compreensão e geração mais robustas."}, "deepseek-reasoner": {"description": "Modelo de raciocínio lançado pela DeepSeek. Antes de fornecer a resposta final, o modelo gera uma cadeia de pensamento para aumentar a precisão da resposta final."}, "deepseek-v2": {"description": "DeepSeek V2 é um modelo de linguagem eficiente Mixture-of-Experts, adequado para demandas de processamento econômico."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B é o modelo de código projetado do DeepSeek, oferecendo forte capacidade de geração de código."}, "deepseek-v3": {"description": "DeepSeek-V3 é um modelo MoE desenvolvido pela Hangzhou DeepSeek Artificial Intelligence Technology Research Co., Ltd., com desempenho destacado em várias avaliações, ocupando o primeiro lugar entre os modelos de código aberto nas principais listas. Em comparação com o modelo V2.5, a velocidade de geração do V3 foi aumentada em 3 vezes, proporcionando uma experiência de uso mais rápida e fluida."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 é um modelo MoE com 671 bilhões de parâmetros, destacando-se em habilidades de programação e técnicas, compreensão de contexto e processamento de textos longos."}, "deepseek/deepseek-chat-v3-0324": {"description": "O DeepSeek V3 é um modelo misto especializado com 685B de parâmetros, sendo a mais recente iteração da série de modelos de chat da equipe DeepSeek.\n\nEle herda o modelo [DeepSeek V3](/deepseek/deepseek-chat-v3) e se destaca em várias tarefas."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "O DeepSeek V3 é um modelo misto especializado com 685B de parâmetros, sendo a mais recente iteração da série de modelos de chat da equipe DeepSeek.\n\nEle herda o modelo [DeepSeek V3](/deepseek/deepseek-chat-v3) e se destaca em várias tarefas."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 melhorou significativamente a capacidade de raciocínio do modelo com muito poucos dados rotulados. Antes de fornecer a resposta final, o modelo gera uma cadeia de pensamento para aumentar a precisão da resposta final."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 melhora significativamente a capacidade de raciocínio do modelo mesmo com poucos dados anotados. Antes de fornecer a resposta final, o modelo gera uma cadeia de pensamento para aumentar a precisão da resposta."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 melhora significativamente a capacidade de raciocínio do modelo mesmo com poucos dados anotados. Antes de fornecer a resposta final, o modelo gera uma cadeia de pensamento para aumentar a precisão da resposta."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B é um grande modelo de linguagem baseado no Llama3.3 70B, que utiliza o ajuste fino da saída do DeepSeek R1 para alcançar um desempenho competitivo comparável aos grandes modelos de ponta."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B é um modelo de linguagem grande destilado baseado no Llama-3.1-8B-Instruct, treinado usando a saída do DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B é um modelo de linguagem grande destilado baseado no Qwen 2.5 14B, treinado usando a saída do DeepSeek R1. Este modelo superou o o1-mini da OpenAI em vários benchmarks, alcançando os mais recentes avanços tecnológicos em modelos densos (state-of-the-art). Aqui estão alguns resultados de benchmarks:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nClassificação CodeForces: 1481\nEste modelo, ajustado a partir da saída do DeepSeek R1, demonstrou desempenho competitivo comparável a modelos de ponta de maior escala."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B é um modelo de linguagem grande destilado baseado no Qwen 2.5 32B, treinado usando a saída do DeepSeek R1. Este modelo superou o o1-mini da OpenAI em vários benchmarks, alcançando os mais recentes avanços tecnológicos em modelos densos (state-of-the-art). Aqui estão alguns resultados de benchmarks:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nClassificação CodeForces: 1691\nEste modelo, ajustado a partir da saída do DeepSeek R1, demonstrou desempenho competitivo comparável a modelos de ponta de maior escala."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 é o mais recente modelo de código aberto <PERSON> pela equipe DeepSeek, com desempenho de inferência extremamente robusto, especialmente em tarefas de matemática, programação e raciocínio, alcançando níveis comparáveis ao modelo o1 da OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 melhorou significativamente a capacidade de raciocínio do modelo com muito poucos dados rotulados. Antes de fornecer a resposta final, o modelo gera uma cadeia de pensamento para aumentar a precisão da resposta final."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 alcançou um avanço significativo na velocidade de inferência em comparação com os modelos anteriores. Classificado como o número um entre os modelos de código aberto, pode competir com os modelos fechados mais avançados do mundo. DeepSeek-V3 utiliza a arquitetura de Atenção Multi-Cabeça (MLA) e DeepSeekMoE, que foram amplamente validadas no DeepSeek-V2. <PERSON><PERSON><PERSON> disso, DeepSeek-V3 introduziu uma estratégia auxiliar sem perdas para balanceamento de carga e definiu objetivos de treinamento de previsão de múltiplos rótulos para obter um desempenho mais forte."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 alcançou um avanço significativo na velocidade de inferência em comparação com os modelos anteriores. Classificado como o número um entre os modelos de código aberto, pode competir com os modelos fechados mais avançados do mundo. DeepSeek-V3 utiliza a arquitetura de Atenção Multi-Cabeça (MLA) e DeepSeekMoE, que foram amplamente validadas no DeepSeek-V2. <PERSON><PERSON><PERSON> disso, DeepSeek-V3 introduziu uma estratégia auxiliar sem perdas para balanceamento de carga e definiu objetivos de treinamento de previsão de múltiplos rótulos para obter um desempenho mais forte."}, "deepseek_r1": {"description": "DeepSeek-R1 é um modelo de inferência impulsionado por aprendizado por reforço (RL), que resolve problemas de repetitividade e legibilidade no modelo. Antes do RL, o DeepSeek-R1 introduziu dados de inicialização a frio, otimizando ainda mais o desempenho de inferência. Ele se compara ao OpenAI-o1 em tarefas de matemática, código e raciocínio, e, através de métodos de treinamento cuidadosamente projetados, melhor<PERSON> o desempenho geral."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B é um modelo obtido através do treinamento de destilação do Llama-3.3-70B-Instruct. Este modelo é parte da série DeepSeek-R1 e, através do uso de amostras geradas pelo DeepSeek-R1, demonstrou desempenho excepcional em matemática, programação e raciocínio."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B é um modelo obtido através da destilação de conhecimento do Qwen2.5-14B. Este modelo foi ajustado com 800 mil amostras selecionadas geradas pelo DeepSeek-R1, demonstrando excelente capacidade de inferência."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B é um modelo obtido através da destilação de conhecimento do Qwen2.5-32B. Este modelo foi ajustado com 800 mil amostras selecionadas geradas pelo DeepSeek-R1, demonstrando desempenho excepcional em várias áreas, incluindo matemática, programação e raciocínio."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite é a nova geração de modelo leve, com velocidade de resposta extrema, alcançando níveis de desempenho e latência de classe mundial."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k é uma versão totalmente aprimorada do Doubao-1.5-Pro, com um aumento significativo de 10% no desempenho geral. Suporta raciocínio com janelas de contexto de 256k e um comprimento de saída de até 12k tokens. <PERSON><PERSON>, janelas maiores e excelente custo-ben<PERSON><PERSON><PERSON>, adequado para uma ampla gama de cenários de aplicação."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro é a nova geração de modelo principal, com desempenho totalmente aprimorado, destacando-se em conhecimento, código, racio<PERSON><PERSON>io, entre outros aspectos."}, "doubao-1.5-thinking-pro": {"description": "O modelo de pensamento profundo Doubao-1.5 apresenta um desempenho excepcional em áreas especializadas como matemática, programação e raciocínio científico, além de tarefas gerais como escrita criativa. Ele alcançou ou se aproximou do nível de elite da indústria em várias referências respeitáveis, como AIME 2024, Codeforces e GPQA. Suporta uma janela de contexto de 128k e uma saída de 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 é um novo modelo de pensamento profundo (versão m com capacidade nativa de inferência multimodal profunda), destacando-se em matemática, programação, raciocínio científico e tarefas gerais como escrita criativa. Alcança ou se aproxima do topo da indústria em benchmarks como AIME 2024, Codeforces e GPQA. Suporta janela de contexto de 128k e saída de 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Novo modelo de pensamento profundo visual, com capacidades avançadas de compreensão e inferência multimodal geral, alcançando desempenho SOTA em 37 dos 59 benchmarks públicos."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS é um modelo Agent nativo para interação com interfaces gráficas (GUI). Possui habilidades humanas de percepção, raciocínio e ação para interação fluida com GUIs."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite é um modelo multimodal atualizado, suportando reconhecimento de imagens de qualquer resolução e proporções extremas, melhorando a capacidade de raciocínio visual, reconhecimento de documentos, compreensão de informações detalhadas e seguimento de instruções. Suporta uma janela de contexto de 128k, com comprimento de saída de até 16k tokens."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro é um modelo multimodal avançado, suportando reconhecimento de imagens em qualquer resolução e proporção extrema, com capacidades aprimoradas de raciocínio visual, reconhecimento de documentos, compreensão de detalhes e seguimento de instruções."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro é um modelo multimodal avançado, suportando reconhecimento de imagens em qualquer resolução e proporção extrema, com capacidades aprimoradas de raciocínio visual, reconhecimento de documentos, compreensão de detalhes e seguimento de instruções."}, "doubao-lite-128k": {"description": "Oferece velocidade de resposta extrema e melhor custo-ben<PERSON><PERSON><PERSON>, proporcionando opções mais flexíveis para diferentes cenários dos clientes. Suporta inferência e fine-tuning com janela de contexto de 128k."}, "doubao-lite-32k": {"description": "Oferece velocidade de resposta extrema e melhor custo-ben<PERSON><PERSON><PERSON>, proporcionando opções mais flexíveis para diferentes cenários dos clientes. Suporta inferência e fine-tuning com janela de contexto de 32k."}, "doubao-lite-4k": {"description": "Oferece velocidade de resposta extrema e melhor custo-ben<PERSON><PERSON><PERSON>, proporcionando opções mais flexíveis para diferentes cenários dos clientes. Suporta inferência e fine-tuning com janela de contexto de 4k."}, "doubao-pro-256k": {"description": "Modelo principal com melhor desempenho, adequado para tarefas complexas, apresentando ótimos resultados em perguntas de referência, resumos, criação, classificação de texto, interpretação de papéis e outros cenários. Suporta inferência e fine-tuning com janela de contexto de 256k."}, "doubao-pro-32k": {"description": "Modelo principal com melhor desempenho, adequado para tarefas complexas, apresentando ótimos resultados em perguntas de referência, resumos, criação, classificação de texto, interpretação de papéis e outros cenários. Suporta inferência e fine-tuning com janela de contexto de 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 é um novo modelo multimodal de pensamento profundo, suportando três modos de pensamento: auto, thinking e non-thinking. No modo non-thinking, o desempenho supera significativamente o Doubao-1.5-pro/250115. Suporta janela de contexto de 256k e saída de até 16k tokens."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash é um modelo multimodal de pensamento profundo com velocidade de inferência extrema, TPOT de apenas 10ms; suporta compreensão textual e visual, com capacidade textual superior à geração lite anterior e compreensão visual comparável à série pro dos concorrentes. Suporta janela de contexto de 256k e saída de até 16k tokens."}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking tem capacidade de pensamento significativamente reforçada, melhorando ainda mais habilidades básicas como codificação, matemática e raciocínio lógico em comparação com Doubao-1.5-thinking-pro, além de suportar compreensão visual. Suporta janela de contexto de 256k e saída de até 16k tokens."}, "doubao-seedream-3-0-t2i-250415": {"description": "O modelo de geração de imagens Doubao foi desenvolvido pela equipe <PERSON>d da ByteDance, suportando entrada de texto e imagem, oferecendo uma experiência de geração de imagens altamente controlável e de alta qualidade. Gera imagens baseadas em prompts textuais."}, "doubao-vision-lite-32k": {"description": "O modelo Doubao-vision é um grande modelo multimodal lançado pela Doubao, com forte capacidade de compreensão e inferência de imagens, além de compreensão precisa de instruções. O modelo demonstra desempenho robusto em extração de informações de texto em imagens e tarefas de inferência baseadas em imagens, podendo ser aplicado a tarefas visuais de perguntas e respostas mais complexas e amplas."}, "doubao-vision-pro-32k": {"description": "O modelo Doubao-vision é um grande modelo multimodal lançado pela Doubao, com forte capacidade de compreensão e inferência de imagens, além de compreensão precisa de instruções. O modelo demonstra desempenho robusto em extração de informações de texto em imagens e tarefas de inferência baseadas em imagens, podendo ser aplicado a tarefas visuais de perguntas e respostas mais complexas e amplas."}, "emohaa": {"description": "O Emohaa é um modelo psicológico com capacidade de consultoria profissional, ajudando os usuários a entender questões emocionais."}, "ernie-3.5-128k": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, cobrindo uma vasta quantidade de dados em chinês e inglês, com forte capacidade geral, capaz de atender à maioria das demandas de diálogo, geração criativa e aplicações de plugins; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ernie-3.5-8k": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, cobrindo uma vasta quantidade de dados em chinês e inglês, com forte capacidade geral, capaz de atender à maioria das demandas de diálogo, geração criativa e aplicações de plugins; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ernie-3.5-8k-preview": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, cobrindo uma vasta quantidade de dados em chinês e inglês, com forte capacidade geral, capaz de atender à maioria das demandas de diálogo, geração criativa e aplicações de plugins; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ernie-4.0-8k-latest": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, com capacidade de modelo amplamente aprimorada em comparação com o ERNIE 3.5, amplamente aplicável a cenários de tarefas complexas em várias áreas; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ernie-4.0-8k-preview": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, com capacidade de modelo amplamente aprimorada em comparação com o ERNIE 3.5, amplamente aplicável a cenários de tarefas complexas em várias áreas; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas."}, "ernie-4.0-turbo-128k": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, com desempenho geral excepcional, amplamente aplicável a cenários de tarefas complexas em várias áreas; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas. Em comparação com o ERNIE 4.0, apresenta desempenho superior."}, "ernie-4.0-turbo-8k-latest": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, com desempenho geral excepcional, amplamente aplicável a cenários de tarefas complexas em várias áreas; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas. Em comparação com o ERNIE 4.0, apresenta desempenho superior."}, "ernie-4.0-turbo-8k-preview": {"description": "Modelo de linguagem de grande escala de nível flagship desenvolvido pela Baidu, com desempenho geral excepcional, amplamente aplicável a cenários de tarefas complexas em várias áreas; suporta integração automática com plugins de busca da Baidu, garantindo a atualidade das informações de perguntas e respostas. Em comparação com o ERNIE 4.0, apresenta desempenho superior."}, "ernie-4.5-8k-preview": {"description": "O modelo ERNIE 4.5 é a nova geração de modelo de base multimodal nativo desenvolvido pela <PERSON>du, alcançando otimização colaborativa por meio de modelagem conjunta de múltiplos modos, com excelente capacidade de compreensão multimodal; apresenta habilidades linguísticas aprimoradas, com melhorias abrangentes em compreensão, geração, lógica e memória, além de redução de alucinações e melhorias significativas em raciocínio lógico e habilidades de codificação."}, "ernie-4.5-turbo-128k": {"description": "O Wenxin 4.5 Turbo apresenta melhorias significativas em redução de alucinações, raciocínio lógico e habilidades de codificação. Em comparação com o Wenxin 4.5, é mais rápido e mais barato. A capacidade do modelo foi amplamente aprimorada, atendendo melhor ao processamento de diálogos longos com múltiplas interações e tarefas de compreensão de documentos longos."}, "ernie-4.5-turbo-32k": {"description": "O Wenxin 4.5 Turbo também apresenta melhorias significativas em redução de alucinações, raciocínio lógico e habilidades de codificação. Em comparação com o Wenxin 4.5, é mais rápido e mais barato. As habilidades de criação de texto e perguntas e respostas de conhecimento foram significativamente aprimoradas. O comprimento da saída e o atraso de frases completas aumentaram em relação ao ERNIE 4.5."}, "ernie-4.5-turbo-vl-32k": {"description": "Nova versão do modelo <PERSON>, com melhorias significativas em compreensão de imagens, criação, tradução e codificação, suportando pela primeira vez um comprimento de contexto de 32K, com redução significativa no atraso do primeiro token."}, "ernie-char-8k": {"description": "Modelo de linguagem de grande escala vertical desenvolvido pela <PERSON>, adequado para aplicações como NPCs de jogos, diálogos de atendimento ao cliente e interpretação de personagens, com estilo de personagem mais distinto e consistente, capacidade de seguir instruções mais forte e desempenho de inferência superior."}, "ernie-char-fiction-8k": {"description": "Modelo de linguagem de grande escala vertical desenvolvido pela <PERSON>, adequado para aplicações como NPCs de jogos, diálogos de atendimento ao cliente e interpretação de personagens, com estilo de personagem mais distinto e consistente, capacidade de seguir instruções mais forte e desempenho de inferência superior."}, "ernie-irag-edit": {"description": "O modelo de edição de imagens ERNIE iRAG, desenvolvido pela <PERSON>du, suporta operações como apagar objetos (erase), repintar objetos (repaint) e gerar variações (variation) baseadas em imagens."}, "ernie-lite-8k": {"description": "ERNIE Lite é um modelo de linguagem de grande escala leve desenvolvido pela <PERSON>, equilibrando excelente desempenho do modelo e eficiência de inferência, adequado para uso em placas de aceleração de IA de baixa potência."}, "ernie-lite-pro-128k": {"description": "Modelo de linguagem de grande escala leve desenvolvido p<PERSON>, equilibrando excelente desempenho do modelo e eficiência de inferência, com desempenho superior ao ERNIE Lite, adequado para uso em placas de aceleração de IA de baixa potência."}, "ernie-novel-8k": {"description": "Modelo de linguagem de grande escala geral desenvolvido p<PERSON>, com vantagens notáveis na capacidade de continuar histórias, também aplicável em cenários como peças curtas e filmes."}, "ernie-speed-128k": {"description": "Modelo de linguagem de alto desempenho desenvolvido pela <PERSON>, lançado em 2024, com excelente capacidade geral, adequado para ser usado como modelo base para ajuste fino, lidando melhor com problemas de cenários específicos, enquanto apresenta excelente desempenho de inferência."}, "ernie-speed-pro-128k": {"description": "Modelo de linguagem de alto desempenho desenvolvido pela <PERSON>, lançado em 2024, com excelente capacidade geral, desempenho superior ao ERNIE Speed, adequado para ser usado como modelo base para ajuste fino, lidando melhor com problemas de cenários específicos, enquanto apresenta excelente desempenho de inferência."}, "ernie-tiny-8k": {"description": "ERNIE Tiny é um modelo de linguagem de grande escala de alto desempenho desenvolvido p<PERSON>, com os menores custos de implantação e ajuste entre os modelos da série Wenxin."}, "ernie-x1-32k": {"description": "Possui habilidades superiores de compreensão, planejamento, reflexão e evolução. Como um modelo de pensamento profundo mais abrangente, o Wenxin X1 combina precisão, criatividade e eloquência, destacando-se em perguntas e respostas de conhecimento em chinês, criação literária, redação de documentos, diálogos cotidianos, raciocínio lógico, cálculos complexos e chamadas de ferramentas."}, "ernie-x1-32k-preview": {"description": "O modelo grande Wenxin X1 possui habilidades aprimoradas de compreensão, planejamento, reflexão e evolução. Como um modelo de pensamento profundo mais abrangente, o Wenxin X1 combina precisão, criatividade e eloquência, destacando-se em perguntas e respostas de conhecimento em chinês, criação literária, redação de documentos, diálogos cotidianos, raciocínio lógico, cálculos complexos e chamadas de ferramentas."}, "ernie-x1-turbo-32k": {"description": "Melhor desempenho e eficácia em comparação com o ERNIE-X1-32K."}, "flux-1-schnell": {"description": "Modelo de geração de imagens a partir de texto com 12 bilhões de parâmetros desenvolvido pela Black Forest Labs, utilizando técnica de destilação de difusão adversarial latente, capaz de gerar imagens de alta qualidade em 1 a 4 passos. Seu desempenho é comparável a alternativas proprietárias e é lançado sob licença Apache-2.0, adequado para uso pessoal, acadêmico e comercial."}, "flux-dev": {"description": "FLUX.1 [dev] é um modelo open source refinado e com pesos voltado para aplicações não comerciais. Mantém qualidade de imagem e capacidade de seguir instruções próximas à versão profissional FLUX, com maior eficiência operacional. Em comparação com modelos padrão de tamanho similar, é mais eficiente no uso de recursos."}, "flux-kontext/dev": {"description": "Modelo de edição de imagem Frontier."}, "flux-merged": {"description": "O modelo FLUX.1-merged combina as características profundas exploradas na fase de desenvolvimento \"DEV\" com as vantagens de execução rápida representadas por \"Schnell\". Essa combinação não só eleva os limites de desempenho do modelo, como também amplia seu campo de aplicação."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] pode processar texto e imagens de referência como entrada, realizando edições locais direcionadas e transformações complexas de cenas inteiras de forma fluida."}, "flux-schnell": {"description": "FLUX.1 [schnell] é atualmente o modelo open source mais avançado de poucos passos, superando concorrentes e até modelos não destilados poderosos como Midjourney v6.0 e DALL·E 3 (HD). Ajustado para preservar toda a diversidade de saída do pré-treinamento, oferece melhorias significativas em qualidade visual, conformidade com instruções, variações de tamanho/proporção, tratamento de fontes e diversidade de saída, proporcionando uma experiência criativa mais rica e variada."}, "flux.1-schnell": {"description": "Transformador de fluxo retificado com 12 bilhões de parâmetros, capaz de gerar imagens a partir de descrições textuais."}, "flux/schnell": {"description": "FLUX.1 [schnell] é um modelo transformador streaming com 12 bilhões de parâmetros, capaz de gerar imagens de alta qualidade a partir de texto em 1 a 4 passos, adequado para uso pessoal e comercial."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Ajuste) oferece desempenho estável e ajustável, sendo a escolha ideal para soluções de tarefas complexas."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Ajuste) oferece excelente suporte multimodal, focando na resolução eficaz de tarefas complexas."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro é o modelo de IA de alto desempenho do Google, projetado para expansão em uma ampla gama de tarefas."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 é um modelo multimodal eficiente, suportando a expansão de aplicações amplas."}, "gemini-1.5-flash-002": {"description": "O Gemini 1.5 Flash 002 é um modelo multimodal eficiente, que suporta uma ampla gama de aplicações."}, "gemini-1.5-flash-8b": {"description": "O Gemini 1.5 Flash 8B é um modelo multimodal eficiente, com suporte para uma ampla gama de aplicações."}, "gemini-1.5-flash-8b-exp-0924": {"description": "O Gemini 1.5 Flash 8B 0924 é o mais recente modelo experimental, com melhorias significativas de desempenho em casos de uso de texto e multimídia."}, "gemini-1.5-flash-8b-latest": {"description": "O Gemini 1.5 Flash 8B é um modelo multimodal eficiente que suporta uma ampla gama de aplicações em expansão."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 oferece capacidade de processamento multimodal otimizada, adequada para diversos cenários de tarefas complexas."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash é o mais recente modelo de IA multimodal do Google, com capacidade de processamento rápido, suportando entradas de texto, imagem e vídeo, adequado para uma variedade de tarefas de expansão eficiente."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 é uma solução de IA multimodal escalável, suportando uma ampla gama de tarefas complexas."}, "gemini-1.5-pro-002": {"description": "O Gemini 1.5 Pro 002 é o mais recente modelo pronto para produção, oferecendo saídas de maior qualidade, com melhorias significativas em tarefas matemáticas, contextos longos e tarefas visuais."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 oferece excelente capacidade de processamento multimodal, proporcionando maior flexibilidade para o desenvolvimento de aplicações."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 combina as mais recentes técnicas de otimização, proporcionando uma capacidade de processamento de dados multimodal mais eficiente."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro suporta até 2 milhões de tokens, sendo a escolha ideal para modelos multimodais de médio porte, adequados para suporte multifacetado em tarefas complexas."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash oferece funcionalidades e melhorias de próxima geração, incluindo velocidade excepcional, uso nativo de ferramentas, geração multimodal e uma janela de contexto de 1M tokens."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash oferece funcionalidades e melhorias de próxima geração, incluindo velocidade excepcional, uso nativo de ferramentas, geração multimodal e uma janela de contexto de 1M tokens."}, "gemini-2.0-flash-exp": {"description": "Variante do modelo Gemini 2.0 Flash, otimizada para custo-benefício e baixa latência."}, "gemini-2.0-flash-exp-image-generation": {"description": "Modelo experimental Gemini 2.0 Flash, suporta geração de imagens"}, "gemini-2.0-flash-lite": {"description": "Variante do modelo Gemini 2.0 Flash, otimizada para custo-benefício e baixa latência."}, "gemini-2.0-flash-lite-001": {"description": "Variante do modelo Gemini 2.0 Flash, otimizada para custo-benefício e baixa latência."}, "gemini-2.0-flash-preview-image-generation": {"description": "Modelo de pré-visualização Gemini 2.0 Flash, suporta geração de imagens"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash é o modelo com melhor custo-benef<PERSON><PERSON> do <PERSON>, oferecendo funcionalidades abrangentes."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite é o modelo mais compacto e com melhor custo-benef<PERSON><PERSON> do <PERSON>, projetado para uso em larga escala."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview é o modelo mais compacto e com melhor custo-benef<PERSON>cio do <PERSON>, projetado para uso em larga escala."}, "gemini-2.5-flash-preview-04-17": {"description": "O Gemini 2.5 Flash Preview é o modelo mais acessível do Google, oferecendo uma gama completa de funcionalidades."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview é o modelo com melhor custo-benef<PERSON><PERSON> do <PERSON>, oferecendo funcionalidades abrangentes."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro é o modelo de pensamento mais avançado do Google, capaz de raciocinar sobre código, matemática e problemas complexos nas áreas de STEM, além de analisar grandes conjuntos de dados, bases de código e documentos usando contextos longos."}, "gemini-2.5-pro-preview-03-25": {"description": "O Gemini 2.5 Pro Preview é o modelo de pensamento mais avançado do Google, capaz de raciocinar sobre problemas complexos em código, matemática e áreas STEM, além de analisar grandes conjuntos de dados, bibliotecas de código e documentos usando longos contextos."}, "gemini-2.5-pro-preview-05-06": {"description": "O Gemini 2.5 Pro Preview é o modelo de pensamento mais avançado do Google, capaz de raciocinar sobre problemas complexos em código, matemática e áreas STEM, além de analisar grandes conjuntos de dados, bibliotecas de código e documentos usando longos contextos."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview é o modelo de pensamento mais avançado do Google, capaz de raciocinar sobre problemas complexos em código, matemática e áreas STEM, além de analisar grandes conjuntos de dados, bibliotecas de código e documentos usando contexto extenso."}, "gemma-7b-it": {"description": "Gemma 7B é adequado para o processamento de tarefas de pequeno a médio porte, combinando custo e eficiência."}, "gemma2": {"description": "Gemma 2 é um modelo eficiente lançado pelo Google, abrangendo uma variedade de cenários de aplicação, desde aplicações pequenas até processamento de dados complexos."}, "gemma2-9b-it": {"description": "Gemma 2 9B é um modelo otimizado para integração de tarefas e ferramentas específicas."}, "gemma2:27b": {"description": "Gemma 2 é um modelo eficiente lançado pelo Google, abrangendo uma variedade de cenários de aplicação, desde aplicações pequenas até processamento de dados complexos."}, "gemma2:2b": {"description": "Gemma 2 é um modelo eficiente lançado pelo Google, abrangendo uma variedade de cenários de aplicação, desde aplicações pequenas até processamento de dados complexos."}, "generalv3": {"description": "Spark Pro é um modelo de linguagem de alto desempenho otimizado para áreas profissionais, focando em matemática, programação, medicina, educação e outros campos, e suportando busca online e plugins integrados como clima e data. Seu modelo otimizado apresenta desempenho excepcional e eficiência em perguntas e respostas complexas, compreensão de linguagem e criação de texto de alto nível, sendo a escolha ideal para cenários de aplicação profissional."}, "generalv3.5": {"description": "Spark3.5 Max é a versão mais completa, suportando busca online e muitos plugins integrados. Suas capacidades centrais totalmente otimizadas, juntamente com a definição de papéis do sistema e a funcionalidade de chamada de funções, fazem com que seu desempenho em vários cenários de aplicação complexos seja extremamente excepcional."}, "glm-4": {"description": "O GLM-4 é a versão antiga lançada em janeiro de 2024, atualmente substituída pelo mais poderoso GLM-4-0520."}, "glm-4-0520": {"description": "O GLM-4-0520 é a versão mais recente do modelo, projetada para tarefas altamente complexas e diversificadas, com desempenho excepcional."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat apresenta alto desempenho em semântica, matemática, raciocínio, código e conhecimento. Também possui navegação na web, execução de código, chamadas de ferramentas personalizadas e raciocínio de texto longo. Suporta 26 idiomas, incluindo japonês, coreano e alemão."}, "glm-4-air": {"description": "O GLM-4-Air é uma versão econômica, com desempenho próximo ao GLM-4, oferecendo alta velocidade a um preço acessível."}, "glm-4-air-250414": {"description": "GLM-4-Air é uma versão de bom custo-benefício, com desempenho próximo ao GLM-4, oferecendo alta velocidade a um preço acessível."}, "glm-4-airx": {"description": "O GLM-4-AirX oferece uma versão eficiente do GLM-4-Air, com velocidade de inferência até 2,6 vezes mais rápida."}, "glm-4-alltools": {"description": "O GLM-4-AllTools é um modelo de agente multifuncional, otimizado para suportar planejamento de instruções complexas e chamadas de ferramentas, como navegação na web, interpretação de código e geração de texto, adequado para execução de múltiplas tarefas."}, "glm-4-flash": {"description": "O GLM-4-<PERSON> é a escolha ideal para tarefas simples, com a maior velocidade e o preço mais acessível."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON> é a escolha ideal para tarefas simples, sendo a mais rápida e gratuita."}, "glm-4-flashx": {"description": "GLM-4-FlashX é uma versão aprimorada do Flash, com velocidade de inferência super rápida."}, "glm-4-long": {"description": "O GLM-4-Long suporta entradas de texto superlongas, adequado para tarefas de memória e processamento de documentos em larga escala."}, "glm-4-plus": {"description": "O GLM-4-Plus, como um modelo de alta inteligência, possui uma forte capacidade de lidar com textos longos e tarefas complexas, com desempenho amplamente aprimorado."}, "glm-4.1v-thinking-flash": {"description": "A série GLM-4.1V-Thinking é atualmente o modelo visual mais potente conhecido na categoria de VLMs de 10 bilhões de parâmetros, integrando tarefas de linguagem visual de ponta no mesmo nível, incluindo compreensão de vídeo, perguntas e respostas sobre imagens, resolução de problemas acadêmicos, reconhecimento óptico de caracteres (OCR), interpretação de documentos e gráficos, agentes GUI, codificação front-end para web, grounding, entre outros. Suas capacidades em várias tarefas superam até modelos com 8 vezes mais parâmetros, como o Qwen2.5-VL-72B. Por meio de técnicas avançadas de aprendizado por reforço, o modelo domina o raciocínio em cadeia para melhorar a precisão e riqueza das respostas, superando significativamente modelos tradicionais sem o mecanismo thinking em termos de resultados finais e interpretabilidade."}, "glm-4.1v-thinking-flashx": {"description": "A série GLM-4.1V-Thinking é atualmente o modelo visual mais potente conhecido na categoria de VLMs de 10 bilhões de parâmetros, integrando tarefas de linguagem visual de ponta no mesmo nível, incluindo compreensão de vídeo, perguntas e respostas sobre imagens, resolução de problemas acadêmicos, reconhecimento óptico de caracteres (OCR), interpretação de documentos e gráficos, agentes GUI, codificação front-end para web, grounding, entre outros. Suas capacidades em várias tarefas superam até modelos com 8 vezes mais parâmetros, como o Qwen2.5-VL-72B. Por meio de técnicas avançadas de aprendizado por reforço, o modelo domina o raciocínio em cadeia para melhorar a precisão e riqueza das respostas, superando significativamente modelos tradicionais sem o mecanismo thinking em termos de resultados finais e interpretabilidade."}, "glm-4.5": {"description": "Modelo flagship mais recent<PERSON> da Zhizhu, suporta modo de pensamento alternado, com capacidades abrangentes que alcançam o estado da arte em modelos open source, e contexto de até 128K tokens."}, "glm-4.5-air": {"description": "Versão leve do GLM-4.5, equili<PERSON><PERSON> desem<PERSON>ho e custo-benef<PERSON><PERSON>, com capacidade flexível de alternar entre modos híbridos de pensamento."}, "glm-4.5-airx": {"description": "Versão ultrarrápida do GLM-4.5-Air, com resposta mais rápida, projetada para demandas de alta velocidade e grande escala."}, "glm-4.5-flash": {"description": "Versão gratuita do GLM-4.5, com desempenho destacado em inferência, codificação e agentes inteligentes."}, "glm-4.5-x": {"description": "Versão ultrarrápida do GLM-4.5, combinando alto desempenho com velocidade de geração de até 100 tokens por segundo."}, "glm-4v": {"description": "O GLM-4V oferece uma forte capacidade de compreensão e raciocínio de imagens, suportando várias tarefas visuais."}, "glm-4v-flash": {"description": "GLM-4V-Flash é focado na compreensão eficiente de uma única imagem, adequado para cenários de análise de imagem rápida, como análise de imagem em tempo real ou processamento em lote de imagens."}, "glm-4v-plus": {"description": "O GLM-4V-Plus possui a capacidade de entender conteúdo de vídeo e múltiplas imagens, adequado para tarefas multimodais."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus possui capacidade de compreensão de conteúdo de vídeo e múltiplas imagens, adequado para tarefas multimodais."}, "glm-z1-air": {"description": "Modelo de inferência: possui forte capacidade de inferência, adequado para tarefas que exigem raciocínio profundo."}, "glm-z1-airx": {"description": "Inferência ultrarrápida: com velocidade de inferência super rápida e forte efeito de raciocínio."}, "glm-z1-flash": {"description": "Série GLM-Z1 com forte capacidade de raciocínio complexo, destacando-se em lógica, matemática e programação."}, "glm-z1-flashx": {"description": "Alta velocidade e baixo custo: versão aprimorada Flash, com inferência ultrarrápida e garantia de concorrência mais rápida."}, "glm-zero-preview": {"description": "O GLM-Zero-Preview possui uma poderosa capacidade de raciocínio complexo, destacando-se em áreas como raciocínio lógico, matemática e programação."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash oferece funcionalidades e melhorias de próxima geração, incluindo velocidade excepcional, uso nativo de ferramentas, geração multimodal e uma janela de contexto de 1M tokens."}, "google/gemini-2.0-flash-exp:free": {"description": "O Gemini 2.0 Flash Experimental é o mais recente modelo de IA multimodal experimental do Google, com melhorias de qualidade em comparação com versões anteriores, especialmente em conhecimento do mundo, código e longos contextos."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash é o modelo principal mais avançado do Google, projetado para tarefas avançadas de raciocínio, codificação, matemática e ciências. Ele possui uma capacidade embutida de \"pensamento\", permitindo respostas com maior precisão e processamento detalhado do contexto.\n\nObservação: este modelo possui duas variantes: com pensamento e sem pensamento. O preço de saída varia significativamente dependendo se a capacidade de pensamento está ativada. Se você escolher a variante padrão (sem o sufixo \":thinking\"), o modelo evitará explicitamente gerar tokens de pensamento.\n\nPara utilizar a capacidade de pensamento e receber tokens de pensamento, você deve escolher a variante \":thinking\", o que resultará em um preço de saída mais alto para o pensamento.\n\nAlém disso, o Gemini 2.5 Flash pode ser configurado através do parâmetro \"máximo de tokens para raciocínio\", conforme descrito na documentação (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "O Gemini 2.5 Flash é o modelo principal mais avançado do Google, projetado para raciocínio avançado, codificação, matemática e tarefas científicas. Ele possui a capacidade de 'pensar' embutida, permitindo que forneça respostas com maior precisão e um tratamento de contexto mais detalhado.\n\nNota: Este modelo possui duas variantes: com e sem 'pensamento'. A precificação da saída varia significativamente dependendo da ativação da capacidade de pensamento. Se você escolher a variante padrão (sem o sufixo ':thinking'), o modelo evitará explicitamente gerar tokens de pensamento.\n\nPara aproveitar a capacidade de pensamento e receber tokens de pensamento, você deve escolher a variante ':thinking', que resultará em uma precificação de saída de pensamento mais alta.\n\nAlém disso, o Gemini 2.5 Flash pode ser configurado através do parâmetro 'número máximo de tokens para raciocínio', conforme descrito na documentação (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "O Gemini 2.5 Flash é o modelo principal mais avançado do Google, projetado para raciocínio avançado, codificação, matemática e tarefas científicas. Ele possui a capacidade de 'pensar' embutida, permitindo que forneça respostas com maior precisão e um tratamento de contexto mais detalhado.\n\nNota: Este modelo possui duas variantes: com e sem 'pensamento'. A precificação da saída varia significativamente dependendo da ativação da capacidade de pensamento. Se você escolher a variante padrão (sem o sufixo ':thinking'), o modelo evitará explicitamente gerar tokens de pensamento.\n\nPara aproveitar a capacidade de pensamento e receber tokens de pensamento, você deve escolher a variante ':thinking', que resultará em uma precificação de saída de pensamento mais alta.\n\nAlém disso, o Gemini 2.5 Flash pode ser configurado através do parâmetro 'número máximo de tokens para raciocínio', conforme descrito na documentação (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro é o modelo de pensamento mais avançado do Google, capaz de raciocinar sobre problemas complexos em código, matemática e áreas STEM, além de analisar grandes conjuntos de dados, bases de código e documentos usando contexto extenso."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview é o modelo de pensamento mais avançado do Google, capaz de raciocinar sobre problemas complexos em código, matemática e áreas STEM, além de analisar grandes conjuntos de dados, bases de código e documentos usando contexto extenso."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash oferece capacidades de processamento multimodal otimizadas, adequadas para uma variedade de cenários de tarefas complexas."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro combina as mais recentes tecnologias de otimização, proporcionando uma capacidade de processamento de dados multimodais mais eficiente."}, "google/gemma-2-27b": {"description": "Gemma 2 é um modelo eficiente lançado pelo Google, abrangendo uma variedade de cenários de aplicação, desde pequenos aplicativos até processamento de dados complexos."}, "google/gemma-2-27b-it": {"description": "Gemma 2 continua a filosofia de design leve e eficiente."}, "google/gemma-2-2b-it": {"description": "Modelo leve de ajuste de instruções do Google."}, "google/gemma-2-9b": {"description": "Gemma 2 é um modelo eficiente lançado pelo Google, abrangendo uma variedade de cenários de aplicação, desde pequenos aplicativos até processamento de dados complexos."}, "google/gemma-2-9b-it": {"description": "Gemma 2 é uma série de modelos de texto de código aberto leve da Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 é uma série de modelos de texto de código aberto leve da Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) oferece capacidade básica de processamento de instruções, adequada para aplicações leves."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B é um modelo de linguagem de código aberto do Google que estabelece novos padrões em eficiência e desempenho."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B é um modelo de linguagem de código aberto do <PERSON>, que estabelece novos padrões em eficiência e desempenho."}, "gpt-3.5-turbo": {"description": "O GPT 3.5 Turbo é adequado para uma variedade de tarefas de geração e compreensão de texto, atualmente apontando para gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "O GPT 3.5 Turbo é adequado para uma variedade de tarefas de geração e compreensão de texto, atualmente apontando para gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "O GPT 3.5 Turbo é adequado para uma variedade de tarefas de geração e compreensão de texto, atualmente apontando para gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "O GPT 3.5 Turbo é adequado para uma variedade de tarefas de geração e compreensão de texto, atualmente apontando para gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo, um modelo eficiente fornecido pela OpenAI, adequado para tarefas de chat e geração de texto, suportando chamadas de função paralelas."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k, um modelo de geração de texto de alta capacidade, adequado para tarefas complexas."}, "gpt-4": {"description": "O GPT-4 oferece uma janela de contexto maior, capaz de lidar com entradas de texto mais longas, adequado para cenários que exigem integração ampla de informações e análise de dados."}, "gpt-4-0125-preview": {"description": "O mais recente modelo GPT-4 Turbo possui funcionalidades visuais. Agora, solicitações visuais podem ser feitas usando o modo JSON e chamadas de função. O GPT-4 Turbo é uma versão aprimorada, oferecendo suporte econômico para tarefas multimodais. Ele encontra um equilíbrio entre precisão e eficiência, adequado para aplicações que requerem interação em tempo real."}, "gpt-4-0613": {"description": "O GPT-4 oferece uma janela de contexto maior, capaz de lidar com entradas de texto mais longas, adequado para cenários que exigem integração ampla de informações e análise de dados."}, "gpt-4-1106-preview": {"description": "O mais recente modelo GPT-4 Turbo possui funcionalidades visuais. Agora, solicitações visuais podem ser feitas usando o modo JSON e chamadas de função. O GPT-4 Turbo é uma versão aprimorada, oferecendo suporte econômico para tarefas multimodais. Ele encontra um equilíbrio entre precisão e eficiência, adequado para aplicações que requerem interação em tempo real."}, "gpt-4-32k": {"description": "O GPT-4 oferece uma janela de contexto maior, capaz de lidar com entradas de texto mais longas, adequado para cenários que exigem integração ampla de informações e análise de dados."}, "gpt-4-32k-0613": {"description": "O GPT-4 oferece uma janela de contexto maior, capaz de lidar com entradas de texto mais longas, adequado para cenários que exigem integração ampla de informações e análise de dados."}, "gpt-4-turbo": {"description": "O mais recente modelo GPT-4 Turbo possui funcionalidades visuais. Agora, solicitações visuais podem ser feitas usando o modo JSON e chamadas de função. O GPT-4 Turbo é uma versão aprimorada, oferecendo suporte econômico para tarefas multimodais. Ele encontra um equilíbrio entre precisão e eficiência, adequado para aplicações que requerem interação em tempo real."}, "gpt-4-turbo-2024-04-09": {"description": "O mais recente modelo GPT-4 Turbo possui funcionalidades visuais. Agora, solicitações visuais podem ser feitas usando o modo JSON e chamadas de função. O GPT-4 Turbo é uma versão aprimorada, oferecendo suporte econômico para tarefas multimodais. Ele encontra um equilíbrio entre precisão e eficiência, adequado para aplicações que requerem interação em tempo real."}, "gpt-4-turbo-preview": {"description": "O mais recente modelo GPT-4 Turbo possui funcionalidades visuais. Agora, solicitações visuais podem ser feitas usando o modo JSON e chamadas de função. O GPT-4 Turbo é uma versão aprimorada, oferecendo suporte econômico para tarefas multimodais. Ele encontra um equilíbrio entre precisão e eficiência, adequado para aplicações que requerem interação em tempo real."}, "gpt-4-vision-preview": {"description": "O mais recente modelo GPT-4 Turbo possui funcionalidades visuais. Agora, solicitações visuais podem ser feitas usando o modo JSON e chamadas de função. O GPT-4 Turbo é uma versão aprimorada, oferecendo suporte econômico para tarefas multimodais. Ele encontra um equilíbrio entre precisão e eficiência, adequado para aplicações que requerem interação em tempo real."}, "gpt-4.1": {"description": "GPT-4.1 é nosso modelo principal para tarefas complexas. Ele é muito adequado para resolver problemas interdisciplinares."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini oferece um equilíbrio entre inteligência, velocidade e custo, tornando-se um modelo atraente para muitos casos de uso."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini oferece um equilíbrio entre inteligência, velocidade e custo, tornando-se um modelo atraente para muitos casos de uso."}, "gpt-4.5-preview": {"description": "Versão de pesquisa do GPT-4.5, que é o nosso maior e mais poderoso modelo GPT até agora. Ele possui um amplo conhecimento sobre o mundo e consegue entender melhor a intenção do usuário, destacando-se em tarefas criativas e planejamento autônomo. O GPT-4.5 aceita entradas de texto e imagem, gerando saídas de texto (incluindo saídas estruturadas). Suporta recursos essenciais para desenvolvedores, como chamadas de função, API em lote e saída em fluxo. O GPT-4.5 se destaca especialmente em tarefas que requerem criatividade, pensamento aberto e diálogo (como escrita, aprendizado ou exploração de novas ideias). A data limite do conhecimento é outubro de 2023."}, "gpt-4o": {"description": "O ChatGPT-4o é um modelo dinâmico, atualizado em tempo real para manter a versão mais atual. Ele combina uma poderosa capacidade de compreensão e geração de linguagem, adequado para cenários de aplicação em larga escala, incluindo atendimento ao cliente, educação e suporte técnico."}, "gpt-4o-2024-05-13": {"description": "O ChatGPT-4o é um modelo dinâmico, atualizado em tempo real para manter a versão mais atual. Ele combina uma poderosa capacidade de compreensão e geração de linguagem, adequado para cenários de aplicação em larga escala, incluindo atendimento ao cliente, educação e suporte técnico."}, "gpt-4o-2024-08-06": {"description": "O ChatGPT-4o é um modelo dinâmico, atualizado em tempo real para manter a versão mais atual. Ele combina uma poderosa capacidade de compreensão e geração de linguagem, adequado para cenários de aplicação em larga escala, incluindo atendimento ao cliente, educação e suporte técnico."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o é um modelo dinâmico, atualizado em tempo real para manter a versão mais atualizada. Combina uma poderosa compreensão e capacidade de geração de linguagem, adequado para cenários de aplicação em larga escala, incluindo atendimento ao cliente, educação e suporte técnico."}, "gpt-4o-audio-preview": {"description": "Modelo de áudio GPT-4o, suporta entrada e saída de áudio."}, "gpt-4o-mini": {"description": "O GPT-4o mini é o mais recente modelo lançado pela OpenAI após o GPT-4 Omni, suportando entrada de texto e imagem e gerando texto como saída. Como seu modelo compacto mais avançado, ele é muito mais acessível do que outros modelos de ponta recentes, custando mais de 60% menos que o GPT-3.5 Turbo. Ele mantém uma inteligência de ponta, ao mesmo tempo que oferece um custo-benefício significativo. O GPT-4o mini obteve uma pontuação de 82% no teste MMLU e atualmente está classificado acima do GPT-4 em preferências de chat."}, "gpt-4o-mini-audio-preview": {"description": "Modelo GPT-4o mini Audio, suporta entrada e saída de áudio."}, "gpt-4o-mini-realtime-preview": {"description": "Versão em tempo real do GPT-4o-mini, suporta entrada e saída de áudio e texto em tempo real."}, "gpt-4o-mini-search-preview": {"description": "A versão prévia do GPT-4o mini para busca é um modelo treinado especificamente para compreender e executar consultas de busca na web, utilizando a API Chat Completions. Além dos custos por token, as consultas de busca na web são cobradas por chamada da ferramenta."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe é um modelo de transcrição de áudio para texto que utiliza GPT-4o. Em comparação com o modelo Whisper original, melhora a taxa de erro de palavras, além do reconhecimento e precisão linguística. Use-o para obter transcrições mais precisas."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS é um modelo de texto para fala baseado em GPT-4o mini, oferecendo uma geração de voz de alta qualidade a um custo mais baixo."}, "gpt-4o-realtime-preview": {"description": "Versão em tempo real do GPT-4o, suporta entrada e saída de áudio e texto em tempo real."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Versão em tempo real do GPT-4o, suporta entrada e saída de áudio e texto em tempo real."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Versão em tempo real do GPT-4o, suportando entrada e saída de áudio e texto em tempo real."}, "gpt-4o-search-preview": {"description": "A versão prévia do GPT-4o para busca é um modelo treinado especificamente para compreender e executar consultas de busca na web, utilizando a API Chat Completions. Além dos custos por token, as consultas de busca na web são cobradas por chamada da ferramenta."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe é um modelo de transcrição de áudio para texto que utiliza GPT-4o. Em comparação com o modelo Whisper original, melhora a taxa de erro de palavras, além do reconhecimento e precisão linguística. Use-o para obter transcrições mais precisas."}, "gpt-image-1": {"description": "Modelo nativo multimodal de geração de imagens do ChatGPT"}, "grok-2-1212": {"description": "Este modelo apresenta melhorias em precisão, conformidade com instruções e capacidade multilíngue."}, "grok-2-image-1212": {"description": "Nosso mais recente modelo de geração de imagens pode criar imagens vívidas e realistas a partir de prompts textuais. Apresenta excelente desempenho em marketing, mídias sociais e entretenimento."}, "grok-2-vision-1212": {"description": "Este modelo apresenta melhorias em precisão, conformidade com instruções e capacidade multilíngue."}, "grok-3": {"description": "Modelo de nível flagship, especializado em extração de dados, programação e resumo de texto para aplicações empresariais, com profundo conhecimento em finanças, saúde, direito e ciências."}, "grok-3-fast": {"description": "Modelo de nível flagship, especializado em extração de dados, programação e resumo de texto para aplicações empresariais, com profundo conhecimento em finanças, saúde, direito e ciências."}, "grok-3-mini": {"description": "Modelo leve que pensa antes de responder. Rápido e inteligente, adequado para tarefas lógicas que não exigem conhecimento profundo de domínio, e capaz de fornecer o rastro original do pensamento."}, "grok-3-mini-fast": {"description": "Modelo leve que pensa antes de responder. Rápido e inteligente, adequado para tarefas lógicas que não exigem conhecimento profundo de domínio, e capaz de fornecer o rastro original do pensamento."}, "grok-4": {"description": "Nosso mais recente e poderoso modelo principal, com desempenho excepcional em processamento de linguagem natural, cálculo matemático e raciocínio — um competidor versátil perfeito."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B é um modelo de linguagem que combina criatividade e inteligência, integrando vários modelos de ponta."}, "hunyuan-a13b": {"description": "O primeiro modelo de raciocínio hí<PERSON> Hunyuan, uma versão aprimorada do hunyuan-standard-256K, com 80 bilhões de parâmetros totais e 13 bilhões ativados. O modo padrão é o modo de pensamento lento, com suporte para alternância entre modos rápido e lento via parâmetros ou instruções, usando prefixos query / no_think para alternar. A capacidade geral foi amplamente melhorada em relação à geração anterior, especialmente em matemática, ciências, compreensão de textos longos e habilidades de agente."}, "hunyuan-code": {"description": "O mais recente modelo de geração de código Hunyuan, treinado com 200B de dados de código de alta qualidade, com seis meses de treinamento de dados SFT de alta qualidade, aumentando o comprimento da janela de contexto para 8K, destacando-se em métricas automáticas de geração de código em cinco linguagens; em avaliações de qualidade de código em dez aspectos em cinco linguagens, o desempenho está na primeira divisão."}, "hunyuan-functioncall": {"description": "O mais recente modelo FunctionCall da arquitetura MOE Hunyuan, treinado com dados de alta qualidade de FunctionCall, com uma janela de contexto de 32K, liderando em várias métricas de avaliação."}, "hunyuan-large": {"description": "O modelo Hunyuan-large possui um total de aproximadamente 389B de parâmetros, com cerca de 52B de parâmetros ativados, sendo o modelo MoE de código aberto com a maior escala de parâmetros e melhor desempenho na arquitetura Transformer atualmente disponível no mercado."}, "hunyuan-large-longcontext": {"description": "Especializado em tarefas de texto longo, como resumo de documentos e perguntas e respostas de documentos, também possui a capacidade de lidar com tarefas gerais de geração de texto. Apresenta desempenho excepcional na análise e geração de textos longos, conseguindo atender efetivamente às demandas complexas e detalhadas de processamento de conteúdo longo."}, "hunyuan-large-vision": {"description": "Este modelo é adequado para cenários de compreensão de imagens e texto, baseado no modelo visual-linguístico Hunyuan Large. Suporta entrada de múltiplas imagens em qualquer resolução junto com texto, gerando conteúdo textual, com foco em tarefas relacionadas à compreensão de imagens e texto, apresentando melhorias significativas em capacidades multilíngues."}, "hunyuan-lite": {"description": "Atualizado para uma estrutura MOE, com uma janela de contexto de 256k, liderando em várias avaliações em NLP, código, matemática e setores diversos em comparação com muitos modelos de código aberto."}, "hunyuan-lite-vision": {"description": "Modelo multimodal mais recente de 7B da Hunyuan, com janela de contexto de 32K, suporta diálogos multimodais em cenários em chinês e português, reconhecimento de objetos em imagens, compreensão de documentos e tabelas, matemática multimodal, entre outros, superando modelos concorrentes de 7B em várias métricas de avaliação."}, "hunyuan-pro": {"description": "Modelo de texto longo MOE-32K com trilhões de parâmetros. Alcança níveis de liderança absoluta em vários benchmarks, com capacidades complexas de instrução e raciocínio, habilidades matemáticas complexas, suporte a chamadas de função, otimizado para áreas como tradução multilíngue, finanças, direito e saúde."}, "hunyuan-role": {"description": "O mais recente modelo de interpretação de papéis Hunyuan, um modelo de interpretação de papéis ajustado e treinado oficialmente pela Hunyuan, que combina o modelo Hunyuan com um conjunto de dados de cenários de interpretação de papéis, apresentando um desempenho básico melhor em cenários de interpretação de papéis."}, "hunyuan-standard": {"description": "Adota uma estratégia de roteamento superior, ao mesmo tempo que mitiga problemas de balanceamento de carga e convergência de especialistas. Em termos de textos longos, o índice de precisão atinge 99,9%. O MOE-32K oferece uma relação custo-benefício relativamente melhor, equilibrando desempenho e preço, permitindo o processamento de entradas de texto longo."}, "hunyuan-standard-256K": {"description": "Adota uma estratégia de roteamento superior, ao mesmo tempo que mitiga problemas de balanceamento de carga e convergência de especialistas. Em termos de textos longos, o índice de precisão atinge 99,9%. O MOE-256K rompe ainda mais em comprimento e desempenho, expandindo significativamente o comprimento de entrada permitido."}, "hunyuan-standard-vision": {"description": "Modelo multimodal mais recent<PERSON> da Hunyuan, suporta respostas em múltiplas línguas, com habilidades equilibradas em chinês e português."}, "hunyuan-t1-20250321": {"description": "Modelo abrangente que constrói habilidades em ciências exatas e humanas, com forte capacidade de captura de informações em textos longos. Suporta raciocínio para responder a problemas científicos de diversas dificuldades, incluindo matemática, lógica, ciências e código."}, "hunyuan-t1-20250403": {"description": "Melhore a capacidade de geração de código em nível de projeto; aumente a qualidade da escrita gerada em texto; aprimore a compreensão de tópicos em múltiplas rodadas, a conformidade com instruções do tipo tob e a compreensão de palavras; otimize problemas de saída com mistura de caracteres tradicionais e simplificados, bem como misturas de chinês e inglês."}, "hunyuan-t1-20250529": {"description": "Otimizado para criação de textos, redação de ensaios, aprimoramento em front-end de código, matemática, raciocínio lógico e outras habilidades científicas, além de melhorar a capacidade de seguir instruções."}, "hunyuan-t1-20250711": {"description": "Melhora significativa em matemática avançada, lógica e habilidades de codificação, otimiza a estabilidade da saída do modelo e aprimora a capacidade de lidar com textos longos."}, "hunyuan-t1-latest": {"description": "O primeiro modelo de inferência Hybrid-Transformer-Mamba em larga escala da indústria, que expande a capacidade de inferência, possui uma velocidade de decodificação excepcional e alinha-se ainda mais às preferências humanas."}, "hunyuan-t1-vision": {"description": "Modelo de pensamento profundo multimodal Hunyuan, suporta cadeias de pensamento nativas multimodais de longo alcance, excelente em diversos cenários de raciocínio com imagens, com melhorias significativas em problemas científicos em comparação com modelos de pensamento rápido."}, "hunyuan-t1-vision-20250619": {"description": "A versão mais recente do modelo de pensamento profundo multimodal t1-vision da Hunyuan, que suporta cadeias de pensamento nativas multimodais, com melhorias abrangentes em relação à versão padrão anterior."}, "hunyuan-turbo": {"description": "Versão de pré-visualização do novo modelo de linguagem de próxima geração Hunyuan, utilizando uma nova estrutura de modelo de especialistas mistos (MoE), com eficiência de inferência mais rápida e desempenho superior em comparação ao Hunyuan-Pro."}, "hunyuan-turbo-20241223": {"description": "Esta versão otimiza: escalonamento de instruções de dados, aumentando significativamente a capacidade de generalização do modelo; melhoria substancial nas habilidades matemáticas, de codificação e de raciocínio lógico; otimização das capacidades de compreensão de texto e palavras; melhoria na qualidade da geração de conteúdo de criação de texto."}, "hunyuan-turbo-latest": {"description": "Otimização da experiência geral, incluindo compreensão de NLP, criação de texto, conversas informais, perguntas e respostas de conhecimento, tradução, entre outros; aumento da humanização, otimização da inteligência emocional do modelo; melhoria na capacidade do modelo de esclarecer ativamente em casos de intenção ambígua; aprimoramento na capacidade de lidar com questões de análise de palavras; melhoria na qualidade e interatividade da criação; aprimoramento da experiência em múltiplas interações."}, "hunyuan-turbo-vision": {"description": "Novo modelo de linguagem visual de próxima geração da Hunyuan, adotando uma nova estrutura de modelo de especialistas mistos (MoE), com melhorias abrangentes em relação ao modelo anterior nas capacidades de reconhecimento básico, criação de conteúdo, perguntas e respostas de conhecimento, e análise e raciocínio relacionados à compreensão de texto e imagem."}, "hunyuan-turbos-20250313": {"description": "Uniformização do estilo dos passos para resolução de problemas matemáticos, reforçando perguntas e respostas em múltiplas rodadas na matemática. Otimização do estilo de resposta na criação de textos, eliminando traços de IA e aumentando a expressividade literária."}, "hunyuan-turbos-20250416": {"description": "Atualização da base pré-treinada para fortalecer a compreensão e conformidade com instruções; aprimoramento das habilidades em matemática, código, lógica e ciências exatas na fase de alinhamento; melhoria da qualidade da escrita criativa, compreensão textual, precisão na tradução e respostas a perguntas em ciências humanas; fortalecimento das capacidades de agentes em diversas áreas, com foco especial na compreensão de diálogos em múltiplas rodadas."}, "hunyuan-turbos-20250604": {"description": "Atualização da base pré-treinada, com melhorias em escrita e compreensão de leitura, aumento significativo nas habilidades de código e ciências, e aprimoramento contínuo no seguimento de instruções complexas."}, "hunyuan-turbos-latest": {"description": "A versão mais recente do hunyuan-TurboS, o modelo de grande porte da Hunyuan, possui uma capacidade de raciocínio mais forte e uma experiência aprimorada."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Especializado em tarefas de texto longo, como resumos de documentos e perguntas sobre documentos, também possui a capacidade de lidar com tarefas gerais de geração de texto. Destaca-se na análise e geração de textos longos, atendendo efetivamente a demandas complexas e detalhadas."}, "hunyuan-turbos-role-plus": {"description": "Modelo de interpretação de papéis da versão mais recente do Hunyuan, ajustado finamente pela equipe oficial Hunyuan. Baseado no modelo Hunyuan e treinado adicionalmente com conjuntos de dados de cenários de interpretação de papéis, oferecendo melhores resultados básicos nesses contextos."}, "hunyuan-turbos-vision": {"description": "Este modelo é adequado para cenários de compreensão de imagens e texto, baseado na mais recente geração turbos da Hunyuan, um modelo de linguagem visual flagship focado em tarefas relacionadas à compreensão de imagens e texto, incluindo reconhecimento de entidades em imagens, perguntas e respostas baseadas em conhecimento, criação de textos e resolução de problemas por foto, com melhorias abrangentes em relação à geração anterior."}, "hunyuan-turbos-vision-20250619": {"description": "A versão mais recente do modelo flagship de linguagem visual turbos-vision da Hunyuan, com melhorias abrangentes em tarefas relacionadas à compreensão de imagens e texto, incluindo reconhecimento de entidades em imagens, perguntas e respostas baseadas em conhecimento, criação de textos e resolução de problemas por foto, em comparação com a versão padrão anterior."}, "hunyuan-vision": {"description": "O mais recente modelo multimodal Hunyuan, que suporta a entrada de imagens e texto para gerar conteúdo textual."}, "image-01": {"description": "Novo modelo de geração de imagens com detalhes refinados, suportando geração de imagens a partir de texto e de outras imagens."}, "image-01-live": {"description": "Modelo de geração de imagens com detalhes refinados, suportando geração a partir de texto e configuração de estilo visual."}, "imagen-4.0-generate-preview-06-06": {"description": "Série de modelos de texto para imagem da 4ª geração Imagen"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Série de modelos de texto para imagem da 4ª geração Imagen, versão Ultra"}, "imagen4/preview": {"description": "Modelo de geração de imagens de mais alta qualidade do Google"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 oferece soluções de diálogo inteligente em múltiplos cenários."}, "internlm2.5-latest": {"description": "Nossa mais recente série de modelos, com desempenho de raciocínio excepcional, suportando um comprimento de contexto de 1M e capacidades aprimoradas de seguimento de instruções e chamadas de ferramentas."}, "internlm3-latest": {"description": "Nossa mais recente série de modelos, com desempenho de inferência excepcional, liderando entre modelos de código aberto de mesma escala. Aponta por padrão para nossa mais recente série de modelos InternLM3."}, "internvl2.5-latest": {"description": "Versão InternVL2.5 que ainda estamos mantendo, com desempenho excelente e estável. Aponta por padrão para nossa mais recente série de modelos InternVL2.5, atualmente direcionando para internvl2.5-78b."}, "internvl3-latest": {"description": "Lançamos nosso mais recente modelo multimodal, com habilidades aprimoradas de compreensão de texto e imagem, e capacidade de entender imagens em longas sequências, com desempenho comparável aos melhores modelos fechados. Aponta por padrão para nossa mais recente série de modelos InternVL, atualmente direcionando para internvl3-78b."}, "irag-1.0": {"description": "iRAG (image based RAG) desenvolvido pela Baidu, tecnologia de geração de imagens baseada em recuperação, que combina recursos de bilhões de imagens do Baidu Search com poderosos modelos base, gerando imagens ultra-realistas que superam sistemas nativos de geração de imagens, eliminando o aspecto artificial da IA e com baixo custo. iRAG é caracterizado por ausência de alucinações, ultra-realismo e resultados imediatos."}, "jamba-large": {"description": "Nosso modelo mais poderoso e avançado, projetado para lidar com tarefas complexas em nível empresarial, com desempenho excepcional."}, "jamba-mini": {"description": "O modelo mais eficiente da sua categoria, equilibrando velocidade e qualidade, com um tamanho menor."}, "jina-deepsearch-v1": {"description": "A busca profunda combina pesquisa na web, leitura e raciocínio para realizar investigações abrangentes. Você pode vê-la como um agente que aceita suas tarefas de pesquisa - ela realizará uma busca extensa e passará por várias iterações antes de fornecer uma resposta. Esse processo envolve pesquisa contínua, raciocínio e resolução de problemas sob diferentes ângulos. Isso é fundamentalmente diferente de gerar respostas diretamente a partir de dados pré-treinados de grandes modelos padrão e de sistemas RAG tradicionais que dependem de buscas superficiais únicas."}, "kimi-k2": {"description": "Kimi-K2 é um modelo base com arquitetura MoE lançado pela Moonshot AI, com capacidades avançadas de código e agente, totalizando 1 trilhão de parâmetros e 32 bilhões ativados. Em testes de desempenho em raciocínio geral, programação, matemática e agentes, supera outros modelos open source populares."}, "kimi-k2-0711-preview": {"description": "kimi-k2 é um modelo base com arquitetura MoE, com capacidades excepcionais em código e agentes, totalizando 1T de parâmetros e 32B de parâmetros ativados. Nos principais benchmarks de raciocínio de conhecimento geral, programação, matemática e agentes, o modelo K2 supera outros modelos open source populares."}, "kimi-latest": {"description": "O produto assistente inteligente Kimi utiliza o mais recente modelo Kimi, que pode conter recursos ainda não estáveis. Suporta compreensão de imagens e seleciona automaticamente o modelo de cobrança de 8k/32k/128k com base no comprimento do contexto da solicitação."}, "kimi-thinking-preview": {"description": "O modelo kimi-thinking-preview, fornecido pela Face Oculta da Lua, é um modelo multimodal de pensamento com capacidades de raciocínio multimodal e geral, especializado em raciocínio profundo para ajudar a resolver problemas mais complexos."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM é um modelo de linguagem experimental e específico para tarefas, treinado para atender aos princípios da ciência da aprendizagem, podendo seguir instruções sistemáticas em cenários de ensino e aprendizagem, atuando como um mentor especialista, entre outros."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM é um modelo de linguagem experimental, específico para tarefas, treinado para atender aos princípios da ciência da aprendizagem, capaz de seguir instruções sistemáticas em cenários de ensino e aprendizagem, atuando como um mentor especialista, entre outros."}, "lite": {"description": "Spark Lite é um modelo de linguagem grande leve, com latência extremamente baixa e alta eficiência de processamento, totalmente gratuito e aberto, suportando funcionalidades de busca online em tempo real. Sua característica de resposta rápida o torna excelente para aplicações de inferência em dispositivos de baixo poder computacional e ajuste fino de modelos, proporcionando aos usuários uma excelente relação custo-benefício e experiência inteligente, especialmente em cenários de perguntas e respostas, geração de conteúdo e busca."}, "llama-2-7b-chat": {"description": "Llama2 é uma série de modelos de linguagem grandes (LLM) desenvolvidos e open source pela Meta, que inclui modelos de texto gerativo pré-treinados e finetunados com escalas variando de 7 bilhões a 70 bilhões de parâmetros. Do ponto de vista arquitetural, o Llama2 é um modelo de linguagem autoregressivo que utiliza uma arquitetura de transformador otimizada. As versões ajustadas utilizam micro-treinamento supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para alinhar as preferências de utilidade e segurança humanas. O Llama2 apresenta um desempenho notável em vários conjuntos de dados acadêmicos, fornecendo inspiração para o design e desenvolvimento de muitos outros modelos."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B oferece capacidade de raciocínio AI mais poderosa, adequada para aplicações complexas, suportando um processamento computacional extenso e garantindo eficiência e precisão."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B é um modelo de alto desempenho, oferecendo capacidade de geração de texto rápida, ideal para cenários de aplicação que exigem eficiência em larga escala e custo-benefício."}, "llama-3.1-instruct": {"description": "O modelo Llama 3.1 com ajuste fino de instruções foi otimizado para cenários de diálogo, superando muitos modelos de chat de código aberto existentes em benchmarks comuns do setor."}, "llama-3.2-11b-vision-instruct": {"description": "Capacidade excepcional de raciocínio visual em imagens de alta resolução, adequada para aplicações de compreensão visual."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "llama-3.2-90b-vision-instruct": {"description": "Capacidade avançada de raciocínio visual para aplicações de agentes de compreensão visual."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "llama-3.2-vision-instruct": {"description": "O modelo Llama 3.2-Vision com ajuste fino de instruções foi otimizado para reconhecimento visual, raciocínio com imagens, descrição de imagens e respostas a perguntas gerais relacionadas a imagens."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 é o modelo de linguagem de código aberto multilíngue mais avançado da série Llama, oferecendo desempenho comparável ao modelo 405B a um custo extremamente baixo. Baseado na estrutura Transformer, e aprimorado por meio de ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para aumentar a utilidade e a segurança. Sua versão ajustada para instruções é otimizada para diálogos multilíngues, superando muitos modelos de chat de código aberto e fechado em vários benchmarks da indústria. A data limite de conhecimento é dezembro de 2023."}, "llama-3.3-70b-versatile": {"description": "O modelo de linguagem multilíngue Meta Llama 3.3 (LLM) é um modelo gerador pré-treinado e ajustado para instruções, com 70B (entrada/saída de texto). O modelo de texto puro ajustado para instruções do Llama 3.3 é otimizado para casos de uso de diálogo multilíngue e supera muitos modelos de chat open source e fechados disponíveis em benchmarks comuns da indústria."}, "llama-3.3-instruct": {"description": "O modelo Llama 3.3 com ajuste fino de instruções foi otimizado para cenários de diálogo, superando muitos modelos de chat open-source existentes em benchmarks comuns do setor."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B oferece capacidade de processamento incomparável para complexidade, projetado sob medida para projetos de alta demanda."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B oferece desempenho de raciocínio de alta qualidade, adequado para uma variedade de necessidades de aplicação."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use oferece poderosa capacidade de chamada de ferramentas, suportando o processamento eficiente de tarefas complexas."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use é um modelo otimizado para uso eficiente de ferramentas, suportando cálculos paralelos rápidos."}, "llama3.1": {"description": "Llama 3.1 é um modelo líder lançado pela Meta, suportando até 405B de parâmetros, aplicável em diálogos complexos, tradução multilíngue e análise de dados."}, "llama3.1:405b": {"description": "Llama 3.1 é um modelo líder lançado pela Meta, suportando até 405B de parâmetros, aplicável em diálogos complexos, tradução multilíngue e análise de dados."}, "llama3.1:70b": {"description": "Llama 3.1 é um modelo líder lançado pela Meta, suportando até 405B de parâmetros, aplicável em diálogos complexos, tradução multilíngue e análise de dados."}, "llava": {"description": "LLaVA é um modelo multimodal que combina um codificador visual e Vicuna, projetado para forte compreensão visual e linguística."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B oferece capacidade de processamento visual integrada, gerando saídas complexas a partir de informações visuais."}, "llava:13b": {"description": "LLaVA é um modelo multimodal que combina um codificador visual e Vicuna, projetado para forte compreensão visual e linguística."}, "llava:34b": {"description": "LLaVA é um modelo multimodal que combina um codificador visual e Vicuna, projetado para forte compreensão visual e linguística."}, "mathstral": {"description": "MathΣtral é projetado para pesquisa científica e raciocínio <PERSON>, oferecendo capacidade de cálculo eficaz e interpretação de resultados."}, "max-32k": {"description": "Spark Max 32K possui uma capacidade de processamento de contexto grande, com melhor compreensão de contexto e capacidade de raciocínio lógico, suportando entradas de texto de 32K tokens, adequado para leitura de documentos longos, perguntas e respostas de conhecimento privado e outros cenários."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct é um modelo de linguagem grande treinado de forma totalmente autônoma pela Wúwèn Xīnqióng. O Megrez-3B-Instruct visa criar uma solução de inteligência de borda rápida, compacta e fácil de usar, através do conceito de integração de hardware e software."}, "meta-llama-3-70b-instruct": {"description": "Um poderoso modelo com 70 bilhões de parâmetros, destacando-se em raciocínio, codificação e amplas aplicações linguísticas."}, "meta-llama-3-8b-instruct": {"description": "Um modelo versátil com 8 bilhões de parâmetros, otimizado para tarefas de diálogo e geração de texto."}, "meta-llama-3.1-405b-instruct": {"description": "Os modelos de texto apenas ajustados por instrução Llama 3.1 são otimizados para casos de uso de diálogo multilíngue e superam muitos dos modelos de chat de código aberto e fechado disponíveis em benchmarks comuns da indústria."}, "meta-llama-3.1-70b-instruct": {"description": "Os modelos de texto apenas ajustados por instrução Llama 3.1 são otimizados para casos de uso de diálogo multilíngue e superam muitos dos modelos de chat de código aberto e fechado disponíveis em benchmarks comuns da indústria."}, "meta-llama-3.1-8b-instruct": {"description": "Os modelos de texto apenas ajustados por instrução Llama 3.1 são otimizados para casos de uso de diálogo multilíngue e superam muitos dos modelos de chat de código aberto e fechado disponíveis em benchmarks comuns da indústria."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) oferece excelente capacidade de processamento de linguagem e uma experiência interativa notável."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 oferece excelente capacidade de processamento de linguagem e uma experiência interativa excepcional."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) é um modelo de chat poderoso, suportando necessidades de diálogo complexas."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) oferece suporte multilíngue, abrangendo um rico conhecimento em diversas áreas."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "O Meta Llama 3.3 é um modelo de linguagem de grande escala multilíngue (LLM) com 70B (entrada/saída de texto) que é um modelo gerado por pré-treinamento e ajuste de instruções. O modelo de texto puro ajustado por instruções do Llama 3.3 foi otimizado para casos de uso de diálogo multilíngue e supera muitos modelos de chat de código aberto e fechados disponíveis em benchmarks de indústria comuns."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite é ideal para ambientes que exigem alta eficiência e baixa latência."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo oferece uma capacidade excepcional de compreensão e geração de linguagem, adequado para as tarefas computacionais mais exigentes."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite é adequado para ambientes com recursos limitados, oferecendo um excelente equilíbrio de desempenho."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo é um modelo de linguagem de alto desempenho, suportando uma ampla gama de cenários de aplicação."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B é um modelo poderoso para pré-treinamento e ajuste de instruções."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "O modelo Llama 3.1 Turbo 405B oferece suporte a um contexto de capacidade extremamente grande para processamento de grandes volumes de dados, destacando-se em aplicações de inteligência artificial em larga escala."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 é o modelo líder lançado pela Meta, suportando até 405B de parâmetros, aplicável em diálogos complexos, tradução multilíngue e análise de dados."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "O modelo Llama 3.1 70B é ajustado para aplicações de alta carga, quantizado para FP8, oferecendo maior eficiência computacional e precisão, garantindo desempenho excepcional em cenários complexos."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "O modelo Llama 3.1 8B utiliza quantização FP8, suportando até 131.072 tokens de contexto, destacando-se entre os modelos de código aberto, ideal para tarefas complexas e superando muitos benchmarks do setor."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct é otimizado para cenários de diálogo de alta qualidade, apresentando desempenho excepcional em várias avaliações humanas."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct otimiza cenários de diálogo de alta qualidade, com desempenho superior a muitos modelos fechados."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct é projetado para diálogos de alta qualidade, destacando-se em avaliações humanas, especialmente em cenários de alta interação."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct é a versão mais recente lançada pela Meta, otimizada para cenários de diálogo de alta qualidade, superando muitos modelos fechados de ponta."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 oferece suporte multilíngue e é um dos modelos geradores líderes do setor."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 é projetado para lidar com tarefas que combinam dados visuais e textuais. Ele se destaca em tarefas como descrição de imagens e perguntas visuais, superando a lacuna entre geração de linguagem e raciocínio visual."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 é o modelo de linguagem de código aberto multilíngue mais avançado da série Llama, oferecendo desempenho comparável ao modelo 405B a um custo extremamente baixo. Baseado na estrutura Transformer, e aprimorado por meio de ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para aumentar a utilidade e a segurança. Sua versão ajustada para instruções é otimizada para diálogos multilíngues, superando muitos modelos de chat de código aberto e fechado em vários benchmarks da indústria. A data limite de conhecimento é dezembro de 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 é o modelo de linguagem de código aberto multilíngue mais avançado da série Llama, oferecendo desempenho comparável ao modelo 405B a um custo extremamente baixo. Baseado na estrutura Transformer, e aprimorado por meio de ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para aumentar a utilidade e a segurança. Sua versão ajustada para instruções é otimizada para diálogos multilíngues, superando muitos modelos de chat de código aberto e fechado em vários benchmarks da indústria. A data limite de conhecimento é dezembro de 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct é o maior e mais poderoso modelo da série Llama 3.1 Instruct, sendo um modelo altamente avançado para raciocínio conversacional e geração de dados sintéticos, que também pode ser usado como base para pré-treinamento ou ajuste fino em domínios específicos. Os modelos de linguagem de grande escala (LLMs) multilíngues oferecidos pelo Llama 3.1 são um conjunto de modelos geradores pré-treinados e ajustados por instruções, incluindo tamanhos de 8B, 70B e 405B (entrada/saída de texto). Os modelos de texto ajustados por instruções do Llama 3.1 (8B, 70B, 405B) são otimizados para casos de uso de diálogo multilíngue e superaram muitos modelos de chat de código aberto disponíveis em benchmarks comuns da indústria. O Llama 3.1 é projetado para uso comercial e de pesquisa em várias línguas. Os modelos de texto ajustados por instruções são adequados para chats semelhantes a assistentes, enquanto os modelos pré-treinados podem se adaptar a várias tarefas de geração de linguagem natural. O modelo Llama 3.1 também suporta a utilização de sua saída para melhorar outros modelos, incluindo geração de dados sintéticos e refinamento. O Llama 3.1 é um modelo de linguagem autoregressivo que utiliza uma arquitetura de transformador otimizada. As versões ajustadas utilizam ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para alinhar-se às preferências humanas em relação à utilidade e segurança."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "A versão atualizada do Meta Llama 3.1 70B Instruct, incluindo um comprimento de contexto expandido de 128K, multilinguismo e capacidades de raciocínio aprimoradas. Os modelos de linguagem de grande porte (LLMs) do Llama 3.1 são um conjunto de modelos geradores pré-treinados e ajustados por instruções, incluindo tamanhos de 8B, 70B e 405B (entrada/saída de texto). Os modelos de texto ajustados por instruções do Llama 3.1 (8B, 70B, 405B) são otimizados para casos de uso de diálogo multilíngue e superaram muitos modelos de chat de código aberto disponíveis em benchmarks de indústria comuns. O Llama 3.1 é projetado para uso comercial e de pesquisa em várias línguas. Os modelos de texto ajustados por instruções são adequados para chats semelhantes a assistentes, enquanto os modelos pré-treinados podem se adaptar a várias tarefas de geração de linguagem natural. O modelo Llama 3.1 também suporta a utilização de suas saídas para melhorar outros modelos, incluindo geração de dados sintéticos e refinamento. O Llama 3.1 é um modelo de linguagem autoregressivo usando uma arquitetura de transformador otimizada. As versões ajustadas utilizam ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para alinhar-se às preferências humanas por ajuda e segurança."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "A versão atualizada do Meta Llama 3.1 8B Instruct, incluindo um comprimento de contexto expandido de 128K, multilinguismo e capacidades de raciocínio aprimoradas. Os modelos de linguagem de grande porte (LLMs) do Llama 3.1 são um conjunto de modelos geradores pré-treinados e ajustados por instruções, incluindo tamanhos de 8B, 70B e 405B (entrada/saída de texto). Os modelos de texto ajustados por instruções do Llama 3.1 (8B, 70B, 405B) são otimizados para casos de uso de diálogo multilíngue e superaram muitos modelos de chat de código aberto disponíveis em benchmarks de indústria comuns. O Llama 3.1 é projetado para uso comercial e de pesquisa em várias línguas. Os modelos de texto ajustados por instruções são adequados para chats semelhantes a assistentes, enquanto os modelos pré-treinados podem se adaptar a várias tarefas de geração de linguagem natural. O modelo Llama 3.1 também suporta a utilização de suas saídas para melhorar outros modelos, incluindo geração de dados sintéticos e refinamento. O Llama 3.1 é um modelo de linguagem autoregressivo usando uma arquitetura de transformador otimizada. As versões ajustadas utilizam ajuste fino supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para alinhar-se às preferências humanas por ajuda e segurança."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 é um modelo de linguagem de grande escala (LLM) aberto voltado para desenvolvedores, pesquisadores e empresas, projetado para ajudá-los a construir, experimentar e expandir suas ideias de IA geradora de forma responsável. Como parte de um sistema de base para inovação da comunidade global, é ideal para criação de conteúdo, IA de diálogo, compreensão de linguagem, P&D e aplicações empresariais."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 é um modelo de linguagem de grande escala (LLM) aberto voltado para desenvolvedores, pesquisadores e empresas, projetado para ajudá-los a construir, experimentar e expandir suas ideias de IA geradora de forma responsável. Como parte de um sistema de base para inovação da comunidade global, é ideal para dispositivos de borda com capacidade de computação e recursos limitados, além de tempos de treinamento mais rápidos."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Capacidades avançadas de raciocínio visual em imagens de alta resolução, adequado para aplicações de compreensão visual."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Capacidades avançadas de raciocínio visual para aplicações de agentes de compreensão visual."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 é o modelo de linguagem grande multilíngue open source mais avançado da série Llama, oferecendo desempenho comparável a modelos de 405B a um custo muito baixo. Baseado na arquitetura Transformer, aprimorado por fine-tuning supervisionado (SFT) e aprendizado por reforço com feedback humano (RLHF) para melhorar utilidade e segurança. A versão ajustada para instruções é otimizada para diálogos multilíngues e supera muitos modelos de chat open source e proprietários em vários benchmarks do setor. Data de corte do conhecimento: dezembro de 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Um poderoso modelo de 70 bilhões de parâmetros, com desempenho excelente em raciocínio, codificação e ampla gama de aplicações linguísticas."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Um modelo versátil de 8 bilhões de parâmetros, otimizado para tarefas de diálogo e geração de texto."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Modelo de texto ajustado para instruções Llama 3.1, otimizado para casos de uso de diálogo multilíngue, com desempenho superior em benchmarks comuns do setor entre muitos modelos de chat open source e proprietários disponíveis."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Modelo de texto ajustado para instruções Llama 3.1, otimizado para casos de uso de diálogo multilíngue, com desempenho superior em benchmarks comuns do setor entre muitos modelos de chat open source e proprietários disponíveis."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Modelo de texto ajustado para instruções Llama 3.1, otimizado para casos de uso de diálogo multilíngue, com desempenho superior em benchmarks comuns do setor entre muitos modelos de chat open source e proprietários disponíveis."}, "meta/llama-3.1-405b-instruct": {"description": "LLM avançado, suporta geração de dados sintéticos, destilação de conhecimento e raciocínio, adequado para chatbots, programação e tarefas de domínio específico."}, "meta/llama-3.1-70b-instruct": {"description": "Capacita diálogos complexos, com excelente compreensão de contexto, capacidade de raciocínio e geração de texto."}, "meta/llama-3.1-8b-instruct": {"description": "Modelo de ponta avançado, com compreensão de linguagem, excelente capacidade de raciocínio e geração de texto."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Modelo de visão-linguagem de ponta, especializado em raciocínio de alta qualidade a partir de imagens."}, "meta/llama-3.2-1b-instruct": {"description": "Modelo de linguagem de ponta avançado e compacto, com compreensão de linguagem, excelente capacidade de raciocínio e geração de texto."}, "meta/llama-3.2-3b-instruct": {"description": "Modelo de linguagem de ponta avançado e compacto, com compreensão de linguagem, excelente capacidade de raciocínio e geração de texto."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Modelo de visão-linguagem de ponta, especializado em raciocínio de alta qualidade a partir de imagens."}, "meta/llama-3.3-70b-instruct": {"description": "Modelo LLM avançado, especializado em raciocínio, matemática, conhecimento geral e chamadas de função."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "O mesmo modelo Phi-3-medium, mas com contexto maior, adequado para RAG ou poucos prompts."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Um modelo de 14 bilhões de parâmetros, com qualidade superior ao Phi-3-mini, focado em dados de alta qualidade e raciocínio intensivo."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "O mesmo modelo Phi-3-mini, mas com contexto maior, adequado para RAG ou poucos prompts."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "O menor membro da família Phi-3, otimizado para qualidade e baixa latência."}, "microsoft/Phi-3-small-128k-instruct": {"description": "O mesmo modelo Phi-3-small, mas com contexto maior, adequado para RAG ou poucos prompts."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Um modelo de 7 bilhões de parâmetros, com qualidade superior ao Phi-3-mini, focado em dados de alta qualidade e raciocínio intensivo."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Versão atualizada do modelo Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Versão atualizada do modelo Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 é um modelo de linguagem fornecido pela Microsoft AI, que se destaca em diálogos complexos, multilíngue, raciocínio e assistentes inteligentes."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B é o modelo Wizard mais avançado da Microsoft, demonstrando um desempenho extremamente competitivo."}, "minicpm-v": {"description": "MiniCPM-V é a nova geração de grandes modelos multimodais lançada pela OpenBMB, com excelente capacidade de reconhecimento de OCR e compreensão multimodal, suportando uma ampla gama de cenários de aplicação."}, "ministral-3b-latest": {"description": "Ministral 3B é o modelo de ponta da Mistral para aplicações de edge computing."}, "ministral-8b-latest": {"description": "Ministral 8B é o modelo de edge computing altamente custo-efetivo da Mistral."}, "mistral": {"description": "Mistral é um modelo de 7B lançado pela Mistral AI, adequado para demandas de processamento de linguagem variáveis."}, "mistral-ai/Mistral-Large-2411": {"description": "O modelo principal da Mistral, ideal para tarefas complexas que requerem raciocínio em grande escala ou alta especialização (geração de texto sintético, geração de código, RAG ou agentes)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo é um modelo de linguagem avançado (LLM) que oferece capacidades de raciocínio, conhecimento mundial e codificação líderes em sua categoria de tamanho."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small é adequado para qualquer tarefa baseada em linguagem que exija alta eficiência e baixa latência."}, "mistral-large": {"description": "Mixtral Large é o modelo de destaque da Mistral, combinando capacidades de geração de código, matemática e raciocínio, suportando uma janela de contexto de 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 é um modelo avançado de linguagem densa (LLM) com 123 bilhões de parâmetros, oferecendo capacidades de raciocínio, conhecimento e codificação de última geração."}, "mistral-large-latest": {"description": "Mistral Large é o modelo de destaque, especializado em tarefas multilíngues, raciocínio complexo e geração de código, sendo a escolha ideal para aplicações de alto nível."}, "mistral-medium-latest": {"description": "O Mistral Medium 3 oferece desempenho de ponta a um custo 8 vezes menor e simplifica fundamentalmente a implantação empresarial."}, "mistral-nemo": {"description": "Mistral Nemo é um modelo de 12B desenvolvido em colaboração entre a Mistral AI e a NVIDIA, oferecendo desempenho eficiente."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 é um modelo de linguagem grande (LLM) ajustado para instruções, baseado no Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small pode ser usado em qualquer tarefa baseada em linguagem que exija alta eficiência e baixa latência."}, "mistral-small-latest": {"description": "Mistral Small é uma opção de alto custo-benefício, r<PERSON><PERSON><PERSON> e confiável, adequada para casos de uso como tradução, resumo e análise de sentimentos."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct é conhecido por seu alto desempenho, adequado para diversas tarefas de linguagem."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B é um modelo ajustado sob demanda, oferecendo respostas otimizadas para tarefas."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 oferece capacidade computacional eficiente e compreensão de linguagem natural, adequada para uma ampla gama de aplicações."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B é um modelo compacto, mas de alto desempenho, especializado em processamento em lote e tarefas simples, como classificação e geração de texto, com boa capacidade de raciocínio."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) é um super modelo de linguagem, suportando demandas de processamento extremamente altas."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B é um modelo de especialistas esparsos pré-treinados, utilizado para tarefas de texto de uso geral."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B é um modelo de especialistas esparsos, que utiliza múltiplos parâmetros para aumentar a velocidade de raciocínio, ideal para tarefas de geração de código e multilíngues."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct é um modelo de padrão industrial de alto desempenho, com otimização de velocidade e suporte a longos contextos."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo é um modelo de 7.3B parâmetros com suporte multilíngue e programação de alto desempenho."}, "mixtral": {"description": "Mixtral é o modelo de especialistas da Mistral AI, com pesos de código aberto, oferecendo suporte em geração de código e compreensão de linguagem."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B oferece alta capacidade de computação paralela com tolerância a falhas, adequado para tarefas complexas."}, "mixtral:8x22b": {"description": "Mixtral é o modelo de especialistas da Mistral AI, com pesos de código aberto, oferecendo suporte em geração de código e compreensão de linguagem."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K é um modelo com capacidade de processamento de contexto ultra longo, adequado para gerar textos muito longos, atendendo a demandas complexas de geração, capaz de lidar com até 128.000 tokens, ideal para pesquisa, acadêmicos e geração de documentos extensos."}, "moonshot-v1-128k-vision-preview": {"description": "O modelo visual Kimi (incluindo moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, etc.) é capaz de entender o conteúdo das imagens, incluindo texto, cores e formas dos objetos."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K oferece capacidade de processamento de contexto de comprimento médio, capaz de lidar com 32.768 tokens, especialmente adequado para gerar vários documentos longos e diálogos complexos, aplicável em criação de conteúdo, geração de relatórios e sistemas de diálogo."}, "moonshot-v1-32k-vision-preview": {"description": "O modelo visual Kimi (incluindo moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, etc.) é capaz de entender o conteúdo das imagens, incluindo texto, cores e formas dos objetos."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K é projetado para tarefas de geração de texto curto, com desempenho de processamento eficiente, capaz de lidar com 8.192 tokens, ideal para diálogos curtos, anotações e geração rápida de conteúdo."}, "moonshot-v1-8k-vision-preview": {"description": "O modelo visual Kimi (incluindo moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, etc.) é capaz de entender o conteúdo das imagens, incluindo texto, cores e formas dos objetos."}, "moonshot-v1-auto": {"description": "O Moonshot V1 Auto pode escolher o modelo adequado com base na quantidade de Tokens ocupados pelo contexto atual."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B é um modelo de código aberto de grande porte, otimizado por meio de aprendizado reforçado em larga escala, capaz de gerar patches robustos e prontos para produção. Este modelo alcançou uma nova pontuação máxima de 60,4% no SWE-bench Verified, estabelecendo um recorde entre modelos de código aberto em tarefas automatizadas de engenharia de software, como correção de defeitos e revisão de código."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 é um modelo base com arquitetura MoE e capacidades avançadas de código e agente, com 1 trilhão de parâmetros totais e 32 bilhões ativados. Em testes de desempenho em raciocínio geral, programação, matemática e agentes, o modelo K2 supera outros modelos open source populares."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 é um modelo base com arquitetura MoE que possui capacidades avançadas de código e agente, com um total de 1 trilhão de parâmetros e 32 bilhões de parâmetros ativados. Nos testes de desempenho de referência em categorias principais como raciocínio de conhecimento geral, programação, matemática e agentes, o modelo K2 supera outros modelos open source populares."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B é uma versão aprimorada do Nous Hermes 2, contendo os conjuntos de dados mais recentes desenvolvidos internamente."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B é um modelo de linguagem em larga escala personalizado pela NVIDIA, projetado para aumentar a utilidade das respostas geradas pelo LLM em relação às consultas dos usuários. Este modelo se destacou em benchmarks como Arena Hard, AlpacaEval 2 LC e GPT-4-Turbo MT-Bench, ocupando o primeiro lugar em todos os três benchmarks de alinhamento automático até 1º de outubro de 2024. O modelo foi treinado usando RLHF (especialmente REINFORCE), Llama-3.1-Nemotron-70B-Reward e HelpSteer2-Preference prompts, com base no modelo Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Modelo de linguagem único, oferecendo precisão e eficiência incomparáveis."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct é um modelo de linguagem de grande porte personalizado pela NVIDIA, projetado para melhorar a utilidade das respostas geradas pelo LLM."}, "o1": {"description": "Focado em raciocínio avançado e resolução de problemas complexos, incluindo tarefas matemáticas e científicas. Muito adequado para aplicativos que exigem compreensão profunda do contexto e gerenciamento de fluxos de trabalho."}, "o1-mini": {"description": "o1-mini é um modelo de raciocínio rápido e econômico, projetado para cenários de programação, matemática e ciências. Este modelo possui um contexto de 128K e uma data limite de conhecimento em outubro de 2023."}, "o1-preview": {"description": "o1 é o novo modelo de raciocínio da OpenAI, adequado para tarefas complexas que exigem amplo conhecimento geral. Este modelo possui um contexto de 128K e uma data limite de conhecimento em outubro de 2023."}, "o1-pro": {"description": "A série o1 é treinada com aprendizado por reforço, capaz de pensar antes de responder e executar tarefas complexas de raciocínio. O modelo o1-pro utiliza mais recursos computacionais para um pensamento mais profundo, oferecendo respostas de qualidade superior continuamente."}, "o3": {"description": "o3 é um modelo versátil e poderoso, com excelente desempenho em várias á<PERSON>. Ele estabelece novos padrões para tarefas de matemática, ciência, programação e raciocínio visual. Também é bom em redação técnica e seguimento de instruções. Os usuários podem utilizá-lo para analisar textos, códigos e imagens, resolvendo problemas complexos em múltiplas etapas."}, "o3-deep-research": {"description": "o3-deep-research é o nosso modelo avançado de pesquisa profunda, projetado para lidar com tarefas complexas de pesquisa em múltiplas etapas. Ele pode buscar e sintetizar informações da internet, além de acessar e utilizar seus dados próprios por meio do conector MCP."}, "o3-mini": {"description": "o3-mini é nosso mais recente modelo de inferência em miniatura, oferecendo alta inteligência com os mesmos custos e metas de latência que o o1-mini."}, "o3-pro": {"description": "O modelo o3-pro utiliza mais computação para pensar mais profundamente e sempre fornecer respostas melhores, suportado apenas via API Responses."}, "o4-mini": {"description": "o4-mini é nosso mais recente modelo compacto da série o. Ele é otimizado para inferência rápida e eficaz, apresentando alta eficiência e desempenho em tarefas de codificação e visuais."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research é o nosso modelo de pesquisa profunda mais rápido e acessível — ideal para lidar com tarefas complexas de pesquisa em múltiplas etapas. Ele pode buscar e sintetizar informações da internet, além de acessar e utilizar seus dados próprios por meio do conector MCP."}, "open-codestral-mamba": {"description": "Codestral Mamba é um modelo de linguagem Mamba 2 focado em geração de código, oferecendo forte suporte para tarefas avançadas de codificação e raciocínio."}, "open-mistral-7b": {"description": "Mistral 7B é um modelo compacto, mas de alto desempenho, especializado em processamento em lote e tarefas simples, como classificação e geração de texto, com boa capacidade de raciocínio."}, "open-mistral-nemo": {"description": "Mistral Nemo é um modelo de 12B desenvolvido em colaboração com a Nvidia, oferecendo excelente desempenho em raciocínio e codificação, fácil de integrar e substituir."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B é um modelo de especialistas maior, focado em tarefas complexas, oferecendo excelente capacidade de raciocínio e maior taxa de transferência."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B é um modelo de especialistas esparsos, utilizando múltiplos parâmetros para aumentar a velocidade de raciocínio, adequado para tarefas de geração de linguagem e código."}, "openai/gpt-4.1": {"description": "GPT-4.1 é nosso modelo principal para tarefas complexas. Ele é extremamente adequado para resolver problemas interdisciplinares."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini oferece um equilíbrio entre inteligência, velocidade e custo, tornando-se um modelo atraente para muitos casos de uso."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano é o modelo GPT-4.1 mais rápido e com melhor custo-benefício."}, "openai/gpt-4o": {"description": "ChatGPT-4o é um modelo dinâmico, atualizado em tempo real para manter a versão mais recente. Combina uma poderosa capacidade de compreensão e geração de linguagem, adequado para cenários de aplicação em larga escala, incluindo atendimento ao cliente, educação e suporte técnico."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini é o mais recente modelo da OpenAI, lançado após o GPT-4 Omni, que suporta entrada de texto e imagem e saída de texto. Como seu modelo compacto mais avançado, é muito mais barato do que outros modelos de ponta recentes e custa mais de 60% menos que o GPT-3.5 Turbo. Ele mantém inteligência de ponta, ao mesmo tempo que oferece uma relação custo-benefício significativa. O GPT-4o mini obteve uma pontuação de 82% no teste MMLU e atualmente está classificado acima do GPT-4 em preferências de chat."}, "openai/o1": {"description": "o1 é o novo modelo de raciocínio da OpenAI, que suporta entrada de texto e imagem e gera texto, adequado para tarefas complexas que exigem amplo conhecimento geral. Este modelo possui um contexto de 200K e data de corte de conhecimento em outubro de 2023."}, "openai/o1-mini": {"description": "o1-mini é um modelo de raciocínio rápido e econômico, projetado para cenários de programação, matemática e ciências. Este modelo possui um contexto de 128K e uma data limite de conhecimento em outubro de 2023."}, "openai/o1-preview": {"description": "o1 é o novo modelo de raciocínio da OpenAI, adequado para tarefas complexas que exigem amplo conhecimento geral. Este modelo possui um contexto de 128K e uma data limite de conhecimento em outubro de 2023."}, "openai/o3": {"description": "o3 é um modelo poderoso e vers<PERSON><PERSON>, que se destaca em várias área<PERSON>. Ele estabelece novos padrões para tarefas de matemática, ciência, programação e raciocínio visual. Também é habilidoso em redação técnica e seguimento de instruções. Os usuários podem utilizá-lo para analisar textos, códigos e imagens, resolvendo problemas complexos em várias etapas."}, "openai/o3-mini": {"description": "o3-mini oferece alta inteligência com os mesmos objetivos de custo e latência que o o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini de alta capacidade de raciocínio oferece alta inteligência com os mesmos objetivos de custo e latência que o o1-mini."}, "openai/o4-mini": {"description": "o4-mini é otimizado para inferência rápida e eficaz, apresentando alta eficiência e desempenho em tarefas de codificação e visuais."}, "openai/o4-mini-high": {"description": "Versão de alto nível de inferência do o4-mini, otimizada para inferência rápida e eficaz, apresentando alta eficiência e desempenho em tarefas de codificação e visuais."}, "openrouter/auto": {"description": "Com base no comprimento do contexto, tema e complexidade, sua solicitação será enviada para Llama 3 70B Instruct, Claude 3.5 Sonnet (autoajustável) ou GPT-4o."}, "phi3": {"description": "Phi-3 é um modelo leve e aberto lançado pela Microsoft, adequado para integração eficiente e raciocínio de conhecimento em larga escala."}, "phi3:14b": {"description": "Phi-3 é um modelo leve e aberto lançado pela Microsoft, adequado para integração eficiente e raciocínio de conhecimento em larga escala."}, "pixtral-12b-2409": {"description": "O modelo Pixtral demonstra forte capacidade em tarefas de compreensão de gráficos e imagens, perguntas e respostas de documentos, raciocínio multimodal e seguimento de instruções, podendo ingerir imagens em resolução natural e proporções, além de processar um número arbitrário de imagens em uma janela de contexto longa de até 128K tokens."}, "pixtral-large-latest": {"description": "Pixtral Large é um modelo multimodal de código aberto com 124 bilhões de parâmetros, baseado no Mistral Large 2. Este é o segundo modelo da nossa família multimodal, demonstrando capacidades de compreensão de imagem de nível avançado."}, "pro-128k": {"description": "Spark Pro 128K possui uma capacidade de processamento de contexto extremamente grande, capaz de lidar com até 128K de informações contextuais, especialmente adequado para análise completa e processamento de associações lógicas de longo prazo em conteúdos longos, podendo oferecer lógica fluida e consistente e suporte a diversas citações em comunicações textuais complexas."}, "qvq-72b-preview": {"description": "O modelo QVQ é um modelo de pesquisa experimental desenvolvido pela equipe <PERSON>, focado em melhorar a capacidade de raciocínio visual, especialmente na área de raciocínio matemático."}, "qvq-max": {"description": "Modelo de raciocínio visual QVQ <PERSON>, que suporta entrada visual e saída de cadeia de pensamento, demonstrando capacidades superiores em matemática, programação, análise visual, criação e tarefas gerais."}, "qvq-plus": {"description": "Modelo de raciocínio visual. Suporta entrada visual e saída em cadeia de pensamento. Versão plus lançada após o modelo qvq-max, com velocidade de raciocínio mais rápida e melhor equilíbrio entre desempenho e custo em comparação ao qvq-max."}, "qwen-coder-plus": {"description": "<PERSON><PERSON> <PERSON><PERSON>."}, "qwen-coder-turbo": {"description": "<PERSON><PERSON> <PERSON><PERSON>."}, "qwen-coder-turbo-latest": {"description": "<PERSON><PERSON> <PERSON>."}, "qwen-long": {"description": "O Qwen é um modelo de linguagem em larga escala que suporta contextos de texto longos e funcionalidades de diálogo baseadas em documentos longos e múltiplos cenários."}, "qwen-math-plus": {"description": "Modelo matemá<PERSON>o <PERSON> Qianwen especializado em resolução de problemas matemáticos."}, "qwen-math-plus-latest": {"description": "O modelo de matemática Qwen é especificamente projetado para resolver problemas matemáticos."}, "qwen-math-turbo": {"description": "Modelo matemá<PERSON>o <PERSON> Qianwen especializado em resolução de problemas matemáticos."}, "qwen-math-turbo-latest": {"description": "O modelo de matemática Qwen é especificamente projetado para resolver problemas matemáticos."}, "qwen-max": {"description": "Modelo de linguagem em larga escala com trilhões de parâmetros do Qwen, suportando entradas em diferentes idiomas, como português e inglês, atualmente a versão API por trás do produto Qwen 2.5."}, "qwen-omni-turbo": {"description": "A série de modelos Qwen-Omni suporta entrada de múltiplas modalidades, incluindo vídeo, áudio, imagem e texto, e gera saída em áudio e texto."}, "qwen-plus": {"description": "Versão aprimorada do modelo de linguagem em larga escala Qwen, que suporta entradas em diferentes idiomas, como português e inglês."}, "qwen-turbo": {"description": "O modelo de linguagem em larga escala Qwen suporta entradas em diferentes idiomas, como português e inglês."}, "qwen-vl-chat-v1": {"description": "O Qwen VL suporta uma maneira de interação flexível, incluindo múltiplas imagens, perguntas e respostas em várias rodadas, e capacidades criativas."}, "qwen-vl-max": {"description": "Modelo visual-linguístico de escala ultra grande Tongyi Qianwen. Em comparação com a versão aprimorada, ele eleva ainda mais a capacidade de raciocínio visual e conformidade com instruções, oferecendo níveis superiores de percepção e cognição visual."}, "qwen-vl-max-latest": {"description": "Modelo de linguagem visual em escala ultra grande Qwen. Em comparação com a versão aprimorada, melhora ainda mais a capacidade de raciocínio visual e de seguir instruções, oferecendo um nível mais alto de percepção e cognição visual."}, "qwen-vl-ocr": {"description": "<PERSON>yi Qi<PERSON>wen OCR é um modelo proprietário para extração de texto, focado em imagens de documentos, tabelas, questões e escrita manual. Ele pode reconhecer múltiplos idiomas, incluindo chinês, ingl<PERSON>s, francês, japonês, coreano, alemão, russo, italiano, vietnamita e árabe."}, "qwen-vl-plus": {"description": "Versão aprimorada do modelo visual-linguístico em larga escala Tongyi Qianwen. Melhora significativamente a capacidade de reconhecimento de detalhes e texto, suportando imagens com resolução superior a um milhão de pixels e proporções de qualquer tamanho."}, "qwen-vl-plus-latest": {"description": "Versão aprimorada do modelo de linguagem visual em larga escala Qwen. Aumenta significativamente a capacidade de reconhecimento de detalhes e de texto, suportando resolução de mais de um milhão de pixels e imagens de qualquer proporção."}, "qwen-vl-v1": {"description": "Inicializado com o modelo de linguagem Qwen-7B, adicionando um modelo de imagem, um modelo pré-treinado com resolução de entrada de imagem de 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 é uma nova série de modelos de linguagem grande Qwen. Qwen2 7B é um modelo baseado em transformer, com excelente desempenho em compreensão de linguagem, capacidade multilíngue, programação, matemática e raciocínio."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 é uma nova série de grandes modelos de linguagem, com capacidades de compreensão e geração mais robustas."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL é a versão mais recente do modelo Qwen-VL, alcançando desempenho de ponta em benchmarks de compreensão visual, incluindo MathVista, DocVQA, RealWorldQA e MTVQA. Qwen2-VL é capaz de entender vídeos de mais de 20 minutos, permitindo perguntas e respostas, diálogos e criação de conteúdo de alta qualidade baseados em vídeo. Ele também possui capacidades complexas de raciocínio e tomada de decisão, podendo ser integrado a dispositivos móveis, robôs, etc., para operações automáticas baseadas em ambientes visuais e instruções textuais. Além do inglês e do chinês, o Qwen2-VL agora também suporta a compreensão de texto em diferentes idiomas em imagens, incluindo a maioria das línguas europeias, japonês, coreano, árabe e vietnamita."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct é uma das mais recentes séries de modelos de linguagem grande lançadas pela Alibaba Cloud. Este modelo de 72B apresenta capacidades significativamente aprimoradas em áreas como codificação e matemática. O modelo também oferece suporte a múltiplas línguas, cobrindo mais de 29 idiomas, incluindo chinês e inglês. O modelo teve melhorias significativas em seguir instruções, entender dados estruturados e gerar saídas estruturadas (especialmente JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct é uma das mais recentes séries de modelos de linguagem grande lançadas pela Alibaba Cloud. Este modelo de 32B apresenta capacidades significativamente aprimoradas em áreas como codificação e matemática. O modelo oferece suporte a múltiplas línguas, cobrindo mais de 29 idiomas, incluindo chinês e inglês. O modelo teve melhorias significativas em seguir instruções, entender dados estruturados e gerar saídas estruturadas (especialmente JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM voltado para chinês e inglês, focado em linguagem, programação, matemática, raciocínio e outras áreas."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "LLM avançado, suporta geração de código, raciocínio e correção, abrangendo linguagens de programação populares."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Modelo de código de médio porte poderoso, suporta comprimento de contexto de 32K, especializado em programação multilíngue."}, "qwen/qwen3-14b": {"description": "Qwen3-14B é um modelo de linguagem causal denso de 14 bilhões de parâmetros da série Qwen3, projetado para raciocínio complexo e diálogos eficientes. Ele suporta a alternância sem costura entre o modo de 'pensamento' para tarefas de matemática, programação e raciocínio lógico e o modo 'não pensante' para diálogos gerais. Este modelo foi ajustado para seguir instruções, usar ferramentas de agentes, escrever criativamente e realizar tarefas multilíngues em mais de 100 idiomas e dialetos. Ele processa nativamente um contexto de 32K tokens e pode ser expandido para 131K tokens usando uma extensão baseada em YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B é um modelo de linguagem causal denso de 14 bilhões de parâmetros da série Qwen3, projetado para raciocínio complexo e diálogos eficientes. Ele suporta a alternância sem costura entre o modo de 'pensamento' para tarefas de matemática, programação e raciocínio lógico e o modo 'não pensante' para diálogos gerais. Este modelo foi ajustado para seguir instruções, usar ferramentas de agentes, escrever criativamente e realizar tarefas multilíngues em mais de 100 idiomas e dialetos. Ele processa nativamente um contexto de 32K tokens e pode ser expandido para 131K tokens usando uma extensão baseada em YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B é um modelo de mistura especializada (MoE) de 235 bilhões de parâmetros desenvolvido pela <PERSON>wen, ativando 22 bilhões de parâmetros a cada passagem para frente. Ele suporta a alternância sem costura entre o modo de 'pensamento' para raciocínio complexo, matemática e tarefas de código e o modo 'não pensante' para eficiência em diálogos gerais. Este modelo demonstra forte capacidade de raciocínio, suporte multilíngue (mais de 100 idiomas e dialetos), alta capacidade de seguir instruções e chamar ferramentas de agentes. Ele processa nativamente uma janela de contexto de 32K tokens e pode ser expandido para 131K tokens usando uma extensão baseada em YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B é um modelo de mistura especializada (MoE) de 235 bilhões de parâmetros desenvolvido pela <PERSON>wen, ativando 22 bilhões de parâmetros a cada passagem para frente. Ele suporta a alternância sem costura entre o modo de 'pensamento' para raciocínio complexo, matemática e tarefas de código e o modo 'não pensante' para eficiência em diálogos gerais. Este modelo demonstra forte capacidade de raciocínio, suporte multilíngue (mais de 100 idiomas e dialetos), alta capacidade de seguir instruções e chamar ferramentas de agentes. Ele processa nativamente uma janela de contexto de 32K tokens e pode ser expandido para 131K tokens usando uma extensão baseada em YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 é a última geração da série de modelos de linguagem Qwen, com uma arquitetura de mistura densa e especializada (MoE), destacando-se em raciocínio, suporte multilíngue e tarefas avançadas de agente. Sua capacidade única de alternar sem costura entre modos de pensamento para raciocínio complexo e modos não pensantes para diálogos eficientes garante um desempenho multifuncional e de alta qualidade.\n\nQwen3 supera significativamente modelos anteriores, como QwQ e Qwen2.5, oferecendo habilidades excepcionais em matemática, codificação, raciocínio lógico, escrita criativa e diálogos interativos. A variante Qwen3-30B-A3B contém 30,5 bilhões de parâmetros (3,3 bilhões de parâmetros ativados), 48 camadas, 128 especialistas (8 ativados por tarefa) e suporta um contexto de até 131K tokens (usando YaRN), estabelecendo um novo padrão para modelos de código aberto."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 é a última geração da série de modelos de linguagem Qwen, com uma arquitetura de mistura densa e especializada (MoE), destacando-se em raciocínio, suporte multilíngue e tarefas avançadas de agente. Sua capacidade única de alternar sem costura entre modos de pensamento para raciocínio complexo e modos não pensantes para diálogos eficientes garante um desempenho multifuncional e de alta qualidade.\n\nQwen3 supera significativamente modelos anteriores, como QwQ e Qwen2.5, oferecendo habilidades excepcionais em matemática, codificação, raciocínio lógico, escrita criativa e diálogos interativos. A variante Qwen3-30B-A3B contém 30,5 bilhões de parâmetros (3,3 bilhões de parâmetros ativados), 48 camadas, 128 especialistas (8 ativados por tarefa) e suporta um contexto de até 131K tokens (usando YaRN), estabelecendo um novo padrão para modelos de código aberto."}, "qwen/qwen3-32b": {"description": "Qwen3-32B é um modelo de linguagem causal denso de 32 bilhões de parâmetros da série Qwen3, otimizado para raciocínio complexo e diálogos eficientes. Ele suporta a alternância sem costura entre o modo de 'pensamento' para tarefas de matemática, codificação e raciocínio lógico e o modo 'não pensante' para diálogos mais rápidos e gerais. Este modelo demonstra um desempenho robusto em seguir instruções, usar ferramentas de agentes, escrever criativamente e realizar tarefas multilíngues em mais de 100 idiomas e dialetos. Ele processa nativamente um contexto de 32K tokens e pode ser expandido para 131K tokens usando uma extensão baseada em YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B é um modelo de linguagem causal denso de 32 bilhões de parâmetros da série Qwen3, otimizado para raciocínio complexo e diálogos eficientes. Ele suporta a alternância sem costura entre o modo de 'pensamento' para tarefas de matemática, codificação e raciocínio lógico e o modo 'não pensante' para diálogos mais rápidos e gerais. Este modelo demonstra um desempenho robusto em seguir instruções, usar ferramentas de agentes, escrever criativamente e realizar tarefas multilíngues em mais de 100 idiomas e dialetos. Ele processa nativamente um contexto de 32K tokens e pode ser expandido para 131K tokens usando uma extensão baseada em YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B é um modelo de linguagem causal denso de 8 bilhões de parâmetros da série Qwen3, projetado para tarefas intensivas em raciocínio e diálogos eficientes. Ele suporta a alternância sem costura entre o modo de 'pensamento' para matemática, codificação e raciocínio lógico e o modo 'não pensante' para diálogos gerais. Este modelo foi ajustado para seguir instruções, integrar agentes, escrever criativamente e usar em mais de 100 idiomas e dialetos. Ele suporta nativamente uma janela de contexto de 32K tokens e pode ser expandido para 131K tokens através do YaRN."}, "qwen2": {"description": "Qwen2 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2-72b-instruct": {"description": "Qwen2 é a nova série de modelos de linguagem grandes desenvolvida pela equipe Qwen. Baseia-se na arquitetura Transformer e utiliza funções de ativação SwiGLU, vieses de atenção QKV (attention QKV bias), atenção de consulta em grupo (group query attention), uma mistura de atenção de janela deslizante (mixture of sliding window attention) e atenção completa. Além disso, a equipe Qwen também aprimorou o tokenizador para adaptar-se a múltiplas línguas naturais e códigos."}, "qwen2-7b-instruct": {"description": "Qwen2 é uma nova série de modelos de linguagem grandes desenvolvida pela equipe Qwen. Baseia-se na arquitetura Transformer e utiliza funções de ativação SwiGLU, viés de atenção QKV (attention QKV bias), atenção de consulta em grupo (group query attention), uma mistura de atenção de janela deslizante e atenção completa (mixture of sliding window attention and full attention). Além disso, a equipe Qwen também aprimorou o tokenizador para adaptar-se a várias línguas naturais e códigos."}, "qwen2.5": {"description": "Qwen2.5 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2.5-14b-instruct": {"description": "Modelo de 14B parâmetros do Qwen 2.5, disponível como c<PERSON><PERSON> aberto."}, "qwen2.5-14b-instruct-1m": {"description": "Modelo de 72B de código aberto do <PERSON>wen2.5."}, "qwen2.5-32b-instruct": {"description": "Modelo de 32B parâmetros do Qwen 2.5, disponível como c<PERSON><PERSON> aberto."}, "qwen2.5-72b-instruct": {"description": "Modelo de 72B parâmetros do Qwen 2.5, disponível como c<PERSON><PERSON> aberto."}, "qwen2.5-7b-instruct": {"description": "Modelo de 7B parâmetros do Qwen 2.5, disponível como c<PERSON><PERSON> aberto."}, "qwen2.5-coder-1.5b-instruct": {"description": "Versão open source do modelo de código do Qwen."}, "qwen2.5-coder-14b-instruct": {"description": "Versão open source do modelo de código <PERSON>wen."}, "qwen2.5-coder-32b-instruct": {"description": "Versão open source do modelo de código Qwen."}, "qwen2.5-coder-7b-instruct": {"description": "Versão de código aberto do modelo de código <PERSON>."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder é o mais recente modelo de linguagem de grande escala especializado em código da série Qwen (anteriormente conhecido como CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 é a mais recente série de modelos de linguagem de grande escala Qwen. Para o Qwen2.5, lançamos diversos modelos de linguagem base e modelos de linguagem ajustados por instrução, com parâmetros variando de 500 milhões a 7,2 bilhões."}, "qwen2.5-math-1.5b-instruct": {"description": "O modelo <PERSON>wen-Math possui poderosas capacidades de resolução de problemas matemáticos."}, "qwen2.5-math-72b-instruct": {"description": "<PERSON> modelo <PERSON>-Math possui uma forte capacidade de resolução de problemas matemáticos."}, "qwen2.5-math-7b-instruct": {"description": "<PERSON> modelo <PERSON>-Math possui uma forte capacidade de resolução de problemas matemáticos."}, "qwen2.5-omni-7b": {"description": "O modelo da série Qwen-Omni suporta a entrada de múltiplos tipos de dados, incluindo vídeo, áudio, imagens e texto, e produz saídas em áudio e texto."}, "qwen2.5-vl-32b-instruct": {"description": "A série de modelos Qwen2.5-VL aprimorou o nível de inteligência, praticidade e aplicabilidade dos modelos, proporcionando um desempenho superior em cenários como conversação natural, criação de conteúdo, serviços de conhecimento especializado e desenvolvimento de código. A versão 32B utiliza técnicas de aprendizado por reforço para otimizar o modelo, oferecendo, em comparação com outros modelos da série Qwen2.5 VL, um estilo de saída mais alinhado com as preferências humanas, capacidade de raciocínio para problemas matemáticos complexos e compreensão detalhada e raciocínio sobre imagens."}, "qwen2.5-vl-72b-instruct": {"description": "Aprimoramento geral em seguimento de instruções, matemática, resolução de problemas e código, com capacidade de reconhecimento de objetos aprimorada, suporte a formatos diversos para localização precisa de elementos visuais, compreensão de arquivos de vídeo longos (até 10 minutos) e localização de eventos em segundos, capaz de entender a sequência e a velocidade do tempo, suportando controle de agentes em OS ou Mobile com forte capacidade de extração de informações e saída em formato Json. Esta versão é a de 72B, a mais poderosa da série."}, "qwen2.5-vl-7b-instruct": {"description": "Aprimoramento geral em seguimento de instruções, matemática, resolução de problemas e código, com capacidade de reconhecimento de objetos aprimorada, suporte a formatos diversos para localização precisa de elementos visuais, compreensão de arquivos de vídeo longos (até 10 minutos) e localização de eventos em segundos, capaz de entender a sequência e a velocidade do tempo, suportando controle de agentes em OS ou Mobile com forte capacidade de extração de informações e saída em formato Json. Esta versão é a de 72B, a mais poderosa da série."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL é a versão mais recente do modelo de linguagem visual da família de modelos Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2.5:1.5b": {"description": "Qwen2.5 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2.5:72b": {"description": "Qwen2.5 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2:0.5b": {"description": "Qwen2 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2:1.5b": {"description": "Qwen2 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen2:72b": {"description": "Qwen2 é a nova geração de modelo de linguagem em larga escala da Alibaba, oferecendo desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen3": {"description": "Qwen3 é a nova geração do modelo de linguagem em larga escala da Alibaba, que oferece desempenho excepcional para atender a diversas necessidades de aplicação."}, "qwen3-0.6b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-1.7b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-14b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-235b-a22b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-235b-a22b-instruct-2507": {"description": "Modelo open source no modo não reflexivo baseado no Qwen3, com melhorias modestas em criatividade subjetiva e segurança do modelo em relação à versão anterior (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Modelo open source no modo reflexivo baseado no Qwen3, com melhorias significativas em lógica, capacidades gerais, enriquecimento de conhecimento e criatividade em relação à versão anterior (Tongyi Qianwen 3-235B-A22B), adequado para cenários de raciocínio complexo e avançado."}, "qwen3-30b-a3b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-30b-a3b-instruct-2507": {"description": "Em comparação com a versão anterior (Qwen3-30B-A3B), houve um aumento significativo na capacidade geral em chinês, inglês e múltiplos idiomas. Otimizado especialmente para tarefas subjetivas e abertas, alinhando-se muito melhor às preferências dos usuários e fornecendo respostas mais úteis."}, "qwen3-30b-a3b-thinking-2507": {"description": "Baseado no modelo open source do modo reflexivo Qwen3, esta versão apresenta melhorias substanciais em lógica, capacidade geral, conhecimento e criatividade em relação à versão anterior (Tongyi Qianwen 3-30B-A3B), sendo adequada para cenários complexos que exigem raciocínio avançado."}, "qwen3-32b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-4b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-8b": {"description": "Qwen3 é um novo modelo de linguagem de próxima geração com capacidades significativamente aprimoradas, alcançando níveis líderes da indústria em raciocínio, generalidade, agentes e multilíngue, e suporta a alternância de modos de pensamento."}, "qwen3-coder-480b-a35b-instruct": {"description": "Versão open source do modelo de código Tongyi Qianwen. O mais recente qwen3-coder-480b-a35b-instruct é um modelo de geração de código baseado no Qwen3, com forte capacidade de agente de codificação, especializado em chamadas de ferramentas e interação com ambientes, capaz de programação autônoma, combinando excelência em código com capacidades gerais."}, "qwen3-coder-plus": {"description": "Modelo de código <PERSON>. A série mais recente Qwen3-Coder-Plus é baseada no Qwen3, com forte capacidade de agente de codificação, especializada em chamadas de ferramentas e interação com ambientes, permitindo programação autônoma, combinando excelência em código com capacidades gerais."}, "qwq": {"description": "QwQ é um modelo de pesquisa experimental, focado em melhorar a capacidade de raciocínio da IA."}, "qwq-32b": {"description": "Modelo de inferência QwQ treinado com base no modelo Qwen2.5-32B, que melhorou significativamente a capacidade de inferência do modelo através de aprendizado por reforço. Os indicadores principais do modelo, como có<PERSON> mate<PERSON>o (AIME 24/25, LiveCodeBench) e alguns indicadores gerais (IFEval, LiveBench, etc.), alcançaram o nível do DeepSeek-R1 versão completa, com todos os indicadores superando significativamente o DeepSeek-R1-Distill-Qwen-32B, que também é baseado no Qwen2.5-32B."}, "qwq-32b-preview": {"description": "O modelo QwQ é um modelo de pesquisa experimental desenvolvido pela equipe <PERSON>, focado em aprimorar a capacidade de raciocínio da IA."}, "qwq-plus": {"description": "Modelo de raciocínio QwQ treinado com base no modelo Qwen2.5, que aprimora significativamente a capacidade de raciocínio por meio de aprendizado por reforço. Os principais indicadores em matemática e código (AIME 24/25, LiveCodeBench), bem como alguns indicadores gerais (IFEval, LiveBench, etc.), alcançam o nível completo do DeepSeek-R1."}, "qwq_32b": {"description": "Modelo de inferência de tamanho médio da série Qwen. Comparado a modelos tradicionais de ajuste de instruções, o QwQ, com suas capacidades de pensamento e raciocínio, pode melhorar significativamente o desempenho em tarefas de downstream, especialmente na resolução de problemas difíceis."}, "r1-1776": {"description": "R1-1776 é uma versão do modelo DeepSeek R1, treinada posteriormente para fornecer informações factuais não filtradas e imparciais."}, "solar-mini": {"description": "Solar Mini é um LLM compacto, com desempenho superior ao GPT-3.5, possuindo forte capacidade multilíngue, suportando ingl<PERSON> e coreano, oferecendo uma solução eficiente e compacta."}, "solar-mini-ja": {"description": "Solar Mini (Ja) expande as capacidades do Solar Mini, focando no japonês, enquanto mantém eficiência e desempenho excepcional no uso de inglês e coreano."}, "solar-pro": {"description": "Solar Pro é um LLM de alta inteligência lançado pela Upstage, focado na capacidade de seguir instruções em um único GPU, com pontuação IFEval acima de 80. Atualmente suporta inglês, com uma versão oficial planejada para lançamento em novembro de 2024, que expandirá o suporte a idiomas e comprimento de contexto."}, "sonar": {"description": "Produto de busca leve baseado em contexto de busca, mais rápido e mais barato que o Sonar Pro."}, "sonar-deep-research": {"description": "A Pesquisa Profunda realiza uma pesquisa abrangente de nível especialista e a sintetiza em relatórios acessíveis e acionáveis."}, "sonar-pro": {"description": "Produto de busca avançada que suporta contexto de busca, consultas avançadas e acompanhamento."}, "sonar-reasoning": {"description": "Novo produto API suportado pelo modelo de raciocínio da DeepSeek."}, "sonar-reasoning-pro": {"description": "Um novo produto de API suportado pelo modelo de raciocínio DeepSeek."}, "stable-diffusion-3-medium": {"description": "Modelo de geração de imagens a partir de texto mais recente lançado pela Stability AI. Esta versão mantém as vantagens das anteriores e apresenta melhorias significativas na qualidade da imagem, compreensão textual e diversidade de estilos, capaz de interpretar prompts complexos de linguagem natural com maior precisão e gerar imagens mais precisas e variadas."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large é um modelo multimodal de difusão transformadora (MMDiT) para geração de imagens a partir de texto com 800 milhões de parâmetros, oferecendo qualidade de imagem excepcional e alta correspondência com prompts, suportando geração de imagens de alta resolução de até 1 milhão de pixels, e operando eficientemente em hardware de consumo comum."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo é um modelo baseado no stable-diffusion-3.5-large que utiliza a técnica de destilação de difusão adversarial (ADD), oferecendo maior velocidade."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 é inicializado com pesos do checkpoint stable-diffusion-v1.2 e ajustado por 595k passos em \"laion-aesthetics v2 5+\" com resolução 512x512, reduzindo em 10% a condicionamento textual para melhorar a amostragem guiada sem classificador."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl apresenta melhorias significativas em relação à v1.5, com desempenho comparável ao modelo open source SOTA midjourney. As melhorias incluem: backbone unet três vezes maior; módulo de refinamento para melhorar a qualidade da imagem gerada; técnicas de treinamento mais eficientes, entre outras."}, "stable-diffusion-xl-base-1.0": {"description": "Grande modelo de geração de imagens a partir de texto desenvolvido e open source pela Stability AI, com capacidade criativa de ponta na indústria. Possui excelente compreensão de instruções e suporta definição de prompts inversos para geração precisa de conteúdo."}, "step-1-128k": {"description": "Equilibra desempenho e custo, adequado para cenários gerais."}, "step-1-256k": {"description": "Possui capacidade de processamento de contexto ultra longo, especialmente adequado para análise de documentos longos."}, "step-1-32k": {"description": "Suporta diálogos de comprimento médio, adequado para diversas aplicações."}, "step-1-8k": {"description": "<PERSON><PERSON> pequeno, adequado para tarefas leves."}, "step-1-flash": {"description": "Modelo de alta velocidade, adequado para diálogos em tempo real."}, "step-1.5v-mini": {"description": "Este modelo possui uma poderosa capacidade de compreensão de vídeo."}, "step-1o-turbo-vision": {"description": "Este modelo possui uma poderosa capacidade de compreensão de imagens, superando o 1o em áreas de matemática e programação. O modelo é menor que o 1o e oferece uma velocidade de saída mais rápida."}, "step-1o-vision-32k": {"description": "Este modelo possui uma poderosa capacidade de compreensão de imagens. Em comparação com a série de modelos step-1v, apresenta um desempenho visual superior."}, "step-1v-32k": {"description": "Suporta entradas visuais, aprimorando a experiência de interação multimodal."}, "step-1v-8k": {"description": "Modelo visual compacto, adequado para tarefas básicas de texto e imagem."}, "step-1x-edit": {"description": "Modelo focado em tarefas de edição de imagens, capaz de modificar e aprimorar imagens com base em imagens e descrições textuais fornecidas pelo usuário. Suporta múltiplos formatos de entrada, incluindo descrições textuais e imagens de exemplo. O modelo compreende a intenção do usuário e gera resultados de edição de imagem conforme solicitado."}, "step-1x-medium": {"description": "Modelo com forte capacidade de geração de imagens, suportando entrada via descrições textuais. Possui suporte nativo ao chinês, compreendendo e processando melhor descrições textuais em chinês, capturando com maior precisão as informações semânticas para convertê-las em características visuais, permitindo geração de imagens mais precisas. Gera imagens de alta resolução e qualidade, com certa capacidade de transferência de estilo."}, "step-2-16k": {"description": "Suporta interações de contexto em larga escala, adequado para cenários de diálogo complexos."}, "step-2-16k-exp": {"description": "Versão experimental do modelo step-2, contendo os recursos mais recentes, em atualização contínua. Não é recomendado para uso em ambientes de produção formal."}, "step-2-mini": {"description": "Um modelo de grande escala de alta velocidade baseado na nova arquitetura de atenção auto-desenvolvida MFA, alcançando resultados semelhantes ao step1 com um custo muito baixo, enquanto mantém uma maior taxa de transferência e um tempo de resposta mais rápido. Capaz de lidar com tarefas gerais, possui especialização em habilidades de codificação."}, "step-2x-large": {"description": "Nova geração do modelo Xingchen Step, focado em geração de imagens, capaz de criar imagens de alta qualidade a partir de descrições textuais fornecidas pelo usuário. O novo modelo gera imagens com textura mais realista e melhor capacidade de geração de texto em chinês e inglês."}, "step-r1-v-mini": {"description": "Este modelo é um grande modelo de inferência com forte capacidade de compreensão de imagens, capaz de processar informações de imagem e texto, gerando conteúdo textual após um profundo raciocínio. O modelo se destaca no campo do raciocínio visual, além de possuir habilidades de raciocínio matemático, código e texto de primeira linha. O comprimento do contexto é de 100k."}, "taichu_llm": {"description": "O modelo de linguagem Taichu possui uma forte capacidade de compreensão de linguagem, além de habilidades em criação de texto, perguntas e respostas, programação de código, cál<PERSON>los matemáticos, raciocínio lógico, análise de sentimentos e resumo de texto. Inova ao combinar pré-treinamento com grandes dados e conhecimento rico de múltiplas fontes, aprimorando continuamente a tecnologia de algoritmos e absorvendo novos conhecimentos de vocabulário, estrutura, gramática e semântica de grandes volumes de dados textuais, proporcionando aos usuários informações e serviços mais convenientes e uma experiência mais inteligente."}, "taichu_o1": {"description": "taichu_o1 é um novo grande modelo de inferência de próxima geração, que realiza cadeias de pensamento humano por meio de interações multimodais e aprendizado por reforço, suportando deduções de decisões complexas, enquanto exibe caminhos de raciocínio modeláveis com alta precisão de saída, adequado para análise de estratégias e raciocínio profundo."}, "taichu_vl": {"description": "Integra capacidades de compreensão de imagens, transferência de conhecimento e atribuição lógica, destacando-se na área de perguntas e respostas baseadas em texto e imagem."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct possui 80 bilhões de parâmetros, ativando 13 bilhões para competir com modelos maiores, suportando racioc<PERSON>io híbrido de “pensamento rápido/pensamento lento”; compreensão estável de textos longos; validado pelo BFCL-v3 e τ-Bench, com capacidades de agente líderes; combinando GQA e múltiplos formatos de quantização para inferência eficiente."}, "text-embedding-3-large": {"description": "O modelo de vetorização mais poderoso, adequado para tarefas em inglês e não inglês."}, "text-embedding-3-small": {"description": "Modelo de Embedding de nova geração, eficiente e econômico, adequado para recuperação de conhecimento, aplicações RAG e outros cenários."}, "thudm/glm-4-32b": {"description": "O GLM-4-32B-0414 é um modelo de linguagem de pesos abertos bilíngue (chinês-inglês) de 32B, otimizado para geração de código, chamadas de função e tarefas baseadas em agentes. Ele foi pré-treinado em 15T de dados de alta qualidade e re-raciocínio, e aprimorado com alinhamento de preferências humanas, amostragem de rejeição e aprendizado por reforço. Este modelo se destaca em raciocínio complexo, geração de artefatos e tarefas de saída estruturada, alcançando desempenho comparável ao GPT-4o e DeepSeek-V3-0324 em vários testes de referência."}, "thudm/glm-4-32b:free": {"description": "O GLM-4-32B-0414 é um modelo de linguagem de pesos abertos bilíngue (chinês-inglês) de 32B, otimizado para geração de código, chamadas de função e tarefas baseadas em agentes. Ele foi pré-treinado em 15T de dados de alta qualidade e re-raciocínio, e aprimorado com alinhamento de preferências humanas, amostragem de rejeição e aprendizado por reforço. Este modelo se destaca em raciocínio complexo, geração de artefatos e tarefas de saída estruturada, alcançando desempenho comparável ao GPT-4o e DeepSeek-V3-0324 em vários testes de referência."}, "thudm/glm-4-9b-chat": {"description": "Versão de código aberto da última geração do modelo pré-treinado GLM-4, lançado pela Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 é um modelo de linguagem de 9 bilhões de parâmetros da série GLM-4 desenvolvido pela THUDM. O GLM-4-9B-0414 é treinado usando as mesmas estratégias de aprendizado por reforço e alinhamento de seu modelo correspondente maior de 32B, alcançando alto desempenho em relação ao seu tamanho, tornando-o adequado para implantações com recursos limitados que ainda exigem forte capacidade de compreensão e geração de linguagem."}, "thudm/glm-z1-32b": {"description": "O GLM-Z1-32B-0414 é uma variante de raciocínio aprimorada do GLM-4-32B, construída para resolver problemas de matemática profunda, lógica e voltados para código. Ele aplica aprendizado por reforço estendido (tarefa específica e baseado em preferências emparelhadas gerais) para melhorar o desempenho em tarefas complexas de múltiplos passos. Em comparação com o modelo base GLM-4-32B, o Z1 melhora significativamente as capacidades de raciocínio estruturado e formal.\n\nEste modelo suporta a execução forçada de etapas de 'pensamento' por meio de engenharia de prompts e oferece maior coerência para saídas de formato longo. Ele é otimizado para fluxos de trabalho de agentes e suporta longos contextos (via YaRN), chamadas de ferramentas JSON e configurações de amostragem de granularidade fina para raciocínio estável. É ideal para casos de uso que exigem raciocínio cuidadoso, de múltiplos passos ou deduções formais."}, "thudm/glm-z1-32b:free": {"description": "O GLM-Z1-32B-0414 é uma variante de raciocínio aprimorada do GLM-4-32B, construída para resolver problemas de matemática profunda, lógica e voltados para código. Ele aplica aprendizado por reforço estendido (tarefa específica e baseado em preferências emparelhadas gerais) para melhorar o desempenho em tarefas complexas de múltiplos passos. Em comparação com o modelo base GLM-4-32B, o Z1 melhora significativamente as capacidades de raciocínio estruturado e formal.\n\nEste modelo suporta a execução forçada de etapas de 'pensamento' por meio de engenharia de prompts e oferece maior coerência para saídas de formato longo. Ele é otimizado para fluxos de trabalho de agentes e suporta longos contextos (via YaRN), chamadas de ferramentas JSON e configurações de amostragem de granularidade fina para raciocínio estável. É ideal para casos de uso que exigem raciocínio cuidadoso, de múltiplos passos ou deduções formais."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 é um modelo de linguagem de 9 bilhões de parâmetros da série GLM-4 desenvolvido pela THUDM. Ele utiliza técnicas inicialmente aplicadas a modelos maiores do GLM-Z1, incluindo aprendizado por reforço expandido, alinhamento de classificação em pares e treinamento para tarefas intensivas em raciocínio, como matemática, código e lógica. Apesar de seu tamanho menor, ele demonstra um desempenho robusto em tarefas gerais de raciocínio e supera muitos modelos de código aberto em seu nível de peso."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B é um modelo de raciocínio profundo de 32 bilhões de parâmetros da série GLM-4-Z1, otimizado para tarefas complexas e abertas que exigem longos períodos de reflexão. Ele é construído sobre o glm-4-32b-0414, adicionando uma fase de aprendizado por reforço adicional e estratégias de alinhamento em múltiplas etapas, introduzindo a capacidade de 'reflexão' destinada a simular processamento cognitivo expandido. Isso inclui raciocínio iterativo, análise de múltiplos saltos e fluxos de trabalho aprimorados por ferramentas, como busca, recuperação e síntese consciente de citações.\n\nEste modelo se destaca em escrita de pesquisa, análise comparativa e perguntas complexas. Ele suporta chamadas de função para primitivos de busca e navegação (`search`, `click`, `open`, `finish`), permitindo seu uso em pipelines baseados em agentes. O comportamento reflexivo é moldado por recompensas baseadas em regras e um mecanismo de decisão atrasada controlado por múltiplos ciclos, com referência a estruturas de pesquisa profunda como a pilha de alinhamento interna da OpenAI. Esta variante é adequada para cenários que exigem profundidade em vez de velocidade."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera é criado pela combinação do DeepSeek-R1 e DeepSeek-V3 (0324), unindo a capacidade de raciocínio do R1 e as melhorias de eficiência de tokens do V3. Ele é baseado na arquitetura DeepSeek-MoE Transformer e otimizado para tarefas gerais de geração de texto.\n\nEste modelo combina os pesos pré-treinados de duas fontes para equilibrar o desempenho em raciocínio, eficiência e tarefas de seguir instruções. Ele é lançado sob a licença MIT, destinado a uso em pesquisa e comercial."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena <PERSON> (7B) oferece capacidade de computação aprimorada através de estratégias e arquiteturas de modelo eficientes."}, "tts-1": {"description": "O mais recente modelo de texto para fala, otimizado para velocidade em cenários em tempo real."}, "tts-1-hd": {"description": "O mais recente modelo de texto para fala, otimizado para qualidade."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) é adequado para tarefas de instrução refinadas, oferecendo excelente capacidade de processamento de linguagem."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet eleva o padrão da indústria, superando modelos concorrentes e Claude 3 Opus, apresentando um desempenho excepcional em uma ampla gama de avaliações, enquanto mantém a velocidade e o custo de nossos modelos de nível médio."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet é o modelo de próxima geração mais rápido da Anthropic. Em comparação com o Claude 3 Haiku, o Claude 3.7 Sonnet apresenta melhorias em várias habilidades e supera o maior modelo da geração anterior, o Claude 3 Opus, em muitos testes de referência de inteligência."}, "v0-1.0-md": {"description": "O modelo v0-1.0-md é uma versão antiga que oferece serviços através da API v0"}, "v0-1.5-lg": {"description": "O modelo v0-1.5-lg é adequado para tarefas avançadas de pensamento ou raciocínio"}, "v0-1.5-md": {"description": "O modelo v0-1.5-md é adequado para tarefas diárias e geração de interfaces de usuário (UI)"}, "wan2.2-t2i-flash": {"description": "Versão ultrarrápida Wanxiang 2.2, modelo mais recente. Atualizações abrangentes em criatividade, estabilidade e realismo, com alta velocidade de geração e excelente custo-benefício."}, "wan2.2-t2i-plus": {"description": "Versão profissional Wanxiang 2.2, modelo mais recente. Atualizações abrangentes em criatividade, estabilidade e realismo, com geração de detalhes ricos."}, "wanx-v1": {"description": "Modelo básico de geração de imagens a partir de texto, correspondente ao modelo geral 1.0 do site oficial <PERSON>."}, "wanx2.0-t2i-turbo": {"description": "Especializado em retratos com textura, velocidade média e custo baixo. Corresponde ao modelo ultrarrápido 2.0 do site oficial <PERSON><PERSON>."}, "wanx2.1-t2i-plus": {"description": "Versão totalmente atualizada. Geração de imagens com detalhes mais ricos, velocidade um pouco mais lenta. Corresponde ao modelo profissional 2.1 do site oficial Tong<PERSON>."}, "wanx2.1-t2i-turbo": {"description": "Versão totalmente atualizada. Geração rápida, resultados abrangentes e excelente custo-benefício. Corresponde ao modelo ultrarrápido 2.1 do site oficial Tong<PERSON>."}, "whisper-1": {"description": "Modelo universal de reconhecimento de voz, suportando reconhecimento de voz multilíngue, tradução de voz e identificação de idioma."}, "wizardlm2": {"description": "WizardLM 2 é um modelo de linguagem fornecido pela Microsoft AI, destacando-se em diálogos complexos, multilíngue, raciocínio e assistentes inteligentes."}, "wizardlm2:8x22b": {"description": "WizardLM 2 é um modelo de linguagem fornecido pela Microsoft AI, destacando-se em diálogos complexos, multilíngue, raciocínio e assistentes inteligentes."}, "x1": {"description": "O modelo Spark X1 será aprimorado ainda mais, mantendo a liderança em tarefas matemáticas no país, e alcançando resultados em tarefas gerais como raciocínio, geração de texto e compreensão de linguagem que se comparam ao OpenAI o1 e DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 é uma versão aprimorada do Yi. Ele usa um corpus de alta qualidade com 500B tokens para continuar o pré-treinamento do Yi e é refinado com 3M amostras de ajuste fino diversificadas."}, "yi-large": {"description": "Modelo de nova geração com trilhões de parâmetros, oferecendo capacidades excepcionais de perguntas e respostas e geração de texto."}, "yi-large-fc": {"description": "Baseado no modelo yi-large, suporta e aprimora a capacidade de chamadas de ferramentas, adequado para diversos cenários de negócios que exigem a construção de agentes ou fluxos de trabalho."}, "yi-large-preview": {"description": "<PERSON><PERSON><PERSON> inicial, recomenda-se o uso do yi-large (nova versão)."}, "yi-large-rag": {"description": "Serviço de alto nível baseado no modelo yi-large, combinando técnicas de recuperação e geração para fornecer respostas precisas, com serviços de busca em tempo real na web."}, "yi-large-turbo": {"description": "Excelente relação custo-benefício e desempenho excepcional. Ajuste de alta precisão baseado em desempenho, velocidade de raciocínio e custo."}, "yi-lightning": {"description": "Modelo de alto desempenho mais recente, garan<PERSON><PERSON> de alta qualidade enquanto a velocidade de raciocínio é significativamente aprimorada."}, "yi-lightning-lite": {"description": "<PERSON><PERSON><PERSON> leve, recomendada para uso com yi-lightning."}, "yi-medium": {"description": "Modelo de tamanho médio com ajuste fino, equilibrando capacidades e custo. Otimização profunda da capacidade de seguir instruções."}, "yi-medium-200k": {"description": "Janela de contexto ultra longa de 200K, oferecendo compreensão e geração de texto em profundidade."}, "yi-spark": {"description": "Modelo leve e ágil. Oferece capacidades aprimoradas de cálculos matemáticos e escrita de código."}, "yi-vision": {"description": "Modelo para tarefas visuais complexas, oferecendo alta performance em compreensão e análise de imagens."}, "yi-vision-v2": {"description": "Modelo para tarefas visuais complexas, oferecendo alta performance em compreensão e análise baseadas em múltiplas imagens."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 é um modelo base projetado para aplicações de agentes inteligentes, utilizando arquitetura Mixture-of-Experts (MoE). Otimizado para chamadas de ferramentas, navegação web, engenharia de software e programação front-end, suporta integração perfeita com agentes de código como Claude Code e Roo Code. Adota modo de raciocínio híbrido, adaptando-se a cenários de raciocínio complexo e uso cotidiano."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air é um modelo base projetado para aplicações de agentes inteligentes, utilizando arquitetura Mixture-of-Experts (MoE). Otimizado para chamadas de ferramentas, navegação web, engenharia de software e programação front-end, suporta integração perfeita com agentes de código como Claude Code e Roo Code. Adota modo de raciocínio híbrido, adaptando-se a cenários de raciocínio complexo e uso cotidiano."}}