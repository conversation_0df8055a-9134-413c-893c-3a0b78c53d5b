{"01-ai/yi-1.5-34b-chat": {"description": "Zero One Everything, il più recente modello open source fine-tuned, con 34 miliardi di parametri, supporta vari scenari di dialogo, con dati di addestramento di alta qualità, allineati alle preferenze umane."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One Everything, il più recente modello open source fine-tuned, con 9 miliardi di parametri, supporta vari scenari di dialogo, con dati di addestramento di alta qualità, allineati alle preferenze umane."}, "360/deepseek-r1": {"description": "【Versione 360】DeepSeek-R1 ha utilizzato tecniche di apprendimento rinforzato su larga scala nella fase di post-addestramento, migliorando notevolmente la capacità di inferenza del modello con pochissimi dati etichettati. Le prestazioni sono paragonabili alla versione ufficiale OpenAI o1 in compiti di matematica, codice e ragionamento in linguaggio naturale."}, "360gpt-pro": {"description": "360GPT Pro, come membro importante della serie di modelli AI di 360, soddisfa le diverse applicazioni del linguaggio naturale con un'efficace capacità di elaborazione del testo, supportando la comprensione di testi lunghi e conversazioni a più turni."}, "360gpt-pro-trans": {"description": "<PERSON>lo dedicato alla traduzione, otti<PERSON><PERSON><PERSON> con un profondo affinamento, con risultati di traduzione all'avanguardia."}, "360gpt-turbo": {"description": "360GPT Turbo offre potenti capacità di calcolo e dialogo, con un'eccellente comprensione semantica e efficienza di generazione, rappresentando una soluzione ideale per assistenti intelligenti per aziende e sviluppatori."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K enfatizza la sicurezza semantica e l'orientamento alla responsabilità, progettato specificamente per scenari applicativi con elevati requisiti di sicurezza dei contenuti, garantendo l'accuratezza e la robustezza dell'esperienza utente."}, "360gpt2-o1": {"description": "360gpt2-o1 utilizza la ricerca ad albero per costruire catene di pensiero e introduce un meccanismo di riflessione, addestrato tramite apprendimento rinforzato, dotando il modello della capacità di auto-riflessione e correzione degli errori."}, "360gpt2-pro": {"description": "360GPT2 Pro è un modello avanzato di elaborazione del linguaggio naturale lanciato da 360, con eccellenti capacità di generazione e comprensione del testo, in particolare nel campo della generazione e creazione, capace di gestire compiti complessi di conversione linguistica e interpretazione di ruoli."}, "360zhinao2-o1": {"description": "360zhinao2-o1 utilizza la ricerca ad albero per costruire catene di pensiero e introduce un meccanismo di riflessione, addestrato tramite apprendimento rinforzato, dotando il modello della capacità di auto-riflessione e correzione degli errori."}, "4.0Ultra": {"description": "Spark4.0 Ultra è la versione più potente della serie di modelli Spark, migliorando la comprensione e la sintesi del contenuto testuale mentre aggiorna il collegamento alla ricerca online. È una soluzione completa per migliorare la produttività lavorativa e rispondere con precisione alle esigenze, rappresentando un prodotto intelligente all'avanguardia nel settore."}, "AnimeSharp": {"description": "AnimeSharp (noto anche come “4x‑AnimeSharp”) è un modello open source di super-risoluzione sviluppato da Kim2091 basato sull'architettura ESRGAN, focalizzato sull'ingrandimento e l'affilatura di immagini in stile anime. Nel febbraio 2022 è stato rinominato da “4x-TextSharpV1”, originariamente adatto anche per immagini di testo, ma con prestazioni ottimizzate significativamente per contenuti anime."}, "Baichuan2-Turbo": {"description": "Utilizza tecnologie di ricerca avanzate per collegare completamente il grande modello con la conoscenza di settore e la conoscenza globale. Supporta il caricamento di vari documenti come PDF, Word e l'immissione di URL, con acquisizione di informazioni tempestiva e completa, e risultati di output accurati e professionali."}, "Baichuan3-Turbo": {"description": "Ottimizza<PERSON> per scenari aziendali ad alta frequenza, con un notevole miglioramento delle prestazioni e un ottimo rapporto qualità-prezzo. Rispetto al modello Baichuan2, la creazione di contenuti è migliorata del 20%, le domande di conoscenza del 17% e le capacità di interpretazione di ruoli del 40%. Le prestazioni complessive superano quelle di GPT3.5."}, "Baichuan3-Turbo-128k": {"description": "Dotato di una finestra di contesto ultra lunga di 128K, otti<PERSON><PERSON><PERSON> per scenari aziendali ad alta frequenza, con un notevole miglioramento delle prestazioni e un ottimo rapporto qualità-prezzo. Rispetto al modello Baichuan2, la creazione di contenuti è migliorata del 20%, le domande di conoscenza del 17% e le capacità di interpretazione di ruoli del 40%. Le prestazioni complessive superano quelle di GPT3.5."}, "Baichuan4": {"description": "Il modello ha la migliore capacità in Cina, superando i modelli mainstream esteri in compiti cinesi come enciclopedie, testi lunghi e creazione di contenuti. Ha anche capacità multimodali leader nel settore, con prestazioni eccellenti in vari benchmark di valutazione."}, "Baichuan4-Air": {"description": "Il modello con le migliori capacità in patria, supera i modelli principali esteri in compiti cinesi come enciclopedie, testi lunghi e creazione di contenuti. Possiede anche capacità multimodali leader del settore, con prestazioni eccellenti in vari benchmark di valutazione."}, "Baichuan4-Turbo": {"description": "Il modello con le migliori capacità in patria, supera i modelli principali esteri in compiti cinesi come enciclopedie, testi lunghi e creazione di contenuti. Possiede anche capacità multimodali leader del settore, con prestazioni eccellenti in vari benchmark di valutazione."}, "DeepSeek-R1": {"description": "LLM avanzato ed efficiente, specializzato in ragionamento, matematica e programmazione."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1—il modello più grande e intelligente del pacchetto DeepSeek—è stato distillato nell'architettura Llama 70B. Basato su benchmark e valutazioni umane, questo modello è più intelligente del Llama 70B originale, eccellendo in particolare in compiti che richiedono precisione matematica e fattuale."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Il modello di distillazione DeepSeek-R1 basato su Qwen2.5-Math-1.5B ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Il modello di distillazione DeepSeek-R1 basato su Qwen2.5-14B ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "La serie DeepSeek-R1 ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source, superando il livello di OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Il modello di distillazione DeepSeek-R1 basato su Qwen2.5-Math-7B ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source."}, "DeepSeek-V3": {"description": "DeepSeek-V3 è un modello MoE sviluppato internamente dalla DeepSeek Company. I risultati di DeepSeek-V3 in molte valutazioni superano quelli di altri modelli open source come Qwen2.5-72B e Llama-3.1-405B, e si confronta alla pari con i modelli closed source di punta a livello mondiale come GPT-4o e Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite offre una velocità di risposta eccezionale e un miglior rapporto qualità-prezzo, fornendo ai clienti scelte più flessibili per diversi scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite offre una velocità di risposta eccezionale e un miglior rapporto qualità-prezzo, fornendo ai clienti scelte più flessibili per diversi scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite offre una velocità di risposta eccezionale e un miglior rapporto qualità-prezzo, fornendo ai clienti scelte più flessibili per diversi scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 4k."}, "Doubao-pro-128k": {"description": "Il modello principale con le migliori prestazioni, adatto per gestire compiti complessi, con ottimi risultati in domande di riferimento, sintesi, creazione, classificazione del testo, role-playing e altri scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 128k."}, "Doubao-pro-32k": {"description": "Il modello principale con le migliori prestazioni, adatto per gestire compiti complessi, con ottimi risultati in domande di riferimento, sintesi, creazione, classificazione del testo, role-playing e altri scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 32k."}, "Doubao-pro-4k": {"description": "Il modello principale con le migliori prestazioni, adatto per gestire compiti complessi, con ottimi risultati in domande di riferimento, sintesi, creazione, classificazione del testo, role-playing e altri scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 4k."}, "DreamO": {"description": "DreamO è un modello open source di generazione di immagini personalizzate sviluppato congiuntamente da ByteDance e l'Università di Pechino, progettato per supportare la generazione di immagini multitasking tramite un'architettura unificata. Utilizza un metodo di modellazione combinata efficiente per generare immagini altamente coerenti e personalizzate in base a molteplici condizioni specificate dall'utente, come identità, soggetto, stile e sfondo."}, "ERNIE-3.5-128K": {"description": "Modello di linguaggio di grande scala di punta sviluppato da Baidu, che copre un'enorme quantità di dati in cinese e inglese, con potenti capacità generali, in grado di soddisfare la maggior parte delle esigenze di domande e risposte, generazione creativa e scenari di applicazione dei plugin; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ERNIE-3.5-8K": {"description": "Modello di linguaggio di grande scala di punta sviluppato da Baidu, che copre un'enorme quantità di dati in cinese e inglese, con potenti capacità generali, in grado di soddisfare la maggior parte delle esigenze di domande e risposte, generazione creativa e scenari di applicazione dei plugin; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ERNIE-3.5-8K-Preview": {"description": "Modello di linguaggio di grande scala di punta sviluppato da Baidu, che copre un'enorme quantità di dati in cinese e inglese, con potenti capacità generali, in grado di soddisfare la maggior parte delle esigenze di domande e risposte, generazione creativa e scenari di applicazione dei plugin; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ERNIE-4.0-8K-Latest": {"description": "Modello di linguaggio di grande scala ultra avanzato sviluppato da Baidu, che rispetto a ERNIE 3.5 ha subito un aggiornamento completo delle capacità del modello, ampiamente applicabile a scenari di compiti complessi in vari settori; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ERNIE-4.0-8K-Preview": {"description": "Modello di linguaggio di grande scala ultra avanzato sviluppato da Baidu, che rispetto a ERNIE 3.5 ha subito un aggiornamento completo delle capacità del modello, ampiamente applicabile a scenari di compiti complessi in vari settori; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Il modello linguistico ultra grande di Baidu, auto-sviluppato, offre eccellenti prestazioni generali, ampiamente utilizzabile in scenari complessi di vari settori; supporta l'integrazione automatica dei plugin di ricerca di Baidu, garantendo l'attualità delle informazioni nelle risposte. Rispetto a ERNIE 4.0, offre prestazioni superiori."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Modello di linguaggio di grande scala ultra avanzato sviluppa<PERSON> da Baidu, con prestazioni complessive eccezionali, ampiamente applicabile a scenari di compiti complessi in vari settori; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte. Rispetto a ERNIE 4.0, offre prestazioni superiori."}, "ERNIE-Character-8K": {"description": "Modello di linguaggio verticale sviluppato da Baidu, adatto per applicazioni come NPC nei giochi, dialoghi di assistenza clienti, e interpretazione di ruoli nei dialoghi, con uno stile di personaggio più distintivo e coerente, capacità di seguire le istruzioni più forte e prestazioni di inferenza superiori."}, "ERNIE-Lite-Pro-128K": {"description": "Modello di linguaggio leggero sviluppa<PERSON> da Baidu, che combina prestazioni eccellenti del modello con prestazioni di inferenza, con risultati migliori rispetto a ERNIE Lite, adatto per l'uso in schede di accelerazione AI a bassa potenza."}, "ERNIE-Speed-128K": {"description": "Modello di linguaggio ad alte prestazioni sviluppato da Baidu, lanciato nel 2024, con capacità generali eccellenti, adatto come modello di base per il fine-tuning, per gestire meglio le problematiche di scenari specifici, mantenendo al contempo prestazioni di inferenza eccezionali."}, "ERNIE-Speed-Pro-128K": {"description": "Modello di linguaggio ad alte prestazioni sviluppato da Baidu, lanciato nel 2024, con capacità generali eccellenti, risultati migliori rispetto a ERNIE Speed, adatto come modello di base per il fine-tuning, per gestire meglio le problematiche di scenari specifici, mantenendo al contempo prestazioni di inferenza eccezionali."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev è un modello multimodale di generazione e modifica di immagini sviluppato da Black Forest Labs, basato sull'architettura Rectified Flow Transformer, con una scala di 12 miliardi di parametri. Si concentra sulla generazione, ricostruzione, miglioramento o modifica di immagini in base a condizioni contestuali fornite. Combina i vantaggi della generazione controllata dei modelli di diffusione con la capacità di modellazione contestuale dei Transformer, supportando output di alta qualità e applicazioni estese come il restauro, il completamento e la ricostruzione di scene visive."}, "FLUX.1-dev": {"description": "FLUX.1-dev è un modello linguistico multimodale open source sviluppato da Black Forest Labs, ottimizzato per compiti testo-immagine, che integra capacità di comprensione e generazione sia visive che testuali. Basato su modelli linguistici avanzati come Mistral-7B, utilizza un codificatore visivo progettato con cura e un raffinamento a più fasi tramite istruzioni per realizzare capacità collaborative testo-immagine e ragionamento su compiti complessi."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) è un modello innovativo, adatto per applicazioni in più settori e compiti complessi."}, "HelloMeme": {"description": "HelloMeme è uno strumento AI che genera automaticamente meme, GIF o brevi video basati sulle immagini o azioni fornite dall'utente. Non richiede alcuna competenza in disegno o programmazione; basta fornire un'immagine di riferimento e lo strumento creerà contenuti belli, divertenti e coerenti nello stile."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full, lanciato da HiDream.ai, è un modello open source multimodale avanzato per l'editing di immagini, basato sull'architettura Diffusion Transformer e integrato con potenti capacità di comprensione linguistica (incluso LLaMA 3.1-8B-Instruct). Supporta la generazione di immagini, il trasferimento di stile, l'editing locale e la ridipintura tramite comandi in linguaggio naturale, offrendo eccellenti capacità di comprensione ed esecuzione testo-immagine."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled è un modello leggero di generazione di immagini da testo, ottimizzato tramite distillazione per produrre rapidamente immagini di alta qualità, particolarmente adatto a ambienti con risorse limitate e a compiti di generazione in tempo reale."}, "InstantCharacter": {"description": "InstantCharacter, rilasciato dal team AI di Tencent nel 2025, è un modello di generazione di personaggi personalizzati senza necessità di tuning, progettato per generare personaggi coerenti e ad alta fedeltà in diversi scenari. Supporta la modellazione del personaggio basata su una singola immagine di riferimento e consente di trasferire il personaggio in vari stili, pose e sfondi in modo flessibile."}, "InternVL2-8B": {"description": "InternVL2-8B è un potente modello linguistico visivo, supporta l'elaborazione multimodale di immagini e testo, in grado di riconoscere con precisione il contenuto delle immagini e generare descrizioni o risposte correlate."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B è un potente modello linguistico visivo, supporta l'elaborazione multimodale di immagini e testo, in grado di riconoscere con precisione il contenuto delle immagini e generare descrizioni o risposte correlate."}, "Kolors": {"description": "Kolors è un modello di generazione di immagini da testo sviluppato dal team Kolors di Kuaishou. Addestrato su miliardi di parametri, eccelle nella qualità visiva, nella comprensione semantica del cinese e nella resa del testo."}, "Kwai-Kolors/Kolors": {"description": "<PERSON><PERSON><PERSON>, sviluppato dal team Kolors di Kuaishou, è un modello di generazione di immagini da testo su larga scala basato su diffusione latente. Addestrato su miliardi di coppie testo-immagine, mostra vantaggi significativi nella qualità visiva, accuratezza semantica complessa e resa dei caratteri in cinese e inglese. Supporta input in entrambe le lingue e si distingue nella comprensione e generazione di contenuti specifici in cinese."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Eccellenti capacità di ragionamento visivo su immagini ad alta risoluzione, adatte per applicazioni di comprensione visiva."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Capacità avanzate di ragionamento visivo per applicazioni di agenti di comprensione visiva."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Modello di testo ottimizzato per le istruzioni di Llama 3.1, progettato per casi d'uso di dialogo multilingue, che si distingue in molti modelli di chat open source e chiusi in benchmark di settore comuni."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Modello di testo ottimizzato per le istruzioni di Llama 3.1, progettato per casi d'uso di dialogo multilingue, che si distingue in molti modelli di chat open source e chiusi in benchmark di settore comuni."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Modello di testo ottimizzato per le istruzioni di Llama 3.1, progettato per casi d'uso di dialogo multilingue, che si distingue in molti modelli di chat open source e chiusi in benchmark di settore comuni."}, "Meta-Llama-3.2-1B-Instruct": {"description": "Modello di linguaggio di piccole dimensioni all'avanguardia, dotato di comprensione linguistica, eccellenti capacità di ragionamento e generazione di testo."}, "Meta-Llama-3.2-3B-Instruct": {"description": "Modello di linguaggio di piccole dimensioni all'avanguardia, dotato di comprensione linguistica, eccellenti capacità di ragionamento e generazione di testo."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 è il modello di linguaggio open source multilingue più avanzato della serie Llama, che offre prestazioni paragonabili a un modello da 405B a un costo estremamente ridotto. Basato su una struttura Transformer e migliorato tramite fine-tuning supervisionato (SFT) e apprendimento rinforzato con feedback umano (RLHF) per aumentarne l'utilità e la sicurezza. La sua versione ottimizzata per le istruzioni è progettata per dialoghi multilingue, superando molti modelli di chat open source e chiusi in vari benchmark di settore. La data di conoscenza è dicembre 2023."}, "MiniMax-M1": {"description": "Modello di inferenza completamente sviluppato internamente. Leader mondiale: 80K catene di pensiero x 1M input, prestazioni paragonabili ai migliori modelli internazionali."}, "MiniMax-Text-01": {"description": "Nella serie di modelli MiniMax-01, abbiamo fatto un'innovazione audace: per la prima volta abbiamo implementato su larga scala un meccanismo di attenzione lineare, rendendo l'architettura Transformer tradizionale non più l'unica opzione. Questo modello ha un numero di parametri che raggiunge i 456 miliardi, con un'attivazione singola di 45,9 miliardi. Le prestazioni complessive del modello sono paragonabili a quelle dei migliori modelli internazionali, mentre è in grado di gestire in modo efficiente contesti globali lunghi fino a 4 milioni di token, 32 volte rispetto a GPT-4o e 20 volte rispetto a Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 è un modello di inferenza a grande scala con pesi open source e attenzione mista, con 456 miliardi di parametri, di cui circa 45,9 miliardi attivati per ogni token. Il modello supporta nativamente un contesto ultra-lungo di 1 milione di token e, grazie al meccanismo di attenzione lampo, riduce del 75% il carico computazionale in operazioni floating point rispetto a DeepSeek R1 in compiti di generazione con 100.000 token. Inoltre, MiniMax-M1 adotta un'architettura MoE (Mixture of Experts), combinando l'algoritmo CISPO e un design di attenzione mista per un addestramento efficiente tramite apprendimento rinforzato, raggiungendo prestazioni leader nel settore per inferenze con input lunghi e scenari reali di ingegneria software."}, "Moonshot-Kimi-K2-Instruct": {"description": "Con un totale di 1 trilione di parametri e 32 miliardi di parametri attivi, questo modello non pensante raggiunge livelli d'eccellenza in conoscenze all'avanguardia, matematica e programmazione, ed è particolarmente adatto a compiti di agenti generici. Ottimizzato per attività di agenti, non solo risponde a domande ma può anche agire. Ideale per chat improvvisate, conversazioni generiche e esperienze di agenti, è un modello riflessivo che non richiede lunghi tempi di elaborazione."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) è un modello di istruzioni ad alta precisione, adatto per calcoli complessi."}, "OmniConsistency": {"description": "OmniConsistency migliora la coerenza stilistica e la generalizzazione nei compiti di immagine a immagine introducendo Diffusion Transformers (DiTs) su larga scala e dati stilizzati accoppiati, prevenendo il degrado dello stile."}, "Phi-3-medium-128k-instruct": {"description": "<PERSON><PERSON><PERSON> modello Phi-3-medium, ma con una dimensione di contesto più grande per RAG o prompting a pochi colpi."}, "Phi-3-medium-4k-instruct": {"description": "Un modello con 14 miliardi di parametri, dimostra una qualità migliore rispetto a Phi-3-mini, con un focus su dati densi di ragionamento di alta qualità."}, "Phi-3-mini-128k-instruct": {"description": "<PERSON><PERSON>o modello Phi-3-mini, ma con una dimensione di contesto più grande per RAG o prompting a pochi colpi."}, "Phi-3-mini-4k-instruct": {"description": "Il membro più piccolo della famiglia Phi-3. <PERSON><PERSON><PERSON><PERSON><PERSON> sia per qualità che per bassa latenza."}, "Phi-3-small-128k-instruct": {"description": "<PERSON><PERSON>o modello Phi-3-small, ma con una dimensione di contesto più grande per RAG o prompting a pochi colpi."}, "Phi-3-small-8k-instruct": {"description": "Un modello con 7 miliardi di parametri, dimostra una qualità migliore rispetto a Phi-3-mini, con un focus su dati densi di ragionamento di alta qualità."}, "Phi-3.5-mini-instruct": {"description": "Versione aggiornata del modello Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "Versione aggiornata del modello Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct è un modello linguistico di grandi dimensioni con fine-tuning per istruzioni nella serie Qwen2, con una dimensione di 7B parametri. Questo modello si basa sull'architettura Transformer, utilizzando funzioni di attivazione SwiGLU, bias QKV di attenzione e attenzione a query di gruppo. È in grado di gestire input di grandi dimensioni. Ha dimostrato prestazioni eccellenti in comprensione linguistica, generazione, capacità multilingue, codifica, matematica e ragionamento in vari benchmark, superando la maggior parte dei modelli open source e mostrando competitività paragonabile a modelli proprietari in alcuni compiti. Qwen2-7B-Instruct ha mostrato miglioramenti significativi in vari test rispetto a Qwen1.5-7B-Chat."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct è uno dei più recenti modelli linguistici di grandi dimensioni rilasciati da Alibaba Cloud. Questo modello da 7B ha capacità notevolmente migliorate in codifica e matematica. Il modello offre anche supporto multilingue, coprendo oltre 29 lingue, tra cui cinese e inglese. Ha mostrato miglioramenti significativi nel seguire istruzioni, comprendere dati strutturati e generare output strutturati (soprattutto JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct è l'ultima versione della serie di modelli linguistici di grandi dimensioni specifici per il codice rilasciata da Alibaba Cloud. <PERSON><PERSON> modello, basato su Qwen2.5, ha migliorato significativamente le capacità di generazione, ragionamento e riparazione del codice grazie all'addestramento su 55 trilioni di token. Ha potenziato non solo le capacità di codifica, ma ha anche mantenuto i vantaggi nelle abilità matematiche e generali. Il modello fornisce una base più completa per applicazioni pratiche come agenti di codice."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL è il nuovo membro della serie Qwen, dotato di potenti capacità di comprensione visiva. È in grado di analizzare il testo, i grafici e il layout all'interno delle immagini, nonché di comprendere video lunghi e catturare eventi. <PERSON><PERSON><PERSON> effettuare ragionamenti, manipolare strumenti, supportare la localizzazione di oggetti in diversi formati e generare output strutturati. Inoltre, è stato ottimizzato per la formazione dinamica di risoluzione e frame rate nella comprensione video, migliorando l'efficienza dell'encoder visivo."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking è un modello di linguaggio visivo open source (VLM) rilasciato congiuntamente da Zhipu AI e dal laboratorio KEG dell'Università di Tsinghua, progettato specificamente per gestire compiti cognitivi multimodali complessi. Basato sul modello di base GLM-4-9B-0414, il modello introduce il meccanismo di ragionamento \"Catena di Pensiero\" (Chain-of-Thought) e utilizza strategie di apprendimento rinforzato, migliorando significativamente la capacità di ragionamento cross-modale e la stabilità."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat è la versione open source del modello pre-addestrato GLM-4 della serie sviluppata da Zhipu AI. Questo modello ha dimostrato prestazioni eccellenti in vari aspetti, tra cui semantica, matematica, ragionamento, codice e conoscenza. Oltre a supportare conversazioni multi-turno, GLM-4-9B-Chat offre anche funzionalità avanzate come navigazione web, esecuzione di codice, chiamate a strumenti personalizzati (Function Call) e ragionamento su testi lunghi. Il modello supporta 26 lingue, tra cui cinese, inglese, giapponese, coreano e tedesco. Ha mostrato prestazioni eccellenti in vari benchmark, come AlignBench-v2, MT-Bench, MMLU e C-Eval. Questo modello supporta una lunghezza di contesto massima di 128K, rendendolo adatto per ricerche accademiche e applicazioni commerciali."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 è un modello di inferenza guidato dall'apprendimento per rinforzo (RL) che affronta i problemi di ripetitività e leggibilità nel modello. Prima dell'RL, DeepSeek-R1 ha introdotto dati di cold start, ottimizzando ulteriormente le prestazioni di inferenza. Si comporta in modo comparabile a OpenAI-o1 in compiti matematici, di codifica e di inferenza, e migliora l'efficacia complessiva grazie a metodi di addestramento ben progettati."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B è un modello ottenuto attraverso il distillamento del knowledge da Qwen2.5-Math-7B. Questo modello è stato fine-tunato utilizzando 800.000 campioni selezionati generati da DeepSeek-R1, dimostrando un'ottima capacità di inferenza. Ha ottenuto risultati eccellenti in diversi benchmark, raggiungendo una precisione del 92,8% su MATH-500, un tasso di passaggio del 55,5% su AIME 2024 e una valutazione di 1189 su CodeForces, dimostrando una forte capacità matematica e di programmazione per un modello di 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 è un modello di linguaggio con 6710 miliardi di parametri, basato su un'architettura di esperti misti (MoE) che utilizza attenzione multilivello (MLA) e la strategia di bilanciamento del carico senza perdite ausiliarie, ottimizzando l'efficienza di inferenza e addestramento. Pre-addestrato su 14,8 trilioni di token di alta qualità e successivamente affinato tramite supervisione e apprendimento per rinforzo, DeepSeek-V3 supera altri modelli open source, avvicinandosi ai modelli chiusi di punta."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 è un modello base con architettura MoE dotato di potenti capacità di codice e agenti, con 1 trilione di parametri totali e 32 miliardi di parametri attivi. Nei test di benchmark su ragionamento generale, programmazione, matematica e agenti, il modello K2 supera altri modelli open source principali."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview è un modello di elaborazione del linguaggio naturale innovativo, in grado di gestire in modo efficiente compiti complessi di generazione di dialoghi e comprensione del contesto."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview è un modello di ricerca sviluppato dal team Qwen, focalizzato sulle capacità di inferenza visiva, con vantaggi unici nella comprensione di scenari complessi e nella risoluzione di problemi matematici legati alla visione."}, "Qwen/QwQ-32B": {"description": "QwQ è il modello di inferenza della serie Qwen. Rispetto ai tradizionali modelli di ottimizzazione delle istruzioni, QwQ possiede capacità di pensiero e ragionamento, consentendo prestazioni significativamente migliorate nei compiti downstream, specialmente nella risoluzione di problemi complessi. QwQ-32B è un modello di inferenza di medie dimensioni, in grado di ottenere prestazioni competitive rispetto ai modelli di inferenza all'avanguardia (come DeepSeek-R1, o1-mini). Questo modello utilizza tecnologie come RoPE, SwiGLU, RMSNorm e Attention QKV bias, con una struttura di rete a 64 strati e 40 teste di attenzione Q (nel GQA, KV è 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview è l'ultimo modello di ricerca sperimentale di Qwen, focalizzato sul miglioramento delle capacità di ragionamento dell'IA. Esplorando meccanismi complessi come la mescolanza linguistica e il ragionamento ricorsivo, i principali vantaggi includono potenti capacità di analisi del ragionamento, abilità matematiche e di programmazione. Tuttavia, ci sono anche problemi di cambio linguistico, cicli di ragionamento, considerazioni di sicurezza e differenze in altre capacità."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 è un modello di linguaggio universale avanzato, supportando vari tipi di istruzioni."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct è un modello linguistico di grandi dimensioni con fine-tuning per istruzioni nella serie Qwen2, con una dimensione di 72B parametri. Questo modello si basa sull'architettura Transformer, utilizzando funzioni di attivazione SwiGLU, bias QKV di attenzione e attenzione a query di gruppo. È in grado di gestire input di grandi dimensioni. Ha dimostrato prestazioni eccellenti in comprensione linguistica, generazione, capacità multilingue, codifica, matematica e ragionamento in vari benchmark, superando la maggior parte dei modelli open source e mostrando competitività paragonabile a modelli proprietari in alcuni compiti."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL è l'ultima iterazione del modello Qwen-VL, che ha raggiunto prestazioni all'avanguardia nei benchmark di comprensione visiva."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 è una nuova serie di modelli di linguaggio di grandi dimensioni, progettata per ottimizzare l'elaborazione di compiti istruzionali."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 è una nuova serie di modelli di linguaggio di grandi dimensioni, progettata per ottimizzare l'elaborazione di compiti istruzionali."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Un grande modello linguistico sviluppato dal team di Alibaba Cloud Tongyi Qianwen"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 è una nuova serie di modelli linguistici di grandi dimensioni, con una maggiore capacità di comprensione e generazione."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 è una nuova serie di modelli linguistici di grandi dimensioni, progettata per ottimizzare l'elaborazione dei compiti istruzionali."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 è una nuova serie di modelli di linguaggio di grandi dimensioni, progettata per ottimizzare l'elaborazione di compiti istruzionali."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 è una nuova serie di modelli linguistici di grandi dimensioni, progettata per ottimizzare l'elaborazione dei compiti istruzionali."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder si concentra sulla scrittura di codice."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct è l'ultima versione della serie di modelli linguistici di grandi dimensioni specifici per il codice rilasciata da Alibaba Cloud. <PERSON><PERSON> modello, basato su Qwen2.5, ha migliorato significativamente le capacità di generazione, ragionamento e riparazione del codice grazie all'addestramento su 55 trilioni di token. Ha potenziato non solo le capacità di codifica, ma ha anche mantenuto i vantaggi nelle abilità matematiche e generali. Il modello fornisce una base più completa per applicazioni pratiche come agenti di codice."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct è un modello multimodale di grande dimensione sviluppato dal team di Qwen2.5-VL, parte della serie Qwen2.5-VL. Questo modello non solo è in grado di riconoscere oggetti comuni, ma può anche analizzare testo, grafici, icone, disegni e layout all'interno delle immagini. Funziona come un agente visivo, capace di ragionare e manipolare strumenti in modo dinamico, con la capacità di utilizzare computer e telefoni cellulari. In<PERSON>re, questo modello può localizzare con precisione gli oggetti all'interno delle immagini e generare output strutturati per fatture, tabelle e altro ancora. Rispetto al modello precedente Qwen2-VL, questa versione ha visto un miglioramento nelle capacità matematiche e di risoluzione di problemi grazie al learning by reinforcement, e il suo stile di risposta è più allineato alle preferenze umane."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL è un modello di linguaggio visivo della serie Qwen2.5. Questo modello presenta miglioramenti significativi in diversi aspetti: dispone di una capacità di comprensione visiva migliore, in grado di riconoscere oggetti comuni, analizzare testi, grafici e layout; come agente visivo, può ragionare e guidare dinamicamente l'uso degli strumenti; supporta la comprensione di video di durata superiore a un'ora e la cattura di eventi chiave; può localizzare oggetti nelle immagini con precisione attraverso la generazione di bounding box o punti; supporta la generazione di output strutturati, particolarmente adatti a dati scannerizzati come fatture e tabelle."}, "Qwen/Qwen3-14B": {"description": "Qwen3 è un nuovo modello di Tongyi Qianwen con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in ragionamento, generico, agenti e multilingue, e supporta il passaggio della modalità di pensiero."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 è un nuovo modello di Tongyi Qianwen con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in ragionamento, generico, agenti e multilingue, e supporta il passaggio della modalità di pensiero."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 è un modello linguistico di grandi dimensioni ibrido esperto (MoE) di punta sviluppato dal team Tongyi Qianwen di Alibaba Cloud. Con 235 miliardi di parametri totali e 22 miliardi attivi per inferenza, è una versione aggiornata del modello non pensante Qwen3-235B-A22B, focalizzata su miglioramenti significativi in aderenza alle istruzioni, ragionamento logico, comprensione testuale, matematica, scienza, programmazione e uso di strumenti. Inoltre, amplia la copertura di conoscenze multilingue e allinea meglio le preferenze degli utenti in compiti soggettivi e aperti, generando testi più utili e di alta qualità."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 è un modello linguistico di grandi dimensioni della serie Qwen3 sviluppato dal team Tongyi Qianwen di Alibaba, specializzato in compiti di ragionamento complessi. Basato su architettura MoE con 235 miliardi di parametri totali e circa 22 miliardi attivi per token, combina alta efficienza computazionale con prestazioni elevate. Come modello di “pensiero”, eccelle in ragionamento logico, matematica, scienza, programmazione e test accademici, raggiungendo livelli top tra i modelli open source di ragionamento. Migliora anche capacità generali come aderenza alle istruzioni, uso di strumenti e generazione testuale, supportando nativamente contesti lunghi fino a 256K token, ideale per scenari di ragionamento profondo e gestione di documenti estesi."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 è un nuovo modello di Tongyi Qianwen con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in ragionamento, generico, agenti e multilingue, e supporta il passaggio della modalità di pensiero."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 è una versione aggiornata della modalità non pensante di Qwen3-30B-A3B. Si tratta di un modello esperto misto (MoE) con un totale di 30,5 miliardi di parametri e 3,3 miliardi di parametri attivi. Il modello presenta miglioramenti chiave in diversi ambiti, tra cui un significativo potenziamento nella capacità di seguire istruzioni, ragionamento logico, comprensione del testo, matematica, scienze, programmazione e utilizzo di strumenti. In<PERSON>re, ha fatto progressi sostanziali nella copertura della conoscenza multilingue a coda lunga e si allinea meglio alle preferenze degli utenti in compiti soggettivi e aperti, permettendo di generare risposte più utili e testi di qualità superiore. La capacità di comprensione di testi lunghi è stata estesa fino a 256K. Questo modello supporta esclusivamente la modalità non pensante e non genera tag `<think></think>` nell'output."}, "Qwen/Qwen3-32B": {"description": "Qwen3 è un nuovo modello di Tongyi Qianwen con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in ragionamento, generico, agenti e multilingue, e supporta il passaggio della modalità di pensiero."}, "Qwen/Qwen3-8B": {"description": "Qwen3 è un nuovo modello di Tongyi Qianwen con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in ragionamento, generico, agenti e multilingue, e supporta il passaggio della modalità di pensiero."}, "Qwen2-72B-Instruct": {"description": "Qwen2 è l'ultima serie del modello Qwen, supporta un contesto di 128k, e rispetto ai modelli open source attualmente migliori, Qwen2-72B supera significativamente i modelli leader attuali in comprensione del linguaggio naturale, conoscenza, codice, matematica e capacità multilingue."}, "Qwen2-7B-Instruct": {"description": "Qwen2 è l'ultima serie del modello Qwen, in grado di superare i modelli open source ottimali di dimensioni simili e anche modelli di dimensioni maggiori. Qwen2 7B ha ottenuto vantaggi significativi in vari test, in particolare nella comprensione del codice e del cinese."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B è un potente modello di linguaggio visivo, supporta l'elaborazione multimodale di immagini e testo, in grado di riconoscere con precisione il contenuto delle immagini e generare descrizioni o risposte correlate."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct è un grande modello linguistico con 14 miliardi di parametri, con prestazioni eccellenti, otti<PERSON><PERSON><PERSON> per scenari in cinese e multilingue, supporta applicazioni di domande e risposte intelligenti, generazione di contenuti e altro."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct è un grande modello linguistico con 32 miliardi di parametri, con prestazioni equilibrate, ottimizzato per scenari in cinese e multilingue, supporta applicazioni di domande e risposte intelligenti, generazione di contenuti e altro."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct supporta un contesto di 16k, generando testi lunghi oltre 8K. Supporta chiamate di funzione e interazioni senza soluzione di continuità con sistemi esterni, aumentando notevolmente flessibilità e scalabilità. La conoscenza del modello è notevolmente aumentata e ha migliorato significativamente le capacità di codifica e matematica, con supporto per oltre 29 lingue."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct è un grande modello linguistico con 7 miliardi di parametri, supporta chiamate di funzione e interazioni senza soluzione di continuità con sistemi esterni, aumentando notevolmente flessibilità e scalabilità. Ottimizzato per scenari in cinese e multilingue, supporta applicazioni di domande e risposte intelligenti, generazione di contenuti e altro."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct è un modello di istruzioni per la programmazione basato su un pre-addestramento su larga scala, con potenti capacità di comprensione e generazione del codice, in grado di gestire in modo efficiente vari compiti di programmazione, particolarmente adatto per la scrittura intelligente di codice, la generazione di script automatizzati e la risoluzione di problemi di programmazione."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct è un grande modello linguistico progettato per la generazione di codice, la comprensione del codice e scenari di sviluppo efficienti, con una scala di 32 miliardi di parametri all'avanguardia nel settore, in grado di soddisfare esigenze di programmazione diversificate."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B è un modello MoE (esperto misto) che introduce la “modalità di ragionamento ibrido”, consentendo agli utenti di passare senza soluzione di continuità tra la modalità “pensante” e quella “non pensante”. Supporta la comprensione e il ragionamento in 119 lingue e dialetti, dispone di potenti capacità di chiamata di strumenti e compete con i principali modelli di mercato come DeepSeek R1, OpenAI o1, o3-mini, Grok 3 e Google Gemini 2.5 Pro in vari benchmark relativi a capacità generali, codice e matematica, competenze multilingue, conoscenza e ragionamento."}, "Qwen3-32B": {"description": "Qwen3-32B è un modello denso (Dense Model) che introduce la “modalità di ragionamento ibrido”, permettendo agli utenti di passare senza soluzione di continuità tra la modalità “pensante” e quella “non pensante”. Grazie a miglioramenti nell'architettura del modello, all'aumento dei dati di addestramento e a metodi di training più efficaci, le prestazioni complessive sono comparabili a quelle di Qwen2.5-72B."}, "SenseChat": {"description": "Modello di base (V4), lunghezza del contesto di 4K, con potenti capacità generali."}, "SenseChat-128K": {"description": "<PERSON>lo di base (V4), lunghezza del contesto di 128K, si distingue in compiti di comprensione e generazione di testi lunghi."}, "SenseChat-32K": {"description": "<PERSON>lo di base (V4), lunghezza del contesto di 32K, applicabile in vari scenari."}, "SenseChat-5": {"description": "Modello dell'ultima versione (V5.5), lunghezza del contesto di 128K, con capacità significativamente migliorate in ragionamento matematico, conversazioni in inglese, seguire istruzioni e comprensione di testi lunghi, paragonabile a GPT-4o."}, "SenseChat-5-1202": {"description": "Basato sulla versione V5.5 più recente, mostra miglioramenti significativi rispetto alla versione precedente in capacità di base in cinese e inglese, chat, conoscenze scientifiche e umanistiche, scrittura, logica matematica e controllo della lunghezza del testo."}, "SenseChat-5-Cantonese": {"description": "Lunghezza del contesto di 32K, supera GPT-4 nella comprensione delle conversazioni in cantonese, paragonabile a GPT-4 Turbo in vari ambiti come conoscenza, ragionamento, matematica e scrittura di codice."}, "SenseChat-5-beta": {"description": "Alcune prestazioni superiori a SenseCat-5-1202"}, "SenseChat-Character": {"description": "Modello standard, lunghezza del contesto di 8K, alta velocità di risposta."}, "SenseChat-Character-Pro": {"description": "<PERSON><PERSON>, lunghezza del contesto di 32K, capacità complessivamente migliorate, supporta conversazioni in cinese/inglese."}, "SenseChat-Turbo": {"description": "<PERSON>tto per domande e risposte rapide, scenari di micro-ottimizzazione del modello."}, "SenseChat-Turbo-1202": {"description": "È l'ultima versione leggera del modello, raggi<PERSON>do oltre il 90% delle capacità del modello completo, riducendo significativamente i costi di inferenza."}, "SenseChat-Vision": {"description": "L'ultima versione del modello (V5.5) supporta l'input di più immagini, ottimizzando le capacità di base del modello, con notevoli miglioramenti nel riconoscimento delle proprietà degli oggetti, nelle relazioni spaziali, nel riconoscimento degli eventi, nella comprensione delle scene, nel riconoscimento delle emozioni, nel ragionamento logico e nella comprensione e generazione del testo."}, "SenseNova-V6-5-Pro": {"description": "Attraverso un aggiornamento completo dei dati multimodali, linguistici e di ragionamento e l'ottimizzazione delle strategie di addestramento, il nuovo modello ha ottenuto miglioramenti significativi nelle capacità di ragionamento multimodale e nel seguire istruzioni generalizzate. Supporta una finestra contestuale fino a 128k e si distingue in compiti specializzati come il riconoscimento OCR e l'identificazione di IP culturali e turistici."}, "SenseNova-V6-5-Turbo": {"description": "Attraverso un aggiornamento completo dei dati multimodali, linguistici e di ragionamento e l'ottimizzazione delle strategie di addestramento, il nuovo modello ha ottenuto miglioramenti significativi nelle capacità di ragionamento multimodale e nel seguire istruzioni generalizzate. Supporta una finestra contestuale fino a 128k e si distingue in compiti specializzati come il riconoscimento OCR e l'identificazione di IP culturali e turistici."}, "SenseNova-V6-Pro": {"description": "Realizza un'unificazione nativa delle capacità di immagini, testi e video, superando i limiti tradizionali della multimodalità disgiunta, e ha conquistato il doppio campionato nelle valutazioni OpenCompass e SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "Equilibra il ragionamento profondo visivo e linguistico, realizzando un pensiero lento e un ragionamento approfondito, presentando un processo completo della catena di pensiero."}, "SenseNova-V6-Turbo": {"description": "Realizza un'unificazione nativa delle capacità di immagini, testi e video, superando i limiti tradizionali della multimodalità disgiunta, e si distingue in modo completo nelle dimensioni fondamentali delle capacità multimodali e linguistiche, eccellendo in molte valutazioni e posizionandosi ripetutamente ai vertici nazionali e internazionali."}, "Skylark2-lite-8k": {"description": "Il modello di seconda generazione Skylark (Skylark2-lite) ha un'elevata velocità di risposta, adatto per scenari in cui sono richieste elevate prestazioni in tempo reale, attento ai costi e con requisiti di precisione del modello non elevati, con una lunghezza della finestra di contesto di 8k."}, "Skylark2-pro-32k": {"description": "Il modello di seconda generazione Skylark (Skylark2-pro) offre una maggiore precisione, adatto per scenari complessi di generazione di testi, come la scrittura di contenuti in ambito professionale, narrativa e traduzioni di alta qualità, con una lunghezza della finestra di contesto di 32k."}, "Skylark2-pro-4k": {"description": "Il modello di seconda generazione Skylark (Skylark2-pro) offre una maggiore precisione, adatto per scenari complessi di generazione di testi, come la scrittura di contenuti in ambito professionale, narrativa e traduzioni di alta qualità, con una lunghezza della finestra di contesto di 4k."}, "Skylark2-pro-character-4k": {"description": "Il modello di seconda generazione Skylark (Skylark2-pro-character) presenta eccellenti capacità di role-playing e chat, specializzandosi nel recitare diversi ruoli in base alle richieste dell'utente e nel portare avanti conversazioni naturali e fluide. È adatto per la creazione di chatbot, assistenti virtuali e customer service online, con elevate velocità di risposta."}, "Skylark2-pro-turbo-8k": {"description": "Il modello di seconda generazione Skylark (Skylark2-pro-turbo-8k) è più veloce nell'inferenza e più economico, con una lunghezza della finestra di contesto di 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 è il nuovo modello open source della serie GLM, con 32 miliardi di parametri. Le sue prestazioni sono comparabili a quelle della serie GPT di OpenAI e della serie V3/R1 di DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 è un modello di piccole dimensioni della serie GLM, con 9 miliardi di parametri. Questo modello eredita le caratteristiche tecniche della serie GLM-4-32B, ma offre opzioni di distribuzione più leggere. Nonostante le dimensioni ridotte, GLM-4-9B-0414 mostra ancora capacità eccezionali in generazione di codice, progettazione di pagine web, generazione di grafica SVG e scrittura basata su ricerca."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking è un modello di linguaggio visivo open source (VLM) rilasciato congiuntamente da Zhipu AI e dal laboratorio KEG dell'Università di Tsinghua, progettato specificamente per gestire compiti cognitivi multimodali complessi. Basato sul modello di base GLM-4-9B-0414, il modello introduce il meccanismo di ragionamento \"Catena di Pensiero\" (Chain-of-Thought) e utilizza strategie di apprendimento rinforzato, migliorando significativamente la capacità di ragionamento cross-modale e la stabilità."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 è un modello di inferenza con capacità di pensiero profondo. Questo modello è stato sviluppato sulla base di GLM-4-32B-0414 attraverso un avvio a freddo e un apprendimento rinforzato esteso, ed è stato ulteriormente addestrato in compiti di matematica, codice e logica. Rispetto al modello di base, GLM-Z1-32B-0414 ha migliorato significativamente le capacità matematiche e la capacità di risolvere compiti complessi."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 è un modello di piccole dimensioni della serie GLM, con soli 9 miliardi di parametri, ma mantiene un'incredibile capacità pur rispettando la tradizione open source. Nonostante le dimensioni ridotte, questo modello mostra prestazioni eccezionali in ragionamento matematico e compiti generali, posizionandosi ai vertici tra i modelli open source di dimensioni simili."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 è un modello di inferenza profonda con capacità di riflessione (paragonabile alla Deep Research di OpenAI). A differenza dei modelli tipici di pensiero profondo, il modello di riflessione utilizza un tempo di riflessione più lungo per affrontare problemi più aperti e complessi."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B è una versione open source, progettata per fornire un'esperienza di dialogo ottimizzata per applicazioni conversazionali."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B è il primo modello di ragionamento a lungo contesto di grandi dimensioni (LRM) addestrato con apprendimento rinforzato, ottimizzato per compiti di ragionamento su testi lunghi. Il modello utilizza un framework di apprendimento rinforzato con espansione progressiva del contesto, garantendo una transizione stabile da contesti brevi a lunghi. <PERSON>ei sette benchmark di domande su documenti a lungo contesto, QwenLong-L1-32B supera modelli di punta come OpenAI-o3-mini e Qwen3-235B-A22B, con prestazioni comparabili a Claude-3.7-Sonnet-Thinking. Eccelle in ragionamento matematico, logico e multi-hop."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, mantenendo le eccellenti capacità linguistiche generali della serie originale, ha notevolmente migliorato la logica matematica e le capacità di codifica attraverso un addestramento incrementale su 500 miliardi di token di alta qualità."}, "abab5.5-chat": {"description": "Focalizzato su scenari di produttività, supporta l'elaborazione di compiti complessi e la generazione di testo efficiente, adatto per applicazioni professionali."}, "abab5.5s-chat": {"description": "Progettato per scenari di dialogo con personaggi cinesi, offre capacità di generazione di dialoghi di alta qualità, adatto per vari scenari applicativi."}, "abab6.5g-chat": {"description": "Progettato per dialoghi con personaggi multilingue, supporta la generazione di dialoghi di alta qualità in inglese e in molte altre lingue."}, "abab6.5s-chat": {"description": "Adatto per una vasta gamma di compiti di elaborazione del linguaggio naturale, inclusa la generazione di testo e i sistemi di dialogo."}, "abab6.5t-chat": {"description": "O<PERSON><PERSON><PERSON><PERSON> per scenari di dialogo con personaggi cinesi, offre capacità di generazione di dialoghi fluida e conforme alle espressioni cinesi."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 è un modello linguistico di grandi dimensioni all'avanguardia, ottimizzato tramite apprendimento rinforzato e dati di cold start, con prestazioni eccezionali nel ragionamento, nella matematica e nella programmazione."}, "accounts/fireworks/models/deepseek-v3": {"description": "Un potente modello linguistico Mixture-of-Experts (MoE) for<PERSON><PERSON> da Deepseek, con un totale di 671B di parametri, attivando 37B di parametri per ogni token."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Il modello di istruzioni Llama 3 70B è ottimizzato per dialoghi multilingue e comprensione del linguaggio naturale, superando le prestazioni della maggior parte dei modelli concorrenti."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Il modello di istruzioni Llama 3 8B è ottimizzato per dialoghi e compiti multilingue, offrendo prestazioni eccellenti e alta efficienza."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Il modello di istruzioni Llama 3 8B (versione HF) è coerente con i risultati dell'implementazione ufficiale, garantendo alta coerenza e compatibilità cross-platform."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Il modello di istruzioni Llama 3.1 405B ha parametri su scala estremamente grande, adatto per compiti complessi e seguimento di istruzioni in scenari ad alto carico."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Il modello di istruzioni Llama 3.1 70B offre capacità superiori di comprensione e generazione del linguaggio, ideale per compiti di dialogo e analisi."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Il modello di istruzioni Llama 3.1 8B è ottimizzato per dialoghi multilingue, in grado di superare la maggior parte dei modelli open e closed source su benchmark di settore comuni."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Modello di ragionamento visivo di Meta con 11 miliardi di parametri. <PERSON>o modello è ottimizzato per il riconoscimento visivo, il ragionamento visivo, la descrizione delle immagini e la risposta a domande generali riguardanti le immagini. <PERSON>o modello è in grado di comprendere dati visivi, come grafici e tabelle, e colmare il divario tra visione e linguaggio generando descrizioni testuali dei dettagli delle immagini."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Il modello di istruzioni Llama 3.2 3B è un modello multilingue leggero lanciato da Meta. Questo modello è progettato per migliorare l'efficienza, offrendo miglioramenti significativi in termini di latenza e costi rispetto a modelli più grandi. I casi d'uso esemplari di questo modello includono query e riscrittura di suggerimenti, nonché supporto alla scrittura."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Modello di ragionamento visivo di Meta con 90 miliardi di parametri. <PERSON>o modello è ottimizzato per il riconoscimento visivo, il ragionamento visivo, la descrizione delle immagini e la risposta a domande generali riguardanti le immagini. <PERSON>o modello è in grado di comprendere dati visivi, come grafici e tabelle, e colmare il divario tra visione e linguaggio generando descrizioni testuali dei dettagli delle immagini."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct è la versione aggiornata di dicembre di Llama 3.1 70B. Questo modello è stato migliorato rispetto a Llama 3.1 70B (rilasciato a luglio 2024), potenziando le capacità di chiamata degli strumenti, il supporto per testi multilingue, le abilità matematiche e di programmazione. Il modello raggiunge livelli di eccellenza nel ragionamento, nella matematica e nel rispetto delle istruzioni, offrendo prestazioni simili a quelle di 3.1 405B, con vantaggi significativi in termini di velocità e costi."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Modello con 24B di parametri, dotato di capacità all'avanguardia comparabili a modelli di dimensioni maggiori."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Il modello di istruzioni Mixtral MoE 8x22B, con parametri su larga scala e architettura multi-esperto, supporta in modo completo l'elaborazione efficiente di compiti complessi."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Il modello di istruzioni Mixtral MoE 8x7B, con architettura multi-esperto, offre un'elevata efficienza nel seguire e eseguire istruzioni."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "Il modello MythoMax L2 13B combina tecnologie di fusione innovative, specializzandosi in narrazione e interpretazione di ruoli."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Il modello di istruzioni Phi 3 Vision è un modello multimodale leggero, in grado di gestire informazioni visive e testuali complesse, con forti capacità di ragionamento."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "Il modello QwQ è un modello di ricerca sperimentale sviluppato dal team Qwen, focalizzato sul potenziamento delle capacità di ragionamento dell'IA."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "La versione 72B del modello Qwen-VL è il risultato dell'ultima iterazione di Alibaba, rappresentando quasi un anno di innovazione."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 è una serie di modelli linguistici solo decoder sviluppata dal team Qwen di Alibaba Cloud. Questi modelli offrono dimensioni diverse, tra cui 0.5B, 1.5B, 3B, 7B, 14B, 32B e 72B, e ci sono varianti base e di istruzione."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct è l'ultima versione della serie di modelli linguistici di grandi dimensioni specifici per il codice rilasciata da Alibaba Cloud. <PERSON><PERSON> modello, basato su Qwen2.5, ha migliorato significativamente le capacità di generazione, ragionamento e riparazione del codice grazie all'addestramento su 55 trilioni di token. Ha potenziato non solo le capacità di codifica, ma ha anche mantenuto i vantaggi nelle abilità matematiche e generali. Il modello fornisce una base più completa per applicazioni pratiche come agenti di codice."}, "accounts/yi-01-ai/models/yi-large": {"description": "Il modello Yi-Large offre capacità eccezionali di elaborazione multilingue, utilizzabile per vari compiti di generazione e comprensione del linguaggio."}, "ai21-jamba-1.5-large": {"description": "Un modello multilingue con 398 miliardi di parametri (94 miliardi attivi), offre una finestra di contesto lunga 256K, chiamata di funzione, output strutturato e generazione ancorata."}, "ai21-jamba-1.5-mini": {"description": "Un modello multilingue con 52 miliardi di parametri (12 miliardi attivi), offre una finestra di contesto lunga 256K, chiamata di funzione, output strutturato e generazione ancorata."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Un modello multilingue con 398 miliardi di parametri (94 miliardi attivi), offre una finestra contestuale lunga 256K token, chiamate di funzione, output strutturati e generazione basata su fatti."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Un modello multilingue con 52 miliardi di parametri (12 miliardi attivi), offre una finestra contestuale lunga 256K token, chiamate di funzione, output strutturati e generazione basata su fatti."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet ha elevato gli standard del settore, superando i modelli concorrenti e Claude 3 Opus, dimostrando prestazioni eccezionali in una vasta gamma di valutazioni, mantenendo la velocità e i costi dei nostri modelli di livello medio."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet ha elevato gli standard del settore, superando le prestazioni dei modelli concorrenti e di Claude 3 Opus, dimostrando eccellenza in una vasta gamma di valutazioni, mantenendo al contempo la velocità e i costi dei nostri modelli di livello medio."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku è il modello più veloce e compatto di Anthropic, offrendo una velocità di risposta quasi istantanea. Può rispondere rapidamente a query e richieste semplici. I clienti saranno in grado di costruire un'esperienza AI senza soluzione di continuità che imita l'interazione umana. Claude 3 Haiku può gestire immagini e restituire output testuali, con una finestra di contesto di 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus è il modello AI più potente di Anthropic, con prestazioni all'avanguardia in compiti altamente complessi. Può gestire prompt aperti e scenari mai visti prima, con un'eccellente fluidità e comprensione simile a quella umana. Claude 3 Opus mostra le possibilità all'avanguardia dell'AI generativa. Claude 3 Opus può gestire immagini e restituire output testuali, con una finestra di contesto di 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet di Anthropic raggiunge un equilibrio ideale tra intelligenza e velocità, particolarmente adatto per carichi di lavoro aziendali. Offre la massima utilità a un prezzo inferiore rispetto ai concorrenti ed è progettato per essere un pilastro affidabile e durevole, adatto per implementazioni AI su larga scala. Claude 3 Sonnet può gestire immagini e restituire output testuali, con una finestra di contesto di 200K."}, "anthropic.claude-instant-v1": {"description": "Un modello veloce, economico e comunque molto capace, in grado di gestire una serie di compiti, tra cui conversazioni quotidiane, analisi testuali, sintesi e domande e risposte su documenti."}, "anthropic.claude-v2": {"description": "Un modello di Anthropic che dimostra elevate capacità in una vasta gamma di compiti, dalla conversazione complessa alla generazione di contenuti creativi, fino al seguire istruzioni dettagliate."}, "anthropic.claude-v2:1": {"description": "Versione aggiornata di Claude 2, con una finestra di contesto doppia e miglioramenti nella affidabilità, nel tasso di allucinazione e nell'accuratezza basata su prove nei contesti di documenti lunghi e RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku è il modello più veloce e compatto di Anthropic, progettato per fornire risposte quasi istantanee. Ha prestazioni direzionali rapide e accurate."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus è il modello più potente di Anthropic per gestire compiti altamente complessi. Eccelle in prestazioni, intelligenza, fluidità e comprensione."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku è il modello di nuova generazione più veloce di Anthropic. Rispetto a Claude 3 Haiku, Claude 3.5 <PERSON><PERSON> ha migliorato le proprie capacità e ha superato il modello più grande della generazione precedente, Claude 3 Opus, in molti test di intelligenza."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet offre capacità superiori rispetto a Opus e una velocità maggiore rispetto a Sonnet, mantenendo lo stesso prezzo di Sonnet. Sonnet è particolarmente abile in programmazione, scienza dei dati, elaborazione visiva e compiti di agenzia."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet è il modello più intelligente di Anthropic fino ad oggi ed è il primo modello di ragionamento ibrido sul mercato. Claude 3.7 Sonnet può generare risposte quasi istantanee o pensieri prolungati e graduali, consentendo agli utenti di vedere chiaramente questi processi. Sonnet è particolarmente abile nella programmazione, nella scienza dei dati, nell'elaborazione visiva e nei compiti di agenzia."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 è il modello più potente di Anthropic per gestire compiti altamente complessi. Eccelle in prestazioni, intelligenza, fluidità e capacità di comprensione."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 può generare risposte quasi istantanee o un ragionamento esteso e graduale, che gli utenti possono osservare chiaramente. Gli utenti API possono anche controllare con precisione il tempo di riflessione del modello."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B è un modello linguistico di grandi dimensioni a parametri sparsi con 72 miliardi di parametri totali e 16 miliardi di parametri attivati, basato sull'architettura Mixture of Group Experts (MoGE). Durante la fase di selezione degli esperti, gli esperti sono raggruppati e il token attiva un numero uguale di esperti all'interno di ogni gruppo, garantendo un bilanciamento del carico degli esperti e migliorando significativamente l'efficienza di distribuzione del modello sulla piattaforma Ascend."}, "aya": {"description": "Aya 23 è un modello multilingue lanciato da Cohere, supporta 23 lingue, facilitando applicazioni linguistiche diversificate."}, "aya:35b": {"description": "Aya 23 è un modello multilingue lanciato da Cohere, supporta 23 lingue, facilitando applicazioni linguistiche diversificate."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B è un modello di linguaggio open source sviluppato da Baichuan Intelligence, con 13 miliardi di parametri, che ha ottenuto i migliori risultati nella sua categoria in benchmark autorevoli sia in cinese che in inglese."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B è un modello linguistico di grandi dimensioni sviluppato da Baidu basato sull'architettura Mixture of Experts (MoE). Il modello ha un totale di 300 miliardi di parametri, ma durante l'inferenza attiva solo 47 miliardi di parametri per token, garantendo così un equilibrio tra prestazioni elevate ed efficienza computazionale. Come uno dei modelli principali della serie ERNIE 4.5, eccelle in compiti di comprensione del testo, generazione, ragionamento e programmazione. Il modello utilizza un innovativo metodo di pre-addestramento multimodale eterogeneo MoE, addestrando congiuntamente testo e modalità visive, migliorando efficacemente le capacità complessive, con risultati particolarmente evidenti nell'aderenza alle istruzioni e nella memoria della conoscenza mondiale."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse è un modello multilingue ad alte prestazioni da 32B, progettato per sfidare le prestazioni dei modelli monolingue attraverso innovazioni in ottimizzazione delle istruzioni, arbitraggio dei dati, addestramento delle preferenze e fusione dei modelli. Supporta 23 lingue."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse è un modello multilingue ad alte prestazioni da 8B, progettato per sfidare le prestazioni dei modelli monolingue attraverso innovazioni in ottimizzazione delle istruzioni, arbitraggio dei dati, addestramento delle preferenze e fusione dei modelli. Supporta 23 lingue."}, "c4ai-aya-vision-32b": {"description": "Aya Vision è un modello multimodale all'avanguardia, eccellente in diversi benchmark chiave per capacità linguistiche, testuali e visive. Supporta 23 lingue. Questa versione da 32 miliardi di parametri si concentra sulle prestazioni multilingue all'avanguardia."}, "c4ai-aya-vision-8b": {"description": "Aya Vision è un modello multimodale all'avanguardia, eccellente in diversi benchmark chiave per capacità linguistiche, testuali e visive. Questa versione da 8 miliardi di parametri si concentra su bassa latenza e prestazioni ottimali."}, "charglm-3": {"description": "CharGLM-3 è progettato per il gioco di ruolo e la compagnia emotiva, supporta una memoria multi-turno ultra-lunga e dialoghi personalizzati, con ampie applicazioni."}, "charglm-4": {"description": "CharGLM-4 è progettato per il gioco di ruolo e la compagnia emotiva, supportando una memoria multi-turno ultra-lunga e dialoghi personalizzati, con ampie applicazioni."}, "chatglm3": {"description": "ChatGLM3 è un modello a sorgente chiusa sviluppato da Zhipu AI e dal laboratorio KEG di Tsinghua. Dopo un pre-addestramento su una vasta quantità di identificatori cinesi e inglesi e un addestramento allineato alle preferenze umane, rispetto alla prima generazione di modelli, ha ottenuto miglioramenti del 16%, 36% e 280% rispettivamente in MMLU, C-Eval e GSM8K, e ha raggiunto il vertice della classifica C-Eval per compiti in cinese. È adatto a scenari che richiedono un alto livello di conoscenza, capacità di ragionamento e creatività, come la stesura di testi pubblicitari, la scrittura di romanzi, la composizione di testi informativi e la generazione di codice."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base è il modello di base open source più recente della serie ChatGLM, sviluppato da Zhipu con una dimensione di 6 miliardi di parametri."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o è un modello dinamico, aggiornato in tempo reale per mantenere la versione più recente. Combina una potente comprensione e generazione del linguaggio, adatta a scenari di applicazione su larga scala, inclusi servizi clienti, educazione e supporto tecnico."}, "claude-2.0": {"description": "Claude 2 offre progressi nelle capacità chiave per le aziende, inclusi contesti leader del settore fino a 200K token, riduzione significativa della frequenza di allucinazioni del modello, suggerimenti di sistema e una nuova funzionalità di test: chiamate di strumenti."}, "claude-2.1": {"description": "Claude 2 offre progressi nelle capacità chiave per le aziende, inclusi contesti leader del settore fino a 200K token, riduzione significativa della frequenza di allucinazioni del modello, suggerimenti di sistema e una nuova funzionalità di test: chiamate di strumenti."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku è il modello di prossima generazione più veloce di Anthropic. Rispetto a Claude 3 Haiku, Claude 3.5 <PERSON><PERSON> ha migliorato le proprie capacità in vari ambiti e ha superato il modello di generazione precedente, Claude 3 Opus, in molti test di intelligenza."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet offre capacità superiori a Opus e velocità più elevate rispetto a Sonnet, mantenendo lo stesso prezzo. Sonnet è particolarmente abile in programmazione, scienza dei dati, elaborazione visiva e compiti di agenti."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet offre capacità superiori a Opus e velocità maggiore rispetto a Sonnet, mantenendo lo stesso prezzo di Sonnet. Sonnet è particolarmente abile nella programmazione, nella scienza dei dati, nell'elaborazione visiva e nei compiti di agenzia."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet è il modello più recente di Anthropic, con prestazioni all'avanguardia in una vasta gamma di valutazioni, superando i modelli concorrenti e Claude 3.5 Sonnet, dimostrando eccellenza in una vasta gamma di valutazioni, mantenendo al contempo la velocità e i costi dei nostri modelli di livello medio."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku è il modello più veloce e compatto di Anthropic, progettato per risposte quasi istantanee. Ha prestazioni di orientamento rapide e accurate."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus è il modello più potente di Anthropic per gestire compiti altamente complessi. Eccelle in prestazioni, intelligenza, fluidità e comprensione."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet offre un equilibrio ideale tra intelligenza e velocità per i carichi di lavoro aziendali. Fornisce la massima utilità a un prezzo inferiore, affidabile e adatto per distribuzioni su larga scala."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 è il modello più potente di Anthropic per gestire compiti altamente complessi. Eccelle in prestazioni, intelligenza, fluidità e comprensione."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet può generare risposte quasi istantanee o pensieri graduali prolungati, consentendo agli utenti di vedere chiaramente questi processi. Gli utenti dell'API possono anche controllare in modo dettagliato il tempo di riflessione del modello."}, "codegeex-4": {"description": "CodeGeeX-4 è un potente assistente di programmazione AI, supporta domande intelligenti e completamento del codice in vari linguaggi di programmazione, migliorando l'efficienza dello sviluppo."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B è un modello di generazione di codice multilingue, supporta funzionalità complete tra cui completamento e generazione di codice, interprete di codice, ricerca in rete, chiamate di funzione e domande e risposte sul codice a livello di repository, coprendo vari scenari di sviluppo software. È un modello di generazione di codice di punta con meno di 10B di parametri."}, "codegemma": {"description": "CodeGemma è un modello linguistico leggero dedicato a vari compiti di programmazione, supporta iterazioni rapide e integrazione."}, "codegemma:2b": {"description": "CodeGemma è un modello linguistico leggero dedicato a vari compiti di programmazione, supporta iterazioni rapide e integrazione."}, "codellama": {"description": "Code Llama è un LLM focalizzato sulla generazione e discussione di codice, combinando un ampio supporto per i linguaggi di programmazione, adatto per ambienti di sviluppo."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama è un LLM focalizzato sulla generazione e discussione di codice, con un ampio supporto per diversi linguaggi di programmazione, adatto per ambienti di sviluppo."}, "codellama:13b": {"description": "Code Llama è un LLM focalizzato sulla generazione e discussione di codice, combinando un ampio supporto per i linguaggi di programmazione, adatto per ambienti di sviluppo."}, "codellama:34b": {"description": "Code Llama è un LLM focalizzato sulla generazione e discussione di codice, combinando un ampio supporto per i linguaggi di programmazione, adatto per ambienti di sviluppo."}, "codellama:70b": {"description": "Code Llama è un LLM focalizzato sulla generazione e discussione di codice, combinando un ampio supporto per i linguaggi di programmazione, adatto per ambienti di sviluppo."}, "codeqwen": {"description": "CodeQwen1.5 è un modello di linguaggio di grandi dimensioni addestrato su un ampio set di dati di codice, progettato per risolvere compiti di programmazione complessi."}, "codestral": {"description": "Codestral è il primo modello di codice di Mistral AI, offre un supporto eccezionale per i compiti di generazione di codice."}, "codestral-latest": {"description": "Codestral è un modello generativo all'avanguardia focalizzato sulla generazione di codice, ottimizzato per compiti di completamento e riempimento intermedio."}, "codex-mini-latest": {"description": "codex-mini-latest è una versione ottimizzata di o4-mini, progettata specificamente per Codex CLI. Per l'uso diretto tramite API, consigliamo di iniziare con gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B è un modello progettato per seguire istruzioni, dialogo e programmazione."}, "cogview-4": {"description": "CogView-4 è il primo modello open source di Zhipu che supporta la generazione di caratteri cinesi, con miglioramenti completi nella comprensione semantica, nella qualità della generazione delle immagini e nella capacità di generare testi in cinese e inglese. Supporta input bilingue cinese-inglese di qualsiasi lunghezza e può generare immagini a risoluzione arbitraria entro un intervallo specificato."}, "cohere-command-r": {"description": "Command R è un modello generativo scalabile mirato a RAG e all'uso di strumenti per abilitare l'IA su scala aziendale."}, "cohere-command-r-plus": {"description": "Command R+ è un modello ottimizzato per RAG all'avanguardia progettato per affrontare carichi di lavoro di livello aziendale."}, "cohere/Cohere-command-r": {"description": "Command R è un modello generativo scalabile progettato per l'uso con RAG e strumenti, che consente alle aziende di implementare AI a livello produttivo."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ è un modello ottimizzato all'avanguardia per RAG, progettato per gestire carichi di lavoro aziendali."}, "command": {"description": "Un modello di dialogo che segue le istruzioni, con alta qualità e maggiore affidabilità nelle attività linguistiche, e una lunghezza di contesto più lunga rispetto ai nostri modelli generativi di base."}, "command-a-03-2025": {"description": "Command A è il nostro modello più potente fino ad oggi, eccellente nell'uso degli strumenti, nell'agenz<PERSON>, nella generazione aumentata da recupero (RAG) e in scenari applicativi multilingue. Command A ha una lunghezza di contesto di 256K, può essere eseguito con solo due GPU e ha un throughput aumentato del 150% rispetto a Command R+ 08-2024."}, "command-light": {"description": "Una versione Command più piccola e veloce, quasi altrettanto potente, ma più rapida."}, "command-light-nightly": {"description": "Per ridurre l'intervallo di tempo tra i rilasci delle versioni principali, abbiamo lanciato una versione notturna del modello Command. Per la serie command-light, questa versione è chiamata command-light-nightly. Si prega di notare che command-light-nightly è l'ultima, la più sperimentale e (potenzialmente) instabile versione. Le versioni notturne vengono aggiornate regolarmente senza preavviso, quindi non si consiglia di utilizzarle in ambienti di produzione."}, "command-nightly": {"description": "Per ridurre l'intervallo di tempo tra i rilasci delle versioni principali, abbiamo lanciato una versione notturna del modello Command. Per la serie Command, questa versione è chiamata command-cightly. Si prega di notare che command-nightly è l'ultima, la più sperimentale e (potenzialmente) instabile versione. Le versioni notturne vengono aggiornate regolarmente senza preavviso, quindi non si consiglia di utilizzarle in ambienti di produzione."}, "command-r": {"description": "Command R è un LLM ottimizzato per compiti di dialogo e contesti lunghi, particolarmente adatto per interazioni dinamiche e gestione della conoscenza."}, "command-r-03-2024": {"description": "Command R è un modello di dialogo che segue le istruzioni, con una qualità superiore e una maggiore affidabilità nelle attività linguistiche, e una lunghezza di contesto più lunga rispetto ai modelli precedenti. Può essere utilizzato per flussi di lavoro complessi, come generazione di codice, generazione aumentata da recupero (RAG), uso di strumenti e agenzia."}, "command-r-08-2024": {"description": "command-r-08-2024 è una versione aggiornata del modello Command R, rilasciata nell'agosto 2024."}, "command-r-plus": {"description": "Command R+ è un modello di linguaggio di grandi dimensioni ad alte prestazioni, progettato per scenari aziendali reali e applicazioni complesse."}, "command-r-plus-04-2024": {"description": "Command R+ è un modello di dialogo che segue le istruzioni, con una qualità superiore e una maggiore affidabilità nelle attività linguistiche, e una lunghezza di contesto più lunga rispetto ai modelli precedenti. È particolarmente adatto per flussi di lavoro complessi RAG e per l'uso di strumenti in più passaggi."}, "command-r-plus-08-2024": {"description": "Command R+ è un modello di dialogo che segue le istruzioni, mostrando una qualità superiore nei compiti linguistici, maggiore affidabilità e una lunghezza di contesto più lunga rispetto ai modelli precedenti. È particolarmente adatto per flussi di lavoro RAG complessi e utilizzo di strumenti in più passaggi."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 è una versione aggiornata, piccola ed efficiente, rilasciata nel dicembre 2024. Eccelle in compiti che richiedono ragionamento complesso e elaborazione in più passaggi, come RAG, uso di strumenti e agenzia."}, "compound-beta": {"description": "Compound-beta è un sistema AI composito, supportato da diversi modelli disponibili pubblicamente su GroqCloud, in grado di utilizzare strumenti in modo intelligente e selettivo per rispondere alle domande degli utenti."}, "compound-beta-mini": {"description": "Compound-beta-mini è un sistema AI composito, supportato da modelli pubblicamente disponibili su GroqCloud, in grado di utilizzare strumenti in modo intelligente e selettivo per rispondere alle domande degli utenti."}, "computer-use-preview": {"description": "Il modello computer-use-preview è un modello dedicato progettato per \"strumenti di utilizzo del computer\", addestrato per comprendere ed eseguire compiti correlati al computer."}, "dall-e-2": {"description": "Seconda generazione del modello DALL·E, supporta la generazione di immagini più realistiche e accurate, con una risoluzione quattro volte superiore rispetto alla prima generazione."}, "dall-e-3": {"description": "L'ultimo modello DALL·E, rilasciato a novembre 2023. Supporta la generazione di immagini più realistiche e accurate, con una maggiore capacità di dettaglio."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct offre capacità di elaborazione di istruzioni altamente affidabili, supportando applicazioni in vari settori."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 è un modello di inferenza guidato da apprendimento rinforzato (RL) che affronta i problemi di ripetitività e leggibilità nel modello. Prima dell'RL, DeepSeek-R1 ha introdotto dati di cold start, ottimizzando ulteriormente le prestazioni di inferenza. Si comporta in modo comparabile a OpenAI-o1 in compiti matematici, di codifica e di inferenza, e migliora l'efficacia complessiva attraverso metodi di addestramento accuratamente progettati."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 migliora significativamente la profondità delle capacità di ragionamento e inferenza grazie all’uso di risorse computazionali aumentate e all’introduzione di meccanismi di ottimizzazione algoritmica nel post-addestramento. Il modello eccelle in vari benchmark, inclusi matematica, programmazione e logica generale, avvicinandosi alle prestazioni di modelli leader come O3 e Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B è un modello ottenuto distillando la catena di pensieri da DeepSeek-R1-0528 al modello base Qwen3 8B. Rappresenta lo stato dell’arte tra i modelli open source, superando Qwen3 8B del 10% nel test AIME 2024 e raggiungendo le prestazioni di Qwen3-235B-thinking. Eccelle in ragionamento matematico, programmazione e logica generale, con architettura identica a Qwen3-8B ma con configurazione del tokenizer condivisa con DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "Il modello di distillazione DeepSeek-R1 ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Il modello di distillazione DeepSeek-R1 ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "Il modello di distillazione DeepSeek-R1 ottimizza le prestazioni di inferenza attraverso l'apprendimento rinforzato e dati di avvio a freddo, aggiornando il benchmark multi-task del modello open source."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B è un modello ottenuto tramite distillazione della conoscenza basato su Qwen2.5-32B. Questo modello è stato messo a punto utilizzando 800.000 campioni selezionati generati da DeepSeek-R1, mostrando prestazioni eccezionali in vari campi come matematica, programmazione e ragionamento. Ha ottenuto risultati eccellenti in vari test di benchmark, raggiungendo un'accuratezza del 94,3% in MATH-500, dimostrando una forte capacità di ragionamento matematico."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B è un modello ottenuto tramite distillazione della conoscenza basato su Qwen2.5-Math-7B. Questo modello è stato messo a punto utilizzando 800.000 campioni selezionati generati da DeepSeek-R1, mostrando eccellenti capacità di inferenza. Ha ottenuto risultati eccezionali in vari test di benchmark, raggiungendo un'accuratezza del 92,8% in MATH-500, una percentuale di passaggio del 55,5% in AIME 2024 e un punteggio di 1189 su CodeForces, dimostrando forti capacità matematiche e di programmazione come modello di dimensioni 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 combina le eccellenti caratteristiche delle versioni precedenti, migliorando le capacità generali e di codifica."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 è un modello linguistico a esperti misti (MoE) con 6710 miliardi di parametri, che utilizza attenzione latente multi-testa (MLA) e architettura DeepSeekMoE, combinando strategie di bilanciamento del carico senza perdite ausiliarie per ottimizzare l'efficienza di inferenza e addestramento. Pre-addestrato su 14,8 trilioni di token di alta qualità e successivamente affinato supervisionato e tramite apprendimento rinforzato, DeepSeek-V3 supera le prestazioni di altri modelli open source, avvicinandosi ai modelli closed source leader."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B è un modello avanzato addestrato per dialoghi ad alta complessità."}, "deepseek-ai/deepseek-r1": {"description": "LLM altamente efficiente, specializzato in ragionamento, matematica e programmazione."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 è un modello linguistico visivo a esperti misti (MoE) sviluppato sulla base di DeepSeekMoE-27B, che utilizza un'architettura MoE con attivazione sparsa, raggiungendo prestazioni eccezionali attivando solo 4,5 miliardi di parametri. Questo modello eccelle in vari compiti, tra cui domande visive, riconoscimento ottico dei caratteri, comprensione di documenti/tabelle/grafici e localizzazione visiva."}, "deepseek-chat": {"description": "Un nuovo modello open source che integra capacità generali e di codifica, mantenendo non solo le capacità conversazionali generali del modello Chat originale, ma anche la potente capacità di elaborazione del codice del modello Coder, allineandosi meglio alle preferenze umane. Inoltre, DeepSeek-V2.5 ha ottenuto notevoli miglioramenti in vari aspetti, come i compiti di scrittura e il rispetto delle istruzioni."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B è un modello di linguaggio per codice, addestrato su 20 trilioni di dati, di cui l'87% è codice e il 13% è in cinese e inglese. Il modello introduce una dimensione della finestra di 16K e compiti di completamento, fornendo funzionalità di completamento del codice e riempimento di frammenti a livello di progetto."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 è un modello di codice open source di esperti misti, eccelle nei compiti di codice, paragonabile a GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 è un modello di codice open source di esperti misti, eccelle nei compiti di codice, paragonabile a GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 è un modello di inferenza guidato da apprendimento rinforzato (RL) che affronta i problemi di ripetitività e leggibilità nel modello. Prima dell'RL, DeepSeek-R1 ha introdotto dati di cold start, ottimizzando ulteriormente le prestazioni di inferenza. Si comporta in modo comparabile a OpenAI-o1 in compiti matematici, di codifica e di inferenza, e migliora l'efficacia complessiva attraverso metodi di addestramento accuratamente progettati."}, "deepseek-r1-0528": {"description": "<PERSON><PERSON> completo da 685 miliardi di parametri, r<PERSON><PERSON><PERSON><PERSON> il 28 maggio 2025. DeepSeek-R1 utilizza ampiamente tecniche di apprendimento rinforzato nella fase post-addestramento, migliorando notevolmente le capacità di ragionamento del modello con pochissimi dati annotati. Eccelle in matematica, programmazione, ragionamento in linguaggio naturale e altre attività."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B versione veloce, supporta la ricerca online in tempo reale, fornendo una velocità di risposta più rapida mantenendo le prestazioni del modello."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B versione standard, supporta la ricerca online in tempo reale, adatta per conversazioni e compiti di elaborazione del testo che richiedono informazioni aggiornate."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama è un modello derivato da Llama attraverso la distillazione di DeepSeek-R1."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - il modello più grande e intelligente del pacchetto DeepSeek - è stato distillato nell'architettura Llama 70B. Basato su test di benchmark e valutazioni umane, questo modello è più intelligente del Llama 70B originale, mostrando prestazioni eccezionali in compiti che richiedono precisione matematica e fattuale."}, "deepseek-r1-distill-llama-8b": {"description": "Il modello della serie DeepSeek-R1-Distill è stato ottenuto tramite la tecnologia di distillazione della conoscenza, ottimizzando i campioni generati da DeepSeek-R1 su modelli open source come Qwen e Llama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Rilasciato per la prima volta il 14 febbraio 2025, distillato dal team di ricerca del grande modello Qianfan utilizzando Llama3_70B come modello base (costruito con Meta Llama), con l'aggiunta di dati di Qianfan nel set di dati di distillazione."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Rilasciato per la prima volta il 14 febbraio 2025, distillato dal team di ricerca del grande modello Qianfan utilizzando Llama3_8B come modello base (costruito con Meta Llama), con l'aggiunta di dati di Qianfan nel set di dati di distillazione."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen è un modello distillato da Qwen basato su DeepSeek-R1."}, "deepseek-r1-distill-qwen-1.5b": {"description": "Il modello della serie DeepSeek-R1-Distill è stato ottenuto tramite la tecnologia di distillazione della conoscenza, ottimizzando i campioni generati da DeepSeek-R1 su modelli open source come Qwen e Llama."}, "deepseek-r1-distill-qwen-14b": {"description": "Il modello della serie DeepSeek-R1-Distill è stato ottenuto tramite la tecnologia di distillazione della conoscenza, ottimizzando i campioni generati da DeepSeek-R1 su modelli open source come Qwen e Llama."}, "deepseek-r1-distill-qwen-32b": {"description": "Il modello della serie DeepSeek-R1-Distill è stato ottenuto tramite la tecnologia di distillazione della conoscenza, ottimizzando i campioni generati da DeepSeek-R1 su modelli open source come Qwen e Llama."}, "deepseek-r1-distill-qwen-7b": {"description": "Il modello della serie DeepSeek-R1-Distill è stato ottenuto tramite la tecnologia di distillazione della conoscenza, ottimizzando i campioni generati da DeepSeek-R1 su modelli open source come Qwen e Llama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 versione veloce completa, supporta la ricerca online in tempo reale, combinando la potenza dei 671B parametri con una velocità di risposta più rapida."}, "deepseek-r1-online": {"description": "DeepSeek R1 versione completa, con 671B parametri, supporta la ricerca online in tempo reale, con capacità di comprensione e generazione più potenti."}, "deepseek-reasoner": {"description": "Modello di ragionamento lanciato da DeepSeek. Prima di fornire la risposta finale, il modello genera una catena di pensiero per migliorare l'accuratezza della risposta finale."}, "deepseek-v2": {"description": "DeepSeek V2 è un modello di linguaggio Mixture-of-Experts efficiente, adatto per esigenze di elaborazione economica."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B è il modello di codice progettato di DeepSeek, offre potenti capacità di generazione di codice."}, "deepseek-v3": {"description": "DeepSeek-V3 è un modello MoE sviluppato internamente da Hangzhou DeepSeek Artificial Intelligence Technology Research Co., Ltd., con risultati eccezionali in molteplici valutazioni, posizionandosi al primo posto tra i modelli open source nelle classifiche principali. Rispetto al modello V2.5, la velocità di generazione è aumentata di 3 volte, offrendo un'esperienza utente più rapida e fluida."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 è un modello MoE con 671 miliardi di parametri, con vantaggi notevoli nelle capacità di programmazione e tecniche, comprensione del contesto e gestione di testi lunghi."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 è un modello misto esperto con 685B di parametri, l'ultima iterazione della serie di modelli di chat di punta del team DeepSeek.\n\nEredita il modello [DeepSeek V3](/deepseek/deepseek-chat-v3) e si comporta eccezionalmente in vari compiti."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 è un modello misto esperto con 685B di parametri, l'ultima iterazione della serie di modelli di chat di punta del team DeepSeek.\n\nEredita il modello [DeepSeek V3](/deepseek/deepseek-chat-v3) e si comporta eccezionalmente in vari compiti."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 ha notevolmente migliorato le capacità di ragionamento del modello con pochissimi dati etichettati. Prima di fornire la risposta finale, il modello genera una catena di pensiero per migliorare l'accuratezza della risposta finale."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 migliora notevolmente la capacità di ragionamento del modello anche con pochissimi dati annotati. Prima di fornire la risposta finale, il modello genera una catena di pensieri per aumentare la precisione della risposta."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 migliora notevolmente la capacità di ragionamento del modello anche con pochissimi dati annotati. Prima di fornire la risposta finale, il modello genera una catena di pensieri per aumentare la precisione della risposta."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B è un grande modello di linguaggio basato su Llama3.3 70B, che utilizza il fine-tuning dell'output di DeepSeek R1 per raggiungere prestazioni competitive paragonabili a quelle dei modelli all'avanguardia di grandi dimensioni."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B è un modello di linguaggio distillato basato su Llama-3.1-8B-Instruct, addestrato utilizzando l'output di DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B è un modello di linguaggio distillato basato su Qwen 2.5 14B, addestrato utilizzando l'output di DeepSeek R1. <PERSON>o modello ha superato OpenAI's o1-mini in diversi benchmark, raggiungendo risultati all'avanguardia per i modelli densi. Ecco alcuni risultati dei benchmark:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nQuesto modello, attraverso il fine-tuning dell'output di DeepSeek R1, ha dimostrato prestazioni competitive paragonabili a modelli all'avanguardia di dimensioni maggiori."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B è un modello di linguaggio distillato basato su Qwen 2.5 32B, addestrato utilizzando l'output di DeepSeek R1. <PERSON>o modello ha superato OpenAI's o1-mini in diversi benchmark, raggiungendo risultati all'avanguardia per i modelli densi. Ecco alcuni risultati dei benchmark:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nQuesto modello, attraverso il fine-tuning dell'output di DeepSeek R1, ha dimostrato prestazioni competitive paragonabili a modelli all'avanguardia di dimensioni maggiori."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 è l'ultimo modello open source rilasciato dal team di DeepSeek, con prestazioni di inferenza eccezionali, in particolare nei compiti di matematica, programmazione e ragionamento, raggi<PERSON>do livelli comparabili a quelli del modello o1 di OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 ha notevolmente migliorato le capacità di ragionamento del modello con pochissimi dati etichettati. Prima di fornire la risposta finale, il modello genera una catena di pensiero per migliorare l'accuratezza della risposta finale."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 ha realizzato un significativo progresso nella velocità di inferenza rispetto ai modelli precedenti. Si posiziona al primo posto tra i modelli open source e può competere con i modelli closed source più avanzati al mondo. DeepSeek-V3 utilizza l'architettura Multi-Head Latent Attention (MLA) e DeepSeekMoE, che sono state ampiamente validate in DeepSeek-V2. Inoltre, DeepSeek-V3 ha introdotto una strategia ausiliaria senza perdita per il bilanciamento del carico e ha stabilito obiettivi di addestramento per la previsione multi-etichetta per ottenere prestazioni superiori."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 ha realizzato un significativo progresso nella velocità di inferenza rispetto ai modelli precedenti. Si posiziona al primo posto tra i modelli open source e può competere con i modelli closed source più avanzati al mondo. DeepSeek-V3 utilizza l'architettura Multi-Head Latent Attention (MLA) e DeepSeekMoE, che sono state ampiamente validate in DeepSeek-V2. Inoltre, DeepSeek-V3 ha introdotto una strategia ausiliaria senza perdita per il bilanciamento del carico e ha stabilito obiettivi di addestramento per la previsione multi-etichetta per ottenere prestazioni superiori."}, "deepseek_r1": {"description": "DeepSeek-R1 è un modello di inferenza guidato da apprendimento rinforzato (RL), che risolve problemi di ripetitività e leggibilità nel modello. Prima dell'RL, DeepSeek-R1 ha introdotto dati di avvio a freddo, ottimizzando ulteriormente le prestazioni di inferenza. Mostra prestazioni comparabili a OpenAI-o1 in compiti di matematica, codice e ragionamento, e ha migliorato l'efficacia complessiva attraverso metodi di addestramento ben progettati."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B è un modello ottenuto tramite addestramento di distillazione basato su Llama-3.3-70B-Instruct. Questo modello fa parte della serie DeepSeek-R1 e mostra prestazioni eccellenti in matematica, programmazione e ragionamento, affinato utilizzando campioni generati da DeepSeek-R1."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B è un modello ottenuto tramite distillazione della conoscenza basato su Qwen2.5-14B. Questo modello è stato affinato utilizzando 800.000 campioni selezionati generati da DeepSeek-R1, mostrando eccellenti capacità di inferenza."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B è un modello ottenuto tramite distillazione della conoscenza basato su Qwen2.5-32B. Questo modello è stato affinato utilizzando 800.000 campioni selezionati generati da DeepSeek-R1, mostrando prestazioni eccezionali in vari campi, tra cui matematica, programmazione e ragionamento."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite è un modello leggero di nuova generazione, con una velocità di risposta eccezionale, raggiungendo standard di livello mondiale sia in termini di prestazioni che di latenza."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k è una versione completamente aggiornata di Doubao-1.5-Pro, con un miglioramento complessivo delle prestazioni del 10%. Supporta il ragionamento con una finestra di contesto di 256k e una lunghezza di output massima di 12k token. Maggiore prestazioni, finestra più ampia e un eccellente rapporto qualità-prezzo, adatto a una gamma più ampia di scenari applicativi."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro è un modello di nuova generazione, con prestazioni completamente aggiornate, eccellente in conoscenza, codice, ragionamento e altro."}, "doubao-1.5-thinking-pro": {"description": "Il modello di pensiero profondo Doubao-1.5, completamente nuovo, si distingue in ambiti professionali come matematica, programmazione e ragionamento scientifico, oltre che in compiti generali come la scrittura creativa, raggiungendo o avvicinandosi ai livelli di eccellenza del settore in numerosi benchmark autorevoli come AIME 2024, Codeforces e GPQA. Supporta una finestra di contesto di 128k e un output di 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 è un nuovo modello di pensiero profondo (versione m con capacità native di inferenza multimodale profonda), eccellente in matematica, programmazione, ragionamento scientifico e compiti generali come scrittura creativa. Raggiunge o si avvicina al livello top nel settore in benchmark autorevoli come AIME 2024, Codeforces, GPQA. Supporta una finestra contestuale di 128k e output di 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Nuovo modello di pensiero profondo visivo con capacità multimodali generali potenziate, che ha raggiunto prestazioni SOTA in 37 su 59 benchmark pubblici."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS è un modello Agent nativo progettato per l'interazione con interfacce grafiche (GUI). Interagisce senza soluzione di continuità con la GUI attraverso capacità umanoidi di percezione, ragionamento e azione."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite è un grande modello multimodale aggiornato, che supporta il riconoscimento di immagini a qualsiasi risoluzione e proporzioni estremamente lunghe, migliorando le capacità di ragionamento visivo, riconoscimento di documenti, comprensione delle informazioni dettagliate e rispetto delle istruzioni. Supporta una finestra di contesto di 128k e una lunghezza di uscita massima di 16k token."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro è un modello multimodale avanzato che supporta il riconoscimento di immagini a qualsiasi risoluzione e rapporti d'aspetto estremi, migliorando il ragionamento visivo, il riconoscimento documentale, la comprensione dei dettagli e l'aderenza alle istruzioni."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro è un modello multimodale avanzato che supporta il riconoscimento di immagini a qualsiasi risoluzione e rapporti d'aspetto estremi, migliorando il ragionamento visivo, il riconoscimento documentale, la comprensione dei dettagli e l'aderenza alle istruzioni."}, "doubao-lite-128k": {"description": "Offre una velocità di risposta eccezionale e un miglior rapporto qualità-prezzo, fornendo ai clienti scelte più flessibili per diversi scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 128k."}, "doubao-lite-32k": {"description": "Offre una velocità di risposta eccezionale e un miglior rapporto qualità-prezzo, fornendo ai clienti scelte più flessibili per diversi scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 32k."}, "doubao-lite-4k": {"description": "Offre una velocità di risposta eccezionale e un miglior rapporto qualità-prezzo, fornendo ai clienti scelte più flessibili per diversi scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 4k."}, "doubao-pro-256k": {"description": "Il modello principale con le migliori prestazioni, adatto per gestire compiti complessi, con ottimi risultati in domande di riferimento, sintesi, creazione, classificazione del testo, role-playing e altri scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 256k."}, "doubao-pro-32k": {"description": "Il modello principale con le migliori prestazioni, adatto per gestire compiti complessi, con ottimi risultati in domande di riferimento, sintesi, creazione, classificazione del testo, role-playing e altri scenari. Supporta inferenza e fine-tuning con una finestra contestuale di 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 è un nuovo modello multimodale di pensiero profondo che supporta tre modalità di pensiero: auto, thinking e non-thinking. In modalità non-thinking, le prestazioni del modello migliorano significativamente rispetto a Doubao-1.5-pro/250115. Supporta una finestra contestuale di 256k e una lunghezza massima di output di 16k token."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash è un modello multimodale di pensiero profondo con velocità di inferenza estrema, con TPOT di soli 10 ms; supporta sia la comprensione testuale che visiva, con capacità di comprensione testuale superiore alla generazione lite precedente e comprensione visiva paragonabile ai modelli pro della concorrenza. Supporta una finestra contestuale di 256k e una lunghezza massima di output di 16k token."}, "doubao-seed-1.6-thinking": {"description": "Il modello Doubao-Seed-1.6-thinking ha capacità di pensiero notevolmente potenziate; rispetto a Doubao-1.5-thinking-pro, migliora ulteriormente le capacità di base come coding, matematica e ragionamento logico, supportando anche la comprensione visiva. Supporta una finestra contestuale di 256k e una lunghezza massima di output di 16k token."}, "doubao-seedream-3-0-t2i-250415": {"description": "Il modello di generazione immagini Doubao è sviluppato dal team Seed di ByteDance, supporta input di testo e immagini, offrendo un'esperienza di generazione immagini altamente controllabile e di alta qualità. Genera immagini basate su prompt testuali."}, "doubao-vision-lite-32k": {"description": "Il modello Doubao-vision è un modello multimodale lanciato da Doubao, con potenti capacità di comprensione e ragionamento delle immagini e una precisa comprensione delle istruzioni. Il modello mostra prestazioni eccellenti nell'estrazione di informazioni da testo e immagini e in compiti di ragionamento basati su immagini, applicabile a compiti di domande visive più complessi e ampi."}, "doubao-vision-pro-32k": {"description": "Il modello Doubao-vision è un modello multimodale lanciato da Doubao, con potenti capacità di comprensione e ragionamento delle immagini e una precisa comprensione delle istruzioni. Il modello mostra prestazioni eccellenti nell'estrazione di informazioni da testo e immagini e in compiti di ragionamento basati su immagini, applicabile a compiti di domande visive più complessi e ampi."}, "emohaa": {"description": "Emohaa è un modello psicologico, con capacità di consulenza professionale, aiuta gli utenti a comprendere i problemi emotivi."}, "ernie-3.5-128k": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, coprendo un'enorme quantità di dati in cinese e inglese, con forti capacità generali, in grado di soddisfare la maggior parte delle esigenze di domande e risposte, generazione creativa e scenari di applicazione di plugin; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ernie-3.5-8k": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, coprendo un'enorme quantità di dati in cinese e inglese, con forti capacità generali, in grado di soddisfare la maggior parte delle esigenze di domande e risposte, generazione creativa e scenari di applicazione di plugin; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ernie-3.5-8k-preview": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, coprendo un'enorme quantità di dati in cinese e inglese, con forti capacità generali, in grado di soddisfare la maggior parte delle esigenze di domande e risposte, generazione creativa e scenari di applicazione di plugin; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ernie-4.0-8k-latest": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, con un aggiornamento completo delle capacità rispetto a ERNIE 3.5, ampiamente applicabile a scenari di compiti complessi in vari campi; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ernie-4.0-8k-preview": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, con un aggiornamento completo delle capacità rispetto a ERNIE 3.5, ampiamente applicabile a scenari di compiti complessi in vari campi; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte."}, "ernie-4.0-turbo-128k": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, con prestazioni complessive eccezionali, ampiamente applicabile a scenari di compiti complessi in vari campi; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte. Rispetto a ERNIE 4.0, offre prestazioni migliori."}, "ernie-4.0-turbo-8k-latest": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, con prestazioni complessive eccezionali, ampiamente applicabile a scenari di compiti complessi in vari campi; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte. Rispetto a ERNIE 4.0, offre prestazioni migliori."}, "ernie-4.0-turbo-8k-preview": {"description": "Il modello di linguaggio di grandi dimensioni di punta sviluppato internamente da Baidu, con prestazioni complessive eccezionali, ampiamente applicabile a scenari di compiti complessi in vari campi; supporta l'integrazione automatica con il plugin di ricerca di Baidu, garantendo l'aggiornamento delle informazioni nelle risposte. Rispetto a ERNIE 4.0, offre prestazioni migliori."}, "ernie-4.5-8k-preview": {"description": "Il modello di grandi dimensioni Wenxin 4.5 è una nuova generazione di modello di base multimodale sviluppato autonomamente da Baidu, realizzato attraverso la modellazione congiunta di più modalità per ottenere un'ottimizzazione collaborativa, con eccellenti capacità di comprensione multimodale; presenta capacità linguistiche più avanzate, con miglioramenti significativi nella comprensione, generazione, logica e memoria, riducendo le illusioni e migliorando il ragionamento logico e le capacità di codifica."}, "ernie-4.5-turbo-128k": {"description": "ERNIE 4.5 Turbo ha mostrato miglioramenti significativi nella riduzione delle allucinazioni, nel ragionamento logico e nelle capacità di codifica. Rispetto a ERNIE 4.5, è più veloce e più economico. Le capacità del modello sono state ampliate per soddisfare meglio le esigenze di gestione di conversazioni lunghe e storiche e di domande e risposte su documenti lunghi."}, "ernie-4.5-turbo-32k": {"description": "ERNIE 4.5 Turbo ha mostrato miglioramenti significativi nella riduzione delle allucinazioni, nel ragionamento logico e nelle capacità di codifica. Rispetto a ERNIE 4.5, è più veloce e più economico. Le capacità di creazione di testi e domande e risposte sono notevolmente migliorate. La lunghezza dell'output e il ritardo delle frasi complete sono aumentati rispetto a ERNIE 4.5."}, "ernie-4.5-turbo-vl-32k": {"description": "Nuova versione del modello ERNIE, con capacità significativamente migliorate nella comprensione, creazione, traduzione e codifica delle immagini, supportando per la prima volta una lunghezza di contesto di 32K, con un ritardo del primo token notevolmente ridotto."}, "ernie-char-8k": {"description": "Un modello di linguaggio di grandi dimensioni sviluppato internamente da Baidu, adatto per scenari di applicazione come NPC nei giochi, dialoghi di assistenza clienti e interpretazione di ruoli nei dialoghi, con uno stile di personaggio più distintivo e coerente, capacità di seguire istruzioni più forti e prestazioni di inferenza migliori."}, "ernie-char-fiction-8k": {"description": "Un modello di linguaggio di grandi dimensioni sviluppato internamente da Baidu, adatto per scenari di applicazione come NPC nei giochi, dialoghi di assistenza clienti e interpretazione di ruoli nei dialoghi, con uno stile di personaggio più distintivo e coerente, capacità di seguire istruzioni più forti e prestazioni di inferenza migliori."}, "ernie-irag-edit": {"description": "Il modello di editing immagini ERNIE iRAG sviluppato da Baidu supporta operazioni come cancellazione (erase), ridipintura (repaint) e variazione (variation) basate su immagini."}, "ernie-lite-8k": {"description": "ERNIE Lite è un modello di linguaggio di grandi dimensioni sviluppato internamente da Baidu, che bilancia prestazioni eccellenti del modello e prestazioni di inferenza, adatto per l'uso con schede di accelerazione AI a bassa potenza."}, "ernie-lite-pro-128k": {"description": "Un modello di linguaggio di grandi dimensioni leggero sviluppato internamente da Baidu, che bilancia prestazioni eccellenti del modello e prestazioni di inferenza, con risultati migliori rispetto a ERNIE Lite, adatto per l'uso con schede di accelerazione AI a bassa potenza."}, "ernie-novel-8k": {"description": "Un modello di linguaggio di grandi dimensioni sviluppato internamente da Baidu, con un evidente vantaggio nella capacità di continuare romanzi, utilizzabile anche in scenari come cortometraggi e film."}, "ernie-speed-128k": {"description": "Il modello di linguaggio di grandi dimensioni ad alte prestazioni sviluppato internamente da Baidu, rilasciato nel 2024, con capacità generali eccellenti, adatto come modello di base per la messa a punto, per affrontare meglio i problemi specifici, mantenendo eccellenti prestazioni di inferenza."}, "ernie-speed-pro-128k": {"description": "Il modello di linguaggio di grandi dimensioni ad alte prestazioni sviluppato internamente da Baidu, rilasciato nel 2024, con capacità generali eccellenti, con risultati migliori rispetto a ERNIE Speed, adatto come modello di base per la messa a punto, per affrontare meglio i problemi specifici, mantenendo eccellenti prestazioni di inferenza."}, "ernie-tiny-8k": {"description": "ERNIE Tiny è un modello di linguaggio di grandi dimensioni ad alte prestazioni sviluppato internamente da Baidu, con i costi di distribuzione e messa a punto più bassi della serie Wencin."}, "ernie-x1-32k": {"description": "Possiede una comprensione, pianificazione, riflessione e capacità evolutive superiori. Come modello di pensiero profondo più completo, ERNIE-X1 combina accuratezza, creatività e stile, eccellendo in domande e risposte in cinese, creazione letteraria, scrittura di documenti, conversazioni quotidiane, ragionamento logico, calcoli complessi e utilizzo di strumenti."}, "ernie-x1-32k-preview": {"description": "Il grande modello Wénxīn X1 possiede una comprensione, pianificazione, riflessione e capacità evolutive superiori. Come modello di pensiero profondo più completo, Wénxīn X1 combina precisione, creatività e stile, eccellendo in domande e risposte in cinese, creazione letteraria, scrittura di documenti, conversazioni quotidiane, ragionamento logico, calcoli complessi e invocazione di strumenti."}, "ernie-x1-turbo-32k": {"description": "Rispetto a ERNIE-X1-32K, il modello offre prestazioni e risultati migliori."}, "flux-1-schnell": {"description": "Modello di generazione immagini da testo con 12 miliardi di parametri sviluppato da Black Forest Labs, che utilizza la tecnologia di distillazione di diffusione antagonista latente, capace di generare immagini di alta qualità in 1-4 passaggi. Le prestazioni sono comparabili a soluzioni proprietarie, rilasciato sotto licenza Apache-2.0 per uso personale, di ricerca e commerciale."}, "flux-dev": {"description": "FLUX.1 [dev] è un modello open source raffinato e pesato per uso non commerciale. Mantiene qualità d'immagine e aderenza alle istruzioni simili alla versione professionale FLUX, ma con maggiore efficienza operativa. Rispetto a modelli standard di dimensioni simili, utilizza le risorse in modo più efficiente."}, "flux-kontext/dev": {"description": "Modello di editing immagini Frontier."}, "flux-merged": {"description": "Il modello FLUX.1-merged combina le caratteristiche approfondite esplorate nella fase di sviluppo \"DEV\" con i vantaggi di esecuzione rapida rappresentati da \"Schnell\". Questa combinazione non solo estende i limiti di prestazione del modello, ma ne amplia anche l'ambito di applicazione."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] è in grado di elaborare testo e immagini di riferimento come input, realizzando senza soluzione di continuità modifiche locali mirate e complesse trasformazioni dell'intera scena."}, "flux-schnell": {"description": "FLUX.1 [schnell], attualmente il modello open source più avanzato a pochi passaggi, supera non solo i concorrenti simili ma anche potenti modelli non raffinati come Midjourney v6.0 e DALL·E 3 (HD). Ottimizzato per mantenere tutta la diversità di output della fase di pre-addestramento, migliora significativamente qualità visiva, aderenza alle istruzioni, variazioni di dimensione/proporzione, gestione dei font e diversità di output rispetto ai modelli più avanzati sul mercato, offrendo un'esperienza creativa più ricca e variegata."}, "flux.1-schnell": {"description": "Trasformatore di flusso rettificato con 12 miliardi di parametri, capace di generare immagini basate su descrizioni testuali."}, "flux/schnell": {"description": "FLUX.1 [schnell] è un modello trasformatore a flusso con 12 miliardi di parametri, capace di generare immagini di alta qualità da testo in 1-4 passaggi, adatto per uso personale e commerciale."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (<PERSON><PERSON>) offre prestazioni stabili e ottimizzabili, è la scelta ideale per soluzioni a compiti complessi."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (<PERSON><PERSON>) offre un'eccellente supporto multimodale, focalizzandosi sulla risoluzione efficace di compiti complessi."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro è il modello AI ad alte prestazioni di Google, progettato per l'espansione su una vasta gamma di compiti."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 è un modello multimodale efficiente, supporta l'espansione per applicazioni ampie."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 è un modello multimodale altamente efficiente, che supporta un'ampia gamma di applicazioni."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B è un modello multimodale altamente efficiente, che supporta un'ampia gamma di applicazioni."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 è il modello sperimentale più recente, con miglioramenti significativi nelle prestazioni sia nei casi d'uso testuali che multimodali."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B è un modello multimodale efficiente che supporta un'ampia gamma di applicazioni estese."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 offre capacità di elaborazione multimodale ottimizzate, adatte a vari scenari di compiti complessi."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash è il più recente modello AI multimodale di Google, dotato di capacità di elaborazione rapida, supporta input di testo, immagini e video, ed è adatto per un'ampia gamma di compiti di scalabilità efficiente."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 è una soluzione AI multimodale scalabile, supporta un'ampia gamma di compiti complessi."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 è il modello più recente pronto per la produzione, che offre output di qualità superiore, con miglioramenti significativi in particolare in matematica, contesti lunghi e compiti visivi."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 offre eccellenti capacità di elaborazione multimodale, offrendo maggiore flessibilità nello sviluppo di applicazioni."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 integra le tecnologie di ottimizzazione più recenti, offrendo capacità di elaborazione dei dati multimodali più efficienti."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro supporta fino a 2 milioni di token, è la scelta ideale per modelli multimodali di medie dimensioni, adatta a un supporto multifunzionale per compiti complessi."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash offre funzionalità e miglioramenti di nuova generazione, tra cui velocità eccezionale, utilizzo di strumenti nativi, generazione multimodale e una finestra di contesto di 1M token."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash offre funzionalità e miglioramenti di nuova generazione, tra cui velocità eccezionale, utilizzo di strumenti nativi, generazione multimodale e una finestra di contesto di 1M token."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash è una variante del modello ottimizzata per obiettivi come il rapporto costo-efficacia e la bassa latenza."}, "gemini-2.0-flash-exp-image-generation": {"description": "Modello sperimentale Gemini 2.0 Flash, supporta la generazione di immagini"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash è una variante del modello Flash, ottimizzata per obiettivi come il rapporto costo-efficacia e la bassa latenza."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash è una variante del modello Flash, ottimizzata per obiettivi come il rapporto costo-efficacia e la bassa latenza."}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash modello di anteprima, supporta la generazione di immagini"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash è il modello Google con il miglior rapporto qualità-prezzo, offrendo funzionalità complete."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite è il modello più piccolo e conveniente di Google, progettato per un utilizzo su larga scala."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview è il modello Google più piccolo e con il miglior rapporto qualità-prezzo, progettato per un utilizzo su larga scala."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview è il modello più conveniente di Google, che offre funzionalità complete."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview è il modello Google con il miglior rapporto qualità-prezzo, che offre funzionalità complete."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro è il modello di pensiero più avanzato di Google, capace di ragionare su codice, matematica e problemi complessi nei campi STEM, oltre a utilizzare contesti lunghi per analizzare grandi dataset, codebase e documenti."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview è il modello di pensiero più avanzato di Google, in grado di ragionare su problemi complessi in codice, matematica e nei campi STEM, oltre a utilizzare analisi di lungo contesto per grandi set di dati, codici sorgente e documenti."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview è il modello di pensiero più avanzato di Google, in grado di ragionare su problemi complessi nel codice, nella matematica e nei campi STEM, utilizzando analisi di lungo contesto per esaminare grandi set di dati, librerie di codice e documenti."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview è il modello di pensiero più avanzato di Google, capace di ragionare su problemi complessi in codice, matematica e ambito STEM, oltre a utilizzare contesti estesi per analizzare grandi dataset, librerie di codice e documenti."}, "gemma-7b-it": {"description": "Gemma 7B è adatto per l'elaborazione di compiti di piccole e medie dimensioni, combinando efficienza dei costi."}, "gemma2": {"description": "Gemma 2 è un modello efficiente lanciato da Google, coprendo una vasta gamma di scenari applicativi, da applicazioni di piccole dimensioni a elaborazioni di dati complesse."}, "gemma2-9b-it": {"description": "Gemma 2 9B è un modello ottimizzato per l'integrazione di compiti specifici e strumenti."}, "gemma2:27b": {"description": "Gemma 2 è un modello efficiente lanciato da Google, coprendo una vasta gamma di scenari applicativi, da applicazioni di piccole dimensioni a elaborazioni di dati complesse."}, "gemma2:2b": {"description": "Gemma 2 è un modello efficiente lanciato da Google, coprendo una vasta gamma di scenari applicativi, da applicazioni di piccole dimensioni a elaborazioni di dati complesse."}, "generalv3": {"description": "Spark Pro è un modello linguistico di grandi dimensioni ad alte prestazioni, ottimizzato per settori professionali, focalizzandosi su matematica, programmazione, medicina, educazione e altro, supportando la ricerca online e plugin integrati per meteo, data e altro. Il modello ottimizzato mostra prestazioni eccellenti e alta efficienza in domande e risposte complesse, comprensione del linguaggio e creazione di testi di alto livello, rendendolo una scelta ideale per scenari di applicazione professionale."}, "generalv3.5": {"description": "Spark3.5 Max è la versione più completa, supportando la ricerca online e numerosi plugin integrati. Le sue capacità core completamente ottimizzate, insieme alla definizione dei ruoli di sistema e alla funzionalità di chiamata di funzioni, lo rendono estremamente eccellente e performante in vari scenari di applicazione complessi."}, "glm-4": {"description": "GLM-4 è la versione flagship rilasciata a gennaio 2024, attualmente sostituita da GLM-4-0520, più potente."}, "glm-4-0520": {"description": "GLM-4-0520 è l'ultima versione del modello, progettata per compiti altamente complessi e diversificati, con prestazioni eccezionali."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat mostra elevate prestazioni in vari aspetti come semantica, matematica, ragionamento, codice e conoscenza. Ha anche la capacità di navigare in rete, eseguire codice, chiamare strumenti personalizzati e inferire testi lunghi. Supporta 26 lingue, tra cui gia<PERSON>e, coreano e tedesco."}, "glm-4-air": {"description": "GLM-4-Air è una versione economica, con prestazioni simili a GLM-4, che offre velocità elevate a un prezzo accessibile."}, "glm-4-air-250414": {"description": "GLM-4-Air è una versione conveniente, con prestazioni simili a GLM-4, offrendo velocità elevate a un prezzo accessibile."}, "glm-4-airx": {"description": "GLM-4-AirX offre una versione efficiente di GLM-4-Air, con velocità di inferenza fino a 2,6 volte superiore."}, "glm-4-alltools": {"description": "GLM-4-AllTools è un modello di agente multifunzionale, otti<PERSON><PERSON><PERSON> per supportare la pianificazione di istruzioni complesse e le chiamate agli strumenti, come la navigazione web, l'interpretazione del codice e la generazione di testo, adatto per l'esecuzione di più compiti."}, "glm-4-flash": {"description": "GLM-4-<PERSON> è l'ideale per compiti semplici, con la massima velocità e il prezzo più conveniente."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON> è l'ideale per compiti semplici, con la massima velocità e gratuito."}, "glm-4-flashx": {"description": "GLM-4-Flash<PERSON> è una versione potenziata di Flash, con una velocità di inferenza super veloce."}, "glm-4-long": {"description": "GLM-4-Long supporta input di testo ultra-lunghi, adatto per compiti di memoria e gestione di documenti su larga scala."}, "glm-4-plus": {"description": "GLM-4-Plus, come flagship ad alta intelligenza, ha potenti capacità di elaborazione di testi lunghi e compiti complessi, con prestazioni complessive migliorate."}, "glm-4.1v-thinking-flash": {"description": "La serie GLM-4.1V-Thinking è attualmente il modello visivo più performante tra i modelli VLM di livello 10 miliardi di parametri noti, integrando le migliori prestazioni SOTA nelle attività di linguaggio visivo di pari livello, tra cui comprensione video, domande sulle immagini, risoluzione di problemi disciplinari, riconoscimento OCR, interpretazione di documenti e grafici, agent GUI, coding front-end web, grounding e altro. Le capacità in molteplici compiti superano persino il modello Qwen2.5-VL-72B con 8 volte più parametri. Grazie a tecniche avanzate di apprendimento rinforzato, il modello padroneggia il ragionamento tramite catena di pensiero per migliorare accuratezza e ricchezza delle risposte, superando significativamente i modelli tradizionali non-thinking in termini di risultati finali e interpretabilità."}, "glm-4.1v-thinking-flashx": {"description": "La serie GLM-4.1V-Thinking è attualmente il modello visivo più performante tra i modelli VLM di livello 10 miliardi di parametri noti, integrando le migliori prestazioni SOTA nelle attività di linguaggio visivo di pari livello, tra cui comprensione video, domande sulle immagini, risoluzione di problemi disciplinari, riconoscimento OCR, interpretazione di documenti e grafici, agent GUI, coding front-end web, grounding e altro. Le capacità in molteplici compiti superano persino il modello Qwen2.5-VL-72B con 8 volte più parametri. Grazie a tecniche avanzate di apprendimento rinforzato, il modello padroneggia il ragionamento tramite catena di pensiero per migliorare accuratezza e ricchezza delle risposte, superando significativamente i modelli tradizionali non-thinking in termini di risultati finali e interpretabilità."}, "glm-4.5": {"description": "Ultimo modello di punta di Zhipu, supporta la modalità di pensiero commutabile, con capacità complessive al livello SOTA dei modelli open source e una lunghezza di contesto fino a 128K."}, "glm-4.5-air": {"description": "Versione leggera di GLM-4.5, bilancia prestazioni e rapporto qualità-prezzo, con capacità di commutazione flessibile tra modelli di pensiero ibridi."}, "glm-4.5-airx": {"description": "Versione ultra-veloce di GLM-4.5-Air, con tempi di risposta più rapidi, progettata per esigenze di grande scala e alta velocità."}, "glm-4.5-flash": {"description": "Versione gratuita di GLM-4.5, con ottime prestazioni in inferenza, codice e agenti intelligenti."}, "glm-4.5-x": {"description": "Versione ultra-veloce di GLM-4.5, con prestazioni potenti e velocità di generazione fino a 100 token al secondo."}, "glm-4v": {"description": "GLM-4V offre potenti capacità di comprensione e ragionamento visivo, supportando vari compiti visivi."}, "glm-4v-flash": {"description": "GLM-4V-Flash si concentra sulla comprensione efficiente di un'unica immagine, adatta a scenari di analisi rapida delle immagini, come l'analisi delle immagini in tempo reale o l'elaborazione di immagini in batch."}, "glm-4v-plus": {"description": "GLM-4V-Plus ha la capacità di comprendere contenuti video e più immagini, adatto per compiti multimodali."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus ha la capacità di comprendere contenuti video e molte immagini, adatto per compiti multimodali."}, "glm-z1-air": {"description": "<PERSON>lo di inferenza: dotato di potenti capacità di inferenza, adatto per compiti che richiedono un ragionamento profondo."}, "glm-z1-airx": {"description": "Inferenza ultraveloce: con una velocità di inferenza super rapida e prestazioni di ragionamento potenti."}, "glm-z1-flash": {"description": "Serie GLM-Z1 con forti capacità di ragionamento complesso, eccellente in logica, matematica e programmazione."}, "glm-z1-flashx": {"description": "Alta velocità e basso costo: versione potenziata Flash, con velocità di inferenza ultra-rapida e migliore garanzia di concorrenza."}, "glm-zero-preview": {"description": "GLM-Zero-Preview possiede potenti capacità di ragionamento complesso, eccellendo nei campi del ragionamento logico, della matematica e della programmazione."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash offre funzionalità e miglioramenti di nuova generazione, tra cui velocità eccezionale, utilizzo di strumenti nativi, generazione multimodale e una finestra di contesto di 1M token."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental è il più recente modello AI multimodale sperimentale di Google, con un miglioramento della qualità rispetto alle versioni storiche, in particolare per quanto riguarda la conoscenza del mondo, il codice e il lungo contesto."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash è il modello principale più avanzato di Google, progettato per compiti avanzati di ragionamento, codifica, matematica e scienze. Include capacità di “pensiero” integrate, che gli permettono di fornire risposte con maggiore accuratezza e una gestione più dettagliata del contesto.\n\nNota: questo modello ha due varianti: con pensiero e senza pensiero. Il prezzo di output varia significativamente a seconda che la capacità di pensiero sia attivata o meno. Se scegli la variante standard (senza il suffisso “:thinking”), il modello eviterà esplicitamente di generare token di pensiero.\n\nPer sfruttare la capacità di pensiero e ricevere token di pensiero, devi selezionare la variante “:thinking”, che comporta un prezzo di output più elevato per il pensiero.\n\nInoltre, Gemini 2.5 Flash può essere configurato tramite il parametro “max tokens for reasoning”, come descritto nella documentazione (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash è il modello principale più avanzato di Google, progettato per ragionamenti avanzati, codifica, matematica e compiti scientifici. Include capacità di 'pensiero' integrate, permettendo di fornire risposte con maggiore accuratezza e una gestione contestuale più dettagliata.\n\nNota: questo modello ha due varianti: pensiero e non pensiero. I prezzi di output variano significativamente a seconda che la capacità di pensiero sia attivata o meno. Se scegli la variante standard (senza il suffisso ':thinking'), il modello eviterà esplicitamente di generare token di pensiero.\n\nPer sfruttare la capacità di pensiero e ricevere token di pensiero, devi scegliere la variante ':thinking', che comporterà un prezzo di output di pensiero più elevato.\n\nInoltre, Gemini 2.5 Flash può essere configurato tramite il parametro 'numero massimo di token per il ragionamento', come descritto nella documentazione (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash è il modello principale più avanzato di Google, progettato per ragionamenti avanzati, codifica, matematica e compiti scientifici. Include capacità di 'pensiero' integrate, permettendo di fornire risposte con maggiore accuratezza e una gestione contestuale più dettagliata.\n\nNota: questo modello ha due varianti: pensiero e non pensiero. I prezzi di output variano significativamente a seconda che la capacità di pensiero sia attivata o meno. Se scegli la variante standard (senza il suffisso ':thinking'), il modello eviterà esplicitamente di generare token di pensiero.\n\nPer sfruttare la capacità di pensiero e ricevere token di pensiero, devi scegliere la variante ':thinking', che comporterà un prezzo di output di pensiero più elevato.\n\nInoltre, Gemini 2.5 Flash può essere configurato tramite il parametro 'numero massimo di token per il ragionamento', come descritto nella documentazione (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro è il modello di pensiero più avanzato di Google, capace di ragionare su problemi complessi nel codice, nella matematica e nei campi STEM, nonché di analizzare grandi set di dati, codebase e documenti utilizzando un contesto esteso."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview è il modello di pensiero più avanzato di Google, in grado di ragionare su problemi complessi nel campo del codice, della matematica e delle STEM, oltre a utilizzare un contesto esteso per analizzare grandi set di dati, repository di codice e documenti."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash offre capacità di elaborazione multimodale ottimizzate, adatte a vari scenari di compiti complessi."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro combina le più recenti tecnologie di ottimizzazione, offrendo una capacità di elaborazione dei dati multimodali più efficiente."}, "google/gemma-2-27b": {"description": "Gemma 2 è un modello efficiente lanciato da Google, coprendo una varietà di scenari applicativi, dalle piccole applicazioni all'elaborazione di dati complessi."}, "google/gemma-2-27b-it": {"description": "Gemma 2 continua il concetto di design leggero ed efficiente."}, "google/gemma-2-2b-it": {"description": "Modello di ottimizzazione delle istruzioni leggero di Google"}, "google/gemma-2-9b": {"description": "Gemma 2 è un modello efficiente lanciato da Google, coprendo una varietà di scenari applicativi, dalle piccole applicazioni all'elaborazione di dati complessi."}, "google/gemma-2-9b-it": {"description": "Gemma 2 è una serie di modelli di testo open source leggeri di Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 è la serie di modelli di testo open source leggeri di Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) offre capacità di elaborazione di istruzioni di base, adatta per applicazioni leggere."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B è un modello linguistico open source di Google che ha stabilito nuovi standard in termini di efficienza e prestazioni."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B è un modello linguistico open source di Google, che ha stabilito nuovi standard in termini di efficienza e prestazioni."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo, adatto a una varietà di compiti di generazione e comprensione del testo, attualmente punta a gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo, adatto a una varietà di compiti di generazione e comprensione del testo, attualmente punta a gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo, adatto a una varietà di compiti di generazione e comprensione del testo, attualmente punta a gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo, adatto a una varietà di compiti di generazione e comprensione del testo, attualmente punta a gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo è un modello efficiente fornito da OpenAI, adatto per chat e generazione di testo, che supporta chiamate di funzione parallele."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k è un modello di generazione di testo ad alta capacità, adatto per compiti complessi."}, "gpt-4": {"description": "GPT-4 offre una finestra di contesto più ampia, in grado di gestire input testuali più lunghi, adatta a scenari che richiedono un'integrazione ampia delle informazioni e analisi dei dati."}, "gpt-4-0125-preview": {"description": "L'ultimo modello GPT-4 Turbo ha funzionalità visive. Ora, le richieste visive possono essere effettuate utilizzando il formato JSON e le chiamate di funzione. GPT-4 Turbo è una versione potenziata che offre supporto economico per compiti multimodali. Trova un equilibrio tra accuratezza ed efficienza, adatta a scenari di applicazione che richiedono interazioni in tempo reale."}, "gpt-4-0613": {"description": "GPT-4 offre una finestra di contesto più ampia, in grado di gestire input testuali più lunghi, adatta a scenari che richiedono un'integrazione ampia delle informazioni e analisi dei dati."}, "gpt-4-1106-preview": {"description": "L'ultimo modello GPT-4 Turbo ha funzionalità visive. Ora, le richieste visive possono essere effettuate utilizzando il formato JSON e le chiamate di funzione. GPT-4 Turbo è una versione potenziata che offre supporto economico per compiti multimodali. Trova un equilibrio tra accuratezza ed efficienza, adatta a scenari di applicazione che richiedono interazioni in tempo reale."}, "gpt-4-32k": {"description": "GPT-4 offre una finestra di contesto più ampia, in grado di gestire input testuali più lunghi, adatta a scenari che richiedono un'integrazione ampia delle informazioni e analisi dei dati."}, "gpt-4-32k-0613": {"description": "GPT-4 offre una finestra di contesto più ampia, in grado di gestire input testuali più lunghi, adatta a scenari che richiedono un'integrazione ampia delle informazioni e analisi dei dati."}, "gpt-4-turbo": {"description": "L'ultimo modello GPT-4 Turbo ha funzionalità visive. Ora, le richieste visive possono essere effettuate utilizzando il formato JSON e le chiamate di funzione. GPT-4 Turbo è una versione potenziata che offre supporto economico per compiti multimodali. Trova un equilibrio tra accuratezza ed efficienza, adatta a scenari di applicazione che richiedono interazioni in tempo reale."}, "gpt-4-turbo-2024-04-09": {"description": "L'ultimo modello GPT-4 Turbo ha funzionalità visive. Ora, le richieste visive possono essere effettuate utilizzando il formato JSON e le chiamate di funzione. GPT-4 Turbo è una versione potenziata che offre supporto economico per compiti multimodali. Trova un equilibrio tra accuratezza ed efficienza, adatta a scenari di applicazione che richiedono interazioni in tempo reale."}, "gpt-4-turbo-preview": {"description": "L'ultimo modello GPT-4 Turbo ha funzionalità visive. Ora, le richieste visive possono essere effettuate utilizzando il formato JSON e le chiamate di funzione. GPT-4 Turbo è una versione potenziata che offre supporto economico per compiti multimodali. Trova un equilibrio tra accuratezza ed efficienza, adatta a scenari di applicazione che richiedono interazioni in tempo reale."}, "gpt-4-vision-preview": {"description": "L'ultimo modello GPT-4 Turbo ha funzionalità visive. Ora, le richieste visive possono essere effettuate utilizzando il formato JSON e le chiamate di funzione. GPT-4 Turbo è una versione potenziata che offre supporto economico per compiti multimodali. Trova un equilibrio tra accuratezza ed efficienza, adatta a scenari di applicazione che richiedono interazioni in tempo reale."}, "gpt-4.1": {"description": "GPT-4.1 è il nostro modello di punta per compiti complessi. È particolarmente adatto per risolvere problemi trasversali."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini offre un equilibrio tra intelligenza, velocità e costo, rendendolo un modello attraente per molti casi d'uso."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini offre un equilibrio tra intelligenza, velocità e costo, rendendolo un modello attraente per molti casi d'uso."}, "gpt-4.5-preview": {"description": "Anteprima di ricerca di GPT-4.5, il nostro modello GPT più grande e potente fino ad oggi. Possiede una vasta conoscenza del mondo e comprende meglio le intenzioni degli utenti, eccellendo in compiti creativi e nella pianificazione autonoma. GPT-4.5 accetta input testuali e visivi e genera output testuali (inclusi output strutturati). Supporta funzionalità chiave per gli sviluppatori, come chiamate di funzione, API in batch e output in streaming. GPT-4.5 si distingue particolarmente in compiti che richiedono pensiero creativo, aperto e dialogo (come scrittura, apprendimento o esplorazione di nuove idee). La data di scadenza delle conoscenze è ottobre 2023."}, "gpt-4o": {"description": "ChatGPT-4o è un modello dinamico, aggiornato in tempo reale per mantenere la versione più recente. Combina una potente comprensione e generazione del linguaggio, adatta a scenari di applicazione su larga scala, inclusi servizi clienti, educazione e supporto tecnico."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o è un modello dinamico, aggiornato in tempo reale per mantenere la versione più recente. Combina una potente comprensione e generazione del linguaggio, adatta a scenari di applicazione su larga scala, inclusi servizi clienti, educazione e supporto tecnico."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o è un modello dinamico, aggiornato in tempo reale per mantenere la versione più recente. Combina una potente comprensione e generazione del linguaggio, adatta a scenari di applicazione su larga scala, inclusi servizi clienti, educazione e supporto tecnico."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o è un modello dinamico che si aggiorna in tempo reale per mantenere sempre l'ultima versione. Combina una potente comprensione del linguaggio e capacità di generazione, rendendolo adatto a scenari di applicazione su larga scala, inclusi assistenza clienti, istruzione e supporto tecnico."}, "gpt-4o-audio-preview": {"description": "Modello GPT-4o Audio, supporta input e output audio."}, "gpt-4o-mini": {"description": "GPT-4o mini è il modello più recente lanciato da OpenAI dopo il GPT-4 Omni, supporta input visivi e testuali e produce output testuali. Come il loro modello di punta in formato ridotto, è molto più economico rispetto ad altri modelli all'avanguardia recenti e costa oltre il 60% in meno rispetto a GPT-3.5 Turbo. Mantiene un'intelligenza all'avanguardia, offrendo un rapporto qualità-prezzo significativo. GPT-4o mini ha ottenuto un punteggio dell'82% nel test MMLU e attualmente è classificato più in alto di GPT-4 per preferenze di chat."}, "gpt-4o-mini-audio-preview": {"description": "Modello GPT-4o mini Audio, supporta input e output audio."}, "gpt-4o-mini-realtime-preview": {"description": "Versione in tempo reale di GPT-4o-mini, supporta input e output audio e testuali in tempo reale."}, "gpt-4o-mini-search-preview": {"description": "La versione preview di GPT-4o mini per la ricerca è un modello appositamente addestrato per comprendere ed eseguire query di ricerca web, utilizzando l’API Chat Completions. Oltre ai costi per token, le query di ricerca web comportano un costo per ogni chiamata allo strumento."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe è un modello di trascrizione vocale che utilizza GPT-4o per convertire audio in testo. Rispetto al modello Whisper originale, migliora il tasso di errore delle parole e la precisione nel riconoscimento linguistico. Usalo per ottenere trascrizioni più accurate."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS è un modello di sintesi vocale basato su GPT-4o mini, che offre una generazione di voce di alta qualità a un costo più basso."}, "gpt-4o-realtime-preview": {"description": "Versione in tempo reale di GPT-4o, supporta input e output audio e testuali in tempo reale."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Versione in tempo reale di GPT-4o, supporta input e output audio e testuali in tempo reale."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Versione in tempo reale di GPT-4o, supporta input e output audio e testuali in tempo reale."}, "gpt-4o-search-preview": {"description": "La versione preview di GPT-4o per la ricerca è un modello appositamente addestrato per comprendere ed eseguire query di ricerca web, utilizzando l’API Chat Completions. Oltre ai costi per token, le query di ricerca web comportano un costo per ogni chiamata allo strumento."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe è un modello di trascrizione vocale che utilizza GPT-4o per convertire audio in testo. Rispetto al modello Whisper originale, migliora il tasso di errore delle parole e la precisione nel riconoscimento linguistico. Usalo per ottenere trascrizioni più accurate."}, "gpt-image-1": {"description": "Modello nativo multimodale di generazione immagini di ChatGPT"}, "grok-2-1212": {"description": "Questo modello ha migliorato l'accuratezza, il rispetto delle istruzioni e le capacità multilingue."}, "grok-2-image-1212": {"description": "Il nostro ultimo modello di generazione immagini può creare immagini vivide e realistiche basate su prompt testuali. Eccelle nella generazione di immagini per marketing, social media e intrattenimento."}, "grok-2-vision-1212": {"description": "Questo modello ha migliorato l'accuratezza, il rispetto delle istruzioni e le capacità multilingue."}, "grok-3": {"description": "<PERSON><PERSON> di punta, eccelle in estrazione dati, programmazione e sintesi testuale per applicazioni aziendali, con profonda conoscenza nei settori finanziario, medico, legale e scientifico."}, "grok-3-fast": {"description": "<PERSON><PERSON> di punta, eccelle in estrazione dati, programmazione e sintesi testuale per applicazioni aziendali, con profonda conoscenza nei settori finanziario, medico, legale e scientifico."}, "grok-3-mini": {"description": "<PERSON>lo leggero che riflette prima di rispondere. Veloce e intelligente, adatto a compiti logici che non richiedono conoscenze di dominio profonde, con tracciamento del processo di pensiero originale."}, "grok-3-mini-fast": {"description": "<PERSON>lo leggero che riflette prima di rispondere. Veloce e intelligente, adatto a compiti logici che non richiedono conoscenze di dominio profonde, con tracciamento del processo di pensiero originale."}, "grok-4": {"description": "Il nostro modello di punta più recente e potente, eccellente nell'elaborazione del linguaggio naturale, nel calcolo matematico e nel ragionamento — un vero campione versatile e completo."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B è un modello linguistico che combina creatività e intelligenza, unendo diversi modelli di punta."}, "hunyuan-a13b": {"description": "Hunyuan è il primo modello di ragionamento ibrido, versione aggiornata di hunyuan-standard-256K, con 80 miliardi di parametri totali e 13 miliardi attivati. Di default opera in modalità pensiero lento, ma supporta il passaggio tra modalità pensiero lento e veloce tramite parametri o istruzioni, con il cambio che avviene aggiungendo / no_think prima della query. Le capacità complessive sono migliorate rispetto alla generazione precedente, con miglioramenti significativi in matematica, scienze, comprensione di testi lunghi e capacità agent."}, "hunyuan-code": {"description": "Ultimo modello di generazione di codice di Hunyuan, addestrato su un modello di base con 200B di dati di codice di alta qualità, con sei mesi di addestramento su dati SFT di alta qualità, la lunghezza della finestra di contesto è aumentata a 8K, e si posiziona tra i primi in cinque indicatori di valutazione automatica della generazione di codice; nelle valutazioni di alta qualità su dieci aspetti di codice in cinque lingue, le prestazioni sono nella prima fascia."}, "hunyuan-functioncall": {"description": "Ultimo modello FunctionCall con architettura MOE di Hunyuan, addestrato su dati di alta qualità per le chiamate di funzione, con una finestra di contesto di 32K, è in testa in vari indicatori di valutazione."}, "hunyuan-large": {"description": "Il modello Hunyuan-large ha un numero totale di parametri di circa 389B, con circa 52B di parametri attivati, ed è il modello MoE open source con la più grande scala di parametri e le migliori prestazioni nel settore, basato su architettura Transformer."}, "hunyuan-large-longcontext": {"description": "Specializzato nel gestire compiti di testi lunghi come riassunti di documenti e domande e risposte sui documenti, possiede anche capacità di generazione di testi generali. Eccelle nell'analisi e nella generazione di testi lunghi, in grado di affrontare efficacemente esigenze complesse e dettagliate di elaborazione di contenuti lunghi."}, "hunyuan-large-vision": {"description": "Questo modello è adatto per scenari di comprensione testo-immagine, basato sul modello misto Large di Hunyuan. Supporta input di più immagini a risoluzione arbitraria più testo, generando contenuti testuali, con un focus sulle attività di comprensione testo-immagine e un significativo miglioramento nelle capacità multilingue."}, "hunyuan-lite": {"description": "Aggiornato a una struttura MOE, con una finestra di contesto di 256k, è in testa a molti modelli open source in vari set di valutazione su NLP, codice, matematica e settori."}, "hunyuan-lite-vision": {"description": "Il modello multimodale Hunyuan più recente da 7B, con una finestra contestuale di 32K, supporta dialoghi multimodali in cinese e inglese, riconoscimento di oggetti nelle immagini, comprensione di documenti e tabelle, matematica multimodale, e supera i modelli concorrenti da 7B in vari indicatori di valutazione."}, "hunyuan-pro": {"description": "Modello di testo lungo MOE-32K con un miliardo di parametri. Ragg<PERSON>nge livelli di eccellenza in vari benchmark, con capacità di istruzioni complesse e ragionamento, supporta le chiamate di funzione, ottimizzato per traduzione multilingue, finanza, diritto e medicina."}, "hunyuan-role": {"description": "Ultimo modello di ruolo di Hunyuan, un modello di ruolo fine-tuned ufficialmente rilasciato da Hunyuan, addestrato su un dataset di scenari di ruolo, con migliori prestazioni di base in scenari di ruolo."}, "hunyuan-standard": {"description": "Utilizza una strategia di routing migliore, alleviando i problemi di bilanciamento del carico e convergenza degli esperti. Per i testi lunghi, l'indice di recupero è del 99,9%. MOE-32K offre un buon rapporto qualità-prezzo, bilanciando efficacia e costo, e gestisce l'input di testi lunghi."}, "hunyuan-standard-256K": {"description": "Utilizza una strategia di routing migliore, alleviando i problemi di bilanciamento del carico e convergenza degli esperti. Per i testi lunghi, l'indice di recupero è del 99,9%. MOE-256K supera ulteriormente in lunghezza ed efficacia, ampliando notevolmente la lunghezza massima di input."}, "hunyuan-standard-vision": {"description": "Il modello multimodale più recente di Hunyuan, supporta risposte in più lingue, con capacità equilibrate in cinese e inglese."}, "hunyuan-t1-********": {"description": "Costruisce completamente le capacità del modello in scienze umane e scientifiche, con una forte capacità di catturare informazioni in testi lunghi. Supporta il ragionamento per risolvere problemi scientifici di varia difficoltà, inclusi matematica, logica, scienza e codice."}, "hunyuan-t1-********": {"description": "Migliorare la capacità di generazione del codice a livello di progetto; migliorare la qualità della scrittura generata dal testo; potenziare la comprensione multi-turno degli argomenti, l’aderenza alle istruzioni toB e la comprensione di parole e termini; ottimizzare i problemi di output misto tra cinese semplificato e tradizionale e tra cinese e inglese."}, "hunyuan-t1-20250529": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> per la creazione di testi, la scrittura di saggi, il frontend del codice, la matematica, il ragionamento logico e altre competenze scientifiche, con miglioramenti nella capacità di seguire istruzioni."}, "hunyuan-t1-20250711": {"description": "Miglioramento significativo delle capacità in matematica avanzata, logica e codice, ottimizzazione della stabilità dell'output e potenziamento della capacità di gestione di testi lunghi."}, "hunyuan-t1-latest": {"description": "Il primo modello di inferenza ibrido su larga scala Hybrid-Transformer-Mamba del settore, che espande le capacità di inferenza, offre una velocità di decodifica eccezionale e allinea ulteriormente le preferenze umane."}, "hunyuan-t1-vision": {"description": "Modello di comprensione multimodale profonda <PERSON>, supporta catene di pensiero native multimodali, eccelle in vari scenari di ragionamento visivo e migliora significativamente rispetto ai modelli di pensiero rapido nei problemi scientifici."}, "hunyuan-t1-vision-20250619": {"description": "L'ultima versione del modello di pensiero profondo multimodale t1-vision di Hunyuan, supporta catene di pensiero native multimodali e presenta miglioramenti completi rispetto alla versione predefinita della generazione precedente."}, "hunyuan-turbo": {"description": "Anteprima della nuova generazione di modelli di linguaggio di Hunyuan, utilizza una nuova struttura di modello ibrido di esperti (MoE), con una maggiore efficienza di inferenza e prestazioni superiori rispetto a hunyuan-pro."}, "hunyuan-turbo-20241223": {"description": "Ottimizzazione di questa versione: scaling delle istruzioni sui dati, notevole aumento della capacità di generalizzazione del modello; notevole miglioramento delle capacità matematiche, di codifica e di ragionamento logico; ottimizzazione delle capacità di comprensione del testo e delle parole; ottimizzazione della qualità della generazione dei contenuti di creazione del testo."}, "hunyuan-turbo-latest": {"description": "Ottimizzazione dell'esperienza generale, inclusi comprensione NLP, creazione di testi, conversazione, domande e risposte, traduzione, e altro; miglioramento dell'umanizzazione, ottimizzazione dell'intelligenza emotiva del modello; potenziamento della capacità del modello di chiarire attivamente in caso di ambiguità; miglioramento della gestione di problemi di analisi di parole e frasi; aumento della qualità e dell'interattività della creazione; miglioramento dell'esperienza multi-turno."}, "hunyuan-turbo-vision": {"description": "Il nuovo modello di punta di linguaggio visivo di Hunyuan, adotta una nuova struttura di modello esperto misto (MoE), con miglioramenti complessivi nelle capacità di riconoscimento di base, creazione di contenuti, domande e risposte, analisi e ragionamento rispetto alla generazione precedente."}, "hunyuan-turbos-20250313": {"description": "Uniformare lo stile dei passaggi di risoluzione dei problemi matematici e rafforzare il question answering multi-turno in matematica. Ottimizzare lo stile delle risposte nella creazione testuale, eliminando l’impronta AI e aumentando la qualità letteraria."}, "hunyuan-turbos-20250416": {"description": "Aggiornamento della base pre-addestrata per rafforzare la comprensione e l’aderenza alle istruzioni; miglioramento delle capacità scientifiche in matematica, programmazione, logica e scienze durante la fase di allineamento; potenziamento delle capacità umanistiche come la qualità della scrittura creativa, la comprensione testuale, la precisione della traduzione e il question answering; rafforzamento delle capacità degli agenti in vari settori, con particolare attenzione alla comprensione multi-turno."}, "hunyuan-turbos-20250604": {"description": "Aggiornamento della base pre-addestrata, con miglioramenti nelle capacità di scrittura e comprensione della lettura, notevoli progressi nelle competenze di programmazione e scientifiche, e continui miglioramenti nell’aderenza a istruzioni complesse."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS è l'ultima versione del modello di punta Hunyuan, con capacità di pensiero più forti e un'esperienza utente migliore."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Eccelle nella gestione di compiti di testo lungo come riassunti di documenti e domande sui documenti, e possiede anche la capacità di gestire compiti di generazione di testo generico. Mostra prestazioni eccezionali nell'analisi e generazione di testi lunghi, affrontando efficacemente le esigenze di elaborazione di contenuti complessi e dettagliati."}, "hunyuan-turbos-role-plus": {"description": "Ultima versione del modello di role-playing di Hunyuan, finemente addestrato ufficialmente, basato sul modello Hunyuan e ulteriormente addestrato con dataset specifici per scenari di role-playing, offrendo migliori prestazioni di base in tali contesti."}, "hunyuan-turbos-vision": {"description": "Questo modello è adatto per scenari di comprensione testo-immagine ed è basato sulla più recente versione turbos di Hunyuan, una nuova generazione di modello linguistico visivo di punta focalizzato su compiti di comprensione testo-immagine, inclusi riconoscimento di entità basato su immagini, domande di conoscenza, creazione di testi e risoluzione di problemi tramite foto, con miglioramenti completi rispetto alla generazione precedente."}, "hunyuan-turbos-vision-20250619": {"description": "L'ultima versione del modello linguistico visivo di punta turbos-vision di Hunyuan, con miglioramenti completi rispetto alla versione predefinita della generazione precedente in compiti di comprensione testo-immagine, inclusi riconoscimento di entità basato su immagini, domande di conoscenza, creazione di testi e risoluzione di problemi tramite foto."}, "hunyuan-vision": {"description": "Ultimo modello multimodale di Hunyuan, supporta l'input di immagini e testo per generare contenuti testuali."}, "image-01": {"description": "Nuovo modello di generazione immagini con resa dettagliata, supporta generazione da testo a immagine e da immagine a immagine."}, "image-01-live": {"description": "Modello di generazione immagini con resa dettagliata, supporta generazione da testo a immagine e impostazioni di stile."}, "imagen-4.0-generate-preview-06-06": {"description": "Serie di modelli di generazione di immagini da testo di quarta generazione Imagen"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Serie di modelli di generazione di immagini da testo di quarta generazione Imagen versione Ultra"}, "imagen4/preview": {"description": "Il modello di generazione immagini di massima qualità di Google"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 offre soluzioni di dialogo intelligente in vari scenari."}, "internlm2.5-latest": {"description": "La nostra ultima serie di modelli, con prestazioni di ragionamento eccezionali, supporta una lunghezza di contesto di 1M e offre una migliore capacità di seguire istruzioni e chiamare strumenti."}, "internlm3-latest": {"description": "La nostra ultima serie di modelli, con prestazioni di inferenza eccezionali, è leader tra i modelli open source della stessa classe. Punta di default ai modelli della serie InternLM3 appena rilasciati."}, "internvl2.5-latest": {"description": "La versione InternVL2.5 che stiamo ancora mantenendo, offre prestazioni eccellenti e stabili. Punta di default al nostro ultimo modello della serie InternVL2.5, attualmente indirizzato a internvl2.5-78b."}, "internvl3-latest": {"description": "Il nostro ultimo modello multimodale, con una maggiore capacità di comprensione delle immagini e del testo, e una comprensione delle immagini a lungo termine, offre prestazioni paragonabili ai migliori modelli closed-source. Punta di default al nostro ultimo modello della serie InternVL, attualmente indirizzato a internvl3-78b."}, "irag-1.0": {"description": "iRAG (image based RAG) sviluppato da Baidu è una tecnologia di generazione immagini da testo potenziata da retrieval, che combina risorse di miliardi di immagini di Baidu Search con potenti modelli di base per generare immagini ultra-realistiche, superando di gran lunga i sistemi nativi di generazione da testo a immagine, eliminando l'effetto artificiale AI e mantenendo bassi costi. iRAG è caratterizzato da assenza di allucinazioni, realismo estremo e risultati immediati."}, "jamba-large": {"description": "Il nostro modello più potente e avanzato, progettato per gestire compiti complessi a livello aziendale, con prestazioni eccezionali."}, "jamba-mini": {"description": "Il modello più efficiente della sua categoria, che bilancia velocità e qualità, con un volume ridotto."}, "jina-deepsearch-v1": {"description": "La ricerca approfondita combina la ricerca online, la lettura e il ragionamento, consentendo indagini complete. Puoi considerarlo come un agente che accetta il tuo compito di ricerca - eseguirà una ricerca approfondita e iterativa prima di fornire una risposta. Questo processo implica una continua ricerca, ragionamento e risoluzione dei problemi da diverse angolazioni. Questo è fondamentalmente diverso dai modelli di grandi dimensioni standard che generano risposte direttamente dai dati pre-addestrati e dai tradizionali sistemi RAG che si basano su ricerche superficiali una tantum."}, "kimi-k2": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, la<PERSON><PERSON> da Moonshot AI, è un modello base con architettura MoE dotato di potenti capacità di codice e agenti, con 1 trilione di parametri totali e 32 miliardi di parametri attivi. Nei test di benchmark su ragionamento generale, programmazione, matematica e agenti, il modello K2 supera altri modelli open source principali."}, "kimi-k2-0711-preview": {"description": "kimi-k2 è un modello base con architettura MoE dotato di potenti capacità di codice e Agent, con un totale di 1T parametri e 32B parametri attivi. Nei test di benchmark per ragionamento generale, programmazione, matematica e Agent, il modello K2 supera altri modelli open source principali."}, "kimi-latest": {"description": "Il prodotto Kimi Smart Assistant utilizza il più recente model<PERSON>, che potrebbe includere funzionalità non ancora stabili. Supporta la comprensione delle immagini e selezionerà automaticamente il modello di fatturazione 8k/32k/128k in base alla lunghezza del contesto della richiesta."}, "kimi-thinking-preview": {"description": "Il modello kimi-thinking-preview, for<PERSON><PERSON> <PERSON>'s Dark Side, è un modello multimodale di pensiero con capacità di ragionamento multimodale e generale, eccellente nel ragionamento profondo per aiutare a risolvere problemi più complessi."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM è un modello linguistico sperimentale, specifico per compiti, addestrato per rispettare i principi della scienza dell'apprendimento, in grado di seguire istruzioni sistematiche in contesti di insegnamento e apprendimento, fungendo da tutor esperto."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM è un modello linguistico sperimentale, specifico per compiti, addestrato per rispettare i principi della scienza dell'apprendimento, in grado di seguire istruzioni sistematiche in contesti di insegnamento e apprendimento, fungendo da tutor esperto."}, "lite": {"description": "Spark Lite è un modello di linguaggio di grandi dimensioni leggero, con latenza estremamente bassa e capacità di elaborazione efficiente, completamente gratuito e aperto, supporta funzionalità di ricerca online in tempo reale. La sua caratteristica di risposta rapida lo rende eccellente per applicazioni di inferenza su dispositivi a bassa potenza e per il fine-tuning dei modelli, offrendo agli utenti un'ottima efficienza dei costi e un'esperienza intelligente, soprattutto nei contesti di domande e risposte, generazione di contenuti e ricerca."}, "llama-2-7b-chat": {"description": "Llama2 è una serie di modelli linguistici di grandi dimensioni (LLM) sviluppati e resi open source da Meta. Questa serie comprende modelli generativi di testo pre-addestrati e finetunati, con dimensioni che variano da 7 miliardi a 70 miliardi di parametri. Sul piano architettonico, Llama2 è un modello linguistico autoregressivo che utilizza un'architettura di trasformatore ottimizzata. Le versioni aggiornate utilizzano il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF) per allineare le preferenze di utilità e sicurezza umane. Llama2 supera Llama in diverse basi di dati accademiche, fornendo ispirazione per la progettazione e lo sviluppo di molti altri modelli."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B offre capacità di ragionamento AI più potenti, adatto per applicazioni complesse, supporta un'elaborazione computazionale elevata garantendo efficienza e precisione."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B è un modello ad alte prestazioni, offre capacità di generazione di testo rapida, particolarmente adatto per scenari applicativi che richiedono efficienza su larga scala e costi contenuti."}, "llama-3.1-instruct": {"description": "Il modello Llama 3.1 per l'addestramento di istruzioni è stato ottimizzato per scenari di conversazione, superando molti dei modelli di chat open source esistenti nelle comuni benchmark settoriali."}, "llama-3.2-11b-vision-instruct": {"description": "Eccellenti capacità di ragionamento visivo su immagini ad alta risoluzione, adatte ad applicazioni di comprensione visiva."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Eccelle in compiti come la descrizione delle immagini e le domande visive, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "llama-3.2-90b-vision-instruct": {"description": "Capacità avanzate di ragionamento visivo per applicazioni di agenti di comprensione visiva."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Eccelle in compiti come la descrizione delle immagini e le domande visive, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "llama-3.2-vision-instruct": {"description": "Il modello Llama 3.2-Vision istruito è ottimizzato per il riconoscimento visivo, l' inferenza di immagini, la descrizione di immagini e la risposta a domande comuni relative a immagini."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 è il modello di linguaggio open source multilingue più avanzato della serie Llama, che offre prestazioni paragonabili a un modello da 405B a un costo estremamente ridotto. Basato su una struttura Transformer, migliora l'utilità e la sicurezza attraverso il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF). La sua versione ottimizzata per le istruzioni è progettata per dialoghi multilingue e supera molti modelli di chat open source e chiusi in vari benchmark di settore. La data di scadenza delle conoscenze è dicembre 2023."}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 è un modello linguistico di grandi dimensioni multilingue (LLM) da 70B (input/output testuale) con pre-addestramento e aggiustamento delle istruzioni. Il modello di testo puro di Llama 3.3 è ottimizzato per casi d'uso di dialogo multilingue e supera molti modelli di chat open-source e chiusi nei benchmark di settore comuni."}, "llama-3.3-instruct": {"description": "Il modello Llama 3.3 per l'addestramento di istruzioni è stato ottimizzato per scenari di conversazione, superando molti modelli di chat open source esistenti nelle comuni benchmark settoriali."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B offre capacità di elaborazione della complessità senza pari, progettato su misura per progetti ad alta richiesta."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B offre prestazioni di ragionamento di alta qualità, adatto per esigenze applicative in vari scenari."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use offre potenti capacità di invocazione degli strumenti, supporta l'elaborazione efficiente di compiti complessi."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use è un modello ottimizzato per l'uso efficiente degli strumenti, supporta calcoli paralleli rapidi."}, "llama3.1": {"description": "Llama 3.1 è il modello leader <PERSON><PERSON><PERSON>a, supporta fino a 405B parametri, applicabile a dialoghi complessi, traduzione multilingue e analisi dei dati."}, "llama3.1:405b": {"description": "Llama 3.1 è il modello leader <PERSON><PERSON><PERSON>a, supporta fino a 405B parametri, applicabile a dialoghi complessi, traduzione multilingue e analisi dei dati."}, "llama3.1:70b": {"description": "Llama 3.1 è il modello leader <PERSON><PERSON><PERSON>a, supporta fino a 405B parametri, applicabile a dialoghi complessi, traduzione multilingue e analisi dei dati."}, "llava": {"description": "LLaVA è un modello multimodale che combina un codificatore visivo e Vicuna, per una potente comprensione visiva e linguistica."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B offre capacità di elaborazione visiva integrate, generando output complessi attraverso input visivi."}, "llava:13b": {"description": "LLaVA è un modello multimodale che combina un codificatore visivo e Vicuna, per una potente comprensione visiva e linguistica."}, "llava:34b": {"description": "LLaVA è un modello multimodale che combina un codificatore visivo e Vicuna, per una potente comprensione visiva e linguistica."}, "mathstral": {"description": "MathΣtral è progettato per la ricerca scientifica e il ragionamento matematico, offre capacità di calcolo efficaci e interpretazione dei risultati."}, "max-32k": {"description": "Spark Max 32K è dotato di una grande capacità di elaborazione del contesto, con una comprensione del contesto e capacità di ragionamento logico superiori, supporta input testuali fino a 32K token, adatto per la lettura di documenti lunghi, domande e risposte su conoscenze private e altri scenari."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct è un modello di linguaggio grande completamente addestrato da Wuwen Xi<PERSON>. Megrez-3B-Instruct mira a creare una soluzione di intelligenza per dispositivi finali, rapida, compatta e facile da usare, attraverso il concetto di collaborazione hardware-software."}, "meta-llama-3-70b-instruct": {"description": "Un potente modello con 70 miliardi di parametri che eccelle nel ragionamento, nella codifica e nelle ampie applicazioni linguistiche."}, "meta-llama-3-8b-instruct": {"description": "Un modello versatile con 8 miliardi di parametri ottimizzato per compiti di dialogo e generazione di testo."}, "meta-llama-3.1-405b-instruct": {"description": "I modelli di testo solo ottimizzati per istruzioni Llama 3.1 sono progettati per casi d'uso di dialogo multilingue e superano molti dei modelli di chat open source e chiusi disponibili su benchmark industriali comuni."}, "meta-llama-3.1-70b-instruct": {"description": "I modelli di testo solo ottimizzati per istruzioni Llama 3.1 sono progettati per casi d'uso di dialogo multilingue e superano molti dei modelli di chat open source e chiusi disponibili su benchmark industriali comuni."}, "meta-llama-3.1-8b-instruct": {"description": "I modelli di testo solo ottimizzati per istruzioni Llama 3.1 sono progettati per casi d'uso di dialogo multilingue e superano molti dei modelli di chat open source e chiusi disponibili su benchmark industriali comuni."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) offre eccellenti capacità di elaborazione linguistica e un'interazione di alta qualità."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 offre eccellenti capacità di elaborazione del linguaggio e un'esperienza interattiva di alta qualità."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) è un potente modello di chat, in grado di gestire esigenze di dialogo complesse."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) offre supporto multilingue, coprendo una vasta gamma di conoscenze di dominio."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Eccelle in compiti come la descrizione di immagini e le domande visive, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Eccelle in compiti come la descrizione di immagini e le domande visive, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Eccelle in compiti come la descrizione di immagini e le domande visive, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Il modello di linguaggio di grandi dimensioni multilingue Meta Llama 3.3 (LLM) è un modello generativo pre-addestrato e regolato per istruzioni da 70B (input/output di testo). Il modello di testo puro di Llama 3.3 regolato per istruzioni è ottimizzato per casi d'uso di dialogo multilingue e supera molti modelli di chat open source e chiusi disponibili su benchmark di settore comuni."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Eccelle in compiti come la descrizione di immagini e le domande visive, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite è adatto per ambienti che richiedono alta efficienza e bassa latenza."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo offre capacità superiori di comprensione e generazione del linguaggio, adatto per i compiti computazionali più impegnativi."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite è adatto per ambienti a risorse limitate, offrendo prestazioni bilanciate eccellenti."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo è un modello di linguaggio ad alte prestazioni, supportando una vasta gamma di scenari applicativi."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B è un potente modello pre-addestrato e ottimizzato per istruzioni."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "Il modello Llama 3.1 Turbo 405B offre un supporto di contesto di capacità estremamente grande per l'elaborazione di big data, eccellendo nelle applicazioni di intelligenza artificiale su larga scala."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 è il modello leader <PERSON><PERSON><PERSON>a, supporta fino a 405B parametri, applicabile a conversazioni complesse, traduzione multilingue e analisi dei dati."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Il modello Llama 3.1 70B è stato ottimizzato per applicazioni ad alto carico, quantizzato a FP8 per fornire una maggiore efficienza computazionale e accuratezza, garantendo prestazioni eccezionali in scenari complessi."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Il modello Llama 3.1 8B utilizza la quantizzazione FP8, supportando fino a 131.072 token di contesto, ed è un leader tra i modelli open source, adatto per compiti complessi, superando molti benchmark di settore."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct è ottimizzato per scenari di dialogo di alta qualità, con prestazioni eccellenti in varie valutazioni umane."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct è ottimizzato per scenari di dialogo di alta qualità, con prestazioni superiori a molti modelli chiusi."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct è progettato per dialoghi di alta qualità, con prestazioni eccezionali nelle valutazioni umane, particolarmente adatto per scenari ad alta interazione."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct è l'ultima versione rilasciata da Meta, ottimizzata per scenari di dialogo di alta qualità, superando molti modelli chiusi di punta."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 offre supporto multilingue ed è uno dei modelli generativi leader nel settore."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Si distingue in compiti come la descrizione delle immagini e il question answering visivo, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 è progettato per gestire compiti che combinano dati visivi e testuali. Si distingue in compiti come la descrizione delle immagini e il question answering visivo, colmando il divario tra generazione del linguaggio e ragionamento visivo."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 è il modello di linguaggio open source multilingue più avanzato della serie Llama, che offre prestazioni paragonabili a un modello da 405B a un costo estremamente ridotto. Basato su una struttura Transformer, migliora l'utilità e la sicurezza attraverso il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF). La sua versione ottimizzata per le istruzioni è progettata per dialoghi multilingue e supera molti modelli di chat open source e chiusi in vari benchmark di settore. La data di scadenza delle conoscenze è dicembre 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 è il modello di linguaggio open source multilingue più avanzato della serie Llama, che offre prestazioni paragonabili a un modello da 405B a un costo estremamente ridotto. Basato su una struttura Transformer, migliora l'utilità e la sicurezza attraverso il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF). La sua versione ottimizzata per le istruzioni è progettata per dialoghi multilingue e supera molti modelli di chat open source e chiusi in vari benchmark di settore. La data di scadenza delle conoscenze è dicembre 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct è il modello più grande e potente della serie Llama 3.1 Instruct, un modello avanzato per la generazione di dati e il ragionamento conversazionale, utilizzabile anche come base per un pre-addestramento o un fine-tuning specializzato in determinati settori. I modelli di linguaggio di grandi dimensioni (LLMs) multilingue forniti da Llama 3.1 sono un insieme di modelli generativi pre-addestrati e ottimizzati per le istruzioni, che includono dimensioni di 8B, 70B e 405B (input/output di testo). I modelli di testo ottimizzati per le istruzioni di Llama 3.1 (8B, 70B, 405B) sono stati progettati per casi d'uso conversazionali multilingue e hanno superato molti modelli di chat open source disponibili in benchmark di settore comuni. Llama 3.1 è progettato per usi commerciali e di ricerca in diverse lingue. I modelli di testo ottimizzati per le istruzioni sono adatti a chat simili a assistenti, mentre i modelli pre-addestrati possono adattarsi a vari compiti di generazione di linguaggio naturale. I modelli Llama 3.1 supportano anche l'uso della loro output per migliorare altri modelli, inclusa la generazione di dati sintetici e il raffinamento. Llama 3.1 è un modello di linguaggio autoregressivo basato su un'architettura di trasformatore ottimizzata. Le versioni ottimizzate utilizzano il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF) per allinearsi alle preferenze umane in termini di utilità e sicurezza."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Meta Llama 3.1 70B Instruct è una versione aggiornata, con una lunghezza di contesto estesa a 128K, multilinguismo e capacità di ragionamento migliorate. I modelli di linguaggio di grandi dimensioni (LLMs) forniti da Llama 3.1 sono un insieme di modelli generativi pre-addestrati e regolati per istruzioni, che includono dimensioni di 8B, 70B e 405B (input/output testuali). I modelli di testo regolati per istruzioni di Llama 3.1 (8B, 70B, 405B) sono ottimizzati per casi d'uso di conversazione multilingue e superano molti modelli di chat open source disponibili nei test di benchmark di settore comuni. Llama 3.1 è progettato per usi commerciali e di ricerca in più lingue. I modelli di testo regolati per istruzioni sono adatti per chat simili a quelle di un assistente, mentre i modelli pre-addestrati possono adattarsi a vari compiti di generazione di linguaggio naturale. I modelli Llama 3.1 supportano anche l'uso della loro output per migliorare altri modelli, inclusa la generazione di dati sintetici e il raffinamento. Llama 3.1 è un modello di linguaggio autoregressivo basato su un'architettura di trasformatore ottimizzata. Le versioni regolate utilizzano il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF) per allinearsi alle preferenze umane per utilità e sicurezza."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Meta Llama 3.1 8B Instruct è una versione aggiornata, con una lunghezza di contesto estesa a 128K, multilinguismo e capacità di ragionamento migliorate. I modelli di linguaggio di grandi dimensioni (LLMs) forniti da Llama 3.1 sono un insieme di modelli generativi pre-addestrati e regolati per istruzioni, che includono dimensioni di 8B, 70B e 405B (input/output testuali). I modelli di testo regolati per istruzioni di Llama 3.1 (8B, 70B, 405B) sono ottimizzati per casi d'uso di conversazione multilingue e superano molti modelli di chat open source disponibili nei test di benchmark di settore comuni. Llama 3.1 è progettato per usi commerciali e di ricerca in più lingue. I modelli di testo regolati per istruzioni sono adatti per chat simili a quelle di un assistente, mentre i modelli pre-addestrati possono adattarsi a vari compiti di generazione di linguaggio naturale. I modelli Llama 3.1 supportano anche l'uso della loro output per migliorare altri modelli, inclusa la generazione di dati sintetici e il raffinamento. Llama 3.1 è un modello di linguaggio autoregressivo basato su un'architettura di trasformatore ottimizzata. Le versioni regolate utilizzano il fine-tuning supervisionato (SFT) e l'apprendimento per rinforzo con feedback umano (RLHF) per allinearsi alle preferenze umane per utilità e sicurezza."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 è un modello di linguaggio di grandi dimensioni (LLM) open source progettato per sviluppatori, ricercatori e aziende, per aiutarli a costruire, sperimentare e scalare responsabilmente le loro idee di AI generativa. Come parte di un sistema di base per l'innovazione della comunità globale, è particolarmente adatto per la creazione di contenuti, AI conversazionale, comprensione del linguaggio, ricerca e applicazioni aziendali."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 è un modello di linguaggio di grandi dimensioni (LLM) open source progettato per sviluppatori, ricercatori e aziende, per aiutarli a costruire, sperimentare e scalare responsabilmente le loro idee di AI generativa. Come parte di un sistema di base per l'innovazione della comunità globale, è particolarmente adatto per dispositivi a bassa potenza e risorse limitate, oltre a garantire tempi di addestramento più rapidi."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Eccelle nelle capacità di ragionamento su immagini ad alta risoluzione, adatto ad applicazioni di comprensione visiva."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Capacità avanzate di ragionamento su immagini per applicazioni di agenti di comprensione visiva."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 è il modello linguistico open source multilingue più avanzato della serie Llama, che offre prestazioni paragonabili a un modello da 405 miliardi di parametri a costi estremamente contenuti. Basato su architettura Transformer, migliorato tramite fine-tuning supervisionato (SFT) e apprendimento rinforzato con feedback umano (RLHF) per utilità e sicurezza. La versione ottimizzata per istruzioni è progettata per dialoghi multilingue e supera molti modelli di chat open source e proprietari in diversi benchmark industriali. Data di cut-off della conoscenza: dicembre 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Un potente modello da 70 miliardi di parametri, eccellente in ragionamento, codifica e ampie applicazioni linguistiche."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Un modello versatile da 8 miliardi di parametri, ottimizzato per compiti di dialogo e generazione di testo."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Modello testuale Llama 3.1 ottimizzato per istruzioni, progettato per casi d'uso di dialogo multilingue, con prestazioni eccellenti in molti benchmark industriali rispetto a numerosi modelli di chat open source e proprietari."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Modello testuale Llama 3.1 ottimizzato per istruzioni, progettato per casi d'uso di dialogo multilingue, con prestazioni eccellenti in molti benchmark industriali rispetto a numerosi modelli di chat open source e proprietari."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Modello testuale Llama 3.1 ottimizzato per istruzioni, progettato per casi d'uso di dialogo multilingue, con prestazioni eccellenti in molti benchmark industriali rispetto a numerosi modelli di chat open source e proprietari."}, "meta/llama-3.1-405b-instruct": {"description": "LLM avanzato, supporta la generazione di dati sintetici, la distillazione della conoscenza e il ragionamento, adatto per chatbot, programmazione e compiti specifici."}, "meta/llama-3.1-70b-instruct": {"description": "Abilita conversazioni complesse, con eccellenti capacità di comprensione del contesto, ragionamento e generazione di testo."}, "meta/llama-3.1-8b-instruct": {"description": "<PERSON><PERSON> all'avanguardia, dotato di comprensione del linguaggio, eccellenti capacità di ragionamento e generazione di testo."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Modello visivo-linguistico all'avanguardia, specializzato nel ragionamento di alta qualità a partire dalle immagini."}, "meta/llama-3.2-1b-instruct": {"description": "Modello linguistico all'avanguardia di piccole dimensioni, dotato di comprensione del linguaggio, eccellenti capacità di ragionamento e generazione di testo."}, "meta/llama-3.2-3b-instruct": {"description": "Modello linguistico all'avanguardia di piccole dimensioni, dotato di comprensione del linguaggio, eccellenti capacità di ragionamento e generazione di testo."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Modello visivo-linguistico all'avanguardia, specializzato nel ragionamento di alta qualità a partire dalle immagini."}, "meta/llama-3.3-70b-instruct": {"description": "LLM avanzato, special<PERSON><PERSON><PERSON> in ragionamento, matematica, conoscenze generali e chiamate di funzione."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "<PERSON><PERSON><PERSON> modello Phi-3-medium, ma con una dimensione del contesto maggiore, adatto per RAG o pochi prompt."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Un modello da 14 miliardi di parametri, con qualità superiore a Phi-3-mini, focalizzato su dati di alta qualità e intensivi di ragionamento."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Stesso modello Phi-3-mini, ma con una dimensione del contesto maggiore, adatto per RAG o pochi prompt."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Il membro più piccolo della famiglia Phi-3, o<PERSON><PERSON><PERSON><PERSON> per qualità e bassa latenza."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Stesso modello Phi-3-small, ma con una dimensione del contesto maggiore, adatto per RAG o pochi prompt."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Un modello da 7 miliardi di parametri, con qualità superiore a Phi-3-mini, focalizzato su dati di alta qualità e intensivi di ragionamento."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Versione aggiornata del modello Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Versione aggiornata del modello Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 è un modello linguistico fornito da Microsoft AI, particolarmente efficace in conversazioni complesse, multilingue, ragionamento e assistenti intelligenti."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B è il modello Wizard più avanzato di Microsoft AI, mostrando prestazioni estremamente competitive."}, "minicpm-v": {"description": "MiniCPM-V è la nuova generazione di modelli multimodali lanciata da OpenBMB, dotata di eccellenti capacità di riconoscimento OCR e comprensione multimodale, supportando una vasta gamma di scenari applicativi."}, "ministral-3b-latest": {"description": "Ministral 3B è il modello di punta di Mistral per edge computing."}, "ministral-8b-latest": {"description": "Ministral 8B è un modello edge ad alto rapporto qualità-prezzo di Mistral."}, "mistral": {"description": "Mistral è un modello da 7B lanciato da Mistral AI, adatto per esigenze di elaborazione linguistica variabili."}, "mistral-ai/Mistral-Large-2411": {"description": "Il modello di punta di Mistral, ideale per compiti complessi che richiedono capacità di ragionamento su larga scala o alta specializzazione (generazione di testo sintetico, generazione di codice, RAG o agenti)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo è un modello linguistico all'avanguardia (LLM) che offre capacità di ragionamento, conoscenza del mondo e codifica tra le migliori nella sua categoria di dimensioni."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small è adatto a qualsiasi compito basato sul linguaggio che richieda alta efficienza e bassa latenza."}, "mistral-large": {"description": "Mixtral Large è il modello di punta di Mistral, combinando capacità di generazione di codice, matematica e ragionamento, supporta una finestra di contesto di 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 è un avanzato modello linguistico denso e di grandi dimensioni (LLM), con 123 miliardi di parametri, che dispone delle capacità di inferenza, conoscenza e codifica più avanzate."}, "mistral-large-latest": {"description": "Mistral Large è il modello di punta, specializzato in compiti multilingue, ragionamento complesso e generazione di codice, è la scelta ideale per applicazioni di alta gamma."}, "mistral-medium-latest": {"description": "Mistral Medium 3 offre prestazioni all'avanguardia a un costo otto volte inferiore, semplificando radicalmente il deployment aziendale."}, "mistral-nemo": {"description": "Mistral Nemo è un modello da 12B lanciato in collaborazione tra Mistral AI e NVIDIA, offre prestazioni eccellenti."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407, un grande modello linguistico (LLM), è una versione finetunata con istruzioni di Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small può essere utilizzato in qualsiasi compito basato su linguaggio che richiede alta efficienza e bassa latenza."}, "mistral-small-latest": {"description": "Mistral Small è un'opzione economica, veloce e affidabile, adatta per casi d'uso come traduzione, sintesi e analisi del sentiment."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct è noto per le sue alte prestazioni, adatto per vari compiti linguistici."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B è un modello fine-tuned su richiesta, fornendo risposte ottimizzate per i compiti."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 offre capacità computazionali efficienti e comprensione del linguaggio naturale, adatta per una vasta gamma di applicazioni."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B è un modello compatto ma ad alte prestazioni, specializzato nell'elaborazione batch e in compiti semplici come classificazione e generazione di testo, con buone capacità di ragionamento."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) è un super modello di linguaggio, supportando esigenze di elaborazione estremamente elevate."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B è un modello di esperti misti pre-addestrato, utilizzato per compiti testuali generali."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B è un modello di esperti sparsi che utilizza più parametri per migliorare la velocità di ragionamento, adatto a compiti di generazione multilingue e di codice."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct è un modello standard di settore ad alte prestazioni, ottimizzato per velocità e supporto di contesti lunghi."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo è un modello con 7.3B parametri, supporta più lingue e offre prestazioni elevate nella programmazione."}, "mixtral": {"description": "Mixtral è il modello di esperti di Mistral AI, con pesi open source, offre supporto per la generazione di codice e la comprensione del linguaggio."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B offre capacità di calcolo parallelo ad alta tolleranza, adatto per compiti complessi."}, "mixtral:8x22b": {"description": "Mixtral è il modello di esperti di Mistral AI, con pesi open source, offre supporto per la generazione di codice e la comprensione del linguaggio."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K è un modello con capacità di elaborazione di contesti ultra lunghi, adatto per generare testi molto lunghi, soddisfacendo le esigenze di compiti complessi, in grado di gestire contenuti fino a 128.000 token, particolarmente adatto per applicazioni di ricerca, accademiche e generazione di documenti di grandi dimensioni."}, "moonshot-v1-128k-vision-preview": {"description": "Il modello visivo Kimi (inclusi moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, ecc.) è in grado di comprendere il contenuto delle immagini, inclusi testo, colori e forme degli oggetti."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K offre capacità di elaborazione di contesti di lunghezza media, in grado di gestire 32.768 token, particolarmente adatto per generare vari documenti lunghi e dialoghi complessi, utilizzato in creazione di contenuti, generazione di report e sistemi di dialogo."}, "moonshot-v1-32k-vision-preview": {"description": "Il modello visivo Kimi (inclusi moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, ecc.) è in grado di comprendere il contenuto delle immagini, inclusi testo, colori e forme degli oggetti."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K è progettato per generare compiti di testo brevi, con prestazioni di elaborazione efficienti, in grado di gestire 8.192 token, particolarmente adatto per dialoghi brevi, appunti e generazione rapida di contenuti."}, "moonshot-v1-8k-vision-preview": {"description": "Il modello visivo Kimi (inclusi moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, ecc.) è in grado di comprendere il contenuto delle immagini, inclusi testo, colori e forme degli oggetti."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto può selezionare il modello appropriato in base al numero di token utilizzati nel contesto attuale."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B è un modello open source di grandi dimensioni per il codice, ottimizzato tramite apprendimento rinforzato su larga scala, capace di generare patch robuste e pronte per la produzione. Questo modello ha raggiunto un nuovo record del 60,4% su SWE-bench Verified, superando tutti i modelli open source nelle attività di ingegneria del software automatizzata come la correzione di difetti e la revisione del codice."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 è un modello base con architettura MoE dotato di potenti capacità di codice e agenti, con 1 trilione di parametri totali e 32 miliardi di parametri attivi. Nei test di benchmark su ragionamento generale, programmazione, matematica e agenti, il modello K2 supera altri modelli open source principali."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 è un modello di base con architettura MoE dotato di potenti capacità di codice e agenti, con un totale di 1T parametri e 32B parametri attivi. Nei test di benchmark per categorie principali come ragionamento generale, programmazione, matematica e agenti, il modello K2 supera le altre principali soluzioni open source."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B è una versione aggiornata di Nous Hermes 2, contenente i più recenti dataset sviluppati internamente."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B è un modello linguistico di grandi dimensioni personalizzato da NVIDIA, progettato per migliorare l'utilità delle risposte generate dai LLM alle domande degli utenti. Questo modello ha ottenuto risultati eccellenti nei benchmark come Arena Hard, AlpacaEval 2 LC e GPT-4-Turbo MT-Bench, classificandosi al primo posto in tutti e tre i benchmark di allineamento automatico fino al 1 ottobre 2024. Il modello è stato addestrato utilizzando RLHF (in particolare REINFORCE), Llama-3.1-Nemotron-70B-Reward e HelpSteer2-Preference come suggerimenti, basandosi sul modello Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Modello linguistico unico, offre prestazioni di accuratezza ed efficienza senza pari."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct è un modello linguistico di grandi dimensioni personalizzato da NVIDIA, progettato per migliorare l'utilità delle risposte generate da LLM."}, "o1": {"description": "Focalizzato su inferenze avanzate e risoluzione di problemi complessi, inclusi compiti matematici e scientifici. È particolarmente adatto per applicazioni che richiedono una comprensione profonda del contesto e flussi di lavoro agenti."}, "o1-mini": {"description": "o1-mini è un modello di inferenza rapido ed economico progettato per applicazioni di programmazione, matematica e scienza. Questo modello ha un contesto di 128K e una data di cutoff della conoscenza di ottobre 2023."}, "o1-preview": {"description": "o1 è il nuovo modello di inferenza di OpenAI, adatto a compiti complessi che richiedono una vasta conoscenza generale. Questo modello ha un contesto di 128K e una data di cutoff della conoscenza di ottobre 2023."}, "o1-pro": {"description": "La serie di modelli o1 è stata addestrata con apprendimento rinforzato, in grado di riflettere prima di rispondere ed eseguire compiti di ragionamento complessi. Il modello o1-pro utilizza più risorse computazionali per un pensiero più approfondito, offrendo risposte di qualità superiore in modo continuo."}, "o3": {"description": "o3 è un modello versatile e potente, che si distingue in vari campi. Stabilisce nuovi standard per compiti di matematica, scienza, programmazione e ragionamento visivo. È anche abile nella scrittura tecnica e nel seguire istruzioni. Gli utenti possono utilizzarlo per analizzare testi, codici e immagini, risolvendo problemi complessi in più passaggi."}, "o3-deep-research": {"description": "o3-deep-research è il nostro modello di ricerca approfondita più avanzato, progettato specificamente per gestire compiti di ricerca complessi e articolati. Può cercare e sintetizzare informazioni da Internet, oltre a poter accedere e utilizzare i tuoi dati tramite il connettore MCP."}, "o3-mini": {"description": "o3-mini è il nostro ultimo modello di inferenza compatto, che offre un'intelligenza elevata con gli stessi obiettivi di costo e latenza di o1-mini."}, "o3-pro": {"description": "Il modello o3-pro utilizza maggiori risorse computazionali per un pensiero più profondo e fornisce sempre risposte migliori, supportato solo tramite l'API Responses."}, "o4-mini": {"description": "o4-mini è il nostro ultimo modello della serie o in formato ridotto. È ottimizzato per un'inferenza rapida ed efficace, mostrando un'elevata efficienza e prestazioni in compiti di codifica e visivi."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research è il nostro modello di ricerca approfondita più rapido ed economico, ideale per gestire compiti di ricerca complessi e articolati. Può cercare e sintetizzare informazioni da Internet, oltre a poter accedere e utilizzare i tuoi dati tramite il connettore MCP."}, "open-codestral-mamba": {"description": "Codestral Mamba è un modello linguistico Mamba 2 focalizzato sulla generazione di codice, offre un forte supporto per compiti avanzati di codifica e ragionamento."}, "open-mistral-7b": {"description": "Mistral 7B è un modello compatto ma ad alte prestazioni, specializzato nell'elaborazione batch e in compiti semplici, come la classificazione e la generazione di testo, con buone capacità di ragionamento."}, "open-mistral-nemo": {"description": "Mistral Nemo è un modello da 12B sviluppato in collaborazione con Nvidia, offre prestazioni di ragionamento e codifica eccezionali, facile da integrare e sostituire."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B è un modello di esperti più grande, focalizzato su compiti complessi, offre eccellenti capacità di ragionamento e una maggiore capacità di elaborazione."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B è un modello di esperti sparsi, che utilizza più parametri per aumentare la velocità di ragionamento, adatto per gestire compiti di generazione di linguaggio e codice multilingue."}, "openai/gpt-4.1": {"description": "GPT-4.1 è il nostro modello di punta per compiti complessi. È particolarmente adatto per risolvere problemi trasversali."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini offre un equilibrio tra intelligenza, velocità e costo, rendendolo un modello attraente per molti casi d'uso."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano è il modello GPT-4.1 più veloce e conveniente."}, "openai/gpt-4o": {"description": "ChatGPT-4o è un modello dinamico, aggiornato in tempo reale per mantenere la versione più recente. Combina potenti capacità di comprensione e generazione del linguaggio, adatto a scenari di applicazione su larga scala, tra cui assistenza clienti, istruzione e supporto tecnico."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini è il modello più recente di OpenAI, lanciato dopo GPT-4 Omni, che supporta input visivi e testuali e produce output testuali. Come il loro modello di piccole dimensioni più avanzato, è molto più economico rispetto ad altri modelli all'avanguardia recenti e costa oltre il 60% in meno rispetto a GPT-3.5 Turbo. Mantiene un'intelligenza all'avanguardia, offrendo un notevole rapporto qualità-prezzo. GPT-4o mini ha ottenuto un punteggio dell'82% nel test MMLU e attualmente è classificato più in alto di GPT-4 per preferenze di chat."}, "openai/o1": {"description": "o1 è il nuovo modello di ragionamento di OpenAI, supporta input di testo e immagini e produce output testuali, adatto a compiti complessi che richiedono una vasta conoscenza generale. Il modello ha un contesto di 200K token e una data di cut-off della conoscenza a ottobre 2023."}, "openai/o1-mini": {"description": "o1-mini è un modello di inferenza rapido ed economico progettato per applicazioni di programmazione, matematica e scienza. Questo modello ha un contesto di 128K e una data di cutoff della conoscenza di ottobre 2023."}, "openai/o1-preview": {"description": "o1 è il nuovo modello di inferenza di OpenAI, adatto a compiti complessi che richiedono una vasta conoscenza generale. Questo modello ha un contesto di 128K e una data di cutoff della conoscenza di ottobre 2023."}, "openai/o3": {"description": "o3 è un modello potente e versatile, che si distingue in diversi ambiti. Stabilisce nuovi standard per compiti di matematica, scienza, programmazione e ragionamento visivo. È anche abile nella scrittura tecnica e nel seguire istruzioni. Gli utenti possono utilizzarlo per analizzare testi, codici e immagini, risolvendo problemi complessi in più passaggi."}, "openai/o3-mini": {"description": "o3-mini offre alta intelligenza mantenendo gli stessi obiettivi di costo e latenza di o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini alta versione di ragionamento, offre alta intelligenza mantenendo gli stessi obiettivi di costo e latenza di o1-mini."}, "openai/o4-mini": {"description": "o4-mini è ottimizzato per un'inferenza rapida ed efficace, mostrando un'elevata efficienza e prestazioni in compiti di codifica e visivi."}, "openai/o4-mini-high": {"description": "Versione ad alta capacità di inferenza di o4-mini, ottimizzata per un'inferenza rapida ed efficace, mostrando un'elevata efficienza e prestazioni in compiti di codifica e visivi."}, "openrouter/auto": {"description": "In base alla lunghezza del contesto, al tema e alla complessità, la tua richiesta verrà inviata a Llama 3 70B Instruct, Claude 3.5 Sonnet (auto-regolato) o GPT-4o."}, "phi3": {"description": "Phi-3 è un modello open source leggero lanciato da Microsoft, adatto per integrazioni efficienti e ragionamento su larga scala."}, "phi3:14b": {"description": "Phi-3 è un modello open source leggero lanciato da Microsoft, adatto per integrazioni efficienti e ragionamento su larga scala."}, "pixtral-12b-2409": {"description": "Il modello Pixtral dimostra potenti capacità in compiti di comprensione di grafici e immagini, domande e risposte su documenti, ragionamento multimodale e rispetto delle istruzioni, in grado di elaborare immagini a risoluzione naturale e proporzioni, e di gestire un numero arbitrario di immagini in una finestra di contesto lunga fino a 128K token."}, "pixtral-large-latest": {"description": "Pixtral Large è un modello multimodale open source con 124 miliardi di parametri, costruito su Mistral Large 2. Questo è il nostro secondo modello nella famiglia multimodale, che mostra capacità di comprensione delle immagini a livello all'avanguardia."}, "pro-128k": {"description": "Spark Pro 128K è dotato di una capacità di elaborazione del contesto eccezionale, in grado di gestire fino a 128K di informazioni contestuali, particolarmente adatto per l'analisi completa e la gestione di associazioni logiche a lungo termine in contenuti lunghi, fornendo una logica fluida e coerente e un supporto variegato per le citazioni in comunicazioni testuali complesse."}, "qvq-72b-preview": {"description": "Il modello QVQ è un modello di ricerca sperimentale sviluppato dal team Qwen, focalizzato sul miglioramento delle capacità di ragionamento visivo, in particolare nel campo del ragionamento matematico."}, "qvq-max": {"description": "Modello di ragionamento visivo QVQ di Tongyi Qianwen, supporta input visivi e output di catene di pensieri, mostrando capacità superiori in matematica, programmazione, analisi visiva, creazione e compiti generali."}, "qvq-plus": {"description": "Modello di ragionamento visivo. Supporta input visivi e output a catena di pensiero. Versione plus lanciata dopo il modello qvq-max, con velocità di ragionamento più elevata e un equilibrio migliore tra prestazioni ed efficienza rispetto a qvq-max."}, "qwen-coder-plus": {"description": "<PERSON><PERSON> di codice <PERSON>."}, "qwen-coder-turbo": {"description": "<PERSON><PERSON> di codice <PERSON>."}, "qwen-coder-turbo-latest": {"description": "<PERSON><PERSON> di codice <PERSON>."}, "qwen-long": {"description": "Qwen è un modello di linguaggio su larga scala che supporta contesti di testo lunghi e funzionalità di dialogo basate su documenti lunghi e multipli."}, "qwen-math-plus": {"description": "<PERSON>lo matematico <PERSON>wen specializzato nella risoluzione di problemi matematici."}, "qwen-math-plus-latest": {"description": "Il modello matematico <PERSON>yi Qwen è progettato specificamente per la risoluzione di problemi matematici."}, "qwen-math-turbo": {"description": "<PERSON>lo matematico <PERSON>wen specializzato nella risoluzione di problemi matematici."}, "qwen-math-turbo-latest": {"description": "Il modello matematico <PERSON>yi Qwen è progettato specificamente per la risoluzione di problemi matematici."}, "qwen-max": {"description": "<PERSON>wen Max è un modello linguistico di grandi dimensioni con trilioni di parametri, supporta input in diverse lingue, tra cui cinese e inglese e attualmente è il modello API dietro la versione 2.5 di Qwen."}, "qwen-omni-turbo": {"description": "La serie di modelli Qwen-Omni supporta input multimodali, inclusi video, audio, immagini e testo, e produce output audio e testuale."}, "qwen-plus": {"description": "Qwen Plus è una versione potenziata del modello linguistico di grandi dimensioni, che supporta input in diverse lingue, tra cui cinese e inglese."}, "qwen-turbo": {"description": "Qwen è un modello linguistico di grandi dimensioni che supporta input in diverse lingue, tra cui cinese e inglese."}, "qwen-vl-chat-v1": {"description": "Qwen VL supporta modalità di interazione flessibili, inclusi modelli di domande e risposte multipli e creativi."}, "qwen-vl-max": {"description": "Modello multimodale di grandissima scala Tongyi Qianwen. Rispetto alla versione potenziata, migliora ulteriormente le capacità di ragionamento visivo e l’aderenza alle istruzioni, offrendo un livello superiore di percezione e cognizione visiva."}, "qwen-vl-max-latest": {"description": "Modello di linguaggio visivo Qwen di grande scala. Rispetto alla versione potenziata, migliora ulteriormente la capacità di ragionamento visivo e di aderenza alle istruzioni, offrendo un livello superiore di percezione visiva e cognizione."}, "qwen-vl-ocr": {"description": "Tongyi Qianwen OCR è un modello specializzato nell’estrazione di testo, focalizzato su immagini di documenti, tabelle, esercizi e scrittura a mano. Riconosce molteplici lingue, tra cui cinese, inglese, francese, gia<PERSON><PERSON>e, coreano, tedesco, russo, italiano, vietnamita e arabo."}, "qwen-vl-plus": {"description": "Versione potenziata del modello multimodale di grande scala Tongyi Qianwen. Migliora notevolmente la capacità di riconoscimento dei dettagli e del testo, supportando immagini con risoluzione superiore a un milione di pixel e proporzioni di dimensioni arbitrarie."}, "qwen-vl-plus-latest": {"description": "Versione potenziata del modello di linguaggio visivo Qwen. Migliora notevolmente la capacità di riconoscimento dei dettagli e di riconoscimento del testo, supportando risoluzioni superiori a un milione di pixel e immagini di qualsiasi rapporto di aspetto."}, "qwen-vl-v1": {"description": "Inizializzato con il modello di linguaggio Qwen-7B, aggiunge un modello di immagine, con una risoluzione di input dell'immagine di 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 è la nuova serie di modelli di linguaggio Qwen. Qwen2 7B è un modello basato su transformer, che mostra prestazioni eccezionali nella comprensione del linguaggio, nelle capacità multilingue, nella programmazione, nella matematica e nel ragionamento."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 è una nuova serie di modelli di linguaggio di grandi dimensioni, con capacità di comprensione e generazione più forti."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL è l'ultima iterazione del modello Qwen-VL, raggiungendo prestazioni all'avanguardia nei benchmark di comprensione visiva, inclusi MathVista, DocVQA, RealWorldQA e MTVQA. Qwen2-VL è in grado di comprendere video di oltre 20 minuti, per domande e risposte, dialoghi e creazione di contenuti di alta qualità basati su video. Ha anche capacità di ragionamento e decisione complesse, che possono essere integrate con dispositivi mobili, robot e altro, per operazioni automatiche basate su ambienti visivi e istruzioni testuali. Oltre all'inglese e al cinese, Qwen2-VL ora supporta anche la comprensione di testi in diverse lingue all'interno delle immagini, comprese la maggior parte delle lingue europee, giapponese, coreano, arabo e vietnamita."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct è uno dei più recenti modelli di linguaggio rilasciati da Alibaba Cloud. Questo modello da 72B ha capacità notevolmente migliorate in campi come la codifica e la matematica. Il modello offre anche supporto multilingue, coprendo oltre 29 lingue, tra cui cinese e inglese. Ha mostrato miglioramenti significativi nel seguire istruzioni, comprendere dati strutturati e generare output strutturati (in particolare JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct è uno dei più recenti modelli di linguaggio rilasciati da Alibaba Cloud. Questo modello da 32B ha capacità notevolmente migliorate in campi come la codifica e la matematica. Il modello offre anche supporto multilingue, coprendo oltre 29 lingue, tra cui cinese e inglese. Ha mostrato miglioramenti significativi nel seguire istruzioni, comprendere dati strutturati e generare output strutturati (in particolare JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM orientato al cinese e all'inglese, focalizzato su linguaggio, programmazione, matematica, ragionamento e altro."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "LLM avanzato, supporta la generazione di codice, il ragionamento e la correzione, coprendo i linguaggi di programmazione più diffusi."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Potente modello di codice di medie dimensioni, supporta una lunghezza di contesto di 32K, specializzato in programmazione multilingue."}, "qwen/qwen3-14b": {"description": "Qwen3-14B è un modello linguistico causale denso con 14,8 miliardi di parametri della serie Qwen3, progettato per ragionamenti complessi e dialoghi efficienti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per compiti di matematica, programmazione e ragionamento logico e modalità 'non di pensiero' per dialoghi generali. Questo modello è stato ottimizzato per seguire istruzioni, utilizzo di strumenti per agenti, scrittura creativa e compiti multilingue in oltre 100 lingue e dialetti. Gestisce nativamente un contesto di 32K token e può essere esteso a 131K token utilizzando estensioni basate su YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B è un modello linguistico causale denso con 14,8 miliardi di parametri della serie Qwen3, progettato per ragionamenti complessi e dialoghi efficienti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per compiti di matematica, programmazione e ragionamento logico e modalità 'non di pensiero' per dialoghi generali. Questo modello è stato ottimizzato per seguire istruzioni, utilizzo di strumenti per agenti, scrittura creativa e compiti multilingue in oltre 100 lingue e dialetti. Gestisce nativamente un contesto di 32K token e può essere esteso a 131K token utilizzando estensioni basate su YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B è un modello esperto a miscelazione (MoE) con 235 miliardi di parametri sviluppato da <PERSON>, attivando 22 miliardi di parametri ad ogni passaggio in avanti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per ragionamenti complessi, matematica e compiti di codifica e modalità 'non di pensiero' per dialoghi generali. Questo modello dimostra forti capacità di ragionamento, supporto multilingue (in oltre 100 lingue e dialetti), avanzate capacità di seguire istruzioni e chiamate a strumenti per agenti. Gestisce nativamente una finestra di contesto di 32K token e può essere esteso a 131K token utilizzando estensioni basate su YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B è un modello esperto a miscelazione (MoE) con 235 miliardi di parametri sviluppato da <PERSON>, attivando 22 miliardi di parametri ad ogni passaggio in avanti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per ragionamenti complessi, matematica e compiti di codifica e modalità 'non di pensiero' per dialoghi generali. Questo modello dimostra forti capacità di ragionamento, supporto multilingue (in oltre 100 lingue e dialetti), avanzate capacità di seguire istruzioni e chiamate a strumenti per agenti. Gestisce nativamente una finestra di contesto di 32K token e può essere esteso a 131K token utilizzando estensioni basate su YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 è l'ultima generazione della serie di modelli linguistici Qwen, con un'architettura a miscelazione esperta (MoE) densa, che eccelle in inferenza, supporto multilingue e compiti avanzati. La sua capacità unica di passare senza soluzione di continuità tra modalità di pensiero per il ragionamento complesso e modalità non di pensiero per dialoghi efficienti garantisce prestazioni multifunzionali e di alta qualità.\n\nQwen3 supera significativamente i modelli precedenti come QwQ e Qwen2.5, offrendo prestazioni eccezionali in matematica, codifica, ragionamento di buon senso, scrittura creativa e dialoghi interattivi. La variante Qwen3-30B-A3B contiene 30,5 miliardi di parametri (3,3 miliardi di parametri attivati), 48 strati, 128 esperti (8 attivati per compito) e supporta un contesto di fino a 131K token (utilizzando YaRN), stabilendo un nuovo standard per i modelli open source."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 è l'ultima generazione della serie di modelli linguistici Qwen, con un'architettura a miscelazione esperta (MoE) densa, che eccelle in inferenza, supporto multilingue e compiti avanzati. La sua capacità unica di passare senza soluzione di continuità tra modalità di pensiero per il ragionamento complesso e modalità non di pensiero per dialoghi efficienti garantisce prestazioni multifunzionali e di alta qualità.\n\nQwen3 supera significativamente i modelli precedenti come QwQ e Qwen2.5, offrendo prestazioni eccezionali in matematica, codifica, ragionamento di buon senso, scrittura creativa e dialoghi interattivi. La variante Qwen3-30B-A3B contiene 30,5 miliardi di parametri (3,3 miliardi di parametri attivati), 48 strati, 128 esperti (8 attivati per compito) e supporta un contesto di fino a 131K token (utilizzando YaRN), stabilendo un nuovo standard per i modelli open source."}, "qwen/qwen3-32b": {"description": "Qwen3-32B è un modello linguistico causale denso con 32,8 miliardi di parametri della serie Qwen3, ottimizzato per ragionamenti complessi e dialoghi efficienti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per compiti di matematica, codifica e ragionamento logico e modalità 'non di pensiero' per dialoghi più rapidi e generali. Questo modello mostra prestazioni robuste in seguire istruzioni, utilizzo di strumenti per agenti, scrittura creativa e compiti multilingue in oltre 100 lingue e dialetti. Gestisce nativamente un contesto di 32K token e può essere esteso a 131K token utilizzando estensioni basate su YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B è un modello linguistico causale denso con 32,8 miliardi di parametri della serie Qwen3, ottimizzato per ragionamenti complessi e dialoghi efficienti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per compiti di matematica, codifica e ragionamento logico e modalità 'non di pensiero' per dialoghi più rapidi e generali. Questo modello mostra prestazioni robuste in seguire istruzioni, utilizzo di strumenti per agenti, scrittura creativa e compiti multilingue in oltre 100 lingue e dialetti. Gestisce nativamente un contesto di 32K token e può essere esteso a 131K token utilizzando estensioni basate su YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B è un modello linguistico causale denso con 8,2 miliardi di parametri della serie Qwen3, progettato per compiti intensivi di inferenza e dialoghi efficienti. Supporta un passaggio senza soluzione di continuità tra modalità di 'pensiero' per matematica, codifica e ragionamento logico e modalità 'non di pensiero' per dialoghi generali. Questo modello è stato ottimizzato per seguire istruzioni, integrazione di agenti, scrittura creativa e utilizzo multilingue in oltre 100 lingue e dialetti. Supporta nativamente una finestra di contesto di 32K token e può essere esteso a 131K token tramite YaRN."}, "qwen2": {"description": "Qwen2 è la nuova generazione di modelli di linguaggio su larga scala di Alibaba, supporta prestazioni eccellenti per esigenze applicative diversificate."}, "qwen2-72b-instruct": {"description": "Qwen2 è la nuova serie di modelli linguistici di grande dimensione sviluppata dal team Qwen. Si basa sull'architettura Transformer e utilizza funzioni di attivazione SwiGLU, bias QKV dell'attenzione, attenzione a query di gruppo, una combinazione di attenzione a finestra scorrevole e attenzione completa. Inoltre, il team Qwen ha migliorato il tokenizzatore per adattarlo a diverse lingue naturali e codici."}, "qwen2-7b-instruct": {"description": "Qwen2 è la nuova serie di modelli linguistici di grandi dimensioni presentata dal team Qwen. Si basa sull'architettura Transformer e utilizza funzioni di attivazione SwiGLU, bias QKV dell'attenzione (attention QKV bias), attenzione a query di gruppo (group query attention), una combinazione di attenzione a finestra scorrevole (sliding window attention) e attenzione completa. Inoltre, il team Qwen ha migliorato il tokenizzatore per adattarlo a diverse lingue naturali e codici."}, "qwen2.5": {"description": "Qwen2.5 è la nuova generazione di modelli linguistici su larga scala di Alibaba, che supporta esigenze applicative diversificate con prestazioni eccellenti."}, "qwen2.5-14b-instruct": {"description": "Modello da 14B di Tongyi Qwen 2.5, open source."}, "qwen2.5-14b-instruct-1m": {"description": "Il modello da 72B di Qwen2.5 è open source."}, "qwen2.5-32b-instruct": {"description": "Modello da 32B di Tongyi Qwen 2.5, open source."}, "qwen2.5-72b-instruct": {"description": "<PERSON>lo da 72B <PERSON> Tongyi Qwen 2.5, open source."}, "qwen2.5-7b-instruct": {"description": "Modello da 7B di Tongyi Qwen 2.5, open source."}, "qwen2.5-coder-1.5b-instruct": {"description": "Versione open-source del modello di codice Qwen."}, "qwen2.5-coder-14b-instruct": {"description": "Versione open source del modello di codice <PERSON>."}, "qwen2.5-coder-32b-instruct": {"description": "Versione open source del modello di codice Qwen di <PERSON>."}, "qwen2.5-coder-7b-instruct": {"description": "Versione open source del modello di codice <PERSON>."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder è il modello linguistico di grandi dimensioni più recente della serie Qwen, dedicato specificamente al codice (precedentemente noto come CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 è la serie più recente del modello linguistico Qwen. Per Qwen2.5, abbiamo rilasciato diversi modelli linguistici di base e modelli linguistici finetunati con istruzioni, con un intervallo di parametri da 500 milioni a 7,2 miliardi."}, "qwen2.5-math-1.5b-instruct": {"description": "Il modello Qwen-Math ha potenti capacità di risoluzione di problemi matematici."}, "qwen2.5-math-72b-instruct": {"description": "Il modello Qwen-Math ha potenti capacità di risoluzione di problemi matematici."}, "qwen2.5-math-7b-instruct": {"description": "Il modello Qwen-Math ha potenti capacità di risoluzione di problemi matematici."}, "qwen2.5-omni-7b": {"description": "La serie di modelli Qwen-Omni supporta l'input di dati multimodali, inclusi video, audio, immagini e testo, e produce output audio e testo."}, "qwen2.5-vl-32b-instruct": {"description": "La serie di modelli Qwen2.5-VL ha migliorato il livello di intelligenza, praticità e applicabilità del modello, rendendolo più performante in scenari come conversazioni naturali, creazione di contenuti, servizi di conoscenza specialistica e sviluppo di codice. La versione 32B utilizza tecniche di apprendimento rinforzato per ottimizzare il modello, offrendo uno stile di output più in linea con le preferenze umane, capacità di ragionamento per problemi matematici complessi e comprensione e ragionamento dettagliati di immagini rispetto ad altri modelli della serie Qwen2.5 VL."}, "qwen2.5-vl-72b-instruct": {"description": "Miglioramento complessivo nella seguire istruzioni, matematica, risoluzione di problemi e codice, con capacità di riconoscimento universale migliorate, supporto per formati diversi per il posizionamento preciso degli elementi visivi, comprensione di file video lunghi (fino a 10 minuti) e localizzazione di eventi in tempo reale, capacità di comprendere sequenze temporali e velocità, supporto per il controllo di agenti OS o Mobile basato su capacità di analisi e localizzazione, forte capacità di estrazione di informazioni chiave e output in formato Json, questa versione è la 72B, la versione più potente della serie."}, "qwen2.5-vl-7b-instruct": {"description": "Miglioramento complessivo nella seguire istruzioni, matematica, risoluzione di problemi e codice, con capacità di riconoscimento universale migliorate, supporto per formati diversi per il posizionamento preciso degli elementi visivi, comprensione di file video lunghi (fino a 10 minuti) e localizzazione di eventi in tempo reale, capacità di comprendere sequenze temporali e velocità, supporto per il controllo di agenti OS o Mobile basato su capacità di analisi e localizzazione, forte capacità di estrazione di informazioni chiave e output in formato Json, questa versione è la 72B, la versione più potente della serie."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL è la versione più recente del modello visivo-linguistico della famiglia Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 è la nuova generazione di modelli linguistici su larga scala di Alibaba, che supporta esigenze applicative diversificate con prestazioni eccellenti."}, "qwen2.5:1.5b": {"description": "Qwen2.5 è la nuova generazione di modelli linguistici su larga scala di Alibaba, che supporta esigenze applicative diversificate con prestazioni eccellenti."}, "qwen2.5:72b": {"description": "Qwen2.5 è la nuova generazione di modelli linguistici su larga scala di Alibaba, che supporta esigenze applicative diversificate con prestazioni eccellenti."}, "qwen2:0.5b": {"description": "Qwen2 è la nuova generazione di modelli di linguaggio su larga scala di Alibaba, supporta prestazioni eccellenti per esigenze applicative diversificate."}, "qwen2:1.5b": {"description": "Qwen2 è la nuova generazione di modelli di linguaggio su larga scala di Alibaba, supporta prestazioni eccellenti per esigenze applicative diversificate."}, "qwen2:72b": {"description": "Qwen2 è la nuova generazione di modelli di linguaggio su larga scala di Alibaba, supporta prestazioni eccellenti per esigenze applicative diversificate."}, "qwen3": {"description": "Qwen3 è la nuova generazione di modelli linguistici su larga scala di Alibaba, che supporta una varietà di esigenze applicative con prestazioni eccellenti."}, "qwen3-0.6b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-1.7b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-14b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-235b-a22b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-235b-a22b-instruct-2507": {"description": "Modello open source non pensante basato su Qwen3, con miglioramenti lievi nella creatività soggettiva e nella sicurezza rispetto alla versione precedente (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Modello open source in modalità pensiero basato su Qwen3, con miglioramenti significativi in logica, capacità generali, potenziamento della conoscenza e creatività rispetto alla versione precedente (Tongyi Qianwen 3-235B-A22B), adatto a scenari di ragionamento complessi e impegnativi."}, "qwen3-30b-a3b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-30b-a3b-instruct-2507": {"description": "Rispetto alla versione precedente (Qwen3-30B-A3B), le capacità generali in cinese, inglese e multilingue sono state notevolmente migliorate. Ottimizzazione specifica per compiti soggettivi e aperti, con un allineamento molto più marcato alle preferenze degli utenti, in grado di fornire risposte più utili."}, "qwen3-30b-a3b-thinking-2507": {"description": "Modello open source in modalità pensante basato su Qwen3, che rispetto alla versione precedente (Tongyi Qianwen 3-30B-A3B) presenta miglioramenti significativi nelle capacità logiche, generali, di conoscenza e creative, adatto a scenari complessi che richiedono un ragionamento avanzato."}, "qwen3-32b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-4b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-8b": {"description": "Qwen3 è un modello di nuova generazione con capacità notevolmente migliorate, rag<PERSON><PERSON><PERSON> livelli leader del settore in inferenza, generazione generale, agenti e multilinguismo, e supporta il passaggio tra modalità di pensiero."}, "qwen3-coder-480b-a35b-instruct": {"description": "Versione open source del modello di codice Tongyi Qianwen. L'ultimo qwen3-coder-480b-a35b-instruct è un modello di generazione codice basato su Qwen3, con potenti capacità di Coding Agent, esperto nell'uso di strumenti e interazione ambientale, capace di programmazione autonoma con eccellenti capacità di codice e capacità generali."}, "qwen3-coder-plus": {"description": "Modello di codice Tongyi Qianwen. L'ultima serie Qwen3-Coder-Plus è un modello di generazione codice basato su Qwen3, con potenti capacità di Coding Agent, esperto nell'uso di strumenti e interazione ambientale, capace di programmazione autonoma con eccellenti capacità di codice e capacità generali."}, "qwq": {"description": "QwQ è un modello di ricerca sperimentale, focalizzato sul miglioramento delle capacità di ragionamento dell'IA."}, "qwq-32b": {"description": "Il modello di inferenza QwQ, addestrato sul modello Qwen2.5-32B, ha notevolmente migliorato le capacità di inferenza del modello attraverso l'apprendimento rinforzato. I principali indicatori core (AIME 24/25, LiveCodeBench) e alcuni indicatori generali (IFEval, LiveBench, ecc.) raggiungono il livello della versione completa di DeepSeek-R1, con tutti gli indicatori che superano significativamente il DeepSeek-R1-Distill-Qwen-32B, anch'esso basato su Qwen2.5-32B."}, "qwq-32b-preview": {"description": "Il modello QwQ è un modello di ricerca sperimentale sviluppato dal team Qwen, focalizzato sul potenziamento delle capacità di ragionamento dell'IA."}, "qwq-plus": {"description": "Modello di ragionamento QwQ basato su Qwen2.5, che ha migliorato significativamente le capacità di ragionamento tramite apprendimento rinforzato. Gli indicatori chiave in matematica e codice (AIME 24/25, LiveCodeBench) e alcuni indicatori generali (IFEval, LiveBench, ecc.) raggiungono il livello completo di DeepSeek-R1."}, "qwq_32b": {"description": "Modello di inferenza di dimensioni medie della serie Qwen. Rispetto ai modelli tradizionali ottimizzati per le istruzioni, QwQ, con le sue capacità di pensiero e ragionamento, può migliorare significativamente le prestazioni nei compiti downstream, specialmente nella risoluzione di problemi difficili."}, "r1-1776": {"description": "R1-1776 è una versione del modello DeepSeek R1, addestrata successivamente per fornire informazioni fattuali non verificate e prive di pregiudizi."}, "solar-mini": {"description": "Solar Mini è un LLM compatto, con prestazioni superiori a GPT-3.5, dotato di potenti capacità multilingue, supporta inglese e coreano, offrendo soluzioni efficienti e compatte."}, "solar-mini-ja": {"description": "Solar Mini (Ja) espande le capacità di Solar Mini, concentrandosi sul giapponese, mantenendo al contempo prestazioni elevate ed efficienti nell'uso dell'inglese e del coreano."}, "solar-pro": {"description": "Solar Pro è un LLM altamente intelligente lanciato da Upstage, focalizzato sulla capacità di seguire istruzioni su singolo GPU, con un punteggio IFEval superiore a 80. Attualmente supporta l'inglese, con una versione ufficiale prevista per novembre 2024, che espanderà il supporto linguistico e la lunghezza del contesto."}, "sonar": {"description": "Prodotto di ricerca leggero basato sul contesto di ricerca, più veloce e più economico rispetto a Sonar Pro."}, "sonar-deep-research": {"description": "Deep Research conduce ricerche complete a livello esperto e le sintetizza in rapporti accessibili e utilizzabili."}, "sonar-pro": {"description": "Prodotto di ricerca avanzata che supporta il contesto di ricerca, query avanzate e follow-up."}, "sonar-reasoning": {"description": "Nuovo prodotto API supportato dal modello di ragionamento DeepSeek."}, "sonar-reasoning-pro": {"description": "Nuovo prodotto API supportato dal modello di ragionamento DeepSeek."}, "stable-diffusion-3-medium": {"description": "Ultimo modello di generazione immagini da testo lanciato da Stability AI. Questa versione migliora significativamente qualità dell'immagine, comprensione testuale e varietà di stili rispetto alle precedenti, interpretando con maggiore precisione prompt linguistici complessi e generando immagini più accurate e diversificate."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large è un modello generativo multimodale a diffusione trasformativa (MMDiT) con 800 milioni di parametri, che offre qualità d'immagine eccellente e alta corrispondenza con i prompt, supportando la generazione di immagini ad alta risoluzione fino a 1 milione di pixel, e funzionando efficientemente su hardware consumer standard."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo è un modello basato su stable-diffusion-3.5-large che utilizza la tecnologia di distillazione di diffusione antagonista (ADD) per una maggiore velocità."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 è inizializzato con i pesi del checkpoint stable-diffusion-v1.2 e raffinato per 595k passi a risoluzione 512x512 su \"laion-aesthetics v2 5+\", riducendo del 10% la condizionalità testuale per migliorare il campionamento guidato senza classificatore."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl presenta miglioramenti significativi rispetto alla versione v1.5 ed è comparabile agli attuali modelli SOTA open source come Midjourney. Le migliorie includono un backbone unet tre volte più grande, un modulo di raffinamento per migliorare la qualità delle immagini generate e tecniche di addestramento più efficienti."}, "stable-diffusion-xl-base-1.0": {"description": "Modello di generazione immagini da testo sviluppato e open source da Stability AI, con capacità creative di alto livello nel settore. Offre eccellente comprensione delle istruzioni e supporta definizioni di prompt inversi per generazioni di contenuti precise."}, "step-1-128k": {"description": "Equilibrio tra prestazioni e costi, adatto per scenari generali."}, "step-1-256k": {"description": "Capacità di elaborazione di contesto ultra lungo, particolarmente adatto per l'analisi di documenti lunghi."}, "step-1-32k": {"description": "Supporta dialoghi di lunghezza media, adatto per vari scenari applicativi."}, "step-1-8k": {"description": "<PERSON>lo di piccole dimensioni, adatto per compiti leggeri."}, "step-1-flash": {"description": "<PERSON>lo ad alta velocità, adatto per dialoghi in tempo reale."}, "step-1.5v-mini": {"description": "<PERSON>o modello possiede potenti capacità di comprensione video."}, "step-1o-turbo-vision": {"description": "Questo modello ha potenti capacità di comprensione delle immagini, superando 1o nei campi matematici e di codifica. Il modello è più piccolo di 1o e offre una velocità di output più rapida."}, "step-1o-vision-32k": {"description": "Questo modello possiede una potente capacità di comprensione delle immagini. Rispetto ai modelli della serie step-1v, offre prestazioni visive superiori."}, "step-1v-32k": {"description": "Supporta input visivi, migliorando l'esperienza di interazione multimodale."}, "step-1v-8k": {"description": "Modello visivo di piccole dimensioni, adatto per compiti di base di testo e immagine."}, "step-1x-edit": {"description": "<PERSON><PERSON> specializzato in compiti di editing immagini, capace di modificare e migliorare immagini basandosi su input di immagini e descrizioni testuali fornite dall'utente. Supporta vari formati di input, inclusi descrizioni testuali e immagini di esempio, comprendendo l'intento dell'utente e generando risultati di editing conformi alle richieste."}, "step-1x-medium": {"description": "Modello con potenti capacità di generazione immagini, che supporta input tramite descrizioni testuali. Offre supporto nativo per il cinese, comprendendo e processando meglio descrizioni testuali in cinese, catturando con maggiore precisione il significato semantico e traducendolo in caratteristiche visive per una generazione più accurata. Produce immagini ad alta risoluzione e qualità, con capacità di trasferimento di stile."}, "step-2-16k": {"description": "Supporta interazioni di contesto su larga scala, adatto per scenari di dialogo complessi."}, "step-2-16k-exp": {"description": "Versione sperimentale del modello step-2, contenente le ultime funzionalità, in aggiornamento continuo. Non raccomandato per l'uso in ambienti di produzione ufficiali."}, "step-2-mini": {"description": "Un modello di grandi dimensioni ad alta velocità basato sulla nuova architettura di attenzione auto-sviluppata MFA, in grado di raggiungere risultati simili a quelli di step1 a un costo molto basso, mantenendo al contempo una maggiore capacità di elaborazione e tempi di risposta più rapidi. È in grado di gestire compiti generali, con competenze particolari nella programmazione."}, "step-2x-large": {"description": "Nuova generazione del modello Xingchen Step, focalizzato sulla generazione di immagini di alta qualità basate su descrizioni testuali fornite dall'utente. Il nuovo modello produce immagini con texture più realistiche e capacità migliorate nella generazione di testo in cinese e inglese."}, "step-r1-v-mini": {"description": "Questo modello è un grande modello di inferenza con potenti capacità di comprensione delle immagini, in grado di gestire informazioni visive e testuali, producendo contenuti testuali dopo un profondo ragionamento. Questo modello si distingue nel campo del ragionamento visivo, mostrando anche capacità di ragionamento matematico, codice e testo di primo livello. La lunghezza del contesto è di 100k."}, "taichu_llm": {"description": "Il modello linguistico Taichu di Zīdōng ha una straordinaria capacità di comprensione del linguaggio e abilità in creazione di testi, domande di conoscenza, programmazione, calcoli matematici, ragionamento logico, analisi del sentimento e sintesi di testi. Combina in modo innovativo il pre-addestramento su grandi dati con una ricca conoscenza multi-sorgente, affinando continuamente la tecnologia degli algoritmi e assorbendo costantemente nuove conoscenze da dati testuali massivi, migliorando continuamente le prestazioni del modello. Fornisce agli utenti informazioni e servizi più convenienti e un'esperienza più intelligente."}, "taichu_o1": {"description": "taichu_o1 è un nuovo grande modello di inferenza, che realizza catene di pensiero simili a quelle umane attraverso interazioni multimodali e apprendimento rinforzato, supportando deduzioni decisionali complesse, mostrando percorsi di pensiero di inferenza modellabile mantenendo un'uscita ad alta precisione, adatto per analisi strategiche e pensiero profondo."}, "taichu_vl": {"description": "Integra capacità di comprensione delle immagini, trasferimento di conoscenze e attribuzione logica, mostrando prestazioni eccezionali nel campo delle domande e risposte basate su testo e immagini."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct ha 80 miliardi di parametri, ma attivando solo 13 miliardi di parametri può competere con modelli più grandi, supportando un ragionamento ibrido “pensiero veloce/pensiero lento”; garantisce una comprensione stabile di testi lunghi; validato da BFCL-v3 e τ-Bench, con capacità agent leader; integra GQA e formati multi-quantizzazione per un'inferenza efficiente."}, "text-embedding-3-large": {"description": "Il modello di vettorizzazione più potente, adatto per compiti in inglese e non inglese."}, "text-embedding-3-small": {"description": "Modello di Embedding di nuova generazione, efficiente ed economico, adatto per la ricerca di conoscenza, applicazioni RAG e altri scenari."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 è un modello linguistico a pesi aperti bilingue (cinese e inglese) da 32B, ottimizzato per la generazione di codice, chiamate a funzioni e compiti agenti. È stato pre-addestrato su 15T di dati di alta qualità e di ri-ragionamento, e ulteriormente perfezionato utilizzando l'allineamento delle preferenze umane, il campionamento di rifiuto e l'apprendimento rinforzato. Questo modello mostra prestazioni eccezionali in ragionamenti complessi, generazione di artefatti e compiti di output strutturato, raggiungendo prestazioni comparabili a GPT-4o e DeepSeek-V3-0324 in vari benchmark."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 è un modello linguistico a pesi aperti bilingue (cinese e inglese) da 32B, ottimizzato per la generazione di codice, chiamate a funzioni e compiti agenti. È stato pre-addestrato su 15T di dati di alta qualità e di ri-ragionamento, e ulteriormente perfezionato utilizzando l'allineamento delle preferenze umane, il campionamento di rifiuto e l'apprendimento rinforzato. Questo modello mostra prestazioni eccezionali in ragionamenti complessi, generazione di artefatti e compiti di output strutturato, raggiungendo prestazioni comparabili a GPT-4o e DeepSeek-V3-0324 in vari benchmark."}, "thudm/glm-4-9b-chat": {"description": "La versione open source dell'ultima generazione del modello pre-addestrato GLM-4 rilasciato da Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 è un modello linguistico con 9 miliardi di parametri della serie GLM-4 sviluppato da THUDM. GLM-4-9B-0414 utilizza le stesse strategie di apprendimento rinforzato e allineamento del suo modello corrispondente più grande da 32B, raggi<PERSON>do alte prestazioni rispetto alle sue dimensioni, rendendolo adatto per implementazioni a risorse limitate che richiedono ancora forti capacità di comprensione e generazione del linguaggio."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 è una variante di ragionamento potenziata di GLM-4-32B, costruita per la risoluzione di problemi di matematica profonda, logica e orientati al codice. Utilizza l'apprendimento rinforzato esteso (specifico per compiti e basato su preferenze generali) per migliorare le prestazioni in compiti complessi a più passaggi. Rispetto al modello di base GLM-4-32B, Z1 ha migliorato significativamente le capacità di ragionamento strutturato e nei domini formali.\n\nQuesto modello supporta l'applicazione di 'passaggi di pensiero' tramite ingegneria dei prompt e offre una coerenza migliorata per output di lungo formato. È ottimizzato per flussi di lavoro agenti e supporta contesti lunghi (tramite YaRN), chiamate a strumenti JSON e configurazioni di campionamento a grana fine per un ragionamento stabile. È particolarmente adatto per casi d'uso che richiedono ragionamenti approfonditi, a più passaggi o deduzioni formali."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 è una variante di ragionamento potenziata di GLM-4-32B, costruita per la risoluzione di problemi di matematica profonda, logica e orientati al codice. Utilizza l'apprendimento rinforzato esteso (specifico per compiti e basato su preferenze generali) per migliorare le prestazioni in compiti complessi a più passaggi. Rispetto al modello di base GLM-4-32B, Z1 ha migliorato significativamente le capacità di ragionamento strutturato e nei domini formali.\n\nQuesto modello supporta l'applicazione di 'passaggi di pensiero' tramite ingegneria dei prompt e offre una coerenza migliorata per output di lungo formato. È ottimizzato per flussi di lavoro agenti e supporta contesti lunghi (tramite YaRN), chiamate a strumenti JSON e configurazioni di campionamento a grana fine per un ragionamento stabile. È particolarmente adatto per casi d'uso che richiedono ragionamenti approfonditi, a più passaggi o deduzioni formali."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 è un modello linguistico con 9 miliardi di parametri della serie GLM-4 sviluppato da THUDM. Utilizza tecniche inizialmente applicate a modelli GLM-Z1 più grandi, inclusi apprendimento rinforzato esteso, allineamento di ranking a coppie e addestramento per compiti di ragionamento intensivo come matematica, codifica e logica. Nonostante le sue dimensioni più piccole, mostra prestazioni robuste in compiti di ragionamento generali e supera molti modelli open source nel suo livello di pesi."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B è un modello di inferenza profonda con 32 miliardi di parametri della serie GLM-4-Z1, ottimizzato per compiti complessi e aperti che richiedono un lungo periodo di riflessione. Si basa su glm-4-32b-0414, aggiungendo ulteriori fasi di apprendimento rinforzato e strategie di allineamento multi-fase, introducendo la capacità di 'riflessione' progettata per simulare un'elaborazione cognitiva estesa. Questo include ragionamento iterativo, analisi multi-salto e flussi di lavoro potenziati da strumenti, come ricerca, recupero e sintesi consapevole delle citazioni.\n\nQuesto modello eccelle nella scrittura di ricerca, analisi comparativa e domande complesse. Supporta chiamate di funzione per primari di ricerca e navigazione (`search`, `click`, `open`, `finish`), rendendolo utilizzabile in pipeline basate su agenti. Il comportamento di riflessione è modellato da un controllo ciclico multi-turno con premi basati su regole e meccanismi di decisione ritardata, e viene confrontato con framework di ricerca approfondita come l'allineamento interno di OpenAI. Questa variante è adatta per scenari che richiedono profondità piuttosto che velocità."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera è stato creato combinando DeepSeek-R1 e DeepSeek-V3 (0324), unendo le capacità di inferenza di R1 e i miglioramenti di efficienza dei token di V3. Si basa sull'architettura DeepSeek-MoE Transformer ed è ottimizzato per compiti generali di generazione di testo.\n\nQuesto modello combina i pesi pre-addestrati di due modelli sorgente per bilanciare le prestazioni in inferenza, efficienza e compiti di seguire istruzioni. È rilasciato sotto la licenza MIT, destinato a scopi di ricerca e commerciali."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) offre capacità di calcolo potenziate attraverso strategie e architetture di modelli efficienti."}, "tts-1": {"description": "L'ultimo modello di sintesi vocale, otti<PERSON><PERSON><PERSON> per la velocità in scenari in tempo reale."}, "tts-1-hd": {"description": "L'ultimo modello di sintesi vocale, ottimizzato per la qualità."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) è adatto per compiti di istruzione dettagliati, offrendo eccellenti capacità di elaborazione linguistica."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet ha elevato gli standard del settore, superando le prestazioni dei modelli concorrenti e di Claude 3 Opus, dimostrando eccellenza in una vasta gamma di valutazioni, mantenendo al contempo la velocità e i costi dei nostri modelli di livello medio."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet è il modello di prossima generazione più veloce di Anthropic. Rispetto a Claude 3 Haiku, Claude 3.7 Sonnet ha migliorato le sue capacità in vari ambiti e ha superato il modello di generazione precedente, Claude 3 Opus, in molti test di intelligenza."}, "v0-1.0-md": {"description": "Il modello v0-1.0-md è una versione precedente fornita tramite l'API v0"}, "v0-1.5-lg": {"description": "Il modello v0-1.5-lg è adatto per compiti avanzati di pensiero o ragionamento"}, "v0-1.5-md": {"description": "Il modello v0-1.5-md è adatto per compiti quotidiani e generazione di interfacce utente (UI)"}, "wan2.2-t2i-flash": {"description": "Versione ultra-veloce Wanxiang 2.2, modello più recente. Miglioramenti completi in creatività, stabilità e realismo, con velocità di generazione elevata e ottimo rapporto qualità-prezzo."}, "wan2.2-t2i-plus": {"description": "Versione professionale Wanxiang 2.2, modello più recente. Miglioramenti completi in creatività, stabilità e realismo, con dettagli di generazione ricchi."}, "wanx-v1": {"description": "Modello base di generazione immagini da testo, corrispondente al modello generico 1.0 ufficiale di Tongyi Wanxiang."}, "wanx2.0-t2i-turbo": {"description": "Specializzato in ritratti realistici, con velocità media e costi contenuti. Corrisponde al modello ultra-veloce 2.0 ufficiale di Tongyi Wanxiang."}, "wanx2.1-t2i-plus": {"description": "Versione completamente aggiornata, con dettagli di immagine più ricchi e velocità leggermente inferiore. Corrisponde al modello professionale 2.1 ufficiale di Tongyi Wanxiang."}, "wanx2.1-t2i-turbo": {"description": "Versione completamente aggiornata, con velocità elevata, prestazioni complete e ottimo rapporto qualità-prezzo. Corrisponde al modello ultra-veloce 2.1 ufficiale di Tongyi Wanxiang."}, "whisper-1": {"description": "Modello universale di riconoscimento vocale, supporta riconoscimento vocale multilingue, traduzione vocale e identificazione della lingua."}, "wizardlm2": {"description": "WizardLM 2 è un modello di linguaggio fornito da Microsoft AI, particolarmente efficace in dialoghi complessi, multilingue, ragionamento e assistenti intelligenti."}, "wizardlm2:8x22b": {"description": "WizardLM 2 è un modello di linguaggio fornito da Microsoft AI, particolarmente efficace in dialoghi complessi, multilingue, ragionamento e assistenti intelligenti."}, "x1": {"description": "Il modello Spark X1 sarà ulteriormente aggiornato, rag<PERSON><PERSON><PERSON> risultati in compiti generali come ragionamento, generazione di testo e comprensione del linguaggio, in linea con OpenAI o1 e DeepSeek R1, partendo da una posizione di leadership nei compiti matematici."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 è una versione aggiornata di Yi. Utilizza un corpus di alta qualità di 500B token per il pre-addestramento continuo di Yi e viene finetunato su 3M campioni di micro-tuning diversificati."}, "yi-large": {"description": "Un nuovo modello con centinaia di miliardi di parametri, offre capacità eccezionali di risposta e generazione di testi."}, "yi-large-fc": {"description": "Basato sul modello yi-large, supporta e potenzia le capacità di chiamata di strumenti, adatto per vari scenari aziendali che richiedono la costruzione di agenti o flussi di lavoro."}, "yi-large-preview": {"description": "Versione iniziale, si consiglia di utilizzare yi-large (nuova versione)."}, "yi-large-rag": {"description": "<PERSON><PERSON><PERSON> basato sul modello yi-large, combina tecnologie di recupero e generazione per fornire risposte precise, con servizi di ricerca in tempo reale su tutto il web."}, "yi-large-turbo": {"description": "Eccellente rapporto qualità-prezzo e prestazioni superiori. Ottimizzazione ad alta precisione in base a prestazioni, velocità di inferenza e costi."}, "yi-lightning": {"description": "Il modello di ultima generazione ad alte prestazioni, che garantisce output di alta qualità e migliora notevolmente la velocità di ragionamento."}, "yi-lightning-lite": {"description": "<PERSON>e leggera, si consiglia di utilizzare yi-lightning."}, "yi-medium": {"description": "Modello di dimensioni medie aggiornato e ottimizzato, con capacità bilanciate e un buon rapporto qualità-prezzo. Ottimizzazione profonda delle capacità di seguire istruzioni."}, "yi-medium-200k": {"description": "Finestra di contesto ultra lunga di 200K, offre capacità di comprensione e generazione di testi lunghi."}, "yi-spark": {"description": "<PERSON><PERSON><PERSON> e potente, modello leggero e veloce. Offre capacità potenziate di calcolo matematico e scrittura di codice."}, "yi-vision": {"description": "Modello per compiti visivi complessi, offre elevate prestazioni nella comprensione e analisi delle immagini."}, "yi-vision-v2": {"description": "Modello per compiti visivi complessi, che offre capacità di comprensione e analisi ad alte prestazioni basate su più immagini."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 è un modello base progettato per applicazioni agenti intelligenti, che utilizza un'architettura Mixture-of-Experts (MoE). Ottimizzato profondamente per chiamate a strumenti, navigazione web, ingegneria del software e programmazione frontend, supporta integrazioni fluide con agenti di codice come Claude Code e Roo Code. Adotta una modalità di inferenza ibrida per adattarsi a scenari di ragionamento complessi e uso quotidiano."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air è un modello base progettato per applicazioni agenti intelligenti, che utilizza un'architettura Mixture-of-Experts (MoE). Ottimizzato profondamente per chiamate a strumenti, navigazione web, ingegneria del software e programmazione frontend, supporta integrazioni fluide con agenti di codice come Claude Code e Roo Code. Adotta una modalità di inferenza ibrida per adattarsi a scenari di ragionamento complessi e uso quotidiano."}}