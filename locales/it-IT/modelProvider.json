{"azure": {"azureApiVersion": {"desc": "Versione dell'API di Azure, nel formato YYYY-MM-DD, consulta [ultima versione](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> el<PERSON>", "title": "Versione API Azure"}, "empty": "Inserisci l'ID del modello per aggiungere il primo modello", "endpoint": {"desc": "Quando si controllano le risorse dal portale di Azure, questo valore si trova nella sezione 'Chiavi e endpoint'", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Indirizzo API Azure"}, "modelListPlaceholder": "Seleziona o aggiungi il modello OpenAI che hai distribuito", "title": "Azure OpenAI", "token": {"desc": "Quando si controllano le risorse dal portale di Azure, questo valore si trova nella sezione 'Chiavi e endpoint'. Puoi usare KEY1 o KEY2", "placeholder": "Chiave API Azure", "title": "Chiave API"}}, "azureai": {"azureApiVersion": {"desc": "Versione API di Azure, seguendo il formato YYYY-MM-DD, consulta [l'ultima versione](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> el<PERSON>", "title": "Versione API di Azure"}, "endpoint": {"desc": "Trova l'endpoint di inferenza del modello Azure AI nella panoramica del progetto Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Endpoint di Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Trova la chiave API nella panoramica del progetto Azure AI", "placeholder": "Chiave Azure", "title": "Chiave"}}, "bedrock": {"accessKeyId": {"desc": "Inserisci l'ID chiave di accesso AWS", "placeholder": "ID chiave di accesso AWS", "title": "ID chiave di accesso AWS"}, "checker": {"desc": "Verifica se AccessKeyId / SecretAccessKey sono stati inseriti correttamente"}, "region": {"desc": "Inserisci la regione AWS", "placeholder": "Regione AWS", "title": "Regione AWS"}, "secretAccessKey": {"desc": "Inserisci la chiave di accesso segreta AWS", "placeholder": "Chiave di accesso segreta AWS", "title": "Chiave di accesso segreta AWS"}, "sessionToken": {"desc": "Se stai utilizzando AWS SSO/STS, inserisci il tuo AWS Session Token", "placeholder": "AWS Session Token", "title": "AWS Session Token (opzionale)"}, "title": "Bedrock", "unlock": {"customRegion": "Regione del servizio personalizzata", "customSessionToken": "Token di sessione personalizzato", "description": "Inserisci la tua chiave di accesso AWS AccessKeyId / SecretAccessKey per avviare la sessione. L'applicazione non memorizzerà la tua configurazione di autenticazione", "imageGenerationDescription": "Inserisci il tuo AWS AccessKeyId / SecretAccessKey per iniziare a generare. L'applicazione non registrerà le tue credenziali di autenticazione", "title": "Usa le informazioni di autenticazione Bedrock personalizzate"}}, "cloudflare": {"apiKey": {"desc": "Compila l'Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Inserisci l'ID dell'account Cloudflare o l'indirizzo API personalizzato", "placeholder": "ID account Cloudflare / URL API personalizzato", "title": "ID account Cloudflare / indirizzo API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Inserisci la tua API Key", "title": "API Key"}, "basicTitle": "Informazioni di base", "configTitle": "Informazioni di configurazione", "confirm": "<PERSON><PERSON>", "createSuccess": "Creazione avvenuta con successo", "description": {"placeholder": "Descrizione del fornitore (opzionale)", "title": "Descrizione del fornitore"}, "id": {"desc": "Identificatore unico del fornitore di servizi, non modificabile dopo la creazione", "format": "<PERSON><PERSON><PERSON> contenere solo numeri, lettere minuscole, trattini (-) e underscore (_) ", "placeholder": "Si consiglia di utilizzare solo lettere minuscole, ad esempio openai, non modificabile dopo la creazione", "required": "Inserisci l'ID del fornitore", "title": "ID del fornitore"}, "logo": {"required": "Carica un logo del fornitore valido", "title": "Logo del fornitore"}, "name": {"placeholder": "Inserisci il nome visualizzato del fornitore", "required": "Inserisci il nome del fornitore", "title": "Nome del fornitore"}, "proxyUrl": {"required": "Inserisci l'indirizzo del proxy", "title": "Indirizzo proxy"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Seleziona il tipo di SDK", "title": "Formato della richiesta"}, "title": "<PERSON>rea fornitore AI personalizzato"}, "github": {"personalAccessToken": {"desc": "Inserisci il tuo PAT di Github, clicca [qui](https://github.com/settings/tokens) per crearne uno", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Inserisci il tuo token HuggingFace, clicca [qui](https://huggingface.co/settings/tokens) per crearne uno", "placeholder": "hf_xxxxxxxxx", "title": "Token <PERSON>"}}, "list": {"title": {"disabled": "Fornitore non attivato", "enabled": "<PERSON><PERSON><PERSON>ti<PERSON>"}}, "menu": {"addCustomProvider": "Aggiungi fornitore <PERSON>", "all": "<PERSON><PERSON>", "list": {"disabled": "Non attivato", "enabled": "<PERSON><PERSON><PERSON><PERSON>"}, "notFound": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "searchProviders": "Cerca fornitori...", "sort": "Ordinamento personalizzato"}, "ollama": {"checker": {"desc": "Verifica se l'indirizzo del proxy è stato compilato correttamente", "title": "Controllo della connettività"}, "customModelName": {"desc": "Agg<PERSON>ngi modelli <PERSON>, separati da virgola (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Nome del modello personalizzato"}, "download": {"desc": "Ollama sta scaricando questo modello, per favore non chiudere questa pagina. Il download verrà interrotto e riprenderà dal punto in cui si è interrotto in caso di riavvio.", "failed": "Download del modello fallito, controlla la rete o le impostazioni di Ollama e riprova", "remainingTime": "Te<PERSON>", "speed": "Velocità di download", "title": "Download del modello in corso {{model}}"}, "endpoint": {"desc": "Deve includere http(s)://, può rimanere vuoto se non specificato localmente", "title": "Indirizzo del proxy dell'interfaccia"}, "title": "Ollama", "unlock": {"cancel": "Annulla download", "confirm": "Download", "description": "Inserisci l'etichetta del modello Ollama per continuare la sessione", "downloaded": "{{completed}} / {{total}}", "starting": "Inizio download...", "title": "Scarica il modello Ollama specificato"}}, "providerModels": {"config": {"aesGcm": "La tua chiave e l'indirizzo proxy saranno crittografati utilizzando l'algoritmo di crittografia <1>AES-GCM</1>", "apiKey": {"desc": "Inserisci la tua {{name}} API Key", "descWithUrl": "Per favore inserisci la tua API Key di {{name}}, <3>clicca qui per ottenerla</3>", "placeholder": "{{name}} API Key", "title": "API Key"}, "baseURL": {"desc": "Deve contenere http(s)://", "invalid": "Inserisci un URL valido", "placeholder": "https://your-proxy-url.com/v1", "title": "Indirizzo proxy API"}, "checker": {"button": "Controlla", "desc": "Verifica se l'API Key e l'indirizzo proxy sono stati inseriti correttamente", "pass": "Controllo superato", "title": "Verifica connettività"}, "fetchOnClient": {"desc": "La modalità di richiesta client avvierà direttamente la richiesta di sessione dal browser, migliorando la velocità di risposta", "title": "Utilizza la modalità di richiesta client"}, "helpDoc": "Guida alla configurazione", "responsesApi": {"desc": "Utilizza il nuovo formato di richiesta di OpenAI per sbloccare funzionalità avanzate come la catena di pensiero", "title": "Utilizza lo standard Responses API"}, "waitingForMore": "Altri modelli sono in fase di <1>implementazione</1>, resta sintonizzato"}, "createNew": {"title": "Crea modello AI personalizzato"}, "item": {"config": "Configura modello", "customModelCards": {"addNew": "Crea e aggiungi modello {{id}}", "confirmDelete": "Stai per eliminare questo modello personalizzato, una volta eliminato non sarà recuperabile, procedi con cautela."}, "delete": {"confirm": "Confermi di voler eliminare il modello {{displayName}}?", "success": "Eliminazione avvenuta con successo", "title": "Elimina modello"}, "modelConfig": {"azureDeployName": {"extra": "Campo effettivamente richiesto in Azure OpenAI", "placeholder": "Inserisci il nome di distribuzione del modello in Azure", "title": "Nome di distribuzione del modello"}, "deployName": {"extra": "Questo campo verrà utilizzato come ID del modello quando si invia la richiesta", "placeholder": "Inserisci il nome o l'ID effettivo del modello distribuito", "title": "Nome di distribuzione del modello"}, "displayName": {"placeholder": "Inserisci il nome visualizzato del modello, ad esempio ChatGPT, GPT-4, ecc.", "title": "Nome visualizzato del modello"}, "files": {"extra": "L'attuale implementazione del caricamento file è solo una soluzione temporanea, limitata a tentativi personali. Attendere implementazioni complete per il caricamento file.", "title": "Supporto per il caricamento file"}, "functionCall": {"extra": "Questa configurazione abiliterà solo la capacità del modello di utilizzare strumenti, consentendo così di aggiungere plugin di tipo strumento al modello. Tuttavia, se il modello supporta realmente l'uso degli strumenti dipende interamente dal modello stesso; si prega di testarne l'usabilità", "title": "Supporto all'uso degli strumenti"}, "id": {"extra": "Non modificabile dopo la creazione, verrà utilizzato come ID del modello durante la chiamata all'AI", "placeholder": "Inserisci l'ID del modello, ad esempio gpt-4o o claude-3.5-sonnet", "title": "ID del modello"}, "modalTitle": "Configurazione modello <PERSON>to", "reasoning": {"extra": "Questa configurazione attiverà solo la capacità di pensiero profondo del modello; l'effetto specifico dipende interamente dal modello stesso. Si prega di testare autonomamente se il modello possiede una capacità di pensiero profondo utilizzabile.", "title": "Supporto per il pensiero profondo"}, "tokens": {"extra": "Imposta il numero massimo di token supportati dal modello", "title": "Finestra di contesto massima", "unlimited": "Illimitato"}, "vision": {"extra": "Questa configurazione abiliterà solo la configurazione di caricamento immagini nell'app, la disponibilità di riconoscimento dipende interamente dal modello stesso, testare autonomamente la disponibilità di riconoscimento visivo di questo modello.", "title": "Supporto per riconoscimento visivo"}}, "pricing": {"image": "${{amount}}/Immagine", "inputCharts": "${{amount}}/M caratteri", "inputMinutes": "${{amount}}/minuti", "inputTokens": "Ingresso ${{amount}}/M", "outputTokens": "Uscita ${{amount}}/M"}, "releasedAt": "Rilasciato il {{releasedAt}}"}, "list": {"addNew": "Aggiungi modello", "disabled": "Non attivato", "disabledActions": {"showMore": "<PERSON><PERSON> tutto"}, "empty": {"desc": "Si prega di creare un modello personalizzato o di importare un modello per iniziare a utilizzarlo", "title": "Nessun modello disponibile"}, "enabled": "<PERSON><PERSON><PERSON><PERSON>", "enabledActions": {"disableAll": "Disattiva tutto", "enableAll": "Attiva tutto", "sort": "Ordinamento modelli personalizzato"}, "enabledEmpty": "<PERSON><PERSON><PERSON> modello attivato, attiva i modelli desiderati dall'elenco qui sotto~", "fetcher": {"clear": "Cancella i modelli recuperati", "fetch": "Recupera l'elenco dei modelli", "fetching": "Recupero dell'elenco dei modelli in corso...", "latestTime": "Ultimo a<PERSON>rna<PERSON>: {{time}}", "noLatestTime": "<PERSON><PERSON><PERSON> elenco recuperato finora"}, "resetAll": {"conform": "Sei sicuro di voler ripristinare tutte le modifiche al modello corrente? Dopo il ripristino, l'elenco dei modelli correnti tornerà allo stato predefinito", "success": "<PERSON><PERSON><PERSON><PERSON> avvenuto con successo", "title": "<PERSON><PERSON><PERSON><PERSON> tutte le modifiche"}, "search": "Cerca modelli...", "searchResult": "Trovati {{count}} modelli", "title": "Elenco dei modelli", "total": "Totale di {{count}} modelli disponibili"}, "searchNotFound": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato"}, "sortModal": {"success": "Ordinamento aggiornato con successo", "title": "Ordinamento personalizzato", "update": "Aggiorna"}, "updateAiProvider": {"confirmDelete": "Stai per eliminare questo fornitore AI, una volta eliminato non sarà recuperabile, confermi di voler eliminare?", "deleteSuccess": "Eliminazione avvenuta con successo", "tooltip": "Aggiorna la configurazione di base del fornitore", "updateSuccess": "Aggiornamento avvenuto con successo"}, "updateCustomAiProvider": {"title": "Aggiorna la configurazione del fornitore di AI personalizzato"}, "vertexai": {"apiKey": {"desc": "Inserisci le tue chiavi Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Chiavi Vertex AI"}}, "zeroone": {"title": "01.AI ZeroOne"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}