{"01-ai/yi-1.5-34b-chat": {"description": "Zero One Everything, en son a<PERSON><PERSON><PERSON> kaynak ince ayar modelidir, 34 milyar paramet<PERSON><PERSON>, ince ayar çeşitli diyalog senaryolarını destekler, y<PERSON><PERSON><PERSON> kaliteli eğitim verileri ile insan tercihleri ile hizalanmıştır."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One Everything, en son a<PERSON><PERSON><PERSON> kaynak ince ayar modelidir, 9 milyar parametreye <PERSON>, ince ayar çeşitli diyalog senaryolarını destekler, y<PERSON><PERSON><PERSON> kaliteli eğitim verileri ile insan tercihleri ile hizalanmıştır."}, "360/deepseek-r1": {"description": "[360 Da<PERSON><PERSON><PERSON><PERSON><PERSON>] DeepSeek-R1, son eğ<PERSON><PERSON> a<PERSON>nda geniş çapta pekiştirme öğrenimi teknikleri kull<PERSON>, çok az etiketli veri ile modelin çıkarım yeteneğini büyük ölçüde artırmıştır. <PERSON><PERSON><PERSON><PERSON>, kod, doğ<PERSON> dil çıkarımı gibi görevlerde, OpenAI o1 resmi sürümü ile benzer performans sergilemektedir."}, "360gpt-pro": {"description": "360GPT Pro, 360 AI model se<PERSON><PERSON><PERSON> önemli bir üyesi o<PERSON>ak, çeşitli doğal dil uygulama senaryolarını karşılamak için etkili metin iş<PERSON>e <PERSON>, uzun metin anlama ve çoklu diyalog gibi işlevleri destekler."}, "360gpt-pro-trans": {"description": "<PERSON><PERSON><PERSON> i<PERSON><PERSON> o<PERSON>ak ta<PERSON>lanmış model, derin<PERSON><PERSON><PERSON> ince ayar yapılmış ve çeviri sonuçları lider konumdadır."}, "360gpt-turbo": {"description": "360GPT Turbo, güçlü hesaplama ve diyalog yetenekleri sunar, mükemmel anlam anlama ve oluşturma verimliliğine <PERSON>tir, işletmeler ve geliştiriciler için ideal bir akıllı asistan çözümüdür."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K, anlam güvenliği ve sorumluluk odaklılığı vurgular, içerik güvenliği konusunda yüksek gereksinimlere sahip uygulama senaryoları için ta<PERSON>lanmıştır, kullanıcı deneyiminin doğruluğunu ve sağlamlığını garanti eder."}, "360gpt2-o1": {"description": "360gpt2-o1, d<PERSON><PERSON><PERSON><PERSON> zincirini ağaç arama ile inşa eder ve yansıtma mekanizmasını entegre eder, pek<PERSON>ş<PERSON><PERSON><PERSON> öğrenimi il<PERSON> eğ<PERSON>li<PERSON>, model k<PERSON><PERSON> yansıtma ve hata düzeltme yeteneğine sa<PERSON>."}, "360gpt2-pro": {"description": "360GPT2 Pro, 360 şirketi tarafından sunulan yüksek düzeyde doğal dil işleme modelidir, müke<PERSON>l metin oluşturma ve anlama yeteneğ<PERSON>, özellikle oluşturma ve yaratma alanında olağanüstü performans gösterir, karmaşık dil dönüşümleri ve rol canlandırma görevlerini işleyebilir."}, "360zhinao2-o1": {"description": "360zhinao2-o1, dü<PERSON><PERSON>nce zincirini oluşturmak için ağaç araması kullanır ve yansıtma mekanizmasını entegre eder, pekiştirme öğrenimi ile eğ<PERSON>lir, model kend<PERSON> yansıtma ve hata düzeltme yeteneğine sahiptir."}, "4.0Ultra": {"description": "Spark4.0 Ultra, <PERSON><PERSON><PERSON><PERSON> büyük model serisinin en güçlü versiyonudur, çev<PERSON>içi arama bağlantısını yükseltirken, metin içeriğini anlama ve özetleme yeteneğini artırır. Ofis verimliliğini artırmak ve taleplere doğru yanıt vermek için kapsamlı bir çözüm sunar, sektördeki akıllı ürünlerin öncüsüdür."}, "AnimeSharp": {"description": "AnimeSharp (di<PERSON><PERSON> ad<PERSON><PERSON> “4x‑AnimeSharp”), Kim2091 tarafından ESRGAN mimarisi temel alınarak geliştirilen açık kaynaklı bir süper çözünürlük modelidir ve anime tarzı görüntülerin büyütülmesi ve keskinleştirilmesine odaklanır. Şubat 2022'de “4x-TextSharpV1” olarak yeniden adlandırılmıştır; başlangıçta metin görüntüleri için de uygundu ancak performansı anime içeriği için önemli ölçüde optimize edilmiştir."}, "Baichuan2-Turbo": {"description": "Arama artırma teknolojisi kullanarak büyük model ile alan bilgisi ve tüm ağ bilgisi arasında kapsamlı bir bağlantı sağlar. PDF, Word gibi çeşitli belge yüklemelerini ve URL girişini destekler, bilgi edinimi zamanında ve kapsamlıdır, çıktı sonuçları doğru ve profesyoneldir."}, "Baichuan3-Turbo": {"description": "Kurumsal yüksek frekanslı senaryolar için optimize edilmiş, etkisi büyük ölçüde artırılmış ve yüksek maliyet etkinliği sunmaktadır. Baichuan2 modeline kıyasla, içerik üretimi %20, bilgi sorgulama %17, rol oynama yeteneği %40 oranında artmıştır. Genel performansı GPT3.5'ten daha iyidir."}, "Baichuan3-Turbo-128k": {"description": "128K ultra uzun bağlam penceresine sahip, kurumsal yüksek frekanslı senaryolar için optimize edilmiş, etkisi büyük ölçüde artırılmış ve yüksek maliyet etkinliği sunmaktadır. Baichuan2 modeline kıyasla, içerik üretimi %20, bilgi sorgulama %17, rol oynama yeteneği %40 oranında artmıştır. Genel performansı GPT3.5'ten daha iyidir."}, "Baichuan4": {"description": "Model yetenekleri ülke içinde <PERSON>, bi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> üretim gibi Çince görevlerde yurtdışındaki önde gelen modelleri geride bırakmaktadır. <PERSON><PERSON><PERSON><PERSON>, sektör lideri çok modlu yeteneklere sahiptir ve birçok yetkili değerlendirme kriterinde mükemmel performans göstermektedir."}, "Baichuan4-Air": {"description": "Model yetenekleri ülk<PERSON> iç<PERSON>, bi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> <PERSON>retim gibi Çince görevlerde uluslararası ana akım modelleri aşmaktadır. Ayrıca, sektörde lider çok modlu yeteneklere sahip olup, birçok yetkili değerlendirme ölçütünde mükemmel performans sergilemektedir."}, "Baichuan4-Turbo": {"description": "Model yetenekleri ülk<PERSON> iç<PERSON>, bi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> <PERSON>retim gibi Çince görevlerde uluslararası ana akım modelleri aşmaktadır. Ayrıca, sektörde lider çok modlu yeteneklere sahip olup, birçok yetkili değerlendirme ölçütünde mükemmel performans sergilemektedir."}, "DeepSeek-R1": {"description": "En gelişmiş verimli LLM, a<PERSON><PERSON><PERSON>, matematik ve <PERSON>lama konularında uzmandır."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 - DeepSeek setindeki daha büyük ve daha akıllı model - Llama 70B mimarisine damıtılmıştır. Kıyaslamalar ve insan değerlendirmelerine <PERSON>, bu model orijinal Llama 70B'den daha akıll<PERSON><PERSON>, özellikle matematik ve gerçeklik doğruluğu gerektiren görevlerde mükemmel performans göstermektedir."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Qwen2.5-Math-1.5B temel alınarak oluşturulmuş DeepSeek-R1 damıtma modeli, pekiştirme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, a<PERSON>ık kaynak model çoklu görev standartlarını yeniler."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Qwen2.5-14B temel alınarak oluşturulmuş DeepSeek-R1 damıtma modeli, pekiştirme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, açık kaynak model çoklu görev standartlarını yeniler."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1 serisi, pek<PERSON>ş<PERSON>rme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, açık kaynak model çoklu görev standartlarını yeniler, OpenAI-o1-mini seviyesini aşar."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Qwen2.5-Math-7B temel alınarak oluşturulmuş DeepSeek-R1 damıtma modeli, pekiştirme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, açık kaynak model çoklu görev standartlarını yeniler."}, "DeepSeek-V3": {"description": "DeepSeek-V3, <PERSON><PERSON>yış şirketi tarafından geliştirilen bir MoE modelidir. DeepSeek-V3, birçok değerlendirmede Qwen2.5-72B ve Llama-3.1-405B gibi diğer açık kaynak modelleri geride bırakmış ve performans açısından dünya çapında en iyi kapalı kaynak model olan GPT-4o ve Claude-3.5-Sonnet ile eşit seviyededir."}, "Doubao-lite-128k": {"description": "<PERSON><PERSON><PERSON><PERSON>lite, son derece hızlı yanıt süresi ve daha iyi fiyat-performans oranı ile müşterilere farklı senaryolar için daha esnek seçenekler sunar. 128k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "Doubao-lite-32k": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>e, son derece hızlı yanıt süresi ve daha iyi fiyat-performans oranı ile müşterilere farklı senaryolar için daha esnek seçenekler sunar. 32k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "Doubao-lite-4k": {"description": "<PERSON><PERSON><PERSON><PERSON>lite, son derece hızlı yanıt süresi ve daha iyi fiyat-performans oranı ile müşterilere farklı senaryolar için daha esnek seçenekler sunar. 4k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "Doubao-pro-128k": {"description": "En etkili ana model o<PERSON><PERSON>, karmaşık görevlerin işlenmesi için uygundur. <PERSON><PERSON><PERSON> soru-ceva<PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ya<PERSON><PERSON>, metin s<PERSON>, rol yapma gibi senaryolarda mükemmel performans gösterir. 128k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "Doubao-pro-32k": {"description": "En etkili ana model o<PERSON><PERSON>, karmaşık görevlerin işlenmesi için uygundur. <PERSON><PERSON><PERSON> soru-ceva<PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ya<PERSON><PERSON>, metin s<PERSON>, rol yapma gibi senaryolarda mükemmel performans gösterir. 32k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "Doubao-pro-4k": {"description": "En etkili ana model o<PERSON><PERSON>, karmaşık görevlerin işlenmesi için uygundur. <PERSON><PERSON><PERSON> soru-ceva<PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ya<PERSON><PERSON>, metin s<PERSON>, rol yapma gibi senaryolarda mükemmel performans gösterir. 4k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "DreamO": {"description": "DreamO, ByteDance ve Pekin Üniversitesi iş birliğiyle geliştirilen açık kaynaklı, çok görevli görüntü üretim modelidir. Birleşik mimari sayesinde kullanıcı tarafından belirtilen kimlik, konu, stil, arka plan gibi çoklu koşullara göre yüksek tutarlılıkta ve özelleştirilmiş görüntüler oluşturabilir."}, "ERNIE-3.5-128K": {"description": "Baidu'nun kendi gel<PERSON>, b<PERSON>yük ölçekli bir dil modeli olan ERNIE-3.5, geniş bir Çin ve İngilizce veri kümesini kapsar. Güçlü genel yeteneklere sahip olup, <PERSON><PERSON><PERSON><PERSON>, soru<PERSON>c<PERSON><PERSON>, yaratıcı içerik üretimi ve eklenti uygulama senaryolarını karşılayabilir; ayrıca, Baidu arama eklentisi ile otomatik entegrasyonu destekleyerek, soru-cevap bilgilerinin güncelliğini sağlar."}, "ERNIE-3.5-8K": {"description": "Baidu'nun kendi gel<PERSON>, b<PERSON>yük ölçekli bir dil modeli olan ERNIE-3.5, geniş bir Çin ve İngilizce veri kümesini kapsar. Güçlü genel yeteneklere sahip olup, <PERSON><PERSON><PERSON><PERSON>, soru<PERSON>c<PERSON><PERSON>, yaratıcı içerik üretimi ve eklenti uygulama senaryolarını karşılayabilir; ayrıca, Baidu arama eklentisi ile otomatik entegrasyonu destekleyerek, soru-cevap bilgilerinin güncelliğini sağlar."}, "ERNIE-3.5-8K-Preview": {"description": "Baidu'nun kendi gel<PERSON>, b<PERSON>yük ölçekli bir dil modeli olan ERNIE-3.5, geniş bir Çin ve İngilizce veri kümesini kapsar. Güçlü genel yeteneklere sahip olup, <PERSON><PERSON><PERSON><PERSON>, soru<PERSON>c<PERSON><PERSON>, yaratıcı içerik üretimi ve eklenti uygulama senaryolarını karşılayabilir; ayrıca, Baidu arama eklentisi ile otomatik entegrasyonu destekleyerek, soru-cevap bilgilerinin güncelliğini sağlar."}, "ERNIE-4.0-8K-Latest": {"description": "Baidu'nun kendi geliştirdiği amiral gemisi ultra büyük ölçekli dil modeli, ERNIE 3.5'e kıyasla model yet<PERSON><PERSON><PERSON><PERSON> kapsamlı bir yükseltme gerçek<PERSON>ş<PERSON>r, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyonu destekler, yanıt bilgilerini güncel tutar."}, "ERNIE-4.0-8K-Preview": {"description": "Baidu'nun kendi geliştirdiği amiral gemisi ultra büyük ölçekli dil modeli, ERNIE 3.5'e kıyasla model yet<PERSON><PERSON><PERSON><PERSON> kapsamlı bir yükseltme gerçek<PERSON>ş<PERSON>r, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyonu destekler, yanıt bilgilerini güncel tutar."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Baidu tarafından geli<PERSON>rilen, g<PERSON><PERSON> ölçekli büyük dil modeli, genel performansı mükemmeldir ve her alanda karmaşık görev sahneleri için geniş bir şekilde kullanılabilir; Baidu arama eklentisi ile otomatik entegrasyonu destekler, yanıt bilgi güncellemelerinin zamanlamasını güvence altına alır. ERNIE 4.0'a kıyas<PERSON>, performans olarak daha üstündür."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Baidu'nun kendi geliştirdiği amiral gemisi ultra büyük ölçekli dil modeli, genel performansı müke<PERSON>l olup, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; <PERSON><PERSON> arama eklentisi ile otomatik entegrasyonu destekler, yanıt bilgilerini güncel tutar. ERNIE 4.0'a kıyasla performans açısından daha üstündür."}, "ERNIE-Character-8K": {"description": "Baidu'nun kendi geliştirdiği dikey senaryo büyük dil modeli, oyun NPC'leri, müşteri hizmetleri diyalogları, diyalog karakter rolü gibi uygulama senaryoları iç<PERSON> u<PERSON>, karakter tarzı daha belirgin ve tutarlıdır, talimatları takip etme yeteneği daha güçlüdür ve çıkarım performansı daha iyidir."}, "ERNIE-Lite-Pro-128K": {"description": "Baidu'nun kendi gel<PERSON>ş<PERSON>rdiğ<PERSON> ha<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> dil modeli, mükemmel model performansı ve çıkarım <PERSON> den<PERSON>, ERNIE Lite'dan daha iyi sonuçlar verir, düş<PERSON>k hesaplama gücüne sahip AI hızlandırıcı kartları için uygundur."}, "ERNIE-Speed-128K": {"description": "Baidu'nun 2024 yılında piyasaya sürdüğü kendi geliştirdiği yüksek performanslı büyük dil modeli, genel yetenekleri müke<PERSON>l olu<PERSON>, beli<PERSON><PERSON> se<PERSON>o so<PERSON>nı daha iyi işlemek için temel model olarak ince ayar yapmak için uygundur ve mükemmel çıkarım performansına sahiptir."}, "ERNIE-Speed-Pro-128K": {"description": "Baidu'nun 2024 yılında piyasaya sürdüğü kendi geliştirdiği yüksek performanslı büyük dil modeli, genel yetenekleri müke<PERSON>l <PERSON>, ERNIE Speed'den daha iyi sonuçlar verir, beli<PERSON><PERSON> se<PERSON>o so<PERSON>nı daha iyi işlemek için temel model olarak ince ayar yapmak için uygundur ve mükemmel çıkarım performansına sahiptir."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Black Forest Labs tarafından geliştirilen, Rectified Flow Transformer mimarisine dayanan çok modlu görüntü oluşturma ve düzenleme modelidir. 12 milyar parametreye sahip olup, verilen ba<PERSON><PERSON> koşullarında gör<PERSON><PERSON><PERSON> oluşturma, yeni<PERSON> yapılandırma, iyileştirme ve düzenleme işlemlerine odaklanır. Model, difüzyon modellerinin kontrollü üretim avantajlarını ve Transformer'ın bağlam modelleme yeteneklerini birleştirerek yüksek kaliteli görüntü çıktısı sağlar ve görüntü onarımı, ta<PERSON><PERSON><PERSON>a, gö<PERSON>l sahne yeniden yapılandırma gibi görevlerde geniş uygulama alanına sahiptir."}, "FLUX.1-dev": {"description": "FLUX.1-dev, Black Forest Labs tarafından geliştirilen açık kaynaklı çok modlu dil modelidir (Multimodal Language Model, MLLM). Görüntü ve metin anlama ile üretim yeteneklerini birleştirerek görsel ve metin görevleri için optimize edilmiştir. Mistral-7B gibi gelişmiş büyük dil modelleri temel alınarak, özenle tasarlanmış görsel kodlayıcı ve çok aşamalı talimat ince ayarı ile görsel-metinsel işbirliği ve karmaşık görev çıkarımı sağlar."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B), çok alanlı uygulamalar ve karmaşık görevler için uygun yenilikçi bir modeldir."}, "HelloMeme": {"description": "HelloMeme, sağladığın<PERSON>z resim veya hareketlere dayanarak otomatik olarak meme, GIF veya kısa video oluşturabilen bir yapay zeka aracıdır. Hiçbir çizim veya programlama bilgisi gerektirmez; sadece referans resim hazırlamanız yeterlidir, b<PERSON><PERSON>ce size güzel, eğlenceli ve tutarlı tarzda içerikler oluşturur."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full, ZhiXiang Future (HiDream.ai) tarafından geliştirilen açık kaynaklı çok modlu görüntü düzenleme büyük modelidir. Gelişmiş Diffusion Transformer mimarisi ve güçlü dil anlama yeteneği (gömülü LLaMA 3.1-8B-Instruct) ile doğal dil komutlarıyla görüntü oluşturma, stil transferi, yerel düzenleme ve içerik yeniden çizim desteği sunar; üstün görsel-metinsel anlama ve yürütme kabiliyetine sahiptir."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled, dam<PERSON><PERSON><PERSON> optimizasyonu ile hafifletilmiş, hızlı yüksek kaliteli görüntü üretebilen bir metinden görüntüye modeldir. Özellikle düşük kaynaklı ortamlar ve gerçek zamanlı üretim görevleri için uygundur."}, "InstantCharacter": {"description": "InstantC<PERSON>cter, Tencent AI ekibi tarafından 2025 yılında yayınlanan, ince ayar gerektirmeyen (tuning-free) kişiselleştirilmiş karakter oluşturma modelidir. Yüksek doğrulukta ve sahneler arası tutarlı karakter üretmeyi hedefler. Sadece bir referans görüntüye dayanarak karakter modellemesi yapabilir ve bu karakteri farklı stiller, hareketler ve arka planlara esnek şekilde taşıyabilir."}, "InternVL2-8B": {"description": "InternVL2-8B, güçlü bir görsel dil modelidir. Görüntü ve metinlerin çok modlu işlenmesini destekler, görünt<PERSON> içeriğini hassas bir şekilde tanıyabilir ve ilgili açıklamalar veya yanıtlar üretebilir."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B, gü<PERSON>lü bir görsel dil modelidir. Görüntü ve metinlerin çok modlu işlenmesini destekler, gör<PERSON>nt<PERSON> içeriğini hassas bir şekilde tanıyabilir ve ilgili açıklamalar veya yanıtlar üretebilir."}, "Kolors": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Kolors ekibi tarafından geliştirilen metinden görüntüye modeldir. Milyarlarca parametre ile eğitilmiş olup, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> anlamsal anlama ve metin işleme konularında belirgin avantajlara sahiptir."}, "Kwai-Kolors/Kolors": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Kolors ekibi tarafından geliştirilen, latent difüzyon tabanlı büyük ölçekli metinden görüntüye üretim modelidir. Milyarlarca metin-görüntü çiftinden eğitilerek görsel kalite, karmaşık anlamsal doğruluk ve Çince-İngilizce karakter işleme alanlarında üstün performans gösterir. Hem Çince hem İngilizce girişleri destekler ve özellikle Çince içerik anlama ve üretiminde başarılıdır."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Yüksek çözünürlüklü görüntülerde mükemmel görüntü akıl yür<PERSON><PERSON><PERSON>, gö<PERSON>l anlama uygulamaları için uygundur."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Görsel anlama ajan u<PERSON>gulamaları için gelişmiş görüntü akıl yürütme yeteneği."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 talimat ayarlı metin modeli, çok dilli diyalog kullanım durumları için optimize edilmiştir ve birçok mevcut açık kaynak ve kapalı sohbet modelinde yaygın endüstri kıyaslamalarında mükemmel performans göstermektedir."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 talimat ayarlı metin modeli, çok dilli diyalog kullanım durumları için optimize edilmiştir ve birçok mevcut açık kaynak ve kapalı sohbet modelinde yaygın endüstri kıyaslamalarında mükemmel performans göstermektedir."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 talimat ayarlı metin modeli, çok dilli diyalog kullanım durumları için optimize edilmiştir ve birçok mevcut açık kaynak ve kapalı sohbet modelinde yaygın endüstri kıyaslamalarında mükemmel performans göstermektedir."}, "Meta-Llama-3.2-1B-Instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, en son teknolojiye sahip küçük dil modeli, dil an<PERSON>, müke<PERSON>l akıl yür<PERSON>t<PERSON> yeteneği ve metin oluşturma yeteneğine sahiptir."}, "Meta-Llama-3.2-3B-Instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, en son teknolojiye sahip küçük dil modeli, dil an<PERSON>, müke<PERSON>l akıl yür<PERSON>t<PERSON> yeteneği ve metin oluşturma yeteneğine sahiptir."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3, Llama serisinin en gelişmiş çok dilli açık kaynak büyük dil modelidir ve 405B modelinin performansını çok düşük maliyetle deneyimlemenizi sağlar. Transformer yapısına dayanmaktadır ve yararlılığını ve güvenliğini artırmak için denetimli ince ayar (SFT) ve insan geri bildirimi ile güçlendirilmiş öğrenme (RLHF) kullanılmıştır. Talimat ayarlı versiyonu çok dilli diyaloglar için optimize edilmiştir ve birçok endüstri kıyaslamasında birçok açık kaynak ve kapalı sohbet modelinden daha iyi performans göstermektedir. Bilgi kesim tarihi 2023 yılı Aralık ayıdır."}, "MiniMax-M1": {"description": "Tamamen kendi geliştirdiğimiz yeni çıkarım modeli. Küresel lider: 80K düşünce zinciri x 1M giriş, performansı uluslararası üst düzey modellerle eşdeğer."}, "MiniMax-Text-01": {"description": "MiniMax-01 serisi modellerinde cesur yenilikler yaptık: ilk kez büyük ölçekli lineer dikkat mekanizmasını gerçekleştirdik, geleneksel Transformer mimarisi artık tek seçenek değil. Bu modelin parametre sayısı 456 milyara kadar çıkmakt<PERSON>, tek bir aktivasyonda 45.9 milyar. Modelin genel performansı, yurtdışındaki en iyi modellerle karşılaştırılabilirken, dünya genelinde 4 milyon token uzunluğundaki bağlamı verimli bir şekilde işleyebilir, bu da GPT-4o'nun 32 katı, Claude-3.5-Sonnet'in 20 katıdır."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1, açık kaynak ağırlıklı büyük ölçekli karma dikkat çıkarım modeli olup, 456 milyar parametreye sahiptir ve her Token yaklaşık 45.9 milyar parametreyi aktive eder. Model, doğal olarak 1 milyon Token uzunluğunda bağlamı destekler ve şimşek dikkat mekanizması sayesinde 100 bin Token üretim görevlerinde DeepSeek R1'e kıyasla %75 daha az kayan nokta işlemi kullanır. Ayrıca, MiniMax-M1 MoE (karışık uzman) mimarisini, CISPO algoritması ve karma dikkat tasarımı ile verimli pekiştirmeli öğrenme eğitimiyle birleştirerek uzun giriş çıkarımı ve gerçek yazılım mühendisliği senaryolarında sektör lideri performans sunar."}, "Moonshot-Kimi-K2-Instruct": {"description": "Toplam 1 trilyon parametre, 32 milyar aktif parametreye sahip. Düşünme modeli olmayanlar arasında, <PERSON><PERSON><PERSON><PERSON>, matematik ve kodlama alanlarında en üst düzeyde performans gösterir ve genel ajan görevlerinde daha yetkindir. <PERSON><PERSON> göre<PERSON> için optimize edilmiştir; sadece soruları yanıtlamakla kalmaz, aynı zamanda eylem de gerçekleştirebilir. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, genel sohbet ve ajan deneyimleri için en uygunudur; uzun düşünme gerektirmeyen refleks seviyesinde bir modeldir."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - <PERSON>tral 8x7B-D<PERSON> (46.7B), <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>sa<PERSON><PERSON><PERSON> i<PERSON><PERSON> yü<PERSON><PERSON> hassasiyetli bir talimat modelidir."}, "OmniConsistency": {"description": "OmniConsistency, büyük ölçekli Difüzyon Transformerlar (DiTs) ve eşleştirilmiş stilize veri kullanarak görüntüden görüntüye (Image-to-Image) görevlerinde stil tutarlılığı ve genelleme yet<PERSON>ğini artırır, stil bozulmasın<PERSON> önler."}, "Phi-3-medium-128k-instruct": {"description": "Aynı Phi-3-medium modeli, ancak RAG veya az sayıda örnek isteme için daha büyük bir bağlam boyutuna sahiptir."}, "Phi-3-medium-4k-instruct": {"description": "14B parametreli bir model, Phi-3-mini'den daha iyi kalite sunar, <PERSON><PERSON><PERSON><PERSON> ka<PERSON>, a<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoğ<PERSON> veriye odaklanır."}, "Phi-3-mini-128k-instruct": {"description": "Aynı Phi-3-mini modeli, ancak RAG veya az sayıda örnek isteme için daha büyük bir bağlam boyutuna sahiptir."}, "Phi-3-mini-4k-instruct": {"description": "Phi-3 ailesinin en küçük üyesi. Hem kalite hem de düşük gecikme için optimize edilmiştir."}, "Phi-3-small-128k-instruct": {"description": "Aynı Phi-3-small modeli, ancak RAG veya az sayıda örnek isteme için daha büyük bir bağlam boyutuna sahiptir."}, "Phi-3-small-8k-instruct": {"description": "7B parametreli bir model, Phi-3-mini'den daha iyi kalite sunar, <PERSON><PERSON><PERSON><PERSON> ka<PERSON>, a<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoğ<PERSON> veriye odaklanır."}, "Phi-3.5-mini-instruct": {"description": "Phi-3-mini model<PERSON><PERSON> güncellenmiş versiyonu."}, "Phi-3.5-vision-instrust": {"description": "Phi-3-<PERSON><PERSON><PERSON><PERSON> model<PERSON>n güncellenmiş versiyonu."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct, Qwen2 serisindeki talimat ince ayar büyük dil modelidir ve parametre ölçeği 7B'dir. Bu model, Transformer mimarisi temelinde, SwiGLU aktivasyon fonksiyonu, dikkat QKV önyargısı ve grup sorgu dikkati gibi teknikler kullanmaktadır. Büyük ölçekli girişleri işleyebilme yeteneğine sahiptir. Bu model, dil anlam<PERSON>, <PERSON>retim, çok dilli yetenek, kodlam<PERSON>, matematik ve akıl yürütme gibi birçok standart testte mükemmel performans sergilemekte ve çoğu açık kaynak modelini geride bırakmakta, bazı görevlerde özel modellere karşı rekabet edebilir. Qwen2-7B-Instruct, birçok değerlendirmede Qwen1.5-7B-Chat'ten daha iyi performans göstermekte ve belirgin bir performans artışı sergilemektedir."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-<PERSON><PERSON><PERSON><PERSON>, Alibaba Cloud tarafından yayınlanan en son b<PERSON><PERSON><PERSON><PERSON> dil modeli serilerinden biridir. Bu 7B modeli, kodlama ve matematik gibi alanlarda önemli ölçüde geliştirilmiş yeteneklere sahiptir. Model ayr<PERSON><PERSON>, <PERSON><PERSON><PERSON>, İngilizce gibi 29'dan fazla dili kapsayan çok dilli destek sunmaktadır. Model, talimat takibi, yapılandırılmış verileri anlama ve yapılandırılmış çıktı (özellikle JSON) üretme konularında önemli iyileştirmeler göstermektedir."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-<PERSON><PERSON><PERSON><PERSON>, Alibaba Cloud tarafından yayınlanan kod odaklı büyük dil modeli serisinin en son versiyonudur. Bu model, Qwen2.5 temelinde, 5.5 trilyon token ile eğitilerek kod üretimi, akıl yürütme ve düzeltme yeteneklerini önemli ölçüde artırmıştır. Hem kodlama yeteneklerini geliştirmiş hem de matematik ve genel yetenek avantajlarını korumuştur. Model, kod akıllı ajanları gibi pratik uygulamalar için daha kapsamlı bir temel sunmaktadır."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-<PERSON><PERSON>, <PERSON>wen serisinin yeni üyesidir ve güçlü görsel anlama yeteneğine sahiptir. Görsellerdeki metinleri, grafikleri ve düzenleri analiz edebilir, uzun videoları anlayabilir ve olayları yakalayabilir. Akıl yürütme yapabilir, araçları kullanabilir, çoklu format nesne konumlandırmayı destekler ve yapılandırılmış çıktılar üretebilir. Video anlama için dinamik çözünürlük ve kare hızı eğitimini optimize etmiş ve görsel kodlayıcı verimliliğini artırmıştır."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking, Zhipu AI ve Tsinghua Üniversitesi KEG Laboratuvarı tarafından ortaklaşa yayınlanan açık kaynaklı bir görsel dil modeli (VLM) olup, karmaşık çok modlu bilişsel görevleri işlemek için tasarlanmıştır. Bu model, GLM-4-9B-0414 temel modeli üzerine kurulmuş olup, \"Düşünce Zinciri\" (Chain-of-Thought) akıl yürütme mekanizmasını ve pekiştirmeli öğrenme stratejisini benimseyerek, modlar arası akıl yürütme yeteneği ve kararlılığını önemli ölçüde artırmıştır."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-<PERSON><PERSON>, Zhipu AI tarafından sunulan GLM-4 seris<PERSON> önceden eğitilmiş modellerin açık kaynak versiyonudur. Bu model, anlam, matematik, ak<PERSON>l yü<PERSON><PERSON><PERSON><PERSON>, kod ve bilgi gibi birçok alanda mükemmel performans sergilemektedir. Çoklu diyalogları desteklemenin yanı sıra, GLM-4-9B-<PERSON><PERSON>, web tarayıcı, kod yürütme, özelleştirilmiş araç çağrısı (Function Call) ve uzun metin akıl yürütme gibi gelişmiş özelliklere de sahiptir. Model, <PERSON><PERSON><PERSON>, İng<PERSON>zce, Japonca, Korece ve Almanca gibi 26 dili desteklemektedir. GLM-4-9B-<PERSON><PERSON>, AlignBench-v2, MT-Bench, MMLU ve C-Eval gibi birçok standart testte mükemmel performans sergilemiştir. Bu model, maksimum 128K bağlam uzunluğunu desteklemekte olup, akademik araştırmalar ve ticari uygulamalar için uygundur."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1, model<PERSON><PERSON> tekrarlılık ve okunabilirlik sorunlarını çözen bir güçlendirilmiş öğrenme (RL) destekli çıkarım modelidir. RL'den önce, DeepSeek-R1 soğuk başlangıç verileri tanıtarak çıkarım performansını daha da optimize etmiştir. Matematik, kod ve çıkarım görevlerinde OpenAI-o1 ile benzer performans göstermektedir ve özenle tasarlanmış eğitim yöntemleri ile genel etkisini artırmıştır."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-7B, Qwen2.5-Math-7B modelinden bilgi damıtma yöntemiyle elde edilmiş bir modeldir. Bu model, DeepSeek-R1 tarafından oluşturulan 800 bin seçkin örnekle ince ayar yapılarak geliştirilmiş olup, üstün akıl yürütme yeteneği sergilemektedir. Çeşitli kıyaslama testlerinde başarılı performans gösteren model, MATH-500'de %92,8 do<PERSON><PERSON>luk, AIME 2024'te %55,5 geçme oranı ve CodeForces'ta 1189 puan alarak, 7B ölçeğindeki bir model için güçlü matematik ve programlama yeteneklerini ortaya koymuştur."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3, 6710 milyar parametreye sahip bir karma uzman (MoE) dil modelidir ve çok başlı potansi<PERSON><PERSON> di<PERSON> (MLA) ve DeepSeekMoE mimarisini kull<PERSON>rak, yardımcı kayıplar olmadan yük dengeleme stratejileri ile çıkarım ve eğitim verimliliğini optimize etmektedir. 14.8 trilyon yüksek kaliteli token üzerinde önceden eğitilmiş ve denetimli ince ayar ve güçlendirilmiş öğrenme ile, DeepSeek-V3 performans açısından diğer açık kaynak modelleri geride bırakmakta ve lider kapalı kaynak modellere yaklaşmaktadır."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "<PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON> kodlama ve ajan yeteneklerine sahip MoE mimarili temel modeldir; toplam 1 trilyon parametre, 32 milyar aktif parametreye sahiptir. <PERSON><PERSON> bi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, matematik ve ajan gibi ana kategorilerdeki kıyaslama testlerinde K2 modeli diğer önde gelen açık kaynak modelleri geride bırakır."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview, karmaşık diyalog oluşturma ve bağlam anlama görevlerini etkili bir şekilde işleyebilen yenilikçi bir doğal dil işleme modelidir."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview, Qwen ekibi tarafından geliştirilen ve görsel çıkarım yeteneklerine odaklanan bir araştırma modelidir. Karmaşık sahne anlayışı ve görsel ile ilgili matematiksel sorunları çözme konusundaki benzersiz avantajları ile dikkat çekmektedir."}, "Qwen/QwQ-32B": {"description": "QwQ, <PERSON>wen serisinin çıkarım modelidir. Geleneksel talimat ayarlama modellerine kıyasla, QwQ düşünme ve çıkarım yeteneğine sahiptir ve özellikle zor problemleri çözme konusunda önemli ölçüde artırılmış performans sergileyebilir. QwQ-32B, orta ölçekli bir çıkarım modelidir ve en son çıkarım modelleri (örneğin, DeepSeek-R1, o1-mini) ile karşılaştırıldığında rekabetçi bir performans elde edebilir. Bu model, RoPE, SwiGLU, RMSNorm ve Attention QKV bias gibi teknikleri kullanmakta olup, 64 katmanlı bir ağ yapısına ve 40 Q dikkat başlığına (GQA mimarisinde KV 8'dir) sahiptir."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview, Qwen'in en son deneysel araştırma modelidir ve AI akıl yürütme yeteneklerini artırmaya odaklanmaktadır. <PERSON><PERSON>, özyinelemeli akıl yürütme gibi karmaşık mekanizmaları keşfederek, g<PERSON><PERSON><PERSON>ü akıl yürütme analizi, matematik ve programlama yetenekleri gibi ana avantajlar sunmaktadır. <PERSON><PERSON><PERSON><PERSON> birl<PERSON>, dil ge<PERSON><PERSON><PERSON> sorunları, akıl yür<PERSON>t<PERSON> döngüleri, güvenlik endişeleri ve diğer yetenek farklılıkları gibi zorluklar da bulunmaktadır."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2, çok çeşitli talimat türlerini destekleyen gelişmiş bir genel dil modelidir."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct, Qwen2 serisindeki talimat ince ayar büyük dil modelidir ve parametre ölçeği 72B'dir. Bu model, Transformer mimarisi temelinde, SwiGLU aktivasyon fonksiyonu, dikkat QKV önyargısı ve grup sorgu dikkati gibi teknikler kullanmaktadır. Büyük ölçekli girişleri işleyebilme yeteneğine sahiptir. Bu model, dil anlam<PERSON>, <PERSON>retim, çok dilli yetenek, kodlam<PERSON>, matematik ve akıl yürütme gibi birçok standart testte mükemmel performans sergilemekte ve çoğu açık kaynak modelini geride bırakmakta, bazı görevlerde özel modellere karşı rekabet edebilir."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-V<PERSON>, Qwen-VL modelinin en son yine<PERSON>e versiyonudur ve görsel anlama kıyaslama testlerinde en gelişmiş performansı sergilemiştir."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5, talimat tabanlı görevlerin işlenmesini optimize etmek için tasarlanmış yeni bir büyük dil modeli serisidir."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5, talimat tabanlı görevlerin işlenmesini optimize etmek için tasarlanmış yeni bir büyük dil modeli serisidir."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Alibaba Cloud <PERSON> ekibi tarafından geliştirilen büyük bir dil modeli"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5, <PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> anlama ve üretim <PERSON> ile yeni bir büyük dil modeli serisidir."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5, komut tabanlı görevlerin işlenmesini optimize etmek için tasarlanmış yeni bir büyük dil modeli serisidir."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5, talimat tabanlı görevlerin işlenmesini optimize etmek için tasarlanmış yeni bir büyük dil modeli serisidir."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5, komut tabanlı görevlerin işlenmesini optimize etmek için tasarlanmış yeni bir büyük dil modeli serisidir."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-<PERSON><PERSON>, kod ya<PERSON>ı<PERSON><PERSON><PERSON> o<PERSON>lanmaktadır."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-<PERSON><PERSON><PERSON><PERSON>, Alibaba Cloud tarafından yayınlanan kod odaklı büyük dil modeli serisinin en son versiyonudur. Bu model, Qwen2.5 temelinde, 5.5 trilyon token ile eğitilerek kod üretimi, akıl yürütme ve düzeltme yeteneklerini önemli ölçüde artırmıştır. Hem kodlama yeteneklerini geliştirmiş hem de matematik ve genel yetenek avantajlarını korumuştur. Model, kod akıllı ajanları gibi pratik uygulamalar için daha kapsamlı bir temel sunmaktadır."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-<PERSON><PERSON><PERSON><PERSON>, Tongyi Qianwen ekibi tarafından geliştirilen çok modelli bir büyük modeldir ve Qwen2.5-VL serisinin bir parçasıdır. Bu model yalnızca yaygın nesneleri tanımakla kalmaz, aynı zamanda görüntülerdeki metinleri, tab<PERSON>ları, simgeleri, grafikleri ve düzenleri analiz edebilir. Görsel bir akıllı ajan olar<PERSON> çalışabilir, araçları dinamik olarak yönetebilir ve bilgisayar ile telefon kullanma yeteneğine sahiptir. Ayrıca, bu model görüntülerdeki nesneleri hassas bir şekilde konumlandırabilir ve fatura, tablo gibi belgeler için yapılandırılmış çıktılar üretebilir. Önceki model Qwen2-V<PERSON>'ye kıyasla, bu sürüm matematik ve problem çözme yeteneklerinde pekiştirmeli öğrenme ile daha da geliştirilmiştir ve yanıt tarzı insan tercihlerine daha uygun hale getirilmiştir."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL, Qwen2.5 serisindeki görsel-dil modelidir. Bu model birçok alanda önemli gelişmeler sunmaktadır: Gelişmiş görsel anlama yeteneğiyle yaygın nesneleri tanıyabilir, met<PERSON><PERSON><PERSON>, gra<PERSON><PERSON>ri ve düzenleri analiz edebilir; görsel bir ajan olarak akıl yürütebilir ve araç kullanımını dinamik olarak yönlendirebilir; 1 saati aşan uzun videoları anlayabilir ve önemli olayları yakalayabilir; görüntülerdeki nesneleri sınırlayıcı kutular veya noktalar oluşturarak hassas bir şekilde konumlandırabilir; yapılandırılmış çıktılar üretebilir, özellikle fatura, tablo gibi taranmış veriler için uygundur."}, "Qwen/Qwen3-14B": {"description": "Qwen<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gene<PERSON>, <PERSON><PERSON> ve çok dilli gibi birçok temel yetenekte önemli ölçüde geliştirilmiş yeni nesil Tongyi Qianwen büyük modelidir ve düşünme modu geçişini destekler."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gene<PERSON>, <PERSON><PERSON> ve çok dilli gibi birçok temel yetenekte önemli ölçüde geliştirilmiş yeni nesil Tongyi Qianwen büyük modelidir ve düşünme modu geçişini destekler."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3 seris<PERSON><PERSON>, Alibaba Cloud Tongyi Qianwen ekibi tarafından geliştirilen amiral gemisi hibrit uzman (MoE) büyük dil modelidir. Toplam 235 milyar parametreye, her çıkarımda 22 milyar aktif parametreye sahiptir. Qwen3-235B-A22B'nin düşünme modu olmayan güncellenmiş versiyonudur; talimat uyumu, mantıksal çıkarım, metin anlam<PERSON>, matematik, bilim, programlama ve araç kullanımı gibi genel yeteneklerde önemli iyileştirmeler sunar. Ayrıca çok dilli uzun kuyruk bilgisi kapsamını artırır ve kullanıcıların öznel ve açık uçlu görev tercihlerine daha iyi uyum sağlayarak daha faydalı ve kaliteli metinler üretir."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Alibaba Tongyi Qianwen ekibi tarafından geliştirilen Qwen3 serisinden büyük dil modelidir ve karmaşık yüksek zorlukta çıkarım görevlerine odaklanır. MoE mimarisi temel alınmış olup toplam 235 milyar parametreye sahiptir; her token işlenirken yaklaşık 22 milyar parametre aktif olur, bö<PERSON>ce güçlü performansla birlikte hesaplama verimliliği sağlanır. <PERSON><PERSON> bir \"düşünme\" modeli olarak, mantıks<PERSON> çıkarım, matematik, bilim, programlama ve akademik kıyaslama testlerinde insan uzmanlığı gerektiren görevlerde üstün performans gösterir ve açık kaynak düşünme modelleri arasında en üst seviyededir. Ayrıca talimat uyumu, araç kullanımı ve metin üretimi gibi genel yetenekleri geliştirir ve 256K uzun bağlam anlama desteği ile derin çıkarım ve uzun belge işleme senaryoları için idealdir."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gene<PERSON>, <PERSON><PERSON> ve çok dilli gibi birçok temel yetenekte önemli ölçüde geliştirilmiş yeni nesil Tongyi Qianwen büyük modelidir ve düşünme modu geçişini destekler."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507, Qwen3-30B-A3B'nin dü<PERSON>ünme modu olmayan güncellenmiş bir versiyonudur. Bu, toplam 30,5 milyar parametre ve 3,3 milyar aktif parametreye sahip bir <PERSON><PERSON> (MoE) modelidir. Model, ta<PERSON><PERSON> taki<PERSON>, mant<PERSON>ks<PERSON> akıl yü<PERSON><PERSON><PERSON>, metin anlam<PERSON>, matemat<PERSON>, bilim, kodlama ve araç kullanımı gibi genel yeteneklerde önemli geliştirmeler içermektedir. Ayrıca, çok dilli uzun kuyruk bilgi kapsamı açısından kayda değer ilerlemeler kaydetmiş ve kullanıcıların öznel ve açık uçlu görevlerdeki tercihlerine daha iyi uyum sağlayarak daha faydalı yanıtlar ve daha yüksek kaliteli metinler üretebilmektedir. <PERSON><PERSON> ek o<PERSON>, modelin uzun metin anlama kapasitesi 256K'ya kadar artırılmıştır. Bu model yaln<PERSON><PERSON><PERSON> düşünme modu dışındadır ve çıktılarında `<think></think>` etiketleri oluşturmaz."}, "Qwen/Qwen3-32B": {"description": "Qwen<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gene<PERSON>, <PERSON><PERSON> ve çok dilli gibi birçok temel yetenekte önemli ölçüde geliştirilmiş yeni nesil Tongyi Qianwen büyük modelidir ve düşünme modu geçişini destekler."}, "Qwen/Qwen3-8B": {"description": "Qwen<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gene<PERSON>, <PERSON><PERSON> ve çok dilli gibi birçok temel yetenekte önemli ölçüde geliştirilmiş yeni nesil Tongyi Qianwen büyük modelidir ve düşünme modu geçişini destekler."}, "Qwen2-72B-Instruct": {"description": "Qwen2, <PERSON>wen modelinin en yeni serisidir ve 128k bağlamı destekler. Mevcut en iyi açık kaynak modellerle karşılaştırıldığında, Qwen2-72B doğal dil anlama, bil<PERSON>, kod, matematik ve çok dilli yetenekler açısından mevcut lider modelleri önemli ölçüde aşmaktadır."}, "Qwen2-7B-Instruct": {"description": "Qwen2, Qwen modelinin en yeni serisidir ve eşit ölçekli en iyi açık kaynak modelleri hatta daha büyük ölçekli modelleri aşabilmektedir. Qwen2 7B, birçok değerlendirmede belirgin bir avantaj elde etmiş, özellikle kod ve Çince anlama konusunda."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B, g<PERSON><PERSON><PERSON>nt<PERSON> ve metin için çok modlu işleme desteği sunan güçlü bir görsel dil modelidir, gör<PERSON><PERSON><PERSON> içeriğini hassas bir şekilde tanıyabilir ve ilgili açıklamalar veya yanıtlar üretebilir."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct, 14 milyar parametreye sahip büyük bir dil modelidir. Performansı mü<PERSON><PERSON><PERSON>, <PERSON><PERSON>ce ve çok dilli senaryoları optimize eder, ak<PERSON><PERSON><PERSON> soru-cevap, içerik üretimi gibi uygulamaları destekler."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Inst<PERSON><PERSON>, 32 milyar parametreye sahip büyük bir dil modelidir. Performans den<PERSON>, <PERSON><PERSON>ce ve çok dilli senaryoları optimize eder, ak<PERSON>ll<PERSON> soru-cevap, içerik üretimi gibi uygulamaları destekler."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct, 16k bağlamı destekler ve 8K'dan uzun metinler üretebilir. Fonksiyon çağrısı ile dış sistemlerle sorunsuz etkile<PERSON><PERSON>, esneklik ve ölçeklenebilirliği büyük ölçüde artırır. Modelin bilgisi belirgin şekilde artmış ve kodlama ile matematik yetenekleri büyük ölçüde geliştirilmiştir, 29'dan fazla dil desteği sunmaktadır."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct, 7 milyar parametreye sahip büyük bir dil modelidir. Fonksiyon çağrısı ile dış sistemlerle sorunsuz etkileşim destekler, esneklik ve ölçeklenebilirliği büyük ölçüde artırır. Çince ve çok dilli senaryoları optimize eder, akıllı soru-cevap, içerik üretimi gibi uygulamaları destekler."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-In<PERSON><PERSON><PERSON>, büyük ölçekli önceden eğitilmiş bir programlama talimat modelidir, g<PERSON><PERSON><PERSON><PERSON> kod anlama ve üretme yeteneğ<PERSON>, çeşitli programlama görevlerini verimli bir şekilde işleyebilir, özellikle akıllı kod yazma, otomatik betik oluşturma ve programlama sorunlarına yanıt verme için uygundur."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-<PERSON><PERSON><PERSON><PERSON>, kod <PERSON><PERSON><PERSON><PERSON>, kod anlama ve verimli geliştirme senaryoları için tasarlanmış büyük bir dil modelidir. Sektördeki en ileri 32B parametre ölçeğini kullanarak çeşitli programlama ihtiyaçlarını karşılayabilir."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B, MoE (Hibrit Uzman Modeli) modelidir ve \"Hibrit Akıl Yürütme Modu\"nu tanıtmaktadır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"düşünme modu\" ile \"dü<PERSON>ünme modu dışı\" a<PERSON><PERSON><PERSON> kesintisiz geçiş yapmasını destekler, 119 dil ve lehçede anlama ve akıl yürütme yeteneğine sahiptir ve güçlü araç çağırma kapasitesine sahiptir. <PERSON><PERSON><PERSON><PERSON><PERSON> yetenekler, kod<PERSON><PERSON> ve matematik, çok dilli yetenekler, bilgi ve akıl yürütme gibi çeşitli kıyaslama testlerinde, DeepSeek R1, OpenAI o1, o3-mini, Grok 3 ve Google Gemini 2.5 Pro gibi piyasadaki önde gelen büyük modellerle rekabet edebilmektedir."}, "Qwen3-32B": {"description": "Qwen3-32B, <PERSON><PERSON><PERSON> Model (Dense Model) o<PERSON><PERSON> \"Hibrit Akıl Yürütme Modu\"nu tanıtmaktadır. Ku<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"düşünme modu\" ile \"dü<PERSON><PERSON>nme modu dışı\" aras<PERSON>nda kesintisiz geçiş yapmasını destekler. Model mi<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş eğitim verisi ve daha etkili eğitim yöntemleri sayesinde genel performansı Qwen2.5-72B ile karşılaştırılabilir düzeydedir."}, "SenseChat": {"description": "Temel sürüm model (V4), 4K bağlam uzunluğu ile genel yetenekleri güçlüdür."}, "SenseChat-128K": {"description": "Temel sürüm model (V4), 128K bağlam uzunluğu ile uzun metin anlama ve üretme görevlerinde mükemmel performans sergilemektedir."}, "SenseChat-32K": {"description": "Temel sürüm model (V4), 32K bağlam uzunluğu ile çeşitli senaryolarda esnek bir şekilde uygulanabilir."}, "SenseChat-5": {"description": "En son sür<PERSON><PERSON> model (V5.5), 128K bağlam uzunluğu, mate<PERSON><PERSON><PERSON> a<PERSON>ıl <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> diyalog, talimat takibi ve uzun metin anlama gibi alanlarda önemli gelişmeler göstermektedir ve GPT-4o ile karşılaştırılabilir."}, "SenseChat-5-1202": {"description": "V5.5 tabanlı en son s<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> sü<PERSON><PERSON><PERSON> kı<PERSON>la Çince ve İngilizce temel yetenekler, so<PERSON><PERSON>, fen bilimleri bilgisi, sosyal bilimler bilgisi, <PERSON><PERSON><PERSON><PERSON>, mate<PERSON><PERSON><PERSON> mantık ve kelime sayısı kontrolü gibi birçok alanda belirgin gelişmeler sunar."}, "SenseChat-5-Cantonese": {"description": "32K bağlam uzu<PERSON> ile, Kantonca diyalog anlama konusunda GPT-4'ü aşmakta, bi<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, matematik ve kod yazma gibi birçok alanda GPT-4 Turbo ile rekabet edebilmektedir."}, "SenseChat-5-beta": {"description": "Bazı performansları SenseCat-5-1202'den daha iyidir."}, "SenseChat-Character": {"description": "Standart sürüm model, 8K bağlam uzunluğu ile yüksek yanıt hızı sunmaktadır."}, "SenseChat-Character-Pro": {"description": "Gelişmiş sürüm model, 32K bağlam uzunluğu ile yetenekleri tamamen geliştirilmiş, Çince/İngilizce diyalogları desteklemektedir."}, "SenseChat-Turbo": {"description": "Hızlı soru-cevap ve model ince ayar senaryoları için uygundur."}, "SenseChat-Turbo-1202": {"description": "En son hafi<PERSON> ve<PERSON><PERSON> model<PERSON>, tam modelin %90'ından fazla yetenek sunar ve çıkarım maliyetini önemli ölçüde azaltır."}, "SenseChat-Vision": {"description": "En son versi<PERSON> modeli (V5.5), <PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> giri<PERSON>, modelin temel yetenek optimizasyonunu tamamen gerçekleştirir; ne<PERSON><PERSON> <PERSON><PERSON> tanıma, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> tan<PERSON>ma, <PERSON><PERSON><PERSON> an<PERSON>, <PERSON><PERSON><PERSON> tan<PERSON>, mant<PERSON><PERSON><PERSON> bilgi ç<PERSON>ımı ve metin anlama üretimi gibi alanlarda önemli gelişmeler sağlamıştır."}, "SenseNova-V6-5-Pro": {"description": "Çok modlu, dil ve akıl yürütme verilerinin kapsamlı güncellenmesi ve eğitim stratejilerinin optimize edilmesiyle, yeni model çok modlu akıl yürütme ve genel talimat takibi yeteneklerinde önemli gelişmeler sağlamıştır. 128k'ya kadar bağlam penceresini destekler ve OCR ile kültürel turizm IP tanıma gibi özel görevlerde üstün performans gösterir."}, "SenseNova-V6-5-Turbo": {"description": "Çok modlu, dil ve akıl yürütme verilerinin kapsamlı güncellenmesi ve eğitim stratejilerinin optimize edilmesiyle, yeni model çok modlu akıl yürütme ve genel talimat takibi yeteneklerinde önemli gelişmeler sağlamıştır. 128k'ya kadar bağlam penceresini destekler ve OCR ile kültürel turizm IP tanıma gibi özel görevlerde üstün performans gösterir."}, "SenseNova-V6-Pro": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin ve video yeteneklerinin yerel birliğini sağlar, geleneksel çok modlu ayrım sınırlamalarını aşar, OpenCompass ve SuperCLUE değerlendirmelerinde çift şampiyonluk kazanmıştır."}, "SenseNova-V6-Reasoner": {"description": "Görsel ve dil derin akıl yürütmesini bir araya get<PERSON>, yavaş düşünme ve derin akıl yürütmeyi gerçekleştirir, tam bir düşünce zinciri sürecini sunar."}, "SenseNova-V6-Turbo": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin ve video yeteneklerinin yerel birliğini sağlar, geleneksel çok modlu ayrım sınırlamalarını aşar, çoklu temel yetenekler, dil temel yetenekleri gibi ana boyutlarda kapsamlı bir şekilde önde gelir, hem edebi hem de mantıksal olarak dengelidir ve birçok değerlendirmede ulusal ve uluslararası birinci lig seviyesinde yer almıştır."}, "Skylark2-lite-8k": {"description": "Skylark'in (Bulut Şarkıcısı) ikinci nesil modeli, Skylark2-lite modeli yüksek yanıt hızı ile donatılmıştır; gerçek zamanlı talep gereksinimleri yüksek, maliyet duyarlı ve model hassasiyetine daha az ihtiyaç duyulan senaryolar için uygundur; bağlam pencere uzunluğu 8k'dır."}, "Skylark2-pro-32k": {"description": "Skylark'in (Bulut Şarkıcısı) i<PERSON><PERSON> ne<PERSON>l modeli, Skylark2-pro s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yüksek model hassa<PERSON><PERSON><PERSON> sa<PERSON>; profesy<PERSON>l alan metin <PERSON>, roman ya<PERSON>, yüks<PERSON> kaliteli çeviri gibi daha karmaşık metin üretim sahneleri için uygundur ve bağlam pencere uzunluğu 32k'dır."}, "Skylark2-pro-4k": {"description": "Skylark'in (Bulut Şarkıcısı) ikinci nesil modeli, Skylark2-pro modeli yüksek model has<PERSON><PERSON><PERSON><PERSON>; profesy<PERSON>l alan metin <PERSON>, roman ya<PERSON>, yüks<PERSON> kaliteli çeviri gibi daha karmaşık metin üretim sahneleri için uygundur ve bağlam pencere uzunluğu 4k'dır."}, "Skylark2-pro-character-4k": {"description": "Skylark'in (Bulut Şarkıcısı) ikinci nesil modeli, Skylark2-pro-character modeli, müke<PERSON>l rol yapma ve sohbet yeteneklerine sahiptir; kullan<PERSON><PERSON><PERSON>dan gelen istem taleplerine göre farklı roller üstlenme kabiliyeti ile sohbet edebilir. Rol stili belirgindir ve diyalog içeriği doğal ve akıcıdır. Chatbot, sanal asistan ve çevrimiçi müşteri hizmetleri gibi senaryolar için uygundur ve yüksek yanıt hızı vardır."}, "Skylark2-pro-turbo-8k": {"description": "Skylark'in (Bulut Şarkıcısı) i<PERSON><PERSON>l modeli, Skylark2-pro-turbo-8k ile daha hızlı çıkarım gerçekleştirir, maliyeti düşüktür ve bağlam pencere uzunluğu 8k'dır."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414, GLM serisinin yeni nesil açık kaynak modelidir ve 32 milyar parametreye sahiptir. Bu model, OpenAI'nin GPT serisi ve DeepSeek'in V3/R1 serisi ile karşılaştırılabilir performans sunar."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414, GLM serisinin küçük modelidir ve 9 milyar parametreye sahiptir. Bu model, GLM-4-32B serisinin teknik özelliklerini devralır, ancak daha hafif bir dağıtım seçeneği sunar. Boyutu daha küçük olmasına rağmen, GLM-4-9B-0414, kod oluşturma, web tasarımı, SVG grafik oluşturma ve arama tabanlı yazım gibi görevlerde mükemmel yetenekler sergiler."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking, Zhipu AI ve Tsinghua Üniversitesi KEG Laboratuvarı tarafından ortaklaşa yayınlanan açık kaynaklı bir görsel dil modeli (VLM) olup, karmaşık çok modlu bilişsel görevleri işlemek için tasarlanmıştır. Bu model, GLM-4-9B-0414 temel modeli üzerine kurulmuş olup, \"Düşünce Zinciri\" (Chain-of-Thought) akıl yürütme mekanizmasını ve pekiştirmeli öğrenme stratejisini benimseyerek, modlar arası akıl yürütme yeteneği ve kararlılığını önemli ölçüde artırmıştır."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414, derin dü<PERSON><PERSON><PERSON><PERSON> yeteneğine sahip bir çıkarım modelidir. Bu model, GLM-4-32B-0414 temel alınarak soğuk başlatma ve genişletilmiş pekiştirme öğrenimi ile geliştirilmiştir ve matematik, kod ve mantık görevlerinde daha fazla eğitim almıştır. Temel model ile karşılaştırıldığında, GLM-Z1-32B-0414, matematik yeteneklerini ve karmaşık görevleri çözme yeteneğini önemli ölçüde artırmıştır."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414, GLM serisinin küçük modelidir, yalnızca 9 milyar parametreye <PERSON>r, ancak açık kaynak geleneğini sürdürürken etkileyici yetenekler sergiler. <PERSON><PERSON><PERSON> daha küçük olmasına ra<PERSON>, bu model matematik çıkarımı ve genel görevlerde mükemmel performans gösterir, genel performansı eşit boyuttaki açık kaynak modeller arasında lider konumdadır."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414, derin düşünme yeteneğine sahip bir derin çıkarım modelidir (OpenAI'nin Derin Araştırması ile karşılaştırılabilir). Tipik derin düşünme modellerinin aksine, düşünme modeli daha uzun süreli derin düşünme ile daha açık ve karmaşık sorunları çözmektedir."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B açık kaynak versiyonu, diyalog uygulamaları için optimize edilmiş bir diyalog deneyimi sunar."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B, uzun bağlamlı büyük ölçekli akıl yürütme modeli (LRM) olup, pekiştirmeli öğrenme ile eğitilen ilk modeldir ve uzun metin akıl yürütme görevlerine optimize edilmiştir. Model, kade<PERSON><PERSON> bağlam genişletme pekiştirmeli öğrenme çerçevesiyle kısa bağlamdan uzun bağlama stabil geçiş sağlar. Yedi uzun bağlamlı belge soru-cevap kıyaslama testinde, QwenLong-L1-32B OpenAI-o3-mini ve Qwen3-235B-A22B gibi amiral gemisi modelleri geride bırakmış ve Claude-3.7-Sonnet-Thinking ile karşılaştırılabilir performans göstermiştir. Model özellikle matematiksel akıl yürütme, mantıksal akıl yürütme ve çok adımlı akıl yürütme gibi karmaşık görevlerde uzmandır."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, orijinal model se<PERSON><PERSON><PERSON> mü<PERSON>mmel genel dil yeteneklerini korurken, 500 milyar yüksek kaliteli token ile artımlı eğitim sayesinde matematiksel mantık ve kodlama yeteneklerini büyük ölçüde artırmıştır."}, "abab5.5-chat": {"description": "Üretkenlik senaryoları iç<PERSON>, karmaşık görev işleme ve verimli metin üretimini destekler, profesyonel alan u<PERSON>aları için uygundur."}, "abab5.5s-chat": {"description": "Çin karakter diyalog senaryoları için <PERSON>, <PERSON><PERSON><PERSON><PERSON> kaliteli Çin diyalog üretim yet<PERSON>ği sunar ve çeşitli uygulama senaryoları için uygundur."}, "abab6.5g-chat": {"description": "Çok dilli karakter diyalogları için <PERSON>, İngilizce ve diğer birçok dilde yüksek kaliteli diyalog üretimini destekler."}, "abab6.5s-chat": {"description": "<PERSON><PERSON>, diyalog sistemleri gibi geniş doğal dil işleme görevleri için uygundur."}, "abab6.5t-chat": {"description": "Çin karakter diyalog senaryoları için optimize edilmiş, akıcı ve Çin ifade alışkanlıklarına uygun diyalog üretim <PERSON>."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1, g<PERSON><PERSON>lendirilmiş öğrenme ve soğuk başlangıç verileri ile optimize edilmiş, müke<PERSON><PERSON> akıl yü<PERSON><PERSON><PERSON><PERSON>, matematik ve programlama performansına sahip en son teknoloji büyük bir dil modelidir."}, "accounts/fireworks/models/deepseek-v3": {"description": "Deepseek tarafından sunulan güçlü Mixture-of-Experts (MoE) dil modeli, toplamda 671B parametreye sahiptir ve her bir etiket için 37B parametre etkinleştirilmektedir."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Llama 3 70B talimat modeli, çok dilli diyalog ve doğal dil anlama için optimize edil<PERSON><PERSON><PERSON>r, <PERSON><PERSON><PERSON><PERSON> rakip modelden daha iyi performans gösterir."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Llama 3 8B talimat modeli, diyalog ve çok dilli görevler için optimize edilmiştir, mükemmel ve etkili performans sunar."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Llama 3 8B talimat modeli (HF versiyonu), resmi uygulama sonuçlarıyla uyumlu olup yüksek tutarlılık ve platformlar arası uyumluluk sunar."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Llama 3.1 405B talimat modeli, devasa parametreler ile karmaşık görevler ve yüksek yük senaryolarında talimat takibi için uygundur."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Llama 3.1 70B talimat modeli, m<PERSON><PERSON><PERSON><PERSON> doğal dil anlama ve üretim <PERSON>, diyalog ve analiz görevleri için idealdir."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Llama 3.1 8B talimat modeli, çok dilli diyaloglar için optimize edilmiştir ve yaygın endüstri standartlarını aşmaktadır."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Meta'nın 11B parametreli komut ayarlı görüntü akıl yürütme modelidir. Bu model, g<PERSON><PERSON><PERSON> tanıma, g<PERSON><PERSON><PERSON><PERSON><PERSON> akıl yürütme, g<PERSON><PERSON><PERSON><PERSON><PERSON> betimleme ve görüntü hakkında genel sorulara yanıt verme üzerine optimize edilmiştir. Bu model, grafikler ve resimler gibi görsel verileri anlayabilir ve görüntü detaylarını metin olarak betimleyerek görsel ile dil arasındaki boşluğu kapatır."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B komut modeli, <PERSON>a tarafından sunulan hafif çok dilli bir modeldir. Bu model, verimliliği artırmak amacıyla daha büyük modellere göre gecikme ve maliyet açısından önemli iyileştirmeler sunar. Bu modelin örnek kullanım alanları arasında sorgulama, öneri yeniden yazma ve yazma desteği bulunmaktadır."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Meta'nın 90B parametreli komut ayarlı görüntü akıl yürütme modelidir. Bu model, g<PERSON><PERSON><PERSON> tanıma, g<PERSON><PERSON><PERSON><PERSON><PERSON> akıl yürütme, g<PERSON><PERSON><PERSON><PERSON><PERSON> betimleme ve görüntü hakkında genel sorulara yanıt verme üzerine optimize edilmiştir. Bu model, grafikler ve resimler gibi görsel verileri anlayabilir ve görüntü detaylarını metin olarak betimleyerek görsel ile dil arasındaki boşluğu kapatır."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct, Llama 3.1 70B'nin Aralık güncellemesi olan bir modeldir. Bu model, Llama 3.1 70B (2024 Temmuz'da piyasaya sürüldü) temel alınarak geliştirilmiş olup, <PERSON><PERSON><PERSON>, çok dilli metin <PERSON>, matematik ve programlama yeteneklerini artırmıştır. Model, a<PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON><PERSON>, matematik ve talimat takibi alanlarında sektördeki en yüksek standartlara ulaşmış olup, 3.1 405B ile benzer performans sunarken hız ve maliyet açısından önemli avantajlar sağlamaktadır."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "24B parametreli model, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> model<PERSON><PERSON> ka<PERSON>şılaştırılabilir en son tekno<PERSON><PERSON>."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B talimat modeli, büyük ölçekli parametreler ve çok uzmanlı mimarisi ile karmaşık görevlerin etkili işlenmesini destekler."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B talimat modeli, çok uzmanlı mimarisi ile etkili talimat takibi ve yürütme sunar."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMax L2 13B modeli, yenilikçi birleşim teknolojileri ile hikaye anlatımı ve rol yapma konularında uzmandır."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision talimat modeli, karmaşık görsel ve metin bilgilerini işleyebilen hafif çok modlu bir modeldir ve güçlü akıl yürütme yeteneklerine sa<PERSON>tir."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "QwQ modeli, <PERSON><PERSON> ekibi tarafından geliştirilen deneysel bir araştırma modelidir ve AI akıl yürütme yeteneklerini artırmaya odaklanmaktadır."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Qwen-VL modelinin 72B ve<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ın en son iterasyonunun bir ürünüdür ve son bir yılın yeniliklerini temsil etmektedir."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "<PERSON>wen2.5, Alibaba <PERSON> Qwen ekibi tarafından geliştirilen yalnızca kodlayıcı içeren bir dizi dil modelidir. Bu modeller, 0.5B, 1.5B, 3B, 7B, 14B, 32B ve 72B gibi farklı boyutları sunar ve temel (base) ve komut (instruct) versiyonlarına sahiptir."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct, Alibaba Cloud tarafından yayınlanan kod odaklı büyük dil modeli serisinin en son versiyonudur. Bu model, Qwen2.5 temelinde, 5.5 trilyon token ile eğitilerek kod üretimi, akıl yürütme ve düzeltme yeteneklerini önemli ölçüde artırmıştır. Hem kodlama yeteneklerini geliştirmiş hem de matematik ve genel yetenek avantajlarını korumuştur. Model, kod akıllı ajanları gibi pratik uygulamalar için daha kapsamlı bir temel sunmaktadır."}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Large modeli, mü<PERSON><PERSON><PERSON> çok dilli işleme <PERSON> sunar ve her türlü dil üretimi ve anlama görevleri için uygundur."}, "ai21-jamba-1.5-large": {"description": "398B parametreli (94B aktif) çok dilli bir model, 256K uzun bağlam penceresi, fonksiyon çağrısı, yapılandırılmış çıktı ve temellendirilmiş üretim sunar."}, "ai21-jamba-1.5-mini": {"description": "52B parametreli (12B aktif) çok dilli bir model, 256K uzun bağlam penceresi, fonksiyon çağrısı, yapılandırılmış çıktı ve temellendirilmiş üretim sunar."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "398 milyar parametreli (94 milyar aktif) çok dilli model, 256K uzun bağlam penceresi, fonksiyon çağrısı, yap<PERSON>landırılmış çıktı ve gerçeklere dayalı üretim sunar."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "52 milyar parametreli (12 milyar aktif) çok dilli model, 256K uzun bağlam penceresi, fonksiyon çağrısı, yap<PERSON>landırılmış çıktı ve gerçeklere dayalı üretim sunar."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON> modelleri ve Claude 3 Opus'u geride bırakarak geniş bir değerlendirmede mükemmel performans sergilerken, orta seviye modellerimizin hızı ve maliyeti ile birlikte gelir."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON> modelleri ve Claude 3 Opus'u geride b<PERSON>, geni<PERSON> bir değerlendirme yelpazesinde mükemmel performans sergilemekte, orta seviye modellerimizin hız ve maliyet avantajlarını sunmaktadır."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku, Anthrop<PERSON>'in en hızlı ve en kompakt modelidir, ne<PERSON><PERSON><PERSON> anında yanıt hızı sunar. Basit sorgular ve taleplere hızlı bir şekilde yanıt verebilir. <PERSON><PERSON><PERSON><PERSON><PERSON>, insan etkileşimini taklit eden kesintisiz bir AI deneyimi oluşturabileceklerdir. Claude 3 Haiku, g<PERSON><PERSON><PERSON><PERSON><PERSON>leri işleyebilir ve metin çıktısı döndürebilir, 200K bağlam penceresine sahiptir."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus, Anthropic'in en güçlü AI modelidir, son derece karma<PERSON><PERSON> görevlerde en ileri düzey performansa sahiptir. Açık uçlu istemleri ve daha önce görülmemiş senaryoları işleyebilir, mü<PERSON><PERSON><PERSON> akıcılık ve insan benzeri anlama yeteneğine sahiptir. Claude 3 Opus, üretken AI olasılıklarının öncüsüdür. Claude 3 Opus, g<PERSON>r<PERSON>nt<PERSON>leri işleyebilir ve metin çıktısı döndürebilir, 200K bağlam penceresine sahiptir."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Anthropic'in Claude 3 Sonnet, zeka ve hız arasında ideal bir denge sağlar - özellikle kurumsal iş yükleri için uygundur. Ra<PERSON><PERSON>ine göre daha düşük bir fiyatla maksimum fayda sunar ve ölçeklenebilir AI dağıtımları için gü<PERSON>ilir, dayanıklı bir ana makine olarak tasarlanmıştır. Claude 3 Sonnet, görüntüleri işleyebilir ve metin çıktısı döndürebilir, 200K bağlam penceresine sahiptir."}, "anthropic.claude-instant-v1": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diyaloglar, metin analiz<PERSON>, özetleme ve belge soru-cevap gibi bir dizi görevi işleyebilen hızlı, ekonomik ve hala oldukça yetenekli bir modeldir."}, "anthropic.claude-v2": {"description": "Anthrop<PERSON>, karmaşık diyaloglardan yaratıcı içerik üretimine ve ayrıntılı talimat takibine kadar geniş bir görev yelpazesinde yüksek yetenek sergileyen bir modeldir."}, "anthropic.claude-v2:1": {"description": "Claude 2'nin g<PERSON><PERSON><PERSON> ve<PERSON>, iki kat daha b<PERSON>y<PERSON>k bir bağlam penceresine sahiptir ve uzun belgeler ve RAG bağlamındaki güvenilirlik, yan<PERSON><PERSON>ama oranı ve kanıta dayalı doğrulukta iyileştirmeler sunar."}, "anthropic/claude-3-haiku": {"description": "Claude 3 <PERSON><PERSON>, Anthrop<PERSON>'in en hızlı ve en kompakt modelidir; ne<PERSON><PERSON><PERSON> anlık yanıtlar sağlamak için tasarlanmıştır. Hızlı ve doğru yönlendirme performansına sahiptir."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus, Anthropic'in son derece karma<PERSON><PERSON>k görevleri işlemek için en güçlü modelidir. Performans, zeka, akıcı<PERSON><PERSON><PERSON> ve anlama açısından olağanüstü bir performans sergiler."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 <PERSON><PERSON>, <PERSON><PERSON><PERSON>'in en hızlı bir sonraki nesil modelidir. Claude 3 <PERSON>ku ile karşılaştırı<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> 3.5 <PERSON><PERSON>, bir<PERSON>ok beceride iyileşme göstermiştir ve birçok zeka kıyaslamasında bir önceki neslin en büyük modeli Claude 3 Opus'u geride bırakmıştır."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 <PERSON><PERSON>, <PERSON>'tan daha fazla yetenek ve Sonnet'ten daha hızlı bir hız sunar; aynı zamanda Sonnet ile aynı fiyatı korur. Sonnet, <PERSON><PERSON><PERSON>, veri bi<PERSON><PERSON>, g<PERSON><PERSON><PERSON> işleme ve ajan görevlerinde özellikle başarılıdır."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 <PERSON><PERSON>, Anthrop<PERSON>'in şimdiye kadarki en akıllı modeli ve piyasadaki ilk karma akıl yürütme modelidir. Claude 3.7 <PERSON><PERSON>, ne<PERSON><PERSON><PERSON> anlık yanıtlar veya uzatılmış adım adım düşünme süreçleri üretebilir; kullanıcılar bu süreçleri net bir şekilde görebilir. Sonnet, programlama, veri bilimi, g<PERSON><PERSON><PERSON> işleme ve temsilci görevlerde özellikle yeteneklidir."}, "anthropic/claude-opus-4": {"description": "<PERSON> 4, Anthropic tarafından yüksek karmaşıklıktaki görevleri işlemek için geliştirilen en güçlü modeldir. Performans, zeka, akıcılık ve anlama yeteneği açısından üstün bir performans sergiler."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4, ne<PERSON><PERSON><PERSON> anında yanıtlar veya uzatılmış adım adım düşünme süreçleri üretebilir; kullanıcılar bu süreçleri net bir şekilde görebilir. API kullanıcıları ayrıca modelin düşünme süresini ayrıntılı olarak kontrol edebilir."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B, 72 milyar parametreli ve 16 milyar parametre aktive eden seyrek büyük bir dil modelidir. Bu model, grup tabanlı uzman karışımı (MoGE) mimarisine dayanır; uzman seçim aşama<PERSON>ında uzmanları gruplar halinde d<PERSON> ve her grupta token başına eşit sayıda uzmanı aktive ederek uzman yük dengesini sağlar. Bu sayede Ascend platformunda modelin dağıtım verimliliği önemli ölçüde artırılmıştır."}, "aya": {"description": "<PERSON><PERSON> 23, <PERSON><PERSON> tara<PERSON>ından sunulan çok dilli bir modeldir, 23 dili destekler ve çok dilli uygulamalar için kolaylık sağlar."}, "aya:35b": {"description": "<PERSON><PERSON> 23, <PERSON><PERSON> tara<PERSON>ından sunulan çok dilli bir modeldir, 23 dili destekler ve çok dilli uygulamalar için kolaylık sağlar."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B, Baichuan Zhi Neng tarafından geliştirilen 130 milyar parametreye sahip açık kaynaklı ticari bir büyük dil modelidir ve yetkili Çince ve İngilizce benchmark'larda aynı boyuttaki en iyi sonuçları elde etmiştir."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B, <PERSON><PERSON> tarafından geliştirilen, karma <PERSON><PERSON> (MoE) mimarisine dayanan büyük bir dil modelidir. Modelin toplam parametre sayısı 300 milyar olup, <PERSON><PERSON><PERSON><PERSON><PERSON> sı<PERSON><PERSON>nda her token için yalnızca 47 milyar parametre aktive edilir; böylece güçlü performans ile hesaplama verimliliği dengelenir. ERNIE 4.5 serisinin temel modellerinden biri olarak, metin anlam<PERSON>, <PERSON><PERSON><PERSON>, akıl yürütme ve programlama gibi görevlerde üstün yetenekler sergiler. Model, metin ve görsel modların ortak eğitimiyle çok modlu heterojen MoE ön eğitim yöntemi kullanarak genel yeteneklerini artırmış, özellikle talimat takibi ve dünya bilgisi hafızasında etkileyici sonuçlar elde etmiştir."}, "c4ai-aya-expanse-32b": {"description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, veri arbit<PERSON>, tercih eğitimi ve model birleştirme yenilikleri ile tek dilli modellerin performansını zorlamak için tasarlanmış yüksek performanslı bir 32B çok dilli modeldir. 23 dili desteklemektedir."}, "c4ai-aya-expanse-8b": {"description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, veri arbit<PERSON>, tercih eğitimi ve model birleştirme yenilikleri ile tek dilli modellerin performansını zorlamak için tasarlanmış yüksek performanslı bir 8B çok dilli modeldir. 23 dili desteklemektedir."}, "c4ai-aya-vision-32b": {"description": "<PERSON><PERSON>, di<PERSON>, metin ve görüntü yet<PERSON>klerinin birden fazla anahtar ölçütünde mükemmel performans sergileyen en son teknoloji çok modlu bir modeldir. 23 dili desteklemektedir. Bu 32 milyar parametreli versiyon, en son teknoloji çok dilli performansa odaklanmaktadır."}, "c4ai-aya-vision-8b": {"description": "<PERSON><PERSON>, di<PERSON>, metin ve görü<PERSON><PERSON>rinin birden fazla anahtar ölçütünde mükemmel performans sergileyen en son teknoloji çok modlu bir modeldir. Bu 8 milyar parametreli ve<PERSON>i<PERSON>, d<PERSON>ş<PERSON>k gecikme ve en iyi performansa odaklanmaktadır."}, "charglm-3": {"description": "CharGLM-3, rol yapma ve duygusal destek iç<PERSON>, ultra uzun çok turlu bellek ve kişiselleştirilmiş diyalog desteği sunan bir modeldir, geniş bir uygulama yelpazesine sahiptir."}, "charglm-4": {"description": "CharGLM-4, rol yapma ve duygusal destek için <PERSON>, uzun süreli çoklu hafıza ve kişiselleştirilmiş diyalogları destekler, geni<PERSON> bir uygulama yelpazesine sahiptir."}, "chatglm3": {"description": "ChatGLM3, ZhiPu AI ve Tsinghua KEG laboratuvarı tarafından yayınlanan kapalı kaynaklı bir modeldir. Büyük miktarda Çince ve İngilizce belirteçlerin önceden eğitilmesi ve insan tercihleriyle hizalama eğitimi ile, birinci nesil modellere göre MMLU, C-Eval ve GSM8K'da sırasıyla %16, %36 ve %280'lük iyileştirmeler elde edilmiştir ve Çince görevler listesinde C-Eval zirvesine ulaşmıştır. <PERSON><PERSON><PERSON> hacmi, çıkarım yeteneği ve yaratıcılık gerektiren senaryolarda kullanılabilir, <PERSON><PERSON><PERSON><PERSON> rekla<PERSON> metni, roman yazımı, bilgi tabanlı yazım, kod oluşturma vb."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base, ZhiPu tarafından geliştirilen ChatGLM serisinin en yeni nesli olan 6 milyar parametrelik açık kaynaklı temel modeldir."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o, güncel versiyonunu korumak için gerçek zamanlı olarak güncellenen dinamik bir modeldir. Güçlü dil anlama ve üretme yeteneklerini birleştirir, m<PERSON><PERSON><PERSON>i <PERSON>leri, eğitim ve teknik destek gibi geniş ölçekli uygulama senaryoları için uygundur."}, "claude-2.0": {"description": "<PERSON> 2, işletmelere kritik yeteneklerin ilerlemesini sunar, sektördeki en iyi 200K token bağlamı, model yan<PERSON><PERSON><PERSON>larının önemli ölçüde azaltılması, sistem ipuçları ve yeni bir test özelliği: araç çağrısı içerir."}, "claude-2.1": {"description": "<PERSON> 2, işletmelere kritik yeteneklerin ilerlemesini sunar, sektördeki en iyi 200K token bağlamı, model yan<PERSON><PERSON><PERSON>larının önemli ölçüde azaltılması, sistem ipuçları ve yeni bir test özelliği: araç çağrısı içerir."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 <PERSON><PERSON>, An<PERSON><PERSON>'in en hızlı bir sonraki nesil modelidir. Claude 3 <PERSON><PERSON> ile karşılaştır<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> 3.5 <PERSON><PERSON>, tüm becerilerde gelişim göstermiştir ve birçok zeka standart testinde bir önceki neslin en büyük modeli olan Claude 3 Opus'u geride bırakmıştır."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 <PERSON><PERSON>, Opus'tan daha fazla yetenek ve Sonnet'ten daha hızlı bir performans sunar, aynı zamanda Sonnet ile aynı fiyatı korur. Sonnet, <PERSON><PERSON><PERSON>, veri bi<PERSON><PERSON>, g<PERSON><PERSON><PERSON> işleme ve ajan görevlerinde özellikle başarılıdır."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 <PERSON><PERSON>, Opus'tan daha fazla yetenek ve Sonnet'ten daha hızlı performans sunarken, aynı fiyatta kalmaktadır. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, veri bi<PERSON>, g<PERSON><PERSON><PERSON> işleme ve aracı görevlerde özellikle güçlüdür."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON> modelleri ve Claude 3 Opus'u geride bı<PERSON>, geni<PERSON> bir değerlendirme yelpazesinde mükemmel performans sergilemekte, orta seviye modellerimizin hız ve maliyet avantajlarını sunmaktadır."}, "claude-3-haiku-20240307": {"description": "Claude 3 <PERSON><PERSON>, <PERSON><PERSON><PERSON>'in en hızlı ve en kompakt modelidir, ne<PERSON><PERSON><PERSON> anlık yanıtlar sağlamak için tasarlanmıştır. Hızlı ve doğru yönlendirme performansına sahiptir."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus, Anthropic'in yüksek karmaşıklıkta görevleri işlemek için en güçlü modelidir. Performans, zeka, akıcılık ve anlama açısından mükemmel bir şekilde öne çıkar."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet, ak<PERSON><PERSON><PERSON> ve hızlı bir denge sunarak kurumsal iş yükleri için idealdir. <PERSON><PERSON> düşük bir fiyatla maksimum fayda <PERSON>, güvenilir ve büyük ölçekli dağıtım için uygundur."}, "claude-opus-4-20250514": {"description": "<PERSON> 4, Anthropic'in son derece karma<PERSON><PERSON>k görevleri işlemek için geliştirdiği en güçlü modeldir. Performans, zeka, akı<PERSON>ıl<PERSON>k ve anlama açısından mükemmel bir şekilde öne çıkmaktadır."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet, ne<PERSON><PERSON><PERSON> anlık yanıtlar veya uzatılmış aşamalı düşünme süreçleri üretebilir; kullanıcılar bu süreçleri net bir şekilde görebilir. API kullanıcıları, modelin düşünme süresini ayrıntılı bir şekilde kontrol edebilir."}, "codegeex-4": {"description": "CodeGeeX-4, çeş<PERSON>li programlama dillerinde akıllı soru-cevap ve kod tamamlama desteği sunan güçlü bir AI programlama asistanıdır, geliş<PERSON>rme verimliliğini artırır."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B, çok dilli kod üretim modeli olup, kod tamamlama ve üretimi, kod yorumlayıcı, web arama, fonksiyon çağrısı, depo düzeyinde kod soru-cevap gibi kapsamlı işlevleri destekler ve yazılım geliştirme için çeşitli senaryoları kapsar. 10B'den az parametreye sahip en iyi kod üretim modelidir."}, "codegemma": {"description": "CodeGemma, farklı programlama görevleri için özel olarak ta<PERSON>lanmış hafif bir dil modelidir, hızlı iterasyon ve entegrasyonu destekler."}, "codegemma:2b": {"description": "CodeGemma, farklı programlama görevleri için özel olarak ta<PERSON>lanmış hafif bir dil modelidir, hızlı iterasyon ve entegrasyonu destekler."}, "codellama": {"description": "Code Llama, kod üretimi ve tartışmalarına odaklanan bir LLM'dir, geniş programlama dili desteği ile geliştirici ortamları için uygundur."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama, kod üretimi ve tartışmalarına odaklanan bir LLM'dir ve geniş bir programlama dili desteği sunarak geliştirici ortamları için uygundur."}, "codellama:13b": {"description": "Code Llama, kod üretimi ve tartışmalarına odaklanan bir LLM'dir, geniş programlama dili desteği ile geliştirici ortamları için uygundur."}, "codellama:34b": {"description": "Code Llama, kod üretimi ve tartışmalarına odaklanan bir LLM'dir, geniş programlama dili desteği ile geliştirici ortamları için uygundur."}, "codellama:70b": {"description": "Code Llama, kod üretimi ve tartışmalarına odaklanan bir LLM'dir, geniş programlama dili desteği ile geliştirici ortamları için uygundur."}, "codeqwen": {"description": "CodeQwen1.5, b<PERSON>yük miktarda kod verisi ile eğitilmiş büyük bir dil modelidir, karmaşık programlama görevlerini çözmek için özel olarak tasarlanmıştır."}, "codestral": {"description": "<PERSON><PERSON><PERSON>, Mistral AI'nın ilk kod modelidir, kod <PERSON>retim görevlerine mükemmel destek sunar."}, "codestral-latest": {"description": "<PERSON><PERSON><PERSON>, kod <PERSON><PERSON><PERSON><PERSON> o<PERSON> son teknolo<PERSON> bir üret<PERSON>, ara doldurma ve kod tamamlama görevlerini optimize etmiştir."}, "codex-mini-latest": {"description": "codex-mini-latest, Codex CLI için özel olarak ince ayarlanmış o4-mini versiyonudur. API üzerinden doğrudan kullanım için, gpt-4.1'den başlamanızı öneririz."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B, ta<PERSON><PERSON> takibi, diyalog ve programlama i<PERSON>in ta<PERSON>mış bir modeldir."}, "cogview-4": {"description": "CogView-4, <PERSON><PERSON><PERSON>'nun <PERSON><PERSON>ce karakter üretimini destekleyen ilk açık kaynaklı metinden görsele modelidir. <PERSON><PERSON>, görüntü üretim kalitesi ve Çince-İngilizce metin üretme yeteneklerinde kapsamlı iyileştirmeler sunar. Her uzunlukta Çince ve İngilizce çift dilli girişi destekler ve verilen aralıkta herhangi bir çözünürlükte görüntü oluşturabilir."}, "cohere-command-r": {"description": "Command R, üretim ölçeğinde AI sağlamak için RAG ve Araç Kullanımına yönelik ölçeklenebilir bir üretken modeldir."}, "cohere-command-r-plus": {"description": "Command R+, kurumsal düzeyde iş yüklerini ele almak için ta<PERSON>ı<PERSON> en son RAG optimize edilmiş bir modeldir."}, "cohere/Cohere-command-r": {"description": "Command R, RAG ve araç kullanımı için ölçeklenebilir bir üretim modeli olup, işletmelerin üretim seviyesinde yapay zeka uygulamalarını gerçekleştirmesine olanak tanır."}, "cohere/Cohere-command-r-plus": {"description": "Command R+, işletme düzeyindeki iş yükleri için <PERSON>, en gelişmiş RAG optimize modelidir."}, "command": {"description": "Dil görevlerinde yüksek kalite ve güvenilirlik sunan, talimatları izleyen bir diyalog modelidir ve temel üretim modelimize göre daha uzun bir bağlam uzunluğuna sahiptir."}, "command-a-03-2025": {"description": "Command A, şimdiye kadar geliştirdiğimiz en güçlü modeldir ve araç kullanımı, ajan, bilgi artırımlı üretim (RAG) ve çok dilli uygulama senaryolarında mükemmel performans sergilemektedir. Command A, 256K bağlam uzunluğuna sahiptir, yalnızca iki GPU ile çalıştırılabilir ve Command R+ 08-2024'e kıyasla %150 daha yüksek bir verimlilik sunar."}, "command-light": {"description": "Neredeyse aynı güçte, ancak daha hızlı olan daha küçük ve daha hızlı bir Command versiyonudur."}, "command-light-nightly": {"description": "Ana sürüm güncellemeleri arasındaki süreyi kısaltmak için Command modelinin her gece sürü<PERSON><PERSON><PERSON>ü sunuyoruz. command-light serisi için bu sürüm command-light-nightly olarak adlandırılmaktadır. Lütfen dikkat edin, command-light-nightly en güncel, en deneysel ve (muhtemelen) kararsız sürümdür. Her gece sürümü düzenli olarak güncellenir ve önceden bildirilmez, bu nedenle üretim ortamında kullanılması önerilmez."}, "command-nightly": {"description": "Ana sürüm güncellemeleri arasındaki süreyi kısaltmak için Command modelinin her gece sür<PERSON><PERSON>ünü sunuyoruz. Command serisi için bu sürüm command-cightly olarak adlandırılmaktadır. Lüt<PERSON> dikkat edin, command-nightly en güncel, en deneysel ve (muhtemelen) kararsız sürümdür. Her gece sürümü düzenli olarak güncellenir ve önceden bildirilmez, bu nedenle üretim ortamında kullanılması önerilmez."}, "command-r": {"description": "Command R, diyalog ve uzun bağlam görevleri için optimize edilmiş bir LLM'dir, dinamik etkileşim ve bilgi yönetimi için özellikle uygundur."}, "command-r-03-2024": {"description": "Command R, dil görevlerinde daha yüksek kalite ve güvenilirlik sunan, talimatları izleyen bir diyalog modelidir ve önceki modellere göre daha uzun bir bağlam uzunluğuna sahiptir. <PERSON><PERSON>, bilgi artırımlı üretim (RAG), araç kullanımı ve ajan gibi karmaşık iş akışları için kullanılabilir."}, "command-r-08-2024": {"description": "command-r-08-2024, Command R model<PERSON>n güncellenmiş versiyonudur ve 2024 yılının Ağustos ayında piyasaya sürülmüştür."}, "command-r-plus": {"description": "Command R+, gerç<PERSON> işletme senaryoları ve karmaşık uygulamalar için tasarlanmış yüksek performanslı bir büyük dil modelidir."}, "command-r-plus-04-2024": {"description": "Command R+, dil görevlerinde daha yüksek kalite ve güvenilirlik sunan, talimatları izleyen bir diyalog modelidir ve önceki modellere göre daha uzun bir bağlam uzunluğuna sahiptir. Karmaşık RAG iş akışları ve çok adımlı araç kullanımı için en uygunudur."}, "command-r-plus-08-2024": {"description": "Command R+ talimatları takip eden bir diyalog modelidir, dil görevlerinde daha yüksek kalite, daha güvenilirlik sunar ve önceki modellere göre daha uzun bağlam uzunluğuna sahiptir. Karmaşık RAG iş akışları ve çok adımlı araç kullanımı için en uygunudur."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024, 2024 yılının Aralık ayında piyasaya sürülen küçük ve verimli bir güncellenmiş versiyondur. RAG, a<PERSON><PERSON>, ajan gibi karmaşık akıl yürütme ve çok adımlı işlemler gerektiren görevlerde mükemmel performans sergilemektedir."}, "compound-beta": {"description": "Compound-beta, GroqCloud'da desteklenen birden fazla açık kullanılabilir modelden güç alan bir bileşik AI sistemidir, kullanıcı sorgularını yanıtlamak için araçları akıllıca ve seçici bir şekilde kullanabilir."}, "compound-beta-mini": {"description": "Compound-beta-mini, GroqCloud'da desteklenen açık kullanılabilir modellerden güç alan bir bileşik AI sistemidir, kullanıcı sorgularını yanıtlamak için araçları akıllıca ve seçici bir şekilde kullanabilir."}, "computer-use-preview": {"description": "computer-use-preview modeli, \"Bilgisayar Kullanım Araçları\" için özel olarak tasarlanmış ve bilgisayarla ilgili görevleri anlama ve yerine getirme konusunda eğitilmiş özel bir modeldir."}, "dall-e-2": {"description": "İkinci nesil DALL·E modeli, daha gerçekçi ve doğru görüntü üretimi destekler, çözünürlüğü birinci neslin 4 katıdır."}, "dall-e-3": {"description": "En son DALL·E modeli, Kasım 2023'te piyasaya sürüldü. Daha gerçekçi ve doğru görüntü üretimi <PERSON>r, <PERSON><PERSON> gü<PERSON><PERSON><PERSON> detay ifade yeteneğine sa<PERSON>r."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct, yüksek güvenilirlikte talimat işleme yetenekleri sunar ve çok çeşitli endüstri uygulamalarını destekler."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1, te<PERSON><PERSON><PERSON><PERSON> (RL) destekli bir çıkarım modelidir ve modeldeki tekrarlama ve okunabilirlik sorunlarını çözmektedir. RL'den önce, DeepSeek-R1 soğuk başlangıç verilerini tanıtarak çıkarım performansını daha da optimize etmiştir. Matematik, kod ve çıkarım görevlerinde OpenAI-o1 ile benzer bir performans sergilemekte ve özenle tasarlanmış eğitim yöntemleri ile genel etkisini artırmaktadır."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hesaplama kaynakları ve son eğitim sürecinde algoritma optimizasyon mekanizmalarının entegrasyonu sayesinde çıkarım ve akıl yürütme derinliğini önemli ölçüde artırmıştır. Model, matematik, programlama ve genel mantık alanlarında çeşitli kıyaslama testlerinde üstün performans göstermektedir. Genel performansı, O3 ve Gemini 2.5 Pro gibi lider modellerle yakındır."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B, DeepSeek-R1-0528 modelinden düşünce zinciri distilasyonu yoluyla Qwen3 8B Base modeline elde edilen bir modeldir. Açık kaynak modeller arasında en ileri (SOTA) performansa sahiptir, AIME 2024 testinde Qwen3 8B'yi %10 aşmış ve Qwen3-235B-thinking performans seviyesine ulaşmıştır. Model matematiksel akıl yürütme, programlama ve genel mantık gibi çeşitli kıyaslama testlerinde üstün performans gösterir; mimarisi Qwen3-8B ile aynıdır ancak DeepSeek-R1-0528'in tokenizer konfigürasyonunu paylaşır."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1 dam<PERSON><PERSON><PERSON> modeli, pekiştirme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, açık kaynak model çoklu görev standartlarını yeniler."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 dam<PERSON><PERSON><PERSON> modeli, pekiştirme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, açık kaynak model çoklu görev standartlarını yeniler."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 dam<PERSON><PERSON><PERSON> modeli, pekiştirme öğrenimi ve soğuk başlatma verileri ile çıkarım performansını optimize eder, açık kaynak model çoklu görev standartlarını yeniler."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B, Qwen2.5-32B temel alınarak bilgi damıtma ile elde edilen bir modeldir. Bu model, DeepSeek-R1 tarafından üretilen 800.000 seçkin örnek ile ince ayar ya<PERSON>, matematik, programlama ve çıkarım gibi birçok alanda olağanüstü performans sergilemektedir. AIME 2024, MATH-500, GPQA Diamond gibi birçok referans testinde mükemmel sonuçlar elde etmiş, MATH-500'de %94.3 doğruluk oranına ulaşarak güçlü matematik çıkarım yeteneğini göstermiştir."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B, Qwen2.5-Math-7B temel alınarak bilgi damıtma ile elde edilen bir modeldir. Bu model, DeepSeek-R1 tarafından üretilen 800.000 seçkin örnek ile ince ayar yapılmış, mükemmel çıkarım yeteneği sergilemektedir. Birçok referans testinde öne çıkmış, MATH-500'de %92.8 doğruluk oranına, AIME 2024'te %55.5 geçiş oranına ula<PERSON>m<PERSON>, CodeForces'ta 1189 puan alarak 7B ölçeğindeki model olarak güçlü matematik ve programlama yeteneğini göstermiştir."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5, <PERSON><PERSON><PERSON> sü<PERSON>ü<PERSON>in müke<PERSON>l özelliklerini bir araya getirir, genel ve kodlama <PERSON> artırır."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3, 6710 milyar parametreye sahip bir karma uzman (MoE) dil modelidir. <PERSON>ok başlı potans<PERSON><PERSON> di<PERSON> (MLA) ve DeepSeekMoE mimarisini kull<PERSON>rak, yardımcı kayıplar olmadan yük dengeleme stratejisi ile çıkarım ve eğitim verimliliğini optimize etmektedir. 14.8 trilyon yüksek kaliteli token üzerinde önceden eğitilmiş ve denetimli ince ayar ile tekrarlayan öğrenme gerçekleştirilmiştir; DeepSeek-V3, performans açısından diğer açık kaynaklı modelleri geride bırakmakta ve lider kapalı kaynaklı modellere yaklaşmaktadır."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B, <PERSON><PERSON>ksek karmaşıklıkta diyaloglar için eğitilmiş gelişmiş bir modeldir."}, "deepseek-ai/deepseek-r1": {"description": "En son teknolojiye sahip verimli LLM, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, matematik ve <PERSON>lama konularında uzmandı<PERSON>."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2, DeepSeekMoE-27B tabanlı bir karma <PERSON>zman (MoE) görsel dil modelidir. Seyrek etkinleştirilen MoE mimarisini kullanarak yalnızca 4.5B parametreyi etkinleştirerek olağanüstü performans sergilemektedir. Bu model, g<PERSON><PERSON><PERSON> soru yanıtlama, optik karakter tan<PERSON>ma, belge/tablolar/grafikler anlama ve görsel konumlandırma gibi birçok görevde mükemmel sonuçlar elde etmektedir."}, "deepseek-chat": {"description": "Genel ve kod yeteneklerini birleştiren yeni bir açık kaynak modeli, yalnızca mevcut Chat modelinin genel diyalog yeteneklerini ve Coder modelinin güçlü kod işleme yeteneklerini korumakla kalmaz, aynı zamanda insan tercihleri ile daha iyi hizalanmıştır. Ayrıca, DeepSeek-V2.5 yazım görevleri, talimat takibi gibi birçok alanda büyük iyileştirmeler sağlamıştır."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B, 20 trilyon veri ile eğitilmiş bir kod dili modelidir. Bunun %87'si kod, %13'ü ise Çince ve İngilizce dillerindendir. Model, 16K pencere boyutu ve boşluk doldurma görevini tanıtarak proje düzeyinde kod tamamlama ve parça doldurma işlevi sunmaktadır."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2, a<PERSON><PERSON>k kaynaklı bir karış<PERSON>k uzman kod modelidir, kod görevlerinde mükemmel performans sergiler ve GPT4-Turbo ile karşılaştırılabilir."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2, a<PERSON><PERSON>k kaynaklı bir karış<PERSON>k uzman kod modelidir, kod görevlerinde mükemmel performans sergiler ve GPT4-Turbo ile karşılaştırılabilir."}, "deepseek-r1": {"description": "DeepSeek-R1, te<PERSON><PERSON><PERSON><PERSON> (RL) destekli bir çıkarım modelidir ve modeldeki tekrarlama ve okunabilirlik sorunlarını çözmektedir. RL'den önce, DeepSeek-R1 soğuk başlangıç verilerini tanıtarak çıkarım performansını daha da optimize etmiştir. Matematik, kod ve çıkarım görevlerinde OpenAI-o1 ile benzer bir performans sergilemekte ve özenle tasarlanmış eğitim yöntemleri ile genel etkisini artırmaktadır."}, "deepseek-r1-0528": {"description": "685 milyar parametreli tam sürüm model, 28 Mayıs 2025'te yayınlandı. DeepSeek-R1, son eğ<PERSON><PERSON> aşamasında pek az etiketli veriyle güçlendirilmiş öğrenme tekniklerini geniş çapta kullanarak modelin çıkarım yeteneğini büyük ölçüde artırdı. Matematik, k<PERSON>lama, doğal dil çıkarımı gibi görevlerde yüksek performans ve güçlü yetenekler sergiler."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B h<PERSON><PERSON><PERSON><PERSON> vers<PERSON><PERSON>, ger<PERSON><PERSON> zamanlı çevrimiçi arama desteği ile, model performansın<PERSON> korurken daha hızlı yanıt süreleri sunar."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B standart ve<PERSON><PERSON><PERSON><PERSON>, ger<PERSON><PERSON> zamanlı çevrimiçi arama desteği ile, en güncel bilgilere ihtiyaç duyan diyalog ve metin işleme görevleri için uygundur."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-<PERSON><PERSON>, DeepSeek-R1'den Llama tabanlı damıtılarak elde edilmiş bir modeldir."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - DeepSeek paketindeki daha b<PERSON>yük ve daha akıllı model - Llama 70B mimarisine damıtılmıştır. Referans testleri ve insan değerlendirmelerine day<PERSON>, bu model orijinal Llama 70B'den daha akıll<PERSON><PERSON>, özellikle matematik ve gerçeklik doğruluğu gerektiren görevlerde mükemmel performans sergilemektedir."}, "deepseek-r1-distill-llama-8b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> modeller, bil<PERSON> dam<PERSON><PERSON><PERSON> teknolojisi ile DeepSeek-R1 tarafından üretilen örneklerin Qwen, <PERSON>lama gibi açık kaynak modeller üzerinde ince ayar yapılmasıyla elde edilmiştir."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "14 Şubat 2025'te ilk kez piyasaya sürülen bu model, <PERSON><PERSON><PERSON> büyük model geliştirme ekibi tarafından Llama3_70B temel modeli (Meta Llama ile oluşturulmuştur) kullanılarak damıtılmıştır ve damıtma verilerine Qianfan'ın metinleri de eklenmiştir."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "14 Şubat 2025'te ilk kez piyasaya sürülen bu model, <PERSON><PERSON><PERSON> büyük model geliştirme ekibi tarafından Llama3_8B temel modeli (Meta Llama ile oluşturulmuştur) kullanılarak damıtılmıştır ve damıtma verilerine Qianfan'ın metinleri de eklenmiştir."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-dist<PERSON>-<PERSON><PERSON>, <PERSON><PERSON> temel alınarak DeepSeek-R1'den damıtılmış bir modeldir."}, "deepseek-r1-distill-qwen-1.5b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> modeller, bil<PERSON> dam<PERSON><PERSON><PERSON> teknolojisi ile DeepSeek-R1 tarafından üretilen örneklerin Qwen, <PERSON>lama gibi açık kaynak modeller üzerinde ince ayar yapılmasıyla elde edilmiştir."}, "deepseek-r1-distill-qwen-14b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> modeller, bil<PERSON> dam<PERSON><PERSON><PERSON> teknolojisi ile DeepSeek-R1 tarafından üretilen örneklerin Qwen, <PERSON>lama gibi açık kaynak modeller üzerinde ince ayar yapılmasıyla elde edilmiştir."}, "deepseek-r1-distill-qwen-32b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> modeller, bil<PERSON> dam<PERSON><PERSON><PERSON> teknolojisi ile DeepSeek-R1 tarafından üretilen örneklerin Qwen, <PERSON>lama gibi açık kaynak modeller üzerinde ince ayar yapılmasıyla elde edilmiştir."}, "deepseek-r1-distill-qwen-7b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> modeller, bil<PERSON> dam<PERSON><PERSON><PERSON> teknolojisi ile DeepSeek-R1 tarafından üretilen örneklerin Qwen, <PERSON>lama gibi açık kaynak modeller üzerinde ince ayar yapılmasıyla elde edilmiştir."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 tam hız<PERSON><PERSON> vers<PERSON>, ger<PERSON><PERSON> zamanlı çevrimiçi arama desteği ile, 671B parametrenin güçlü yetenekleri ile daha hızlı yanıt sürelerini birleştirir."}, "deepseek-r1-online": {"description": "DeepSeek R1 tam s<PERSON><PERSON><PERSON><PERSON><PERSON>, 671B paramet<PERSON><PERSON>lup, ger<PERSON><PERSON> zamanlı çevrimiçi arama desteği ile daha güçlü anlama ve üretim yeteneklerine sahiptir."}, "deepseek-reasoner": {"description": "DeepSeek tarafından sunulan bir akıl yürütme modeli. Model, <PERSON><PERSON> yanıtı vermeden önce bir düşünce zinciri içeriği sunarak nihai cevabın doğruluğunu artırır."}, "deepseek-v2": {"description": "DeepSeek V2, ekonomik ve verimli işleme ihtiyaçları için <PERSON>, etki<PERSON> bir Mixture-of-Experts dil modelidir."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B, DeepSeek'in tasarım kodu modelidir, g<PERSON><PERSON><PERSON><PERSON> kod üretim <PERSON>."}, "deepseek-v3": {"description": "DeepSeek-V3, Hangzhou DeepSeek Yapay Zeka Temel Teknoloji Araştırma Şirketi tarafından geliştirilen MoE modelidir, birçok değerlendirme sonucunda öne çıkmakta ve ana akım listelerde açık kaynak modeller arasında birinci sırada yer almaktadır. V3, V2.5 modeline göre üretim hızında 3 kat artış sağlamış, kullanıcılara daha hızlı ve akıcı bir deneyim sunmuştur."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324, 671B parametreye sahip bir MoE modelidir ve programlama ile teknik <PERSON>, ba<PERSON><PERSON> anlama ve uzun metin işleme gibi alanlarda belirgin avantajlar sunar."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3, 685B parametreye sahip bir uzman karış<PERSON>k modeldir ve DeepSeek ekibinin amiral gemisi sohbet modeli serisinin en son iterasyonudur.\n\nÇeşitli görevlerde mükemmel performans sergileyen [DeepSeek V3](/deepseek/deepseek-chat-v3) modelini devralmıştır."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3, 685B parametreye sahip bir uzman karış<PERSON>k modeldir ve DeepSeek ekibinin amiral gemisi sohbet modeli serisinin en son iterasyonudur.\n\nÇeşitli görevlerde mükemmel performans sergileyen [DeepSeek V3](/deepseek/deepseek-chat-v3) modelini devralmıştır."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1, yaln<PERSON>zca çok az etiketli veri ile modelin akıl yürütme yeteneğini büyük ölçüde artırır. Model, nihai yanıtı vermeden önce bir düşünce zinciri içeriği sunarak nihai yanıtın doğruluğunu artırır."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1, çok az etiketli veri ile modelin akıl yürütme yeteneğini büyük ölçüde artırır. <PERSON><PERSON> yanıtı vermeden önce, model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> artırmak için bir düşünce zinciri çıktısı üretir."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1, çok az etiketli veri ile modelin akıl yürütme yeteneğini büyük ölçüde artırır. <PERSON><PERSON> yanıtı vermeden önce, model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> artırmak için bir düşünce zinciri çıktısı üretir."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B, Llama3.3 70B tabanlı büyük bir dil modelidir ve DeepSeek R1'in çıktısını kullanarak ince ayar yaparak büyük öncü modellerle rekabet edebilecek bir performans elde etmiştir."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B, Llama-3.1-8B-Instruct tabanlı bir damıtılmış büyük dil modelidir ve DeepSeek R1'in çıktısını kullanarak eğitilmiştir."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B, Qwen 2.5 14B tabanlı bir damıtılmış büyük dil modelidir ve DeepSeek R1'in çıktısını kullanarak eğitilmiştir. Bu model, birçok benchmark testinde OpenAI'nin o1-mini'sini geçerek yoğun modellerin (dense models) en son teknik liderlik başarılarını elde etmiştir. İşte bazı benchmark test sonuçları:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nBu model, DeepSeek R1'in çıktısından ince ayar yaparak daha büyük ölçekli öncü modellerle karşılaştırılabilir bir performans sergilemiştir."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B, Qwen 2.5 32B tabanlı bir damıtılmış büyük dil modelidir ve DeepSeek R1'in çıktısını kullanarak eğitilmiştir. Bu model, birçok benchmark testinde OpenAI'nin o1-mini'sini geçerek yoğun modellerin (dense models) en son teknik liderlik başarılarını elde etmiştir. İşte bazı benchmark test sonuçları:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nBu model, DeepSeek R1'in çıktısından ince ayar yaparak daha büyük ölçekli öncü modellerle karşılaştırılabilir bir performans sergilemiştir."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1, DeepSeek ekibinin ya<PERSON>ı<PERSON>ığı en son açık kaynak modelidir ve özellikle matematik, programlama ve akıl yürütme görevlerinde OpenAI'nin o1 modeli ile karşılaştırılabilir bir çıkarım performansına sahiptir."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1, yaln<PERSON>zca çok az etiketli veri ile modelin akıl yürütme yeteneğini büyük ölçüde artırır. Model, nihai yanıtı vermeden önce bir düşünce zinciri içeriği sunarak nihai yanıtın doğruluğunu artırır."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3, <PERSON><PERSON><PERSON><PERSON><PERSON> hızında önceki modellere göre önemli bir atılım gerçekleştirmiştir. Açık kaynak modeller arasında birinci sırada yer almakta ve dünya çapındaki en gelişmiş kapalı kaynak modellerle rekabet edebilmektedir. DeepSeek-V3, DeepSeek-V2'de kapsamlı bir şekilde doğrulanan çok başlı potansi<PERSON>l di<PERSON> (MLA) ve DeepSeekMoE mimarilerini kullanmaktadır. Ayrıca, DeepSeek-V3, yük dengeleme için yardımcı kayıpsız bir strateji geliştirmiştir ve daha güçlü bir performans elde etmek için çok etiketli tahmin eğitim hedefleri belirlemiştir."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3, <PERSON><PERSON><PERSON><PERSON><PERSON> hızında önceki modellere göre önemli bir atılım gerçekleştirmiştir. Açık kaynak modeller arasında birinci sırada yer almakta ve dünya çapındaki en gelişmiş kapalı kaynak modellerle rekabet edebilmektedir. DeepSeek-V3, DeepSeek-V2'de kapsamlı bir şekilde doğrulanan çok başlı potansi<PERSON>l di<PERSON> (MLA) ve DeepSeekMoE mimarilerini kullanmaktadır. Ayrıca, DeepSeek-V3, yük dengeleme için yardımcı kayıpsız bir strateji geliştirmiştir ve daha güçlü bir performans elde etmek için çok etiketli tahmin eğitim hedefleri belirlemiştir."}, "deepseek_r1": {"description": "DeepSeek-R1, pek<PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON> (RL) ile yönlendirilen bir çıkarım modelidir, modeldeki tekrarlama ve okunabilirlik sorunlarını çözmektedir. R<PERSON>'den önce, DeepSeek-R1, so<PERSON><PERSON> başlatma verilerini tanıtarak çıkarım performansını daha da optimize etmiştir. Matematik, kod ve çıkarım görevlerinde OpenAI-o1 ile benzer performans sergilemekte ve özenle tasarlanmış eğitim yöntemleri ile genel etkisini artırmaktadır."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B, Llama-3.3-70B-Instruct temel alınarak damıtma eğitimi ile elde edilen bir modeldir. Bu model, DeepSeek-R1 serisinin bir parçasıdır ve DeepSeek-R1 tarafından üretilen örnekler kullanılarak ince ayar yapılmış, matematik, programlama ve çıkarım gibi birçok alanda mükemmel performans sergilemektedir."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B, Qwen2.5-14B temel alınarak bilgi damıtma ile elde edilen bir modeldir. Bu model, DeepSeek-R1 tarafından üretilen 800.000 seçkin örnek ile ince ayar yapılmış ve mükemmel çıkarım yetenekleri sergilemektedir."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B, Qwen2.5-32B temel alınarak bilgi damıtma ile elde edilen bir modeldir. Bu model, DeepSeek-R1 tarafından üretilen 800.000 seçkin örnek ile ince ayar yapılmış ve matematik, programlama ve çıkarım gibi birçok alanda olağanüstü performans sergilemektedir."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite, tamamen yeni nesil ha<PERSON>, <PERSON>lağ<PERSON><PERSON><PERSON><PERSON> yanıt hızı ile etkisi ve gecikmesi dünya standartlarında bir seviyeye ulaşmıştır."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k, Doubao-1.5-Pro'nun kapsamlı bir yükseltmesi olup, genel performans %10 oranında büyük bir artış göstermektedir. 256k bağlam penceresi ile akıl yü<PERSON><PERSON><PERSON><PERSON>yi destekler, çıkt<PERSON> uzunluğu maksimum 12k token'a kadar desteklenmektedir. <PERSON><PERSON> yüksek performans, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> pencere, yüks<PERSON> maliyet etkinliği ile daha geniş uygulama alanlarına uygundur."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro, tamamen yeni ne<PERSON>l ana model, performans<PERSON> tamamen yüks<PERSON><PERSON><PERSON> olup, bil<PERSON>, kod, akıl y<PERSON>r<PERSON><PERSON>me gibi alanlarda mükemmel bir performans sergilemektedir."}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5, tamamen yeni bir derin d<PERSON><PERSON><PERSON><PERSON> model<PERSON>, mate<PERSON><PERSON>, <PERSON><PERSON><PERSON>, bi<PERSON><PERSON> akıl yürütme gibi uzmanlık alanlarında ve yaratıcı yazım gibi genel görevlerde olağanüstü performans sergilemektedir. AIME 2024, Codeforces, GPQA gibi birçok saygın ölçekte sektörün en üst seviyelerine ulaşmakta veya bunlara yakın bir performans göstermektedir. 128k bağlam penceresi ve 16k çıktı desteği sunmaktadır."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 yeni derin dü<PERSON>ün<PERSON> modeli (m versiyonu yerel çok modlu derin çıkarım yeteneği ile birlikte gelir), matematik, <PERSON><PERSON><PERSON>, bi<PERSON>sel çıkarım gibi uzmanlık alanlarında ve yaratıcı yazım gibi genel görevlerde üstün performans gösterir. AIME 2024, Codeforces, GPQA gibi birçok otoriter kıyaslamada sektörün ilk sıralarına ulaşmıştır veya yaklaşmıştır. 128k bağlam penceresi ve 16k çıktı desteği sağlar."}, "doubao-1.5-thinking-vision-pro": {"description": "<PERSON><PERSON> gö<PERSON>l derin d<PERSON>, <PERSON><PERSON> gü<PERSON><PERSON><PERSON> genel çok modlu anlama ve çıkarım yeteneklerine sahiptir ve 59 açık değerlendirme kıyaslamasından 37'sinde SOTA (en iyi) performans göstermiştir."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-T<PERSON><PERSON>, grafik kullanıcı arayüzü (GUI) etkileşimine özgü yerel bir Agent modelidir. Algılama, çıkarım ve eylem gibi insan benzeri yeteneklerle GUI ile kesintisiz etkileşim <PERSON>ğlar."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite, ye<PERSON> g<PERSON> çok modlu büyük modeldir, herhangi bir çözünürlük ve aşırı en-boy oran<PERSON> gör<PERSON>nt<PERSON> tanıma desteği sunar, <PERSON><PERSON><PERSON><PERSON>, belge tan<PERSON>ma, detay bilgisi anlama ve talimat takibi yeteneklerini artırır. 128k bağlam penceresi destekler, çıktı uzunluğu maksimum 16k token destekler."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro, tama<PERSON> yenilenmiş çok modlu büyük modeldir. <PERSON><PERSON>i bir çözünürlükte ve aşırı en-boy oranlarındaki görüntüleri tanıyabilir, <PERSON><PERSON><PERSON><PERSON>, be<PERSON> tan<PERSON>, detaylı bilgi anlayışını ve komutlara uyumu artırır."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro, tama<PERSON> yenilenmiş çok modlu büyük modeldir. <PERSON><PERSON>i bir çözünürlükte ve aşırı en-boy oranlarındaki görüntüleri tanıyabilir, <PERSON><PERSON><PERSON><PERSON>, be<PERSON> tan<PERSON>, detaylı bilgi anlayışını ve komutlara uyumu artırır."}, "doubao-lite-128k": {"description": "Son derece hızlı yanıt süresi ve daha iyi fiyat-performans oranı ile müşterilere farklı senaryolar için daha esnek seçenekler sunar. 128k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "doubao-lite-32k": {"description": "Son derece hızlı yanıt süresi ve daha iyi fiyat-performans oranı ile müşterilere farklı senaryolar için daha esnek seçenekler sunar. 32k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "doubao-lite-4k": {"description": "Son derece hızlı yanıt süresi ve daha iyi fiyat-performans oranı ile müşterilere farklı senaryolar için daha esnek seçenekler sunar. 4k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "doubao-pro-256k": {"description": "En etkili ana model o<PERSON><PERSON>, karmaşık görevlerin işlenmesi için uygundur. <PERSON><PERSON><PERSON> soru-ceva<PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ya<PERSON><PERSON>, metin s<PERSON>, rol yapma gibi senaryolarda mükemmel performans gösterir. 256k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "doubao-pro-32k": {"description": "En etkili ana model o<PERSON><PERSON>, karmaşık görevlerin işlenmesi için uygundur. <PERSON><PERSON><PERSON> soru-ceva<PERSON>, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ya<PERSON><PERSON>, metin s<PERSON>, rol yapma gibi senaryolarda mükemmel performans gösterir. 32k bağlam penceresi ile çıkarım ve ince ayar desteği sağlar."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6, auto/thinking/non-thinking olmak üzere üç düşünme modunu destekleyen tamamen yeni çok modlu derin düşünme modelidir. Non-thinking modunda, model performansı Doubao-1.5-pro/250115'e kıyasla büyük ölçüde artmıştır. 256k bağlam penceresini destekler ve çıktı uzunluğu maksimum 16k token olabilir."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash, TPOT sadece 10ms olan son derece hızlı çok modlu derin düşünme modelidir; hem metin hem de görsel anlayışı destekler, metin anlama yeteneği önceki lite neslini aşar, görsel anlama ise rakiplerin pro serisi modelleriyle eşdeğerdir. 256k bağlam penceresini destekler ve çıktı uzunluğu maksimum 16k token olabilir."}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking modeli d<PERSON><PERSON><PERSON><PERSON><PERSON> yeteneğinde büyük gelişme göstermiştir, Doubao-1.5-thinking-pro il<PERSON> <PERSON><PERSON><PERSON><PERSON>ştır<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Matematik ve mantıksal akıl yürütme gibi temel yeteneklerde daha da iyileşmiştir, g<PERSON><PERSON><PERSON> anlayışı destekler. 256k bağlam penceresini destekler ve çıktı uzunluğu maksimum 16k token olabilir."}, "doubao-seedream-3-0-t2i-250415": {"description": "Doubao görü<PERSON><PERSON> oluşturma modeli, ByteDance Seed ekibi tarafından geliştirilmiştir; metin ve görüntü girişlerini destekler ve yüksek kontrol edilebilirlik ile yüksek kaliteli görüntü üretimi sunar. Metin istemlerine dayalı görüntü oluşturur."}, "doubao-vision-lite-32k": {"description": "Doubao-vision modeli, <PERSON><PERSON>o tarafından geliştirilen çok modlu büyük bir modeldir. Güçlü görüntü anlama ve çıkarım yeteneklerine ve hassas komut anlama becerisine sahiptir. Model, g<PERSON>r<PERSON><PERSON><PERSON> metin bilgisi çıkarımı ve görüntü tabanlı çıkarım görevlerinde güçlü performans sergiler ve daha karmaşık, geni<PERSON> kapsamlı görsel soru-cevap görevlerinde kullanılabilir."}, "doubao-vision-pro-32k": {"description": "Doubao-vision modeli, <PERSON><PERSON>o tarafından geliştirilen çok modlu büyük bir modeldir. Güçlü görüntü anlama ve çıkarım yeteneklerine ve hassas komut anlama becerisine sahiptir. Model, g<PERSON>r<PERSON><PERSON><PERSON> metin bilgisi çıkarımı ve görüntü tabanlı çıkarım görevlerinde güçlü performans sergiler ve daha karmaşık, geni<PERSON> kapsamlı görsel soru-cevap görevlerinde kullanılabilir."}, "emohaa": {"description": "<PERSON><PERSON><PERSON>, duygusal sorunları anlamalarına yardımcı olmak için profesyonel danışmanlık yeteneklerine sahip bir psikolojik modeldir."}, "ernie-3.5-128k": {"description": "Baidu tarafından geliştirilen amiral gemisi büyük ölçekli dil modeli, geniş bir Çince ve İngilizce veri kü<PERSON><PERSON> kapsar, güçlü genel yeteneklere sahiptir ve çoğu diyalog soru-cevap, <PERSON><PERSON><PERSON><PERSON>, eklenti uygulama senaryolarını karşılayabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar."}, "ernie-3.5-8k": {"description": "Baidu tarafından geliştirilen amiral gemisi büyük ölçekli dil modeli, geniş bir Çince ve İngilizce veri kü<PERSON><PERSON> kapsar, güçlü genel yeteneklere sahiptir ve çoğu diyalog soru-cevap, <PERSON><PERSON><PERSON><PERSON>, eklenti uygulama senaryolarını karşılayabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar."}, "ernie-3.5-8k-preview": {"description": "Baidu tarafından geliştirilen amiral gemisi büyük ölçekli dil modeli, geniş bir Çince ve İngilizce veri kü<PERSON><PERSON> kapsar, güçlü genel yeteneklere sahiptir ve çoğu diyalog soru-cevap, <PERSON><PERSON><PERSON><PERSON>, eklenti uygulama senaryolarını karşılayabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar."}, "ernie-4.0-8k-latest": {"description": "Baidu tarafından geliştirilen amiral gemisi ultra büyük ölçekli dil modeli, ERNIE 3.5'e göre model yet<PERSON><PERSON><PERSON><PERSON> kapsamlı bir yükseltme gerçekleştirmiştir, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar."}, "ernie-4.0-8k-preview": {"description": "Baidu tarafından geliştirilen amiral gemisi ultra büyük ölçekli dil modeli, ERNIE 3.5'e göre model yet<PERSON><PERSON><PERSON><PERSON> kapsamlı bir yükseltme gerçekleştirmiştir, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar."}, "ernie-4.0-turbo-128k": {"description": "Baidu tarafından geliştirilen amiral gemisi ultra büyük ölçekli dil modeli, genel performansı mükemmel, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar. ERNIE 4.0'a göre performans açısından daha üstündür."}, "ernie-4.0-turbo-8k-latest": {"description": "Baidu tarafından geliştirilen amiral gemisi ultra büyük ölçekli dil modeli, genel performansı mükemmel, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar. ERNIE 4.0'a göre performans açısından daha üstündür."}, "ernie-4.0-turbo-8k-preview": {"description": "Baidu tarafından geliştirilen amiral gemisi ultra büyük ölçekli dil modeli, genel performansı mükemmel, çeşitli alanlardaki karmaşık görev senaryolarında geniş bir şekilde uygulanabilir; Baidu arama eklentisi ile otomatik entegrasyon desteği sunarak soru-cevap bilgilerini güncel tutar. ERNIE 4.0'a göre performans açısından daha üstündür."}, "ernie-4.5-8k-preview": {"description": "Wenxin büyük modeli 4.5, <PERSON>du tarafından geliştirilen yeni nesil yerel çok modlu temel büyük modeldir. Birden fazla modun birleşik modellemesi ile işbirlikçi optimizasyon sağlar, çok modlu anlama yeteneği mükemmeldir; di<PERSON> yetenekleri, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, mantık ve hafıza yetenekleri önemli ölçüde geliştirilmiştir, yan<PERSON>lsamaları ortadan kaldırma, mantıksal akıl yürütme ve kod yetenekleri belirgin şekilde artmıştır."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo, ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> giderme, mantıksal akıl yürütme ve kodlama yetenekleri gibi alanlarda belirgin bir artış göstermektedir. Wenxin 4.5 ile karşılaşt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, daha hızlı ve daha düşük maliyetlidir. Modelin yetenekleri genel olarak artırılmıştır, çoklu uzun geçmiş diyalog işleme ve uzun belgeleri anlama görevlerini daha iyi karşılamaktadır."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo, ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> giderme, mantıksal akıl yürütme ve kodlama yetenekleri gibi alanlarda belirgin bir artış göstermektedir. Wenxin 4.5 ile karşılaşt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, daha hı<PERSON>lı ve daha düşük maliyetlidir. <PERSON><PERSON>, bilgi sorgulama gibi yeteneklerde belirgin bir artış sağlanmıştır. Çıktı uzunluğu ve tam cümle gecikmesi, ERNIE 4.5'e kıyasla artmıştır."}, "ernie-4.5-turbo-vl-32k": {"description": "<PERSON><PERSON> büyük modelinin yeni ve<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ya<PERSON><PERSON>, <PERSON><PERSON><PERSON>, kodlama gibi yeteneklerde belirgin bir artış göstermektedir, ilk kez 32K bağlam uzunluğunu desteklemekte ve ilk token gecikmesi belirgin şekilde azaltılmıştır."}, "ernie-char-8k": {"description": "Baidu tarafından geliştirilen dikey senaryo büyük dil modeli, oyun NPC'leri, müşteri hizmetleri diyalogları, diyalog karakter rolü gibi uygulama senaryolarına uy<PERSON>dur, karakter tarzı daha belirgin ve tutarlıdır, talimat takibi yeteneği daha güçlü, çıkarım performansı daha iyidir."}, "ernie-char-fiction-8k": {"description": "Baidu tarafından geliştirilen dikey senaryo büyük dil modeli, oyun NPC'leri, müşteri hizmetleri diyalogları, diyalog karakter rolü gibi uygulama senaryolarına uy<PERSON>dur, karakter tarzı daha belirgin ve tutarlıdır, talimat takibi yeteneği daha güçlü, çıkarım performansı daha iyidir."}, "ernie-irag-edit": {"description": "Baidu tarafından geliştirilen ERNIE iRAG Edit gör<PERSON><PERSON><PERSON> dü<PERSON> modeli, g<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON> (erase), ye<PERSON><PERSON> boyama (repaint) ve varyasyon (variation) gibi işlemleri destekler."}, "ernie-lite-8k": {"description": "ERNIE Lite, <PERSON><PERSON> tarafından geliştirilen ha<PERSON>f b<PERSON><PERSON> dil modelidir, mükemmel model performansı ve çıkarım yeteneği ile düşük hesaplama gücüne sahip AI hızlandırıcı kartları için uygundur."}, "ernie-lite-pro-128k": {"description": "Baidu tarafından geliştirilen ha<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> dil modeli, mükemmel model performansı ve çıkarım yeteneği ile ERNIE Lite'dan daha iyi sonuçlar verir, düşük hesaplama gücüne sahip AI hızlandırıcı kartları için uygundur."}, "ernie-novel-8k": {"description": "Baidu tarafından geliştirilen genel büyük dil modeli, roman devam ettirme yeteneğinde belirgin bir avantaja sa<PERSON>tir, a<PERSON><PERSON> zamanda kısa oyun, film gibi senaryolarda da kullanılabilir."}, "ernie-speed-128k": {"description": "Baidu'nun 2024 yılında yayımladığı en son yü<PERSON><PERSON> perform<PERSON>lı bü<PERSON><PERSON>k dil modeli, genel yet<PERSON><PERSON><PERSON> mü<PERSON>, be<PERSON><PERSON><PERSON> se<PERSON><PERSON> so<PERSON>nı daha iyi ele almak için temel model olarak ince ayar yapılabilir, aynı zamanda mükemmel çıkarım performansına sahiptir."}, "ernie-speed-pro-128k": {"description": "Baidu'nun 2024 yılında yayımladığı en son yü<PERSON><PERSON> performanslı büy<PERSON>k dil modeli, genel yet<PERSON><PERSON><PERSON>, ERNIE Speed'den daha iyi sonuçlar verir, beli<PERSON><PERSON> se<PERSON>o sorun<PERSON>ını daha iyi ele almak için temel model olarak ince ayar yapılabilir, aynı zamanda mükemmel çıkarım performansına sahiptir."}, "ernie-tiny-8k": {"description": "ERNIE Tiny, Baidu tarafından geliştirilen ultra yüksek performanslı büyük dil modelidir, da<PERSON><PERSON><PERSON><PERSON>m ve ince ayar maliyetleri Wenxin serisi modelleri arasında en düşüktür."}, "ernie-x1-32k": {"description": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve evrim yet<PERSON> sa<PERSON>. <PERSON><PERSON> kapsamlı bir derin düşünme modeli <PERSON>, <PERSON><PERSON> X1, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ve edeb<PERSON> bi<PERSON><PERSON>, <PERSON><PERSON><PERSON> bi<PERSON><PERSON>, ed<PERSON><PERSON>, metin <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diyal<PERSON>, mant<PERSON>ks<PERSON> akıl yürütme, karmaşık hesaplamalar ve araç çağırma gibi alanlarda özellikle başarılıdır."}, "ernie-x1-32k-preview": {"description": "Wenxin Büyük Model X1, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve evrim yet<PERSON> sa<PERSON>ti<PERSON>. <PERSON><PERSON> kapsamlı bir derin düşünme modeli o<PERSON>, Wenxin X1, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> ve edebi bir <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> bi<PERSON>, ed<PERSON><PERSON> ya<PERSON>, metin ya<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diyalog, mantıksal akıl yürütme, karmaş<PERSON>k hesaplamalar ve araç çağırma gibi alanlarda özellikle başarılıdır."}, "ernie-x1-turbo-32k": {"description": "ERNIE-X1-32K ile karşılaştırı<PERSON>ı<PERSON><PERSON>, modelin etkisi ve performansı daha iyidir."}, "flux-1-schnell": {"description": "Black Forest Labs tarafından geliştirilen 12 milyar parametreli metinden görüntüye modeldir. Latent adversarial diffusion distillation teknolojisi kullanır ve 1 ila 4 adımda yüksek kaliteli görüntüler oluşturabilir. Performansı kapalı kaynak alternatiflerle karşılaştırılabilir ve Apache-2.0 lisansı altında kişisel, akademik ve ticari kullanıma uygundur."}, "flux-dev": {"description": "FLUX.1 [dev], ticari olmayan uygulamalar için açık kaynaklı ağırlık ve rafine modeldir. FLUX.1 [dev], FLUX profesyonel sürümüne yakın görüntü kalitesi ve talimat uyumu sağlarken daha yüksek çalışma verimliliğine sahiptir. Aynı boyuttaki standart modellere kıyasla kaynak kullanımı açısından daha etkilidir."}, "flux-kontext/dev": {"description": "Frontier görüntü düzenleme modeli."}, "flux-merged": {"description": "FLUX.1-merged modeli, geliştirme aşamasında \"DEV\" tarafından keşfedilen derin özellikler ile \"Schnell\" in yüksek hızlı yürütme avantajlarını birleştirir. Bu sayede model performans sınırlarını artırır ve uygulama alanlarını genişletir."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro], metin ve referans g<PERSON>rüntüleri girdi olarak işleyebilir, hede<PERSON> yönelik yerel düzenlemeler ve karmaşık genel sahne dönüşümlerini sorunsuz bir şekilde gerçekleştirebilir."}, "flux-schnell": {"description": "FLUX.1 [schnell], şu anda açık kaynaklı en gelişmiş az adımlı modeldir; benzer rakiplerini aşmakla kalmaz, Midjourney v6.0 ve DALL·E 3 (HD) gibi güçlü damıtılmamış modellerden bile üstündür. Model, ön eğitim aşamasındaki tüm çıktı çeşitliliğini koruyacak şekilde özel olarak ince ayar yapılmıştır. Piyasadaki en gelişmiş modellere kıyasla görsel kalite, talimat uyumu, boyut/oran <PERSON>ik<PERSON>leri, yazı tipi işleme ve çıktı çeşitliliği gibi alanlarda belirgin iyileştirmeler sunar ve kullanıcılara daha zengin ve çeşitli yaratıcı görüntü üretim deneyimi sağlar."}, "flux.1-schnell": {"description": "120 milyar parametreli düzeltilmiş akış dönüştürücüsüdür ve metin açıklamalarına göre görüntü oluşturabilir."}, "flux/schnell": {"description": "FLUX.1 [schnell], 12 milyar parametreye sahip bir akış dönüştürücü modelidir ve metinden 1 ila 4 adımda yüksek kaliteli görüntüler üretebilir; kişisel ve ticari kullanım için uygundur."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (<PERSON><PERSON>), karar<PERSON><PERSON> ve ayarlanabilir bir performans sunar, karmaşık görev çözümleri için ideal bir seçimdir."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (<PERSON><PERSON>), mükemmel çok modlu destek sunar ve karmaşık görevlerin etkili bir şekilde çözülmesine odaklanır."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro, Google'ın yüksek performanslı AI modelidir ve geniş görev genişletmeleri için ta<PERSON>lanmıştır."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001, g<PERSON><PERSON> uygulama alanları için destekleyen verimli bir çok modlu modeldir."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002, g<PERSON><PERSON> uygulama yelpa<PERSON>ini destekleyen verimli bir çok modlu modeldir."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B, g<PERSON>ş uygulama yelpazesini destekleyen verimli bir çok modlu modeldir."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924, metin ve çok modlu kullanım durumlarında önemli performans artışları sunan en son deneysel modeldir."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B, geniş uygulama desteğiyle çoklu modaliteyi destekleyen yüksek verimli bir modeldir."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827, optimize edilmiş çok modlu işleme yetenekleri sunarak çeşitli karmaşık görev sahnelerine uygundur."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash, Google'ın en son çok modlu AI modelidir, h<PERSON><PERSON><PERSON><PERSON> iş<PERSON><PERSON> yet<PERSON> sahiptir ve metin, g<PERSON><PERSON><PERSON><PERSON><PERSON> ve video giri<PERSON><PERSON> destekler, çeşitli görevlerin verimli bir şekilde genişletilmesine olanak tanır."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001, g<PERSON><PERSON> karmaşık görevleri destekleyen ölçeklenebilir bir çok modlu AI çözümüdür."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002, daha yü<PERSON><PERSON> kaliteli çıktılar sunan en son üretim hazır modeldir; özel<PERSON>le matematik, uzun bağlam ve görsel görevlerde önemli iyileştirmeler sağlamaktadır."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801, olağanüstü çok modlu işleme yetenekleri sunarak uygulama geliştirmeye daha fazla esneklik getirir."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827, en son optimize edil<PERSON>ş teknolojilerle birleştirilmiş daha verimli çok modlu veri iş<PERSON>e <PERSON>."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro, 2 milyon token'a ka<PERSON> destekler, orta ölçekli çok modlu modeller için ideal bir seçimdir ve karmaşık görevler için çok yönlü destek sunar."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> a<PERSON> kullanım<PERSON>, çok modlu üretim ve 1M token bağlam penceresi dahil olmak üzere bir sonraki nesil özellikler ve iyileştirmeler sunar."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> a<PERSON> kullanım<PERSON>, çok modlu üretim ve 1M token bağlam penceresi dahil olmak üzere bir sonraki nesil özellikler ve iyileştirmeler sunar."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash modeli vary<PERSON>, mali<PERSON>t etkinliği ve düşük gecikme gibi hedefler için optimize edilmiştir."}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 <PERSON> den<PERSON>i, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>şturmayı destekler"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash model vary<PERSON><PERSON>, maliyet etkinliği ve düşük gecikme gibi hedefler için optimize edilmiştir."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash model vary<PERSON><PERSON>, maliyet etkinliği ve düşük gecikme gibi hedefler için optimize edilmiştir."}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash önizleme modeli, görüntü üretimini destekler."}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash, Google'ın en yüksek maliyet-performans modelidir ve kapsamlı özellikler sunar."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-<PERSON><PERSON>, Google'ın en küçük ve en uygun maliyetli modeli olup, geniş çaplı kullanım için ta<PERSON>lanmıştır."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-<PERSON><PERSON>, Google'ın en küçük ve en yüksek maliyet-performans modelidir ve büyük ölçekli kullanım için ta<PERSON>lanmıştır."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 <PERSON>, Google'ın en iyi fiyat-performans oranına sahip modelidir ve kapsamlı özellikler sunar."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 <PERSON>, Google'ın en yüksek maliyet-performans modelidir ve kapsamlı özellikler sunar."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro, Google'ın en gelişmiş düşünce modelidir; <PERSON><PERSON><PERSON><PERSON>, matematik ve STEM alanlarındaki karmaşık problemleri çıkarım yapabilir ve uzun bağlam kullanarak büyük veri setleri, kod tabanları ve belgeleri analiz edebilir."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Önizleme, Google'ın en gelişmiş düşünce modeli olup, kod, matematik ve STEM alanlarındaki karmaşık sorunları akıl yürütme yeteneğine sahiptir. Uzun bağlamları analiz ederek büyük veri setleri, kod havuzları ve belgeler üzerinde çalışabilir."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Önizleme, Google'ın en gelişmiş düşünce modelidir ve kod, matematik ve STEM alanlarındaki karmaşık sorunları akıl yürütme yeteneğine sahiptir. Uzun bağlamları analiz ederek büyük veri setleri, kod havuzları ve belgeler üzerinde çalışabilir."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 <PERSON>, Google'ın en gelişmiş düşünce modelidir; <PERSON><PERSON><PERSON><PERSON>, matematik ve STEM alanlarındaki karmaşık problemleri çözebilir ve uzun bağlam kullanarak büyük veri setleri, kod kütüphaneleri ve belgeleri analiz edebilir."}, "gemma-7b-it": {"description": "Gemma 7B, orta ölçekli görev işleme için uygundur ve maliyet etkinliği sunar."}, "gemma2": {"description": "Gemma 2, Google tarafından sunulan verimli bir modeldir, küçük uygulamalardan karmaşık veri işleme senaryolarına kadar çeşitli uygulama alanlarını kapsar."}, "gemma2-9b-it": {"description": "Gemma 2 9B, beli<PERSON><PERSON> görevler ve araç entegrasyonu için optimize edilmiş bir modeldir."}, "gemma2:27b": {"description": "Gemma 2, Google tarafından sunulan verimli bir modeldir, küçük uygulamalardan karmaşık veri işleme senaryolarına kadar çeşitli uygulama alanlarını kapsar."}, "gemma2:2b": {"description": "Gemma 2, Google tarafından sunulan verimli bir modeldir, küçük uygulamalardan karmaşık veri işleme senaryolarına kadar çeşitli uygulama alanlarını kapsar."}, "generalv3": {"description": "Spark Pro, profesyonel alanlar için optimize edilmiş yüksek performanslı büyük dil modelidir, matematik, programlama, sa<PERSON><PERSON><PERSON><PERSON>, eğitim gibi birçok alana odaklanır ve çevrimiçi arama ile yerleşik hava durumu, tarih gibi eklentileri destekler. Optimize edil<PERSON>ş modeli, karmaşık bilgi sorgulama, dil anlama ve yüksek düzeyde metin oluşturma konularında mükemmel performans ve yüksek verimlilik sergiler, profesyonel uygulama senaryoları için ideal bir seçimdir."}, "generalv3.5": {"description": "Spark3.5 Max, en kapsamlı özelliklere sahip versiyondur, çevrimiçi arama ve birçok yerleşik eklentiyi destekler. Kapsamlı optimize edilmiş temel yetenekleri ve sistem rol ayarları ile fonksiyon çağ<PERSON>rma özellikleri, çeşitli karmaşık uygulama senaryolarında son derece mükemmel ve olağanüstü performans sergiler."}, "glm-4": {"description": "GLM-4, Ocak 2024'te piyasaya sürülen eski amiral gemisi versiyonudur, <PERSON><PERSON> anda daha güçlü GLM-4-0520 ile değiştirilmiştir."}, "glm-4-0520": {"description": "GLM-4-0520, son derece ka<PERSON><PERSON><PERSON> ve çeşitli görev<PERSON> i<PERSON>in ta<PERSON> en yeni model vers<PERSON><PERSON><PERSON><PERSON>, olağ<PERSON><PERSON><PERSON><PERSON> perform<PERSON> sergiler."}, "glm-4-9b-chat": {"description": "GLM-4-9B-<PERSON><PERSON>, <PERSON><PERSON>, mate<PERSON><PERSON>, ak<PERSON><PERSON> yü<PERSON><PERSON><PERSON><PERSON>, kod ve bilgi gibi birçok alanda yüksek performans göstermektedir. Ayrıca web tarayıcı, kod yü<PERSON><PERSON>tme, özel araç çağrıları ve uzun metin akıl yürütme yeteneklerine sahiptir. J<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>nca dahil olmak üzere 26 dil desteği sunmaktadır."}, "glm-4-air": {"description": "GLM-4-Air, maliyet etkin bir versiyondur, GLM-4'e yakın performans sunar ve hızlı hız ve uygun fiyat sağlar."}, "glm-4-air-250414": {"description": "GLM-4-Air, maliyet açısından yüksek verimlilik sunan bir versiyondur, GLM-4'e yakın performans sunar, hız<PERSON><PERSON> hız ve uygun fiyat sağlar."}, "glm-4-airx": {"description": "GLM-4-<PERSON><PERSON>, GLM-4-Air'ın verimli bir vers<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> hızı 2.6 katına kadar çıkabilir."}, "glm-4-alltools": {"description": "GLM-4-<PERSON><PERSON><PERSON><PERSON>, karmaşık talimat planlaması ve araç çağrıları gibi çok işlevli görevleri desteklemek için optimize edilmiş bir akıllı modeldir. İnternet tarayıcıları, kod açıklamaları ve metin üretimi gibi çoklu görevleri yerine getirmek için uygundur."}, "glm-4-flash": {"description": "GLM-4-<PERSON>, basit görevleri işlemek için ideal bir seçimdir, en hızlı ve en uygun fiyatlıdır."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON>, basit görevler için ideal bir seçimdir, en hızlı ve ücretsizdir."}, "glm-4-flashx": {"description": "GLM-4-<PERSON><PERSON>, Flash'ın geliştirilmiş bir versiyonudur ve ultra hızlı çıkarım hızı sunar."}, "glm-4-long": {"description": "GLM-4-<PERSON>, ultra uzun metin g<PERSON>tekle<PERSON>, bellek tabanlı görevler ve büyük ölçekli belge işleme için u<PERSON>gundu<PERSON>."}, "glm-4-plus": {"description": "GLM-4-Plus, gü<PERSON><PERSON>ü uzun metin işleme ve karmaşık görevler için yeteneklere sahip yüksek akıllı bir amiral gemisidir, performansı tamamen artırılmıştır."}, "glm-4.1v-thinking-flash": {"description": "GLM-4.1V-Thinking serisi modeller, bilinen 10 milyar parametre seviyesindeki VLM modelleri arasında en güçlü görsel modellerdir. Aynı seviyedeki SOTA görsel dil görevlerini birleştirir; video anlama, g<PERSON><PERSON><PERSON> soru-cevap, akademik problem çözme, OCR metin tanıma, belge ve grafik yorumlama, GUI ajanı, ön uç web kodlama, grounding gibi birçok görevde 8 kat daha büyük parametreli Qwen2.5-VL-72B modelini bile aşan performans gösterir. Önde gelen pekiştirmeli öğrenme teknikleri sayesinde, düşünce zinciri akıl yürütme yoluyla cevapların doğruluğu ve zenginliği artırılmıştır; nihai sonuçlar ve açıklanabilirlik açısından geleneksel düşünce zinciri olmayan modellerin çok ötesindedir."}, "glm-4.1v-thinking-flashx": {"description": "GLM-4.1V-Thinking serisi modeller, bilinen 10 milyar parametre seviyesindeki VLM modelleri arasında en güçlü görsel modellerdir. Aynı seviyedeki SOTA görsel dil görevlerini birleştirir; video anlama, g<PERSON><PERSON><PERSON> soru-cevap, akademik problem çözme, OCR metin tanıma, belge ve grafik yorumlama, GUI ajanı, ön uç web kodlama, grounding gibi birçok görevde 8 kat daha büyük parametreli Qwen2.5-VL-72B modelini bile aşan performans gösterir. Önde gelen pekiştirmeli öğrenme teknikleri sayesinde, düşünce zinciri akıl yürütme yoluyla cevapların doğruluğu ve zenginliği artırılmıştır; nihai sonuçlar ve açıklanabilirlik açısından geleneksel düşünce zinciri olmayan modellerin çok ötesindedir."}, "glm-4.5": {"description": "<PERSON><PERSON><PERSON>'nun en yeni amiral gemisi modeli, d<PERSON><PERSON><PERSON><PERSON><PERSON> modu geçişini destekler ve genel yetenekleri açık kaynak modeller arasında SOTA seviyesine ulaşmıştır; bağlam uzunluğu 128K'ya kadar çıkabilir."}, "glm-4.5-air": {"description": "GLM-4.5'in hafif versiyonu olup performans ve maliyet et<PERSON><PERSON> den<PERSON>; hibrit düşünme modeli olarak esnek geçiş sağlar."}, "glm-4.5-airx": {"description": "GLM-4.5-Air'in ultra hızlı versiyonu olup daha hızlı yanıt süresi sunar ve büyük ölçekli yüksek hız gereksinimleri için tasarlanmıştır."}, "glm-4.5-flash": {"description": "GLM-4.5'in ücretsiz versiyonu o<PERSON>, kodlama ve ajan görevlerinde üstün performans gösterir."}, "glm-4.5-x": {"description": "GLM-4.5'in ultra hızlı versiyonu olup güçlü performansla birlikte saniyede 100 token üretim hızına ulaşır."}, "glm-4v": {"description": "GLM-4V, <PERSON><PERSON><PERSON><PERSON><PERSON> gö<PERSON><PERSON><PERSON><PERSON> anlama ve akıl yür<PERSON><PERSON><PERSON> yetenekleri sunar, çeşitli görsel görevleri destekler."}, "glm-4v-flash": {"description": "GLM-4V-Flash, hızlı görsel analiz veya toplu görsel işleme gibi sahnelerde, tek bir görüntü anlayışına odaklanarak etkili bir performans sunar."}, "glm-4v-plus": {"description": "GLM-4V-Plus, video içeriği ve çoklu görüntüleri anlama <PERSON>, çok modlu görevler için uygundur."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus, video içeriği ve çoklu görüntüleri anlama <PERSON>, çok modlu görevler için uygundur."}, "glm-z1-air": {"description": "Çıkarım modeli: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yet<PERSON>, derin çıkarım gerektiren görevler için uygundur."}, "glm-z1-airx": {"description": "Hızlı çıkarım: S<PERSON>per hızlı çıkarım hızı ve güçlü çıkarım etkisi sunar."}, "glm-z1-flash": {"description": "GLM-<PERSON><PERSON> se<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> çı<PERSON>ı<PERSON> yeteneklerine sahiptir ve mantıksal çıkarım, mate<PERSON><PERSON>, <PERSON><PERSON>a gibi alanlarda üstün performans gösterir."}, "glm-z1-flashx": {"description": "Yüksek hız ve düşük maliyet: <PERSON>tiril<PERSON> versiyon, ultra hızlı çıkarım hızı ve daha hızlı eşzamanlılık garantisi sunar."}, "glm-zero-preview": {"description": "GLM-Zero-Preview, karmaşık akıl yürütme yeteneklerine sahip olup, mantıksal akıl yür<PERSON><PERSON><PERSON>, mate<PERSON><PERSON>, <PERSON>lama gibi alanlarda mükemmel performans sergilemektedir."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> a<PERSON> kullanım<PERSON>, çok modlu üretim ve 1M token bağlam penceresi dahil olmak üzere bir sonraki nesil özellikler ve iyileştirmeler sunar."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 <PERSON>, Google'ın en yeni deneysel çok modlu AI modelidir ve önceki sürümlere göre belirli bir kalite artışı sağlamaktadır, özellikle dünya bilgisi, kod ve uzun bağlam için."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash, Google'ın en gelişmiş ana modeli olup, ileri dü<PERSON> akıl yür<PERSON><PERSON><PERSON>, kod<PERSON><PERSON>, matematik ve bilimsel görevler için tasarlanmıştır. Yerleşik \"düşünme\" yetene<PERSON><PERSON> sayesinde, daha yüksek doğruluk ve ayrıntılı bağlam işleme ile yanıtlar sunabilir.\n\nNot: Bu modelin iki varyantı vardır: düşünme ve düşünmeme. Çıktı fiyatlandırması, düşünme yeteneğinin etkin olup olmamasına göre önemli ölçüde farklılık gösterir. Standart varyantı (\" :thinking\" eki olmayan) seçerseniz, model düşünme tokenları üretmekten açıkça kaçınır.\n\nDüşünme yeteneğinden yararlanmak ve düşünme tokenları almak için \" :thinking\" varyantını seçmeniz gerekir; bu, daha yüks<PERSON> bir düşünme çıktı fiyatlandırmasıyla sonuçlanır.\n\nAyrıca, Gemini 2.5 Flash, belgeler<PERSON> belirtildiği gibi (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning) \"maksimum akıl yürütme token sayısı\" parametresi ile yapılandırılabilir."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash, Google'ın en gelişmiş ana modelidir ve ileri düzey akıl yürütme, kod<PERSON><PERSON>, matematik ve bilimsel görevler için tasarlanmıştır. Daha yüksek doğruluk ve ayrıntılı bağlam işleme ile yanıtlar sunabilen yerleşik 'düşünme' yeteneğine sahiptir.\n\nNot: Bu modelin iki varyantı vardır: düşünme ve düşünmeme. Çıktı fiyatlandırması, düşünme yeteneğinin etkin olup olmamasına göre önemli ölçüde farklılık gösterir. Standart varyantı (':thinking' eki olmadan) seçerseniz, model açıkça düşünme tokenleri üretmekten kaçınacaktır.\n\nDüşünme yeteneğinden yararlanmak ve düşünme tokenleri almak için, ':thinking' varyantını seçmelisiniz; bu, daha yüks<PERSON> düşünme çıktı fiyatlandırması ile sonuçlanacaktır.\n\nAyrıca, Gemini 2.5 Flash, belgede belirtildiği gibi 'akıl yürütme maksimum token sayısı' parametresi ile yapılandırılabilir (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash, Google'ın en gelişmiş ana modelidir ve ileri düzey akıl yürütme, kod<PERSON><PERSON>, matematik ve bilimsel görevler için tasarlanmıştır. Daha yüksek doğruluk ve ayrıntılı bağlam işleme ile yanıtlar sunabilen yerleşik 'düşünme' yeteneğine sahiptir.\n\nNot: Bu modelin iki varyantı vardır: düşünme ve düşünmeme. Çıktı fiyatlandırması, düşünme yeteneğinin etkin olup olmamasına göre önemli ölçüde farklılık gösterir. Standart varyantı (':thinking' eki olmadan) seçerseniz, model açıkça düşünme tokenleri üretmekten kaçınacaktır.\n\nDüşünme yeteneğinden yararlanmak ve düşünme tokenleri almak için, ':thinking' varyantını seçmelisiniz; bu, daha yüks<PERSON> düşünme çıktı fiyatlandırması ile sonuçlanacaktır.\n\nAyrıca, Gemini 2.5 Flash, belgede belirtildiği gibi 'akıl yürütme maksimum token sayısı' parametresi ile yapılandırılabilir (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro, Google'ın en gelişmiş düşünme modeli olup, k<PERSON><PERSON><PERSON>, matematik ve STEM alanlarındaki karmaşık sorunları akıl yürütebilir ve uzun bağlam kullanarak büyük veri setleri, kod tabanları ve belgeleri analiz edebilir."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 <PERSON> Önizlemesi, Google'ın en gelişmiş düşünce modeli olup, <PERSON><PERSON><PERSON><PERSON>, matematik ve STEM alanlarındaki karmaşık sorunları çözme yeteneğine sahiptir ve uzun bağlam kullanarak büyük veri setleri, kod tabanları ve belgeleri analiz edebilir."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash, optimize edilmiş çok modlu işleme yetenekleri sunar ve çeşitli karmaşık görev senaryolarına uygundur."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro, en son optimize edilmiş teknolojileri birleştirerek daha verimli çok modlu veri işleme yetenekle<PERSON> sunar."}, "google/gemma-2-27b": {"description": "Gemma 2, Google tarafından sunulan verimli bir modeldir, küçük uygulamalardan karmaşık veri işleme senaryolarına kadar çeşitli uygulama alanlarını kapsar."}, "google/gemma-2-27b-it": {"description": "Gemma 2, hafiflik ve verimlilik tasarım felsefesini sürdürmektedir."}, "google/gemma-2-2b-it": {"description": "Google'ın ha<PERSON>f ta<PERSON>at a<PERSON> modeli"}, "google/gemma-2-9b": {"description": "Gemma 2, Google tarafından sunulan verimli bir modeldir, küçük uygulamalardan karmaşık veri işleme senaryolarına kadar çeşitli uygulama alanlarını kapsar."}, "google/gemma-2-9b-it": {"description": "Gemma 2, Google'ın hafif açık kaynak metin modeli serisidir."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2, Google'ın hafif açık kaynak metin modeli serisidir."}, "google/gemma-2b-it": {"description": "<PERSON> Instruct (2B), temel talimat iş<PERSON><PERSON> sunar ve hafif u<PERSON> i<PERSON> u<PERSON>."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B, Google tarafından geliştirilen açık kaynaklı bir dil modelidir ve verimlilik ile performansta yeni standartlar belirlemiştir."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B, Google'ın verimlilik ve performans açısından yeni standartlar belirleyen açık kaynaklı bir dil modelidir."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo, ç<PERSON><PERSON><PERSON><PERSON> metin üretimi ve anlama görevleri i<PERSON><PERSON>, <PERSON>u anda gpt-3.5-turbo-0125'e işaret ediyor."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo, ç<PERSON><PERSON><PERSON><PERSON> metin üretimi ve anlama görevleri i<PERSON><PERSON>, <PERSON>u anda gpt-3.5-turbo-0125'e işaret ediyor."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo, ç<PERSON><PERSON><PERSON><PERSON> metin üretimi ve anlama görevleri i<PERSON><PERSON>, <PERSON>u anda gpt-3.5-turbo-0125'e işaret ediyor."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo, ç<PERSON><PERSON><PERSON><PERSON> metin üretimi ve anlama görevleri i<PERSON><PERSON>, <PERSON>u anda gpt-3.5-turbo-0125'e işaret ediyor."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo, OpenAI tarafından sağlanan verimli bir modeldir ve sohbet ve metin üretim görevleri için u<PERSON>du<PERSON>, paralel fonksiyon çağrılarını destekler."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k, karmaşık görevler için uygun yüksek kapasiteli bir metin üretim modelidir."}, "gpt-4": {"description": "GPT-4, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> bir bağlam penceresi sunarak daha uzun metin girişlerini işleyebilir, geniş bilgi entegrasyonu ve veri analizi gerektiren senaryolar için uygundur."}, "gpt-4-0125-preview": {"description": "En son GPT-4 Turbo modeli görsel işlevselliğe sahiptir. Artık görsel talepler JSON formatı ve fonksiyon çağrıları ile işlenebilir. GPT-4 Turbo, çok modlu görevler için maliyet etkin bir destek sunan geliştirilmiş bir versiyondur. Doğruluk ve verimlilik arasında bir denge sağlar, gerçek zamanlı etkileşim gerektiren uygulama senaryoları için uygundur."}, "gpt-4-0613": {"description": "GPT-4, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> bir bağlam penceresi sunarak daha uzun metin girişlerini işleyebilir, geniş bilgi entegrasyonu ve veri analizi gerektiren senaryolar için uygundur."}, "gpt-4-1106-preview": {"description": "En son GPT-4 Turbo modeli görsel işlevselliğe sahiptir. Artık görsel talepler JSON formatı ve fonksiyon çağrıları ile işlenebilir. GPT-4 Turbo, çok modlu görevler için maliyet etkin bir destek sunan geliştirilmiş bir versiyondur. Doğruluk ve verimlilik arasında bir denge sağlar, gerçek zamanlı etkileşim gerektiren uygulama senaryoları için uygundur."}, "gpt-4-32k": {"description": "GPT-4, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> bir bağlam penceresi sunarak daha uzun metin girişlerini işleyebilir, geniş bilgi entegrasyonu ve veri analizi gerektiren senaryolar için uygundur."}, "gpt-4-32k-0613": {"description": "GPT-4, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> bir bağlam penceresi sunarak daha uzun metin girişlerini işleyebilir, geniş bilgi entegrasyonu ve veri analizi gerektiren senaryolar için uygundur."}, "gpt-4-turbo": {"description": "En son GPT-4 Turbo modeli görsel işlevselliğe sahiptir. Artık görsel talepler JSON formatı ve fonksiyon çağrıları ile işlenebilir. GPT-4 Turbo, çok modlu görevler için maliyet etkin bir destek sunan geliştirilmiş bir versiyondur. Doğruluk ve verimlilik arasında bir denge sağlar, gerçek zamanlı etkileşim gerektiren uygulama senaryoları için uygundur."}, "gpt-4-turbo-2024-04-09": {"description": "En son GPT-4 Turbo modeli görsel işlevselliğe sahiptir. Artık görsel talepler JSON formatı ve fonksiyon çağrıları ile işlenebilir. GPT-4 Turbo, çok modlu görevler için maliyet etkin bir destek sunan geliştirilmiş bir versiyondur. Doğruluk ve verimlilik arasında bir denge sağlar, gerçek zamanlı etkileşim gerektiren uygulama senaryoları için uygundur."}, "gpt-4-turbo-preview": {"description": "En son GPT-4 Turbo modeli görsel işlevselliğe sahiptir. Artık görsel talepler JSON formatı ve fonksiyon çağrıları ile işlenebilir. GPT-4 Turbo, çok modlu görevler için maliyet etkin bir destek sunan geliştirilmiş bir versiyondur. Doğruluk ve verimlilik arasında bir denge sağlar, gerçek zamanlı etkileşim gerektiren uygulama senaryoları için uygundur."}, "gpt-4-vision-preview": {"description": "En son GPT-4 Turbo modeli görsel işlevselliğe sahiptir. Artık görsel talepler JSON formatı ve fonksiyon çağrıları ile işlenebilir. GPT-4 Turbo, çok modlu görevler için maliyet etkin bir destek sunan geliştirilmiş bir versiyondur. Doğruluk ve verimlilik arasında bir denge sağlar, gerçek zamanlı etkileşim gerektiren uygulama senaryoları için uygundur."}, "gpt-4.1": {"description": "GPT-4.1, karmaşık görevler i<PERSON>in k<PERSON>ımız amiral gemisi modelidir. Farklı alanlarda sorunları çözmek için son derece uygundur."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini, z<PERSON>, hız ve maliyet arasında bir denge sunarak birçok kullanım durumu için çekici bir model haline getirir."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini, z<PERSON>, hız ve maliyet arasında bir denge sunarak birçok kullanım durumu için çekici bir model haline getirir."}, "gpt-4.5-preview": {"description": "GPT-4.5'in araştırma önizleme sürümü, şimdiye kadar geliştirdiğimiz en büyük ve en güçlü GPT modelidir. Geniş bir dünya bilgisine sahip olup, kullanıcı niyetlerini daha iyi anlayarak yaratıcı görevler ve bağımsız planlama konularında mükemmel bir performans sergilemektedir. GPT-4.5, metin ve görsel girdi alabilir ve metin çıktısı (yapılandırılmış çıktı dahil) üretebilir. Fonksiyon çağrıları, toplu API ve akış çıktısı gibi önemli geliştirici özelliklerini destekler. Yaratıcılık, açık düşünme ve diyalog gerektiren görevlerde (örneğin yazma, öğrenme veya yeni fikirler keşfetme) GPT-4.5 özellikle başarılıdır. Bilgi kesim tarihi Ekim 2023'tür."}, "gpt-4o": {"description": "ChatGPT-4o, güncel versiyonunu korumak için gerçek zamanlı olarak güncellenen dinamik bir modeldir. Güçlü dil anlama ve üretme yeteneklerini birleştirir, m<PERSON><PERSON><PERSON>i <PERSON>leri, eğitim ve teknik destek gibi geniş ölçekli uygulama senaryoları için uygundur."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o, güncel versiyonunu korumak için gerçek zamanlı olarak güncellenen dinamik bir modeldir. Güçlü dil anlama ve üretme yeteneklerini birleştirir, m<PERSON><PERSON><PERSON>i <PERSON>leri, eğitim ve teknik destek gibi geniş ölçekli uygulama senaryoları için uygundur."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o, güncel versiyonunu korumak için gerçek zamanlı olarak güncellenen dinamik bir modeldir. Güçlü dil anlama ve üretme yeteneklerini birleştirir, m<PERSON><PERSON><PERSON>i <PERSON>leri, eğitim ve teknik destek gibi geniş ölçekli uygulama senaryoları için uygundur."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o, g<PERSON>nce<PERSON> en son sürü<PERSON>ü korumak için gerçek zamanlı olarak güncellenen dinamik bir modeldir. Müşteri hizmetleri, eğitim ve teknik destek gibi büyük ölçekli uygulama senaryoları için güçlü dil anlama ve üretme yeteneklerini bir araya getirir."}, "gpt-4o-audio-preview": {"description": "GPT-4<PERSON> Ses modeli, se<PERSON><PERSON> giriş ve çıkış desteği sunar."}, "gpt-4o-mini": {"description": "GPT-4o mini, OpenAI'nin GPT-4 Omni'den sonra tanıttığı en yeni modeldir. <PERSON><PERSON><PERSON><PERSON> ve metin girişi destekler ve metin çıktısı verir. En gelişmiş küçük model olarak, diğer son zamanlardaki öncü modellere göre çok daha ucuzdur ve GPT-3.5 Turbo'dan %60'tan fazla daha ucuzdur. En son teknolojiyi korurken, önemli bir maliyet etkinliği sunar. GPT-4o mini, MMLU testinde %82 puan almış olup, şu anda sohbet tercihleri açısından GPT-4'ün üzerinde yer almaktadır."}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Ses modeli, sesli giriş ve çıkışı destekler."}, "gpt-4o-mini-realtime-preview": {"description": "GPT-4o-mini gerçek zamanlı versiyonu, ses ve metin için gerçek zamanlı giriş ve çıkış desteği sunar."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini arama önizleme sürümü, web arama sorgularını anlama ve yürütme için özel olarak eğitilmiş bir modeldir ve Chat Completions API kullanır. Jeton ücretlerinin yanı sıra, web arama sorguları her araç çağrısı başına ücretlendirilir."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe, GPT-4o kullanarak sesleri metne dönüştüren bir konuşma tanıma modelidir. Orijinal Whisper modeline kıyasla kelime hata oranını düşürür ve dil tanıma ile doğruluğu artırır. Daha doğru transkripsiyonlar için kullanın."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS, GPT-4o mini'ye dayalı bir metin-ses modeldir ve yüksek kaliteli ses üretimi, <PERSON><PERSON><PERSON><PERSON><PERSON> maliyetli oluşturma sunar."}, "gpt-4o-realtime-preview": {"description": "GPT-4o gerçek zamanlı versiyonu, ses ve metin için gerçek zamanlı giriş ve çıkış desteği sunar."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "GPT-4o gerçek zamanlı versiyonu, ses ve metin için gerçek zamanlı giriş ve çıkış desteği sunar."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "GPT-4o gerçek zamanlı sürümü, ses ve metin giriş-çıkışını gerçek zamanlı destekler."}, "gpt-4o-search-preview": {"description": "GPT-4o arama önizleme sürümü, web arama sorgularını anlama ve yürütme için özel olarak eğitilmiş bir modeldir ve Chat Completions API kullanır. Jeton ücretlerinin yanı sıra, web arama sorguları her araç çağrısı başına ücretlendirilir."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe, GPT-4o kullanarak sesleri metne dönüştüren bir konuşma tanıma modelidir. Orijinal Whisper modeline kıyasla kelime hata oranını düşürür ve dil tanıma ile doğruluğu artırır. Daha doğru transkripsiyonlar için kullanın."}, "gpt-image-1": {"description": "ChatGPT'nin yerel çok modlu görüntü oluşturma modeli"}, "grok-2-1212": {"description": "Bu model, <PERSON><PERSON><PERSON><PERSON>, talimat takibi ve çok dilli yetenekler açısından geliştirilmiştir."}, "grok-2-image-1212": {"description": "En yeni görüntü oluşturma modelimiz, metin istemlerine dayanarak canlı ve gerçekçi görüntüler oluşturabilir. <PERSON><PERSON><PERSON>a, sosyal medya ve eğlence gibi alanlarda görüntü üretiminde mükemmel performans sergiler."}, "grok-2-vision-1212": {"description": "Bu model, <PERSON><PERSON><PERSON><PERSON>, talimat takibi ve çok dilli yetenekler açısından geliştirilmiştir."}, "grok-3": {"description": "Amiral gemisi model o<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> ve metin özetleme gibi kurumsal uygulamalarda uzmandır; finans, <PERSON><PERSON><PERSON><PERSON><PERSON>, hukuk ve bilim alanlarında derin bilgiye sahiptir."}, "grok-3-fast": {"description": "Amiral gemisi model o<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> ve metin özetleme gibi kurumsal uygulamalarda uzmandır; finans, <PERSON><PERSON><PERSON><PERSON><PERSON>, hukuk ve bilim alanlarında derin bilgiye sahiptir."}, "grok-3-mini": {"description": "Hafif model o<PERSON><PERSON>, konuşma öncesi düşünür. <PERSON><PERSON>zlı ve akıllı çalışır, derin alan bilgisi gerektirmeyen mantıksal görevler için uygundur ve orijinal düşünce izlerini elde edebilir."}, "grok-3-mini-fast": {"description": "Hafif model o<PERSON><PERSON>, konuşma öncesi düşünür. <PERSON><PERSON>zlı ve akıllı çalışır, derin alan bilgisi gerektirmeyen mantıksal görevler için uygundur ve orijinal düşünce izlerini elde edebilir."}, "grok-4": {"description": "En yeni ve en güçlü amiral gemisi modelimiz, do<PERSON><PERSON> dil işleme, matematiksel hesaplama ve akıl yürütme alanlarında üstün performans sergiliyor — mükemmel bir çok yönlü oyuncu."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B, birden fazla üst düzey modelin birleşimiyle yaratıcı ve zeka odaklı bir dil modelidir."}, "hunyuan-a13b": {"description": "Hunyuan'ın ilk karma akıl yürütme modeli olan hunyuan-standard-256K'nın yükseltilmiş versiyonu, toplam 80 milyar parametre ve 13 milyar parametre aktive eder. Varsayılan olarak yavaş düşünme modundadır ve parametre veya komut yoluyla hızlı ve yavaş düşünme modları arasında geçişi destekler; hızlı/yavaş düşünme geçişi için sorguya / no_think eklenir. Genel yetenekler önceki nesle göre kapsamlı şekilde geliştirilmiş olup, özellikle matematik, bilim, uzun metin anlama ve ajan yeteneklerinde belirgin artışlar vardır."}, "hunyuan-code": {"description": "Hunyuan'ın en son kod oluşturma modeli, 200B yüksek kaliteli kod verisi ile artırılmış temel model ile altı ay boyunca yüksek kaliteli SFT verisi eğitimi almıştır. Bağlam penceresi uzunluğu 8K'ya çıkarılmıştır ve beş büyük dil için kod oluşturma otomatik değerlendirme göstergelerinde ön sıralardadır; beş büyük dilde 10 kriterin her yönüyle yüksek kaliteli değerlendirmelerde performansı birinci sıradadır."}, "hunyuan-functioncall": {"description": "Hunyuan'ın en son MOE mimarisi FunctionCall modeli, y<PERSON><PERSON><PERSON> kaliteli FunctionCall verisi ile eğitilmiş olup, ba<PERSON>lam penceresi 32K'ya ulaşmıştır ve birçok boyutta değerlendirme göstergelerinde lider konumdadır."}, "hunyuan-large": {"description": "Hunyuan-large modelinin toplam parametre sayısı yaklaşık 389B, etkin parametre sayısı yaklaşık 52B'dir; bu, mevcut endüstrideki en büyük parametre ölçeğine sahip ve en iyi performansı gösteren Transformer mimarisinin açık kaynaklı MoE modelidir."}, "hunyuan-large-longcontext": {"description": "Uzun metin gö<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> belge özeti ve belge sorgulama gibi, işleme konusunda uzmandır; aynı zamanda genel metin oluşturma görevlerini de yerine getirme yeteneğine sahiptir. Uzun metinlerin analizi ve oluşturulmasında mükemmel bir performans sergiler, karmaşık ve ayrıntılı uzun metin içerik işleme ihtiyaçlarına etkili bir şekilde yanıt verebilir."}, "hunyuan-large-vision": {"description": "B<PERSON> model, g<PERSON><PERSON><PERSON> ve metin anlama senaryoları için uygundur. Hunyuan Large tabanlı görsel-dil büyük modelidir, herhangi bir çözünürlükte çoklu resim ve metin giri<PERSON><PERSON> destekler, metin üret<PERSON>, g<PERSON>rsel-metinsel anlama görevlerine odaklanır ve çok dilli görsel-metinsel anlama yeteneğinde belirgin gelişme sağ<PERSON>."}, "hunyuan-lite": {"description": "MOE yapısına y<PERSON><PERSON>, bağlam penceresi 256<PERSON>, NLP, kod, matematik, endüstri gibi birçok değerlendirme setinde birçok açık kaynak modelden önde."}, "hunyuan-lite-vision": {"description": "Hunyuan'ın en son 7B çok modlu modeli, b<PERSON><PERSON><PERSON> penceresi 32K, Ç<PERSON>ce ve İngilizce senaryolarında çok modlu diyalog, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, be<PERSON> tablo <PERSON>, çok modlu matematik vb. destekler; birçok boyutta değerlendirme kriterleri 7B rakip modellerden üstündür."}, "hunyuan-pro": {"description": "Trilyon seviyesinde parametre ölçeğine sahip MOE-32K uzun metin modeli. Çeşitli benchmarklarda kesin bir liderlik seviyesine ulaşarak, karmaşık talimatlar ve akıl yürütme yetenekleri ile karmaşık matematik yetenekleri sunar, functioncall desteği ile çok dilli çeviri, finans, hukuk ve sağlık gibi alanlarda önemli optimizasyonlar sağlar."}, "hunyuan-role": {"description": "Hunyuan'ın en son rol yapma modeli, Hunyuan resmi ince ayar eğitimi ile geliştirilmiş rol yapma modelidir. Hunyuan modeli ile rol yapma senaryosu veri seti birleştirilerek artırılmıştır ve rol yapma senaryolarında daha iyi temel performans sunmaktadır."}, "hunyuan-standard": {"description": "Daha iyi bir yönlendirme stratejisi kull<PERSON>, yük dengeleme ve uzman yakınsaması sorunlarını hafifletir. <PERSON><PERSON><PERSON> met<PERSON><PERSON><PERSON>, iğne arama göstergesi %99.9'a ulaşmaktadır. MOE-32K, uzun metin girişlerini işleme yeteneği ile etki ve fiyat dengesini sağlarken, maliyet açısından daha yüksek bir değer sunar."}, "hunyuan-standard-256K": {"description": "Daha iyi bir yönlendirme stratejisi kull<PERSON>, yük dengeleme ve uzman yakınsaması sorunlarını hafifletir. <PERSON><PERSON><PERSON> met<PERSON>, iğne arama göstergesi %99.9'a ulaşmaktadır. MOE-256K, uzunluk ve etki açısından daha fazla bir sıçrama yaparak, girdi uzunluğunu büyük ölçüde genişletir."}, "hunyuan-standard-vision": {"description": "Hunyuan'ın en son çok modlu modeli, çok dilli yan<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ce ve İngilizce yetenekleri dengelidir."}, "hunyuan-t1-20250321": {"description": "Modelin hem fen hem de sosyal bilimler alanındaki yeteneklerini kapsamlı bir şekilde inşa eder, uzun metin bilgilerini yakalama yeteneği yüksektir. Her türlü zorluktaki matematik/ mantık çıkarımı/ bilim/ kod gibi bilimsel sorunları çözme yeteneğini destekler."}, "hunyuan-t1-20250403": {"description": "Proje düzeyinde kod üretme yeteneğini artırır; metin oluşturma ve yazma kalitesini yükseltir; metin anlam<PERSON>, çok turlu konu takibi, toB komut uyumu ve kelime-anlama yeteneklerini geliştirir; karmaşık geleneksel ve basitleştirilmiş Çince ile İngilizce karışık çıktı sorunlarını optimize eder."}, "hunyuan-t1-20250529": {"description": "Metin oluşturma ve kompozisyon yazımını optimize eder; kod ön yüz<PERSON>, matemat<PERSON>, mantıksal çıkarım gibi fen bilimleri yeteneklerini geliştirir ve talimatlara uyum yeteneğini artırır."}, "hunyuan-t1-20250711": {"description": "<PERSON><PERSON><PERSON> matematik, mantık ve kodlama yeteneklerinde büyük iyileştirmeler <PERSON>, model <PERSON><PERSON><PERSON><PERSON>lığını optimize eder ve uzun metin işleme kapasitesini artırır."}, "hunyuan-t1-latest": {"description": "Sektördeki ilk ultra büyük ölçekli Hybrid-Transformer-<PERSON><PERSON> modeli, ç<PERSON><PERSON><PERSON><PERSON> yet<PERSON>rini genişletir, y<PERSON>ks<PERSON>özümleme hızı sunar ve insan tercihleri ile daha iyi hizalanır."}, "hunyuan-t1-vision": {"description": "Hunyuan çok modlu anlayış derin dü<PERSON>ünme modeli, çok modlu doğal uzun düşünce zincirini destekler, çeşitli görsel çıkarım senaryolarında uzmandır ve fen bilimleri problemlerinde hızlı düşünme modellerine kıyasla kapsamlı iyileşme sağlar."}, "hunyuan-t1-vision-20250619": {"description": "Hunyuan'ın en yeni t1-vision çok modlu anlama derin dü<PERSON>ünme modeli, çok modlu doğal düşünce zincirini destekler ve önceki nesil varsayılan modele kıyasla kapsamlı iyileştirmeler sunar."}, "hunyuan-turbo": {"description": "Hunyuan'ın yeni nesil büyük dil modelinin önizleme sürümü, tamamen yeni bir karma uzman modeli (MoE) yapısı kullanır ve hunyuan-pro'ya kıyasla daha hızlı çıkarım verimliliği ve daha güçlü performans sunar."}, "hunyuan-turbo-20241223": {"description": "Bu sürümde yapılan optimizasyonlar: veri talimatı ölçeklendirme, modelin genel genelleme yeteneğini büyük ölçüde artırma; matemat<PERSON>, kod<PERSON><PERSON>, mantıksal akıl yürütme yeteneklerini büyük ölçüde artırma; metin anlama ve kelime anlama ile ilgili yetenekleri optimize etme; metin oluşturma içerik üretim kalitesini optimize etme."}, "hunyuan-turbo-latest": {"description": "<PERSON>l den<PERSON><PERSON>, <PERSON><PERSON>, metin <PERSON>, so<PERSON><PERSON>, bil<PERSON> so<PERSON>, <PERSON><PERSON><PERSON>, alan vb. dahil; insan benzeri özellikleri artırma, modelin duygusal z<PERSON>sını optimize etme; niyet belirsiz olduğunda modelin aktif olarak netleştirme yeteneğini artırma; kelime ve terim analizi ile ilgili sorunların işlenme yeteneğini artırma; yaratım kalitesini ve etkileşimliğini artırma; çoklu tur deneyimini geliştirme."}, "hunyuan-turbo-vision": {"description": "Hunyuan'ın yeni nesil görsel dil amiral modeli, tamamen yeni bir karışık uzman modeli (MoE) yapısını benimser; metin ve görüntü anlama ile ilgili temel tanı<PERSON>, <PERSON><PERSON><PERSON><PERSON>, bi<PERSON><PERSON> so<PERSON>, analiz ve akıl yürütme gibi yeteneklerde bir önceki nesil modele göre kapsamlı bir iyileştirme sağlar."}, "hunyuan-turbos-20250313": {"description": "Matematik problem ç<PERSON>zme adımların<PERSON>n stilini bi<PERSON>tiri<PERSON>, matematikte çok turlu soru-cevapları güçlendirir. <PERSON><PERSON>, yan<PERSON>t stilini optimize eder, ya<PERSON><PERSON> zeka <PERSON> kaldırır, ed<PERSON><PERSON> if<PERSON> artırır."}, "hunyuan-turbos-20250416": {"description": "Ön eğitim tabanı yü<PERSON><PERSON><PERSON>, tabanın komut anlama ve uyum yeteneklerini güçlendirir; hizalama aşamasında matematik, k<PERSON><PERSON><PERSON>, mantık ve bilimsel alanlardaki yetenekleri artırır; yarat<PERSON><PERSON><PERSON> yazı<PERSON> ka<PERSON>, metin an<PERSON>, çeviri doğruluğu ve bilgi tabanlı soru-cevap gibi beşeri bilimler yeteneklerini geliştirir; çeşitli alanlardaki ajan yetene<PERSON>rini güçlendirir, özellikle çok turlu diyalog anlama yeteneğine odaklanır."}, "hunyuan-turbos-20250604": {"description": "Ön eğitim tabanı yükseltildi; yazma ve okuduğunu anlama yetenekleri geliştirildi; kodlama ve fen bilimleri yeteneklerinde önemli iyileştirmeler sağlandı; karmaşık talimatlara uyum gibi alanlarda sürekli gelişme devam ediyor."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> dü<PERSON><PERSON><PERSON> ve daha iyi deneyim sunan en son s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Uzun metin gö<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> belge özetleme ve belge yanıtları gibi, işleme konusunda uzmandır ve genel metin oluşturma görevlerini de yerine getirebilir. Uzun metinlerin analizi ve oluşturulmasında mükemmel performans gösterir, karmaşık ve ayrıntılı uzun metin içerik işleme ihtiyaçlarını etkili bir şekilde karşılar."}, "hunyuan-turbos-role-plus": {"description": "Hunyuan'ın en son rol yapma modeli, Hunyuan tarafından resmi olarak ince ayar ve eğitimle geliştirilmiş, rol yapma senaryoları veri setiyle artırılmıştır ve rol yapma senaryolarında daha iyi temel performans sunar."}, "hunyuan-turbos-vision": {"description": "B<PERSON> model, g<PERSON><PERSON><PERSON> ve metin anlama senaryoları için uygundur ve Hunyuan'ın en yeni turbos tabanlı yeni nesil görsel dil amiral gemisi büyük modelidir. Görsel tabanlı varl<PERSON> tan<PERSON>, bi<PERSON><PERSON> so<PERSON>, met<PERSON>, fotoğrafla problem çözme gibi görevlerde odaklanır ve önceki nesil modele kıyasla kapsamlı iyileştirmeler içerir."}, "hunyuan-turbos-vision-20250619": {"description": "Hunyuan'ın en yeni turbos-vision görsel dil amiral gemisi büyük modeli, g<PERSON>rsel ve metin anlama ile ilgili gö<PERSON>, g<PERSON><PERSON><PERSON> tabanlı varlık tanıma, bil<PERSON> sorg<PERSON>, metin <PERSON>, fotoğrafla problem çözme gibi alanlarda önceki nesil varsayılan modele kıyasla kapsamlı iyileştirmeler sunar."}, "hunyuan-vision": {"description": "Hunyuan'ın en son çok modlu modeli, resim + metin girişi ile metin içeriği oluşturmayı destekler."}, "image-01": {"description": "Yepyeni görüntü oluşturma modeli, ince detaylı görseller sunar; metinden görüntü ve görüntüden görüntü desteği vardır."}, "image-01-live": {"description": "Görüntü oluşturma modeli, ince detaylı görseller sunar; metinden görüntü oluşturmayı ve stil ayarlarını destekler."}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 4. <PERSON><PERSON><PERSON> metinden görüntüye model se<PERSON>i"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 4. ne<PERSON><PERSON> metinden görüntüye model se<PERSON><PERSON> versiyonu"}, "imagen4/preview": {"description": "Google'ın en yüksek kaliteli görüntü oluşturma modeli"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5, çoklu senaryolarda akıllı diyalog çözümleri sunar."}, "internlm2.5-latest": {"description": "En son model <PERSON><PERSON><PERSON>, <PERSON>lağanü<PERSON><PERSON> çıkarım performansı<PERSON>, 1M bağlam uzunluğunu destekler ve daha güçlü talimat takibi ve araç çağırma yetenekleri sunar."}, "internlm3-latest": {"description": "En son model se<PERSON><PERSON>, olağanüstü çıkarım performansına sahiptir ve aynı ölçekli açık kaynak modeller arasında liderdir. Varsayılan olarak en son yayımlanan InternLM3 serisi modellerine işaret eder."}, "internvl2.5-latest": {"description": "Hala bakımını yaptığımız InternVL2.5 s<PERSON><PERSON><PERSON><PERSON><PERSON>, mükemmel ve istikrarlı bir performansa sahiptir. Varsayılan olarak en son yayımladığımız InternVL2.5 serisi modele işaret eder, şu anda internvl2.5-78b'ye işaret ediyor."}, "internvl3-latest": {"description": "En son yayı<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z çok modlu büyük model, <PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> metin-g<PERSON><PERSON><PERSON><PERSON><PERSON> anlama yeteneği ve uzun süreli görüntü anlama yeteneğine sahiptir; performansı en iyi kapalı kaynak modellerle karşılaştırılabilir. Varsayılan olarak en son yayımladığımız InternVL serisi modele işaret eder, şu anda internvl3-78b'ye işaret ediyor."}, "irag-1.0": {"description": "Baidu tarafından geliştirilen iRAG (image based RAG), arama destekli metinden görüntü oluşturma teknolojisidir. Baidu'nun milyarlarca görsel kaynağı ile güçlü temel model yeteneklerini birleştirerek çok gerçekçi görüntüler oluşturur. Genel performansı metinden görüntü oluşturma sistemlerinin çok ötesindedir, yapay zeka izini ortadan kaldırır ve maliyeti düşürür. iRAG, ha<PERSON><PERSON><PERSON><PERSON><PERSON> yapmama, ultra gerçekçilik ve anında erişim özelliklerine sahiptir."}, "jamba-large": {"description": "En güçlü ve en gelişmiş modelimiz, kurumsal düzeyde karmaşık görevleri işlemek için tasarlanmıştır ve olağanüstü performans sunar."}, "jamba-mini": {"description": "Sınıfındaki en verim<PERSON> model, hız ve kaliteyi den<PERSON>, <PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> bir boyuta sa<PERSON>r."}, "jina-deepsearch-v1": {"description": "<PERSON><PERSON> arama, web araması, okuma ve akıl yürütmeyi birleştirerek kapsamlı bir araştırma yapar. <PERSON><PERSON><PERSON>, araştırma görevlerinizi kabul eden bir ajan olarak düşünebilirsiniz - geniş bir arama yapar ve birden fazla yineleme ile cevap verir. <PERSON><PERSON> sü<PERSON>ç, s<PERSON><PERSON><PERSON> araştırma, akıl yürütme ve sorunları çeşitli açılardan çözmeyi içerir. Bu, doğ<PERSON>an önceden eğitilmiş verilerden cevaplar üreten standart büyük modellerle ve tek seferlik yüzey aramasına dayanan geleneksel RAG sistemleriyle temelde farklıdır."}, "kimi-k2": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Moonshot AI tarafından gel<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> kodlama ve ajan yeteneklerine sahip MoE mimarili temel modeldir; toplam 1 trilyon parametre, 32 milyar aktif parametreye sahiptir. <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, matematik ve ajan gibi ana kategorilerdeki kıyaslama testlerinde K2 modeli diğer önde gelen açık kaynak modelleri geride bırakır."}, "kimi-k2-0711-preview": {"description": "kimi-k2, son derece g<PERSON><PERSON><PERSON><PERSON> kodlama ve Agent yeteneklerine sahip MoE mimarili temel bir modeldir. Toplam parametre sayısı 1T, aktif parametre sayısı 32B'dir. <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Agent gibi ana kategorilerde yapılan kıyaslama testlerinde K2 modeli, diğer önde gelen açık kaynak modelleri geride bırakmıştır."}, "kimi-latest": {"description": "<PERSON><PERSON> akı<PERSON><PERSON> asistan ürünü, en son <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> modelini kullanır ve henüz kararlı olmayan özellikler içerebilir. Görüntü anlayışını desteklerken, is<PERSON><PERSON><PERSON> bağlam uzunluğuna göre 8k/32k/128k modelini faturalama modeli olarak otomatik olarak seçecektir."}, "kimi-thinking-preview": {"description": "kimi-thinking-preview modeli, <PERSON>y'ın karanlık yüzü tarafından sunulan çok modlu akıl yürütme ve genel akıl yürütme yeteneklerine sahip çok modlu düşünme modelidir; derin akıl yürütmede uzmandır ve daha zor sorunların çözümüne yardımcı olur."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM, öğrenme bilimleri ilkelerine uygun olarak eğitilmiş, görev odaklı deneysel bir dil modelidir. Eğitim ve öğrenim senaryolarında sistem talimatlarını takip edebilir ve uzman bir mentor olarak görev alabilir."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM, öğrenme bilimleri ilkelerine uygun olarak eğitilmiş, görev odaklı deneysel bir dil modelidir; öğretim ve öğrenim senaryolarında sistem talimatlarını takip edebilir, u<PERSON> bir mentor gibi da<PERSON>."}, "lite": {"description": "Spark Lite, son derece d<PERSON><PERSON><PERSON><PERSON> gecikme süresi ve yüksek verimlilikle çalışan hafif bir büyük dil modelidir. Tamamen ücretsiz ve açık olup, ger<PERSON><PERSON> zamanlı çevrimiçi arama işlevini desteklemektedir. Hızlı yanıt verme özelliği, düşük hesaplama gücüne sahip cihazlarda çıkarım uygulamaları ve model ince ayarlarında mükemmel performans sergileyerek, kullanıcılara maliyet etkinliği ve akıllı deneyim sunmakta, özellikle bilgi sorgulama, içerik oluşturma ve arama senaryolarında başarılı olmaktadır."}, "llama-2-7b-chat": {"description": "Llama2, Meta tarafından geliştirilmiş ve açık kaynaklı büyük dil modeli (LLM) serisidir. Bu, 7 milyar ile 70 milyar parametre arasında değ<PERSON>şen, önceden eğitilmiş ve ince ayarlanmış üretici metin modellerinden oluşan bir gruptır. <PERSON><PERSON><PERSON>, Llama2, optimize edilmiş dönüştürücü mimarisi kullanan bir otoregresif dil modelidir. Ayarlanmış versiyonlar, faydalılık ve güvenliğin insan tercihleriyle hizalanması için gözetimli ince ayarlama (SFT) ve insan geri bildirimleriyle güçlendirilmiş öğrenme (RLHF) kullanır. Llama2, Llama serisine göre çeşitli akademik veri kümelerinde daha iyi performans gösterir ve birçok diğer modelin tasarım ve geliştirilmesine ilham verir."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B, <PERSON><PERSON> güçlü AI akıl yürütme yeteneği <PERSON>, karmaşık uygulamalar için uygundur ve yüksek verimlilik ve doğruluk sağlamak için çok sayıda hesaplama işlemini destekler."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B, hızlı metin üretim yeteneği sunan yüksek performanslı bir modeldir ve büyük ölçekli verimlilik ve maliyet etkinliği gerektiren uygulama senaryoları için son derece uygundur."}, "llama-3.1-instruct": {"description": "Llama 3.1 talimat ince ayarlı modeli, diyalog senaryoları için optimize edilmiştir ve yaygın endüstri kıyaslamalarında birçok mevcut açık kaynaklı sohbet modelini geride bırakmaktadır."}, "llama-3.2-11b-vision-instruct": {"description": "Yüksek çözünürlüklü görüntülerde mükemmel görüntü akıl yür<PERSON><PERSON><PERSON>, gö<PERSON>l anlama uygulamaları için uygundur."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini birleştiren görevleri işlemek için tasarlanmıştır. Görünt<PERSON> tanımlama ve görsel soru-cevap gibi görevlerde mükemmel performans sergiler, dil üretimi ile görsel akıl yürütme arasındaki uçurumu aşar."}, "llama-3.2-90b-vision-instruct": {"description": "Görsel anlayış ajan u<PERSON>rı için ileri düzey görüntü akıl yürü<PERSON><PERSON> yeteneği."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini birleştiren görevleri işlemek için tasarlanmıştır. Görünt<PERSON> tanımlama ve görsel soru-cevap gibi görevlerde mükemmel performans sergiler, dil üretimi ile görsel akıl yürütme arasındaki uçurumu aşar."}, "llama-3.2-vision-instruct": {"description": "Llama 3.2-Vision komut ince ayarlı modeli, g<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON>, gö<PERSON><PERSON><PERSON><PERSON> açıklama ve görüntülerle ilgili genel soruları yanıtlamak için optimize edilmiştir."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3, Llama serisinin en gelişmiş çok dilli açık kaynak büyük dil modelidir ve 405B modelinin performansını çok düşük maliyetle deneyimlemenizi sağlar. Transformer yapısına dayanmaktadır ve denetimli ince ayar (SFT) ve insan geri bildirimi ile güçlendirilmiş öğrenme (RLHF) ile faydalılığını ve güvenliğini artırmıştır. Talimat ayarlı versiyonu, çok dilli diyaloglar için optimize edilmiştir ve birçok endüstri kıyaslamasında birçok açık kaynak ve kapalı sohbet modelinden daha iyi performans göstermektedir. Bilgi kesim tarihi 2023 Aralık'tır."}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 çok dilli büyük dil modeli (LLM), 70B (metin girişi/metin çıkışı) içindeki önceden eğitilmiş ve talimat ayarlanmış bir üretim modelidir. Llama 3.3 talimat ayarlı saf metin modeli, çok dilli konuşma kullanım durumları için optimize edilmiştir ve yaygın endüstri kıyaslamalarında mevcut birçok açık kaynak ve kapalı sohbet modelinden daha üstündür."}, "llama-3.3-instruct": {"description": "Llama 3.3 komut ince ayarlı modeli, diyalog senaryoları için optimize edilmiştir ve yaygın endüstri kıyaslamalarında birçok mevcut açık kaynaklı sohbet modelini geride bırakmaktadır."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B, <PERSON><PERSON><PERSON><PERSON>rmaşıklık işleme yeteneği sunar ve yüksek talepli projeler için özel olarak tasarlanmıştır."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B, y<PERSON>ksek kaliteli akıl yürütme performansı sunar ve çok çeşitli uygulama ihtiyaçları için uygundur."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use, güçlü araç ça<PERSON><PERSON>rma yetenekleri sunar ve karmaşık görevlerin verimli bir şekilde işlenmesini destekler."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use, verimli araç kullanımı için optimize edilmiş bir modeldir ve hızlı paralel hesaplamayı destekler."}, "llama3.1": {"description": "Llama 3.1, <PERSON><PERSON> tara<PERSON>ında<PERSON> sunulan ö<PERSON>ü bir modeldir, 405B parametreye kadar destekler ve karmaşık diyaloglar, çok dilli çeviri ve veri analizi alanlarında kullanılabilir."}, "llama3.1:405b": {"description": "Llama 3.1, <PERSON><PERSON> tara<PERSON>ında<PERSON> sunulan ö<PERSON>ü bir modeldir, 405B parametreye kadar destekler ve karmaşık diyaloglar, çok dilli çeviri ve veri analizi alanlarında kullanılabilir."}, "llama3.1:70b": {"description": "Llama 3.1, <PERSON><PERSON> tara<PERSON>ında<PERSON> sunulan ö<PERSON>ü bir modeldir, 405B parametreye kadar destekler ve karmaşık diyaloglar, çok dilli çeviri ve veri analizi alanlarında kullanılabilir."}, "llava": {"description": "LLaVA, görsel kodlayıcı ve Vicuna'yı birleştiren çok modlu bir modeldir, güçlü görsel ve dil anlama yetenekleri sunar."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B, gö<PERSON>l işleme yeteneklerini birleştirir ve görsel bilgi girişi ile karmaşık çıktılar üretir."}, "llava:13b": {"description": "LLaVA, görsel kodlayıcı ve Vicuna'yı birleştiren çok modlu bir modeldir, güçlü görsel ve dil anlama yetenekleri sunar."}, "llava:34b": {"description": "LLaVA, görsel kodlayıcı ve Vicuna'yı birleştiren çok modlu bir modeldir, güçlü görsel ve dil anlama yetenekleri sunar."}, "mathstral": {"description": "<PERSON><PERSON><PERSON>, bilimsel araştırma ve matematik akıl yürütme için <PERSON>, etkili hesaplama yetenekleri ve sonuç açıklamaları sunar."}, "max-32k": {"description": "Spark Max 32K, <PERSON><PERSON><PERSON><PERSON>k bağlam işleme yeteneği ile donatılmıştır ve daha güçlü bağlam anlama ve mantıksal çıkarım yetenekleri sunmaktadır. 32K token'lık metin girişi desteklemekte olup, uzun belge<PERSON>in okunması, özel bilgi sorgulama gibi senaryolar için uygundur."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct, <PERSON>wen Xin Qiong tarafından tamamen bağımsız olarak eğitilen büyük dil modelidir. Megrez-3B-Instruct, yazılım ve donanım işbirliği felsef<PERSON>yle, h<PERSON><PERSON><PERSON><PERSON>ı<PERSON>ım, küçük ve güçlü, kullanımı kolay bir uç tarafı zeka çözümü oluşturmayı amaçlamaktadır."}, "meta-llama-3-70b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kodlama ve geniş dil uygulamalarında mükemmel bir 70 milyar parametreli model."}, "meta-llama-3-8b-instruct": {"description": "Diyalog ve metin üretim görevleri için optimize edilmiş çok yönlü bir 8 milyar parametreli model."}, "meta-llama-3.1-405b-instruct": {"description": "Llama 3.1 talimat ayarlı yalnızca metin modelleri, çok dilli diyalog kullanım durumları için optimize edilmiştir ve mevcut açık kaynak ve kapalı sohbet modellerinin çoğunu yaygın endüstri standartlarında geride bırakmaktadır."}, "meta-llama-3.1-70b-instruct": {"description": "Llama 3.1 talimat ayarlı yalnızca metin modelleri, çok dilli diyalog kullanım durumları için optimize edilmiştir ve mevcut açık kaynak ve kapalı sohbet modellerinin çoğunu yaygın endüstri standartlarında geride bırakmaktadır."}, "meta-llama-3.1-8b-instruct": {"description": "Llama 3.1 talimat ayarlı yalnızca metin modelleri, çok dilli diyalog kullanım durumları için optimize edilmiştir ve mevcut açık kaynak ve kapalı sohbet modellerinin çoğunu yaygın endüstri standartlarında geride bırakmaktadır."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 <PERSON><PERSON> (13B), m<PERSON><PERSON><PERSON><PERSON> dil i<PERSON><PERSON><PERSON> ve olağanüstü etkileşim den<PERSON>ar."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2, m<PERSON><PERSON><PERSON><PERSON> dil i<PERSON><PERSON><PERSON> ve üstün etkileşim den<PERSON> sunar."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B), karmaşık diyalog ihtiyaçlarını destekleyen güçlü bir sohbet modelidir."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 <PERSON><PERSON> (8B), <PERSON><PERSON> dilli desteği ile zengin alan bil<PERSON> ka<PERSON>."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini bir arada işleme amacıyla tasarlanmıştır. Görüntü betimleme ve görsel soru yanıtlama gibi görevlerde mükemmel performans sergiler, dil üretimi ve görsel akıl yürütme arasındaki boşluğu kapar."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini bir arada işleme amacıyla tasarlanmıştır. Görüntü betimleme ve görsel soru yanıtlama gibi görevlerde mükemmel performans sergiler, dil üretimi ve görsel akıl yürütme arasındaki boşluğu kapar."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini bir arada işleme amacıyla tasarlanmıştır. Görüntü betimleme ve görsel soru yanıtlama gibi görevlerde mükemmel performans sergiler, dil üretimi ve görsel akıl yürütme arasındaki boşluğu kapar."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 çok dilli büyük dil modeli (LLM), 70B (metin girişi/metin çıkışı) içinde önceden eğitilmiş ve talimat ayarlı bir üretim modelidir. Llama 3.3 talimat ayarlı saf metin modeli, çok dilli diyalog kullanım durumları için optimize edilmiştir ve yaygın endüstri standartlarında birçok mevcut açık kaynak ve kapalı sohbet modelinden daha iyi performans göstermektedir."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini bir arada işleme amacıyla tasarlanmıştır. Görüntü betimleme ve görsel soru yanıtlama gibi görevlerde mükemmel performans sergiler, dil üretimi ve görsel akıl yürütme arasındaki boşluğu kapar."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B <PERSON><PERSON><PERSON><PERSON>, yüksek performans ve düşük gecikme gerektiren ortamlara uygundur."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo, en zorlu hesaplama görevleri için mükemmel dil anlama ve üretim yet<PERSON> sunar."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite, kaynak kısıtlı ortamlara uygun, mükemmel denge performansı sunar."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo, geniş uygulama alanlarını destekleyen yüksek performanslı bir büyük dil modelidir."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B, ön eğitim ve talimat ayarlaması için güçlü bir modeldir."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405B Llama 3.1 Turbo modeli, büyük veri işleme için devasa bağlam desteği sunar ve büyük ölçekli AI uygulamalarında öne çıkar."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1, <PERSON><PERSON> tara<PERSON>ında<PERSON> sunulan ö<PERSON>ü bir modeldir, 405B parametreye kadar destekler ve karmaşık diyaloglar, çok dilli çeviri ve veri analizi alanlarında uygulanabilir."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70B modeli, y<PERSON><PERSON><PERSON> yük uygulamaları için ince ayar yapılmış, FP8'e kuantize edilerek daha verimli hesaplama gücü ve doğruluk sağlar, karmaşık senaryolarda mükemmel performans sunar."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8B modeli, FP8 kuantizasyonu ile 131,072'ye kadar ba<PERSON><PERSON> beli<PERSON> des<PERSON>, karmaşık görevler için mükemmel bir açık kaynak modelidir ve birçok endüstri standardını aşar."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct, yüksek kaliteli diyalog senaryoları için optimize edilmiştir ve çeşitli insan değerlendirmelerinde mükemmel performans göstermektedir."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct, yüksek kaliteli diyalog senaryoları için optimize edilmiştir ve birçok kapalı kaynak modelden daha iyi performans göstermektedir."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B <PERSON>stru<PERSON>, yüksek kaliteli diyalog için ta<PERSON>ı<PERSON> olu<PERSON>, insan değerlendirmelerinde öne çıkmakta ve özellikle yüksek etkileşimli senaryolar için uygundur."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B <PERSON>st<PERSON><PERSON>, <PERSON><PERSON>n sunulan en son ve<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> kaliteli diyalog senaryoları için optimize edilmiştir ve birçok önde gelen kapalı kaynak modelden daha iyi performans göstermektedir."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1, çok dilli destek sunar ve sektördeki en önde gelen üretim modellerinden biridir."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini birleştiren görevleri işlemek için tasarlanmıştır. Görüntü tanımlama ve görsel soru yanıtlama gibi görevlerde mükemmel performans sergileyerek dil üretimi ve görsel akıl yürütme arasındaki boşluğu kapatmaktadır."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2, g<PERSON><PERSON><PERSON> ve metin verilerini birleştiren görevleri işlemek için tasarlanmıştır. Görüntü tanımlama ve görsel soru yanıtlama gibi görevlerde mükemmel performans sergileyerek dil üretimi ve görsel akıl yürütme arasındaki boşluğu kapatmaktadır."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3, Llama serisinin en gelişmiş çok dilli açık kaynak büyük dil modelidir ve 405B modelinin performansını çok düşük maliyetle deneyimlemenizi sağlar. Transformer yapısına dayanmaktadır ve denetimli ince ayar (SFT) ve insan geri bildirimi ile güçlendirilmiş öğrenme (RLHF) ile faydalılığını ve güvenliğini artırmıştır. Talimat ayarlı versiyonu, çok dilli diyaloglar için optimize edilmiştir ve birçok endüstri kıyaslamasında birçok açık kaynak ve kapalı sohbet modelinden daha iyi performans göstermektedir. Bilgi kesim tarihi 2023 Aralık'tır."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3, Llama serisinin en gelişmiş çok dilli açık kaynak büyük dil modelidir ve 405B modelinin performansını çok düşük maliyetle deneyimlemenizi sağlar. Transformer yapısına dayanmaktadır ve denetimli ince ayar (SFT) ve insan geri bildirimi ile güçlendirilmiş öğrenme (RLHF) ile faydalılığını ve güvenliğini artırmıştır. Talimat ayarlı versiyonu, çok dilli diyaloglar için optimize edilmiştir ve birçok endüstri kıyaslamasında birçok açık kaynak ve kapalı sohbet modelinden daha iyi performans göstermektedir. Bilgi kesim tarihi 2023 Aralık'tır."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct, Llama 3.1 Instruct modelinin en büyük ve en güçlü versiyonudur. <PERSON><PERSON>, son derece gelişmiş bir diyalog akıl yürütme ve veri sentezleme modelidir ve belirli alanlarda uzmanlaşmış sürekli ön eğitim veya ince ayar için bir temel olarak da kullanılabilir. Llama 3.1, çok dilli büyük dil modelleri (LLM'ler) sunar ve 8B, 70B ve 405B boyutlarında önceden eğitilmiş, talimat ayarlı üretim modellerinden oluşur (metin girişi/çıkışı). Llama 3.1'in talimat ayarlı metin modelleri (8B, 70B, 405B), çok dilli diyalog kullanım durumları için optimize edilmiştir ve yaygın endüstri benchmark testlerinde birçok mevcut açık kaynaklı sohbet modelini geride bırakmıştır. Llama 3.1, çok dilli ticari ve araştırma amaçları için tasarlanmıştır. Talimat ayarlı metin modelleri, asistan benzeri sohbetler için uygundur, önceden eğitilmiş modeller ise çeşitli doğal dil üretim görevlerine uyum sağlayabilir. Llama 3.1 modeli, diğer modellerin çıktısını iyileştirmek için de kullanılabilir, bu da veri sentezleme ve rafine etme işlemlerini içerir. Llama 3.1, optimize edilmiş bir transformer mimarisi kullanarak oluşturulmuş bir otoregresif dil modelidir. Ayarlanmış versiyon, insan yardımseverliği ve güvenlik tercihleri ile uyumlu hale getirmek için denetimli ince ayar (SFT) ve insan geri bildirimi ile güçlendirilmiş öğrenme (RLHF) kullanır."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Meta Llama 3.1 70B Instruct'un güncellenmiş versiyonu, genişletilmiş 128K bağlam uzu<PERSON>ğ<PERSON>, çok dilli yetenek ve geliştirilmiş akıl yürütme yetenekleri içerir. Llama 3.1 tarafından sağlanan çok dilli büyük dil modelleri (LLM'ler), 8B, 70B ve 405B boyutlarında önceden eğitilmiş, talimat ayarlı üretim modelleridir (metin girişi/çıkışı). Llama 3.1 talimat ayarlı metin modelleri (8B, 70B, 405B), çok dilli diyalog kullanım durumları için optimize edilmiştir ve birçok mevcut açık kaynaklı sohbet modelini yaygın endüstri benchmark testlerinde geçmiştir. Llama 3.1, çok dilli ticari ve araştırma amaçları için kullanılmak üzere tasarlanmıştır. Talimat ayarlı metin modelleri, asistan benzeri sohbetler iç<PERSON> u<PERSON>, önceden eğitilmiş modeller ise çeşitli doğal dil üretim görevlerine uyum sağlayabilir. Llama 3.1 modeli, diğer modellerin çıktısını iyileştirmek için de kullanılabilir, bu da sentetik veri üretimi ve rafine etme işlemlerini içerir. Llama 3.1, optimize edilmiş bir dönüştürücü mimarisi kullanarak oluşturulmuş bir otoregresif dil modelidir. Ayarlanmış versiyonlar, insan yardımseverliği ve güvenlik tercihlerini karşılamak için denetimli ince ayar (SFT) ve insan geri bildirimli pekiştirmeli öğrenme (RLHF) kullanır."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Meta Llama 3.1 8B Instruct'un güncellenmiş versiyonu, genişletilmiş 128K bağlam uzun<PERSON>ğ<PERSON>, çok dilli yetenek ve geliştirilmiş akıl yürütme yetenekleri içerir. Llama 3.1 tarafından sağlanan çok dilli büyük dil modelleri (LLM'ler), 8B, 70B ve 405B boyutlarında önceden eğitilmiş, talimat ayarlı üretim modelleridir (metin girişi/çıkışı). Llama 3.1 talimat ayarlı metin modelleri (8B, 70B, 405B), çok dilli diyalog kullanım durumları için optimize edilmiştir ve birçok mevcut açık kaynaklı sohbet modelini yaygın endüstri benchmark testlerinde geçmiştir. Llama 3.1, çok dilli ticari ve araştırma amaçları için kullanılmak üzere tasarlanmıştır. Talimat ayarlı metin modelleri, asistan benzeri sohbetler iç<PERSON> u<PERSON>, önceden eğitilmiş modeller ise çeşitli doğal dil üretim görevlerine uyum sağlayabilir. Llama 3.1 modeli, diğer modellerin çıktısını iyileştirmek için de kullanılabilir, bu da sentetik veri üretimi ve rafine etme işlemlerini içerir. Llama 3.1, optimize edilmiş bir dönüştürücü mimarisi kullanarak oluşturulmuş bir otoregresif dil modelidir. Ayarlanmış versiyonlar, insan yardımseverliği ve güvenlik tercihlerini karşılamak için denetimli ince ayar (SFT) ve insan geri bildirimli pekiştirmeli öğrenme (RLHF) kullanır."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, araştırmacılar ve işletmeler için açık bir büyük dil modelidir (LLM) ve onların üretken AI fikirlerini inşa etmelerine, denemelerine ve sorumlu bir şekilde genişletmelerine yardımcı olmak için tasarlanmıştır. Küresel topluluk yeniliğinin temel sistemlerinden biri olarak, içerik oluşturma, diyalog AI, dil anlama, araştırma ve işletme uygulamaları için son derece uygundur."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, araştırmacılar ve işletmeler için açık bir büyük dil modelidir (LLM) ve onların üretken AI fikirlerini inşa etmelerine, denemelerine ve sorumlu bir şekilde genişletmelerine yardımcı olmak için tasarlanmıştır. Küresel topluluk yeniliğinin temel sistemlerinden biri olarak, sınırlı hesaplama gücü ve kaynaklara sahip, kenar cihazları ve daha hızlı eğitim süreleri için son derece uygundur."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Yüksek çözünürlüklü görüntülerde üstün görsel çıkarım yet<PERSON> sunar, görsel anlama uygulamaları için idealdir."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Görsel anlama ajan uygulamaları için gelişmiş görüntü çıkarım yetenekleri sağlar."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3, Llama serisinin en gelişmiş çok dilli açık kaynak büyük dil modeli olup, 405 milyar parametreli modellere kıyasla çok düşük maliyetle yüksek performans sunar. Transformer mimarisi temel alınmış, deneti<PERSON><PERSON> ince ayar (SFT) ve insan geri bildirimi ile güçlendirilmiş pekiştirmeli öğrenme (RLHF) ile faydalılık ve güvenlik artırılmıştır. Çok dilli diyaloglar için optimize edilmiş talimat ayarlı versiyonu, birçok endüstri kıyaslamasında açık ve kapalı sohbet modellerinden üstün performans gösterir. Bilgi kesim tarihi 2023 Aralık'tır."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kodlama ve geniş dil uygulamalarında üstün performans gösteren güçlü 70 milyar parametreli model."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Diyalog ve metin üretimi görevleri için optimize edilmiş çok yönlü 8 milyar parametreli model."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 talimat ayarlı metin modeli, çok dilli diyalog senaryoları için optimize edilmiştir ve birçok açık ve kapalı sohbet modeli arasında yaygın endüstri kıyaslamalarında üstün performans sergiler."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 talimat ayarlı metin modeli, çok dilli diyalog senaryoları için optimize edilmiştir ve birçok açık ve kapalı sohbet modeli arasında yaygın endüstri kıyaslamalarında üstün performans sergiler."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 talimat ayarlı metin modeli, çok dilli diyalog senaryoları için optimize edilmiştir ve birçok açık ve kapalı sohbet modeli arasında yaygın endüstri kıyaslamalarında üstün performans sergiler."}, "meta/llama-3.1-405b-instruct": {"description": "G<PERSON>şmiş LLM, sentetik veri üretimi, bilgi damıt<PERSON> ve akıl yür<PERSON><PERSON><PERSON>yi destekler, so<PERSON><PERSON> botları, <PERSON><PERSON>a ve belirli alan görevleri için uygundur."}, "meta/llama-3.1-70b-instruct": {"description": "Karmaşık diyalogları güçlendiren, mü<PERSON><PERSON><PERSON> ba<PERSON><PERSON> an<PERSON>, akıl yü<PERSON><PERSON><PERSON><PERSON> yeteneği ve metin üretimi yeteneğine sahip."}, "meta/llama-3.1-8b-instruct": {"description": "En son tek<PERSON><PERSON><PERSON><PERSON> sahip model, dil <PERSON><PERSON><PERSON>, mü<PERSON><PERSON><PERSON> akıl yü<PERSON><PERSON><PERSON><PERSON> yetene<PERSON>i ve metin üretimi yetene<PERSON> sa<PERSON>r."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Gelişmiş görsel-dil modeli, g<PERSON>r<PERSON><PERSON><PERSON>lerden yüksek kaliteli akıl yürütme yapma konusunda uzmandır."}, "meta/llama-3.2-1b-instruct": {"description": "En son teknolojiye sahip küçük dil modeli, dil an<PERSON>, müke<PERSON>l akıl yür<PERSON><PERSON><PERSON> yeteneği ve metin üretimi yetene<PERSON> sa<PERSON>."}, "meta/llama-3.2-3b-instruct": {"description": "En son teknolojiye sahip küçük dil modeli, dil an<PERSON>, müke<PERSON>l akıl yür<PERSON><PERSON><PERSON> yeteneği ve metin üretimi yetene<PERSON> sa<PERSON>."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Gelişmiş görsel-dil modeli, g<PERSON>r<PERSON><PERSON><PERSON>lerden yüksek kaliteli akıl yürütme yapma konusunda uzmandır."}, "meta/llama-3.3-70b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> LLM, ak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mate<PERSON><PERSON>, genel bilgi ve fonksiyon çağrılarında uzmandır."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "Aynı Phi-3-medium modeli, ancak daha büyük bağlam boyutuna sahip olup RAG veya az sayıda istem için uygundur."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "140 milyar parametreli model, Phi-3-mini'den daha yüksek kaliteye sahip olup, yüks<PERSON> kaliteli ve çıkarım yoğun veriye odaklanır."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Aynı Phi-3-mini modeli, ancak daha büyük bağlam boyutuna sahip olup RAG veya az sayıda istem için uygundur."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Phi-3 ailesinin en küçük üyesi olup, kalite ve düşük gecikme için optimize edilmiştir."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Aynı Phi-3-small modeli, ancak daha büyük bağlam boyutuna sahip olup RAG veya az sayıda istem için uygundur."}, "microsoft/Phi-3-small-8k-instruct": {"description": "70 milyar parametreli model, Phi-3-mini'den daha yüksek kaliteye sahip olup, yüks<PERSON> kaliteli ve çıkarım yoğun veriye odaklanır."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Phi-3-mini model<PERSON><PERSON> güncellenmiş versiyonu."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Phi-3-vision model<PERSON><PERSON> g<PERSON> versiyonu."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2, Microsoft AI tarafından sağlanan bir dil modelidir ve karmaşık diyaloglar, çok dilli destek, akıl yürütme ve akıllı asistan alanlarında özellikle başarılıdır."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B, Microsoft'un en gelişmiş AI Wizard modelidir ve son derece rekabetçi bir performans sergiler."}, "minicpm-v": {"description": "MiniCPM-V, OpenBMB tarafından sunulan yeni nesil çok modlu büyük bir modeldir; olağanüstü OCR tanıma ve çok modlu anlama yeteneklerine sahiptir ve geniş bir uygulama yelpazesini destekler."}, "ministral-3b-latest": {"description": "Ministral 3B, Mistral'ın dünya çapında en üst düzey kenar modelidir."}, "ministral-8b-latest": {"description": "Ministral 8B, Mistral'ın fiyat-performans oranı oldukça yüksek kenar modelidir."}, "mistral": {"description": "<PERSON><PERSON><PERSON>, Mistral AI tarafından sunulan 7B modelidir, değişken dil işleme ihtiyaçları için uygundur."}, "mistral-ai/Mistral-Large-2411": {"description": "Mistral'in amiral gemisi modeli olup, büyük ölçekli çıkarım yetenekleri veya yüksek derecede uzmanlaşmış karmaşık görevler (metin sentezi, kod <PERSON><PERSON><PERSON><PERSON>, RAG veya ajanlar) için uygundur."}, "mistral-ai/Mistral-Nemo": {"description": "<PERSON><PERSON><PERSON>, boyut kategor<PERSON> en gelişmiş çıkarım, dünya bilgisi ve kodlama yeteneklerine sahip ileri düzey bir dil modelidir (LLM)."}, "mistral-ai/mistral-small-2503": {"description": "<PERSON><PERSON><PERSON> Small, yüksek verimlilik ve düşük gecikme gerektiren dil tabanlı görevler için uygundur."}, "mistral-large": {"description": "<PERSON>tral Large, <PERSON><PERSON>l'ın amiral gemisi <PERSON>id<PERSON>, k<PERSON> <PERSON><PERSON><PERSON><PERSON>, matematik ve akıl yü<PERSON><PERSON><PERSON><PERSON> yet<PERSON>rini birleştirir, 128k bağlam penceresini destekler."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407, 123 milyar paramet<PERSON>e <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> bir yo<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> dil modelidir (LLM) ve en son akıl <PERSON><PERSON>, bilgi ve kodlama yeteneklerine sa<PERSON>r."}, "mistral-large-latest": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> dilli g<PERSON>, karmaş<PERSON>k akıl yürütme ve kod üretimi için ideal bir seçimdir ve yüksek uç uygulamalar için tasarlanmıştır."}, "mistral-medium-latest": {"description": "Mistral Medium 3, 8 kat daha düşük maliyetle en ileri düzey performansı sunar ve kurumsal dağıtımları temelden basitleştirir."}, "mistral-nemo": {"description": "<PERSON><PERSON><PERSON>, Mistral AI ve NVIDIA işbirliği il<PERSON>, y<PERSON>ksek verimli 12B modelidir."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 büyük dil modeli (LLM), Mistral-Nemo-Base-2407'nin komut ince ayarlı versiyonudur."}, "mistral-small": {"description": "<PERSON><PERSON><PERSON>, yüksek verimlilik ve düşük gecikme gerektiren her dil tabanlı görevde kullanılabilir."}, "mistral-small-latest": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, özetleme ve duygu analizi gibi kullanım durumları için maliyet etkin, hızlı ve güvenilir bir seçenektir."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct, yüksek performansıyla tanınır ve çeşitli dil görevleri için uygundur."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B, talebe göre ince ayar yapılmış bir modeldir ve görevler için optimize edilmiş yanıtlar sunar."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3, g<PERSON>ş uygulamalar için etkili hesaplama gücü ve doğal dil anlama sunar."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B, kompakt ancak yüksek performanslı bir modeldir, sınıflandırma ve metin üretimi gibi basit görevlerde iyi bir akıl yürütme yeteneği ile yoğun işlem yapma konusunda uzmandır."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B), son der<PERSON>e b<PERSON><PERSON><PERSON><PERSON> bir dil modelidir ve çok yüksek iş<PERSON>e tale<PERSON>ini destekler."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B, genel metin görevleri için kull<PERSON>ılan önceden eğitilmiş seyrek karışık uzman modelidir."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B, birden fazla parametre kullanarak akıl yürütme hızını artıran seyrek uzman modelidir, çok dilli ve kod üretim görevleri için uygundur."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct, hız optimizasyonu ve uzun bağlam desteği sunan yüksek performanslı bir endüstri standart modelidir."}, "mistralai/mistral-nemo": {"description": "<PERSON><PERSON><PERSON>, çok dilli destek ve yüksek performanslı programlama sunan 7.3B parametreli bir modeldir."}, "mixtral": {"description": "<PERSON><PERSON>, Mistral AI'nın u<PERSON>id<PERSON>, açık kaynak ağırlıkları ile birlikte gelir ve kod üretimi ve dil anlama konularında destek sunar."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B, y<PERSON><PERSON><PERSON> hata toleransına sahip paralel hesaplama yeteneği sunar ve karmaşık görevler için uygundur."}, "mixtral:8x22b": {"description": "<PERSON><PERSON>, Mistral AI'nın u<PERSON>id<PERSON>, açık kaynak ağırlıkları ile birlikte gelir ve kod üretimi ve dil anlama konularında destek sunar."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K, ultra uzun ba<PERSON><PERSON> işleme yeteneğine sahip bir modeldir, karmaşık üretim görevlerini karşılamak için ultra uzun metinler üretmekte kullanılabilir, 128,000 token'a kadar içeriği işleyebilir, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, akademik ve büyük belgelerin üretilmesi gibi uygulama senaryoları için son derece uygundur."}, "moonshot-v1-128k-vision-preview": {"description": "<PERSON><PERSON> modeli (moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview gibi) resim içeri<PERSON>ini an<PERSON>lir, resim met<PERSON>, resim rengi ve nesne şekilleri gibi içerikleri kapsar."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K, orta uzu<PERSON><PERSON><PERSON> bağ<PERSON> iş<PERSON><PERSON>, 32,768 token'ı işleyebilir, çeşitli uzun belgeler ve karmaşık diyaloglar üretmek için özellikle uygundur, <PERSON><PERSON><PERSON><PERSON>ma, rapor üretimi ve diyalog sistemleri gibi alanlarda kullanılabilir."}, "moonshot-v1-32k-vision-preview": {"description": "<PERSON><PERSON> modeli (moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview gibi) resim içeri<PERSON>ini an<PERSON>lir, resim met<PERSON>, resim rengi ve nesne şekilleri gibi içerikleri kapsar."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K, kısa metin görevleri iç<PERSON>, <PERSON><PERSON><PERSON><PERSON> verimlilikte işleme performansı sunar, 8,192 token'<PERSON> <PERSON><PERSON><PERSON>eb<PERSON>, k<PERSON><PERSON>, not alma ve hızlı içerik üretimi için son derece uygundur."}, "moonshot-v1-8k-vision-preview": {"description": "<PERSON><PERSON> modeli (moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview gibi) resim içeri<PERSON>ini an<PERSON>lir, resim met<PERSON>, resim rengi ve nesne şekilleri gibi içerikleri kapsar."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto, mevcut bağlamın kullandığı Token sayısına göre uygun modeli seçebilir."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B, büyük ölçekli pekiştirmeli öğrenme ile optimize edilmiş açık kaynaklı bir kod modeli olup, sağlam ve doğrudan üretime uygun yamalar üretebilir. Bu model, SWE-bench Verified üzerinde %60,4 ile yeni bir rekor kırarak, açık kaynak modeller arasında hata düzeltme, kod incelemesi gibi otomatik yazılım mühendisliği görevlerinde en yüksek puanı elde etmiştir."}, "moonshotai/Kimi-K2-Instruct": {"description": "<PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON> kodlama ve ajan yeteneklerine sahip MoE mimarili temel modeldir; toplam 1 trilyon parametre, 32 milyar aktif parametreye sahiptir. <PERSON><PERSON> bi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, matematik ve ajan gibi ana kategorilerdeki kıyaslama testlerinde K2 modeli diğer önde gelen açık kaynak modelleri geride bırakır."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2, <PERSON><PERSON><PERSON><PERSON><PERSON> kodlama ve A<PERSON> yet<PERSON>rine sahip MoE mimarili temel bir modeldir, toplam parametre sayısı 1T, aktif parametre sayısı 32B'dir. <PERSON>l bi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, matematik ve Ajan gibi ana kategorilerde yapılan kıyaslama testlerinde, K2 modeli diğer önde gelen açık kaynak modellerini geride bırakmaktadır."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B, Nous Hermes 2'nin g<PERSON><PERSON><PERSON><PERSON>ş versiyonudur ve en son iç geliştirme veri setlerini içermektedir."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B, NVIDIA tarafından özelleştirilmiş büyük bir dil modelidir ve LLM tarafından üretilen yanıtların kullanıcı sorgularına yardımcı olma düzeyini artırmayı amaçlamaktadır. Bu model, Arena Hard, AlpacaEval 2 LC ve GPT-4-Turbo MT-Bench gibi standart testlerde mükemmel performans sergilemiştir ve 1 Ekim 2024 itibarıyla tüm üç otomatik hizalama testinde birinci sıradadır. Model, Llama-3.1-70B-Instruct modelinin temelinde RLHF (özellikle REINFORCE), Llama-3.1-Nemotron-70B-Reward ve HelpSteer2-Preference ipuçları kullanılarak eğitilmiştir."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON> bir dil <PERSON>, <PERSON><PERSON><PERSON> doğruluk ve verimlilik sunar."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct, NVIDIA'nın özel olarak geliştirdiği büyük dil modelidir ve LLM tarafından üretilen yanıtların yardımcı olmasını artırmayı amaçlar."}, "o1": {"description": "Gelişmiş çıkarım ve karmaşık sorunları çözmeye odaklanır, matematik ve bilim görevlerini içerir. Derin bağlam anlayışı ve aracılık iş akışları gerektiren uygulamalar için son derece uygundur."}, "o1-mini": {"description": "o1-mini, program<PERSON><PERSON>, matematik ve bilim uygulama senaryoları için tasarlanmış hızlı ve ekonomik bir akıl yürütme modelidir. Bu model, 128K bağlam ve Ekim 2023 bilgi kesim tarihi ile donatılmıştır."}, "o1-preview": {"description": "o1, OpenAI'nin geniş genel bilgiye ihtiyaç duyan karmaşık görevler için uygun yeni bir akıl yürütme modelidir. Bu model, 128K bağlam ve Ekim 2023 bilgi kesim tarihi ile donatılmıştır."}, "o1-pro": {"description": "o1 serisi modeller, yanıtlamadan önce düşünme yapabilen ve karmaşık akıl yürütme görevlerini yerine getirebilen pekiştirmeli öğrenme ile eğitilmiştir. o1-pro modeli, daha derin düşünme için daha fazla hesaplama kaynağı kullanır ve böylece sürekli olarak daha kaliteli yanıtlar sunar."}, "o3": {"description": "o3, çok çeşitli alanlarda mükemmel performans gösteren çok yönlü güçlü bir modeldir. Mat<PERSON>tik, bi<PERSON>, programlama ve görsel çıkarım görevlerinde yeni standartlar belirler. Ayrıca teknik yazım ve talimat takibi konusunda da uzmandır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin, kod ve görüntüleri analiz ederek çok adımlı karmaşık sorunları çözebilir."}, "o3-deep-research": {"description": "o3-derin<PERSON><PERSON><PERSON>, karmaşık çok adımlı araştırma görevlerini işlemek için özel olarak tasarlanmış en gelişmiş derin araştırma modelimizdir. İnternetten bilgi arayıp sentezleyebilir ve MCP bağlayıcısı aracılığıyla kendi verilerinize erişip bunları kullanabilir."}, "o3-mini": {"description": "o3-mini, aynı maliyet ve gecikme hedefleriyle yüksek zeka sunan en yeni küçük ölçekli çıkarım modelimizdir."}, "o3-pro": {"description": "o3-pro modeli, daha derin <PERSON> ve her zaman daha iyi yanıtlar sunmak için daha fazla hesaplama kullanır, yalnızca Responses API altında kullanılabilir."}, "o4-mini": {"description": "o4-mini, en yeni küçük o serisi modelimizdir. Hızlı ve etkili çıkarım için optimize edilmiştir ve kodlama ile görsel görevlerde son derece yüksek verimlilik ve performans sergiler."}, "o4-mini-deep-research": {"description": "o4-mini-derin-a<PERSON>, daha hızlı ve uygun maliyetli derin araştırma modelimizdir — karmaşık çok adımlı araştırma görevleri için idealdir. İnternetten bilgi arayıp sentezleyebilir ve MCP bağlayıcısı aracılığıyla kendi verilerinize erişip bunları kullanabilir."}, "open-codestral-mamba": {"description": "Codestral Mamba, kod <PERSON>retimine odaklanan Mamba 2 dil modelidir ve ileri düzey kod ve akıl yürütme görevlerine güçlü destek sunar."}, "open-mistral-7b": {"description": "Mistral 7B, kompakt ama yüksek performanslı bir modeldir, sınıflandırma ve metin üretimi gibi basit görevlerde iyi bir akıl yürütme yeteneğine sa<PERSON>tir."}, "open-mistral-nemo": {"description": "<PERSON><PERSON><PERSON> Nemo, Nvidia ile işbirliği içinde geliştirilmiş 12B modelidir, mükemmel akıl yürütme ve kodlama performansı sunar, entegrasyonu ve değiştirilmesi kolaydır."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B, karmaşık görevler için o<PERSON>ış daha büyük bir uzman modelidir, mükemmel akıl yürütme yeteneği ve daha yüksek bir verim sunar."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B, birden fazla parametre kullanarak akıl yürütme hızını artıran seyrek uzman modelidir, çok dilli ve kod üretim görevlerini işlemek için uygundur."}, "openai/gpt-4.1": {"description": "GPT-4.1, karmaşık görevler i<PERSON><PERSON> k<PERSON>ımız amiral gemisi modelidir. Farklı alanlarda sorun çözmek için son derece uygundur."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini, z<PERSON>, hız ve maliyet arasında bir denge sunarak birçok kullanım durumu için çekici bir model haline getirir."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano, en hızlı ve en maliyet etkin GPT-4.1 modelidir."}, "openai/gpt-4o": {"description": "ChatGPT-4o, g<PERSON>nce<PERSON> en son sür<PERSON><PERSON><PERSON> korumak için gerçek zamanlı olarak güncellenen dinamik bir modeldir. Güçlü dil anlama ve üretme yeteneklerini birleştirir, mü<PERSON><PERSON>i <PERSON>leri, eğitim ve teknik destek gibi büyük ölçekli uygulama senaryoları için uygundur."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini, OpenAI'nin GPT-4 Omni'den sonra sunduğu en son modeldir; g<PERSON><PERSON><PERSON> ve metin girişi destekler ve metin çıktısı verir. En gelişmiş küçük model olar<PERSON>, diğer son zamanlardaki öncü modellere göre çok daha ucuzdur ve GPT-3.5 Turbo'dan %60'tan fazla daha ucuzdur. En son teknolojiyi korurken, önemli bir maliyet etkinliği sunar. GPT-4o mini, MMLU testinde %82 puan almış olup, şu anda sohbet tercihleri açısından GPT-4'ün üzerinde bir sıralamaya sahiptir."}, "openai/o1": {"description": "o1, OpenAI'nin yeni ç<PERSON> modeli olu<PERSON>, metin ve görsel girişleri destekler ve metin çıktısı üretir; geniş kapsamlı genel bilgi gerektiren karmaşık görevler için uygundur. Model, 200K bağlam uzunluğuna ve 2023 Ekim bilgi kesim tarihine sahiptir."}, "openai/o1-mini": {"description": "o1-mini, program<PERSON><PERSON>, matematik ve bilim uygulama senaryoları için tasarlanmış hızlı ve ekonomik bir akıl yürütme modelidir. Bu model, 128K bağlam ve Ekim 2023 bilgi kesim tarihi ile donatılmıştır."}, "openai/o1-preview": {"description": "o1, OpenAI'nin geniş genel bilgiye ihtiyaç duyan karmaşık görevler için uygun yeni bir akıl yürütme modelidir. Bu model, 128K bağlam ve Ekim 2023 bilgi kesim tarihi ile donatılmıştır."}, "openai/o3": {"description": "o3, birçok alanda mükemmel performans sergileyen güçlü bir modeldir. <PERSON><PERSON><PERSON><PERSON>, bilim, programlama ve görsel akıl yürütme görevleri için yeni bir standart belirler. Ayrıca teknik yazım ve talimat takibi konusunda da uzmandır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin, kod ve görüntüleri analiz ederek çok adımlı karmaşık problemleri çözebilir."}, "openai/o3-mini": {"description": "o3-mini, o1-mini ile aynı maliyet ve gecikme hedefleri altında yüksek zeka sunar."}, "openai/o3-mini-high": {"description": "o3-mini yüksek akıl yürütme seviyesi, o1-mini ile aynı maliyet ve gecikme hedefleri altında yüksek zeka sunar."}, "openai/o4-mini": {"description": "o4-mini, hızlı ve etkili çıkarım için optimize edilmiştir ve kodlama ile görsel görevlerde son derece yüksek verimlilik ve performans sergiler."}, "openai/o4-mini-high": {"description": "o4-mini yüksek çıkarım seviyesinde, hızlı ve etkili çıkarım için optimize edilmiştir ve kodlama ile görsel görevlerde son derece yüksek verimlilik ve performans sergiler."}, "openrouter/auto": {"description": "<PERSON><PERSON><PERSON>, konu ve karmaşıklığa gö<PERSON> is<PERSON>ğ<PERSON>, Llama 3 70B <PERSON>st<PERSON><PERSON>, Claude 3.5 Sonnet (kendini a<PERSON>) veya GPT-4o'ya gönderilecektir."}, "phi3": {"description": "Phi-3, Microsoft tarafından sunulan hafif bir açık modeldir, verimli entegrasyon ve büyük ölçekli bilgi akıl yürütme için uygundur."}, "phi3:14b": {"description": "Phi-3, Microsoft tarafından sunulan hafif bir açık modeldir, verimli entegrasyon ve büyük ölçekli bilgi akıl yürütme için uygundur."}, "pixtral-12b-2409": {"description": "Pixtral modeli, grafik ve g<PERSON><PERSON><PERSON><PERSON><PERSON>, belge yan<PERSON>, çok modlu akıl yürütme ve talimat takibi gibi görevlerde güçlü yetenekler sergiler, do<PERSON><PERSON>özünürlük ve en boy oranında görüntüleri alabilir ve 128K token uzunluğunda bir bağlam penceresinde herhangi bir sayıda görüntüyü işleyebilir."}, "pixtral-large-latest": {"description": "Pixtral Large, 1240 milyar parametreye sahip açık kaynaklı çok modlu bir modeldir ve Mistral Large 2 üzerine inşa edilmiştir. Bu, çok modlu ailemizdeki ikinci modeldir ve öncü düzeyde görüntü anlama yetenekleri sergilemektedir."}, "pro-128k": {"description": "Spark Pro 128K, olağanüstü bağlam işleme yeteneği ile donatılmıştır ve 128K'ya kadar bağlam bilgilerini işleyebilir. Özellikle uzun metinlerin bütünsel analizi ve uzun vadeli mantıksal ilişkilerin işlenmesi gereken durumlar için uygundur ve karmaşık metin iletişiminde akıcı ve tutarlı bir mantık ile çeşitli alıntı desteği sunmaktadır."}, "qvq-72b-preview": {"description": "QVQ modeli, <PERSON><PERSON> ekibi tarafından geliştirilen deneysel bir araştırma modelidir; görsel akıl yürütme yeteneğini artırmaya odaklanır, özellikle matematik akıl yürütme alanında."}, "qvq-max": {"description": "Tongyi Qianwen QVQ görsel akıl yürütme modeli, görsel giriş ve düşünce zinciri çıktısını destekler; mate<PERSON><PERSON>, <PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> analiz, yaratım ve genel görevlerde daha güçlü performans sergiler."}, "qvq-plus": {"description": "Görsel çıkarım modeli. Görsel girişleri ve düşünce zinciri çıktısını destekler; qvq-max modelinin ardından gelen plus versiyonudur. qvq-max modeline kı<PERSON>, qvq-plus serisi modeller daha hızlı çıkarım yapar ve performans ile maliyet arasında daha dengeli bir sonuç sunar."}, "qwen-coder-plus": {"description": "<PERSON><PERSON> kodlama modeli."}, "qwen-coder-turbo": {"description": "<PERSON><PERSON> kodlama modeli."}, "qwen-coder-turbo-latest": {"description": "<PERSON><PERSON> kodlama modeli."}, "qwen-long": {"description": "<PERSON><PERSON>, uzun metin bağlamını destekleyen ve uzun belgeler, çoklu belgeler gibi çeşitli senaryolar için diyalog işlevselliği sunan büyük ölçekli bir dil modelidir."}, "qwen-math-plus": {"description": "Tongyi Qianwen matematik modeli, matematik problemlerini çözmek için özel olarak ta<PERSON>lanmış dil modelidir."}, "qwen-math-plus-latest": {"description": "Tongyi Qianwen matematik modeli, matematik problemlerini çözmek için özel olarak ta<PERSON>lanmış bir dil modelidir."}, "qwen-math-turbo": {"description": "Tongyi Qianwen matematik modeli, matematik problemlerini çözmek için özel olarak ta<PERSON>lanmış dil modelidir."}, "qwen-math-turbo-latest": {"description": "Tongyi Qianwen matematik modeli, matematik problemlerini çözmek için özel olarak ta<PERSON>lanmış bir dil modelidir."}, "qwen-max": {"description": "<PERSON>yi Qianwen, 100 milyar seviyesinde büyük ölçekli bir dil modelidir ve Çince, İngilizce gibi farklı dil girişlerini destekler; <PERSON><PERSON> anda Tongyi Qianwen 2.5 ürün sürümünün arkasındaki API modelidir."}, "qwen-omni-turbo": {"description": "Qwen-Omni serisi modeller, video, ses, resim ve metin dahil çoklu modalite girişlerini destekler ve ses ile metin çıktısı sağlar."}, "qwen-plus": {"description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, İng<PERSON>zce gibi farklı dil girişlerini destekleyen geliştirilmiş büyük ölçekli bir dil modelidir."}, "qwen-turbo": {"description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ng<PERSON><PERSON>ce gibi farklı dil girişlerini destekleyen büyük ölçekli bir dil modelidir."}, "qwen-vl-chat-v1": {"description": "<PERSON>yi Qianwen VL, <PERSON><PERSON><PERSON>, çok turlu soru-cevap, ya<PERSON><PERSON>m gibi esnek etkileşim yöntemlerini destekleyen bir modeldir."}, "qwen-vl-max": {"description": "Tongyi Qianwen ultra büyük ölçekli görsel-dil modeli. Geliştirilmiş versiyona kıyasla görsel akıl yürütme ve komut uyum yeteneklerini daha da artırır, daha yüksek görsel algı ve bilişsel seviyeler sunar."}, "qwen-vl-max-latest": {"description": "Tongyi Qianwen ultra büyük ölçekli görsel dil modeli. Geliştirilmiş versiyona kıyasla, görsel akıl yürütme yeteneğini ve talimatlara uyum yeteneğini bir kez daha artırır, daha yüksek görsel algı ve bilişsel seviyeler sunar."}, "qwen-vl-ocr": {"description": "Tongyi Qianwen OCR, be<PERSON>, tab<PERSON>, sınav soruları ve el yazısı gibi görüntülerden metin çıkarma konusunda uzmanlaşmış özel modeldir. Çoklu dil tanıma yeteneğine sa<PERSON>tir; desteklenen diller arasında <PERSON>, İngilizce, Fransızca, Japonca, Korece, Almanca, Rusça, İtalyanca, Vietnamca ve Arapça bulunmaktadır."}, "qwen-vl-plus": {"description": "Tongyi Qianwen büyük ölçekli görsel-dil modeli geliştirilmiş versiyonu. Detay tanıma ve metin tanıma yeteneklerini büyük ölçüde artırır, mi<PERSON><PERSON><PERSON><PERSON> piksel <PERSON>özünürlük ve herhangi bir en-boy oranındaki görüntüleri destekler."}, "qwen-vl-plus-latest": {"description": "Tongyi Qianwen büyük ölçekli görsel dil modelinin geliştirilmiş versiyonu. Detay tanıma ve metin tanıma yeteneklerini büyük ölçüde artırır, bir milyondan fazla piksel çözünürlüğü ve herhangi bir en-boy oranındaki görüntüleri destekler."}, "qwen-vl-v1": {"description": "Qwen-7B dil modeli ile başlatılan, 448 çözünürlükte görüntü girişi olan önceden eğitilmiş bir modeldir."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2, tamamen yeni bir <PERSON>wen büyük dil modeli serisidir. Qwen2 7B, dil an<PERSON><PERSON>, <PERSON><PERSON> di<PERSON>, <PERSON><PERSON><PERSON>, matematik ve akıl yürütme konularında mükemmel performans sergileyen bir transformer tabanlı modeldir."}, "qwen/qwen-2-7b-instruct:free": {"description": "<PERSON>wen2, <PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> anlama ve üretme yeteneklerine sahip yeni bir büyük dil modeli serisidir."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-<PERSON><PERSON>, Qwen-VL modelinin en son y<PERSON><PERSON>e versiyonudur ve MathVista, DocVQA, RealWorldQA ve MTVQA gibi görsel anlama benchmark testlerinde en gelişmiş performansa ulaşmıştır. Qwen2-V<PERSON>, <PERSON><PERSON><PERSON><PERSON> kaliteli video tabanlı soru-cevap, diyalog ve içerik oluşturma için 20 dakikadan fazla videoyu anlayabilmektedir. Ayrıca, karmaşık akıl yürütme ve karar verme yeteneklerine sahiptir ve mobil cihazlar, robotlar gibi sistemlerle entegre olarak görsel ortam ve metin talimatlarına dayalı otomatik işlemler gerçekleştirebilmektedir. İngilizce ve Çince'nin yanı sıra, Qwen2-VL artık çoğu Avrupa dili, Japonca, Korece, Arapça ve Vietnamca gibi farklı dillerdeki metinleri de anlayabilmektedir."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-<PERSON><PERSON><PERSON><PERSON>, Alibaba Cloud tarafından yayınlanan en son b<PERSON><PERSON><PERSON><PERSON> dil modeli serilerinden biridir. Bu 72B modeli, kodlama ve matematik gibi alanlarda önemli iyileştirmelere sahiptir. <PERSON> ayr<PERSON><PERSON>, <PERSON><PERSON><PERSON>, İngilizce gibi 29'dan fazla dili kapsayan çok dilli destek sunmaktadır. Model, talimat takibi, yapılandırılmış verileri anlama ve yapılandırılmış çıktı (özellikle JSON) üretme konularında önemli gelişmeler göstermektedir."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-<PERSON><PERSON><PERSON><PERSON>, Alibaba Cloud tarafından yayınlanan en son b<PERSON><PERSON><PERSON><PERSON> dil modeli serilerinden biridir. Bu 32B modeli, kodlama ve matematik gibi alanlarda önemli iyileştirmelere sahiptir. Model, <PERSON><PERSON><PERSON>, İngilizce gibi 29'dan fazla dili kapsayan çok dilli destek sunmaktadır. Model, talimat takibi, yapılandırılmış verileri anlama ve yapılandırılmış çıktı (özellikle JSON) üretme konularında önemli gelişmeler göstermektedir."}, "qwen/qwen2.5-7b-instruct": {"description": "Çince ve İngilizce'ye yönelik LLM, di<PERSON>, <PERSON><PERSON><PERSON>, mate<PERSON><PERSON>, akıl yür<PERSON><PERSON><PERSON> gibi al<PERSON>lanır."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON>ş LLM, kod <PERSON><PERSON><PERSON><PERSON>, ak<PERSON><PERSON> y<PERSON><PERSON><PERSON>tme ve düzeltme desteği sunar, ana akım programlama dillerini kapsar."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Güçlü orta ölçekli kod modeli, 32K bağlam uzunluğunu destekler, çok dilli programlama konusunda uzmandır."}, "qwen/qwen3-14b": {"description": "Qwen3-14B, Qwen3 serisindeki yoğun 14.8 milyar parametreli nedensel dil modelidir ve karmaşık akıl yürütme ve etkili diyalog için tasarlanmıştır. <PERSON><PERSON><PERSON><PERSON>, programlama ve mantık akıl yürütme gibi görevler için 'düşünme' modu ile genel diyalog için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, talimat takibi, ajan araç kullan<PERSON>, yaratıcı yazım ve 100'den fazla dil ve lehçede çok dilli görevler için ince ayar yapılmıştır. 32K token bağlamını yerel olarak işler ve YaRN tabanlı genişletme ile 131K token'a kadar genişletilebilir."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B, Qwen3 serisindeki yoğun 14.8 milyar parametreli nedensel dil modelidir ve karmaşık akıl yürütme ve etkili diyalog için tasarlanmıştır. <PERSON><PERSON><PERSON><PERSON>, programlama ve mantık akıl yürütme gibi görevler için 'düşünme' modu ile genel diyalog için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, talimat takibi, ajan araç kullan<PERSON>, yaratıcı yazım ve 100'den fazla dil ve lehçede çok dilli görevler için ince ayar yapılmıştır. 32K token bağlamını yerel olarak işler ve YaRN tabanlı genişletme ile 131K token'a kadar genişletilebilir."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B, Qwen tarafından geliştirilen 235B parametreli uzman karışımı (MoE) modelidir ve her ileri geçişte 22B parametreyi etkinleştirir. Karmaşık akıl yü<PERSON><PERSON><PERSON><PERSON>, matematik ve kod görevleri için 'düşünme' modu ile genel diyalog verimliliği için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, güçlü akıl yürü<PERSON><PERSON> yetenekleri, çok dilli destek (100'den fazla dil ve lehçe), ileri düzey talimat takibi ve ajan araç çağırma yetenekleri sergiler. 32K token bağlam penceresini yerel olarak işler ve YaRN tabanlı genişletme ile 131K token'a kadar genişletilebilir."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B, Qwen tarafından geliştirilen 235B parametreli uzman karışımı (MoE) modelidir ve her ileri geçişte 22B parametreyi etkinleştirir. Karmaşık akıl yü<PERSON><PERSON><PERSON><PERSON>, matematik ve kod görevleri için 'düşünme' modu ile genel diyalog verimliliği için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, güçlü akıl yürü<PERSON><PERSON> yetenekleri, çok dilli destek (100'den fazla dil ve lehçe), ileri düzey talimat takibi ve ajan araç çağırma yetenekleri sergiler. 32K token bağlam penceresini yerel olarak işler ve YaRN tabanlı genişletme ile 131K token'a kadar genişletilebilir."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> dil modeli serisinin en son neslidir ve yoğun ve uzman karışımı (MoE) mimarisi ile akıl yür<PERSON>tme, çok dilli destek ve ileri düzey görevlerde mükemmel performans sergilemektedir. Karmaşık akıl yürütme düşünce modu ile etkili diyalog için düşünmeden geçiş yapma yeteneği, çok yönlü ve yüksek kaliteli performansı garanti eder.\n\nQwen3, QwQ ve Qwen2.5 gibi önceki modellere kıyasla önemli ölçüde daha üstündür ve matematik, kod<PERSON>a, genel bilgi akıl yürütme, yaratıcı yazım ve etkileşimli diyalog yetenekleri sunar. Qwen3-30B-A3B varyantı, 30.5 milyar parametre (3.3 milyar etkin parametre), 48 katman, 128 uzman (her görevde 8 etkin) i<PERSON><PERSON>r ve 131K token ba<PERSON><PERSON><PERSON><PERSON><PERSON> destekler (YaRN kullanarak), açık kaynaklı modeller için yeni bir standart belirler."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> dil modeli serisinin en son neslidir ve yoğun ve uzman karışımı (MoE) mimarisi ile akıl yür<PERSON>tme, çok dilli destek ve ileri düzey görevlerde mükemmel performans sergilemektedir. Karmaşık akıl yürütme düşünce modu ile etkili diyalog için düşünmeden geçiş yapma yeteneği, çok yönlü ve yüksek kaliteli performansı garanti eder.\n\nQwen3, QwQ ve Qwen2.5 gibi önceki modellere kıyasla önemli ölçüde daha üstündür ve matematik, kod<PERSON>a, genel bilgi akıl yürütme, yaratıcı yazım ve etkileşimli diyalog yetenekleri sunar. Qwen3-30B-A3B varyantı, 30.5 milyar parametre (3.3 milyar etkin parametre), 48 katman, 128 uzman (her görevde 8 etkin) i<PERSON><PERSON>r ve 131K token ba<PERSON><PERSON><PERSON><PERSON><PERSON> destekler (YaRN kullanarak), açık kaynaklı modeller için yeni bir standart belirler."}, "qwen/qwen3-32b": {"description": "Qwen3-32B, Qwen3 serisindeki yoğun 32.8 milyar parametreli nedensel dil modelidir ve karmaşık akıl yürütme ve etkili diyalog için optimize edilmiştir. <PERSON><PERSON><PERSON><PERSON>, kodlama ve mantık akıl yürütme gibi görevler için 'düşünme' modu ile daha hızlı, genel diyalog için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, talimat takibi, ajan ara<PERSON>ull<PERSON>, yaratıcı yazım ve 100'den fazla dil ve lehçede çok dilli görevlerde güçlü performans sergiler. 32K token bağlamını yerel olarak işler ve YaRN tabanlı genişletme ile 131K token'a kadar genişletilebilir."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B, Qwen3 serisindeki yoğun 32.8 milyar parametreli nedensel dil modelidir ve karmaşık akıl yürütme ve etkili diyalog için optimize edilmiştir. <PERSON><PERSON><PERSON><PERSON>, kodlama ve mantık akıl yürütme gibi görevler için 'düşünme' modu ile daha hızlı, genel diyalog için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, talimat takibi, ajan ara<PERSON>ull<PERSON>, yaratıcı yazım ve 100'den fazla dil ve lehçede çok dilli görevlerde güçlü performans sergiler. 32K token bağlamını yerel olarak işler ve YaRN tabanlı genişletme ile 131K token'a kadar genişletilebilir."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B, Qwen3 serisindeki yoğun 8.2 milyar parametreli nedensel dil modelidir ve akıl yürütme yoğun görevler ve etkili diyalog için tasarlanmıştır. <PERSON><PERSON><PERSON><PERSON>, kodlama ve mantık akıl yürütme için 'düşünme' modu ile genel diyalog için 'düşünmeme' modu arasında sorunsuz geçiş yapmayı destekler. Bu model, talimat takibi, ajan entegra<PERSON>, yaratıcı yazım ve 100'den fazla dil ve lehçede çok dilli kullanım için ince ayar yapılmıştır. 32K token bağlam penceresini yerel olarak destekler ve YaRN aracılığıyla 131K token'a genişletilebilir."}, "qwen2": {"description": "Qwen2, Alibaba'<PERSON>ın yeni nesil büyük ölçekli dil modelidir, mükemmel performans ile çeşitli uygulama ihtiyaçlarını destekler."}, "qwen2-72b-instruct": {"description": "<PERSON>wen<PERSON>, <PERSON><PERSON> ekibinin yeni nesil büyük dil modeli serisidir. Bu model, Transformer mimarisine dayanır ve SwiGLU aktivasyon fonksiyonu, dikkat QKV yanlısı (attention QKV bias), grup sorgu dikkati (group query attention), kayan pencere dikkatı (mixture of sliding window attention) ve tam dikkatin karışımı gibi teknikleri kullanır. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> ekibi, çe<PERSON><PERSON><PERSON> doğal diller ve kodları destekleyen belirteçleyiciyi (tokenizer) de geliştirdi."}, "qwen2-7b-instruct": {"description": "<PERSON>wen<PERSON>, <PERSON><PERSON> ekibinin yeni nesil büyük dil modeli serisidir. Bu model, Transformer mimarisine dayanır ve SwiGLU aktivasyon fonksiyonu, dikkat QKV bias, grup sorgu dikkati, kayan pencere dikkatini ve tam dikkat karışımını içeren teknolojiler kullanır. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> ekibi, çeşitli doğal diller ve kodları için belirteçleyiciyi de geliştirdi."}, "qwen2.5": {"description": "Qwen2.5, <PERSON><PERSON><PERSON><PERSON><PERSON>ın yeni nesil büyük ölçekli dil modelidir ve mükemmel performansıyla çeşitli uygulama ihtiyaçlarını desteklemektedir."}, "qwen2.5-14b-instruct": {"description": "Tongyi Qianwen 2.5, ha<PERSON><PERSON> açık 14B ölçeğinde bir modeldir."}, "qwen2.5-14b-instruct-1m": {"description": "Tongyi Qianwen 2.5, 72B ölçeğinde açık kaynak olarak sunulmuştur."}, "qwen2.5-32b-instruct": {"description": "Tongyi Qianwen 2.5, ha<PERSON><PERSON> açık 32B ölçeğinde bir modeldir."}, "qwen2.5-72b-instruct": {"description": "Tongyi Qianwen 2.5, <PERSON><PERSON><PERSON> 72B ölçeğinde bir modeldir."}, "qwen2.5-7b-instruct": {"description": "Tongyi Qianwen 2.5, ha<PERSON><PERSON> açık 7B ölçeğinde bir modeldir."}, "qwen2.5-coder-1.5b-instruct": {"description": "Tongyi Qianwen kodlama modelinin açık kaynak sürümüdür."}, "qwen2.5-coder-14b-instruct": {"description": "Tongyi Qianwen kodlama modeli açık kaynak sürümü."}, "qwen2.5-coder-32b-instruct": {"description": "Tongyi Qianwen kod modeli açık kaynak versiyonu."}, "qwen2.5-coder-7b-instruct": {"description": "Tongyi Qianwen kodlama modelinin açık kaynak versiyonu."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-<PERSON><PERSON>, <PERSON><PERSON> serisinin en yeni kod odaklı büyük dil modelidir (eski adıyla CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5, <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> dil modeli serisinin en son s<PERSON>rümüdür. Qwen2.5 için, 500 milyondan 7.2 milyara kadar değişen parametre aralığında birden fazla temel dil modeli ve komut ayarlı dil modeli yayınladık."}, "qwen2.5-math-1.5b-instruct": {"description": "Qwen-<PERSON>, g<PERSON><PERSON><PERSON><PERSON> matematiksel problem ç<PERSON><PERSON><PERSON>."}, "qwen2.5-math-72b-instruct": {"description": "Qwen-<PERSON>i, güçlü matematik problem ç<PERSON><PERSON><PERSON>."}, "qwen2.5-math-7b-instruct": {"description": "Qwen-<PERSON>i, güçlü matematik problem ç<PERSON><PERSON><PERSON>."}, "qwen2.5-omni-7b": {"description": "Qwen-<PERSON><PERSON><PERSON> serisi modeller, video, ses, resim ve metin gibi çeşitli modlarda veri girişi destekler ve ses ile metin çıktısı verir."}, "qwen2.5-vl-32b-instruct": {"description": "Qwen2.5-V<PERSON> serisi modeller, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, uzmanlık hizmetleri ve kod geliştirme gibi senaryolarda daha iyi performans göstermek için modelin zekâ seviyesini, prat<PERSON><PERSON><PERSON><PERSON> ve uygunluğunu artırmaktadır. 32B sürümü, pekiştirmeli öğrenme teknolojisi kullanılarak optimize edilmiş olup, Qwen2.5 VL serisinin diğer modellerine kıyasla insan tercihlerine daha uygun çıktı tarzı, karmaşık matematik problemlerini çözme yeteneği ve görüntülerin ince detaylarını anlama ve akıl yürütme becerisi sunmaktadır."}, "qwen2.5-vl-72b-instruct": {"description": "<PERSON><PERSON><PERSON> takibi, mate<PERSON><PERSON>, problem <PERSON><PERSON><PERSON><PERSON>, kod<PERSON>a genelind<PERSON>, her türlü nesneyi tanıma yeteneği artışı, çeşit<PERSON> formatları doğrudan hassas bir şekilde görsel unsurları konumlandırma desteği, uzun video dosyalarını (en fazla 10 dakika) anlama ve saniye düzeyinde olay anlarını konumlandırma yeteneği, zaman sıralamasını ve hızını anlama, analiz ve konumlandırma yeteneğine dayanarak OS veya Mobil ajanları kontrol etme desteği, anahtar bilgileri çıkarma yeteneği ve Json formatında çıktı verme yeteneği güçlüdür, bu sürüm 72B versiyonudur, bu serinin en güçlü versiyonudur."}, "qwen2.5-vl-7b-instruct": {"description": "<PERSON><PERSON><PERSON> takibi, mate<PERSON><PERSON>, problem <PERSON><PERSON><PERSON><PERSON>, kod<PERSON>a genelind<PERSON>, her türlü nesneyi tanıma yeteneği artışı, çeşit<PERSON> formatları doğrudan hassas bir şekilde görsel unsurları konumlandırma desteği, uzun video dosyalarını (en fazla 10 dakika) anlama ve saniye düzeyinde olay anlarını konumlandırma yeteneği, zaman sıralamasını ve hızını anlama, analiz ve konumlandırma yeteneğine dayanarak OS veya Mobil ajanları kontrol etme desteği, anahtar bilgileri çıkarma yeteneği ve Json formatında çıktı verme yeteneği güçlüdür, bu sürüm 72B versiyonudur, bu serinin en güçlü versiyonudur."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL, <PERSON>wen model ailesinin en yeni görsel-dil modeli sürümüdür."}, "qwen2.5:0.5b": {"description": "Qwen2.5, <PERSON><PERSON><PERSON><PERSON><PERSON>ın yeni nesil büyük ölçekli dil modelidir ve mükemmel performansıyla çeşitli uygulama ihtiyaçlarını desteklemektedir."}, "qwen2.5:1.5b": {"description": "Qwen2.5, <PERSON><PERSON><PERSON><PERSON><PERSON>ın yeni nesil büyük ölçekli dil modelidir ve mükemmel performansıyla çeşitli uygulama ihtiyaçlarını desteklemektedir."}, "qwen2.5:72b": {"description": "Qwen2.5, <PERSON><PERSON><PERSON><PERSON><PERSON>ın yeni nesil büyük ölçekli dil modelidir ve mükemmel performansıyla çeşitli uygulama ihtiyaçlarını desteklemektedir."}, "qwen2:0.5b": {"description": "Qwen2, Alibaba'<PERSON>ın yeni nesil büyük ölçekli dil modelidir, mükemmel performans ile çeşitli uygulama ihtiyaçlarını destekler."}, "qwen2:1.5b": {"description": "Qwen2, Alibaba'<PERSON>ın yeni nesil büyük ölçekli dil modelidir, mükemmel performans ile çeşitli uygulama ihtiyaçlarını destekler."}, "qwen2:72b": {"description": "Qwen2, Alibaba'<PERSON>ın yeni nesil büyük ölçekli dil modelidir, mükemmel performans ile çeşitli uygulama ihtiyaçlarını destekler."}, "qwen3": {"description": "Qwen3, Alibaba'nın yeni nesil büyük ölçekli dil modelidir ve çeşitli uygulama ihtiyaçlarını mükemmel bir performansla destekler."}, "qwen3-0.6b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-1.7b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-14b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-235b-a22b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-235b-a22b-instruct-2507": {"description": "Qwen3 tabanlı düşünme modu olmayan açık kaynak modelidir; önceki sürüme (Tongyi Qianwen 3-235B-A22B) kıyasla öznel yaratıcı yetenekler ve model güvenliği açısından hafif iyileştirmeler içerir."}, "qwen3-235b-a22b-thinking-2507": {"description": "Qwen3 tabanlı düşünme modu açık kaynak modelidir; önceki sü<PERSON>üme (Tongyi Qianwen 3-235B-A22B) kı<PERSON>la mantıksal yetenekler, genel yetenekler, bilgi artırımı ve yaratıcı yeteneklerde büyük gelişmeler sağlar ve yüksek zorlukta güçlü çıkarım senaryoları için uygundur."}, "qwen3-30b-a3b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-30b-a3b-instruct-2507": {"description": "<PERSON><PERSON><PERSON> sü<PERSON>üme (Qwen3-30B-A3B) k<PERSON><PERSON><PERSON>, hem İngilizce hem de çok dilli genel yeteneklerde büyük gelişmeler sağlanmıştır. Öznel ve açık uçlu görevler için özel optimizasyon yapılmış olup, kullanıcı tercihleriyle belirgin şekilde daha uyumludur ve daha faydalı yanıtlar sunabilir."}, "qwen3-30b-a3b-thinking-2507": {"description": "Qwen3 tabanlı düşünme modu açık kaynak modeli olup, önceki sü<PERSON>üme (Tongyi Qianwen 3-30B-A3B) kı<PERSON>la mantık yeteneği, genel yetenekler, bilgi artırımı ve yaratıcı yeteneklerde önemli gelişmeler göstermektedir. Zorlu ve güçlü akıl yürütme gerektiren senaryolar için uygundur."}, "qwen3-32b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-4b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-8b": {"description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve <PERSON>ok dilli gibi birçok temel yetenekte endüstri lideri seviyesine ulaşan yeni nesil bir modeldir ve düşünme modu geçişini destekler."}, "qwen3-coder-480b-a35b-instruct": {"description": "Tongyi Qianwen kod modeli açık kaynak versiyonudur. En yeni qwen3-coder-480b-a35b-instruct, Qwen3 tabanlı kod oluşturma modelidir; güçlü Kodlama Ajanı yeteneklerine sa<PERSON>r, a<PERSON><PERSON>ğrısı ve ortam etkileşiminde uzmandır, otonom programlama yapabilir ve üstün kodlama yetenekleri ile genel yetenekleri bir arada sunar."}, "qwen3-coder-plus": {"description": "Tongyi Qianwen kod modelidir. En yeni Qwen3-Coder-Plus serisi modeller, Qwen3 tabanlı kod oluşturma modelleridir; güçlü Kodlama Ajanı yeteneklerine sahiptir, a<PERSON><PERSON> ç<PERSON>ğrısı ve ortam etkileşiminde u<PERSON>dır, otonom programlama yapabilir ve üstün kodlama yetenekleri ile genel yetenekleri bir arada sunar."}, "qwq": {"description": "QwQ, AI akıl yü<PERSON><PERSON><PERSON><PERSON>klerini artırmaya odaklanan deneysel bir araştırma modelidir."}, "qwq-32b": {"description": "Qwen2.5-32B modeli üzerine eğitilmiş QwQ çıkarım modeli, pekiştirmeli öğrenme ile modelin çıkarım yeteneğini önemli ölçüde artırmıştır. Modelin matematiksel kodları ve diğer temel göstergeleri (AIME 24/25, LiveCodeBench) ile bazı genel gö<PERSON> (IFEval, LiveBench vb.) DeepSeek-R1 tam sürüm seviyesine ulaşmıştır ve tüm göstergel<PERSON>, yine Qwen2.5-32B tabanlı olan DeepSeek-R1-Distill-Qwen-32B'yi önemli ölçüde aşmaktadır."}, "qwq-32b-preview": {"description": "QwQ modeli, <PERSON><PERSON> ekibi tarafından geliştirilen deneysel bir araştırma modelidir ve AI akıl yürütme yeteneklerini artırmaya odaklanmaktadır."}, "qwq-plus": {"description": "Qwen2.5 modeli temel alınarak eğitilmiş QwQ akıl yürütme modeli, pekiştirmeli öğrenme ile modelin akıl yürütme yeteneğini büyük ölçüde artırmıştır. Model, matematik ve kodlama gibi temel g<PERSON> (AIME 24/25, LiveCodeBench) ve bazı genel gö<PERSON>e (IFEval, LiveBench vb.) DeepSeek-R1 tam sürüm seviyesine ulaşmıştır."}, "qwq_32b": {"description": "Qwen serisinin orta ölçekli çıkarım modelidir. Geleneksel talimat ayarlama modellerine kıyasla, d<PERSON><PERSON>ünme ve çıkarım yeteneğine sahip QwQ, özellikle zorlu görevleri çözme konusunda, alt görevlerde performansı önemli ölçüde artırabilir."}, "r1-1776": {"description": "R1-1776, DeepSeek R1 modelinin bir versiyonudur ve son e<PERSON>, sa<PERSON><PERSON><PERSON><PERSON><PERSON>, tara<PERSON><PERSON>z gerçek bilgileri sunar."}, "solar-mini": {"description": "Solar Mini, GPT-3.5'ten daha iyi performansa sahip kompakt bir LLM'dir, g<PERSON><PERSON><PERSON><PERSON> çok dilli yeteneklere sa<PERSON>, İngilizce ve Korece'yi destekler ve et<PERSON><PERSON>, kompakt çözümler sunar."}, "solar-mini-ja": {"description": "Solar Mini (Ja), Solar Mini'nin <PERSON> g<PERSON>letir, Japonca'ya o<PERSON>ır ve İngilizce ile Korece kullanımında yüksek verimlilik ve mükemmel performans sağlar."}, "solar-pro": {"description": "Solar Pro, Upstage tarafından sunulan yüksek akıllı LLM'dir, tek GPU talimat takibi yeteneğ<PERSON> o<PERSON>ır, IFEval puanı 80'in üzerindedir. Şu anda İngilizceyi desteklemekte olup, resmi versiyonu 2024 Kasım'da piyasaya sürülmesi planlanmaktadır ve dil desteği ile bağlam uzunluğunu genişletecektir."}, "sonar": {"description": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> dayalı hafif bir a<PERSON>, Sonar Pro'dan daha hızlı ve daha ucuzdur."}, "sonar-deep-research": {"description": "Deep Research, kapsamlı uzman düzeyinde araştırmalar yapar ve bunları erişilebilir, uygulanabilir raporlar haline getirir."}, "sonar-pro": {"description": "Gelişmiş sorgular ve takip deste<PERSON>, arama bağlamını destekleyen bir üst düzey arama ürünüdür."}, "sonar-reasoning": {"description": "DeepSeek akıl yürütme modeli tarafından desteklenen yeni API ürünü."}, "sonar-reasoning-pro": {"description": "DeepSeek'in akıl yürütme modeli tarafından desteklenen yeni API ürünü."}, "stable-diffusion-3-medium": {"description": "Stability AI tarafından geliştirilen en yeni metinden görüntü oluşturma büyük modelidir. Önceki sürümlerin avantajlarını koruyarak, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin anlama ve stil çeşitliliği alanlarında önemli iyileştirmeler sunar. Karmaşık doğal dil istemlerini daha doğru yorumlayabilir ve daha kesin, çeşitli görüntüler oluşturabilir."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large, 800 milyon parametreli çok modlu difüzyon dönüştürücü (MMDiT) metinden görüntü oluşturma modelidir. Üstün görüntü kalitesi ve istem uyumu <PERSON>ğ<PERSON>, 1 milyon piksel yüksek çözünürlüklü görüntüler oluşturabilir ve sıradan tüketici donanımında verimli çalışabilir."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo, stable-diffusion-3.5-large temel alınarak adversarial diffusion distillation (ADD) teknolojisi ile hızlandırılmış modeldir."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5, stable-diffusion-v1.2 kontrol noktası ağırlıkları ile başlatılmış ve \"laion-aesthetics v2 5+\" üzerinde 512x512 çözünürlükte 595k adım ince ayar yapılmıştır. %10 daha az metin koşullandırması ile sınıflandırıcı olmayan rehberli örnekleme geliştirilmiştir."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl, v1.5'e kıyasla önemli geliştirmeler içerir ve mevcut açık kaynak metinden görüntü oluşturma SOTA modeli midjourney ile benzer performans gösterir. Gelişmeler şunlardır: daha büyük unet omurgası (öncekinden 3 kat büyük); görünt<PERSON> kalitesini artırmak için iyileştirme modülü eklenmesi; daha verimli eğitim teknikleri."}, "stable-diffusion-xl-base-1.0": {"description": "Stability AI tarafından geliştirilen ve açık kaynaklı metinden görüntü oluşturma büyük modelidir. Yaratıcı görüntü oluşturma yetenekleri sektörde öncüdür. Üstün talimat anlama yeteneğine sahiptir ve ters prompt tanımlamayı destekleyerek içeriği hassas şekilde oluşturabilir."}, "step-1-128k": {"description": "Performans ve maliyet arasında denge <PERSON>, genel senary<PERSON> için u<PERSON>gundu<PERSON>."}, "step-1-256k": {"description": "Ultra uzun bağ<PERSON> iş<PERSON>e <PERSON>, özellikle uzun belgelerin analizine uygundur."}, "step-1-32k": {"description": "Orta uzunlukta diyalogları destekler, çeşitli uygulama senaryoları için uygundur."}, "step-1-8k": {"description": "<PERSON><PERSON><PERSON><PERSON>k model, ha<PERSON><PERSON> g<PERSON><PERSON><PERSON>."}, "step-1-flash": {"description": "<PERSON><PERSON><PERSON><PERSON> hızlı model, gerçek zamanlı diyaloglar için uygundur."}, "step-1.5v-mini": {"description": "Bu model, <PERSON><PERSON><PERSON><PERSON><PERSON> bir video anlama <PERSON>."}, "step-1o-turbo-vision": {"description": "Bu model, <PERSON><PERSON><PERSON><PERSON><PERSON> bir g<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>, matematik ve kod alanında 1o'dan daha üstündür. Model, 1o'dan daha küçüktür ve çıktı hızı daha yüksektir."}, "step-1o-vision-32k": {"description": "Bu model, g<PERSON><PERSON><PERSON><PERSON> bir gör<PERSON><PERSON><PERSON> anlama <PERSON> sahiptir. Step-1v serisi modellere kıyasla daha güçlü bir görsel performansa sahiptir."}, "step-1v-32k": {"description": "<PERSON><PERSON><PERSON><PERSON> girdi <PERSON>, çok modlu et<PERSON><PERSON><PERSON><PERSON> den<PERSON>ırı<PERSON>."}, "step-1v-8k": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> görsel model, temel metin ve görsel görevler i<PERSON>in u<PERSON>gundu<PERSON>."}, "step-1x-edit": {"description": "Bu model, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> düzenleme görevlerine odaklanır ve kullanıcı tarafından sağlanan görüntü ve metin açıklamalarına göre görüntüyü değiştirip iyileştirebilir. Metin açıklamaları ve örnek görüntüler dahil olmak üzere çeşitli giriş formatlarını destekler. Model, kullanıcı niyetini anlayarak istenen düzenleme sonuçlarını üretir."}, "step-1x-medium": {"description": "Bu model g<PERSON><PERSON><PERSON><PERSON> gör<PERSON><PERSON>ü oluşturma yeteneklerine sahiptir ve metin açıklamalarını giriş olarak destekler. <PERSON><PERSON> deste<PERSON>i ile Çince metin açıklamalarını daha iyi anlar ve işler, metin anlamını daha doğru yakalayarak görüntü özelliklerine dönüştürür ve böylece daha hassas görüntü oluşturma sağlar. Model, yüks<PERSON> çözünürlüklü ve kaliteli görüntüler oluşturabilir ve belirli ölçüde stil transferi yeteneğine sahiptir."}, "step-2-16k": {"description": "Büyük ölçekli bağlam etkileşimlerini destekler, karmaşık diyalog senaryoları için uygundur."}, "step-2-16k-exp": {"description": "step-2 model<PERSON><PERSON> den<PERSON> vers<PERSON>, en son özellikleri içerir ve sürekli güncellenmektedir. Resmi üretim ortamında kullanılması önerilmez."}, "step-2-mini": {"description": "Yeni nesil kendi geliştirdiğimiz MFA Attention mimarisine dayanan hızlı büyük model, çok düşük maliyetle step1 ile benzer sonuçlar elde ederken, daha yüks<PERSON> bir throughput ve daha hızlı yanıt süresi sağlıyor. Genel görevleri işleyebilme yeteneğine sahip olup, kodlama yeteneklerinde uzmanlık gösteriyor."}, "step-2x-large": {"description": "Jieyue Xingchen'in yeni nesil görüntü oluşturma modelidir. Model, kullanıc<PERSON> tarafından sağlanan metin açıklamalarına göre yüksek kaliteli görünt<PERSON>ler oluşturur. Yeni model, daha gerçekçi doku ve hem Çince hem İngilizce metin oluşturma yeteneklerinde gelişmiş performans sunar."}, "step-r1-v-mini": {"description": "Bu model, g<PERSON><PERSON><PERSON><PERSON> gö<PERSON><PERSON><PERSON><PERSON> anlama yeteneğine sahip bir çıkarım büyük modelidir, g<PERSON><PERSON><PERSON><PERSON><PERSON> ve metin bilgilerini işleyebilir, derin düşünme sonrası metin oluşturma çıktısı verebilir. Bu model, g<PERSON><PERSON><PERSON> çıkarım alanında öne çıkarken, birin<PERSON> sınıf matematik, kod ve metin çıkarım yeteneklerine de sahiptir. Bağlam uzunluğu 100k'dır."}, "taichu_llm": {"description": "Zidong Taichu dil b<PERSON><PERSON><PERSON><PERSON> modeli, g<PERSON><PERSON><PERSON><PERSON> dil anlama yeteneği ile metin o<PERSON>ma, bilgi sorg<PERSON>, kod <PERSON><PERSON><PERSON>, matemat<PERSON>, mant<PERSON><PERSON><PERSON> akıl <PERSON>, du<PERSON><PERSON> analizi, metin özeti gibi yeteneklere sahiptir. Yenilikçi bir şekilde büyük veri ön eğitimi ile çok kaynaklı zengin bilgiyi birleştirir, algoritma teknolojisini sürekli olarak geliştirir ve büyük metin verilerinden kelime, yap<PERSON>, dil bilgis<PERSON>, anlam gibi yeni bilgileri sürekli olarak edinir, modelin performansını sürekli olarak evrimleştirir. Kullanıcılara daha kolay bilgi ve hizmetler sunar ve daha akıllı bir deneyim sağlar."}, "taichu_o1": {"description": "taichu_o1, yeni nesil çıkarım büyük modelidir, çok modlu etkileşim ve pekiştirme öğrenimi ile insan benzeri düşünme zincirleri oluşturur, karmaş<PERSON>k karar verme senaryolarını destekler, yü<PERSON><PERSON> hassasiyetli çıktılar sunarken model çıkarım düşünce yollarını sergiler, strateji analizi ve derin düşünme gibi senaryolar için uygundur."}, "taichu_vl": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bi<PERSON><PERSON>, mantıks<PERSON> çıkarım gibi yetenekleri birleştirir ve görsel-işitsel soru-cevap alanında öne çıkar."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct, 80 milyar parametreye sahip olup, 13 milyar parametre etkinleştirilerek daha büyük modellerle rekabet edebilir; \"hızlı düşünme/yavaş düşünme\" karma akıl yürütmeyi destekler; uzun metin anlama ka<PERSON>lıdır; BFCL-v3 ve τ-<PERSON>ch ile doğrulanmış, ajan yet<PERSON> liderdir; GQA ve çoklu kuantizasyon formatlarıyla birleşerek verimli akıl yürütme sağlar."}, "text-embedding-3-large": {"description": "En güçlü vektörleştirme modeli, İngilizce ve diğer dillerdeki görevler için uygundur."}, "text-embedding-3-small": {"description": "Verimli ve ekonomik yeni nesil Embedding modeli, bilgi arama, RAG uygulamaları gibi senaryolar için uygundur."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414, kod <PERSON><PERSON><PERSON><PERSON>, fonks<PERSON><PERSON> çağrıları ve ajan tabanlı görevler için optimize edilmiş 32B iki dilli (Çince ve İngilizce) açık ağırlık dil modelidir. 15T yüksek kaliteli ve yeniden akıl yürütme verisi üzerinde önceden eğitilmiştir ve insan tercihleri uyumu, reddetme örnekleme ve pekiştirmeli öğrenme ile daha da geliştirilmiştir. Bu model, karmaşık akıl yürütme, nesne üretimi ve yapılandırılmış çıktı görevlerinde mükemmel performans sergilemekte ve birçok benchmark testinde GPT-4o ve DeepSeek-V3-0324 ile karşılaştırılabilir performans göstermektedir."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414, kod <PERSON><PERSON><PERSON><PERSON>, fonks<PERSON><PERSON> çağrıları ve ajan tabanlı görevler için optimize edilmiş 32B iki dilli (Çince ve İngilizce) açık ağırlık dil modelidir. 15T yüksek kaliteli ve yeniden akıl yürütme verisi üzerinde önceden eğitilmiştir ve insan tercihleri uyumu, reddetme örnekleme ve pekiştirmeli öğrenme ile daha da geliştirilmiştir. Bu model, karmaşık akıl yürütme, nesne üretimi ve yapılandırılmış çıktı görevlerinde mükemmel performans sergilemekte ve birçok benchmark testinde GPT-4o ve DeepSeek-V3-0324 ile karşılaştırılabilir performans göstermektedir."}, "thudm/glm-4-9b-chat": {"description": "Zhi Pu AI tarafından yayınlanan GLM-4 serisinin en son nesil ön eğitim modelinin açık kaynak versiyonudur."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414, THUDM tarafından geliştirilen GLM-4 serisinin 9 milyar parametreli dil modelidir. GLM-4-9B-0414, daha büyük 32B karşılık gelen model ile aynı güçlendirilmiş öğrenme ve hizalama stratejilerini kullanarak eğitilmiştir ve ölçeğine göre yüksek performans sergileyerek hala güçlü dil anlama ve üretim yeteneklerine ihtiyaç duyan kaynak sınırlı dağıtımlar için uygundur."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414, GLM-4-32B'nin geliştirilmiş akıl yürütme varyantıdır ve derin matematik, mantık ve kod odaklı sorun çözme için tasarlanmıştır. Karmaşık çok adımlı görevlerin performansını artırmak için genişletilmiş pekiştirmeli öğrenme (görev spesifik ve genel çift tercih tabanlı) uygular. Temel GLM-4-32B modeline kıyasla, Z1 yapılandırılmış akıl yürütme ve formel alanlardaki yetenekleri önemli ölçüde artırmıştır.\n\nBu model, ipucu mühendisliği ile 'düşünme' adımlarını zorunlu kılmayı destekler ve uzun format çıktılar için geliştirilmiş tutarlılık sağlar. <PERSON><PERSON> iş akışları için optimize edilmiştir ve uzun ba<PERSON> (YaRN aracılığıyla), JSON araç çağrılarını ve kararlı akıl yürütme için ince ayar örnekleme yapılandırmalarını destekler. Derin düşünme, çok adımlı akıl yürütme veya formel çıkarım gerektiren kullanım durumları için idealdir."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414, GLM-4-32B'nin geliştirilmiş akıl yürütme varyantıdır ve derin matematik, mantık ve kod odaklı sorun çözme için tasarlanmıştır. Karmaşık çok adımlı görevlerin performansını artırmak için genişletilmiş pekiştirmeli öğrenme (görev spesifik ve genel çift tercih tabanlı) uygular. Temel GLM-4-32B modeline kıyasla, Z1 yapılandırılmış akıl yürütme ve formel alanlardaki yetenekleri önemli ölçüde artırmıştır.\n\nBu model, ipucu mühendisliği ile 'düşünme' adımlarını zorunlu kılmayı destekler ve uzun format çıktılar için geliştirilmiş tutarlılık sağlar. <PERSON><PERSON> iş akışları için optimize edilmiştir ve uzun ba<PERSON> (YaRN aracılığıyla), JSON araç çağrılarını ve kararlı akıl yürütme için ince ayar örnekleme yapılandırmalarını destekler. Derin düşünme, çok adımlı akıl yürütme veya formel çıkarım gerektiren kullanım durumları için idealdir."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414, THUDM tarafından geliştirilen GLM-4 serisinin 9B parametreli dil modelidir. Daha büyük GLM-Z1 modeline uygulanan teknikleri içermekte olup, güçlendirilmiş öğrenme, çift sıralama hizalaması ve matematik, kodlama ve mantık gibi akıl yürütme yoğun görevler için eğitim almıştır. <PERSON><PERSON> kü<PERSON><PERSON><PERSON> olma<PERSON> rağ<PERSON>, genel akıl yürütme görevlerinde güçlü performans sergilemekte ve ağırlık seviyesinde birçok açık kaynak modelinden daha üstündür."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B, GLM-4-Z1 serisinin 32B parametreli derin akıl yürütme modelidir ve uzun süre düşünmeyi gerektiren karmaşık, açık uçlu görevler için optimize edilmiştir. glm-4-32b-0414 temel alınarak geliştirilmiş ve ek güçlendirilmiş öğrenme aşamaları ve çok aşamalı hizalama stratejileri eklenmiştir; genişletilmiş bilişsel işleme simüle etmek için 'düşünme' yeteneği getirilmiştir. Bu, yinelemeli akıl yürütme, çok adımlı analiz ve arama, alma ve alıntı bilincine sahip sentez gibi araç artırma iş akışlarını içerir.\n\nBu model, araştırma yazımı, karşılaştırmalı analiz ve karmaşık soru-cevap konularında mükemmel performans sergiler. Arama ve navigasyon ilkelere (`search`, `click`, `open`, `finish`) yönelik işlev çağrılarını destekler, böylece ajan tabanlı boru hatlarında kullanılabilir. Düşünme davranışı, kural tabanlı ödüller ve gecikmeli karar verme mekanizması ile çok turlu döngü kontrolü ile şekillendirilir ve OpenAI iç hizalama yığını gibi derin araştırma çerçevelerine göre değerlendirilir. Bu varyant, derinlik gerektiren senaryolar için uygundur."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-<PERSON><PERSON><PERSON>, DeepSeek-R1 ve DeepSeek-V3 (0324) birleştirilerek oluşturulmuştur ve R1'in akıl yürütme yetenekleri ile V3'ün token verimliliği iyileştirmelerini bir araya getirir. DeepSeek-MoE Transformer mimarisine dayanır ve genel metin üretim görevleri için optimize edilmiştir.\n\nBu model, iki kaynak modelin önceden eğitilmiş ağırlıklarını birleştirerek akıl yürütme, verimlilik ve talimat takibi görevlerinin performansını dengelemektedir. MIT lisansı altında yayımlanmış olup, araştırma ve ticari kullanım için tasarlanmıştır."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> (7B), et<PERSON><PERSON> stratejiler ve model mi<PERSON><PERSON> ile artır<PERSON>lmış hesap<PERSON><PERSON>."}, "tts-1": {"description": "En son metinden sese model, ger<PERSON><PERSON> zamanlı senaryolar için hız optimizasyonu yapılmıştır."}, "tts-1-hd": {"description": "En son metinden sese model, ka<PERSON>yi optimize etmek için ta<PERSON>tır."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B), ince ayar gerektiren talimat görevleri için uygundur ve mükemmel dil işleme yet<PERSON> sunar."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON> model<PERSON> ve Claude 3 Opus'u aşan performans sergilemekte; g<PERSON><PERSON>ğerlendirmelerde mükemmel sonuçlar verirken, orta seviye modellerimizin hız ve maliyetine sa<PERSON>tir."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "<PERSON> 3.7 sonnet, Anthrop<PERSON>'in en hızlı bir sonraki nesil modelidir. <PERSON> 3 <PERSON><PERSON> ile karşılaştır<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> 3.7 Son<PERSON>, tüm becerilerde iyileşmeler göstermiştir ve birçok zeka standart testinde bir önceki neslin en büyük modeli olan Claude 3 Opus'u geride bırakmıştır."}, "v0-1.0-md": {"description": "v0-1.0-md modeli, v0 API aracılığıyla hizmet veren eski bir modeldir"}, "v0-1.5-lg": {"description": "v0-1.5-lg model<PERSON>, <PERSON><PERSON><PERSON><PERSON> veya muhakeme görevleri i<PERSON>in <PERSON>r"}, "v0-1.5-md": {"description": "v0-1.5-md modeli, günlük görevler ve kullanıcı arayüzü (UI) oluşturma için uygundur"}, "wan2.2-t2i-flash": {"description": "Wanxiang 2.2 hız<PERSON><PERSON>, mevcut en yeni modeldir. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kararlılık ve gerçekçilikte kapsamlı yükseltmeler sunar; hızlı üretim hızı ve yüksek maliyet performansı sağlar."}, "wan2.2-t2i-plus": {"description": "Wanxiang 2.2 profe<PERSON><PERSON><PERSON>, mevcut en yeni modeldir. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kararlılık ve gerçekçilikte kapsamlı yükseltmeler sunar; detaylı ve zengin görüntüler üretir."}, "wanx-v1": {"description": "Temel metinden görüntü oluşturma modelidir. Tongyi Wanxiang resmi web sitesindeki 1.0 genel modeline karşılık gelir."}, "wanx2.0-t2i-turbo": {"description": "Doku ve portrelerde uzmandır; orta hızda ve düşük maliyetlidir. Tongyi Wanxiang resmi web sitesindeki 2.0 hızlı modele karşılık gelir."}, "wanx2.1-t2i-plus": {"description": "Kapsamlı yükseltilmiş versiyondur. Üretilen görüntü detayları daha zengindir, hız biraz daha yavaştır. Tongyi Wanxiang resmi web sitesindeki 2.1 profesyonel modele karşılık gelir."}, "wanx2.1-t2i-turbo": {"description": "Kapsamlı yükseltilmiş versiyondur. <PERSON>retim hızı hızlı, etkisi kapsamlı ve genel maliyet performansı yüksektir. Tongyi Wanxiang resmi web sitesindeki 2.1 hızlı modele karşılık gelir."}, "whisper-1": {"description": "Genel amaçlı konuşma tanıma modeli olup, çok dilli konuşma tanıma, konuşma çevirisi ve dil tanıma destekler."}, "wizardlm2": {"description": "WizardLM 2, Microsoft AI tarafından sunulan bir dil modelidir, karma<PERSON><PERSON><PERSON> diyaloglar, <PERSON><PERSON> dilli, akıl yürütme ve akıllı asistan alanlarında özellikle başarılıdır."}, "wizardlm2:8x22b": {"description": "WizardLM 2, Microsoft AI tarafından sunulan bir dil modelidir, karma<PERSON><PERSON><PERSON> diyaloglar, <PERSON><PERSON> dilli, akıl yürütme ve akıllı asistan alanlarında özellikle başarılıdır."}, "x1": {"description": "Spark X1 modeli daha da geliştirilecek; önceki matematik görevlerinde ulusal liderlik temelinde, ak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin <PERSON>, dil anlama gibi genel görevlerde OpenAI o1 ve DeepSeek R1 ile karşılaştırılabilir sonuçlar elde edilecektir."}, "yi-1.5-34b-chat": {"description": "Yi-1.5, Yi'nin <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş sürümüdür. Yüksek kaliteli 500B token'lı veri kümesi üzerinde devam eden ön eğitimi ve 3M çeşitlendirilmiş ince ayar örneği üzerinde ince ayarını içerir."}, "yi-large": {"description": "<PERSON><PERSON> ne<PERSON>l yüz milyar parametreli model, g<PERSON><PERSON><PERSON><PERSON> soru yanıtlama ve metin üretim yetenekle<PERSON> sunar."}, "yi-large-fc": {"description": "yi-large modelinin temelinde, araç çağrısı yeteneklerini destekleyip güçlendiren bir yapı sunar, çeşitli ajan veya iş akışı kurma gereksinimleri için uygundur."}, "yi-large-preview": {"description": "<PERSON><PERSON><PERSON> sü<PERSON>, yi-large (yeni sürüm) kullanılması önerilir."}, "yi-large-rag": {"description": "yi-large modelinin güçlü bir hizmeti, arama ve üretim teknolojilerini birleştirerek doğru yanıtlar sunar, ger<PERSON><PERSON> zamanlı olarak tüm ağdan bilgi arama hizmeti sağlar."}, "yi-large-turbo": {"description": "Son derece yüksek maliyet performansı ve mükemmel performans. Performans ve akıl yürütme hızı, maliyet açısından yüksek hassasiyetli ayarlama yapılır."}, "yi-lightning": {"description": "En yeni yüksek performanslı model, yüksek kaliteli çıktıları garanti ederken akıl yürütme hızını büyük ölçüde artırır."}, "yi-lightning-lite": {"description": "<PERSON><PERSON><PERSON>, yi-<PERSON> kullanımını önerir."}, "yi-medium": {"description": "<PERSON><PERSON> model, den<PERSON><PERSON> yet<PERSON>r ve yüksek maliyet performansı sunar. Talimat takibi yetenekleri derinlemesine optimize edilmiştir."}, "yi-medium-200k": {"description": "200K ultra uzun ba<PERSON><PERSON> pen<PERSON>, uzun metinlerin derinlemesine anlaşılması ve üretilmesi yetenekleri sunar."}, "yi-spark": {"description": "Küçük ama et<PERSON>, hafif ve hızlı bir modeldir. Güçlendirilmiş matematiksel işlemler ve kod yazma yetenekleri sunar."}, "yi-vision": {"description": "Ka<PERSON><PERSON><PERSON><PERSON> görsel görevler için model, <PERSON><PERSON><PERSON><PERSON>anslı resim anlama ve analiz yetenekleri sunar."}, "yi-vision-v2": {"description": "Karma<PERSON><PERSON><PERSON> gö<PERSON><PERSON> görevler için model, birden fazla resme dayalı yüksek performanslı anlama ve analiz yetenekleri sunar."}, "zai-org/GLM-4.5": {"description": "GLM-4.5, ak<PERSON><PERSON><PERSON> ajan u<PERSON>aları için tasarlan<PERSON>ış temel modeldir ve Mixture-of-Experts (MoE) mimarisi kullanır. <PERSON><PERSON> çağrısı, web tarama, yazılım mühendisliği ve ön uç programlama alanlarında derin optimizasyonlar içerir. <PERSON>, Roo Code gibi kod ajanlarına sorunsuz entegrasyon destekler. GLM-4.5, karma<PERSON><PERSON>k çıkarım ve günlük kullanım gibi çeşitli senaryolara uyum sağlayan hibrit çıkarım moduna sahiptir."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air, akıll<PERSON> ajan uygulamaları için tasarlanmış temel modeldir ve Mixture-of-Experts (MoE) mimarisi kullanır. <PERSON><PERSON> çağrısı, web tarama, yazılım mühendisliği ve ön uç programlama alanlarında derin optimizasyonlar içerir. Claude Code, Roo Code gibi kod ajanlarına sorunsuz entegrasyon destekler. GLM-4.5, karma<PERSON><PERSON>k çıkarım ve günlük kullanım gibi çeşitli senaryolara uyum sağlayan hibrit çıkarım moduna sahiptir."}}