{"ai21": {"description": "AI21 Labs, işletmeler için temel modeller ve yapay zeka sistemleri geliştirerek, üretimde jeneratif yapay zekanın uygulanmasını hızlandırır."}, "ai360": {"description": "360 AI, 360 şirketi tarafından sunulan yapay zeka modeli ve hizmet platformudur. 360GPT2 Pro, 360GPT Pro, 360GPT Turbo ve 360GPT Turbo Responsibility 8K gibi çeşitli gelişmiş doğal dil işleme modelleri sunmaktadır. Bu modeller, büyük ölçekli parametreler ve çok modlu yetenekleri birleştirerek metin üretimi, anlamsal anlama, diyalog sistemleri ve kod üretimi gibi alanlarda geniş bir uygulama yelpazesine sahiptir. Esnek fiyatlandırma stratejileri ile 360 AI, çeşitli kullanıcı ihtiyaçlarını karşılamakta ve geliştiricilerin entegrasyonunu destekleyerek akıllı uygulamaların yenilik ve gelişimini teşvik etmektedir."}, "aihubmix": {"description": "AiHubMix, çeşitli yapay zeka modellerine tek bir API arayüzü üzerinden erişim <PERSON>."}, "anthropic": {"description": "<PERSON><PERSON><PERSON>, yapay zeka araştırma ve geliştirmeye odaklanan bir şirkettir. <PERSON> 3.5 <PERSON><PERSON>, <PERSON> 3 <PERSON><PERSON>, <PERSON> 3 Opus ve Claude 3 Haiku gibi bir dizi gelişmiş dil modeli sunmaktadır. <PERSON><PERSON> modeller, <PERSON><PERSON>, hız ve maliyet arasında ideal bir denge sağlamaktadır ve kurumsal düzeydeki iş yüklerinden hızlı yanıt gerektiren çeşitli uygulama senaryolarına kadar geniş bir yelpazede kullanılmaktadır. <PERSON> 3.5 <PERSON><PERSON>, en son modeli o<PERSON>, birçok değerlendirmede mükemmel performans sergilemekte ve yüksek maliyet etkinliğini korumaktadır."}, "azure": {"description": "Azure, GPT-3.5 ve en son GPT-4 serisi gibi çeşitli gelişmiş yapay zeka modelleri sunar. Farklı veri türlerini ve karmaşık görevleri destekleyerek güvenli, güven<PERSON>r ve sürdürülebilir yapay zeka çözümleri sağlamaya odaklanmaktadır."}, "azureai": {"description": "Azure, GPT-3.5 ve en son GPT-4 serisi dahil olmak üzere çeşitli gelişmiş AI modelleri sunar, çeşitli veri türlerini ve karmaşık görevleri destekler, <PERSON><PERSON><PERSON><PERSON>, g<PERSON>ven<PERSON>r ve sürdürülebilir AI çözümlerine odaklanır."}, "baichuan": {"description": "Baichuan Intelligent, ya<PERSON>y zeka büyük modellerinin geliştirilmesine odaklanan bir şirkettir. <PERSON><PERSON>i, yerel bilgi ansik<PERSON>edisi, uzun metin işleme ve üretim gibi Çince görevlerde mükemmel performans sergilemekte ve uluslararası ana akım modelleri aşmaktadır. Baichuan Intelligent ayrıca sektördeki lider çok modlu yeteneklere sahiptir ve birçok otoriter değerlendirmede mükemmel sonuçlar elde etmiştir. Modelleri, Baichuan 4, Baichuan 3 Turbo ve Baichuan 3 Turbo 128k gibi farklı uygulama senaryolarına yönelik optimize edilmiş yüksek maliyet etkinliği çözümleri sunmaktadır."}, "bedrock": {"description": "<PERSON><PERSON>, Amazon AWS tarafından sunulan bir hizmettir ve işletmelere gelişmiş yapay zeka dil modelleri ve görsel modeller sağlamaya odaklanmaktadır. <PERSON> a<PERSON><PERSON>, An<PERSON><PERSON>'in <PERSON>, Meta'nın Llama 3.1 serisi gibi seçenekleri içermekte olup, met<PERSON>, <PERSON><PERSON><PERSON>, gör<PERSON>nt<PERSON> işleme gibi çeşitli görevleri desteklemektedir. Farklı ölçek ve ihtiyaçlara uygun kurumsal uygulamalar için geniş bir yelpaze sunmaktadır."}, "cloudflare": {"description": "Cloudflare'ın küresel ağı üzerinde sunucusuz GPU destekli makine öğrenimi modelleri çalıştırın."}, "cohere": {"description": "Cohere, en son çok dilli modelleri, gelişmiş arama işlevselliğini ve modern işletmeler için özel olarak tasarlanmış AI çalışma alanını sunar - hepsi güvenli bir platformda entegre edilmiştir."}, "deepseek": {"description": "DeepSeek, yapay zeka teknolojisi araştırma ve uygulamalarına odaklanan bir şirkettir. En son modeli DeepSeek-V2.5, genel diyalog ve kod işleme yeteneklerini bi<PERSON>ek, insan terc<PERSON>, yazma görevleri ve talimat takibi gibi alanlarda önemli iyileştirmeler sağlamaktadır."}, "fal": {"description": "Geliştiricilere yönelik üretken medya platformu"}, "fireworksai": {"description": "Fireworks AI, i<PERSON><PERSON> çağrısı ve çok modlu işleme üzerine odaklanan önde gelen bir gelişmiş dil modeli hizmet sağlayıcısıdır. En son modeli Firefunction V2, Llama-3 tabanlıdır ve işlev çağrısı, diyalog ve talimat takibi için optimize edilmiştir. Görsel dil modeli FireLLaVA-13B, görüntü ve metin karışık girişi desteklemektedir. Diğer dikkat çekici modeller arasında Llama serisi ve Mixtral serisi bulunmaktadır ve etkili çok dilli talimat takibi ve üretim desteği sunmaktadır."}, "giteeai": {"description": "Gitee AI'nin <PERSON>, AI geliştiricileri kutusun dışında büyük modeller infeksiyon API hizmetini sağlar."}, "github": {"description": "GitHub Modelleri ile <PERSON>, AI mühendisleri olabilir ve sektörün önde gelen AI modelleri ile inşa edebilirler."}, "google": {"description": "Google'ın <PERSON> serisi, Google DeepMind tarafından geliştirilen en gelişmiş ve genel yapay zeka modelleridir. Çok modlu tasarımı sayesinde metin, kod, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ses ve video gibi çeşitli veri türlerini sorunsuz bir şekilde anlama ve işleme yeteneğine sahiptir. Veri merkezlerinden mobil cihazlara kadar çeşitli ortamlarda kullanılabilir, yapay zeka modellerinin verimliliğini ve uygulama kapsamını büyük ölçüde artırmaktadır."}, "groq": {"description": "Groq'un LPU çıkarım motoru, en son ba<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>y<PERSON>k dil modeli (LLM) benchmark testlerinde mükemmel performans sergilemekte ve olağanüstü hız ve verimliliği ile yapay zeka çözümlerinin standartlarını yeniden tanımlamaktadır. Groq, bulut tabanlı dağıtımlarda iyi bir performans sergileyen anlık çıkarım hızının temsilcisidir."}, "higress": {"description": "<PERSON>g<PERSON>, uzun süreli bağlantı işlerine zarar veren Tengine yeniden yükleme sorununu ve gRPC/Dubbo yük dengeleme yeteneklerinin yetersizliğini çözmek için Alibaba içinde geliştirilmiş bir bulut yerel API geçididir."}, "huggingface": {"description": "HuggingFace Inference API, binlerce modeli keşfetmenin hızlı ve ücretsiz bir yolunu sunar, çeşitli görevler için uygundur. Yeni uygulamalar için prototip oluşturuyor ya da makine öğreniminin yeteneklerini deniyorsanız, bu API size birçok alanda yüksek performanslı modellere anında erişim sağ<PERSON>."}, "hunyuan": {"description": "<PERSON>cent <PERSON>n geliştirilen büyük bir dil modeli, <PERSON><PERSON><PERSON><PERSON><PERSON> yet<PERSON>, ka<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>a mantıksal akıl yürütme yeteneğine ve güvenilir görev yerine getirme yeteneğine sahiptir."}, "infiniai": {"description": "Uygulama geliştiricilere yüksek performanslı, kullanımı kolay ve güvenilir büyük model hizmetleri sunar. Büyük model geliştirme den hizmetleştirmeye kadar tüm süreçleri kapsar."}, "internlm": {"description": "Büyük model a<PERSON><PERSON><PERSON><PERSON><PERSON> ve geliştirme araç zincirine adanmış bir açık kaynak organizasyonu. Tüm AI geliştiricilerine verimli ve kullanımı kolay bir açık kaynak platformu sunarak en son büyük model ve algoritma teknolojilerine erişimi kolaylaştırır."}, "jina": {"description": "Jina AI, 2020 y<PERSON><PERSON><PERSON><PERSON>, önde gelen bir arama AI şirketidir. Arama tabanlı platformumuz, işletmelerin güvenilir ve yüksek kaliteli üretken AI ve çok modlu arama uygulamaları geliştirmelerine yardımcı olan vektör modelleri, yeniden sıralayıcılar ve küçük dil modelleri içerir."}, "lmstudio": {"description": "LM Studio, bilgisayarınızda LLM'ler geliştirmek ve denemeler yapmak için bir masaüstü uygulamasıdır."}, "minimax": {"description": "MiniMax, 2021 yılında kurulan genel yapay zeka teknolojisi şirketidir ve kullanıcılarla birlikte akıllı çözümler yaratmayı hedeflemektedir. MiniMax, farklı modlarda genel büyük modeller geliştirmiştir. Bunlar arasında trilyon parametreli MoE metin büyük modeli, ses büyük modeli ve görüntü büyük modeli bulunmaktadır. Ayr<PERSON><PERSON>, Conch AI gibi uygulamalar da sunmaktadır."}, "mistral": {"description": "<PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON>, çok dilli gö<PERSON>, kod üretimi gibi alanlarda geniş bir uygulama yelpazesine sahip gelişmiş genel, profesyonel ve araştırma modelleri sunmaktadır. Fonksiyon çağrısı arayüzü aracılığıyla kullanıcılar, özel işlevleri entegre ederek belirli uygulamalar gerçekleştirebilirler."}, "modelscope": {"description": "ModelScope, Alibaba Cloud tarafından sunulan bir model hizmet platformudur ve zengin AI modelleri ile çıkarım hizmetleri sağlar."}, "moonshot": {"description": "Moonshot, Beijing Yuezhi Anmian Technology Co., Ltd. tarafından sunulan açık kaynaklı bir platformdur. İçerik oluşturma, akademik araştırma, ak<PERSON><PERSON><PERSON> öneri, tıbbi teşhis gibi geniş bir uygulama alanına sahip çeşitli doğal dil işleme modelleri sunmaktadır. Uzun metin işleme ve karmaşık üretim görevlerini desteklemektedir."}, "novita": {"description": "Novita AI, çeşitli büyük dil modelleri ve yapay zeka görüntü üretimi API hizmetleri sunan bir platformdur. Esnek, güvenilir ve maliyet etkin bir yapıya sahiptir. Llama3, Mistral gibi en son açık kaynak modelleri desteklemekte ve üretken yapay zeka uygulama geliştirme için kapsamlı, kullanıcı dostu ve otomatik ölçeklenebilir API çözümleri sunmaktadır. Bu, yapay zeka girişimlerinin hızlı gelişimi için uygundur."}, "nvidia": {"description": "NVIDIA NIM™, b<PERSON><PERSON>, veri me<PERSON>, RTX™ AI kişisel bilgisayarlar ve iş istasyonlarında önceden eğitilmiş ve özelleştirilmiş AI modellerinin dağıtımını destekleyen, kendi kendine barındırılan GPU hızlandırmalı çıkarım mikro hizmetleri için konteynerler sunar."}, "ollama": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modeller, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, mate<PERSON><PERSON><PERSON>, çok dilli işleme ve diyalog etkileşimi gibi alanları kapsamaktadır. Kurumsal düzeyde ve yerelleştirilmiş dağıtım için çeşitli ihtiyaçları desteklemektedir."}, "openai": {"description": "OpenAI, dünya çapında lider bir yapay zeka araştırma kuruluşudur. Geliştirdiği modeller, GPT serisi gibi, do<PERSON><PERSON> dil işleme alanında öncü adımlar atmaktadır. OpenAI, yenilikçi ve etkili yapay zeka çözümleri ile birçok sektörü dönüştürmeyi hedeflemektedir. Ürünleri, belirgin performans ve maliyet etkinliği ile araştırma, ticaret ve yenilikçi uygulamalarda yaygın olarak kullanılmaktadır."}, "openrouter": {"description": "OpenRouter, OpenAI, Anthropic, LLaMA ve daha fazlasını destekleyen çeşitli öncü büyük model a<PERSON><PERSON><PERSON><PERSON> sunan bir hizmet platformudur. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ihtiyaçlarına göre en uygun modeli ve fiyatı esnek bir şekilde seçerek yapay zeka deneyimlerini geliştirebilirler."}, "perplexity": {"description": "Perplexity, çeşitli gelişmiş Llama 3.1 modelleri sunan önde gelen bir diyalog üretim modeli sağlayıcısıdır. Hem çevrimiçi hem de çevrimdışı uygulamaları desteklemekte olup, özellikle karmaşık doğal dil işleme görevleri için uygundur."}, "ppio": {"description": "PPIO Paiou Cloud, istikrarlı ve yüksek maliyet etkinliğe sahip açık kaynak model API hizmeti sunar, DeepSeek'in tüm serisi, <PERSON><PERSON><PERSON>, Qwen gibi sektörün önde gelen büyük modellerini destekler."}, "qiniu": {"description": "<PERSON><PERSON>, esnek seçeneklerle büyük model API hizmetleri sunan lider bulut hizmeti sağlayıcıs<PERSON>ır, DeepSeek, Llama ve Qwen gibi sektörün önde gelen büyük modellerini destekler."}, "qwen": {"description": "<PERSON><PERSON>, Alibaba Cloud tarafından geliştirilen büyük ölçekli bir dil modelidir ve güçlü doğal dil anlama ve üretme yeteneklerine sahiptir. Çeşitli soruları yanıtlayabilir, metin içeriği oluşturabilir, görüşlerini ifade edebilir ve kod yazabilir. Birçok alanda etkili bir şekilde kullanılmaktadır."}, "sambanova": {"description": "SambaNova Cloud, geliştiricilerin en iyi açık kaynak modellerini kolayca kullanmalarını ve en hızlı çıkarım hızından yararlanmalarını sağlar."}, "search1api": {"description": "Search1API, ihtiyaç duyduğunuzda çevrimiçi olabilen DeepSeek serisi modellere eri<PERSON><PERSON>; standart ve hızlı sürümler dahil olmak üzere çeşitli parametre ölçeklerinde model seçimini destekler."}, "sensenova": {"description": "SenseTime, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON> ve kullanımı kolay tam yığın büyük model hiz<PERSON><PERSON><PERSON> sunar."}, "siliconcloud": {"description": "SiliconFlow, insanlığa fayda sağlamak amacıyla AGI'yi hızlandırmaya odaklanmakta ve kullanıcı dostu ve maliyet etkin GenAI yığınları ile büyük ölçekli yapay zeka verimliliğini artırmayı hedeflemektedir."}, "spark": {"description": "iFlytek'in Xinghuo bü<PERSON>ük modeli, çok alanlı ve çok dilli güçlü yapay zeka yetenekleri sunmaktadır. Gelişmiş doğal dil işleme teknolojisini kull<PERSON>, ak<PERSON><PERSON><PERSON>, ak<PERSON><PERSON><PERSON>, akıllı finans gibi çeşitli dikey senaryolar için yenilikçi uygulamalar geliştirmektedir."}, "stepfun": {"description": "Step<PERSON>un büyük modeli, sektördeki lider çok modlu ve karmaşık akıl yürütme yeteneklerine sahiptir. <PERSON><PERSON>n metin anlama ve güçlü kendi kendine yönlendirme arama motoru işlevlerini desteklemektedir."}, "taichu": {"description": "<PERSON>in Bilimler Akademisi Otomasyon Araştırma Enstitüsü ve Wuhan Yapay Zeka Araştırma Enstitüsü, çok modlu büyük modelin yeni neslini sunmaktadır. <PERSON>oklu soru-cevap, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 3D anlama, sinyal analizi gibi kapsamlı soru-cevap görevlerini desteklemekte ve daha güçlü bi<PERSON>, anlama ve yaratma yetenekleri sunarak yeni bir etkileşim deneyimi sağlamaktadır."}, "tencentcloud": {"description": "Bilgi motoru atomik yetenekleri (LLM Knowledge Engine Atomic Power), bilgi motoru üzerine geliştirilmiş bilgi sorgulama tam zincir yetenekleri sunar. <PERSON><PERSON> yetenekler, işletmeler ve geliştiriciler için esnek model uygulamaları oluşturma ve geliştirme imkanı sağlar. Birden fazla atomik yeteneği kullanarak özel model hizmetlerinizi oluşturabilir, belge analizi, par<PERSON>lama, embedding, çoklu yeniden yazım gibi hizmetleri bir araya getirerek işletmenize özel AI çözümleri tasarlayabilirsiniz."}, "togetherai": {"description": "Together AI, yenilikçi yapay zeka modelleri aracılığıyla lider performans elde etmeye odaklanmaktadır. Hızlı ölçeklenme desteği ve sezgisel dağıtım süreçleri dahil olmak üzere geniş özelleştirme yetenekleri sunarak işletmelerin çeşitli ihtiyaçlarını karşılamaktadır."}, "upstage": {"description": "Upstage, çeşitli ticari ihtiyaçlar için yapay zeka modelleri geliştirmeye odaklanmaktadır. Solar LLM ve belge AI gibi modeller, insan yapımı genel zeka (AGI) hedeflemektedir. Chat API aracılığıyla basit diyalog ajanları oluşturmakta ve işlev çağrısı, çeviri, gömme ve belirli alan uygulamalarını desteklemektedir."}, "v0": {"description": "v0, e<PERSON><PERSON>ı<PERSON>; sadece do<PERSON>al dilde fikirlerinizi tanımlaman<PERSON>z yet<PERSON>, o da projeniz için kod ve kullanıcı arayüzü (UI) oluşturur"}, "vertexai": {"description": "Google'un Gemini serisi, Google DeepMind tarafından geliştirilen en gelişmiş ve genel amaçlı AI modelleridir. Çok modlu tasarım için özel olarak oluşturulmuş olup, metin, kod, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ses ve video gibi içeriklerin kesintisiz anlaşılması ve işlenmesini destekler. Veri merkezlerinden mobil cihazlara kadar çeşitli ortamlarda kullanılabilir, AI modellerinin verimliliğini ve uygulama kapsamını büyük ölçüde artırır."}, "vllm": {"description": "vLLM, LLM çıkarımı ve hizmetleri için hızlı ve kullanımı kolay bir kütüphanedir."}, "volcengine": {"description": "ByteDance tarafından sunulan büyük model hizmetleri geliştirme platformu, z<PERSON><PERSON>, güvenlik ve rekabetçi fiyatlarla model ç<PERSON><PERSON><PERSON>rma hizmetleri sunar. Ayrıca model ve<PERSON><PERSON>, ince ayar, <PERSON><PERSON><PERSON><PERSON><PERSON>, değerlendirme gibi uçtan uca işlevler sağlar ve AI uygulama geliştirme sürecinizi her yönüyle güvence altına alır."}, "wenxin": {"description": "Kurumsal düzeyde tek duraklı büyük model ve AI yerel uygulama geliştirme ve hizmet platformu, en kapsamlı ve kullanımı kolay üretken yapay zeka modeli geliştirme, uygulama geliştirme için tam süreç araç zinciri sunar."}, "xai": {"description": "xAI, insan bilimsel keşiflerini hızlandırmak için yapay zeka geliştirmeye adanmış bir şirkettir. <PERSON><PERSON><PERSON><PERSON><PERSON>, evrene dair ortak anlayışımızı ilerletmektir."}, "xinference": {"description": "Xorbits Inference (Xinference), çeşitli yapay zeka modellerinin çalıştırılmasını ve entegrasyonunu kolaylaştıran açık kaynaklı bir platformdur. Xinference ile bulutta veya yerel ortamda herhangi bir açık kaynaklı LLM, gömme modeli ve çoklu modal modeli kullanarak çıkarım yapabilir ve güçlü yapay zeka uygulamaları oluşturabilirsiniz."}, "zeroone": {"description": "01.AI, yapay zeka 2.0 çağının yapay zeka teknolojisine odaklanmakta ve 'insan + yapay zeka' yenilik ve uygulamalarını teşvik etmektedir. Son derece güçlü modeller ve gelişmiş yapay zeka teknolojileri kullanarak insan üretkenliğini artırmayı ve teknolojik güçlendirmeyi hedeflemektedir."}, "zhipu": {"description": "<PERSON><PERSON><PERSON>, çok modlu ve dil modellerinin açık platformunu sunmakta, met<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> anlama ve programlama yardımı gibi geniş bir yapay zeka uygulama senaryosunu desteklemektedir."}}