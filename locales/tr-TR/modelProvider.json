{"azure": {"azureApiVersion": {"desc": "Azure'un API versiyonu, YYYY-AA-GG formatına u<PERSON>, [en son versiyonu](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions) kontrol edin", "fetch": "<PERSON><PERSON><PERSON> al", "title": "Azure API Versiyonu"}, "empty": "İlk modeli eklemek için model k<PERSON><PERSON><PERSON><PERSON> girin", "endpoint": {"desc": "Azure <PERSON><PERSON><PERSON><PERSON> kaynağı kontrol ederken, bu <PERSON><PERSON><PERSON> \"Anahtarlar ve uç noktalar\" bölümünde bulabilirsiniz", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API Adresi"}, "modelListPlaceholder": "Dağıttığınız OpenAI modelini seçin veya ekleyin", "title": "Azure OpenAI", "token": {"desc": "Azure <PERSON><PERSON><PERSON>n kaynağı kontrol ederken, bu <PERSON><PERSON><PERSON> \"Anahtarlar ve uç noktalar\" bölümünde bulabilirsiniz. KEY1 veya KEY2 kullanabilirsiniz", "placeholder": "Azure API Key", "title": "API Key"}}, "azureai": {"azureApiVersion": {"desc": "Azure API sürümü, YYYY-AA-GG formatına u<PERSON>ı<PERSON>, [en son sürümü](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions) kontrol edin", "fetch": "<PERSON><PERSON><PERSON> al", "title": "Azure API Sürümü"}, "endpoint": {"desc": "Azure AI proje özetinden Azure AI model çıkarım uç noktasını bulun", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Azure AI Uç Noktası"}, "title": "Azure OpenAI", "token": {"desc": "Azure AI proje özetinden API anahtarını bulun", "placeholder": "Azure Anahtarı", "title": "<PERSON><PERSON><PERSON>"}}, "bedrock": {"accessKeyId": {"desc": "AWS Access Key Id girin", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "AccessKeyId / SecretAccessKey'in do<PERSON><PERSON> giri<PERSON> test edin"}, "region": {"desc": "AWS Bölgesi girin", "placeholder": "AWS Region", "title": "AWS Bölgesi"}, "secretAccessKey": {"desc": "AWS Secret Access Key girin", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "AWS SSO/STS kullanıyorsanız, lütfen AWS Oturum Tokeninizi girin", "placeholder": "AWS Oturum Tokeni", "title": "AWS Oturum Tokeni (isteğe bağlı)"}, "title": "Bedrock", "unlock": {"customRegion": "<PERSON><PERSON>", "customSessionToken": "<PERSON><PERSON>", "description": "AWS AccessKeyId / SecretAccessKey bilgilerinizi girerek oturumu başlatabilirsiniz. Uygulama kimlik doğrulama bilgilerinizi kaydetmez.", "imageGenerationDescription": "AWS AccessKeyId / SecretAccessKey bilgilerinizi girerek üretime başlayabilirsiniz. Uygulama kimlik doğrulama yapılandırmanızı kaydetmez", "title": "Özel Bedrock Kimlik Bilgilerini Kullan"}}, "cloudflare": {"apiKey": {"desc": "Lütfen doldurun Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Cloudflare hesabınızın ID'sini veya özel API adresinizi girin", "placeholder": "Cloudflare Hesap ID / Özel API Adresi", "title": "Cloudflare Hesap ID / API Adresi"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Lütfen API Anahtarınızı girin", "title": "API Anahtarı"}, "basicTitle": "<PERSON><PERSON>", "configTitle": "Yapılandırma Bilgileri", "confirm": "<PERSON><PERSON>", "createSuccess": "Başarıyla oluşturuldu", "description": {"placeholder": "Hizmet sağlayıcı tanımı (isteğe bağlı)", "title": "Hizmet Sağlayıcı Tanımı"}, "id": {"desc": "Hizmet <PERSON>ıcı<PERSON><PERSON>n ben<PERSON>, oluşturulduktan sonra değiştirilemez", "format": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, tire (-) ve alt çizgi (_) içerebilir", "placeholder": "Küçük harflerle yazılması önerilir, <PERSON><PERSON><PERSON><PERSON>, oluşturduktan sonra değiştirilemez", "required": "Lütfen hizmet sağlayıcı ID'sini girin", "title": "Hizmet Sağlayıcı ID"}, "logo": {"required": "Lütfen geçerli bir hizmet sağlayıcı logosu yükleyin", "title": "Hizmet Sağlayıcı Logosu"}, "name": {"placeholder": "Lütfen hizmet sağlayıcının gösterim adını girin", "required": "Lütfen hizmet sağlayıcı adını girin", "title": "Hizmet Sağlayıcı Adı"}, "proxyUrl": {"required": "Lütfen proxy adresini girin", "title": "Proxy <PERSON>"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Lütfen SDK türünü seçin", "title": "İstek Formatı"}, "title": "Özel AI Hizmet Sağlayıcısı Oluştur"}, "github": {"personalAccessToken": {"desc": "Github PAT'nizi girin, [bura<PERSON>](https://github.com/settings/tokens) tıklayarak oluşturun", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "HuggingFace Token'inizi buraya girin, [buraya](https://huggingface.co/settings/tokens) tıklayarak oluşturun", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "list": {"title": {"disabled": "Hizmet sağlayıcı devre dışı", "enabled": "Hizmet sağlayıcı etkin"}}, "menu": {"addCustomProvider": "Özel Hizmet Sağlayıcı Ekle", "all": "Tümü", "list": {"disabled": "Devre Dışı", "enabled": "Aktif"}, "notFound": "Arama sonuçları bulunamadı", "searchProviders": "Hizmet sağlayıcıları ara...", "sort": "<PERSON><PERSON>"}, "ollama": {"checker": {"desc": "Proxy adresinin doğ<PERSON> giri<PERSON> test edin", "title": "Bağlantı Kontrolü"}, "customModelName": {"desc": "<PERSON><PERSON> modeller <PERSON><PERSON><PERSON>, birden fazla model <PERSON><PERSON><PERSON> (,) kullan<PERSON>n", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Özel Model Adları"}, "download": {"desc": "<PERSON><PERSON><PERSON> 正在下载该模型，请尽量不要关闭本页面。重新下载时将会中断处继续", "failed": "Model indirme başar<PERSON>s<PERSON>z <PERSON>u, lütfen ağı veya Ollama ayarlarını kontrol edip tekrar deneyin", "remainingTime": "剩余时间", "speed": "下载速度", "title": "正在下载模型 {{model}} "}, "endpoint": {"desc": "http(s):// i<PERSON><PERSON><PERSON><PERSON><PERSON>, yerel olarak belirtilmemişse boş bırakılabilir", "title": "Arayüz Proxy Adresi"}, "title": "Ollama", "unlock": {"cancel": "取消下载", "confirm": "下载", "description": "输入你的 Ollama 模型标签，完成即可继续会话", "downloaded": "{{completed}} / {{total}}", "starting": "开始下载...", "title": "下载指定的 Ollama 模型"}}, "providerModels": {"config": {"aesGcm": "Anahtarınız ve proxy adresi gibi bilgiler <1>AES-GCM</1> şifreleme algoritması ile şifrelenecektir", "apiKey": {"desc": "{{name}} API Anahtarınızı girin", "descWithUrl": "Lütfen {{name}} API Anahtarınızı girin, <3>buraya tıklayarak alın</3>", "placeholder": "{{name}} API Anahtarı", "title": "API Anahtarı"}, "baseURL": {"desc": "http(s):// içermelidir", "invalid": "Lütfen geçerli bir URL girin", "placeholder": "https://your-proxy-url.com/v1", "title": "API Proxy Adresi"}, "checker": {"button": "Ko<PERSON><PERSON>", "desc": "API Anahtarı ve proxy adresinin doğru girilip <PERSON> test edin", "pass": "<PERSON><PERSON><PERSON> b<PERSON>şarılı", "title": "Bağlantı Kontrolü"}, "fetchOnClient": {"desc": "İstemci istek modu, ta<PERSON><PERSON><PERSON><PERSON><PERSON> oturum isteği ba<PERSON>ır, yanıt hızını artırabilir", "title": "İstemci İstek Modunu <PERSON>"}, "helpDoc": "Yapılandırma K<PERSON>uzu", "responsesApi": {"desc": "OpenAI'nin yeni nesil istek formatı standardını kullanarak düşünce zinciri gibi gelişmiş özelliklerin kilidini açın", "title": "Responses API Standardını Kullan"}, "waitingForMore": "Daha fazla model <1>planlanıyor</1>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>"}, "createNew": {"title": "Özel AI Modeli Oluştur"}, "item": {"config": "<PERSON><PERSON>", "customModelCards": {"addNew": "{{id}} model<PERSON> oluştur ve ekle", "confirmDelete": "Bu özel modeli silmek üzeresiniz, silind<PERSON><PERSON> sonra geri al<PERSON>, lütfen dikkatli o<PERSON>."}, "delete": {"confirm": "{{displayName}} modelini silmek istediğinize emin misiniz?", "success": "<PERSON><PERSON><PERSON> i<PERSON>lem<PERSON> başarılı", "title": "<PERSON><PERSON>"}, "modelConfig": {"azureDeployName": {"extra": "Azure OpenAI'de gerçek istek için alan", "placeholder": "Lütfen Azure'daki model da<PERSON><PERSON><PERSON><PERSON><PERSON> adını girin", "title": "Model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deployName": {"extra": "<PERSON><PERSON> <PERSON><PERSON>, is<PERSON><PERSON><PERSON> model k<PERSON><PERSON><PERSON><PERSON> o<PERSON> k<PERSON>anılacaktır", "placeholder": "<PERSON>in gerçek dağıtım adını veya kimliğini girin", "title": "Model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "displayName": {"placeholder": "Lütfen modelin gösterim adı<PERSON> girin, <PERSON><PERSON><PERSON><PERSON>tGP<PERSON>, GPT-4 vb.", "title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "files": {"extra": "Mevcut dosya yükleme uygulaması yalnızca bir Hack çözümüdür, yalnızca denemek için geçerlidir. Tam dosya yükleme yeteneği için lütfen sonraki uygulamayı bekleyin.", "title": "<PERSON><PERSON><PERSON>"}, "functionCall": {"extra": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, modelin araçları kullanma yeteneğini açacak ve böylece modele araç sınıfı eklentileri eklenebilecektir. Ancak, gerçek araç kullanımı tamamen modele bağlıdır, kullanılabilirliğini kendiniz test etmelisiniz.", "title": "<PERSON><PERSON> k<PERSON>anımını destekle"}, "id": {"extra": "Oluşturulduktan <PERSON>, AI çağrıldığında model k<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON> kullanılacaktır", "placeholder": "Model k<PERSON><PERSON><PERSON><PERSON> giri<PERSON>, <PERSON><PERSON><PERSON><PERSON> gpt-4o veya claude-3.5-sonnet", "title": "Model ID"}, "modalTitle": "Özel Model Yapılandırması", "reasoning": {"extra": "Bu yapılandırma yalnızca modelin derin düşünme yeteneğini açacaktır, beli<PERSON><PERSON> etkiler tamamen modelin kendisine bağlıdır, lütfen bu modelin kullanılabilir derin düşünme yeteneğine sahip olup olmadığını kendiniz test edin", "title": "<PERSON><PERSON>"}, "tokens": {"extra": "Modelin desteklediği maksimum Token sayısını ayarlayın", "title": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON> penceresi", "unlimited": "Sınırsız"}, "vision": {"extra": "Bu yapılandırma yalnızca uygulamadaki resim yükleme yapılandırmasını açacaktır, tanıma desteği tamamen modele bağlıdır, lütfen bu modelin görsel tanıma yeteneğini test edin.", "title": "Görsel Tanımayı Destekle"}}, "pricing": {"image": "${{amount}}/Resim", "inputCharts": "${{amount}}/<PERSON>", "inputMinutes": "${{amount}}/Dakika", "inputTokens": "Girdi ${{amount}}/M", "outputTokens": "Çıktı ${{amount}}/M"}, "releasedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi: {{releasedAt}}"}, "list": {"addNew": "<PERSON>", "disabled": "<PERSON><PERSON> dı<PERSON>ı", "disabledActions": {"showMore": "<PERSON><PERSON><PERSON>"}, "empty": {"desc": "Lütfen özel bir model <PERSON>luşturun veya kullanmaya başlamadan önce bir model <PERSON><PERSON><PERSON>", "title": "Kullanılabilir model yok"}, "enabled": "<PERSON><PERSON><PERSON>", "enabledActions": {"disableAll": "Hepsini devre dışı bırak", "enableAll": "<PERSON><PERSON><PERSON>", "sort": "Özel model sıralaması"}, "enabledEmpty": "Etkin model yok, lüt<PERSON> aşağıdaki listeden beğendiğiniz modeli etkinleştirin~", "fetcher": {"clear": "Alınan modelleri temizle", "fetch": "Model listesini al", "fetching": "Model listesi alınıyor...", "latestTime": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>: {{time}}", "noLatestTime": "Henüz liste alınmadı"}, "resetAll": {"conform": "Mevcut modelin tüm değişikliklerini sıfırlamak istediğinize emin misiniz? Sıfırladıktan sonra mevcut model list<PERSON> varsayılan duruma dönecektir.", "success": "Sıfırlama başarılı", "title": "<PERSON><PERSON>m değişiklikleri sıfırla"}, "search": "Model ara...", "searchResult": "{{count}} model bulundu", "title": "Model Listesi", "total": "Toplam {{count}} adet model mevcut"}, "searchNotFound": "Arama sonuçları bulunamadı"}, "sortModal": {"success": "Sıralama güncellemesi başarılı", "title": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>"}, "updateAiProvider": {"confirmDelete": "Bu AI hizmet sağlayıcısını silmek üzeresiniz, silind<PERSON>ten sonra geri alı<PERSON>, silmek istediğinize emin misiniz?", "deleteSuccess": "<PERSON><PERSON><PERSON> i<PERSON>lem<PERSON> başarılı", "tooltip": "Hizmet sağlayıcının temel yapılandırmasını güncelle", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarılı"}, "updateCustomAiProvider": {"title": "Özel AI Sağlayıcı Yapılandırmasını Güncelle"}, "vertexai": {"apiKey": {"desc": "Vertex AI Anahtarlarınızı buraya girin", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Vertex AI Anahtarları"}}, "zeroone": {"title": "01.AI Sıfır Bir"}, "zhipu": {"title": "Zeka Haritası"}}