{"01-ai/yi-1.5-34b-chat": {"description": "Zero One Everything، أحدث نموذج مفتوح المصدر تم تعديله، يحتوي على 34 مليار معلمة، ويدعم تعديلات متعددة لمشاهد الحوار، مع بيانات تدريب عالية الجودة تتماشى مع تفضيلات البشر."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One Everything، أحدث نموذج مفتوح المصدر تم تعديله، يحتوي على 9 مليار معلمة، ويدعم تعديلات متعددة لمشاهد الحوار، مع بيانات تدريب عالية الجودة تتماشى مع تفضيلات البشر."}, "360/deepseek-r1": {"description": "【إصدار 360】DeepSeek-R1 استخدم تقنيات التعلم المعزز على نطاق واسع في مرحلة ما بعد التدريب، مما عزز بشكل كبير من قدرة النموذج على الاستدلال مع وجود بيانات محدودة. في المهام الرياضية، البرمجية، واستدلال اللغة الطبيعية، يقدم أداءً يضاهي النسخة الرسمية OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro كعضو مهم في سلسلة نماذج 360 AI، يلبي احتياجات معالجة النصوص المتنوعة بفعالية، ويدعم فهم النصوص الطويلة والحوار المتعدد الجولات."}, "360gpt-pro-trans": {"description": "نموذج مخصص للترجمة، تم تحسينه بشكل عميق، ويقدم نتائج ترجمة رائدة."}, "360gpt-turbo": {"description": "يوفر 360GPT Turbo قدرات حسابية وحوارية قوية، ويتميز بفهم دلالي ممتاز وكفاءة في التوليد، مما يجعله الحل المثالي للمؤسسات والمطورين كمساعد ذكي."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K يركز على الأمان الدلالي والتوجيه المسؤول، مصمم خصيصًا لتطبيقات تتطلب مستوى عالٍ من الأمان في المحتوى، مما يضمن دقة وموثوقية تجربة المستخدم."}, "360gpt2-o1": {"description": "يستخدم 360gpt2-o1 البحث الشجري لبناء سلسلة التفكير، ويقدم آلية للتفكير العميق، ويستخدم التعلم المعزز للتدريب، مما يمنح النموذج القدرة على التفكير الذاتي وتصحيح الأخطاء."}, "360gpt2-pro": {"description": "360GPT2 Pro هو نموذج متقدم لمعالجة اللغة الطبيعية تم إطلاقه من قبل شركة 360، يتمتع بقدرات استثنائية في توليد وفهم النصوص، خاصة في مجالات التوليد والإبداع، ويستطيع التعامل مع مهام تحويل اللغة المعقدة وأداء الأدوار."}, "360zhinao2-o1": {"description": "يستخدم 360zhinao2-o1 البحث الشجري لبناء سلسلة التفكير، ويقدم آلية للتفكير النقدي، ويستخدم التعلم المعزز للتدريب، مما يمنح النموذج القدرة على التفكير الذاتي وتصحيح الأخطاء."}, "4.0Ultra": {"description": "Spark4.0 Ultra هو أقوى إصدار في سلسلة نماذج Spark، حيث يعزز فهم النصوص وقدرات التلخيص مع تحسين روابط البحث عبر الإنترنت. إنه حل شامل يهدف إلى تعزيز إنتاجية المكتب والاستجابة الدقيقة للاحتياجات، ويعتبر منتجًا ذكيًا رائدًا في الصناعة."}, "AnimeSharp": {"description": "AnimeSharp (المعروف أيضًا باسم \"4x‑AnimeSharp\") هو نموذج مفتوح المصدر للتكبير الفائق الدقة طوره Kim2091 استنادًا إلى بنية ESRGAN، يركز على تكبير وتوضيح الصور بأسلوب الأنمي. تم إعادة تسميته في فبراير 2022 من \"4x-TextSharpV1\"، وكان في الأصل مناسبًا أيضًا لصور النصوص لكنه تم تحسين أداؤه بشكل كبير لمحتوى الأنمي."}, "Baichuan2-Turbo": {"description": "يستخدم تقنية تعزيز البحث لتحقيق الربط الشامل بين النموذج الكبير والمعرفة الميدانية والمعرفة من جميع أنحاء الشبكة. يدعم تحميل مستندات PDF وWord وغيرها من المدخلات، مما يضمن الحصول على المعلومات بشكل سريع وشامل، ويقدم نتائج دقيقة واحترافية."}, "Baichuan3-Turbo": {"description": "تم تحسينه لمشاهد الاستخدام المتكررة في الشركات، مما أدى إلى تحسين كبير في الأداء وتكلفة فعالة. مقارنةً بنموذج Baichuan2، زادت قدرة الإبداع بنسبة 20%، وزادت قدرة الإجابة على الأسئلة المعرفية بنسبة 17%، وزادت قدرة التمثيل بنسبة 40%. الأداء العام أفضل من GPT3.5."}, "Baichuan3-Turbo-128k": {"description": "يمتلك نافذة سياق طويلة جدًا تصل إلى 128K، تم تحسينه لمشاهد الاستخدام المتكررة في الشركات، مما أدى إلى تحسين كبير في الأداء وتكلفة فعالة. مقارنةً بنموذج Baichuan2، زادت قدرة الإبداع بنسبة 20%، وزادت قدرة الإجابة على الأسئلة المعرفية بنسبة 17%، وزادت قدرة التمثيل بنسبة 40%. الأداء العام أفضل من GPT3.5."}, "Baichuan4": {"description": "النموذج الأول في البلاد من حيث القدرة، يتفوق على النماذج الرئيسية الأجنبية في المهام الصينية مثل الموسوعات، والنصوص الطويلة، والإبداع. كما يتمتع بقدرات متعددة الوسائط رائدة في الصناعة، ويظهر أداءً ممتازًا في العديد من معايير التقييم الموثوقة."}, "Baichuan4-Air": {"description": "النموذج الأول محليًا، يتفوق على النماذج الرئيسية الأجنبية في المهام الصينية مثل المعرفة الموسوعية، النصوص الطويلة، والإبداع. كما يتمتع بقدرات متعددة الوسائط الرائدة في الصناعة، ويظهر أداءً ممتازًا في العديد من معايير التقييم الموثوقة."}, "Baichuan4-Turbo": {"description": "النموذج الأول محليًا، يتفوق على النماذج الرئيسية الأجنبية في المهام الصينية مثل المعرفة الموسوعية، النصوص الطويلة، والإبداع. كما يتمتع بقدرات متعددة الوسائط الرائدة في الصناعة، ويظهر أداءً ممتازًا في العديد من معايير التقييم الموثوقة."}, "DeepSeek-R1": {"description": "نموذج LLM المتقدم والفعال، بارع في الاستدلال والرياضيات والبرمجة."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 - النموذج الأكبر والأذكى في مجموعة DeepSeek - تم تقطيره إلى هيكل Llama 70B. بناءً على اختبارات الأداء والتقييمات البشرية، فإن هذا النموذج أكثر ذكاءً من Llama 70B الأصلي، خاصة في المهام التي تتطلب الدقة الرياضية والحقائق."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "نموذج التقطير DeepSeek-R1 المستند إلى Qwen2.5-Math-1.5B، تم تحسين أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "نموذج التقطير DeepSeek-R1 المستند إلى Qwen2.5-14B، تم تحسين أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "تسلسل DeepSeek-R1 يحسن أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة، متجاوزًا مستوى OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "نموذج التقطير DeepSeek-R1 المستند إلى Qwen2.5-Math-7B، تم تحسين أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة."}, "DeepSeek-V3": {"description": "DeepSeek-V3 هو نموذج MoE تم تطويره ذاتيًا بواسطة شركة DeepSeek. حقق DeepSeek-V3 نتائج تقييم تفوقت على نماذج مفتوحة المصدر الأخرى مثل Qwen2.5-72B و Llama-3.1-405B، وفي الأداء ينافس النماذج المغلقة الرائدة عالميًا مثل GPT-4o و Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite يتميز بسرعة استجابة فائقة وقيمة أفضل مقابل المال، ويوفر خيارات أكثر مرونة للعملاء في سيناريوهات مختلفة. يدعم الاستدلال والتخصيص مع نافذة سياق 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite يتميز بسرعة استجابة فائقة وقيمة أفضل مقابل المال، ويوفر خيارات أكثر مرونة للعملاء في سيناريوهات مختلفة. يدعم الاستدلال والتخصيص مع نافذة سياق 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite يتميز بسرعة استجابة فائقة وقيمة أفضل مقابل المال، ويوفر خيارات أكثر مرونة للعملاء في سيناريوهات مختلفة. يدعم الاستدلال والتخصيص مع نافذة سياق 4k."}, "Doubao-pro-128k": {"description": "النموذج الرئيسي الأكثر فعالية، مناسب لمعالجة المهام المعقدة، ويحقق أداءً ممتازًا في سيناريوهات مثل الأسئلة المرجعية، التلخيص، الإبداع، تصنيف النصوص، ولعب الأدوار. يدعم الاستدلال والتخصيص مع نافذة سياق 128k."}, "Doubao-pro-32k": {"description": "النموذج الرئيسي الأكثر فعالية، مناسب لمعالجة المهام المعقدة، ويحقق أداءً ممتازًا في سيناريوهات مثل الأسئلة المرجعية، التلخيص، الإبداع، تصنيف النصوص، ولعب الأدوار. يدعم الاستدلال والتخصيص مع نافذة سياق 32k."}, "Doubao-pro-4k": {"description": "النموذج الرئيسي الأكثر فعالية، مناسب لمعالجة المهام المعقدة، ويحقق أداءً ممتازًا في سيناريوهات مثل الأسئلة المرجعية، التلخيص، الإبداع، تصنيف النصوص، ولعب الأدوار. يدعم الاستدلال والتخصيص مع نافذة سياق 4k."}, "DreamO": {"description": "DreamO هو نموذج توليد صور مخصص مفتوح المصدر تم تطويره بالتعاون بين ByteDance وجامعة بكين، يهدف إلى دعم مهام توليد الصور المتعددة من خلال بنية موحدة. يستخدم طريقة نمذجة مركبة فعالة لتوليد صور متسقة ومخصصة بناءً على شروط متعددة مثل الهوية، الموضوع، الأسلوب، والخلفية التي يحددها المستخدم."}, "ERNIE-3.5-128K": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يغطي كمية هائلة من البيانات باللغة الصينية والإنجليزية، ويتميز بقدرات عامة قوية، يمكنه تلبية معظم متطلبات الحوار، والإجابة على الأسئلة، وإنشاء المحتوى، وتطبيقات الإضافات؛ يدعم الاتصال التلقائي بإضافات بحث بايدو، مما يضمن تحديث معلومات الإجابة."}, "ERNIE-3.5-8K": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يغطي كمية هائلة من البيانات باللغة الصينية والإنجليزية، ويتميز بقدرات عامة قوية، يمكنه تلبية معظم متطلبات الحوار، والإجابة على الأسئلة، وإنشاء المحتوى، وتطبيقات الإضافات؛ يدعم الاتصال التلقائي بإضافات بحث بايدو، مما يضمن تحديث معلومات الإجابة."}, "ERNIE-3.5-8K-Preview": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يغطي كمية هائلة من البيانات باللغة الصينية والإنجليزية، ويتميز بقدرات عامة قوية، يمكنه تلبية معظم متطلبات الحوار، والإجابة على الأسئلة، وإنشاء المحتوى، وتطبيقات الإضافات؛ يدعم الاتصال التلقائي بإضافات بحث بايدو، مما يضمن تحديث معلومات الإجابة."}, "ERNIE-4.0-8K-Latest": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي شهد ترقية شاملة في القدرات مقارنةً بـERNIE 3.5، ويستخدم على نطاق واسع في مجالات متعددة لمهام معقدة؛ يدعم الاتصال التلقائي بإضافات بحث بايدو لضمان تحديث معلومات الإجابة."}, "ERNIE-4.0-8K-Preview": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي شهد ترقية شاملة في القدرات مقارنةً بـERNIE 3.5، ويستخدم على نطاق واسع في مجالات متعددة لمهام معقدة؛ يدعم الاتصال التلقائي بإضافات بحث بايدو لضمان تحديث معلومات الإجابة."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي يظهر أداءً ممتازًا في مجالات متعددة، مما يجعله مناسبًا لمجموعة واسعة من المهام المعقدة؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الأسئلة والأجوبة. مقارنة بـ ERNIE 4.0، يظهر أداءً أفضل."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يتميز بأداء شامل ممتاز، ويستخدم على نطاق واسع في مجالات متعددة لمهام معقدة؛ يدعم الاتصال التلقائي بإضافات بحث بايدو لضمان تحديث معلومات الإجابة. مقارنةً بـERNIE 4.0، يتمتع بأداء أفضل."}, "ERNIE-Character-8K": {"description": "نموذج اللغة الكبير الذي طورته بايدو، مناسب لمشاهد الألعاب، والحوار مع خدمة العملاء، وأدوار الحوار، وغيرها من تطبيقات السيناريوهات، حيث يتميز بأسلوب شخصيات واضح ومتسق، وقدرة قوية على اتباع التعليمات، وأداء استدلال أفضل."}, "ERNIE-Lite-Pro-128K": {"description": "نموذج اللغة الخفيف الذي طورته بايدو، يجمع بين أداء النموذج الممتاز وأداء الاستدلال، ويتميز بأداء أفضل من ERNIE Lite، مناسب للاستخدام في بطاقات تسريع الذكاء الاصطناعي ذات القدرة الحاسوبية المنخفضة."}, "ERNIE-Speed-128K": {"description": "نموذج اللغة الكبير عالي الأداء الذي طورته بايدو، والذي تم إصداره في عام 2024، يتمتع بقدرات عامة ممتازة، مناسب كنموذج أساسي للتعديل الدقيق، مما يساعد على معالجة مشكلات السيناريوهات المحددة بشكل أفضل، مع أداء استدلال ممتاز."}, "ERNIE-Speed-Pro-128K": {"description": "نموذج اللغة الكبير عالي الأداء الذي طورته بايدو، والذي تم إصداره في عام 2024، يتمتع بقدرات عامة ممتازة، ويتميز بأداء أفضل من ERNIE Speed، مناسب كنموذج أساسي للتعديل الدقيق، مما يساعد على معالجة مشكلات السيناريوهات المحددة بشكل أفضل، مع أداء استدلال ممتاز."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev هو نموذج متعدد الوسائط لتوليد وتحرير الصور طورته Black Forest Labs، يعتمد على بنية Rectified Flow Transformer ويحتوي على 12 مليار معلمة، يركز على توليد وإعادة بناء وتعزيز أو تحرير الصور بناءً على شروط سياقية محددة. يجمع النموذج بين مزايا التوليد القابل للتحكم في نماذج الانتشار وقدرات نمذجة السياق في Transformer، ويدعم إخراج صور عالية الجودة، ويستخدم على نطاق واسع في إصلاح الصور، إكمال الصور، وإعادة بناء المشاهد البصرية."}, "FLUX.1-dev": {"description": "FLUX.1-dev هو نموذج لغة متعدد الوسائط مفتوح المصدر طورته Black Forest Labs، مُحسّن لمهام النص والصورة، يدمج قدرات فهم وتوليد الصور والنصوص. يعتمد على نماذج اللغة الكبيرة المتقدمة مثل Mistral-7B، ويحقق معالجة متزامنة للنص والصورة واستدلالًا معقدًا من خلال مشفر بصري مصمم بعناية وضبط دقيق متعدد المراحل."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) هو نموذج مبتكر، مناسب لتطبيقات متعددة المجالات والمهام المعقدة."}, "HelloMeme": {"description": "HelloMeme هو أداة ذكاء اصطناعي يمكنها تلقائيًا إنشاء ملصقات تعبيرية، صور متحركة أو مقاطع فيديو قصيرة بناءً على الصور أو الحركات التي تقدمها. لا تحتاج إلى مهارات رسم أو برمجة، فقط قدم صورة مرجعية، وستساعدك في إنشاء محتوى جميل، ممتع ومتناسق في الأسلوب."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full هو نموذج تحرير صور متعدد الوسائط مفتوح المصدر أطلقته HiDream.ai، يعتمد على بنية Diffusion Transformer المتقدمة، ويجمع بين قدرات فهم اللغة القوية (مضمن LLaMA 3.1-8B-Instruct)، يدعم توليد الصور، نقل الأسلوب، التحرير الجزئي وإعادة رسم المحتوى عبر أوامر اللغة الطبيعية، ويتميز بفهم وتنفيذ ممتاز للنص والصورة."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled هو نموذج توليد صور نصية خفيف الوزن، محسن بالتقطير، قادر على توليد صور عالية الجودة بسرعة، ومناسب بشكل خاص للبيئات ذات الموارد المحدودة والمهام التي تتطلب توليدًا فوريًا."}, "InstantCharacter": {"description": "InstantCharacter هو نموذج توليد شخصيات مخصص بدون ضبط دقيق أصدره فريق Tencent AI في 2025، يهدف إلى تحقيق توليد شخصيات متسقة وعالية الدقة عبر مشاهد مختلفة. يدعم بناء نموذج الشخصية استنادًا إلى صورة مرجعية واحدة فقط، ويمكن نقل الشخصية بمرونة إلى أنماط، حركات وخلفيات متنوعة."}, "InternVL2-8B": {"description": "InternVL2-8B هو نموذج قوي للغة البصرية، يدعم المعالجة متعددة الوسائط للصورة والنص، قادر على التعرف بدقة على محتوى الصورة وتوليد أوصاف أو إجابات ذات صلة."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B هو نموذج قوي للغة البصرية، يدعم المعالجة متعددة الوسائط للصورة والنص، قادر على التعرف بدقة على محتوى الصورة وتوليد أوصاف أو إجابات ذات صلة."}, "Kolors": {"description": "Kolors هو نموذج توليد صور نصية طوره فريق Kolors في Kuaishou. تم تدريبه على مليارات المعلمات، ويتميز بجودة بصرية عالية، وفهم دقيق للغة الصينية، وقدرة ممتازة على عرض النصوص."}, "Kwai-Kolors/Kolors": {"description": "Kolors هو نموذج توليد صور نصية واسع النطاق يعتمد على الانتشار الكامن طوره فريق Kolors في Kuaishou. تم تدريبه على مليارات أزواج نص-صورة، ويظهر تفوقًا ملحوظًا في جودة الصور، دقة الفهم الدلالي المعقد، وعرض الأحرف الصينية والإنجليزية. يدعم الإدخال باللغتين الصينية والإنجليزية، ويبرع في فهم وتوليد المحتوى الخاص باللغة الصينية."}, "Llama-3.2-11B-Vision-Instruct": {"description": "قدرات استدلال الصور الممتازة على الصور عالية الدقة، مناسبة لتطبيقات الفهم البصري."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "قدرات استدلال الصور المتقدمة المناسبة لتطبيقات الوكلاء في الفهم البصري."}, "Meta-Llama-3.1-405B-Instruct": {"description": "نموذج نصي تم تعديله تحت الإشراف من Llama 3.1، تم تحسينه لحالات الحوار متعددة اللغات، حيث يتفوق في العديد من نماذج الدردشة مفتوحة ومغلقة المصدر المتاحة في المعايير الصناعية الشائعة."}, "Meta-Llama-3.1-70B-Instruct": {"description": "نموذج نصي تم تعديله تحت الإشراف من Llama 3.1، تم تحسينه لحالات الحوار متعددة اللغات، حيث يتفوق في العديد من نماذج الدردشة مفتوحة ومغلقة المصدر المتاحة في المعايير الصناعية الشائعة."}, "Meta-Llama-3.1-8B-Instruct": {"description": "نموذج نصي تم تعديله تحت الإشراف من Llama 3.1، تم تحسينه لحالات الحوار متعددة اللغات، حيث يتفوق في العديد من نماذج الدردشة مفتوحة ومغلقة المصدر المتاحة في المعايير الصناعية الشائعة."}, "Meta-Llama-3.2-1B-Instruct": {"description": "نموذج لغوي صغير متقدم وحديث، يتمتع بفهم اللغة وقدرات استدلال ممتازة وقدرة على توليد النصوص."}, "Meta-Llama-3.2-3B-Instruct": {"description": "نموذج لغوي صغير متقدم وحديث، يتمتع بفهم اللغة وقدرات استدلال ممتازة وقدرة على توليد النصوص."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 هو النموذج اللغوي مفتوح المصدر متعدد اللغات الأكثر تقدمًا في سلسلة Llama، حيث يقدم تجربة تنافس أداء نموذج 405B بتكلفة منخفضة للغاية. يعتمد على هيكل Transformer، وتم تحسين فائدته وأمانه من خلال التعديل الدقيق تحت الإشراف (SFT) والتعلم المعزز من خلال ردود الفعل البشرية (RLHF). تم تحسين إصدار التعديل الخاص به ليكون مثاليًا للحوار متعدد اللغات، حيث يتفوق في العديد من المعايير الصناعية على العديد من نماذج الدردشة مفتوحة ومغلقة المصدر. تاريخ انتهاء المعرفة هو ديسمبر 2023."}, "MiniMax-M1": {"description": "نموذج استدلال جديد مطور ذاتيًا. رائد عالميًا: 80 ألف سلسلة تفكير × 1 مليون إدخال، أداء يضاهي أفضل النماذج العالمية."}, "MiniMax-Text-01": {"description": "في سلسلة نماذج MiniMax-01، قمنا بإجراء ابتكارات جريئة: تم تنفيذ آلية الانتباه الخطي على نطاق واسع لأول مرة، لم يعد هيكل Transformer التقليدي هو الخيار الوحيد. يصل عدد معلمات هذا النموذج إلى 456 مليار، مع تنشيط واحد يصل إلى 45.9 مليار. الأداء الشامل للنموذج يتساوى مع النماذج الرائدة في الخارج، بينما يمكنه معالجة سياقات تصل إلى 4 ملايين توكن، وهو 32 مرة من GPT-4o و20 مرة من Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 هو نموذج استدلال كبير الحجم مفتوح المصدر يعتمد على الانتباه المختلط، يحتوي على 456 مليار معلمة، حيث يمكن لكل رمز تفعيل حوالي 45.9 مليار معلمة. يدعم النموذج أصلاً سياقًا فائق الطول يصل إلى مليون رمز، ومن خلال آلية الانتباه السريع، يوفر 75% من العمليات الحسابية العائمة في مهام التوليد التي تصل إلى 100 ألف رمز مقارنة بـ DeepSeek R1. بالإضافة إلى ذلك، يعتمد MiniMax-M1 على بنية MoE (الخبراء المختلطون)، ويجمع بين خوارزمية CISPO وتصميم الانتباه المختلط لتدريب تعلم معزز فعال، محققًا أداءً رائدًا في الصناعة في استدلال الإدخالات الطويلة وسيناريوهات هندسة البرمجيات الحقيقية."}, "Moonshot-Kimi-K2-Instruct": {"description": "يحتوي على 1 تريليون معلمة و32 مليار معلمة مفعلة. من بين النماذج غير المعتمدة على التفكير، يحقق مستويات متقدمة في المعرفة الحديثة، الرياضيات والبرمجة، ويتفوق في مهام الوكيل العامة. تم تحسينه بعناية لمهام الوكيل، لا يجيب فقط على الأسئلة بل يتخذ إجراءات. مثالي للدردشة العفوية، التجارب العامة والوكيل، وهو نموذج سريع الاستجابة لا يتطلب تفكيرًا طويلًا."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) هو نموذج تعليمات عالي الدقة، مناسب للحسابات المعقدة."}, "OmniConsistency": {"description": "يعزز OmniConsistency اتساق الأسلوب والقدرة على التعميم في مهام تحويل الصور إلى صور من خلال إدخال Transformers الانتشارية واسعة النطاق (DiTs) وبيانات نمطية مزدوجة، مما يمنع تدهور الأسلوب."}, "Phi-3-medium-128k-instruct": {"description": "نموذج Phi-3-medium نفسه، ولكن مع حجم سياق أكبر لـ RAG أو التوجيه القليل."}, "Phi-3-medium-4k-instruct": {"description": "نموذج بحجم 14B، يثبت جودة أفضل من Phi-3-mini، مع التركيز على البيانات الكثيفة في التفكير عالية الجودة."}, "Phi-3-mini-128k-instruct": {"description": "نموذج Phi-3-mini نفسه، ولكن مع حجم سياق أكبر لـ RAG أو التوجيه القليل."}, "Phi-3-mini-4k-instruct": {"description": "أصغر عضو في عائلة Phi-3. مُحسّن لكل من الجودة وزمن الاستجابة المنخفض."}, "Phi-3-small-128k-instruct": {"description": "نموذج Phi-3-small نفسه، ولكن مع حجم سياق أكبر لـ RAG أو التوجيه القليل."}, "Phi-3-small-8k-instruct": {"description": "نموذج بحجم 7B، يثبت جودة أفضل من Phi-3-mini، مع التركيز على البيانات الكثيفة في التفكير عالية الجودة."}, "Phi-3.5-mini-instruct": {"description": "النسخة المحدثة من نموذج Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "النسخة المحدثة من نموذج Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct هو نموذج لغوي كبير تم تعديله وفقًا للتعليمات في سلسلة Qwen2، بحجم 7B. يعتمد هذا النموذج على بنية Transformer، ويستخدم تقنيات مثل دالة تنشيط SwiGLU، وتحويل QKV، والانتباه الجماعي. يمكنه معالجة المدخلات الكبيرة. أظهر النموذج أداءً ممتازًا في فهم اللغة، والتوليد، والقدرات متعددة اللغات، والترميز، والرياضيات، والاستدلال في العديد من اختبارات المعايير، متجاوزًا معظم النماذج مفتوحة المصدر."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct هو أحد أحدث نماذج اللغة الكبيرة التي أصدرتها Alibaba Cloud. يتمتع هذا النموذج بقدرات محسنة بشكل ملحوظ في مجالات الترميز والرياضيات. كما يوفر دعمًا للغات متعددة، تغطي أكثر من 29 لغة، بما في ذلك الصينية والإنجليزية. أظهر النموذج تحسينات ملحوظة في اتباع التعليمات، وفهم البيانات الهيكلية، وتوليد المخرجات الهيكلية (خاصة JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct هو أحدث إصدار من سلسلة نماذج اللغة الكبيرة المحددة للشيفرة التي أصدرتها Alibaba Cloud. تم تحسين هذا النموذج بشكل كبير في توليد الشيفرة، والاستدلال، وإصلاح الأخطاء، من خلال تدريب على 55 تريليون توكن."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL هو العضو الجديد في سلسلة Qwen، يتمتع بقدرات فهم بصري قوية، يمكنه تحليل النصوص والرسوم البيانية والتخطيطات في الصور، وفهم مقاطع الفيديو الطويلة واستيعاب الأحداث. بإمكانه القيام بالاستدلال والتعامل مع الأدوات، يدعم تحديد الكائنات متعددة التنسيقات وإنشاء مخرجات منظمة، كما تم تحسين ديناميكية الدقة ومعدل الإطارات في التدريب لفهم الفيديو، مع تعزيز كفاءة مشفر الرؤية."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking هو نموذج لغة بصري مفتوح المصدر (VLM) تم إصداره بشكل مشترك من قبل Zhizhu AI ومختبر KEG بجامعة تسينغهوا، مصمم خصيصًا لمعالجة المهام الإدراكية متعددة الوسائط المعقدة. يعتمد النموذج على النموذج الأساسي GLM-4-9B-0414، ومن خلال إدخال آلية الاستدلال \"سلسلة التفكير\" (Chain-of-Thought) واستخدام استراتيجيات التعلم المعزز، تم تحسين قدرته على الاستدلال عبر الوسائط واستقراره بشكل ملحوظ."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat هو الإصدار مفتوح المصدر من نموذج GLM-4 الذي أطلقته Zhizhu AI. أظهر هذا النموذج أداءً ممتازًا في مجالات الدلالات، والرياضيات، والاستدلال، والشيفرة، والمعرفة. بالإضافة إلى دعم المحادثات متعددة الجولات، يتمتع GLM-4-9B-Chat أيضًا بميزات متقدمة مثل تصفح الويب، وتنفيذ الشيفرة، واستدعاء الأدوات المخصصة (Function Call)، والاستدلال على النصوص الطويلة. يدعم النموذج 26 لغة، بما في ذلك الصينية، والإنجليزية، واليابانية، والكورية، والألمانية. أظهر GLM-4-9B-Chat أداءً ممتازًا في العديد من اختبارات المعايير مثل AlignBench-v2 وMT-Bench وMMLU وC-Eval. يدعم النموذج طول سياق يصل إلى 128K، مما يجعله مناسبًا للأبحاث الأكاديمية والتطبيقات التجارية."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 هو نموذج استدلال مدفوع بالتعلم المعزز (RL)، يعالج مشكلات التكرار وقابلية القراءة في النموذج. قبل التعلم المعزز، أدخل DeepSeek-R1 بيانات بدء التشغيل الباردة، مما أدى إلى تحسين أداء الاستدلال. إنه يتفوق في المهام الرياضية، والبرمجة، والاستدلال مقارنةً بـ OpenAI-o1، وقد حسّن الأداء العام من خلال طرق تدريب مصممة بعناية."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B هو نموذج تم الحصول عليه من خلال تقطير المعرفة بناءً على Qwen2.5-Math-7B. تم ضبط هذا النموذج باستخدام 800 ألف عينة مختارة تم إنشاؤها بواسطة DeepSeek-R1، مما يظهر قدرات استدلالية ممتازة. أظهر أداءً متميزًا في العديد من الاختبارات المعيارية، حيث حقق دقة 92.8٪ في MATH-500، ومعدل نجاح 55.5٪ في AIME 2024، ودرجة 1189 في CodeForces، مما يظهر قدرات قوية في الرياضيات والبرمجة كنموذج بحجم 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 هو نموذج لغوي مختلط الخبراء (MoE) يحتوي على 6710 مليار معلمة، يستخدم الانتباه المتعدد الرؤوس (MLA) وهيكل DeepSeekMoE، ويجمع بين استراتيجيات توازن الحمل بدون خسائر مساعدة، مما يحسن كفاءة الاستدلال والتدريب. تم تدريبه مسبقًا على 14.8 تريليون توكن عالية الجودة، وتم إجراء تعديل دقيق تحت الإشراف والتعلم المعزز، مما يجعل DeepSeek-V3 يتفوق على نماذج مفتوحة المصدر الأخرى، ويقترب من النماذج المغلقة الرائدة."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 هو نموذج أساسي يعتمد على بنية MoE مع قدرات قوية في البرمجة والوكيل، يحتوي على 1 تريليون معلمة و32 مليار معلمة مفعلة. يتفوق نموذج K2 في اختبارات الأداء الأساسية في مجالات المعرفة العامة، البرمجة، الرياضيات والوكيل مقارنة بالنماذج المفتوحة المصدر الأخرى."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview هو نموذج معالجة اللغة الطبيعية المبتكر، قادر على معالجة مهام توليد الحوار وفهم السياق بشكل فعال."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview هو نموذج بحثي طورته فريق Qwen يركز على قدرات الاستدلال البصري، حيث يتمتع بميزة فريدة في فهم المشاهد المعقدة وحل المشكلات الرياضية المتعلقة بالرؤية."}, "Qwen/QwQ-32B": {"description": "QwQ هو نموذج استدلال من سلسلة Qwen. مقارنةً بالنماذج التقليدية المعتمدة على تحسين التعليمات، يتمتع QwQ بقدرة على التفكير والاستدلال، مما يتيح له تحقيق أداء معزز بشكل ملحوظ في المهام اللاحقة، خاصة في حل المشكلات الصعبة. QwQ-32B هو نموذج استدلال متوسط الحجم، قادر على تحقيق أداء تنافسي عند مقارنته بأحدث نماذج الاستدلال (مثل DeepSeek-R1، o1-mini). يستخدم هذا النموذج تقنيات مثل RoPE، SwiGLU، RMSNorm وAttention QKV bias، ويتميز بهيكل شبكة مكون من 64 طبقة و40 رأس انتباه Q (حيث KV في هيكل GQA هو 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview هو أحدث نموذج بحث تجريبي من Qwen، يركز على تعزيز قدرات الاستدلال للذكاء الاصطناعي. من خلال استكشاف آليات معقدة مثل خلط اللغة والاستدلال التكراري، تشمل المزايا الرئيسية القدرة القوية على التحليل الاستدلالي، والقدرات الرياضية والبرمجية. في الوقت نفسه، هناك أيضًا مشكلات في تبديل اللغة، ودورات الاستدلال، واعتبارات الأمان، واختلافات في القدرات الأخرى."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 هو نموذج لغوي عام متقدم، يدعم أنواع متعددة من التعليمات."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct هو نموذج لغوي كبير تم تعديله وفقًا للتعليمات في سلسلة Qwen2، بحجم 72B. يعتمد هذا النموذج على بنية Transformer، ويستخدم تقنيات مثل دالة تنشيط SwiGLU، وتحويل QKV، والانتباه الجماعي. يمكنه معالجة المدخلات الكبيرة. أظهر النموذج أداءً ممتازًا في فهم اللغة، والتوليد، والقدرات متعددة اللغات، والترميز، والرياضيات، والاستدلال في العديد من اختبارات المعايير، متجاوزًا معظم النماذج مفتوحة المصدر."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL هو النسخة الأحدث من نموذج Qwen-VL، وقد حقق أداءً متقدمًا في اختبارات الفهم البصري."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 هو سلسلة جديدة من نماذج اللغة الكبيرة، تهدف إلى تحسين معالجة المهام الإرشادية."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 هو سلسلة جديدة من نماذج اللغة الكبيرة، تهدف إلى تحسين معالجة المهام الإرشادية."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "نموذج لغة كبير تم تطويره بواسطة فريق علي بابا السحابي للذكاء الاصطناعي"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 هي سلسلة جديدة من نماذج اللغة الكبيرة، تتمتع بقدرة أكبر على الفهم والتوليد."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 هو سلسلة جديدة من نماذج اللغة الكبيرة، مصممة لتحسين معالجة المهام التوجيهية."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 هو سلسلة جديدة من نماذج اللغة الكبيرة، تهدف إلى تحسين معالجة المهام الإرشادية."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 هو سلسلة جديدة من نماذج اللغة الكبيرة، مصممة لتحسين معالجة المهام التوجيهية."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "يركز Qwen2.5-Coder على كتابة الكود."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct هو أحدث إصدار من سلسلة نماذج اللغة الكبيرة المحددة للشيفرة التي أصدرتها Alibaba Cloud. تم تحسين هذا النموذج بشكل كبير في توليد الشيفرة، والاستدلال، وإصلاح الأخطاء، من خلال تدريب على 55 تريليون توكن."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct هو نموذج متعدد الوسائط تم تطويره بواسطة فريق Tongyi Qianwen، وهو جزء من سلسلة Qwen2.5-VL. لا يتقن هذا النموذج فقط التعرف على الأشياء الشائعة، بل يمكنه أيضًا تحليل النصوص والرسوم البيانية والرموز والأشكال والتخطيطات في الصور. يعمل كعامل ذكي بصري قادر على التفكير والتعامل الديناميكي مع الأدوات، مع امتلاك القدرة على استخدام الحاسوب والهاتف المحمول. بالإضافة إلى ذلك، يمكن لهذا النموذج تحديد مواقع الكائنات في الصور بدقة وإنتاج مخرجات منظمة للفواتير والجداول وغيرها. مقارنةً بالنموذج السابق Qwen2-VL، فقد تم تحسين هذه النسخة بشكل أكبر في القدرات الرياضية وحل المشكلات من خلال التعلم المعزز، كما أن أسلوب الاستجابة أصبح أكثر توافقًا مع تفضيلات البشر."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL هو نموذج اللغة البصرية في سلسلة Qwen2.5. يتميز هذا النموذج بتحسينات كبيرة في جوانب متعددة: قدرة أقوى على الفهم البصري، مع القدرة على التعرف على الأشياء الشائعة وتحليل النصوص والرسوم البيانية والتخطيطات؛ كوسيط بصري يمكنه التفكير وتوجيه استخدام الأدوات ديناميكيًا؛ يدعم فهم مقاطع الفيديو الطويلة التي تزيد عن ساعة واحدة مع القدرة على التقاط الأحداث الرئيسية؛ يمكنه تحديد موقع الأشياء في الصور بدقة من خلال إنشاء مربعات حدودية أو نقاط؛ يدعم إنشاء مخرجات منظمة، وهو مفيد بشكل خاص للبيانات الممسوحة ضوئيًا مثل الفواتير والجداول."}, "Qwen/Qwen3-14B": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الاستدلال، المهام العامة، الوكلاء، واللغات المتعددة، ويدعم تبديل وضع التفكير."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الاستدلال، المهام العامة، الوكلاء، واللغات المتعددة، ويدعم تبديل وضع التفكير."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 هو نموذج لغة كبير من سلسلة Qwen3 طوره فريق Alibaba Tongyi Qianwen، وهو نموذج خبير مختلط (MoE) رائد. يحتوي على 235 مليار معلمة إجمالية و22 مليار معلمة مفعلة في كل استدلال. تم إصداره كنسخة محدثة من Qwen3-235B-A22B غير التفكير، مع تحسينات كبيرة في اتباع التعليمات، الاستدلال المنطقي، فهم النصوص، الرياضيات، العلوم، البرمجة واستخدام الأدوات. يعزز التغطية المعرفية متعددة اللغات ويدعم التوافق الأفضل مع تفضيلات المستخدم في المهام الذاتية والمفتوحة لتوليد نصوص أكثر فائدة وجودة."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 هو نموذج لغة كبير من سلسلة Qwen3 طوره فريق Alibaba Tongyi Qianwen، يركز على مهام الاستدلال المعقدة عالية الصعوبة. يعتمد على بنية MoE ويحتوي على 235 مليار معلمة إجمالية مع تفعيل حوالي 22 مليار معلمة لكل رمز، مما يحسن الكفاءة الحسابية مع الحفاظ على الأداء القوي. كنموذج \"تفكير\" متخصص، يظهر تحسينات كبيرة في الاستدلال المنطقي، الرياضيات، العلوم، البرمجة والاختبارات الأكاديمية، ويصل إلى مستوى رائد بين نماذج التفكير المفتوحة المصدر. يعزز القدرات العامة مثل اتباع التعليمات، استخدام الأدوات وتوليد النصوص، ويدعم فهم سياق طويل يصل إلى 256 ألف رمز، مما يجعله مناسبًا للمهام التي تتطلب استدلالًا عميقًا ومعالجة مستندات طويلة."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الاستدلال، المهام العامة، الوكلاء، واللغات المتعددة، ويدعم تبديل وضع التفكير."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 هو نسخة محدثة من Qwen3-30B-A3B في وضع عدم التفكير. هذا نموذج خبير مختلط (MoE) يحتوي على 30.5 مليار معلمة إجمالية و3.3 مليار معلمة تنشيط. تم تعزيز النموذج بشكل كبير في عدة جوانب، بما في ذلك تحسين كبير في الالتزام بالتعليمات، والتفكير المنطقي، وفهم النصوص، والرياضيات، والعلوم، والبرمجة، واستخدام الأدوات. كما حقق تقدمًا ملموسًا في تغطية المعرفة متعددة اللغات، ويستطيع التوافق بشكل أفضل مع تفضيلات المستخدم في المهام الذاتية والمفتوحة، مما يمكنه من توليد ردود أكثر فائدة ونصوص ذات جودة أعلى. بالإضافة إلى ذلك، تم تعزيز قدرة النموذج على فهم النصوص الطويلة إلى 256 ألف رمز. هذا النموذج يدعم فقط وضع عدم التفكير، ولن ينتج علامات `<think></think>` في مخرجاته."}, "Qwen/Qwen3-32B": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الاستدلال، المهام العامة، الوكلاء، واللغات المتعددة، ويدعم تبديل وضع التفكير."}, "Qwen/Qwen3-8B": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الاستدلال، المهام العامة، الوكلاء، واللغات المتعددة، ويدعم تبديل وضع التفكير."}, "Qwen2-72B-Instruct": {"description": "Qwen2 هو أحدث سلسلة من نموذج Qwen، ويدعم سياقًا يصل إلى 128 ألف، مقارنةً بأفضل النماذج مفتوحة المصدر الحالية، يتفوق Qwen2-72B بشكل ملحوظ في فهم اللغة الطبيعية والمعرفة والترميز والرياضيات والقدرات متعددة اللغات."}, "Qwen2-7B-Instruct": {"description": "Qwen2 هو أحدث سلسلة من نموذج Qwen، قادر على التفوق على النماذج مفتوحة المصدر ذات الحجم المماثل أو حتى النماذج الأكبر حجمًا، حقق Qwen2 7B مزايا ملحوظة في عدة تقييمات، خاصة في فهم الترميز والصينية."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B هو نموذج قوي للغة البصرية، يدعم المعالجة متعددة الوسائط للصورة والنص، ويستطيع التعرف بدقة على محتوى الصورة وتوليد أوصاف أو إجابات ذات صلة."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct هو نموذج لغوي كبير يحتوي على 14 مليار معلمة، يتميز بأداء ممتاز، تم تحسينه لمشاهد اللغة الصينية واللغات المتعددة، ويدعم التطبيقات مثل الأسئلة الذكية وتوليد المحتوى."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct هو نموذج لغوي كبير يحتوي على 32 مليار معلمة، يتميز بأداء متوازن، تم تحسينه لمشاهد اللغة الصينية واللغات المتعددة، ويدعم التطبيقات مثل الأسئلة الذكية وتوليد المحتوى."}, "Qwen2.5-72B-Instruct": {"description": "يدعم Qwen2.5-72B-Instruct سياقًا يصل إلى 16 ألف، وينتج نصوصًا طويلة تتجاوز 8 آلاف. يدعم استدعاء الوظائف والتفاعل السلس مع الأنظمة الخارجية، مما يعزز بشكل كبير من المرونة وقابلية التوسع. لقد زادت معرفة النموذج بشكل ملحوظ، كما تحسنت قدراته في الترميز والرياضيات بشكل كبير، ويدعم أكثر من 29 لغة."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct هو نموذج لغوي كبير يحتوي على 7 مليارات معلمة، يدعم الاتصال الوظيفي مع الأنظمة الخارجية بسلاسة، مما يعزز المرونة وقابلية التوسع بشكل كبير. تم تحسينه لمشاهد اللغة الصينية واللغات المتعددة، ويدعم التطبيقات مثل الأسئلة الذكية وتوليد المحتوى."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct هو نموذج تعليمات برمجة قائم على تدريب مسبق واسع النطاق، يتمتع بقدرة قوية على فهم وتوليد الشيفرات، مما يجعله فعالاً في معالجة مختلف مهام البرمجة، وخاصة كتابة الشيفرات الذكية، وتوليد السكربتات الآلية، وحل مشكلات البرمجة."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct هو نموذج لغوي كبير مصمم خصيصًا لتوليد الشيفرات، وفهم الشيفرات، ومشاهد التطوير الفعالة، مع استخدام حجم 32B من المعلمات الرائدة في الصناعة، مما يلبي احتياجات البرمجة المتنوعة."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B هو نموذج MoE (نموذج خبير مختلط) يقدم \"وضع الاستدلال المختلط\"، ويدعم المستخدمين في التبديل السلس بين \"وضع التفكير\" و\"وضع عدم التفكير\". يدعم فهم واستدلال 119 لغة ولهجة، ويتميز بقدرات قوية على استدعاء الأدوات. في اختبارات الأداء الشاملة، والبرمجة والرياضيات، واللغات المتعددة، والمعرفة والاستدلال، ينافس هذا النموذج النماذج الرائدة في السوق مثل DeepSeek R1، OpenAI o1، o3-mini، Grok 3، وGoogle Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B هو نموذج كثيف (Dense Model) يقدم \"وضع الاستدلال المختلط\"، ويدعم التبديل السلس بين \"وضع التفكير\" و\"وضع عدم التفكير\". بفضل تحسينات في بنية النموذج، وزيادة بيانات التدريب، وأساليب تدريب أكثر فعالية، يقدم أداءً يعادل تقريبًا Qwen2.5-72B."}, "SenseChat": {"description": "نموذج الإصدار الأساسي (V4)، بطول سياق 4K، يمتلك قدرات قوية وعامة."}, "SenseChat-128K": {"description": "نموذج الإصدار الأساسي (V4)، بطول سياق 128K، يتفوق في مهام فهم وتوليد النصوص الطويلة."}, "SenseChat-32K": {"description": "نموذج الإصدار الأساسي (V4)، بطول سياق 32K، يمكن استخدامه بمرونة في مختلف السيناريوهات."}, "SenseChat-5": {"description": "أحدث إصدار من النموذج (V5.5)، بطول سياق 128K، مع تحسينات ملحوظة في القدرة على الاستدلال الرياضي، المحادثات باللغة الإنجليزية، اتباع التعليمات وفهم النصوص الطويلة، مما يجعله في مستوى GPT-4o."}, "SenseChat-5-1202": {"description": "الإصدار الأحدث المبني على V5.5، يظهر تحسينات ملحوظة في القدرات الأساسية باللغتين الصينية والإنجليزية، الدردشة، المعرفة العلمية، المعرفة الأدبية، الكتابة، المنطق الرياضي، والتحكم في عدد الكلمات."}, "SenseChat-5-Cantonese": {"description": "بطول سياق 32K، يتفوق في فهم المحادثات باللغة الكانتونية مقارنة بـ GPT-4، ويضاهي GPT-4 Turbo في مجالات المعرفة، الاستدلال، الرياضيات وكتابة الأكواد."}, "SenseChat-5-beta": {"description": "بعض الأداء يتفوق على SenseCat-5-1202"}, "SenseChat-Character": {"description": "نموذج النسخة القياسية، بطول سياق 8K، بسرعة استجابة عالية."}, "SenseChat-Character-Pro": {"description": "نموذج النسخة المتقدمة، بطول سياق 32K، مع تحسين شامل في القدرات، يدعم المحادثات باللغة الصينية والإنجليزية."}, "SenseChat-Turbo": {"description": "مناسب للأسئلة السريعة، وسيناريوهات ضبط النموذج."}, "SenseChat-Turbo-1202": {"description": "هو أحدث نموذج خفيف الوزن، يحقق أكثر من 90% من قدرات النموذج الكامل، مما يقلل بشكل ملحوظ من تكلفة الاستدلال."}, "SenseChat-Vision": {"description": "النموذج الأحدث (V5.5) يدعم إدخال صور متعددة، ويحقق تحسينات شاملة في القدرات الأساسية للنموذج، مع تحسينات كبيرة في التعرف على خصائص الكائنات، والعلاقات المكانية، والتعرف على الأحداث، وفهم المشاهد، والتعرف على المشاعر، واستنتاج المعرفة المنطقية، وفهم النصوص وتوليدها."}, "SenseNova-V6-5-Pro": {"description": "من خلال تحديث شامل للبيانات متعددة الوسائط، واللغوية، والاستدلالية، وتحسين استراتيجيات التدريب، حقق النموذج الجديد تحسينات ملحوظة في الاستدلال متعدد الوسائط وقدرة متابعة التعليمات العامة، ويدعم نافذة سياق تصل إلى 128 ألف رمز، ويظهر أداءً متميزًا في مهام متخصصة مثل التعرف الضوئي على الحروف (OCR) والتعرف على حقوق الملكية الفكرية في السياحة والثقافة."}, "SenseNova-V6-5-Turbo": {"description": "من خلال تحديث شامل للبيانات متعددة الوسائط، واللغوية، والاستدلالية، وتحسين استراتيجيات التدريب، حقق النموذج الجديد تحسينات ملحوظة في الاستدلال متعدد الوسائط وقدرة متابعة التعليمات العامة، ويدعم نافذة سياق تصل إلى 128 ألف رمز، ويظهر أداءً متميزًا في مهام متخصصة مثل التعرف الضوئي على الحروف (OCR) والتعرف على حقوق الملكية الفكرية في السياحة والثقافة."}, "SenseNova-V6-Pro": {"description": "تحقيق توحيد أصلي لقدرات الصور والنصوص والفيديو، متجاوزًا حدود التعدد النمطي التقليدي المنفصل، وفاز بالبطولة المزدوجة في تقييمات OpenCompass وSuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "يجمع بين الاستدلال العميق البصري واللغوي، لتحقيق التفكير البطيء والاستدلال العميق، ويعرض سلسلة التفكير الكاملة."}, "SenseNova-V6-Turbo": {"description": "تحقيق توحيد أصلي لقدرات الصور والنصوص والفيديو، متجاوزًا حدود التعدد النمطي التقليدي المنفصل، متفوقًا بشكل شامل في القدرات الأساسية متعددة النماذج والقدرات اللغوية الأساسية، متوازنًا بين العلوم والآداب، واحتل مرارًا المرتبة الأولى على المستوى المحلي والدولي في العديد من التقييمات."}, "Skylark2-lite-8k": {"description": "نموذج سكايلارك (Skylark) من الجيل الثاني، نموذج سكايلارك2-لايت يتميز بسرعات استجابة عالية، مناسب للسيناريوهات التي تتطلب استجابة في الوقت الحقيقي، وحساسة للتكاليف، وغير متطلبة لدقة نموذج عالية، بسعة سياق تبلغ 8k."}, "Skylark2-pro-32k": {"description": "نموذج سكايلارك (Skylark) من الجيل الثاني، النسخة سكايلارك2-برو تتميز بدقة نموذج عالية، مناسبة لمهام توليد النصوص المعقدة، مثل إنشاء نصوص في مجالات احترافية، وكتابة الروايات، والترجمة عالية الجودة، بسعة سياق تبلغ 32k."}, "Skylark2-pro-4k": {"description": "نموذج سكايلارك (Skylark) من الجيل الثاني، النسخة سكايلارك2-برو تتميز بدقة نموذج عالية، مناسبة لمهام توليد النصوص المعقدة، مثل إنشاء نصوص في مجالات احترافية، وكتابة الروايات، والترجمة عالية الجودة، بسعة سياق تبلغ 4k."}, "Skylark2-pro-character-4k": {"description": "نموذج سكايلارك (Skylark) من الجيل الثاني، نموذج سكايلارك2-برو-شخصية يتميز بقدرات ممتازة في لعب الأدوار والدردشة، يجيد تجسيد شخصيات مختلفة بناءً على طلب المستخدم والتفاعل بشكل طبيعي، مناسب لبناء روبوتات الدردشة، والمساعدين الافتراضيين، وخدمة العملاء عبر الإنترنت، ويتميز بسرعة استجابة عالية."}, "Skylark2-pro-turbo-8k": {"description": "نموذج سكايلارك (Skylark) من الجيل الثاني، سكايلارك2-برو-توربو-8k يقدم استدلالًا أسرع وتكاليف أقل، بسعة سياق تبلغ 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 هو نموذج الجيل الجديد من سلسلة GLM المفتوحة، يحتوي على 32 مليار معلمة. يمكن مقارنة أداء هذا النموذج مع سلسلة GPT من OpenAI وسلسلة V3/R1 من DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 هو نموذج صغير من سلسلة GLM، يحتوي على 9 مليار معلمة. يرث هذا النموذج الخصائص التقنية من سلسلة GLM-4-32B، لكنه يوفر خيارات نشر أخف. على الرغم من حجمه الصغير، لا يزال GLM-4-9B-0414 يظهر قدرة ممتازة في توليد الأكواد، تصميم الويب، توليد الرسوم البيانية SVG، والكتابة المعتمدة على البحث."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking هو نموذج لغة بصري مفتوح المصدر (VLM) تم إصداره بشكل مشترك من قبل Zhizhu AI ومختبر KEG بجامعة تسينغهوا، مصمم خصيصًا لمعالجة المهام الإدراكية متعددة الوسائط المعقدة. يعتمد النموذج على النموذج الأساسي GLM-4-9B-0414، ومن خلال إدخال آلية الاستدلال \"سلسلة التفكير\" (Chain-of-Thought) واستخدام استراتيجيات التعلم المعزز، تم تحسين قدرته على الاستدلال عبر الوسائط واستقراره بشكل ملحوظ."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 هو نموذج استدلال يتمتع بقدرة على التفكير العميق. تم تطوير هذا النموذج بناءً على GLM-4-32B-0414 من خلال بدء التشغيل البارد وتعزيز التعلم، وتم تدريبه بشكل إضافي في المهام الرياضية، البرمجية، والمنطقية. مقارنة بالنموذج الأساسي، حقق GLM-Z1-32B-0414 تحسينًا ملحوظًا في القدرة الرياضية وحل المهام المعقدة."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 هو نموذج صغير من سلسلة GLM، يحتوي على 9 مليار معلمة، لكنه يظهر قدرة مذهلة مع الحفاظ على تقاليد المصدر المفتوح. على الرغم من حجمه الصغير، إلا أن هذا النموذج لا يزال يظهر أداءً ممتازًا في الاستدلال الرياضي والمهام العامة، حيث يحتل مستوى أداءً رائدًا بين نماذج المصدر المفتوح ذات الحجم المماثل."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 هو نموذج استدلال عميق يتمتع بقدرة على التفكير العميق (مقابل Deep Research من OpenAI). على عكس نماذج التفكير العميق التقليدية، يستخدم نموذج التفكير العميق وقتًا أطول لحل المشكلات الأكثر انفتاحًا وتعقيدًا."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B هو إصدار مفتوح المصدر، يوفر تجربة حوار محسنة لتطبيقات الحوار."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B هو أول نموذج استدلال كبير السياق طويل مدرب بالتعلم المعزز (LRM)، مخصص لتحسين مهام الاستدلال على النصوص الطويلة. يستخدم إطار تعلم معزز لتوسيع السياق تدريجيًا، محققًا انتقالًا مستقرًا من السياق القصير إلى الطويل. في سبعة اختبارات معيارية للأسئلة على مستندات طويلة، تفوق QwenLong-L1-32B على نماذج رائدة مثل OpenAI-o3-mini و Qwen3-235B-A22B، وأداءه قابل للمقارنة مع Claude-3.7-Sonnet-Thinking. يتميز النموذج بمهارات عالية في الاستدلال الرياضي، المنطقي، والاستدلال متعدد القفزات."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B، مع الحفاظ على القدرات اللغوية العامة الممتازة للنموذج الأصلي، تم تدريبه بشكل إضافي على 500 مليار توكن عالي الجودة، مما أدى إلى تحسين كبير في المنطق الرياضي وقدرات الترميز."}, "abab5.5-chat": {"description": "موجه لمشاهد الإنتاجية، يدعم معالجة المهام المعقدة وتوليد النصوص بكفاءة، مناسب للتطبيقات في المجالات المهنية."}, "abab5.5s-chat": {"description": "مصمم لمشاهد الحوار باللغة الصينية، يوفر قدرة توليد حوار عالي الجودة باللغة الصينية، مناسب لمجموعة متنوعة من التطبيقات."}, "abab6.5g-chat": {"description": "مصمم للحوار متعدد اللغات، يدعم توليد حوارات عالية الجودة بالإنجليزية والعديد من اللغات الأخرى."}, "abab6.5s-chat": {"description": "مناسب لمجموعة واسعة من مهام معالجة اللغة الطبيعية، بما في ذلك توليد النصوص، وأنظمة الحوار، وغيرها."}, "abab6.5t-chat": {"description": "محسن لمشا<PERSON>د الحوار باللغة الصينية، يوفر قدرة توليد حوار سلس ومتوافق مع عادات التعبير الصينية."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 هو نموذج لغة كبير متقدم، تم تحسينه من خلال التعلم المعزز وبيانات البدء البارد، ويتميز بأداء ممتاز في الاستدلال، والرياضيات، والبرمجة."}, "accounts/fireworks/models/deepseek-v3": {"description": "نموذج اللغة القوي من Deepseek، الذي يعتمد على مزيج من الخبراء (MoE)، بإجمالي عدد معلمات يبلغ 671 مليار، حيث يتم تفعيل 37 مليار معلمة لكل علامة."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "نموذج Llama 3 70B للتعليمات، مصمم للحوار متعدد اللغات وفهم اللغة الطبيعية، أداءه يتفوق على معظم النماذج المنافسة."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "نموذج Llama 3 8B للتعليمات، تم تحسينه للحوار والمهام متعددة اللغات، يظهر أداءً ممتازًا وفعالًا."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "نموذج Llama 3 8B للتعليمات (نسخة HF)، يتوافق مع نتائج التنفيذ الرسمية، يتمتع بتوافق عالٍ عبر المنصات."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "نموذج Llama 3.1 405B للتعليمات، يتمتع بمعلمات ضخمة، مناسب لمهام معقدة واتباع التعليمات في سيناريوهات ذات حمل عالي."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "نموذج Llama 3.1 70B للتعليمات، يوفر قدرة ممتازة على فهم اللغة الطبيعية وتوليدها، وهو الخيار المثالي لمهام الحوار والتحليل."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "نموذج Llama 3.1 8B للتعليمات، تم تحسينه للحوار متعدد اللغات، قادر على تجاوز معظم النماذج المفتوحة والمغلقة في المعايير الصناعية الشائعة."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "نموذج استدلال الصور المعدل من Meta ذو 11B معلمات. تم تحسين هذا النموذج للتعرف البصري، واستدلال الصور، ووصف الصور، والإجابة عن الأسئلة العامة المتعلقة بالصور. يستطيع النموذج فهم البيانات البصرية مثل الرسوم البيانية والرسوم، ويسد الفجوة بين الرؤية واللغة من خلال توليد أوصاف نصية لجزئيات الصور."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "نموذج التوجيه Llama 3.2 3B هو نموذج متعدد اللغات خفيف الوزن قدمته Meta. يهدف هذا النموذج إلى زيادة الكفاءة، مع تحسينات ملحوظة في التأخير والتكلفة مقارنة بالنماذج الأكبر. تشمل حالات الاستخدام النموذجية لهذا النموذج الاستفسارات وإعادة كتابة الملاحظات والمساعدة في الكتابة."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "نموذج استدلال الصور المعدل من Meta ذو 90B معلمات. تم تحسين هذا النموذج للتعرف البصري، واستدلال الصور، ووصف الصور، والإجابة عن الأسئلة العامة المتعلقة بالصور. يستطيع النموذج فهم البيانات البصرية مثل الرسوم البيانية والرسوم، ويسد الفجوة بين الرؤية واللغة من خلال توليد أوصاف نصية لجزئيات الصور."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct هو الإصدار المحدث من Llama 3.1 70B في ديسمبر. تم تحسين هذا النموذج بناءً على Llama 3.1 70B (الذي تم إصداره في يوليو 2024) لتعزيز استدعاء الأدوات، ودعم النصوص متعددة اللغات، والقدرات الرياضية وبرمجة. لقد حقق هذا النموذج مستويات رائدة في الصناعة في الاستدلال، والرياضيات، واتباع التعليمات، ويستطيع تقديم أداء مشابه لـ 3.1 405B، مع مزايا ملحوظة في السرعة والتكلفة."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "نموذج بـ 24 مليار معلمة، يتمتع بقدرات متقدمة تعادل النماذج الأكبر حجماً."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "نموذج Mixtral MoE 8x22B للتعليمات، مع معلمات ضخمة وهيكل خبير متعدد، يدعم معالجة فعالة لمهام معقدة."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "نموذج Mixtral MoE 8x7B للتعليمات، يوفر هيكل خبير متعدد لتقديم تعليمات فعالة واتباعها."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "نموذج MythoMax L2 13B، يجمع بين تقنيات الدمج الجديدة، بارع في السرد وأدوار الشخصيات."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "نموذج Phi 3 Vision للتعليمات، نموذج متعدد الوسائط خفيف الوزن، قادر على معالجة معلومات بصرية ونصية معقدة، يتمتع بقدرة استدلال قوية."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "نموذج QwQ هو نموذج بحث تجريبي تم تطويره بواسطة فريق Qwen، يركز على تعزيز قدرات الاستدلال للذكاء الاصطناعي."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "الإصدار 72B من نموذج Qwen-VL هو نتيجة أحدث ابتكارات Alibaba، ويمثل ما يقرب من عام من الابتكار."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 هي سلسلة من نماذج اللغة التي طورتها مجموعة Qwen من علي بابا، تحتوي فقط على شريحة فك شفرات. توفر هذه النماذج أحجامًا مختلفة، بما في ذلك 0.5B، 1.5B، 3B، 7B، 14B، 32B و72B، وتأتي بنسخ أساسية (base) ونماذج توجيهية (instruct)."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct هو أحدث إصدار من سلسلة نماذج اللغة الكبيرة المحددة للشيفرة التي أصدرتها Alibaba Cloud. تم تحسين هذا النموذج بشكل كبير في توليد الشيفرة، والاستدلال، وإصلاح الأخطاء، من خلال تدريب على 55 تريليون توكن."}, "accounts/yi-01-ai/models/yi-large": {"description": "نموذج Yi-Large، يتمتع بقدرة معالجة لغوية ممتازة، يمكن استخدامه في جميع أنواع مهام توليد وفهم اللغة."}, "ai21-jamba-1.5-large": {"description": "نموذج متعدد اللغات بحجم 398B (94B نشط)، يقدم نافذة سياق طويلة بحجم 256K، واستدعاء وظائف، وإخراج منظم، وتوليد مستند."}, "ai21-jamba-1.5-mini": {"description": "نموذج متعدد اللغات بحجم 52B (12B نشط)، يقدم نافذة سياق طويلة بحجم 256K، واستدعاء وظائف، وإخراج منظم، وتوليد مستند."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "نموذج متعدد اللغات يحتوي على 398 مليار معلمة (94 مليار نشطة)، يوفر نافذة سياق طويلة تصل إلى 256 ألف كلمة، استدعاء دوال، إخراج منظم وتوليد قائم على الحقائق."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "نموذج متعدد اللغات يحتوي على 52 مليار معلمة (12 مليار نشطة)، يوفر نافذة سياق طويلة تصل إلى 256 ألف كلمة، استدعاء دوال، إخراج منظم وتوليد قائم على الحقائق."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet يرفع المعايير في الصناعة، حيث يتفوق على نماذج المنافسين وClaude 3 Opus، ويظهر أداءً ممتازًا في تقييمات واسعة، مع سرعة وتكلفة تتناسب مع نماذجنا المتوسطة."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "لقد رفع كلود 3.5 سونيت معايير الصناعة، حيث تفوق أداؤه على نماذج المنافسين ونموذج كلود 3 أوبس، وأظهر أداءً ممتازًا في تقييمات واسعة، مع الحفاظ على سرعة وتكلفة نماذجنا المتوسطة."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku هو أسرع وأصغر نموذج من Anthropic، يوفر سرعة استجابة شبه فورية. يمكنه بسرعة الإجابة على الاستفسارات والطلبات البسيطة. سيتمكن العملاء من بناء تجربة ذكاء اصطناعي سلسة تحاكي التفاعل البشري. يمكن لـ Claude 3 Haiku معالجة الصور وإرجاع إخراج نصي، مع نافذة سياقية تبلغ 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus هو أقوى نموذج ذكاء اصطناعي من Anthropic، يتمتع بأداء متقدم في المهام المعقدة للغاية. يمكنه معالجة المطالبات المفتوحة والمشاهد غير المعروفة، مع سلاسة وفهم يشبه البشر. يعرض Claude 3 Opus حدود إمكانيات الذكاء الاصطناعي التوليدي. يمكن لـ Claude 3 Opus معالجة الصور وإرجاع إخراج نصي، مع نافذة سياقية تبلغ 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet من Anthropic يحقق توازنًا مثاليًا بين الذكاء والسرعة - مناسب بشكل خاص لأعباء العمل المؤسسية. يقدم أكبر فائدة بأقل من تكلفة المنافسين، وقد تم تصميمه ليكون نموذجًا موثوقًا وعالي التحمل، مناسبًا لنشر الذكاء الاصطناعي على نطاق واسع. يمكن لـ Claude 3 Sonnet معالجة الصور وإرجاع إخراج نصي، مع نافذة سياقية تبلغ 200K."}, "anthropic.claude-instant-v1": {"description": "نموذج سريع واقتصادي وما زال قويًا للغاية، يمكنه معالجة مجموعة من المهام بما في ذلك المحادثات اليومية، وتحليل النصوص، والتلخيص، والأسئلة والأجوبة على الوثائق."}, "anthropic.claude-v2": {"description": "نموذج يظهر قدرة عالية في مجموعة واسعة من المهام، من المحادثات المعقدة وتوليد المحتوى الإبداعي إلى اتباع التعليمات التفصيلية."}, "anthropic.claude-v2:1": {"description": "الإصدار المحدث من Claude 2، مع نافذة سياقية مضاعفة، وتحسينات في الاعتمادية ومعدل الهلوسة والدقة المستندة إلى الأدلة في الوثائق الطويلة وسياقات RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku هو أسرع وأصغر نموذج من Anthropic، مصمم لتحقيق استجابة شبه فورية. يتمتع بأداء توجيهي سريع ودقيق."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus هو أقوى نموذج من Anthropic لمعالجة المهام المعقدة للغاية. يتميز بأداء ممتاز وذكاء وسلاسة وفهم."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku هو أسرع نموذج من الجيل التالي من Anthropic. مقارنةً بـ Claude 3 Haiku، تم تحسين Claude 3.5 Haiku في جميع المهارات، وتفوق في العديد من اختبارات الذكاء على النموذج الأكبر من الجيل السابق Claude 3 Opus."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet يقدم قدرات تتجاوز Opus وسرعة أكبر من Sonnet، مع الحفاظ على نفس السعر. يتميز Sonnet بمهارات خاصة في البرمجة وعلوم البيانات ومعالجة الصور والمهام الوكيلة."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet هو أكثر النماذج ذكاءً من Anthropic حتى الآن، وهو أيضًا أول نموذج مختلط للتفكير في السوق. يمكن لـ Claude 3.7 Sonnet إنتاج استجابات شبه فورية أو تفكير تدريجي ممتد، حيث يمكن للمستخدمين رؤية هذه العمليات بوضوح. يتميز Sonnet بشكل خاص في البرمجة، وعلوم البيانات، ومعالجة الصور، والمهام الوكيلة."}, "anthropic/claude-opus-4": {"description": "كلود أوبوس 4 هو أقوى نموذج من أنثروبيك لمعالجة المهام المعقدة للغاية. يتميز بأداء ممتاز وذكاء وسلاسة وفهم عميق."}, "anthropic/claude-sonnet-4": {"description": "كلود سونيت 4 يمكنه إنتاج استجابات شبه فورية أو تفكير تدريجي مطول، حيث يمكن للمستخدمين رؤية هذه العمليات بوضوح. كما يمكن لمستخدمي API التحكم بدقة في مدة تفكير النموذج."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B هو نموذج لغة ضخم نادر التنشيط يحتوي على 72 مليار معلمة و16 مليار معلمة نشطة، يعتمد على بنية الخبراء المختلطين المجمعة (MoGE). في مرحلة اختيار الخبراء، يتم تجميع الخبراء وتقيد تنشيط عدد متساوٍ من الخبراء داخل كل مجموعة لكل رمز، مما يحقق توازنًا في تحميل الخبراء ويعزز بشكل كبير كفاءة نشر النموذج على منصة Ascend."}, "aya": {"description": "Aya 23 هو نموذج متعدد اللغات أطلقته Cohere، يدعم 23 لغة، مما يسهل التطبيقات اللغوية المتنوعة."}, "aya:35b": {"description": "Aya 23 هو نموذج متعدد اللغات أطلقته Cohere، يدعم 23 لغة، مما يسهل التطبيقات اللغوية المتنوعة."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B هو نموذج لغوي كبير مفتوح المصدر قابل للاستخدام التجاري تم تطويره بواسطة Baichuan Intelligence، ويحتوي على 13 مليار معلمة، وقد حقق أفضل النتائج في المعايير الصينية والإنجليزية."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B هو نموذج لغة ضخم يعتمد على بنية الخبراء المختلطين (MoE) تم تطويره بواسطة شركة بايدو. يحتوي النموذج على 300 مليار معلمة إجمالاً، لكنه ينشط فقط 47 مليار معلمة لكل رمز أثناء الاستدلال، مما يوازن بين الأداء القوي والكفاءة الحسابية. كأحد النماذج الأساسية في سلسلة ERNIE 4.5، يظهر أداءً متميزًا في مهام فهم النصوص، التوليد، الاستدلال، والبرمجة. يستخدم النموذج طريقة تدريب مسبق مبتكرة متعددة الوسائط ومتغايرة تعتمد على MoE، من خلال التدريب المشترك للنصوص والوسائط البصرية، مما يعزز قدراته الشاملة، خاصة في الالتزام بالتعليمات وتذكر المعرفة العالمية."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse هو نموذج متعدد اللغات عالي الأداء بسعة 32B، يهدف إلى تحدي أداء النماذج أحادية اللغة من خلال تحسين التعليمات، وتداول البيانات، وتدريب التفضيلات، وابتكارات دمج النماذج. يدعم 23 لغة."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse هو نموذج متعدد اللغات عالي الأداء بسعة 8B، يهدف إلى تحدي أداء النماذج أحادية اللغة من خلال تحسين التعليمات، وتداول البيانات، وتدريب التفضيلات، وابتكارات دمج النماذج. يدعم 23 لغة."}, "c4ai-aya-vision-32b": {"description": "Aya Vision هو نموذج متعدد الوسائط متقدم، يظهر أداءً ممتازًا في عدة معايير رئيسية للغة والنص والصورة. يدعم 23 لغة. يركز هذا الإصدار الذي يحتوي على 32 مليار معلمة على الأداء المتقدم متعدد اللغات."}, "c4ai-aya-vision-8b": {"description": "Aya Vision هو نموذج متعدد الوسائط متقدم، يظهر أداءً ممتازًا في عدة معايير رئيسية للغة والنص والصورة. يركز هذا الإصدار الذي يحتوي على 8 مليار معلمة على تقليل زمن الاستجابة وتحقيق أفضل أداء."}, "charglm-3": {"description": "CharGLM-3 مصمم خصيصًا للأدوار التفاعلية والمرافقة العاطفية، يدعم ذاكرة متعددة الجولات طويلة الأمد وحوارات مخصصة، ويستخدم على نطاق واسع."}, "charglm-4": {"description": "CharGLM-4 مصمم خصيصًا للأدوار والشعور بالرفقة، يدعم الذاكرة متعددة الجولات الطويلة والحوار المخصص، ويستخدم على نطاق واسع."}, "chatglm3": {"description": "ChatGLM3 هو نموذج مغلق المصدر تم إصداره بواسطة مختبر KEG في جامعة تسينغهوا وشركة Zhizhu AI، وقد تم تدريبه مسبقًا على كميات هائلة من المعرفة المعرفية باللغتين الصينية والإنجليزية، وتم تحسينه وفقًا للاختيارات البشرية. مقارنة بالنموذج الأول، حقق تحسينات بنسبة 16٪ و 36٪ و 280٪ في MMLU و C-Eval و GSM8K على التوالي، وتصدر قائمة المهام الصينية C-Eval. يناسب هذا النموذج السيناريوهات التي تتطلب كميات كبيرة من المعرفة وقدرات الاستدلال والإبداع، مثل كتابة النصوص الإعلانية وكتابة الروايات وكتابة المحتوى المعرفي وتكوين الكود."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base هو النموذج الأساسي المفتوح المصدر الأحدث من سلسلة ChatGLM التي طورتها شركة Zhìpǔ، ويحتوي على 6 مليارات معلمة."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o هو نموذج ديناميكي يتم تحديثه في الوقت الحقيقي للحفاظ على أحدث إصدار. يجمع بين فهم اللغة القوي وقدرات التوليد، مما يجعله مناسبًا لمجموعة واسعة من التطبيقات، بما في ذلك خدمة العملاء والتعليم والدعم الفني."}, "claude-2.0": {"description": "Claude 2 يوفر تقدمًا في القدرات الأساسية للمؤسسات، بما في ذلك سياق يصل إلى 200K توكن، وتقليل كبير في معدل حدوث الهلوسة في النموذج، وإشعارات النظام، وميزة اختبار جديدة: استدعاء الأدوات."}, "claude-2.1": {"description": "Claude 2 يوفر تقدمًا في القدرات الأساسية للمؤسسات، بما في ذلك سياق يصل إلى 200K توكن، وتقليل كبير في معدل حدوث الهلوسة في النموذج، وإشعارات النظام، وميزة اختبار جديدة: استدعاء الأدوات."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku هو أسرع نموذج من الجيل التالي من Anthropic. مقارنةً بـ Claude 3 Haiku، فإن Claude 3.5 Haiku قد حقق تحسينات في جميع المهارات، وتفوق في العديد من اختبارات الذكاء على أكبر نموذج من الجيل السابق، Claude 3 Opus."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet يوفر قدرات تتجاوز Opus وسرعة أكبر من Sonnet، مع الحفاظ على نفس السعر. Sonnet بارع بشكل خاص في البرمجة، وعلوم البيانات، ومعالجة الصور، ومهام الوكالة."}, "claude-3-5-sonnet-20241022": {"description": "يقدم كلاف 3.5 سونيت قدرات تتجاوز أوبوس وسرعة أكبر من سونيت، مع الحفاظ على نفس الأسعار. سونيت متخصصة بشكل خاص في البرمجة، علوم البيانات، معالجة الصور، والمهام الوكيلة."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet هو أحدث نموذج من Anthropic، يتميز بأداء ممتاز في تقييمات واسعة، ويتفوق على نماذج المنافسين ونموذج Claude 3.5 Sonnet، مع الحفاظ على سرعة وتكلفة نماذجنا المتوسطة."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku هو أسرع وأصغر نموذج من Anthropic، مصمم لتحقيق استجابة شبه فورية. يتمتع بأداء توجيهي سريع ودقيق."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus هو أقوى نموذج من Anthropic لمعالجة المهام المعقدة للغاية. يظهر أداءً ممتازًا في الذكاء، والسلاسة، والفهم."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet يوفر توازنًا مثاليًا بين الذكاء والسرعة لحمولات العمل المؤسسية. يقدم أقصى فائدة بسعر أقل، موثوق ومناسب للنشر على نطاق واسع."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 هو أقوى نموذج من Anthropic لمعالجة المهام المعقدة للغاية. إنه يتفوق في الأداء والذكاء والسلاسة والفهم."}, "claude-sonnet-4-20250514": {"description": "يمكن لClaude 4 Sonnet أن ينتج استجابات شبه فورية أو تفكير تدريجي ممتد، حيث يمكن للمستخدمين رؤية هذه العمليات بوضوح. يمكن لمستخدمي API أيضًا التحكم بدقة في وقت تفكير النموذج."}, "codegeex-4": {"description": "CodeGeeX-4 هو مساعد برمجي قوي، يدعم مجموعة متنوعة من لغات البرمجة في الإجابة الذكية وإكمال الشيفرة، مما يعزز من كفاءة التطوير."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B هو نموذج توليد كود متعدد اللغات، يدعم مجموعة شاملة من الوظائف بما في ذلك إكمال الشيفرات والتوليد، ومفسر الشيفرات، والبحث عبر الإنترنت، واستدعاء الوظائف، وأسئلة وأجوبة على مستوى المستودع، مما يغطي جميع سيناريوهات تطوير البرمجيات. إنه أحد أفضل نماذج توليد الشيفرات بأقل من 10 مليار معلمة."}, "codegemma": {"description": "CodeGemma هو نموذج لغوي خفيف الوزن مخصص لمهام البرمجة المختلفة، يدعم التكرار السريع والتكامل."}, "codegemma:2b": {"description": "CodeGemma هو نموذج لغوي خفيف الوزن مخصص لمهام البرمجة المختلفة، يدعم التكرار السريع والتكامل."}, "codellama": {"description": "Code Llama هو نموذج لغوي كبير يركز على توليد الشيفرة والنقاش، يجمع بين دعم مجموعة واسعة من لغات البرمجة، مناسب لبيئات المطورين."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama هو نموذج LLM يركز على توليد ومناقشة الشيفرة، يجمع بين دعم واسع للغات البرمجة، مناسب لبيئات المطورين."}, "codellama:13b": {"description": "Code Llama هو نموذج لغوي كبير يركز على توليد الشيفرة والنقاش، يجمع بين دعم مجموعة واسعة من لغات البرمجة، مناسب لبيئات المطورين."}, "codellama:34b": {"description": "Code Llama هو نموذج لغوي كبير يركز على توليد الشيفرة والنقاش، يجمع بين دعم مجموعة واسعة من لغات البرمجة، مناسب لبيئات المطورين."}, "codellama:70b": {"description": "Code Llama هو نموذج لغوي كبير يركز على توليد الشيفرة والنقاش، يجمع بين دعم مجموعة واسعة من لغات البرمجة، مناسب لبيئات المطورين."}, "codeqwen": {"description": "CodeQwen1.5 هو نموذج لغوي كبير تم تدريبه على مجموعة كبيرة من بيانات الشيفرة، مصمم لحل مهام البرمجة المعقدة."}, "codestral": {"description": "Codestral هو أول نموذج شيفرة من Mistral AI، يوفر دعمًا ممتازًا لمهام توليد الشيفرة."}, "codestral-latest": {"description": "Codestral هو نموذج توليد متقدم يركز على توليد الشيفرة، تم تحسينه لمهام الملء الوسيط وإكمال الشيفرة."}, "codex-mini-latest": {"description": "codex-mini-latest هو نسخة محسنة من o4-mini، مخصصة لـ Codex CLI. بالنسبة للاستخدام المباشر عبر API، نوصي بالبدء من gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B هو نموذج مصمم للامتثال للتعليمات، والحوار، والبرمجة."}, "cogview-4": {"description": "CogView-4 هو أول نموذج مفتوح المصدر من Zhipu يدعم توليد الحروف الصينية، مع تحسينات شاملة في فهم المعاني، وجودة توليد الصور، وقدرات توليد النصوص باللغتين الصينية والإنجليزية، ويدعم إدخال ثنائي اللغة بأي طول، وقادر على توليد صور بأي دقة ضمن النطاق المحدد."}, "cohere-command-r": {"description": "نموذج توليدي قابل للتوسع يستهدف RAG واستخدام الأدوات لتمكين الذكاء الاصطناعي على نطاق الإنتاج للمؤسسات."}, "cohere-command-r-plus": {"description": "نموذج RAG محسّن من الطراز الأول مصمم للتعامل مع أحمال العمل على مستوى المؤسسات."}, "cohere/Cohere-command-r": {"description": "Command R هو نموذج توليدي قابل للتوسع، مصمم للاستخدام مع RAG والأدوات، لتمكين الشركات من تحقيق ذكاء اصطناعي بمستوى الإنتاج."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ هو نموذج متقدم محسّن لـ RAG، مصمم للتعامل مع أعباء العمل على مستوى المؤسسات."}, "command": {"description": "نموذج حواري يتبع التعليمات، يظهر جودة عالية وموثوقية أكبر في المهام اللغوية، ويتميز بطول سياق أطول مقارنة بنموذجنا الأساسي للتوليد."}, "command-a-03-2025": {"description": "الأمر A هو أقوى نموذج لدينا حتى الآن، حيث يظهر أداءً ممتازًا في استخدام الأدوات، والوكالات، والتوليد المعزز بالاسترجاع (RAG)، وسيناريوهات التطبيقات متعددة اللغات. يتمتع الأمر A بطول سياق يبلغ 256K، ويمكن تشغيله باستخدام وحدتي GPU فقط، وقد زادت الإنتاجية بنسبة 150% مقارنةً بالأمر R+ 08-2024."}, "command-light": {"description": "إصدار أصغر وأسرع من الأمر، قوي تقريبًا بنفس القدر ولكنه أسرع."}, "command-light-nightly": {"description": "لتقليل الفجوة الزمنية بين إصدارات النسخ الرئيسية، أطلقنا إصدارًا ليليًا من نموذج الأمر. بالنسبة لسلسلة command-light، يُطلق على هذا الإصدار اسم command-light-nightly. يرجى ملاحظة أن command-light-nightly هو الإصدار الأحدث والأكثر تجريبية (وربما غير مستقر). يتم تحديث الإصدارات الليلية بانتظام دون إشعار مسبق، لذا لا يُنصح باستخدامها في بيئات الإنتاج."}, "command-nightly": {"description": "لتقليل الفجوة الزمنية بين إصدارات النسخ الرئيسية، أطلقنا إصدارًا ليليًا من نموذج الأمر. بالنسبة لسلسلة الأمر، يُطلق على هذا الإصدار اسم command-cightly. يرجى ملاحظة أن command-nightly هو الإصدار الأحدث والأكثر تجريبية (وربما غير مستقر). يتم تحديث الإصدارات الليلية بانتظام دون إشعار مسبق، لذا لا يُنصح باستخدامها في بيئات الإنتاج."}, "command-r": {"description": "Command R هو نموذج LLM محسن لمهام الحوار والسياقات الطويلة، مناسب بشكل خاص للتفاعل الديناميكي وإدارة المعرفة."}, "command-r-03-2024": {"description": "الأمر R هو نموذج حواري يتبع التعليمات، ويظهر جودة أعلى وموثوقية أكبر في المهام اللغوية، ويتميز بطول سياق أطول مقارنة بالنماذج السابقة. يمكن استخدامه في عمليات العمل المعقدة مثل توليد الشيفرات، والتوليد المعزز بالاسترجاع (RAG)، واستخدام الأدوات، والوكالات."}, "command-r-08-2024": {"description": "الأمر-r-08-2024 هو إصدار محدث من نموذج الأمر R، تم إصداره في أغسطس 2024."}, "command-r-plus": {"description": "Command R+ هو نموذج لغوي كبير عالي الأداء، مصمم لمشاهد الأعمال الحقيقية والتطبيقات المعقدة."}, "command-r-plus-04-2024": {"description": "الأمر R+ هو نموذج حواري يتبع التعليمات، ويظهر جودة أعلى وموثوقية أكبر في المهام اللغوية، ويتميز بطول سياق أطول مقارنة بالنماذج السابقة. إنه الأنسب لعمليات العمل المعقدة في RAG واستخدام الأدوات متعددة الخطوات."}, "command-r-plus-08-2024": {"description": "Command R+ هو نموذج حواري يتبع التعليمات، يقدم جودة أعلى وموثوقية أكبر في المهام اللغوية، مقارنة بالنماذج السابقة، مع طول سياق أطول. هو الأنسب لعمليات العمل المعقدة RAG واستخدام الأدوات متعددة الخطوات."}, "command-r7b-12-2024": {"description": "الأمر-r7b-12-2024 هو إصدار صغير وفعال تم إصداره في ديسمبر 2024. يظهر أداءً ممتازًا في المهام التي تتطلب استدلالًا معقدًا ومعالجة متعددة الخطوات مثل RAG، واستخدام الأدوات، والوكالات."}, "compound-beta": {"description": "Compound-beta هو نظام ذكاء اصطناعي مركب، مدعوم بعدة نماذج مفتوحة متاحة في GroqCloud، يمكنه استخدام الأدوات بشكل ذكي وانتقائي للإجابة على استفسارات المستخدمين."}, "compound-beta-mini": {"description": "Compound-beta-mini هو نظام ذكاء اصطناعي مركب، مدعوم بنماذج مفتوحة متاحة في GroqCloud، يمكنه استخدام الأدوات بشكل ذكي وانتقائي للإجابة على استفسارات المستخدمين."}, "computer-use-preview": {"description": "نموذج computer-use-preview هو نموذج مخصص لأدوات \"استخدام الحاسوب\"، تم تدريبه لفهم وتنفيذ المهام المتعلقة بالحاسوب."}, "dall-e-2": {"description": "النموذج الثاني من DALL·E، يدعم توليد صور أكثر واقعية ودقة، بدقة تعادل أربعة أضعاف الجيل الأول."}, "dall-e-3": {"description": "أحدث نموذج DALL·E، تم إصداره في نوفمبر 2023. يدعم توليد صور أكثر واقعية ودقة، مع قدرة أكبر على التعبير عن التفاصيل."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct يوفر قدرة معالجة تعليمات موثوقة، يدعم تطبيقات متعددة الصناعات."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 هو نموذج استدلال مدفوع بالتعلم المعزز (RL) يعالج مشكلات التكرار وقابلية القراءة في النموذج. قبل استخدام RL، قدم DeepSeek-R1 بيانات بدء باردة، مما أدى إلى تحسين أداء الاستدلال. إنه يقدم أداءً مماثلاً لـ OpenAI-o1 في المهام الرياضية والبرمجية والاستدلال، وقد حسّن النتائج العامة من خلال طرق تدريب مصممة بعناية."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 يعزز بشكل كبير عمق قدرات الاستدلال والاستنتاج من خلال زيادة الموارد الحاسوبية وإدخال آليات تحسين الخوارزميات في مرحلة ما بعد التدريب. يظهر النموذج أداءً ممتازًا في تقييمات معيارية متنوعة، بما في ذلك الرياضيات، البرمجة، والمنطق العام. أداؤه العام يقترب الآن من النماذج الرائدة مثل O3 و Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B هو نموذج تم الحصول عليه من تقطير سلسلة التفكير من DeepSeek-R1-0528 إلى Qwen3 8B Base. حقق هذا النموذج أداءً متقدمًا (SOTA) بين النماذج المفتوحة المصدر، متفوقًا على Qwen3 8B بنسبة 10% في اختبار AIME 2024، ووصل إلى مستوى أداء Qwen3-235B-thinking. أظهر أداءً ممتازًا في الاستدلال الرياضي، البرمجة، والمنطق العام عبر عدة اختبارات معيارية، ويشارك نفس بنية Qwen3-8B لكنه يستخدم تكوين محلل الرموز الخاص بـ DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "نموذج التقطير DeepSeek-R1، تم تحسين أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "نموذج التقطير DeepSeek-R1، تم تحسين أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "نموذج التقطير DeepSeek-R1، تم تحسين أداء الاستدلال من خلال التعلم المعزز وبيانات البداية الباردة، ويعيد نموذج المصدر فتح معايير المهام المتعددة."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B هو نموذج تم الحصول عليه من Qwen2.5-32B من خلال التقطير المعرفي. تم ضبط هذا النموذج باستخدام 800,000 عينة مختارة تم إنشاؤها بواسطة DeepSeek-R1، ويظهر أداءً ممتازًا في مجالات متعددة مثل الرياضيات، البرمجة، والاستدلال. حقق نتائج ممتازة في اختبارات المعايير مثل AIME 2024، MATH-500، وGPQA Diamond، حيث حقق دقة 94.3% في MATH-500، مما يظهر قدرة قوية في الاستدلال الرياضي."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B هو نموذج تم الحصول عليه من Qwen2.5-Math-7B من خلال التقطير المعرفي. تم ضبط هذا النموذج باستخدام 800,000 عينة مختارة تم إنشاؤها بواسطة DeepSeek-R1، ويظهر أداءً ممتازًا في الاستدلال. حقق نتائج ممتازة في اختبارات المعايير، حيث حقق دقة 92.8% في MATH-500، وحقق معدل نجاح 55.5% في AIME 2024، وحصل على تقييم 1189 في CodeForces، مما يظهر قدرة قوية في الرياضيات والبرمجة كنموذج بحجم 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 يجمع بين الميزات الممتازة للإصدارات السابقة، ويعزز القدرات العامة والترميز."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 هو نموذج لغوي مختلط الخبراء (MoE) يحتوي على 6710 مليار معلمة، يستخدم انتباه متعدد الرؤوس (MLA) وبنية DeepSeekMoE، ويجمع بين استراتيجية توازن الحمل بدون خسارة مساعدة، مما يحسن كفاءة الاستدلال والتدريب. من خلال التدريب المسبق على 14.8 تريليون توكن عالي الجودة، وإجراء تعديلات إشرافية وتعلم معزز، يتفوق DeepSeek-V3 في الأداء على نماذج المصدر المفتوح الأخرى، ويقترب من النماذج المغلقة الرائدة."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B هو نموذج متقدم تم تدريبه للحوار المعقد."}, "deepseek-ai/deepseek-r1": {"description": "نموذج لغوي متقدم وفعال، بارع في الاستدلال، والرياضيات، والبرمجة."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 هو نموذج لغوي بصري مختلط الخبراء (MoE) تم تطويره بناءً على DeepSeekMoE-27B، يستخدم بنية MoE ذات تفعيل نادر، محققًا أداءً ممتازًا مع تفعيل 4.5 مليار معلمة فقط. يقدم هذا النموذج أداءً ممتازًا في مهام مثل الأسئلة البصرية، التعرف الضوئي على الأحرف، فهم الوثائق/الجداول/الرسوم البيانية، وتحديد المواقع البصرية."}, "deepseek-chat": {"description": "نموذج مفتوح المصدر الجديد الذي يجمع بين القدرات العامة وقدرات البرمجة، لا يحتفظ فقط بالقدرات الحوارية العامة لنموذج الدردشة الأصلي وقدرات معالجة الشيفرة القوية لنموذج Coder، بل يتماشى أيضًا بشكل أفضل مع تفضيلات البشر. بالإضافة إلى ذلك، حقق DeepSeek-V2.5 تحسينات كبيرة في مهام الكتابة، واتباع التعليمات، وغيرها من المجالات."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B هو نموذج لغة برمجية، تم تدريبه على 20 تريليون بيانات، منها 87% كود و13% لغات صينية وإنجليزية. يقدم النموذج حجم نافذة 16K ومهام ملء الفراغ، مما يوفر إكمال الشيفرات على مستوى المشروع ووظائف ملء المقاطع."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 هو نموذج شيفرة مفتوح المصدر من نوع خبير مختلط، يقدم أداءً ممتازًا في مهام الشيفرة، ويضاهي GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 هو نموذج شيفرة مفتوح المصدر من نوع خبير مختلط، يقدم أداءً ممتازًا في مهام الشيفرة، ويضاهي GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 هو نموذج استدلال مدفوع بالتعلم المعزز (RL) يعالج مشكلات التكرار وقابلية القراءة في النموذج. قبل استخدام RL، قدم DeepSeek-R1 بيانات بدء باردة، مما أدى إلى تحسين أداء الاستدلال. إنه يقدم أداءً مماثلاً لـ OpenAI-o1 في المهام الرياضية والبرمجية والاستدلال، وقد حسّن النتائج العامة من خلال طرق تدريب مصممة بعناية."}, "deepseek-r1-0528": {"description": "نموذج كامل القوة بحجم 685 مليار، صدر في 28 مايو 2025. استخدم DeepSeek-R1 تقنيات التعلم المعزز على نطاق واسع في مرحلة ما بعد التدريب، مما عزز بشكل كبير قدرات الاستدلال للنموذج مع وجود بيانات تعليمية قليلة جدًا. يتمتع بأداء عالي وقدرات قوية في المهام المتعلقة بالرياضيات، البرمجة، والاستدلال اللغوي الطبيعي."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B النسخة السريعة، تدعم البحث المتصل في الوقت الحقيقي، وتوفر سرعة استجابة أسرع مع الحفاظ على أداء النموذج."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B النسخة القياسية، تدعم البحث المتصل في الوقت الحقيقي، مناسبة للمحادثات والمهام النصية التي تتطلب معلومات حديثة."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama هو نموذج مستخلص من DeepSeek-R1 بناءً على Llama."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - النموذج الأكبر والأذكى في مجموعة DeepSeek - تم تقطيره إلى بنية Llama 70B. بناءً على اختبارات المعايير والتقييمات البشرية، يظهر هذا النموذج ذكاءً أكبر من Llama 70B الأصلي، خاصة في المهام التي تتطلب دقة رياضية وحقائق."}, "deepseek-r1-distill-llama-8b": {"description": "نموذج DeepSeek-R1-Di<PERSON>ill تم تطويره من خلال تقنية تقطير المعرفة، حيث تم تعديل عينات تم إنشاؤها بواسطة DeepSeek-R1 على نماذج مفتوحة المصدر مثل Qwen وLlama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "تم إصداره لأول مرة في 14 فبراير 2025، تم استخلاصه بواسطة فريق تطوير نموذج Qianfan باستخدام Llama3_70B كنموذج أساسي (مبني على Meta Llama)، وتم إضافة نصوص Qianfan إلى بيانات الاستخلاص."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "تم إصداره لأول مرة في 14 فبراير 2025، تم استخلاصه بواسطة فريق تطوير نموذج Qianfan باستخدام Llama3_8B كنموذج أساسي (مبني على Meta Llama)، وتم إضافة نصوص Qianfan إلى بيانات الاستخلاص."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen هو نموذج مستخلص من DeepSeek-R1 بناءً على Qwen."}, "deepseek-r1-distill-qwen-1.5b": {"description": "نموذج DeepSeek-R1-Di<PERSON>ill تم تطويره من خلال تقنية تقطير المعرفة، حيث تم تعديل عينات تم إنشاؤها بواسطة DeepSeek-R1 على نماذج مفتوحة المصدر مثل Qwen وLlama."}, "deepseek-r1-distill-qwen-14b": {"description": "نموذج DeepSeek-R1-Di<PERSON>ill تم تطويره من خلال تقنية تقطير المعرفة، حيث تم تعديل عينات تم إنشاؤها بواسطة DeepSeek-R1 على نماذج مفتوحة المصدر مثل Qwen وLlama."}, "deepseek-r1-distill-qwen-32b": {"description": "نموذج DeepSeek-R1-Di<PERSON>ill تم تطويره من خلال تقنية تقطير المعرفة، حيث تم تعديل عينات تم إنشاؤها بواسطة DeepSeek-R1 على نماذج مفتوحة المصدر مثل Qwen وLlama."}, "deepseek-r1-distill-qwen-7b": {"description": "نموذج DeepSeek-R1-Di<PERSON>ill تم تطويره من خلال تقنية تقطير المعرفة، حيث تم تعديل عينات تم إنشاؤها بواسطة DeepSeek-R1 على نماذج مفتوحة المصدر مثل Qwen وLlama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 النسخة السريعة الكاملة، تدعم البحث المتصل في الوقت الحقيقي، تجمع بين القدرات القوية لـ 671 مليار معلمة وسرعة استجابة أسرع."}, "deepseek-r1-online": {"description": "DeepSeek R1 النسخة الكاملة، تحتوي على 671 مليار معلمة، تدعم البحث المتصل في الوقت الحقيقي، وتتمتع بقدرات فهم وتوليد أقوى."}, "deepseek-reasoner": {"description": "نموذج الاستدلال الذي أطلقته DeepSeek. قبل تقديم الإجابة النهائية، يقوم النموذج أولاً بإخراج سلسلة من المحتوى الفكري لتحسين دقة الإجابة النهائية."}, "deepseek-v2": {"description": "DeepSeek V2 هو نموذج لغوي فعال من نوع Mixture-of-Experts، مناسب لاحتياجات المعالجة الاقتصادية."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B هو نموذج تصميم الشيفرة لـ DeepSeek، يوفر قدرة توليد شيفرة قوية."}, "deepseek-v3": {"description": "DeepSeek-V3 هو نموذج MoE تم تطويره بواسطة شركة Hangzhou DeepSeek AI Technology Research Co.، Ltd، وقد حقق نتائج بارزة في العديد من التقييمات، ويحتل المرتبة الأولى بين نماذج المصدر المفتوح في القوائم الرئيسية. مقارنةً بنموذج V2.5، حقق V3 زيادة في سرعة التوليد بمقدار 3 مرات، مما يوفر تجربة استخدام أسرع وأكثر سلاسة للمستخدمين."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 هو نموذج MoE يحتوي على 671 مليار معلمة، ويتميز بقدرات بارزة في البرمجة والتقنية، وفهم السياق ومعالجة النصوص الطويلة."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 هو نموذج مختلط خبير يحتوي على 685B من المعلمات، وهو أحدث إصدار من سلسلة نماذج الدردشة الرائدة لفريق DeepSeek.\n\nيستفيد من نموذج [DeepSeek V3](/deepseek/deepseek-chat-v3) ويظهر أداءً ممتازًا في مجموعة متنوعة من المهام."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 هو نموذج مختلط خبير يحتوي على 685B من المعلمات، وهو أحدث إصدار من سلسلة نماذج الدردشة الرائدة لفريق DeepSeek.\n\nيستفيد من نموذج [DeepSeek V3](/deepseek/deepseek-chat-v3) ويظهر أداءً ممتازًا في مجموعة متنوعة من المهام."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 يعزز بشكل كبير من قدرة النموذج على الاستدلال في ظل وجود بيانات محدودة جدًا. قبل تقديم الإجابة النهائية، يقوم النموذج أولاً بإخراج سلسلة من التفكير لتحسين دقة الإجابة النهائية."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 يعزز بشكل كبير قدرة الاستدلال للنموذج حتى مع وجود بيانات تعليمية قليلة جدًا. قبل إخراج الإجابة النهائية، يقوم النموذج أولاً بإخراج سلسلة من التفكير لتحسين دقة الإجابة النهائية."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 يعزز بشكل كبير قدرة الاستدلال للنموذج حتى مع وجود بيانات تعليمية قليلة جدًا. قبل إخراج الإجابة النهائية، يقوم النموذج أولاً بإخراج سلسلة من التفكير لتحسين دقة الإجابة النهائية."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B هو نموذج لغوي كبير يعتمد على Llama3.3 70B، حيث يحقق أداءً تنافسيًا مماثلاً للنماذج الرائدة الكبيرة من خلال استخدام التعديلات المستندة إلى مخرجات DeepSeek R1."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B هو نموذج لغوي كبير مكرر يعتمد على Llama-3.1-8B-Instruct، تم تدريبه باستخدام مخرجات DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B هو نموذج لغوي كبير مكرر يعتمد على Qwen 2.5 14B، تم تدريبه باستخدام مخرجات DeepSeek R1. لقد تفوق هذا النموذج في العديد من اختبارات المعايير على نموذج OpenAI o1-mini، محققًا أحدث الإنجازات التقنية في النماذج الكثيفة. فيما يلي بعض نتائج اختبارات المعايير:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nتصنيف CodeForces: 1481\nأظهر هذا النموذج أداءً تنافسيًا مماثلاً للنماذج الرائدة الأكبر حجمًا من خلال التعديل المستند إلى مخرجات DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B هو نموذج لغوي كبير مكرر يعتمد على Qwen 2.5 32B، تم تدريبه باستخدام مخرجات DeepSeek R1. لقد تفوق هذا النموذج في العديد من اختبارات المعايير على نموذج OpenAI o1-mini، محققًا أحدث الإنجازات التقنية في النماذج الكثيفة. فيما يلي بعض نتائج اختبارات المعايير:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nتصنيف CodeForces: 1691\nأظهر هذا النموذج أداءً تنافسيًا مماثلاً للنماذج الرائدة الأكبر حجمًا من خلال التعديل المستند إلى مخرجات DeepSeek R1."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 هو أحدث نموذج مفتوح المصدر أطلقه فريق DeepSeek، ويتميز بأداء استدلال قوي للغاية، خاصة في المهام الرياضية والبرمجة والاستدلال، حيث وصل إلى مستوى مماثل لنموذج OpenAI o1."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 يعزز بشكل كبير من قدرة النموذج على الاستدلال في ظل وجود بيانات محدودة جدًا. قبل تقديم الإجابة النهائية، يقوم النموذج أولاً بإخراج سلسلة من التفكير لتحسين دقة الإجابة النهائية."}, "deepseek/deepseek-v3": {"description": "حقق DeepSeek-V3 تقدمًا كبيرًا في سرعة الاستدلال مقارنة بالنماذج السابقة. يحتل المرتبة الأولى بين النماذج المفتوحة المصدر، ويمكن مقارنته بأحدث النماذج المغلقة على مستوى العالم. يعتمد DeepSeek-V3 على بنية الانتباه المتعدد الرؤوس (MLA) وبنية DeepSeekMoE، والتي تم التحقق منها بشكل شامل في DeepSeek-V2. بالإضافة إلى ذلك، قدم DeepSeek-V3 استراتيجية مساعدة غير مدمرة للتوازن في الحمل، وحدد أهداف تدريب متعددة التسمية لتحقيق أداء أقوى."}, "deepseek/deepseek-v3/community": {"description": "حقق DeepSeek-V3 تقدمًا كبيرًا في سرعة الاستدلال مقارنة بالنماذج السابقة. يحتل المرتبة الأولى بين النماذج المفتوحة المصدر، ويمكن مقارنته بأحدث النماذج المغلقة على مستوى العالم. يعتمد DeepSeek-V3 على بنية الانتباه المتعدد الرؤوس (MLA) وبنية DeepSeekMoE، والتي تم التحقق منها بشكل شامل في DeepSeek-V2. بالإضافة إلى ذلك، قدم DeepSeek-V3 استراتيجية مساعدة غير مدمرة للتوازن في الحمل، وحدد أهداف تدريب متعددة التسمية لتحقيق أداء أقوى."}, "deepseek_r1": {"description": "DeepSeek-R1 هو نموذج استدلال مدفوع بالتعلم المعزز (RL)، يعالج مشكلات التكرار والقراءة في النموذج. قبل RL، أدخل DeepSeek-R1 بيانات بدء التشغيل الباردة، مما عزز أداء الاستدلال. يظهر أداءً متساويًا مع OpenAI-o1 في المهام الرياضية، البرمجية، والاستدلال، ومن خلال طرق التدريب المصممة بعناية، تم تحسين الأداء العام."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B هو نموذج تم الحصول عليه من Llama-3.3-70B-Instruct من خلال التدريب بالتقطير. هذا النموذج هو جزء من سلسلة DeepSeek-R1، ويظهر أداءً ممتازًا في مجالات متعددة مثل الرياضيات، البرمجة، والاستدلال من خلال استخدام عينات تم إنشاؤها بواسطة DeepSeek-R1 للتدريب."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B هو نموذج تم الحصول عليه من Qwen2.5-14B من خلال تقطير المعرفة. يستخدم هذا النموذج 800,000 عينة مختارة تم إنشاؤها بواسطة DeepSeek-R1 للتدريب، ويظهر قدرة استدلال ممتازة."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B هو نموذج تم الحصول عليه من Qwen2.5-32B من خلال تقطير المعرفة. يستخدم هذا النموذج 800,000 عينة مختارة تم إنشاؤها بواسطة DeepSeek-R1 للتدريب، ويظهر أداءً ممتازًا في مجالات متعددة مثل الرياضيات، البرمجة، والاستدلال."}, "doubao-1.5-lite-32k": {"description": "دو باو 1.5 لايت هو نموذج الجيل الجديد الخفيف، مع سرعة استجابة قصوى، حيث يصل الأداء والوقت المستغرق إلى مستوى عالمي."}, "doubao-1.5-pro-256k": {"description": "دو باو 1.5 برو 256k هو النسخة المحدثة من دو باو 1.5 برو، حيث تم تحسين الأداء العام بنسبة 10%. يدعم استدلال نافذة السياق 256k، وطول الإخراج يصل إلى 12k توكن. أداء أعلى، نافذة أكبر، قيمة عالية، مناسب لمجموعة واسعة من سيناريوهات الاستخدام."}, "doubao-1.5-pro-32k": {"description": "دو باو 1.5 برو هو نموذج الجيل الجديد الرائد، مع ترقية شاملة في الأداء، حيث يظهر تفوقًا في المعرفة، والبرمجة، والاستدلال، وغيرها."}, "doubao-1.5-thinking-pro": {"description": "نموذج Doubao-1.5 الجديد للتفكير العميق، يتميز بأداء بارز في مجالات الرياضيات، البرمجة، الاستدلال العلمي، وكذلك في المهام العامة مثل الكتابة الإبداعية. حقق أو اقترب من المستوى الأول في العديد من المعايير المرموقة مثل AIME 2024 وCodeforces وGPQA. يدعم نافذة سياق بحجم 128k و16k للإخراج."}, "doubao-1.5-thinking-pro-m": {"description": "نموذج التفكير العميق الجديد Doubao-1.5 (الإصدار m مزود بقدرات استدلال متعددة الوسائط أصلية)، يتميز بأداء بارز في المجالات المتخصصة مثل الرياضيات، البرمجة، الاستدلال العلمي، والمهام العامة مثل الكتابة الإبداعية. وصل أو اقترب من المستوى الأول في معايير AIME 2024، Codeforces، GPQA وغيرها. يدعم نافذة سياق 128k وإخراج 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "نموذج التفكير العميق البصري الجديد، يتمتع بقدرات فهم واستدلال متعددة الوسائط عامة أقوى، وحقق أداءً متفوقًا في 37 من أصل 59 معيار تقييم عام."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS هو نموذج وكيل موجه أصلاً للتفاعل مع واجهات المستخدم الرسومية (GUI). يتفاعل بسلاسة مع GUI من خلال قدرات شبيهة بالبشر في الإدراك، الاستدلال، والعمل."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite هو نموذج كبير متعدد الوسائط تم ترقيته حديثًا، يدعم التعرف على الصور بدقة غير محدودة ونسب عرض إلى ارتفاع متطرفة، ويعزز قدرات الاستدلال البصري، التعرف على الوثائق، فهم المعلومات التفصيلية، واتباع التعليمات. يدعم نافذة سياق 128k، وطول الإخراج يدعم حتى 16k توكن."}, "doubao-1.5-vision-pro": {"description": "نموذج متعدد الوسائط مطور Doubao-1.5-vision-pro يدعم التعرف على الصور بأي دقة ونسب أبعاد متطرفة، معزّز بقدرات الاستدلال البصري، التعرف على الوثائق، فهم التفاصيل، والامتثال للتعليمات."}, "doubao-1.5-vision-pro-32k": {"description": "نموذج متعدد الوسائط مطور Doubao-1.5-vision-pro يدعم التعرف على الصور بأي دقة ونسب أبعاد متطرفة، معزّز بقدرات الاستدلال البصري، التعرف على الوثائق، فهم التفاصيل، والامتثال للتعليمات."}, "doubao-lite-128k": {"description": "يتميز بسرعة استجابة فائقة وقيمة أفضل مقابل المال، ويوفر خيارات أكثر مرونة للعملاء في سيناريوهات مختلفة. يدعم الاستدلال والتخصيص مع نافذة سياق 128k."}, "doubao-lite-32k": {"description": "يتميز بسرعة استجابة فائقة وقيمة أفضل مقابل المال، ويوفر خيارات أكثر مرونة للعملاء في سيناريوهات مختلفة. يدعم الاستدلال والتخصيص مع نافذة سياق 32k."}, "doubao-lite-4k": {"description": "يتميز بسرعة استجابة فائقة وقيمة أفضل مقابل المال، ويوفر خيارات أكثر مرونة للعملاء في سيناريوهات مختلفة. يدعم الاستدلال والتخصيص مع نافذة سياق 4k."}, "doubao-pro-256k": {"description": "النموذج الرئيسي الأكثر فعالية، مناسب لمعالجة المهام المعقدة، ويحقق أداءً ممتازًا في سيناريوهات مثل الأسئلة المرجعية، التلخيص، الإبداع، تصنيف النصوص، ولعب الأدوار. يدعم الاستدلال والتخصيص مع نافذة سياق 256k."}, "doubao-pro-32k": {"description": "النموذج الرئيسي الأكثر فعالية، مناسب لمعالجة المهام المعقدة، ويحقق أداءً ممتازًا في سيناريوهات مثل الأسئلة المرجعية، التلخيص، الإبداع، تصنيف النصوص، ولعب الأدوار. يدعم الاستدلال والتخصيص مع نافذة سياق 32k."}, "doubao-seed-1.6": {"description": "نموذج Doubao-Seed-1.6 متعدد الوسائط للتفكير العميق، يدعم ثلاثة أوضاع تفكير: تلقائي/تفكير/عدم تفكير. في وضع عدم التفكير، يتحسن أداء النموذج بشكل كبير مقارنة بـ Doubao-1.5-pro/250115. يدعم نافذة سياق بحجم 256k وطول إخراج يصل إلى 16k رمز."}, "doubao-seed-1.6-flash": {"description": "نموذج Doubao-Seed-1.6-flash للتفكير العميق متعدد الوسائط مع سرعة استدلال فائقة، حيث يحتاج TPOT فقط إلى 10 مللي ثانية؛ يدعم فهم النصوص والرؤية، وتفوق قدرات فهم النصوص على الجيل السابق lite، وفهم الرؤية يضاهي نماذج pro المنافسة. يدعم نافذة سياق بحجم 256k وطول إخراج يصل إلى 16k رمز."}, "doubao-seed-1.6-thinking": {"description": "نموذج Doubao-Seed-1.6-thinking يعزز قدرات التفكير بشكل كبير، مقارنة بـ Doubao-1.5-thinking-pro، مع تحسينات إضافية في القدرات الأساسية مثل البرمجة والرياضيات والاستدلال المنطقي، ويدعم الفهم البصري. يدعم نافذة سياق بحجم 256k وطول إخراج يصل إلى 16k رمز."}, "doubao-seedream-3-0-t2i-250415": {"description": "نموذج توليد الصور Doubao طوره فريق Seed في ByteDance، يدعم إدخال النص والصورة، ويوفر تجربة توليد صور عالية الجودة وقابلة للتحكم. يولد الصور بناءً على أوامر نصية."}, "doubao-vision-lite-32k": {"description": "نموذج Doubao-vision هو نموذج متعدد الوسائط أطلقته Doubao، يتمتع بقدرات قوية في فهم الصور والاستدلال، بالإضافة إلى دقة عالية في فهم التعليمات. أظهر النموذج أداءً قويًا في استخراج المعلومات من النصوص والصور، والمهام الاستدلالية القائمة على الصور، مما يجعله مناسبًا لمهام الأسئلة البصرية المعقدة والواسعة."}, "doubao-vision-pro-32k": {"description": "نموذج Doubao-vision هو نموذج متعدد الوسائط أطلقته Doubao، يتمتع بقدرات قوية في فهم الصور والاستدلال، بالإضافة إلى دقة عالية في فهم التعليمات. أظهر النموذج أداءً قويًا في استخراج المعلومات من النصوص والصور، والمهام الاستدلالية القائمة على الصور، مما يجعله مناسبًا لمهام الأسئلة البصرية المعقدة والواسعة."}, "emohaa": {"description": "<PERSON><PERSON>aa هو نموذج نفسي، يتمتع بقدرات استشارية متخصصة، يساعد المستخدمين في فهم القضايا العاطفية."}, "ernie-3.5-128k": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يغطي كمية هائلة من البيانات باللغة الصينية والإنجليزية، ويتميز بقدرات عامة قوية، تلبي متطلبات معظم حالات الحوار، والإجابة، والتوليد، وتطبيقات المكونات الإضافية؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة."}, "ernie-3.5-8k": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يغطي كمية هائلة من البيانات باللغة الصينية والإنجليزية، ويتميز بقدرات عامة قوية، تلبي متطلبات معظم حالات الحوار، والإجابة، والتوليد، وتطبيقات المكونات الإضافية؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة."}, "ernie-3.5-8k-preview": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، يغطي كمية هائلة من البيانات باللغة الصينية والإنجليزية، ويتميز بقدرات عامة قوية، تلبي متطلبات معظم حالات الحوار، والإجابة، والتوليد، وتطبيقات المكونات الإضافية؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة."}, "ernie-4.0-8k-latest": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي حقق ترقية شاملة في القدرات مقارنةً بـ ERNIE 3.5، ويستخدم على نطاق واسع في مشاهد المهام المعقدة في مختلف المجالات؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة."}, "ernie-4.0-8k-preview": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي حقق ترقية شاملة في القدرات مقارنةً بـ ERNIE 3.5، ويستخدم على نطاق واسع في مشاهد المهام المعقدة في مختلف المجالات؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة."}, "ernie-4.0-turbo-128k": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي يظهر أداءً ممتازًا بشكل شامل، ويستخدم على نطاق واسع في مشاهد المهام المعقدة في مختلف المجالات؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة. مقارنةً بـ ERNIE 4.0، يظهر أداءً أفضل."}, "ernie-4.0-turbo-8k-latest": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي يظهر أداءً ممتازًا بشكل شامل، ويستخدم على نطاق واسع في مشاهد المهام المعقدة في مختلف المجالات؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة. مقارنةً بـ ERNIE 4.0، يظهر أداءً أفضل."}, "ernie-4.0-turbo-8k-preview": {"description": "نموذج اللغة الكبير الرائد الذي طورته بايدو، والذي يظهر أداءً ممتازًا بشكل شامل، ويستخدم على نطاق واسع في مشاهد المهام المعقدة في مختلف المجالات؛ يدعم الاتصال التلقائي بمكونات البحث من بايدو، مما يضمن تحديث معلومات الإجابة. مقارنةً بـ ERNIE 4.0، يظهر أداءً أفضل."}, "ernie-4.5-8k-preview": {"description": "نموذج ونسين 4.5 هو نموذج أساسي جديد متعدد الوسائط تم تطويره ذاتيًا بواسطة بايدو، من خلال نمذجة متعددة الوسائط لتحقيق تحسين متزامن، ويظهر قدرة ممتازة على الفهم متعدد الوسائط؛ يتمتع بقدرات لغوية متقدمة، مع تحسين شامل في الفهم، والتوليد، والمنطق، والذاكرة، مع تحسين كبير في إزالة الأوهام، والاستدلال المنطقي، وقدرات البرمجة."}, "ernie-4.5-turbo-128k": {"description": "تم تعزيز Wenxin 4.5 Turbo بشكل ملحوظ في مجالات مثل تقليل الهلوسة، والاستدلال المنطقي، وقدرات البرمجة. مقارنةً بـ Wenxin 4.5، فهو أسرع وأقل تكلفة. تم تحسين قدرات النموذج بشكل شامل لتلبية احتياجات معالجة المحادثات الطويلة متعددة الجولات، ومهام فهم الأسئلة والأجوبة للنصوص الطويلة."}, "ernie-4.5-turbo-32k": {"description": "تم تعزيز Wenxin 4.5 Turbo بشكل ملحوظ في مجالات مثل تقليل الهلوسة، والاستدلال المنطقي، وقدرات البرمجة. مقارنةً بـ Wenxin 4.5، فهو أسرع وأقل تكلفة. تم تحسين قدرات الإبداع النصي، والأسئلة والأجوبة بشكل ملحوظ. زادت مدة الإخراج وتأخير الجمل الكاملة مقارنةً بـ ERNIE 4.5."}, "ernie-4.5-turbo-vl-32k": {"description": "إصدار جديد من نموذج <PERSON>، مع تحسينات ملحوظة في فهم الصور، والإبداع، والترجمة، والبرمجة، ويدعم لأول مرة طول سياق يصل إلى 32K، مع تقليل ملحوظ في تأخير أول توكن."}, "ernie-char-8k": {"description": "نموذج اللغة الكبير المخصص الذي طورته بايدو، مناسب لتطبيقات مثل NPC في الألعاب، محادثات خدمة العملاء، وأدوار الحوار، حيث يتميز بأسلوب شخصيات واضح ومتسق، وقدرة قوية على اتباع التعليمات، وأداء استدلال ممتاز."}, "ernie-char-fiction-8k": {"description": "نموذج اللغة الكبير المخصص الذي طورته بايدو، مناسب لتطبيقات مثل NPC في الألعاب، محادثات خدمة العملاء، وأدوار الحوار، حيث يتميز بأسلوب شخصيات واضح ومتسق، وقدرة قوية على اتباع التعليمات، وأداء استدلال ممتاز."}, "ernie-irag-edit": {"description": "نموذج تحرير الصور ERNIE iRAG المطور ذاتيًا من Baidu يدعم عمليات مثل المسح (إزالة الكائنات)، إعادة الرسم (إعادة رسم الكائنات)، والتنوع (توليد متغيرات) بناءً على الصور."}, "ernie-lite-8k": {"description": "ERNIE Lite هو نموذج اللغة الكبير الخفيف الذي طورته بايدو، يجمع بين أداء النموذج الممتاز وأداء الاستدلال، مناسب للاستخدام مع بطاقات تسريع الذكاء الاصطناعي ذات القدرة الحاسوبية المنخفضة."}, "ernie-lite-pro-128k": {"description": "نموذج اللغة الكبير الخفيف الذي طورته بايدو، يجمع بين أداء النموذج الممتاز وأداء الاستدلال، ويظهر أداءً أفضل من ERNIE Lite، مناسب للاستخدام مع بطاقات تسريع الذكاء الاصطناعي ذات القدرة الحاسوبية المنخفضة."}, "ernie-novel-8k": {"description": "نموذج اللغة الكبير العام الذي طورته بايدو، يظهر مزايا واضحة في القدرة على كتابة روايات، ويمكن استخدامه أيضًا في مشاهد مثل المسرحيات القصيرة والأفلام."}, "ernie-speed-128k": {"description": "نموذج اللغة الكبير عالي الأداء الذي طورته بايدو، والذي تم إصداره في عام 2024، يتمتع بقدرات عامة ممتازة، مناسب كنموذج أساسي للتعديل، مما يساعد على معالجة مشكلات المشاهد المحددة بشكل أفضل، ويظهر أداءً ممتازًا في الاستدلال."}, "ernie-speed-pro-128k": {"description": "نموذج اللغة الكبير عالي الأداء الذي طورته بايدو، والذي تم إصداره في عام 2024، يتمتع بقدرات عامة ممتازة، ويظهر أداءً أفضل من ERNIE Speed، مناسب كنموذج أساسي للتعديل، مما يساعد على معالجة مشكلات المشاهد المحددة بشكل أفضل، ويظهر أداءً ممتازًا في الاستدلال."}, "ernie-tiny-8k": {"description": "ERNIE Tiny هو نموذج اللغة الكبير عالي الأداء الذي طورته بايدو، وتكاليف النشر والتعديل هي الأدنى بين نماذج سلسلة Wenxin."}, "ernie-x1-32k": {"description": "يمتلك قدرة أقوى على الفهم والتخطيط والتفكير والتطور. كنموذج تفكير عميق شامل، يتميز Wenxin X1 بالدقة والإبداع والبلاغة، ويظهر أداءً متميزًا في مجالات مثل الأسئلة والأجوبة باللغة الصينية، والإبداع الأدبي، وكتابة النصوص، والحوار اليومي، والاستدلال المنطقي، والحسابات المعقدة، واستخدام الأدوات."}, "ernie-x1-32k-preview": {"description": "نموذج Ernie X1 الكبير يتمتع بقدرات أقوى في الفهم، التخطيط، التفكير النقدي، والتطور. كنموذج تفكير عميق أكثر شمولاً، يجمع Ernie X1 بين الدقة، الإبداع، والبلاغة، ويتميز بشكل خاص في أسئلة المعرفة باللغة الصينية، الإبداع الأدبي، كتابة النصوص، المحادثات اليومية، الاستدلال المنطقي، الحسابات المعقدة، واستدعاء الأدوات."}, "ernie-x1-turbo-32k": {"description": "يتميز هذا النموذج بأداء أفضل مقارنةً بـ ERNIE-X1-32K."}, "flux-1-schnell": {"description": "نموذج توليد صور نصية يحتوي على 12 مليار معلمة طورته Black Forest Labs، يستخدم تقنية تقطير الانتشار التنافسي الكامن، قادر على توليد صور عالية الجودة في 1 إلى 4 خطوات. أداء النموذج يضاهي البدائل المغلقة المصدر، ومتاح بموجب ترخيص Apache-2.0 للاستخدام الشخصي، البحثي والتجاري."}, "flux-dev": {"description": "FLUX.1 [dev] هو نموذج مفتوح المصدر للأوزان المكررة موجه للتطبيقات غير التجارية. يحافظ على جودة الصور وقدرة اتباع التعليمات مماثلة لإصدار FLUX الاحترافي، مع كفاءة تشغيل أعلى. مقارنة بالنماذج القياسية ذات الحجم المماثل، يستخدم الموارد بشكل أكثر فعالية."}, "flux-kontext/dev": {"description": "نموذج تحرير الصور Frontier."}, "flux-merged": {"description": "نموذج FLUX.1-merged يجمع بين ميزات العمق التي استكشفتها نسخة \"DEV\" أثناء التطوير ومزايا التنفيذ السريع التي تمثلها نسخة \"Schnell\". من خلال هذا الدمج، يعزز FLUX.1-merged حدود أداء النموذج ويوسع نطاق تطبيقاته."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] قادر على معالجة النصوص والصور المرجعية كمدخلات، مما يتيح تحريرًا محليًا مستهدفًا وتحولات معقدة للمشهد الكلي بسلاسة."}, "flux-schnell": {"description": "FLUX.1 [schnell] هو النموذج المفتوح المصدر الأكثر تقدمًا حاليًا في فئة النماذج قليلة الخطوات، متفوقًا على المنافسين وحتى على نماذج غير مكررة قوية مثل Midjourney v6.0 وDALL·E 3 (HD). تم ضبط النموذج خصيصًا للحفاظ على تنوع المخرجات الكامل من مرحلة ما قبل التدريب، ويحقق تحسينات ملحوظة في جودة الصورة، الالتزام بالتعليمات، التغيرات في الحجم/النسبة، معالجة الخطوط وتنوع المخرجات مقارنة بأحدث النماذج في السوق، مما يوفر تجربة توليد صور إبداعية أكثر ثراءً وتنوعًا للمستخدمين."}, "flux.1-schnell": {"description": "محول تدفق مصحح يحتوي على 12 مليار معلمة، قادر على توليد الصور بناءً على الوصف النصي."}, "flux/schnell": {"description": "FLUX.1 [schnell] هو نموذج محول متدفق يحتوي على 12 مليار معلمة، قادر على توليد صور عالية الجودة من النص في 1 إلى 4 خطوات، مناسب للاستخدام الشخصي والتجاري."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (تعديل) يوفر أداءً مستقرًا وقابلًا للتعديل، وهو الخيار المثالي لحلول المهام المعقدة."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (تعديل) يوفر دعمًا ممتازًا متعدد الوسائط، مع التركيز على الحلول الفعالة للمهام المعقدة."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro هو نموذج ذكاء اصطناعي عالي الأداء من Google، مصمم للتوسع في مجموعة واسعة من المهام."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 هو نموذج متعدد الوسائط فعال، يدعم التوسع في التطبيقات الواسعة."}, "gemini-1.5-flash-002": {"description": "جمني 1.5 فلاش 002 هو نموذج متعدد الوسائط فعال، يدعم توسيع التطبيقات على نطاق واسع."}, "gemini-1.5-flash-8b": {"description": "جمني 1.5 فلاش 8B هو نموذج متعدد الوسائط عالي الكفاءة، يدعم مجموعة واسعة من التطبيقات."}, "gemini-1.5-flash-8b-exp-0924": {"description": "جمني 1.5 فلاش 8B 0924 هو النموذج التجريبي الأحدث، حيث حقق تحسينات ملحوظة في الأداء في حالات الاستخدام النصية ومتعددة الوسائط."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B هو نموذج متعدد الوسائط فعال يدعم التوسع في مجموعة واسعة من التطبيقات."}, "gemini-1.5-flash-exp-0827": {"description": "جيميني 1.5 فلاش 0827 يقدم قدرة معالجة متعددة الوسائط محسنة، مناسب لمجموعة متنوعة من سيناريوهات المهام المعقدة."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash هو أحدث نموذج ذكاء اصطناعي متعدد الوسائط من Google، يتمتع بقدرات معالجة سريعة، ويدعم إدخال النصوص والصور والفيديو، مما يجعله مناسبًا للتوسع الفعال في مجموعة متنوعة من المهام."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 هو حل ذكاء اصطناعي متعدد الوسائط قابل للتوسع، يدعم مجموعة واسعة من المهام المعقدة."}, "gemini-1.5-pro-002": {"description": "جمني 1.5 برو 002 هو النموذج الأحدث الجاهز للإنتاج، حيث يقدم مخرجات ذات جودة أعلى، مع تحسينات ملحوظة خاصة في الرياضيات والسياقات الطويلة والمهام البصرية."}, "gemini-1.5-pro-exp-0801": {"description": "جيميني 1.5 برو 0801 يوفر قدرة معالجة متعددة الوسائط ممتازة، مما يوفر مرونة أكبر لتطوير التطبيقات."}, "gemini-1.5-pro-exp-0827": {"description": "جيميني 1.5 برو 0827 يدمج أحدث تقنيات التحسين، مما يوفر قدرة معالجة بيانات متعددة الوسائط أكثر كفاءة."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro يدعم ما يصل إلى 2 مليون توكن، وهو الخيار المثالي للنماذج المتوسطة الحجم متعددة الوسائط، مناسب لدعم المهام المعقدة من جوانب متعددة."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash يقدم ميزات وتحسينات من الجيل التالي، بما في ذلك سرعة فائقة، واستخدام أدوات أصلية، وتوليد متعدد الوسائط، ونافذة سياق تصل إلى 1M توكن."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash يقدم ميزات وتحسينات من الجيل التالي، بما في ذلك سرعة فائقة، واستخدام أدوات أصلية، وتوليد متعدد الوسائط، ونافذة سياق تصل إلى 1M توكن."}, "gemini-2.0-flash-exp": {"description": "نموذج جمنيس 2.0 فلاش، تم تحسينه لتحقيق أهداف مثل الكفاءة من حيث التكلفة وانخفاض الكمون."}, "gemini-2.0-flash-exp-image-generation": {"description": "نموذج تجريبي Gemini 2.0 Flash، يدعم توليد الصور"}, "gemini-2.0-flash-lite": {"description": "نموذج جمنّي 2.0 فلاش هو نسخة معدلة، تم تحسينها لتحقيق الكفاءة من حيث التكلفة والحد من التأخير."}, "gemini-2.0-flash-lite-001": {"description": "نموذج جمنّي 2.0 فلاش هو نسخة معدلة، تم تحسينها لتحقيق الكفاءة من حيث التكلفة والحد من التأخير."}, "gemini-2.0-flash-preview-image-generation": {"description": "نموذج معاينة Gemini 2.0 Flash، يدعم توليد الصور"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash هو نموذج Google الأكثر فعالية من حيث التكلفة، ويوفر وظائف شاملة."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-<PERSON><PERSON> ه<PERSON> أصغر وأفضل نموذج من حيث التكلفة من Google، مصمم للاستخدام على نطاق واسع."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview هو أصغر وأكفأ نموذج من Google، مصمم للاستخدام واسع النطاق."}, "gemini-2.5-flash-preview-04-17": {"description": "معاينة فلاش جمنّي 2.5 هي النموذج الأكثر كفاءة من جوجل، حيث تقدم مجموعة شاملة من الميزات."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview هو نموذج Google الأكثر فعالية من حيث التكلفة، يقدم وظائف شاملة."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro هو نموذج التفكير الأكثر تقدمًا من Google، قادر على استدلال المشكلات المعقدة في البرمجة والرياضيات ومجالات STEM، بالإضافة إلى تحليل مجموعات البيانات الكبيرة ومستودعات الأكواد والوثائق باستخدام سياق طويل."}, "gemini-2.5-pro-preview-03-25": {"description": "معاينة Gemini 2.5 Pro هي نموذج التفكير الأكثر تقدمًا من Google، قادر على الاستدلال حول الشيفرات، الرياضيات، والمشكلات المعقدة في مجالات STEM، بالإضافة إلى تحليل مجموعات البيانات الكبيرة، مكتبات الشيفرات، والمستندات باستخدام سياقات طويلة."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview هو نموذج التفكير الأكثر تقدمًا من Google، قادر على الاستدلال حول الشيفرات، الرياضيات، والمشكلات المعقدة في مجالات STEM، بالإضافة إلى تحليل مجموعات البيانات الكبيرة، ومكتبات الشيفرات، والمستندات باستخدام سياقات طويلة."}, "gemini-2.5-pro-preview-06-05": {"description": "جيميني 2.5 برو بريڤيو هو أحدث نموذج تفكيري من جوجل، قادر على استنتاج حلول للمشكلات المعقدة في مجالات البرمجة، الرياضيات، والعلوم والتكنولوجيا والهندسة والرياضيات (STEM)، بالإضافة إلى تحليل مجموعات بيانات كبيرة، قواعد بيانات البرمجة، والوثائق باستخدام سياق طويل."}, "gemma-7b-it": {"description": "Gemma 7B مناسب لمعالجة المهام المتوسطة والصغيرة، ويجمع بين الكفاءة من حيث التكلفة."}, "gemma2": {"description": "Gemma 2 هو نموذج فعال أطلقته Google، يغطي مجموعة متنوعة من سيناريوهات التطبيقات من التطبيقات الصغيرة إلى معالجة البيانات المعقدة."}, "gemma2-9b-it": {"description": "Gemma 2 9B هو نموذج محسن لمهام محددة ودمج الأدوات."}, "gemma2:27b": {"description": "Gemma 2 هو نموذج فعال أطلقته Google، يغطي مجموعة متنوعة من سيناريوهات التطبيقات من التطبيقات الصغيرة إلى معالجة البيانات المعقدة."}, "gemma2:2b": {"description": "Gemma 2 هو نموذج فعال أطلقته Google، يغطي مجموعة متنوعة من سيناريوهات التطبيقات من التطبيقات الصغيرة إلى معالجة البيانات المعقدة."}, "generalv3": {"description": "Spark Pro هو نموذج لغوي كبير عالي الأداء تم تحسينه للحقول المهنية، يركز على الرياضيات، والبرمجة، والطب، والتعليم، ويدعم البحث عبر الإنترنت بالإضافة إلى المكونات الإضافية المدمجة مثل الطقس والتاريخ. يظهر النموذج المحسن أداءً ممتازًا وكفاءة في الإجابة على الأسئلة المعقدة، وفهم اللغة، وإنشاء نصوص عالية المستوى، مما يجعله الخيار المثالي لتطبيقات الاستخدام المهني."}, "generalv3.5": {"description": "Spark3.5 Max هو الإصدار الأكثر شمولاً، يدعم البحث عبر الإنترنت والعديد من المكونات الإضافية المدمجة. تعزز قدراته الأساسية المحسنة، بالإضافة إلى إعدادات الأدوار النظامية ووظائف استدعاء الدوال، أداؤه بشكل استثنائي في مجموعة متنوعة من سيناريوهات التطبيقات المعقدة."}, "glm-4": {"description": "GLM-4 هو الإصدار القديم الذي تم إصداره في يناير 2024، وقد تم استبداله الآن بـ GLM-4-0520 الأقوى."}, "glm-4-0520": {"description": "GLM-4-0520 هو أحدث إصدار من النموذج، مصمم للمهام المعقدة والمتنوعة، ويظهر أداءً ممتازًا."}, "glm-4-9b-chat": {"description": "يظهر GLM-4-9B-Chat أداءً عاليًا في مجالات متعددة مثل الدلالات والرياضيات والاستدلال والترميز والمعرفة. كما أنه مزود بقدرات تصفح الويب وتنفيذ الشيفرات واستدعاء الأدوات المخصصة واستدلال النصوص الطويلة. يدعم 26 لغة بما في ذلك اليابانية والكورية والألمانية."}, "glm-4-air": {"description": "GLM-4-Air هو إصدار ذو قيمة عالية، يتمتع بأداء قريب من GLM-4، ويقدم سرعة عالية وسعرًا معقولًا."}, "glm-4-air-250414": {"description": "GLM-4-Air هو إصدار ذو قيمة عالية، أداءه قريب من GLM-4، يوفر سرعة عالية وسعرًا معقولًا."}, "glm-4-airx": {"description": "GLM-4-<PERSON><PERSON> يقدم إصدارًا فعالًا من GLM-4-Air، حيث تصل سرعة الاستدلال إلى 2.6 مرة."}, "glm-4-alltools": {"description": "GLM-4-AllTools هو نموذج وكيل متعدد الوظائف، تم تحسينه لدعم تخطيط التعليمات المعقدة واستدعاء الأدوات، مثل تصفح الإنترنت، وتفسير الشيفرة، وتوليد النصوص، مناسب لتنفيذ المهام المتعددة."}, "glm-4-flash": {"description": "GLM-4-<PERSON> <PERSON><PERSON> الخيار المثالي لمعالجة المهام البسيطة، حيث يتمتع بأسرع سرعة وأفضل سعر."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON> <PERSON><PERSON> الخيار المثالي لمعالجة المهام البسيطة، الأسرع والأكثر مجانية."}, "glm-4-flashx": {"description": "GLM-4-<PERSON><PERSON> هو إصدار معزز من Flash، يتميز بسرعة استدلال فائقة."}, "glm-4-long": {"description": "GLM-4-<PERSON> يدعم إدخالات نصية طويلة جدًا، مما يجعله مناسبًا للمهام الذاكرية ومعالجة الوثائق الكبيرة."}, "glm-4-plus": {"description": "GLM-4-Plus كنموذج رائد ذكي، يتمتع بقدرات قوية في معالجة النصوص الطويلة والمهام المعقدة، مع تحسين شامل في الأداء."}, "glm-4.1v-thinking-flash": {"description": "سلسلة نماذج GLM-4.1V-Thinking هي أقوى نماذج اللغة البصرية المعروفة على مستوى 10 مليارات معلمة، وتدمج مهام اللغة البصرية المتقدمة من نفس المستوى، بما في ذلك فهم الفيديو، الأسئلة والأجوبة على الصور، حل المسائل العلمية، التعرف على النصوص OCR، تفسير الوثائق والرسوم البيانية، وكلاء واجهة المستخدم الرسومية، ترميز صفحات الويب الأمامية، والتثبيت الأرضي، وغيرها. تتفوق قدرات هذه المهام على نموذج Qwen2.5-VL-72B الذي يحتوي على أكثر من 8 أضعاف عدد المعلمات. من خلال تقنيات التعلم المعزز الرائدة، يتقن النموذج تحسين دقة وإثراء الإجابات عبر استدلال سلسلة التفكير، متفوقًا بشكل ملحوظ على النماذج التقليدية غير المعتمدة على التفكير من حيث النتائج النهائية وقابلية التفسير."}, "glm-4.1v-thinking-flashx": {"description": "سلسلة نماذج GLM-4.1V-Thinking هي أقوى نماذج اللغة البصرية المعروفة على مستوى 10 مليارات معلمة، وتدمج مهام اللغة البصرية المتقدمة من نفس المستوى، بما في ذلك فهم الفيديو، الأسئلة والأجوبة على الصور، حل المسائل العلمية، التعرف على النصوص OCR، تفسير الوثائق والرسوم البيانية، وكلاء واجهة المستخدم الرسومية، ترميز صفحات الويب الأمامية، والتثبيت الأرضي، وغيرها. تتفوق قدرات هذه المهام على نموذج Qwen2.5-VL-72B الذي يحتوي على أكثر من 8 أضعاف عدد المعلمات. من خلال تقنيات التعلم المعزز الرائدة، يتقن النموذج تحسين دقة وإثراء الإجابات عبر استدلال سلسلة التفكير، متفوقًا بشكل ملحوظ على النماذج التقليدية غير المعتمدة على التفكير من حيث النتائج النهائية وقابلية التفسير."}, "glm-4.5": {"description": "أحدث نموذج رائد من Z<PERSON>hu، يدعم تبديل وضع التفكير، ويحقق مستوى SOTA بين النماذج المفتوحة المصدر في القدرات الشاملة، مع طول سياق يصل إلى 128 ألف رمز."}, "glm-4.5-air": {"description": "نسخة خفيفة من GLM-4.5، تجمع بين الأداء والقيمة، وتدعم التبديل المرن بين نماذج التفكير المختلطة."}, "glm-4.5-airx": {"description": "نسخة فائقة السرعة من GLM-4.5-Air، تستجيب بسرعة أكبر، مصممة لتلبية الطلبات الكبيرة عالية السرعة."}, "glm-4.5-flash": {"description": "نسخة مجانية من GLM-4.5، تقدم أداءً ممتازًا في الاستدلال، البرمجة، والوكيل."}, "glm-4.5-x": {"description": "نسخة فائقة السرعة من GLM-4.5، تجمع بين أداء قوي وسرعة توليد تصل إلى 100 رمز في الثانية."}, "glm-4v": {"description": "GLM-4V يوفر قدرات قوية في فهم الصور والاستدلال، ويدعم مجموعة متنوعة من المهام البصرية."}, "glm-4v-flash": {"description": "يتميز GLM-4V-Flash بتركيزه على فهم الصور الفردية بكفاءة، وهو مناسب لسيناريوهات تحليل الصور السريعة، مثل تحليل الصور في الوقت الفعلي أو معالجة الصور بكميات كبيرة."}, "glm-4v-plus": {"description": "GLM-4V-Plus يتمتع بقدرة على فهم محتوى الفيديو والصور المتعددة، مما يجعله مناسبًا للمهام متعددة الوسائط."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus يتمتع بقدرة على فهم محتوى الفيديو والصور المتعددة، مناسب للمهام متعددة الوسائط."}, "glm-z1-air": {"description": "نموذج استدلال: يتمتع بقدرة استدلال قوية، مناسب للمهام التي تتطلب استدلالًا عميقًا."}, "glm-z1-airx": {"description": "استدلال فائق السرعة: يتمتع بسرعة استدلال فائقة وأداء استدلال قوي."}, "glm-z1-flash": {"description": "سلسلة GLM-Z1 تتميز بقدرات استدلال معقدة قوية، وتتفوق في مجالات الاستدلال المنطقي، الرياضيات، والبرمجة."}, "glm-z1-flashx": {"description": "سرعة عالية وتكلفة منخفضة: نسخة محسنة من Flash، سرعة استدلال فائقة، وضمان تزامن أسرع."}, "glm-zero-preview": {"description": "يمتلك GLM-Zero-Preview قدرة قوية على الاستدلال المعقد، ويظهر أداءً ممتازًا في مجالات الاستدلال المنطقي، والرياضيات، والبرمجة."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash يقدم ميزات وتحسينات من الجيل التالي، بما في ذلك سرعة فائقة، واستخدام أدوات أصلية، وتوليد متعدد الوسائط، ونافذة سياق تصل إلى 1M توكن."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental هو أحدث نموذج ذكاء اصطناعي متعدد الوسائط من Google، مع تحسينات ملحوظة في الجودة مقارنة بالإصدارات السابقة، خاصة في المعرفة العالمية، الشيفرات، والسياقات الطويلة."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash هو النموذج الرئيسي الأكثر تقدمًا من Google، مصمم خصيصًا للمهام المتقدمة في الاستدلال، الترميز، الرياضيات والعلوم. يحتوي على قدرة مدمجة على \"التفكير\"، مما يمكنه من تقديم استجابات بدقة أعلى ومعالجة سياقية أكثر تفصيلاً.\n\nملاحظة: يحتوي هذا النموذج على نسختين: نسخة التفكير ونسخة غير التفكير. تختلف تكلفة الإخراج بشكل ملحوظ بناءً على تفعيل قدرة التفكير. إذا اخترت النسخة القياسية (بدون لاحقة \":thinking\"), سيتجنب النموذج بوضوح توليد رموز التفكير.\n\nلاستغلال قدرة التفكير واستلام رموز التفكير، يجب عليك اختيار النسخة \":thinking\"، والتي ستؤدي إلى تكلفة إخراج أعلى للتفكير.\n\nبالإضافة إلى ذلك، يمكن تكوين Gemini 2.5 Flash من خلال معلمة \"الحد الأقصى لعدد رموز الاستدلال\" كما هو موضح في الوثائق (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash هو النموذج الرائد الأكثر تقدمًا من Google، مصمم للاستدلال المتقدم، الترميز، المهام الرياضية والعلمية. يحتوي على قدرة \"التفكير\" المدمجة، مما يمكّنه من تقديم استجابات بدقة أعلى ومعالجة سياقات أكثر تفصيلاً.\n\nملاحظة: يحتوي هذا النموذج على نوعين: التفكير وغير التفكير. تختلف تسعير الإخراج بشكل ملحوظ بناءً على ما إذا كانت قدرة التفكير مفعلة. إذا اخترت النوع القياسي (بدون لاحقة \" :thinking \")، سيتجنب النموذج بشكل صريح توليد رموز التفكير.\n\nلاستغلال قدرة التفكير واستقبال رموز التفكير، يجب عليك اختيار النوع \" :thinking \"، مما سيؤدي إلى تسعير إخراج تفكير أعلى.\n\nبالإضافة إلى ذلك، يمكن تكوين Gemini 2.5 Flash من خلال معلمة \"الحد الأقصى لعدد رموز الاستدلال\"، كما هو موضح في الوثائق (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash هو النموذج الرائد الأكثر تقدمًا من Google، مصمم للاستدلال المتقدم، الترميز، المهام الرياضية والعلمية. يحتوي على قدرة \"التفكير\" المدمجة، مما يمكّنه من تقديم استجابات بدقة أعلى ومعالجة سياقات أكثر تفصيلاً.\n\nملاحظة: يحتوي هذا النموذج على نوعين: التفكير وغير التفكير. تختلف تسعير الإخراج بشكل ملحوظ بناءً على ما إذا كانت قدرة التفكير مفعلة. إذا اخترت النوع القياسي (بدون لاحقة \" :thinking \")، سيتجنب النموذج بشكل صريح توليد رموز التفكير.\n\nلاستغلال قدرة التفكير واستقبال رموز التفكير، يجب عليك اختيار النوع \" :thinking \"، مما سيؤدي إلى تسعير إخراج تفكير أعلى.\n\nبالإضافة إلى ذلك، يمكن تكوين Gemini 2.5 Flash من خلال معلمة \"الحد الأقصى لعدد رموز الاستدلال\"، كما هو موضح في الوثائق (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro هو نموذج التفكير الأكثر تقدمًا من Google، قادر على الاستدلال في مسائل معقدة في البرمجة، الرياضيات ومجالات العلوم والتكنولوجيا والهندسة والرياضيات (STEM)، بالإضافة إلى استخدام السياق الطويل لتحليل مجموعات بيانات كبيرة، قواعد الشيفرة والمستندات."}, "google/gemini-2.5-pro-preview": {"description": "معاينة Gemini 2.5 Pro هي أحدث نموذج تفكيري من Google، قادر على استنتاج المشكلات المعقدة في مجالات البرمجة والرياضيات والعلوم والتكنولوجيا والهندسة والرياضيات (STEM)، بالإضافة إلى استخدام سياق طويل لتحليل مجموعات البيانات الكبيرة، وقواعد الشيفرة، والوثائق."}, "google/gemini-flash-1.5": {"description": "يقدم Gemini 1.5 Flash قدرات معالجة متعددة الوسائط محسّنة، مناسبة لمجموعة متنوعة من سيناريوهات المهام المعقدة."}, "google/gemini-pro-1.5": {"description": "يجمع Gemini 1.5 Pro بين أحدث تقنيات التحسين، مما يوفر قدرة معالجة بيانات متعددة الوسائط بشكل أكثر كفاءة."}, "google/gemma-2-27b": {"description": "Gemma 2 هو نموذج فعال أطلقته Google، يغطي مجموعة متنوعة من سيناريوهات التطبيقات من التطبيقات الصغيرة إلى معالجة البيانات المعقدة."}, "google/gemma-2-27b-it": {"description": "Gemma 2 تستمر في مفهوم التصميم الخفيف والفعال."}, "google/gemma-2-2b-it": {"description": "نموذج تحسين التعليمات الخفيف من Google"}, "google/gemma-2-9b": {"description": "Gemma 2 هو نموذج فعال أطلقته Google، يغطي مجموعة متنوعة من سيناريوهات التطبيقات من التطبيقات الصغيرة إلى معالجة البيانات المعقدة."}, "google/gemma-2-9b-it": {"description": "Gemma 2 هو سلسلة نماذج نصية مفتوحة المصدر خفيفة الوزن من Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 هو سلسلة نماذج نصية مفتوحة المصدر خفيفة الوزن من Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) يوفر قدرة أساسية على معالجة التعليمات، مناسب للتطبيقات الخفيفة."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B هو نموذج لغة مفتوح المصدر من جوجل، وضع معايير جديدة في الكفاءة والأداء."}, "google/gemma-3-27b-it": {"description": "جيمّا 3 27B هو نموذج لغوي مفتوح المصدر من جوجل، وقد وضع معايير جديدة من حيث الكفاءة والأداء."}, "gpt-3.5-turbo": {"description": "نموذج GPT 3.5 Turbo، مناسب لمجموعة متنوعة من مهام توليد وفهم النصوص، يشير حاليًا إلى gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "نموذج GPT 3.5 Turbo، مناسب لمجموعة متنوعة من مهام توليد وفهم النصوص، يشير حاليًا إلى gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "نموذج GPT 3.5 Turbo، مناسب لمجموعة متنوعة من مهام توليد وفهم النصوص، يشير حاليًا إلى gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "نموذج GPT 3.5 Turbo، مناسب لمجموعة متنوعة من مهام توليد وفهم النصوص، يشير حاليًا إلى gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "جي بي تي 3.5 توربو، نموذج فعال مقدم من OpenAI، مناسب للدردشة ومهام توليد النصوص، يدعم استدعاءات الوظائف المتوازية."}, "gpt-35-turbo-16k": {"description": "جي بي تي 3.5 توربو 16k، نموذج توليد نصوص عالي السعة، مناسب للمهام المعقدة."}, "gpt-4": {"description": "يوفر GPT-4 نافذة سياقية أكبر، مما يمكنه من معالجة إدخالات نصية أطول، مما يجعله مناسبًا للمواقف التي تتطلب دمج معلومات واسعة وتحليل البيانات."}, "gpt-4-0125-preview": {"description": "نموذج GPT-4 Turbo الأحدث يتمتع بقدرات بصرية. الآن، يمكن استخدام الطلبات البصرية باستخدام نمط JSON واستدعاء الوظائف. GPT-4 Turbo هو إصدار معزز يوفر دعمًا فعالًا من حيث التكلفة للمهام متعددة الوسائط. يجد توازنًا بين الدقة والكفاءة، مما يجعله مناسبًا للتطبيقات التي تتطلب تفاعلات في الوقت الحقيقي."}, "gpt-4-0613": {"description": "يوفر GPT-4 نافذة سياقية أكبر، مما يمكنه من معالجة إدخالات نصية أطول، مما يجعله مناسبًا للمواقف التي تتطلب دمج معلومات واسعة وتحليل البيانات."}, "gpt-4-1106-preview": {"description": "نموذج GPT-4 Turbo الأحدث يتمتع بقدرات بصرية. الآن، يمكن استخدام الطلبات البصرية باستخدام نمط JSON واستدعاء الوظائف. GPT-4 Turbo هو إصدار معزز يوفر دعمًا فعالًا من حيث التكلفة للمهام متعددة الوسائط. يجد توازنًا بين الدقة والكفاءة، مما يجعله مناسبًا للتطبيقات التي تتطلب تفاعلات في الوقت الحقيقي."}, "gpt-4-32k": {"description": "يوفر GPT-4 نافذة سياقية أكبر، مما يمكنه من معالجة إدخالات نصية أطول، مما يجعله مناسبًا للمواقف التي تتطلب دمج معلومات واسعة وتحليل البيانات."}, "gpt-4-32k-0613": {"description": "يوفر GPT-4 نافذة سياقية أكبر، مما يمكنه من معالجة إدخالات نصية أطول، مما يجعله مناسبًا للمواقف التي تتطلب دمج معلومات واسعة وتحليل البيانات."}, "gpt-4-turbo": {"description": "نموذج GPT-4 Turbo الأحدث يتمتع بقدرات بصرية. الآن، يمكن استخدام الطلبات البصرية باستخدام نمط JSON واستدعاء الوظائف. GPT-4 Turbo هو إصدار معزز يوفر دعمًا فعالًا من حيث التكلفة للمهام متعددة الوسائط. يجد توازنًا بين الدقة والكفاءة، مما يجعله مناسبًا للتطبيقات التي تتطلب تفاعلات في الوقت الحقيقي."}, "gpt-4-turbo-2024-04-09": {"description": "نموذج GPT-4 Turbo الأحدث يتمتع بقدرات بصرية. الآن، يمكن استخدام الطلبات البصرية باستخدام نمط JSON واستدعاء الوظائف. GPT-4 Turbo هو إصدار معزز يوفر دعمًا فعالًا من حيث التكلفة للمهام متعددة الوسائط. يجد توازنًا بين الدقة والكفاءة، مما يجعله مناسبًا للتطبيقات التي تتطلب تفاعلات في الوقت الحقيقي."}, "gpt-4-turbo-preview": {"description": "نموذج GPT-4 Turbo الأحدث يتمتع بقدرات بصرية. الآن، يمكن استخدام الطلبات البصرية باستخدام نمط JSON واستدعاء الوظائف. GPT-4 Turbo هو إصدار معزز يوفر دعمًا فعالًا من حيث التكلفة للمهام متعددة الوسائط. يجد توازنًا بين الدقة والكفاءة، مما يجعله مناسبًا للتطبيقات التي تتطلب تفاعلات في الوقت الحقيقي."}, "gpt-4-vision-preview": {"description": "نموذج GPT-4 Turbo الأحدث يتمتع بقدرات بصرية. الآن، يمكن استخدام الطلبات البصرية باستخدام نمط JSON واستدعاء الوظائف. GPT-4 Turbo هو إصدار معزز يوفر دعمًا فعالًا من حيث التكلفة للمهام متعددة الوسائط. يجد توازنًا بين الدقة والكفاءة، مما يجعله مناسبًا للتطبيقات التي تتطلب تفاعلات في الوقت الحقيقي."}, "gpt-4.1": {"description": "GPT-4.1 هو نموذجنا الرائد للمهام المعقدة. إنه مثالي لحل المشكلات عبر مجالات متعددة."}, "gpt-4.1-mini": {"description": "يوفر GPT-4.1 mini توازنًا بين الذكاء والسرعة والتكلفة، مما يجعله نموذجًا جذابًا للعديد من الاستخدامات."}, "gpt-4.1-nano": {"description": "يوفر GPT-4.1 mini توازنًا بين الذكاء والسرعة والتكلفة، مما يجعله نموذجًا جذابًا للعديد من الاستخدامات."}, "gpt-4.5-preview": {"description": "نسخة المعاينة البحثية لـ GPT-4.5، وهي أكبر وأقوى نموذج GPT لدينا حتى الآن. تتمتع بمعرفة واسعة عن العالم وتفهم أفضل لنوايا المستخدم، مما يجعلها بارعة في المهام الإبداعية والتخطيط الذاتي. يمكن لـ GPT-4.5 قبول المدخلات النصية والصورية وتوليد مخرجات نصية (بما في ذلك المخرجات الهيكلية). تدعم ميزات المطورين الأساسية مثل استدعاء الدوال، وواجهة برمجة التطبيقات الجماعية، والمخرجات المتدفقة. تتألق GPT-4.5 بشكل خاص في المهام التي تتطلب التفكير الإبداعي، والتفكير المفتوح، والحوار (مثل الكتابة، والتعلم، أو استكشاف أفكار جديدة). تاريخ انتهاء المعرفة هو أكتوبر 2023."}, "gpt-4o": {"description": "ChatGPT-4o هو نموذج ديناميكي يتم تحديثه في الوقت الحقيقي للحفاظ على أحدث إصدار. يجمع بين فهم اللغة القوي وقدرات التوليد، مما يجعله مناسبًا لمجموعة واسعة من التطبيقات، بما في ذلك خدمة العملاء والتعليم والدعم الفني."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o هو نموذج ديناميكي يتم تحديثه في الوقت الحقيقي للحفاظ على أحدث إصدار. يجمع بين فهم اللغة القوي وقدرات التوليد، مما يجعله مناسبًا لمجموعة واسعة من التطبيقات، بما في ذلك خدمة العملاء والتعليم والدعم الفني."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o هو نموذج ديناميكي يتم تحديثه في الوقت الحقيقي للحفاظ على أحدث إصدار. يجمع بين فهم اللغة القوي وقدرات التوليد، مما يجعله مناسبًا لمجموعة واسعة من التطبيقات، بما في ذلك خدمة العملاء والتعليم والدعم الفني."}, "gpt-4o-2024-11-20": {"description": "تشات جي بي تي-4o هو نموذج ديناميكي يتم تحديثه في الوقت الفعلي للحفاظ على أحدث إصدار. يجمع بين الفهم اللغوي القوي وقدرة التوليد، مما يجعله مناسبًا لتطبيقات واسعة النطاق، بما في ذلك خدمة العملاء والتعليم والدعم الفني."}, "gpt-4o-audio-preview": {"description": "نموذج GPT-4o Audio، يدعم إدخال وإخراج الصوت."}, "gpt-4o-mini": {"description": "نموذج GPT-4o mini هو أحدث نموذج أطلقته OpenAI بعد GPT-4 Omni، ويدعم إدخال الصور والنصوص وإخراج النصوص. كأحد نماذجهم المتقدمة الصغيرة، فهو أرخص بكثير من النماذج الرائدة الأخرى في الآونة الأخيرة، وأرخص بأكثر من 60% من GPT-3.5 Turbo. يحتفظ بذكاء متقدم مع قيمة ممتازة. حصل GPT-4o mini على 82% في اختبار MMLU، وهو حاليًا يتفوق على GPT-4 في تفضيلات الدردشة."}, "gpt-4o-mini-audio-preview": {"description": "نموذج GPT-4o mini Audio، يدعم إدخال وإخراج الصوت."}, "gpt-4o-mini-realtime-preview": {"description": "الإصدار المصغر الفوري من GPT-4o، يدعم إدخال وإخراج الصوت والنص في الوقت الحقيقي."}, "gpt-4o-mini-search-preview": {"description": "نسخة معاينة بحث GPT-4o mini هي نموذج مدرب خصيصًا لفهم وتنفيذ استعلامات البحث على الويب، باستخدام واجهة برمجة تطبيقات Chat Completions. بالإضافة إلى رسوم الرموز، يتم فرض رسوم على استعلامات البحث على الويب لكل استدعاء أداة."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe هو نموذج تحويل الصوت إلى نص يستخدم GPT-4o لتفريغ الصوت. مقارنةً بنموذج Whisper الأصلي، يحسن معدل الخطأ في الكلمات ويعزز التعرف على اللغة والدقة. استخدمه للحصول على تفريغ أكثر دقة."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS هو نموذج تحويل النص إلى كلام، مبني على GPT-4o mini، يقدم إنتاج كلمات صوتية عالية الجودة بسعر أقل."}, "gpt-4o-realtime-preview": {"description": "الإصدار الفوري من GPT-4o، يدعم إدخال وإخراج الصوت والنص في الوقت الحقيقي."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "الإصدار الفوري من GPT-4o، يدعم إدخال وإخراج الصوت والنص في الوقت الحقيقي."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "نسخة GPT-4o الحية، تدعم الإدخال والإخراج الصوتي والنصي في الوقت الحقيقي."}, "gpt-4o-search-preview": {"description": "نسخة معاينة بحث GPT-4o هي نموذج مدرب خصيصًا لفهم وتنفيذ استعلامات البحث على الويب، باستخدام واجهة برمجة تطبيقات Chat Completions. بالإضافة إلى رسوم الرموز، يتم فرض رسوم على استعلامات البحث على الويب لكل استدعاء أداة."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe هو نموذج تحويل الصوت إلى نص يستخدم GPT-4o لتفريغ الصوت. مقارنةً بنموذج Whisper الأصلي، يحسن معدل الخطأ في الكلمات ويعزز التعرف على اللغة والدقة. استخدمه للحصول على تفريغ أكثر دقة."}, "gpt-image-1": {"description": "نموذج توليد الصور متعدد الوسائط الأصلي من ChatGPT"}, "grok-2-1212": {"description": "لقد تم تحسين هذا النموذج في الدقة، والامتثال للتعليمات، والقدرة على التعامل مع لغات متعددة."}, "grok-2-image-1212": {"description": "نموذج توليد الصور الأحدث لدينا قادر على توليد صور حيوية وواقعية بناءً على الأوامر النصية. يبرع في مجالات التسويق، وسائل التواصل الاجتماعي، والترفيه."}, "grok-2-vision-1212": {"description": "لقد تم تحسين هذا النموذج في الدقة، والامتثال للتعليمات، والقدرة على التعامل مع لغات متعددة."}, "grok-3": {"description": "نموذج رائد، بارع في استخراج البيانات، البرمجة، وتلخيص النصوص لتطبيقات المؤسسات، يمتلك معرفة عميقة في مجالات المالية، الطب، القانون، والعلوم."}, "grok-3-fast": {"description": "نموذج رائد، بارع في استخراج البيانات، البرمجة، وتلخيص النصوص لتطبيقات المؤسسات، يمتلك معرفة عميقة في مجالات المالية، الطب، القانون، والعلوم."}, "grok-3-mini": {"description": "نموذج خفيف الوزن، يفكر قبل المحادثة. سريع وذكي، مناسب للمهام المنطقية التي لا تتطلب معرفة متخصصة عميقة، ويستطيع تتبع مسار التفكير الأصلي."}, "grok-3-mini-fast": {"description": "نموذج خفيف الوزن، يفكر قبل المحادثة. سريع وذكي، مناسب للمهام المنطقية التي لا تتطلب معرفة متخصصة عميقة، ويستطيع تتبع مسار التفكير الأصلي."}, "grok-4": {"description": "نموذجنا الرائد الأحدث والأقوى، يتميز بأداء ممتاز في معالجة اللغة الطبيعية، الحسابات الرياضية، والاستدلال — إنه لاعب شامل مثالي."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B هو نموذج لغوي يجمع بين الإبداع والذكاء من خلال دمج عدة نماذج رائدة."}, "hunyuan-a13b": {"description": "هو أول نموذج استدلال مختلط من Hunyuan، نسخة مطورة من hunyuan-standard-256K، يحتوي على 80 مليار معلمة و13 مليار معلمة نشطة. الوضع الافتراضي هو وضع التفكير البطيء، ويدعم التبديل بين أوضاع التفكير السريع والبطيء عبر المعلمات أو التعليمات، حيث يتم التبديل بإضافة / no_think قبل الاستعلام. تم تحسين القدرات الشاملة مقارنة بالجيل السابق، مع تحسينات ملحوظة في الرياضيات، العلوم، فهم النصوص الطويلة، وقدرات الوكيل."}, "hunyuan-code": {"description": "نموذج توليد الشيفرة الأحدث من Hunyuan، تم تدريبه على نموذج أساسي من بيانات الشيفرة عالية الجودة بحجم 200B، مع تدريب عالي الجودة على بيانات SFT لمدة ستة أشهر، وزيادة طول نافذة السياق إلى 8K، ويحتل مرتبة متقدمة في مؤشرات التقييم التلقائي لتوليد الشيفرة في خمس لغات؛ كما أنه في الطليعة في تقييمات الشيفرة عالية الجودة عبر عشرة معايير في خمس لغات."}, "hunyuan-functioncall": {"description": "نموذج Hunyuan الأحدث من نوع MOE FunctionCall، تم تدريبه على بيانات FunctionCall عالية الجودة، مع نافذة سياق تصل إلى 32K، ويحتل مرتبة متقدمة في مؤشرات التقييم عبر عدة أبعاد."}, "hunyuan-large": {"description": "نموذج Hunyuan-large يحتوي على حوالي 389 مليار معلمة، مع حوالي 52 مليار معلمة نشطة، وهو أكبر نموذج MoE مفتوح المصدر في الصناعة من حيث حجم المعلمات وأفضلها من حيث الأداء."}, "hunyuan-large-longcontext": {"description": "يتفوق في معالجة المهام الطويلة مثل تلخيص الوثائق والأسئلة والأجوبة المتعلقة بالوثائق، كما يمتلك القدرة على معالجة مهام إنشاء النصوص العامة. يظهر أداءً ممتازًا في تحليل وإنشاء النصوص الطويلة، مما يمكنه من التعامل بفعالية مع متطلبات معالجة المحتوى الطويل المعقد والمفصل."}, "hunyuan-large-vision": {"description": "هذا النموذج مناسب لمشاهد فهم الصور والنصوص، وهو نموذج لغوي بصري كبير مبني على تدريب Hunyuan Large، يدعم إدخال صور متعددة بأي دقة مع نص، ويولد محتوى نصي، مع تركيز على مهام فهم الصور والنصوص، مع تحسين ملحوظ في القدرات متعددة اللغات."}, "hunyuan-lite": {"description": "تم الترقية إلى هيكل MOE، مع نافذة سياق تصل إلى 256k، متفوقًا على العديد من النماذج مفتوحة المصدر في تقييمات NLP، البرمجة، الرياضيات، والصناعات."}, "hunyuan-lite-vision": {"description": "نموذج مختلط حديث بقدرة 7 مليار معلمة، مع نافذة سياقية 32K، يدعم المحادثات متعددة الوسائط في السيناريوهات الصينية والإنجليزية، والتعرف على كائنات الصور، وفهم جداول الوثائق، والرياضيات متعددة الوسائط، ويتفوق في مؤشرات التقييم على نماذج المنافسة ذات 7 مليار معلمة في عدة أبعاد."}, "hunyuan-pro": {"description": "نموذج نصوص طويلة MOE-32K بحجم تريليون من المعلمات. يحقق مستوى رائد مطلق في مختلف المعايير، مع القدرة على التعامل مع التعليمات المعقدة والاستدلال، ويتميز بقدرات رياضية معقدة، ويدعم استدعاء الوظائف، مع تحسينات رئيسية في مجالات الترجمة متعددة اللغات، المالية، القانونية، والرعاية الصحية."}, "hunyuan-role": {"description": "نموذج Hunyuan الأحدث لتقمص الأدوار، تم تطويره من قبل Hunyuan مع تدريب دقيق، يعتمد على نموذج Hunyuan مع مجموعة بيانات سيناريوهات تقمص الأدوار، مما يوفر أداءً أفضل في سيناريوهات تقمص الأدوار."}, "hunyuan-standard": {"description": "يستخدم استراتيجية توجيه أفضل، مع تخفيف مشكلات التوازن في الحمل وتوافق الخبراء. في مجال النصوص الطويلة، تصل نسبة مؤشر البحث إلى 99.9%. MOE-32K يقدم قيمة أفضل، مع تحقيق توازن بين الأداء والسعر، مما يسمح بمعالجة المدخلات النصية الطويلة."}, "hunyuan-standard-256K": {"description": "يستخدم استراتيجية توجيه أفضل، مع تخفيف مشكلات التوازن في الحمل وتوافق الخبراء. في مجال النصوص الطويلة، تصل نسبة مؤشر البحث إلى 99.9%. MOE-256K يحقق اختراقًا إضافيًا في الطول والأداء، مما يوسع بشكل كبير طول المدخلات الممكنة."}, "hunyuan-standard-vision": {"description": "نموذج متعدد الوسائط حديث يدعم الإجابة بعدة لغات، مع توازن في القدرات بين الصينية والإنجليزية."}, "hunyuan-t1-20250321": {"description": "بناء شامل لقدرات النموذج في العلوم الإنسانية والطبيعية، مع قدرة قوية على التقاط المعلومات من النصوص الطويلة. يدعم الاستدلال والإجابة على مشكلات علمية متنوعة من الرياضيات/المنطق/العلوم/الشيفرات."}, "hunyuan-t1-20250403": {"description": "تعزيز قدرة توليد الأكواد على مستوى المشروع؛ تحسين جودة كتابة النصوص المولدة؛ تعزيز قدرة فهم النصوص متعددة الجولات، والامتثال لتعليمات toB، وفهم الكلمات؛ تحسين مشاكل الخلط بين النصوص المبسطة والتقليدية والخلط بين اللغات الصينية والإنجليزية في المخرجات."}, "hunyuan-t1-20250529": {"description": "محسن لإنشاء النصوص وكتابة المقالات، مع تحسين القدرات في البرمجة الأمامية، الرياضيات، والمنطق العلمي، بالإضافة إلى تعزيز القدرة على اتباع التعليمات."}, "hunyuan-t1-20250711": {"description": "تحسين كبير في القدرات الرياضية، المنطقية والبرمجية عالية الصعوبة، مع تحسين استقرار مخرجات النموذج وتعزيز قدرات النصوص الطويلة."}, "hunyuan-t1-latest": {"description": "أول نموذج استدلال هجين ضخم في الصناعة، يوسع قدرات الاستدلال، بسرعة فك تشفير فائقة، ويعزز التوافق مع تفضيلات البشر."}, "hunyuan-t1-vision": {"description": "نموذج تفكير عميق متعدد الوسائط من Hunyuan، يدعم سلاسل التفكير الأصلية متعددة الوسائط، بارع في معالجة مختلف سيناريوهات الاستدلال على الصور، ويحقق تحسينًا شاملاً مقارنة بنموذج التفكير السريع في مسائل العلوم."}, "hunyuan-t1-vision-20250619": {"description": "أحدث نموذج تفكير عميق متعدد الوسائط t1-vision من Hunyuan، يدعم سلسلة التفكير الأصلية متعددة الوسائط، مع تحسين شامل مقارنة بالإصدار الافتراضي السابق."}, "hunyuan-turbo": {"description": "نسخة المعاينة من الجيل الجديد من نموذج اللغة الكبير، يستخدم هيكل نموذج الخبراء المختلط (MoE) الجديد، مما يوفر كفاءة استدلال أسرع وأداء أقوى مقارنة بـ hunyuan-pro."}, "hunyuan-turbo-20241223": {"description": "تحسينات في هذا الإصدار: توجيه البيانات، مما يعزز بشكل كبير قدرة النموذج على التعميم؛ تحسين كبير في القدرات الرياضية، البرمجية، وقدرات الاستدلال المنطقي؛ تحسين القدرات المتعلقة بفهم النصوص والكلمات؛ تحسين جودة إنشاء محتوى النص."}, "hunyuan-turbo-latest": {"description": "تحسين تجربة شاملة، بما في ذلك فهم اللغة الطبيعية، إنشاء النصوص، الدردشة، الأسئلة والأجوبة المعرفية، الترجمة، والمجالات الأخرى؛ تعزيز الطابع الإنساني، وتحسين الذكاء العاطفي للنموذج؛ تعزيز قدرة النموذج على توضيح النوايا الغامضة؛ تحسين القدرة على معالجة الأسئلة المتعلقة بتحليل الكلمات؛ تحسين جودة الإبداع والتفاعل؛ تعزيز تجربة التفاعل المتعدد الجولات."}, "hunyuan-turbo-vision": {"description": "نموذج اللغة البصرية الرائد من الجيل الجديد، يستخدم هيكل نموذج الخبراء المختلط (MoE) الجديد، مع تحسين شامل في القدرات المتعلقة بفهم النصوص والصور، وإنشاء المحتوى، والأسئلة والأجوبة المعرفية، والتحليل والاستدلال مقارنة بالنماذج السابقة."}, "hunyuan-turbos-20250313": {"description": "توحيد أسلو<PERSON> خطوات حل المسائل الرياضية، وتعزيز الأسئلة والأجوبة متعددة الجولات في الرياضيات. تحسين أسلوب الإجابة في الإبداع النصي، إزالة الطابع الآلي، وزيادة البلاغة."}, "hunyuan-turbos-20250416": {"description": "ترقية قاعدة التدريب المسبق لتعزيز فهم القاعدة والامتثال للتعليمات؛ تعزيز القدرات العلمية مثل الرياضيات، البرمجة، المنطق، والعلوم خلال مرحلة المحاذاة؛ تحسين جودة الكتابة الإبداعية، فهم النصوص، دقة الترجمة، والإجابة على الأسئلة المعرفية في المجالات الأدبية؛ تعزيز قدرات الوكلاء في مختلف المجالات، مع التركيز على تحسين فهم الحوار متعدد الجولات."}, "hunyuan-turbos-20250604": {"description": "ترقية قاعدة التدريب المسبق، مع تحسينات في مهارات الكتابة وفهم القراءة، وزيادة كبيرة في القدرات البرمجية والعلمية، وتحسين مستمر في اتباع التعليمات المعقدة."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS هو أحدث إصدار من نموذج هونيان الرائد، يتمتع بقدرات تفكير أقوى وتجربة أفضل."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "بارع في معالجة المهام الطويلة مثل تلخيص الوثائق والأسئلة والأجوبة، كما يمتلك القدرة على معالجة مهام توليد النصوص العامة. يظهر أداءً ممتازًا في تحليل وتوليد النصوص الطويلة، ويمكنه التعامل بفعالية مع متطلبات معالجة المحتوى الطويل والمعقد."}, "hunyuan-turbos-role-plus": {"description": "أحدث نموذج تمثيل الأدوار من Hunyuan، نموذج تم تدريبه بدقة من قبل Hunyuan الرسمي، يعتمد على نموذج Hunyuan مع بيانات مشاهد تمثيل الأدوار للتدريب الإضافي، ويقدم أداءً أساسيًا أفضل في مشاهد تمثيل الأدوار."}, "hunyuan-turbos-vision": {"description": "هذا النموذج مناسب لمشاهد فهم النصوص والصور، وهو نموذج اللغة البصرية الرائد من الجيل الجديد المبني على أحدث إصدار من Hunyuan turbos، يركز على مهام فهم النصوص والصور، بما في ذلك التعرف على الكيانات بناءً على الصور، الأسئلة المعرفية، إنشاء النصوص، وحل المسائل عبر التصوير، مع تحسين شامل مقارنة بالجيل السابق."}, "hunyuan-turbos-vision-20250619": {"description": "أحدث نموذج رائد للغة البصرية turbos-vision من Hunyuan، مع تحسين شامل في مهام فهم النصوص والصور، بما في ذلك التعرف على الكيانات بناءً على الصور، الأسئلة المعرفية، إنشاء النصوص، وحل المسائل عبر التصوير، مقارنة بالإصدار الافتراضي السابق."}, "hunyuan-vision": {"description": "نموذج Hunyuan الأحدث متعدد الوسائط، يدعم إدخال الصور والنصوص لتوليد محتوى نصي."}, "image-01": {"description": "نموذج توليد صور جديد يقدم تفاصيل دقيقة، يدعم توليد الصور من النصوص والصور."}, "image-01-live": {"description": "نموذج توليد صور يقدم تفاصيل دقيقة، يدعم توليد الصور من النصوص مع إمكانية ضبط الأسلوب الفني."}, "imagen-4.0-generate-preview-06-06": {"description": "سلسلة نموذج Imagen للجيل الرابع لتحويل النص إلى صورة"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "نسخة ألترا من سلسلة نموذج Imagen للجيل الرابع لتحويل النص إلى صورة"}, "imagen4/preview": {"description": "نموذج توليد الصور الأعلى جودة من Google"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 يوفر حلول حوار ذكية في عدة سيناريوهات."}, "internlm2.5-latest": {"description": "سلسلة نماذجنا الأحدث، تتمتع بأداء استدلال ممتاز، تدعم طول سياق يصل إلى 1 مليون، بالإضافة إلى قدرة أقوى على اتباع التعليمات واستدعاء الأدوات."}, "internlm3-latest": {"description": "سلسلة نماذجنا الأحدث، تتمتع بأداء استدلال ممتاز، تتصدر نماذج المصدر المفتوح من نفس الفئة. تشير بشكل افتراضي إلى أحدث نماذج سلسلة InternLM3 التي تم إصدارها."}, "internvl2.5-latest": {"description": "نحن لا نزال ندعم إصدار InternVL2.5، الذي يتمتع بأداء ممتاز ومستقر. يشير بشكل افتراضي إلى أحدث نموذج من سلسلة InternVL2.5، الحالي هو internvl2.5-78b."}, "internvl3-latest": {"description": "أحدث نموذج متعدد الوسائط تم إصداره، يتمتع بقدرات فهم أقوى للنصوص والصور، وفهم الصور على المدى الطويل، وأدائه يتساوى مع النماذج المغلقة الرائدة. يشير بشكل افتراضي إلى أحدث نموذج من سلسلة InternVL، الحالي هو internvl3-78b."}, "irag-1.0": {"description": "نموذج iRAG (استرجاع معزز بالصور) المطور ذاتيًا من Baidu، يجمع بين موارد صور بحث Baidu الضخمة وقدرات النموذج الأساسي القوية لتوليد صور فائقة الواقعية، متفوقًا بشكل كبير على أنظمة توليد الصور النصية الأصلية، مع إزالة الطابع الاصطناعي وتقليل التكلفة. يتميز iRAG بعدم وجود هلوسة، واقعية فائقة، وسرعة في الحصول على النتائج."}, "jamba-large": {"description": "أقوى وأحدث نموذج لدينا، مصمم لمعالجة المهام المعقدة على مستوى المؤسسات، ويتميز بأداء استثنائي."}, "jamba-mini": {"description": "النموذج الأكثر كفاءة في فئته، يجمع بين السرعة والجودة، ويتميز بحجمه الصغير."}, "jina-deepsearch-v1": {"description": "البحث العميق يجمع بين البحث عبر الإنترنت، والقراءة، والاستدلال، مما يتيح إجراء تحقيق شامل. يمكنك اعتباره وكيلًا يتولى مهام البحث الخاصة بك - حيث يقوم بإجراء بحث واسع النطاق ويخضع لعدة تكرارات قبل تقديم الإجابة. تتضمن هذه العملية بحثًا مستمرًا، واستدلالًا، وحل المشكلات من زوايا متعددة. وهذا يختلف اختلافًا جوهريًا عن النماذج الكبيرة القياسية التي تولد الإجابات مباشرة من البيانات المدربة مسبقًا، وكذلك عن أنظمة RAG التقليدية التي تعتمد على البحث السطحي لمرة واحدة."}, "kimi-k2": {"description": "Kimi-K2 هو نموذج أساسي يعتمد على بنية MoE أطلقته Moonshot AI، يتمتع بقدرات قوية في البرمجة والوكيل، يحتوي على 1 تريليون معلمة و32 مليار معلمة مفعلة. يتفوق نموذج K2 في اختبارات الأداء الأساسية في مجالات المعرفة العامة، البرمجة، الرياضيات والوكيل مقارنة بالنماذج المفتوحة المصدر الأخرى."}, "kimi-k2-0711-preview": {"description": "kimi-k2 هو نموذج أساسي بمعمارية MoE يتمتع بقدرات فائقة في البرمجة والوكيل، مع إجمالي 1 تريليون معلمة و32 مليار معلمة مفعلة. في اختبارات الأداء الأساسية في مجالات المعرفة العامة، البرمجة، الرياضيات، والوكيل، يتفوق نموذج K2 على النماذج المفتوحة المصدر الرئيسية الأخرى."}, "kimi-latest": {"description": "يستخدم منتج كيمي المساعد الذكي أحدث نموذج كبير من كيمي، وقد يحتوي على ميزات لم تستقر بعد. يدعم فهم الصور، وسيختار تلقائيًا نموذج 8k/32k/128k كنموذج للتسعير بناءً على طول سياق الطلب."}, "kimi-thinking-preview": {"description": "نموذج kimi-thinking-preview هو نموذج تفكير متعدد الوسائط يتمتع بقدرات استدلال متعددة الوسائط وعامة، مقدم من الجانب المظلم للقمر، يتقن الاستدلال العميق ويساعد في حل المزيد من المسائل الصعبة."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM هو نموذج لغوي تجريبي محدد المهام، تم تدريبه ليتماشى مع مبادئ علوم التعلم، يمكنه اتباع التعليمات النظامية في سيناريوهات التعليم والتعلم، ويعمل كمدرب خبير."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM هو نموذج لغوي تجريبي، محدد المهام، تم تدريبه وفقًا لمبادئ علوم التعلم، يمكنه اتباع التعليمات النظامية في سيناريوهات التعليم والتعلم، ويعمل كمرشد خبير."}, "lite": {"description": "سبارك لايت هو نموذج لغوي كبير خفيف الوزن، يتميز بتأخير منخفض للغاية وكفاءة عالية في المعالجة، وهو مجاني تمامًا ومفتوح، ويدعم وظيفة البحث عبر الإنترنت في الوقت الحقيقي. تجعل خصائص استجابته السريعة منه مثاليًا لتطبيقات الاستدلال على الأجهزة ذات القدرة الحاسوبية المنخفضة وضبط النماذج، مما يوفر للمستخدمين قيمة ممتازة من حيث التكلفة وتجربة ذكية، خاصة في مجالات الأسئلة والأجوبة المعرفية، وتوليد المحتوى، وسيناريوهات البحث."}, "llama-2-7b-chat": {"description": "Llama2 هو سلسلة من النماذج اللغوية الكبيرة (LLM) التي طورتها Meta وأطلقتها كمصدر مفتوح، وهي تتكون من نماذج توليد نص مسبقة التدريب ومتخصصة بحجم يتراوح من 7 مليارات إلى 70 مليار معلمة. على مستوى العمارة، Llama2 هو نموذج لغوي تراجعي تلقائي يستخدم معمارية محول محسنة. الإصدارات المعدلة تستخدم التدريب الدقيق تحت الإشراف (SFT) والتعلم التقويمي مع تعزيزات من البشر (RLHF) لتوافق تفضيلات البشر فيما يتعلق بالفائدة والأمان. أظهر Llama2 أداءً أفضل بكثير من سلسلة Llama في العديد من المجموعات الأكاديمية، مما قدم إلهامًا لتصميم وتطوير العديد من النماذج الأخرى."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B يوفر قدرة استدلال ذكائي أقوى، مناسب للتطبيقات المعقدة، يدعم معالجة حسابية ضخمة ويضمن الكفاءة والدقة."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B هو نموذج عالي الأداء، يوفر قدرة سريعة على توليد النصوص، مما يجعله مثاليًا لمجموعة من التطبيقات التي تتطلب كفاءة كبيرة وتكلفة فعالة."}, "llama-3.1-instruct": {"description": "تم تحسين نموذج Llama 3.1 المعدل للتعليمات خصيصًا لسيناريوهات الحوار، حيث يتفوق على العديد من نماذج الدردشة مفتوحة المصدر الحالية في معايير الصناعة الشائعة."}, "llama-3.2-11b-vision-instruct": {"description": "قدرة استدلال الصور التي تبرز في الصور عالية الدقة، مناسبة لتطبيقات الفهم البصري."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 مصمم للتعامل مع المهام التي تجمع بين البيانات البصرية والنصية. يظهر أداءً ممتازًا في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة والاستدلال البصري."}, "llama-3.2-90b-vision-instruct": {"description": "قدرة استدلال الصور المتقدمة المناسبة لتطبيقات الوكلاء في الفهم البصري."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 مصمم للتعامل مع المهام التي تجمع بين البيانات البصرية والنصية. يظهر أداءً ممتازًا في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة والاستدلال البصري."}, "llama-3.2-vision-instruct": {"description": "تم تحسين نموذج Llama 3.2-Vision المعدل للتعليمات للتعرف البصري، والاستدلال على الصور، ووصف الصور، والإجابة على الأسئلة العامة المتعلقة بالصور."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 هو النموذج الأكثر تقدمًا في سلسلة Llama، وهو نموذج لغوي مفتوح المصدر متعدد اللغات، يوفر تجربة أداء تنافس نموذج 405B بتكلفة منخفضة للغاية. يعتمد على هيكل Transformer، وتم تحسين فائدته وأمانه من خلال التعديل الدقيق تحت الإشراف (SFT) والتعلم المعزز من خلال التغذية الراجعة البشرية (RLHF). تم تحسين نسخة التعديل الخاصة به لتكون مثالية للحوار متعدد اللغات، حيث يتفوق في العديد من المعايير الصناعية على العديد من نماذج الدردشة المفتوحة والمغلقة. تاريخ انتهاء المعرفة هو ديسمبر 2023."}, "llama-3.3-70b-versatile": {"description": "ميتّا لاما 3.3 هو نموذج لغة كبير متعدد اللغات (LLM) يضم 70 مليار (إدخال نص/إخراج نص) من النموذج المدرب مسبقًا والمعدل وفقًا للتعليمات. تم تحسين نموذج لاما 3.3 المعدل وفقًا للتعليمات للاستخدامات الحوارية متعددة اللغات ويتفوق على العديد من النماذج المتاحة مفتوحة المصدر والمغلقة في المعايير الصناعية الشائعة."}, "llama-3.3-instruct": {"description": "تم تحسين نموذج Llama 3.3 المعدل للتعليمات خصيصًا لسيناريوهات المحادثة، حيث تفوق على العديد من نماذج الدردشة مفتوحة المصدر الحالية في اختبارات المعايير الصناعية الشائعة."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B يوفر قدرة معالجة معقدة لا مثيل لها، مصمم خصيصًا للمشاريع ذات المتطلبات العالية."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B يوفر أداء استدلال عالي الجودة، مناسب لمتطلبات التطبيقات متعددة السيناريوهات."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use يوفر قدرة قوية على استدعاء الأدوات، يدعم معالجة فعالة للمهام المعقدة."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use هو نموذج محسن للاستخدام الفعال للأدوات، يدعم الحسابات المتوازية السريعة."}, "llama3.1": {"description": "Llama 3.1 هو النموذج الرائد الذي أطلقته Meta، يدعم ما يصل إلى 405B من المعلمات، ويمكن تطبيقه في مجالات الحوار المعقد، والترجمة متعددة اللغات، وتحليل البيانات."}, "llama3.1:405b": {"description": "Llama 3.1 هو النموذج الرائد الذي أطلقته Meta، يدعم ما يصل إلى 405B من المعلمات، ويمكن تطبيقه في مجالات الحوار المعقد، والترجمة متعددة اللغات، وتحليل البيانات."}, "llama3.1:70b": {"description": "Llama 3.1 هو النموذج الرائد الذي أطلقته Meta، يدعم ما يصل إلى 405B من المعلمات، ويمكن تطبيقه في مجالات الحوار المعقد، والترجمة متعددة اللغات، وتحليل البيانات."}, "llava": {"description": "LLaVA هو نموذج متعدد الوسائط يجمع بين مشفرات بصرية وVicuna، يستخدم لفهم بصري ولغوي قوي."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B يو<PERSON>ر قدرة معالجة بصرية مدمجة، من خلال إدخال المعلومات البصرية لتوليد مخرجات معقدة."}, "llava:13b": {"description": "LLaVA هو نموذج متعدد الوسائط يجمع بين مشفرات بصرية وVicuna، يستخدم لفهم بصري ولغوي قوي."}, "llava:34b": {"description": "LLaVA هو نموذج متعدد الوسائط يجمع بين مشفرات بصرية وVicuna، يستخدم لفهم بصري ولغوي قوي."}, "mathstral": {"description": "MathΣtral مصمم للبحث العلمي والاستدلال الرياضي، يوفر قدرة حسابية فعالة وتفسير النتائج."}, "max-32k": {"description": "سبارك ماكس 32K مزود بقدرة معالجة سياق كبيرة، مع فهم أقوى للسياق وقدرة على الاستدلال المنطقي، يدعم إدخال نصوص تصل إلى 32K توكن، مما يجعله مناسبًا لقراءة الوثائق الطويلة، والأسئلة والأجوبة المعرفية الخاصة، وغيرها من السيناريوهات."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct هو نموذج لغة كبير تم تدريبه بشكل مستقل من قبل شركة ووون تشينغ. يهدف Megrez-3B-Instruct إلى تقديم حل ذكاء على جهاز نهائي سريع وصغير وسهل الاستخدام من خلال مفهوم التكامل بين البرمجيات والأجهزة."}, "meta-llama-3-70b-instruct": {"description": "نموذج قوي بحجم 70 مليار معلمة يتفوق في التفكير، والترميز، وتطبيقات اللغة الواسعة."}, "meta-llama-3-8b-instruct": {"description": "نموذج متعدد الاستخدامات بحجم 8 مليار معلمة، مُحسّن لمهام الحوار وتوليد النصوص."}, "meta-llama-3.1-405b-instruct": {"description": "نموذج Llama 3.1 المُعدل للتعليمات، مُحسّن لاستخدامات الحوار متعددة اللغات ويتفوق على العديد من نماذج الدردشة المفتوحة والمغلقة المتاحة في المعايير الصناعية الشائعة."}, "meta-llama-3.1-70b-instruct": {"description": "نموذج Llama 3.1 المُعدل للتعليمات، مُحسّن لاستخدامات الحوار متعددة اللغات ويتفوق على العديد من نماذج الدردشة المفتوحة والمغلقة المتاحة في المعايير الصناعية الشائعة."}, "meta-llama-3.1-8b-instruct": {"description": "نموذج Llama 3.1 المُعدل للتعليمات، مُحسّن لاستخدامات الحوار متعددة اللغات ويتفوق على العديد من نماذج الدردشة المفتوحة والمغلقة المتاحة في المعايير الصناعية الشائعة."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) يوفر قدرة ممتازة على معالجة اللغة وتجربة تفاعلية رائعة."}, "meta-llama/Llama-2-70b-hf": {"description": "يوفر LLaMA-2 قدرة معالجة لغوية ممتازة وتجربة تفاعلية رائعة."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) هو نموذج دردشة قوي، يدعم احتياجات الحوار المعقدة."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) يوفر دعمًا متعدد اللغات، ويغطي مجموعة واسعة من المعرفة في المجالات."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "تم تصميم LLaMA 3.2 لمعالجة المهام التي تجمع بين البيانات البصرية والنصية. إنه يبرز في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة واستدلال الرؤية."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "تم تصميم LLaMA 3.2 لمعالجة المهام التي تجمع بين البيانات البصرية والنصية. إنه يبرز في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة واستدلال الرؤية."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "تم تصميم LLaMA 3.2 لمعالجة المهام التي تجمع بين البيانات البصرية والنصية. إنه يبرز في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة واستدلال الرؤية."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "نموذج Meta Llama 3.3 متعدد اللغات (LLM) هو نموذج توليد تم تدريبه مسبقًا وضبطه على التعليمات في 70B (إدخال نص/إخراج نص). تم تحسين نموذج Llama 3.3 المعدل على التعليمات لحالات استخدام الحوار متعدد اللغات، ويتفوق على العديد من نماذج الدردشة المفتوحة والمغلقة المتاحة في المعايير الصناعية الشائعة."}, "meta-llama/Llama-Vision-Free": {"description": "تم تصميم LLaMA 3.2 لمعالجة المهام التي تجمع بين البيانات البصرية والنصية. إنه يبرز في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة واستدلال الرؤية."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite مناسب للبيئات التي تتطلب أداءً عاليًا وزمن استجابة منخفض."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo يوفر قدرة ممتازة على فهم اللغة وتوليدها، مناسب لأكثر المهام الحسابية تطلبًا."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite مناسب للبيئات ذات الموارد المحدودة، ويوفر أداءً متوازنًا ممتازًا."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo هو نموذج لغوي كبير عالي الأداء، يدعم مجموعة واسعة من سيناريوهات التطبيق."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B هو نموذج قوي للتدريب المسبق وضبط التعليمات."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "نموذج Llama 3.1 Turbo 405B يوفر دعمًا كبيرًا للسياق لمعالجة البيانات الكبيرة، ويظهر أداءً بارزًا في تطبيقات الذكاء الاصطناعي على نطاق واسع."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 هو نموذج رائد أطلقته Meta، يدعم ما يصل إلى 405B من المعلمات، ويمكن تطبيقه في مجالات المحادثات المعقدة، والترجمة متعددة اللغات، وتحليل البيانات."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "نموذج Llama 3.1 70B تم ضبطه بدقة، مناسب للتطبيقات ذات الحمل العالي، تم تكميمه إلى FP8 لتوفير قدرة حسابية ودقة أعلى، مما يضمن أداءً ممتازًا في السيناريوهات المعقدة."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "نموذج Llama 3.1 8B يستخدم FP8 للتكميم، يدعم ما يصل إلى 131,072 علامة سياق، وهو من بين الأفضل في النماذج المفتوحة المصدر، مناسب للمهام المعقدة، ويظهر أداءً ممتازًا في العديد من المعايير الصناعية."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct تم تحسينه لمشاهد الحوار عالية الجودة، ويظهر أداءً ممتازًا في مختلف التقييمات البشرية."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct تم تحسينه لمشاهد الحوار عالية الجودة، ويظهر أداءً أفضل من العديد من النماذج المغلقة."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct مصمم للحوار عالي الجودة، ويظهر أداءً بارزًا في التقييمات البشرية، مما يجعله مناسبًا بشكل خاص للمشاهد التفاعلية العالية."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct هو أحدث إصدار من Meta، تم تحسينه لمشاهد الحوار عالية الجودة، ويظهر أداءً أفضل من العديد من النماذج المغلقة الرائدة."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 يوفر دعمًا متعدد اللغات، وهو واحد من النماذج الرائدة في الصناعة في مجال التوليد."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "تم تصميم LLaMA 3.2 لمعالجة المهام التي تجمع بين البيانات البصرية والنصية. إنه يتفوق في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة والاستدلال البصري."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "تم تصميم LLaMA 3.2 لمعالجة المهام التي تجمع بين البيانات البصرية والنصية. إنه يتفوق في مهام وصف الصور والأسئلة البصرية، متجاوزًا الفجوة بين توليد اللغة والاستدلال البصري."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 هو النموذج الأكثر تقدمًا في سلسلة Llama، وهو نموذج لغوي مفتوح المصدر متعدد اللغات، يوفر تجربة أداء تنافس نموذج 405B بتكلفة منخفضة للغاية. يعتمد على هيكل Transformer، وتم تحسين فائدته وأمانه من خلال التعديل الدقيق تحت الإشراف (SFT) والتعلم المعزز من خلال التغذية الراجعة البشرية (RLHF). تم تحسين نسخة التعديل الخاصة به لتكون مثالية للحوار متعدد اللغات، حيث يتفوق في العديد من المعايير الصناعية على العديد من نماذج الدردشة المفتوحة والمغلقة. تاريخ انتهاء المعرفة هو ديسمبر 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 هو النموذج الأكثر تقدمًا في سلسلة Llama، وهو نموذج لغوي مفتوح المصدر متعدد اللغات، يوفر تجربة أداء تنافس نموذج 405B بتكلفة منخفضة للغاية. يعتمد على هيكل Transformer، وتم تحسين فائدته وأمانه من خلال التعديل الدقيق تحت الإشراف (SFT) والتعلم المعزز من خلال التغذية الراجعة البشرية (RLHF). تم تحسين نسخة التعديل الخاصة به لتكون مثالية للحوار متعدد اللغات، حيث يتفوق في العديد من المعايير الصناعية على العديد من نماذج الدردشة المفتوحة والمغلقة. تاريخ انتهاء المعرفة هو ديسمبر 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "نموذج Meta Llama 3.1 405B Instruct هو أكبر وأقوى نموذج في مجموعة نماذج Llama 3.1 Instruct، وهو نموذج متقدم للغاية لتوليد البيانات والحوار، ويمكن استخدامه كأساس للتدريب المستمر أو التخصيص في مجالات معينة. توفر Llama 3.1 نماذج لغوية كبيرة متعددة اللغات (LLMs) وهي مجموعة من النماذج المدربة مسبقًا والمعدلة وفقًا للتعليمات، بما في ذلك أحجام 8B و70B و405B (إدخال/إخراج نصي). تم تحسين نماذج النص المعدلة وفقًا للتعليمات (8B و70B و405B) لحالات الاستخدام الحوارية متعددة اللغات، وقد تفوقت في العديد من اختبارات المعايير الصناعية الشائعة على العديد من نماذج الدردشة مفتوحة المصدر المتاحة. تم تصميم Llama 3.1 للاستخدام التجاري والبحثي في عدة لغات. نماذج النص المعدلة وفقًا للتعليمات مناسبة للدردشة الشبيهة بالمساعد، بينما يمكن للنماذج المدربة مسبقًا التكيف مع مجموعة متنوعة من مهام توليد اللغة الطبيعية. تدعم نماذج Llama 3.1 أيضًا تحسين نماذج أخرى باستخدام مخرجاتها، بما في ذلك توليد البيانات الاصطناعية والتنقيح. Llama 3.1 هو نموذج لغوي ذاتي التكرار يستخدم بنية المحولات المحسّنة. تستخدم النسخ المعدلة التعلم المعزز مع التغذية الراجعة البشرية (RLHF) لتلبية تفضيلات البشر فيما يتعلق بالمساعدة والأمان."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "الإصدار المحدث من Meta Llama 3.1 70B Instruct، يتضمن طول سياق موسع يبلغ 128K، ودعم لغات متعددة، وقدرات استدلال محسنة. توفر Llama 3.1 نماذج لغوية كبيرة متعددة اللغات (LLMs) وهي مجموعة من النماذج التوليدية المدربة مسبقًا والمعدلة للتعليمات، بما في ذلك أحجام 8B و70B و405B (إدخال/إخراج نص). تم تحسين نماذج النص المعدلة للتعليمات (8B و70B و405B) لحالات الاستخدام متعددة اللغات، وتفوقت في اختبارات المعايير الصناعية الشائعة على العديد من نماذج الدردشة مفتوحة المصدر المتاحة. تم تصميم Llama 3.1 للاستخدام التجاري والبحثي في لغات متعددة. نماذج النص المعدلة للتعليمات مناسبة للدردشة الشبيهة بالمساعد، بينما يمكن للنماذج المدربة مسبقًا التكيف مع مجموعة متنوعة من مهام توليد اللغة الطبيعية. تدعم نماذج Llama 3.1 أيضًا تحسين نماذج أخرى باستخدام مخرجات نموذجها، بما في ذلك توليد البيانات الاصطناعية والتنقيح. Llama 3.1 هو نموذج لغوي ذاتي التكرار يستخدم بنية المحولات المحسنة. تستخدم النسخ المعدلة التعلم الموجه بالإشراف (SFT) والتعلم المعزز مع التغذية الراجعة البشرية (RLHF) لتلبية تفضيلات البشر فيما يتعلق بالمساعدة والأمان."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "الإصدار المحدث من Meta Llama 3.1 8B Instruct، يتضمن طول سياق موسع يبلغ 128K، ودعم لغات متعددة، وقدرات استدلال محسنة. توفر Llama 3.1 نماذج لغوية كبيرة متعددة اللغات (LLMs) وهي مجموعة من النماذج التوليدية المدربة مسبقًا والمعدلة للتعليمات، بما في ذلك أحجام 8B و70B و405B (إدخال/إخراج نص). تم تحسين نماذج النص المعدلة للتعليمات (8B و70B و405B) لحالات الاستخدام متعددة اللغات، وتفوقت في اختبارات المعايير الصناعية الشائعة على العديد من نماذج الدردشة مفتوحة المصدر المتاحة. تم تصميم Llama 3.1 للاستخدام التجاري والبحثي في لغات متعددة. نماذج النص المعدلة للتعليمات مناسبة للدردشة الشبيهة بالمساعد، بينما يمكن للنماذج المدربة مسبقًا التكيف مع مجموعة متنوعة من مهام توليد اللغة الطبيعية. تدعم نماذج Llama 3.1 أيضًا تحسين نماذج أخرى باستخدام مخرجات نموذجها، بما في ذلك توليد البيانات الاصطناعية والتنقيح. Llama 3.1 هو نموذج لغوي ذاتي التكرار يستخدم بنية المحولات المحسنة. تستخدم النسخ المعدلة التعلم الموجه بالإشراف (SFT) والتعلم المعزز مع التغذية الراجعة البشرية (RLHF) لتلبية تفضيلات البشر فيما يتعلق بالمساعدة والأمان."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 هو نموذج لغوي كبير مفتوح (LLM) موجه للمطورين والباحثين والشركات، يهدف إلى مساعدتهم في بناء وتجربة وتوسيع أفكارهم في الذكاء الاصطناعي بشكل مسؤول. كجزء من نظام الابتكار المجتمعي العالمي، فهو مثالي لإنشاء المحتوى، والذكاء الاصطناعي الحواري، وفهم اللغة، والبحث والتطوير، وتطبيقات الأعمال."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 هو نموذج لغوي كبير مفتوح (LLM) موجه للمطورين والباحثين والشركات، يهدف إلى مساعدتهم في بناء وتجربة وتوسيع أفكارهم في الذكاء الاصطناعي بشكل مسؤول. كجزء من نظام الابتكار المجتمعي العالمي، فهو مثالي للأجهزة ذات القدرة الحاسوبية والموارد المحدودة، والأجهزة الطرفية، وأوقات التدريب الأسرع."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "يتميز بقدرات استدلال على الصور عالية الدقة، مناسب لتطبيقات الفهم البصري."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "قدرات استدلال متقدمة على الصور لتطبيقات الوكلاء في الفهم البصري."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 هو أحدث نموذج لغوي كبير متعدد اللغات مفتوح المصدر من سلسلة Llama، يقدم أداءً مماثلاً لنموذج 405 مليار معلمات بتكلفة منخفضة جدًا. يعتمد على بنية Transformer، وتم تحسينه من خلال التعديل الدقيق الخاضع للإشراف (SFT) والتعلم المعزز من خلال تغذية راجعة بشرية (RLHF) لتعزيز الفائدة والأمان. النسخة المعدلة للتعليمات مخصصة للحوار متعدد اللغات، وتتفوق على العديد من نماذج الدردشة المفتوحة والمغلقة في معايير الصناعة المتعددة. تاريخ المعرفة حتى ديسمبر 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "نموذج قوي يحتوي على 70 مليار معلمة، يتميز بأداء ممتاز في الاستدلال، البرمجة، وتطبيقات اللغة المتنوعة."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "نموذج متعدد الاستخدامات يحتوي على 8 مليارات معلمة، محسن لمهام الحوار وتوليد النصوص."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "نموذج نصي معدل للتعليمات من Llama 3.1، محسن لحالات استخدام الحوار متعدد اللغات، ويحقق أداءً ممتازًا في العديد من معايير الصناعة مقارنة بالعديد من نماذج الدردشة المفتوحة والمغلقة."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "نموذج نصي معدل للتعليمات من Llama 3.1، محسن لحالات استخدام الحوار متعدد اللغات، ويحقق أداءً ممتازًا في العديد من معايير الصناعة مقارنة بالعديد من نماذج الدردشة المفتوحة والمغلقة."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "نموذج نصي معدل للتعليمات من Llama 3.1، محسن لحالات استخدام الحوار متعدد اللغات، ويحقق أداءً ممتازًا في العديد من معايير الصناعة مقارنة بالعديد من نماذج الدردشة المفتوحة والمغلقة."}, "meta/llama-3.1-405b-instruct": {"description": "نموذج لغوي متقدم، يدعم توليد البيانات الاصطناعية، وتقطير المعرفة، والاستدلال، مناسب للدردشة، والبرمجة، والمهام الخاصة."}, "meta/llama-3.1-70b-instruct": {"description": "يمكنه تمكين المحادثات المعقدة، ويتميز بفهم سياقي ممتاز، وقدرات استدلال، وقدرة على توليد النصوص."}, "meta/llama-3.1-8b-instruct": {"description": "نموذج متقدم من الطراز الأول، يتمتع بفهم اللغة، وقدرات استدلال ممتازة، وقدرة على توليد النصوص."}, "meta/llama-3.2-11b-vision-instruct": {"description": "نموذج متقدم للرؤية واللغة، بارع في إجراء استدلال عالي الجودة من الصور."}, "meta/llama-3.2-1b-instruct": {"description": "نموذج لغوي صغير متقدم، يتمتع بفهم اللغة، وقدرات استدلال ممتازة، وقدرة على توليد النصوص."}, "meta/llama-3.2-3b-instruct": {"description": "نموذج لغوي صغير متقدم، يتمتع بفهم اللغة، وقدرات استدلال ممتازة، وقدرة على توليد النصوص."}, "meta/llama-3.2-90b-vision-instruct": {"description": "نموذج متقدم للرؤية واللغة، بارع في إجراء استدلال عالي الجودة من الصور."}, "meta/llama-3.3-70b-instruct": {"description": "نموذج لغوي متقدم، بارع في الاستدلال، والرياضيات، والمعرفة العامة، واستدعاء الدوال."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "نفس نموذج Phi-3-medium ولكن مع حجم سياق أكبر، مناسب لـ RAG أو القليل من التلميحات."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "نموذج يحتوي على 14 مليار معلمة، جودة أفضل من Phi-3-mini، يركز على بيانات عالية الجودة وكثيفة الاستدلال."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "نفس نموذج Phi-3-mini ولكن مع حجم سياق أكبر، مناسب لـ RAG أو القليل من التلميحات."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "أصغر عضو في عائلة Phi-3، محسن للجودة وزمن الاستجابة المنخفض."}, "microsoft/Phi-3-small-128k-instruct": {"description": "نفس نموذج Phi-3-small ولكن مع حجم سياق أكبر، مناسب لـ RAG أو القليل من التلميحات."}, "microsoft/Phi-3-small-8k-instruct": {"description": "نموذج يحتوي على 7 مليارات معلمة، جودة أفضل من Phi-3-mini، يركز على بيانات عالية الجودة وكثيفة الاستدلال."}, "microsoft/Phi-3.5-mini-instruct": {"description": "نسخة محدثة من نموذج Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "نسخة محدثة من نموذج Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 هو نموذج لغوي تقدمه Microsoft AI، يتميز بأداء ممتاز في المحادثات المعقدة، واللغات المتعددة، والاستدلال، ومساعدات الذكاء."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B هو نموذج Wizard المتقدم من Microsoft، يظهر أداءً تنافسيًا للغاية."}, "minicpm-v": {"description": "MiniCPM-V هو نموذج متعدد الوسائط من الجيل الجديد تم إطلاقه بواسطة OpenBMB، ويتميز بقدرات استثنائية في التعرف على النصوص وفهم الوسائط المتعددة، ويدعم مجموعة واسعة من سيناريوهات الاستخدام."}, "ministral-3b-latest": {"description": "Ministral 3B هو نموذج حافة عالمي المستوى من Mistral."}, "ministral-8b-latest": {"description": "Ministral 8B هو نموذج حافة ذات قيمة ممتازة من Mistral."}, "mistral": {"description": "Mistral هو نموذج 7B أطلقته Mistral AI، مناسب لاحتياجات معالجة اللغة المتغيرة."}, "mistral-ai/Mistral-Large-2411": {"description": "النموذج الرائد من Mistral، مناسب للمهام المعقدة التي تتطلب قدرات استدلال واسعة النطاق أو تخصص عالي (توليد نصوص مركبة، توليد أكواد، RAG أو وكلاء)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo هو نموذج لغوي متقدم (LLM) يتمتع بأحدث قدرات الاستدلال والمعرفة العالمية والبرمجة ضمن فئته الحجمية."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small مناسب لأي مهمة لغوية تتطلب كفاءة عالية وزمن استجابة منخفض."}, "mistral-large": {"description": "Mixtral Large هو النموذج الرائد من Mistral، يجمع بين قدرات توليد الشيفرة، والرياضيات، والاستدلال، ويدعم نافذة سياق تصل إلى 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 هو نموذج لغوي كبير متقدم (LLM) بكثافة عالية، يضم 123 مليار معلمة، ويتمتع بقدرات استدلالية ومعرفية وبرمجية متطورة."}, "mistral-large-latest": {"description": "Mistral Large هو النموذج الرائد، يتفوق في المهام متعددة اللغات، والاستدلال المعقد، وتوليد الشيفرة، وهو الخيار المثالي للتطبيقات الراقية."}, "mistral-medium-latest": {"description": "Mistral Medium 3 يقدم أداءً متقدمًا بتكلفة 8 مرات أقل، مما يبسط بشكل جذري نشر المؤسسات."}, "mistral-nemo": {"description": "Mistral Nemo تم تطويره بالتعاون بين Mistral AI وNVIDIA، وهو نموذج 12B عالي الأداء."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 هو نموذج لغوي كبير (LLM) وهو نسخة معدلة بالتعليمات من Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "يمكن استخدام Mistral Small في أي مهمة تعتمد على اللغة تتطلب كفاءة عالية وزمن استجابة منخفض."}, "mistral-small-latest": {"description": "Mistral Small هو خيار فعال من حيث التكلفة وسريع وموثوق، مناسب لمهام الترجمة، والتلخيص، وتحليل المشاعر."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct معروف بأدائه العالي، مناسب لمهام لغوية متعددة."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B هو نموذج تم ضبطه حسب الطلب، يوفر إجابات محسنة للمهام."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 يوفر قدرة حسابية فعالة وفهم اللغة الطبيعية، مناسب لمجموعة واسعة من التطبيقات."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B هو نموذج مضغوط ولكنه عالي الأداء، متفوق في المعالجة الجماعية والمهام البسيطة مثل التصنيف وتوليد النصوص، مع قدرة استدلال جيدة."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) هو نموذج لغوي كبير للغاية، يدعم احتياجات معالجة عالية جدًا."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B هو نموذج خبير مختلط مدرب مسبقًا، يستخدم لمهام النص العامة."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B هو نموذج خبير متفرق، يستفيد من معلمات متعددة لزيادة سرعة الاستدلال، مناسب لمعالجة المهام متعددة اللغات وتوليد الأكواد."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct هو نموذج صناعي عالي الأداء يجمع بين تحسين السرعة ودعم السياقات الطويلة."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo هو نموذج ببارامترات 7.3B يدعم عدة لغات ويتميز بأداء برمجي عالي."}, "mixtral": {"description": "Mixtral هو نموذج خبير من Mistral AI، يتمتع بأوزان مفتوحة المصدر، ويوفر دعمًا في توليد الشيفرة وفهم اللغة."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B يوفر قدرة حسابية متوازية عالية التحمل، مناسب للمهام المعقدة."}, "mixtral:8x22b": {"description": "Mixtral هو نموذج خبير من Mistral AI، يتمتع بأوزان مفتوحة المصدر، ويوفر دعمًا في توليد الشيفرة وفهم اللغة."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K هو نموذج يتمتع بقدرة معالجة سياقات طويلة جدًا، مناسب لتوليد نصوص طويلة جدًا، يلبي احتياجات المهام المعقدة، قادر على معالجة ما يصل إلى 128,000 توكن، مما يجعله مثاليًا للبحث، والأكاديميات، وتوليد الوثائق الكبيرة."}, "moonshot-v1-128k-vision-preview": {"description": "نموذج <PERSON>i البصري (بما في ذلك moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview وغيرها) قادر على فهم محتوى الصور، بما في ذلك النصوص والألوان وأشكال الأجسام."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K يوفر قدرة معالجة سياقات متوسطة الطول، قادر على معالجة 32,768 توكن، مناسب بشكل خاص لتوليد مجموعة متنوعة من الوثائق الطويلة والحوار المعقد، ويستخدم في إنشاء المحتوى، وتوليد التقارير، وأنظمة الحوار."}, "moonshot-v1-32k-vision-preview": {"description": "نموذج <PERSON>i البصري (بما في ذلك moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview وغيرها) قادر على فهم محتوى الصور، بما في ذلك النصوص والألوان وأشكال الأجسام."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K مصمم خصيصًا لتوليد مهام النصوص القصيرة، يتمتع بأداء معالجة فعال، قادر على معالجة 8,192 توكن، مما يجعله مثاليًا للحوار القصير، والتدوين السريع، وتوليد المحتوى السريع."}, "moonshot-v1-8k-vision-preview": {"description": "نموذج <PERSON>i البصري (بما في ذلك moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview وغيرها) قادر على فهم محتوى الصور، بما في ذلك النصوص والألوان وأشكال الأجسام."}, "moonshot-v1-auto": {"description": "يمكن لـ Moonshot V1 Auto اختيار النموذج المناسب بناءً على عدد الرموز المستخدمة في السياق الحالي."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B هو نموذج مفتوح المصدر للبرمجة، تم تحسينه عبر تعلم معزز واسع النطاق، قادر على إنتاج تصحيحات مستقرة وجاهزة للإنتاج مباشرة. حقق هذا النموذج نتيجة قياسية جديدة بنسبة 60.4% على SWE-bench Verified، محطماً الأرقام القياسية للنماذج المفتوحة المصدر في مهام هندسة البرمجيات الآلية مثل إصلاح العيوب ومراجعة الشيفرة."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 هو نموذج أساسي يعتمد على بنية MoE يتمتع بقدرات قوية في البرمجة والوكيل، يحتوي على 1 تريليون معلمة و32 مليار معلمة مفعلة. يتفوق نموذج K2 في اختبارات الأداء الأساسية في مجالات المعرفة العامة، البرمجة، الرياضيات والوكيل مقارنة بالنماذج المفتوحة المصدر الأخرى."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 هو نموذج أساسي مبني على بنية MoE يتمتع بقدرات فائقة في البرمجة والوكيل، مع إجمالي 1 تريليون معلمة و32 مليار معلمة مفعلة. في اختبارات الأداء المعيارية في مجالات المعرفة العامة، البرمجة، الرياضيات، والوكيل، يتفوق نموذج K2 على النماذج المفتوحة المصدر الرئيسية الأخرى."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B هو إصدار مطور من Nous Hermes 2، ويحتوي على أحدث مجموعات البيانات المطورة داخليًا."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B هو نموذج لغوي كبير مخصص من NVIDIA، يهدف إلى تحسين استجابة LLM لمساعدة استفسارات المستخدمين. لقد أظهر النموذج أداءً ممتازًا في اختبارات المعايير مثل Arena Hard وAlpacaEval 2 LC وGPT-4-Turbo MT-Bench، حيث احتل المرتبة الأولى في جميع اختبارات المحاذاة التلقائية الثلاثة حتى 1 أكتوبر 2024. تم تدريب النموذج باستخدام RLHF (خاصة REINFORCE) وLlama-3.1-Nemotron-70B-Reward وHelpSteer2-Preference على أساس نموذج Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "نموذج لغوي فريد، يقدم دقة وأداء لا مثيل لهما."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B هو نموذج لغوي كبير مخصص من NVIDIA، مصمم لتحسين فائدة الاستجابات التي يولدها LLM."}, "o1": {"description": "يركز على الاستدلال المتقدم وحل المشكلات المعقدة، بما في ذلك المهام الرياضية والعلمية. مثالي للتطبيقات التي تتطلب فهمًا عميقًا للسياق وإدارة سير العمل."}, "o1-mini": {"description": "o1-mini هو نموذج استدلال سريع وفعال من حيث التكلفة مصمم لتطبيقات البرمجة والرياضيات والعلوم. يحتوي هذا النموذج على 128K من السياق وتاريخ انتهاء المعرفة في أكتوبر 2023."}, "o1-preview": {"description": "o1 هو نموذج استدلال جديد من OpenAI، مناسب للمهام المعقدة التي تتطلب معرفة عامة واسعة. يحتوي هذا النموذج على 128K من السياق وتاريخ انتهاء المعرفة في أكتوبر 2023."}, "o1-pro": {"description": "نماذج سلسلة o1 مدربة بالتعلم المعزز، قادرة على التفكير قبل الإجابة وتنفيذ مهام استدلال معقدة. يستخدم نموذج o1-pro موارد حسابية أكبر للتفكير الأعمق، مما يضمن تقديم إجابات ذات جودة أعلى باستمرار."}, "o3": {"description": "o3 هو نموذج قوي شامل، يظهر أداءً ممتازًا في مجالات متعددة. يضع معايير جديدة في المهام الرياضية، العلمية، البرمجية، واستدلال الرؤية. كما أنه بارع في الكتابة التقنية واتباع التعليمات. يمكن للمستخدمين استخدامه لتحليل النصوص، الأكواد، والصور، وحل المشكلات المعقدة متعددة الخطوات."}, "o3-deep-research": {"description": "o3-deep-research هو نموذج البحث العميق الأكثر تقدمًا لدينا، مصمم خصيصًا للتعامل مع مهام البحث المعقدة متعددة الخطوات. يمكنه البحث وتجميع المعلومات من الإنترنت، كما يمكنه الوصول إلى بياناتك الخاصة واستخدامها من خلال موصل MCP."}, "o3-mini": {"description": "o3-mini هو أحدث نموذج استدلال صغير لدينا، يقدم ذكاءً عالياً تحت نفس تكاليف التأخير والأداء مثل o1-mini."}, "o3-pro": {"description": "نموذج o3-pro يستخدم موارد حسابية أكبر للتفكير الأعمق وتقديم إجابات أفضل باستمرار، ويدعم الاستخدام فقط عبر واجهة برمجة التطبيقات Responses API."}, "o4-mini": {"description": "o4-mini هو أحدث نموذج صغير من سلسلة o. تم تحسينه للاستدلال السريع والفعال، ويظهر كفاءة وأداء عاليين في المهام البرمجية والرؤية."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research هو نموذج البحث العميق الأسرع والأكثر اقتصادية لدينا — مثالي للتعامل مع مهام البحث المعقدة متعددة الخطوات. يمكنه البحث وتجميع المعلومات من الإنترنت، كما يمكنه الوصول إلى بياناتك الخاصة واستخدامها من خلال موصل MCP."}, "open-codestral-mamba": {"description": "Codestral Mamba هو نموذج لغة Mamba 2 يركز على توليد الشيفرة، ويوفر دعمًا قويًا لمهام الشيفرة المتقدمة والاستدلال."}, "open-mistral-7b": {"description": "Mistral 7B هو نموذج مدمج ولكنه عالي الأداء، يتفوق في معالجة الدفعات والمهام البسيطة، مثل التصنيف وتوليد النصوص، ويتميز بقدرة استدلال جيدة."}, "open-mistral-nemo": {"description": "Mistral Nemo هو نموذج 12B تم تطويره بالتعاون مع Nvidia، يوفر أداء استدلال وترميز ممتاز، سهل التكامل والاستبدال."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B هو نموذج خبير أكبر، يركز على المهام المعقدة، ويوفر قدرة استدلال ممتازة وإنتاجية أعلى."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B هو نموذج خبير نادر، يستخدم عدة معلمات لزيادة سرعة الاستدلال، مناسب لمعالجة المهام متعددة اللغات وتوليد الشيفرة."}, "openai/gpt-4.1": {"description": "GPT-4.1 هو نموذجنا الرائد للمهام المعقدة. إنه مثالي لحل المشكلات عبر مجالات متعددة."}, "openai/gpt-4.1-mini": {"description": "يوفر GPT-4.1 mini توازنًا بين الذكاء والسرعة والتكلفة، مما يجعله نموذجًا جذابًا للعديد من الاستخدامات."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano هو أسرع وأقل تكلفة من نماذج GPT-4.1."}, "openai/gpt-4o": {"description": "ChatGPT-4o هو نموذج ديناميكي يتم تحديثه في الوقت الحقيقي للحفاظ على أحدث إصدار. يجمع بين فهم اللغة القوي وقدرة التوليد، مما يجعله مناسبًا لمجموعة واسعة من التطبيقات، بما في ذلك خدمة العملاء والتعليم والدعم الفني."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini هو أحدث نموذج من OpenAI تم إطلاقه بعد GPT-4 Omni، ويدعم إدخال النصوص والصور وإخراج النصوص. كأحد نماذجهم المتقدمة الصغيرة، فهو أرخص بكثير من النماذج الرائدة الأخرى في الآونة الأخيرة، وأرخص بأكثر من 60% من GPT-3.5 Turbo. يحتفظ بذكاء متقدم مع قيمة ممتازة. حصل GPT-4o mini على 82% في اختبار MMLU، وهو حاليًا يتفوق على GPT-4 في تفضيلات الدردشة."}, "openai/o1": {"description": "o1 هو نموذج الاستدلال الجديد من OpenAI، يدعم إدخال الصور والنصوص ويخرج نصًا، مناسب للمهام المعقدة التي تتطلب معرفة عامة واسعة. يتميز هذا النموذج بسياق يصل إلى 200 ألف كلمة وتاريخ معرفة حتى أكتوبر 2023."}, "openai/o1-mini": {"description": "o1-mini هو نموذج استدلال سريع وفعال من حيث التكلفة مصمم لتطبيقات البرمجة والرياضيات والعلوم. يحتوي هذا النموذج على 128K من السياق وتاريخ انتهاء المعرفة في أكتوبر 2023."}, "openai/o1-preview": {"description": "o1 هو نموذج استدلال جديد من OpenAI، مناسب للمهام المعقدة التي تتطلب معرفة عامة واسعة. يحتوي هذا النموذج على 128K من السياق وتاريخ انتهاء المعرفة في أكتوبر 2023."}, "openai/o3": {"description": "o3 هو نموذج قوي شامل، يظهر أداءً ممتازًا في مجالات متعددة. إنه يضع معيارًا جديدًا لمهام الرياضيات والعلوم والبرمجة والتفكير البصري. كما أنه بارع في الكتابة التقنية واتباع التعليمات. يمكن للمستخدمين الاستفادة منه في تحليل النصوص والرموز والصور، وحل المشكلات المعقدة متعددة الخطوات."}, "openai/o3-mini": {"description": "o3-mini يقدم ذكاءً عاليًا بنفس تكلفة وأهداف التأخير مثل o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini عالي المستوى من حيث الاستدلال، يقدم ذكاءً عاليًا بنفس تكلفة وأهداف التأخير مثل o1-mini."}, "openai/o4-mini": {"description": "o4-mini تم تحسينه للاستدلال السريع والفعال، ويظهر كفاءة وأداء عاليين في المهام البرمجية والرؤية."}, "openai/o4-mini-high": {"description": "o4-mini إصدار عالي من حيث مستوى الاستدلال، تم تحسينه للاستدلال السريع والفعال، ويظهر كفاءة وأداء عاليين في المهام البرمجية والرؤية."}, "openrouter/auto": {"description": "استنادًا إلى طول السياق، والموضوع، والتعقيد، سيتم إرسال طلبك إلى Llama 3 70B Instruct، أو Claude 3.5 Sonnet (التعديل الذاتي) أو GPT-4o."}, "phi3": {"description": "Phi-3 هو نموذج مفتوح خفيف الوزن أطلقته Microsoft، مناسب للتكامل الفعال واستدلال المعرفة على نطاق واسع."}, "phi3:14b": {"description": "Phi-3 هو نموذج مفتوح خفيف الوزن أطلقته Microsoft، مناسب للتكامل الفعال واستدلال المعرفة على نطاق واسع."}, "pixtral-12b-2409": {"description": "نموذج Pixtral يظهر قدرات قوية في فهم الرسوم البيانية والصور، والإجابة على الأسئلة المتعلقة بالمستندات، والاستدلال متعدد الوسائط، واتباع التعليمات، مع القدرة على إدخال الصور بدقة طبيعية ونسبة عرض إلى ارتفاع، بالإضافة إلى معالجة عدد غير محدود من الصور في نافذة سياق طويلة تصل إلى 128K توكن."}, "pixtral-large-latest": {"description": "بيكسترا لارج هو نموذج متعدد الوسائط مفتوح المصدر يحتوي على 124 مليار معلمة، مبني على نموذج ميسترال لارج 2. هذا هو النموذج الثاني في عائلتنا متعددة الوسائط، ويظهر مستوى متقدم من القدرة على فهم الصور."}, "pro-128k": {"description": "سبارك برو 128K مزود بقدرة معالجة سياق كبيرة جدًا، قادر على معالجة ما يصل إلى 128K من معلومات السياق، مما يجعله مناسبًا بشكل خاص للتحليل الشامل ومعالجة الروابط المنطقية طويلة الأمد في المحتوى الطويل، ويمكنه تقديم منطق سلس ومتسق ودعم متنوع للاقتباسات في الاتصالات النصية المعقدة."}, "qvq-72b-preview": {"description": "نموذج QVQ هو نموذج بحث تجريبي تم تطويره بواسطة فريق Qwen، يركز على تعزيز قدرات الاستدلال البصري، خاصة في مجال الاستدلال الرياضي."}, "qvq-max": {"description": "نموذج Tongyi Qianwen QVQ للاستدلال البصري، يدعم الإدخال البصري وإخراج سلسلة التفكير، ويظهر قدرة أقوى في الرياضيات، البرمجة، التحليل البصري، الإبداع، والمهام العامة."}, "qvq-plus": {"description": "نموذج استدلال بصري يدعم الإدخال البصري وإخراج سلسلة التفكير. النسخة بلس التي تلت نموذج qvq-max، تتميز بسرعة استدلال أعلى وتوازن أفضل بين الأداء والتكلفة مقارنة بنموذج qvq-max."}, "qwen-coder-plus": {"description": "نموذج Tongyi <PERSON>wen للبرمجة."}, "qwen-coder-turbo": {"description": "نموذج Tongyi <PERSON>wen للبرمجة."}, "qwen-coder-turbo-latest": {"description": "نموذج <PERSON>wen للبرمجة."}, "qwen-long": {"description": "نموذج <PERSON>wen العملاق للغة، يدعم سياقات نصية طويلة، بالإضافة إلى وظائف الحوار المستندة إلى الوثائق الطويلة والعديد من الوثائق."}, "qwen-math-plus": {"description": "نموذج Tongyi Qianwen للرياضيات مخصص لحل المسائل الرياضية."}, "qwen-math-plus-latest": {"description": "نموذج <PERSON>wen الرياضي مصمم خصيصًا لحل المسائل الرياضية."}, "qwen-math-turbo": {"description": "نموذج Tongyi Qianwen للرياضيات مخصص لحل المسائل الرياضية."}, "qwen-math-turbo-latest": {"description": "نموذج <PERSON>wen الرياضي مصمم خصيصًا لحل المسائل الرياضية."}, "qwen-max": {"description": "نموذج لغة ضخم من توغي بمستوى مئات المليارات، يدعم إدخال لغات مختلفة مثل الصينية والإنجليزية. هو النموذج الذي يقف خلف إصدار توغي 2.5."}, "qwen-omni-turbo": {"description": "سلسلة نماذج Qwen-Omni تدعم إدخال بيانات متعددة الوسائط، بما في ذلك الفيديو، الصوت، الصور، والنص، وتنتج صوتًا ونصًا."}, "qwen-plus": {"description": "نموذج لغة ضخم من توغي، نسخة معززة، يدعم إدخال لغات مختلفة مثل الصينية والإنجليزية."}, "qwen-turbo": {"description": "نموذج لغة ضخم من توغي، يدعم إدخال لغات مختلفة مثل الصينية والإنجليزية."}, "qwen-vl-chat-v1": {"description": "نموذج <PERSON>wen العملاق للغة البصرية يدعم طرق تفاعل مرنة، بما في ذلك الصور المتعددة، والأسئلة والأجوبة المتعددة، والإبداع."}, "qwen-vl-max": {"description": "نموذج Tongyi Qianwen البصري فائق الحجم. مقارنة بالنسخة المعززة، يعزز مرة أخرى قدرة الاستدلال البصري والامتثال للتعليمات، ويوفر مستوى أعلى من الإدراك البصري والمعرفي."}, "qwen-vl-max-latest": {"description": "نموذج اللغة البصرية الكبير Qwen. مقارنةً بالنسخة المحسّنة، تعزز مرة أخرى من قدرة الاستدلال البصري وقدرة اتباع التعليمات، مما يوفر مستوى أعلى من الإدراك البصري والمعرفة."}, "qwen-vl-ocr": {"description": "نموذج OCR الخاص بـ Tongyi Qianwen مخصص لاستخراج النصوص، يركز على استخراج النصوص من الصور مثل الوثائق، الجداول، الأسئلة، والكتابة اليدوية. يمكنه التعرف على عدة لغات، منها: الصينية، الإنجليزية، الفرنسية، اليابانية، الكورية، الألمانية، الروسية، الإيطالية، الفيتنامية، والعربية."}, "qwen-vl-plus": {"description": "نسخة معززة من نموذج Tongyi Qianwen الكبير للغة البصرية. تعزز بشكل كبير قدرة التعرف على التفاصيل والقدرة على التعرف على النصوص، تدعم صورًا بدقة تزيد عن مليون بكسل وأبعاد بأي نسبة عرض إلى ارتفاع."}, "qwen-vl-plus-latest": {"description": "نسخة محسّنة من نموذج اللغة البصرية الكبير Qwen. تعزز بشكل كبير من قدرة التعرف على التفاصيل وقدرة التعرف على النصوص، وتدعم دقة تصل إلى أكثر من مليون بكسل وأبعاد صور بأي نسبة عرض إلى ارتفاع."}, "qwen-vl-v1": {"description": "نموذج تم تدريبه باستخدام نموذج Qwen-7B اللغوي، مع إضافة نموذج الصور، بدقة إدخال الصور 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 هو سلسلة جديدة من نماذج اللغة الكبيرة Qwen. Qwen2 7B هو نموذج يعتمد على بنية transformer، ويظهر أداءً ممتازًا في فهم اللغة، والقدرات متعددة اللغات، والبرمجة، والرياضيات، والاستدلال."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 هو سلسلة جديدة من نماذج اللغة الكبيرة، تتمتع بقدرات فهم وتوليد أقوى."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL هو الإصدار الأحدث من نموذج Qwen-VL، وقد حقق أداءً متقدمًا في اختبارات الفهم البصري، بما في ذلك MathVista وDocVQA وRealWorldQA وMTVQA. يمكن لـ Qwen2-VL فهم مقاطع الفيديو التي تزيد مدتها عن 20 دقيقة، مما يتيح إجابات عالية الجودة على الأسئلة المستندة إلى الفيديو، والمحادثات، وإنشاء المحتوى. كما يتمتع بقدرات استدلال واتخاذ قرارات معقدة، ويمكن دمجه مع الأجهزة المحمولة والروبوتات، مما يتيح التشغيل التلقائي بناءً على البيئة البصرية والتعليمات النصية. بالإضافة إلى الإنجليزية والصينية، يدعم Qwen2-VL الآن فهم النصوص بلغات مختلفة في الصور، بما في ذلك معظم اللغات الأوروبية واليابانية والكورية والعربية والفيتنامية."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct هو أحد أحدث نماذج اللغة الكبيرة التي أصدرتها Alibaba Cloud. يتمتع هذا النموذج 72B بقدرات محسنة بشكل ملحوظ في مجالات الترميز والرياضيات. كما يوفر النموذج دعمًا متعدد اللغات، يغطي أكثر من 29 لغة، بما في ذلك الصينية والإنجليزية. وقد حقق النموذج تحسينات ملحوظة في اتباع التعليمات وفهم البيانات الهيكلية وتوليد المخرجات الهيكلية (خاصة JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct هو أحد أحدث نماذج اللغة الكبيرة التي أصدرتها Alibaba Cloud. يتمتع هذا النموذج 32B بقدرات محسنة بشكل ملحوظ في مجالات الترميز والرياضيات. كما يوفر النموذج دعمًا متعدد اللغات، يغطي أكثر من 29 لغة، بما في ذلك الصينية والإنجليزية. وقد حقق النموذج تحسينات ملحوظة في اتباع التعليمات وفهم البيانات الهيكلية وتوليد المخرجات الهيكلية (خاصة JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "نموذج لغوي موجه للغة الصينية والإنجليزية، يستهدف مجالات اللغة، والبرمجة، والرياضيات، والاستدلال، وغيرها."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "نموذج لغوي متقدم، يدعم توليد الشيفرة، والاستدلال، والإصلاح، ويغطي لغات البرمجة الرئيسية."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "نموذج قوي للبرمجة متوسطة الحجم، يدعم طول سياق يصل إلى 32K، بارع في البرمجة متعددة اللغات."}, "qwen/qwen3-14b": {"description": "Qwen3-14B هو نموذج لغوي سببي مكثف يحتوي على 14.8 مليار معلمة، مصمم للاستدلال المعقد والحوار الفعال. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الرياضيات، والبرمجة، والاستدلال المنطقي، ونمط \"غير التفكير\" المستخدم في الحوار العام. تم ضبط هذا النموذج ليكون مناسبًا للامتثال للتعليمات، واستخدام أدوات الوكلاء، والكتابة الإبداعية، واستخدامه عبر أكثر من 100 لغة ولهجة. يدعم بشكل أصلي معالجة 32K رمز، ويمكن توسيعها باستخدام التمديد القائم على YaRN إلى 131K رمز."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B هو نموذج لغوي سببي مكثف يحتوي على 14.8 مليار معلمة، مصمم للاستدلال المعقد والحوار الفعال. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الرياضيات، والبرمجة، والاستدلال المنطقي، ونمط \"غير التفكير\" المستخدم في الحوار العام. تم ضبط هذا النموذج ليكون مناسبًا للامتثال للتعليمات، واستخدام أدوات الوكلاء، والكتابة الإبداعية، واستخدامه عبر أكثر من 100 لغة ولهجة. يدعم بشكل أصلي معالجة 32K رمز، ويمكن توسيعها باستخدام التمديد القائم على YaRN إلى 131K رمز."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B هو نموذج مختلط خبير (MoE) يحتوي على 235 مليار معلمة تم تطويره بواسطة Qwen، حيث يتم تنشيط 22 مليار معلمة في كل تمرير للأمام. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الاستدلال المعقد، والرياضيات، ومهام البرمجة، ونمط \"غير التفكير\" المستخدم في الحوار العام. يظهر هذا النموذج قدرات استدلال قوية، ودعمًا للغات المتعددة (أكثر من 100 لغة ولهجة)، وقدرات متقدمة في الامتثال للتعليمات واستدعاء أدوات الوكلاء. يدعم بشكل أصلي معالجة نافذة سياق من 32K رمز، ويمكن توسيعها باستخدام التمديد القائم على YaRN إلى 131K رمز."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B هو نموذج مختلط خبير (MoE) يحتوي على 235 مليار معلمة تم تطويره بواسطة Qwen، حيث يتم تنشيط 22 مليار معلمة في كل تمرير للأمام. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الاستدلال المعقد، والرياضيات، ومهام البرمجة، ونمط \"غير التفكير\" المستخدم في الحوار العام. يظهر هذا النموذج قدرات استدلال قوية، ودعمًا للغات المتعددة (أكثر من 100 لغة ولهجة)، وقدرات متقدمة في الامتثال للتعليمات واستدعاء أدوات الوكلاء. يدعم بشكل أصلي معالجة نافذة سياق من 32K رمز، ويمكن توسيعها باستخدام التمديد القائم على YaRN إلى 131K رمز."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 هو الجيل الأحدث من سلسلة نماذج اللغة الكبيرة Qwen، ويتميز بهيكل مختلط مكثف وخبير (MoE)، حيث يظهر أداءً ممتازًا في الاستدلال، ودعم اللغات المتعددة، والمهام المتقدمة. تضمن قدرته الفريدة على التبديل بسلاسة بين نمط التفكير المعقد ونمط الحوار الفعال أداءً متعدد الاستخدامات وعالي الجودة.\n\nيتفوق Qwen3 بشكل ملحوظ على النماذج السابقة مثل QwQ وQwen2.5، حيث يقدم قدرات استثنائية في الرياضيات، والترميز، والاستدلال العام، والكتابة الإبداعية، والحوار التفاعلي. يحتوي نموذج Qwen3-30B-A3B على 30.5 مليار معلمة (3.3 مليار معلمة نشطة)، و48 طبقة، و128 خبيرًا (يتم تنشيط 8 لكل مهمة)، ويدعم حتى 131K من سياق الرموز (باستخدام YaRN)، مما يضع معيارًا جديدًا للنماذج مفتوحة المصدر."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 هو الجيل الأحدث من سلسلة نماذج اللغة الكبيرة Qwen، ويتميز بهيكل مختلط مكثف وخبير (MoE)، حيث يظهر أداءً ممتازًا في الاستدلال، ودعم اللغات المتعددة، والمهام المتقدمة. تضمن قدرته الفريدة على التبديل بسلاسة بين نمط التفكير المعقد ونمط الحوار الفعال أداءً متعدد الاستخدامات وعالي الجودة.\n\nيتفوق Qwen3 بشكل ملحوظ على النماذج السابقة مثل QwQ وQwen2.5، حيث يقدم قدرات استثنائية في الرياضيات، والترميز، والاستدلال العام، والكتابة الإبداعية، والحوار التفاعلي. يحتوي نموذج Qwen3-30B-A3B على 30.5 مليار معلمة (3.3 مليار معلمة نشطة)، و48 طبقة، و128 خبيرًا (يتم تنشيط 8 لكل مهمة)، ويدعم حتى 131K من سياق الرموز (باستخدام YaRN)، مما يضع معيارًا جديدًا للنماذج مفتوحة المصدر."}, "qwen/qwen3-32b": {"description": "Qwen3-32B هو نموذج لغوي سببي مكثف يحتوي على 32.8 مليار معلمة، تم تحسينه للاستدلال المعقد والحوار الفعال. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الرياضيات والترميز والاستدلال المنطقي، ونمط \"غير التفكير\" المستخدم في الحوار العام الأسرع. يظهر هذا النموذج أداءً قويًا في الامتثال للتعليمات، واستخدام أدوات الوكلاء، والكتابة الإبداعية، واستخدامه عبر أكثر من 100 لغة ولهجة. يدعم بشكل أصلي معالجة 32K رمز، ويمكن توسيعها باستخدام التمديد القائم على YaRN إلى 131K رمز."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B هو نموذج لغوي سببي مكثف يحتوي على 32.8 مليار معلمة، تم تحسينه للاستدلال المعقد والحوار الفعال. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الرياضيات والترميز والاستدلال المنطقي، ونمط \"غير التفكير\" المستخدم في الحوار العام الأسرع. يظهر هذا النموذج أداءً قويًا في الامتثال للتعليمات، واستخدام أدوات الوكلاء، والكتابة الإبداعية، واستخدامه عبر أكثر من 100 لغة ولهجة. يدعم بشكل أصلي معالجة 32K رمز، ويمكن توسيعها باستخدام التمديد القائم على YaRN إلى 131K رمز."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B هو نموذج لغوي سببي مكثف يحتوي على 8.2 مليار معلمة، مصمم للمهام التي تتطلب استدلالًا مكثفًا والحوار الفعال. يدعم التبديل بسلاسة بين نمط \"التفكير\" المستخدم في الرياضيات والترميز والاستدلال المنطقي، ونمط \"غير التفكير\" المستخدم في الحوار العام. تم ضبط هذا النموذج ليكون مناسبًا للامتثال للتعليمات، ودمج الوكلاء، والكتابة الإبداعية، واستخدامه عبر أكثر من 100 لغة ولهجة. يدعم بشكل أصلي نافذة سياق من 32K رمز، ويمكن توسيعها إلى 131K رمز عبر YaRN."}, "qwen2": {"description": "Qwen2 هو نموذج لغوي كبير من الجيل الجديد من Alibaba، يدعم أداءً ممتازًا لتلبية احتياجات التطبيقات المتنوعة."}, "qwen2-72b-instruct": {"description": "Qwen2 هو سلسلة نماذج لغوية كبيرة جديدة تم إطلاقها من قبل فريق Qwen. تعتمد هذه النماذج على هندسة Transformer وتستخدم دالة التنشيط SwiGLU، وتحيز الانتباه QKV (attention QKV bias)، وانتباه الاستفسار الجماعي (group query attention)، وخلط انتباه النافذة المتزحلقة والانتباه الكامل (mixture of sliding window attention and full attention). بالإضافة إلى ذلك، قام فريق Qwen بتحسين مجزئ يتكيف مع العديد من اللغات الطبيعية والأكواد."}, "qwen2-7b-instruct": {"description": "Qwen2 هو سلسلة نماذج لغوية كبيرة جديدة تم طرحها من قبل فريق Qwen. يعتمد هذا النموذج على هندسة Transformer، ويستخدم دالة التنشيط SwiGLU، وتحيز QKV للانتباه (attention QKV bias)، وانتباه الاستفسار الجماعي (group query attention)، وخلط انتباه النافذة المتزحلقة والانتباه الكامل. بالإضافة إلى ذلك، قام فريق Qwen بتحسين المقطّع الذي يتكيف مع العديد من اللغات الطبيعية والأكواد."}, "qwen2.5": {"description": "Qwen2.5 هو الجيل الجديد من نماذج اللغة الكبيرة من Alibaba، يدعم احتياجات التطبيقات المتنوعة بأداء ممتاز."}, "qwen2.5-14b-instruct": {"description": "نموذج Qwen 2.5 مفتوح المصدر بحجم 14B."}, "qwen2.5-14b-instruct-1m": {"description": "نموذج بحجم 72B مفتوح المصدر من <PERSON> 2.5."}, "qwen2.5-32b-instruct": {"description": "نموذج Qwen 2.5 مفتوح المصدر بحجم 32B."}, "qwen2.5-72b-instruct": {"description": "نموذج Qwen 2.5 مفتوح المصدر بحجم 72B."}, "qwen2.5-7b-instruct": {"description": "نموذ<PERSON> Qwen 2.5 مفتوح المصدر بحجم 7B."}, "qwen2.5-coder-1.5b-instruct": {"description": "نموذج كود تونغي، النسخة مفتوحة المصدر."}, "qwen2.5-coder-14b-instruct": {"description": "نسخة مفتوحة المصدر من نموذج Tongyi <PERSON>wen للبرمجة."}, "qwen2.5-coder-32b-instruct": {"description": "الإصدار المفتوح من نموذج كود <PERSON> الشامل."}, "qwen2.5-coder-7b-instruct": {"description": "نسخة مفتوحة المصدر من نموذج Qwen للبرمجة."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder هو أحدث نموذج لغوي كبير مخصص للبرمجة في سلسلة Qwen (المعروف سابقًا باسم CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 هي أحدث سلسلة من نماذج Qwen للغة الكبيرة. بالنسبة لـ Qwen2.5، قمنا بإصدار نماذج لغة أساسية متعددة ونماذج لغة مضبوطة بالتعليمات، مع نطاق معلمات يتراوح من 0.5 مليار إلى 72 مليار."}, "qwen2.5-math-1.5b-instruct": {"description": "نموذ<PERSON>wen-Math لديه قدرة قوية على حل المسائل الرياضية."}, "qwen2.5-math-72b-instruct": {"description": "نموذج Qwen-Math يتمتع بقدرات قوية في حل المسائل الرياضية."}, "qwen2.5-math-7b-instruct": {"description": "نموذج Qwen-Math يتمتع بقدرات قوية في حل المسائل الرياضية."}, "qwen2.5-omni-7b": {"description": "تدعم نماذج سلسلة Qwen-Omni إدخال بيانات متعددة الأنماط، بما في ذلك الفيديو والصوت والصور والنصوص، وتخرج الصوت والنص."}, "qwen2.5-vl-32b-instruct": {"description": "سلسلة نماذج Qwen2.5-VL تعزز مستوى الذكاء والفعّالية والملاءمة للنماذج، مما يجعل أداءها أفضل في سيناريوهات مثل المحادثات الطبيعية، وإنشاء المحتوى، وتقديم الخدمات المتخصصة، وتطوير الأكواد. يستخدم الإصدار 32B تقنية التعلم المعزز لتحسين النموذج، مقارنةً بنماذج سلسلة Qwen2.5 VL الأخرى، حيث يقدم أسلوب إخراج أكثر توافقًا مع تفضيلات البشر، وقدرة على استنتاج المسائل الرياضية المعقدة، بالإضافة إلى فهم واستدلال دقيق للصور."}, "qwen2.5-vl-72b-instruct": {"description": "تحسين شامل في اتباع التعليمات، الرياضيات، حل المشكلات، والبرمجة، وزيادة قدرة التعرف على العناصر البصرية، يدعم تنسيقات متعددة لتحديد العناصر البصرية بدقة، ويدعم فهم ملفات الفيديو الطويلة (حتى 10 دقائق) وتحديد اللحظات الزمنية بدقة، قادر على فهم التسلسل الزمني والسرعة، يدعم التحكم في أنظمة التشغيل أو الوكلاء المحمولة بناءً على قدرات التحليل والتحديد، قوي في استخراج المعلومات الرئيسية وإخراج البيانات بتنسيق Json، هذه النسخة هي النسخة 72B، وهي الأقوى في هذه السلسلة."}, "qwen2.5-vl-7b-instruct": {"description": "تحسين شامل في اتباع التعليمات، الرياضيات، حل المشكلات، والبرمجة، وزيادة قدرة التعرف على العناصر البصرية، يدعم تنسيقات متعددة لتحديد العناصر البصرية بدقة، ويدعم فهم ملفات الفيديو الطويلة (حتى 10 دقائق) وتحديد اللحظات الزمنية بدقة، قادر على فهم التسلسل الزمني والسرعة، يدعم التحكم في أنظمة التشغيل أو الوكلاء المحمولة بناءً على قدرات التحليل والتحديد، قوي في استخراج المعلومات الرئيسية وإخراج البيانات بتنسيق Json، هذه النسخة هي النسخة 72B، وهي الأقوى في هذه السلسلة."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL هو أحدث إصدار من نماذج الرؤية واللغة في عائلة نماذج Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 هو الجيل الجديد من نماذج اللغة الكبيرة من Alibaba، يدعم احتياجات التطبيقات المتنوعة بأداء ممتاز."}, "qwen2.5:1.5b": {"description": "Qwen2.5 هو الجيل الجديد من نماذج اللغة الكبيرة من Alibaba، يدعم احتياجات التطبيقات المتنوعة بأداء ممتاز."}, "qwen2.5:72b": {"description": "Qwen2.5 هو الجيل الجديد من نماذج اللغة الكبيرة من Alibaba، يدعم احتياجات التطبيقات المتنوعة بأداء ممتاز."}, "qwen2:0.5b": {"description": "Qwen2 هو نموذج لغوي كبير من الجيل الجديد من Alibaba، يدعم أداءً ممتازًا لتلبية احتياجات التطبيقات المتنوعة."}, "qwen2:1.5b": {"description": "Qwen2 هو نموذج لغوي كبير من الجيل الجديد من Alibaba، يدعم أداءً ممتازًا لتلبية احتياجات التطبيقات المتنوعة."}, "qwen2:72b": {"description": "Qwen2 هو نموذج لغوي كبير من الجيل الجديد من Alibaba، يدعم أداءً ممتازًا لتلبية احتياجات التطبيقات المتنوعة."}, "qwen3": {"description": "Qwen3 هو الجيل الجديد من نموذج اللغة واسع النطاق من علي بابا، يدعم مجموعة متنوعة من احتياجات التطبيقات بأداء ممتاز."}, "qwen3-0.6b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-1.7b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-14b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-235b-a22b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-235b-a22b-instruct-2507": {"description": "نموذج مفتوح المصدر غير تفكيري مبني على Qwen3، مع تحسينات طفيفة في القدرات الإبداعية والسلامة مقارنة بالإصدار السابق (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "نموذج مفتوح المصدر تفكيري مبني على Qwen3، مع تحسينات كبيرة في القدرات المنطقية، العامة، تعزيز المعرفة والإبداع مقارنة بالإصدار السابق (Tongyi Qianwen 3-235B-A22B)، مناسب للمهام المعقدة التي تتطلب استدلالًا قويًا."}, "qwen3-30b-a3b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-30b-a3b-instruct-2507": {"description": "تحسنت القدرات العامة للنموذج بشكل كبير في اللغتين الصينية والإنجليزية واللغات المتعددة مقارنة بالإصدار السابق (Qwen3-30B-A3B). تم تحسين المهام المفتوحة الذاتية بشكل خاص لتتوافق بشكل أفضل مع تفضيلات المستخدم، مما يمكنه من تقديم ردود أكثر فائدة."}, "qwen3-30b-a3b-thinking-2507": {"description": "نموذج مفتوح المصدر لوضع التفكير مبني على Qwen3، مع تحسينات كبيرة في القدرات المنطقية، والقدرات العامة، وتعزيز المعرفة، والقدرة الإبداعية مقارنة بالإصدار السابق (Tongyi Qianwen 3-30B-A3B)، مناسب للسيناريوهات التي تتطلب استدلالًا عالي الصعوبة."}, "qwen3-32b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-4b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-8b": {"description": "Qwen3 هو نموذج جديد من الجيل التالي مع تحسينات كبيرة في القدرات، حيث يصل إلى مستويات رائدة في الصناعة في الاستدلال، والعموم، والوكلاء، واللغات المتعددة، ويدعم التبديل بين أنماط التفكير."}, "qwen3-coder-480b-a35b-instruct": {"description": "نسخة مفتوحة المصدر من نموذج كود <PERSON>wen. أحدث نموذج qwen3-coder-480b-a35b-instruct مبني على Qwen3 لتوليد الكود، يتمتع بقدرات قوية كوكيل برمجي، بارع في استدعاء الأدوات والتفاعل مع البيئة، قادر على البرمجة الذاتية مع أداء برمجي ممتاز وقدرات عامة."}, "qwen3-coder-plus": {"description": "نموذج كود <PERSON>wen. أحدث سلسلة Qwen3-Coder-Plus مبنية على Qwen3 لتوليد الكود، تتمتع بقدرات قوية كوكيل برمجي، بارعة في استدعاء الأدوات والتفاعل مع البيئة، قادرة على البرمجة الذاتية مع أداء برمجي ممتاز وقدرات عامة."}, "qwq": {"description": "QwQ هو نموذج بحث تجريبي يركز على تحسين قدرات الاستدلال للذكاء الاصطناعي."}, "qwq-32b": {"description": "نموذج استدلال QwQ المدرب على نموذج Qwen2.5-32B، الذي يعزز بشكل كبير من قدرة الاستدلال للنموذج من خلال التعلم المعزز. تصل المؤشرات الأساسية للنموذج (AIME 24/25، LiveCodeBench) وبعض المؤشرات العامة (IFEval، LiveBench وغيرها) إلى مستوى DeepSeek-R1 الكامل، حيث تتجاوز جميع المؤشرات بشكل ملحوظ نموذج DeepSeek-R1-Distill-Qwen-32B المعتمد أيضًا على Qwen2.5-32B."}, "qwq-32b-preview": {"description": "نموذج QwQ هو نموذج بحث تجريبي تم تطويره بواسطة فريق Qwen، يركز على تعزيز قدرات الاستدلال للذكاء الاصطناعي."}, "qwq-plus": {"description": "نموذج استدلال QwQ المدرب على نموذج Qwen2.5، يعزز بشكل كبير قدرة الاستدلال من خلال التعلم المعزز. حقق النموذج مؤشرات رئيسية في الرياضيات والبرمجة (AIME 24/25، LiveCodeBench) وبعض المؤشرات العامة (IFEval، LiveBench وغيرها) بمستوى DeepSeek-R1 الكامل."}, "qwq_32b": {"description": "نموذج استدلال متوسط الحجم من سلسلة Qwen. مقارنة بنماذج تحسين التعليمات التقليدية، يظهر QwQ، الذي يتمتع بقدرة على التفكير والاستدلال، أداءً محسّنًا بشكل ملحوظ في المهام اللاحقة، خاصة عند حل المشكلات الصعبة."}, "r1-1776": {"description": "R1-1776 هو إصدار من نموذج DeepSeek R1، تم تدريبه لاحقًا لتقديم معلومات حقائق غير خاضعة للرقابة وغير متحيزة."}, "solar-mini": {"description": "Solar Mini هو نموذج LLM مدمج، يتفوق على GPT-3.5، ويتميز بقدرات متعددة اللغات قوية، ويدعم الإنجليزية والكورية، ويقدم حلولًا فعالة وصغيرة الحجم."}, "solar-mini-ja": {"description": "Solar Mini (Ja) يوسع من قدرات Solar Mini، مع التركيز على اللغة اليابانية، مع الحفاظ على الكفاءة والأداء الممتاز في استخدام الإنجليزية والكورية."}, "solar-pro": {"description": "Solar Pro هو نموذج LLM عالي الذكاء تم إطلاقه من قبل Upstage، يركز على قدرة اتباع التعليمات على وحدة معالجة الرسوميات الواحدة، وسجل IFEval فوق 80. حاليًا يدعم اللغة الإنجليزية، ومن المقرر إصدار النسخة الرسمية في نوفمبر 2024، مع توسيع دعم اللغات وطول السياق."}, "sonar": {"description": "منتج بحث خفيف الوزن يعتمد على سياق البحث، أسرع وأرخص من Sonar Pro."}, "sonar-deep-research": {"description": "تقوم Deep Research بإجراء أبحاث شاملة على مستوى الخبراء وتجميعها في تقارير يمكن الوصول إليها وقابلة للتنفيذ."}, "sonar-pro": {"description": "منتج بحث متقدم يدعم سياق البحث، مع دعم للاستعلامات المتقدمة والمتابعة."}, "sonar-reasoning": {"description": "منتج API الجديد المدعوم من نموذج الاستدلال من DeepSeek."}, "sonar-reasoning-pro": {"description": "منتج API جديد مدعوم من نموذج الاستدلال DeepSeek."}, "stable-diffusion-3-medium": {"description": "نموذج توليد صور نصية كبير أحدث من Stability AI. هذا الإصدار يحسن جودة الصور، فهم النصوص وتنوع الأساليب بشكل ملحوظ مقارنة بالأجيال السابقة، قادر على تفسير أوامر اللغة الطبيعية المعقدة بدقة وتوليد صور أكثر دقة وتنوعًا."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large هو نموذج مولد صور نصية متعدد الوسائط (MMDiT) يحتوي على 800 مليون معلمة، يتميز بجودة صور ممتازة وتوافق عالي مع الأوامر النصية، يدعم توليد صور عالية الدقة تصل إلى مليون بكسل، ويعمل بكفاءة على الأجهزة الاستهلاكية العادية."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo هو نموذج مبني على stable-diffusion-3.5-large يستخدم تقنية تقطير الانتشار التنافسي (ADD) لتحقيق سرعة أعلى."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 تم تهيئته باستخدام أوزان نقطة التحقق stable-diffusion-v1.2، وتم ضبطه بدقة على \"laion-aesthetics v2 5+\" بدقة 512x512 عبر 595 ألف خطوة، مع تقليل شرطية النص بنسبة 10% لتحسين التوليد بدون مصنف."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl يحتوي على تحسينات كبيرة مقارنة بالإصدار v1.5، ويعادل أداء نموذج midjourney المفتوح المصدر الرائد. تشمل التحسينات: بنية unet أكبر بثلاثة أضعاف، إضافة وحدة تحسين لتحسين جودة الصور المولدة، وتقنيات تدريب أكثر كفاءة."}, "stable-diffusion-xl-base-1.0": {"description": "نموذج توليد صور نصية كبير طورته Stability AI ومفتوح المصدر، يتميز بقدرات توليد صور إبداعية رائدة في الصناعة. يمتلك فهمًا ممتازًا للتعليمات ويدعم تعريف العكس (Reverse Prompt) لتوليد محتوى دقيق."}, "step-1-128k": {"description": "يوفر توازنًا بين الأداء والتكلفة، مناسب لمجموعة متنوعة من السيناريوهات."}, "step-1-256k": {"description": "يمتلك قدرة معالجة سياق طويلة جدًا، مناسب بشكل خاص لتحليل الوثائق الطويلة."}, "step-1-32k": {"description": "يدعم حوارات متوسطة الطول، مناسب لمجموعة متنوعة من تطبيقات السيناريو."}, "step-1-8k": {"description": "نموذج صغير، مناسب للمهام الخفيفة."}, "step-1-flash": {"description": "نموذج عالي السرعة، مناسب للحوار في الوقت الحقيقي."}, "step-1.5v-mini": {"description": "يمتلك هذا النموذج قدرة قوية على فهم الفيديو."}, "step-1o-turbo-vision": {"description": "يمتلك هذا النموذج قدرة قوية على فهم الصور، ويتفوق في مجالات الرياضيات والبرمجة مقارنةً بـ 1o. النموذج أصغر من 1o، وسرعة الإخراج أسرع."}, "step-1o-vision-32k": {"description": "يمتلك هذا النموذج قدرة قوية على فهم الصور. مقارنةً بسلسلة نماذج step-1v، فإنه يتمتع بأداء بصري أقوى."}, "step-1v-32k": {"description": "يدعم المدخلات البصرية، يعزز تجربة التفاعل متعدد الوسائط."}, "step-1v-8k": {"description": "نموذج بصري صغير، مناسب للمهام الأساسية المتعلقة بالنصوص والصور."}, "step-1x-edit": {"description": "نموذج متخصص في مهام تحرير الصور، قادر على تعديل وتعزيز الصور بناءً على الصور والأوصاف النصية التي يقدمها المستخدم. يدعم تنسيقات إدخال متعددة، بما في ذلك الأوصاف النصية والصور النموذجية. يفهم نية المستخدم ويولد نتائج تحرير صور متوافقة مع المتطلبات."}, "step-1x-medium": {"description": "نموذج قوي لتوليد الصور يدعم الإدخال عبر الأوصاف النصية. يدعم اللغة الصينية بشكل أصلي، قادر على فهم ومعالجة الأوصاف النصية الصينية بدقة، والتقاط المعاني الدلالية وتحويلها إلى ميزات صور لتحقيق توليد صور أكثر دقة. يولد صورًا عالية الدقة والجودة، ويمتلك قدرات نقل الأسلوب."}, "step-2-16k": {"description": "يدعم تفاعلات سياق كبيرة، مناسب لمشاهد الحوار المعقدة."}, "step-2-16k-exp": {"description": "الإصدار التجريبي من نموذج step-2، يحتوي على أحدث الميزات، يتم تحديثه بشكل دوري. لا يُوصى باستخدامه في بيئات الإنتاج الرسمية."}, "step-2-mini": {"description": "نموذج كبير سريع يعتمد على بنية الانتباه الجديدة MFA، يحقق نتائج مشابهة لـ step1 بتكلفة منخفضة جداً، مع الحفاظ على قدرة أعلى على المعالجة وزمن استجابة أسرع. يمكنه التعامل مع المهام العامة، ويتميز بقدرات قوية في البرمجة."}, "step-2x-large": {"description": "نموذج الجيل الجديد من Step Star، يركز على مهام توليد الصور، قادر على توليد صور عالية الجودة بناءً على الأوصاف النصية المقدمة من المستخدم. يتميز النموذج الجديد بجودة صور أكثر واقعية وقدرات أفضل في توليد النصوص الصينية والإنجليزية."}, "step-r1-v-mini": {"description": "هذا النموذج هو نموذج استدلال كبير يتمتع بقدرة قوية على فهم الصور، يمكنه معالجة المعلومات النصية والصورية، ويخرج نصوصًا بعد تفكير عميق. يظهر هذا النموذج أداءً بارزًا في مجال الاستدلال البصري، كما يمتلك قدرات رياضية، برمجية، ونصية من الدرجة الأولى. طول السياق هو 100k."}, "taichu_llm": {"description": "نموذج اللغة الكبير TaiChu يتمتع بقدرات قوية في فهم اللغة، بالإضافة إلى إنشاء النصوص، والإجابة على الأسئلة، وبرمجة الأكواد، والحسابات الرياضية، والاستدلال المنطقي، وتحليل المشاعر، وتلخيص النصوص. يجمع بشكل مبتكر بين التدريب المسبق على البيانات الضخمة والمعرفة الغنية من مصادر متعددة، من خلال تحسين تقنيات الخوارزميات باستمرار واستيعاب المعرفة الجديدة من البيانات النصية الضخمة، مما يحقق تطورًا مستمرًا في أداء النموذج. يوفر للمستخدمين معلومات وخدمات أكثر سهولة وتجربة أكثر ذكاءً."}, "taichu_o1": {"description": "taichu_o1 هو نموذج استدلال كبير من الجيل الجديد، يحقق سلسلة من التفكير الشبيه بالبشر من خلال التفاعل متعدد الوسائط والتعلم المعزز، يدعم استنتاجات القرارات المعقدة، ويظهر مسارات تفكير قابلة للنموذج مع الحفاظ على دقة عالية في المخرجات، مناسب لتحليل الاستراتيجيات والتفكير العميق."}, "taichu_vl": {"description": "يجمع بين فهم الصور، ونقل المعرفة، والاستدلال المنطقي، ويظهر أداءً بارزًا في مجال الأسئلة والأجوبة النصية والصورية."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct يحتوي على 80 مليار معلمة، ويمكن تفعيل 13 مليار معلمة فقط لمنافسة النماذج الأكبر، ويدعم الاستدلال المختلط بين \"التفكير السريع/التفكير البطيء\"؛ فهم مستقر للنصوص الطويلة؛ تم التحقق من قدرات الوكيل عبر BFCL-v3 وτ-Bench، مع أداء متقدم؛ يجمع بين GQA وتنسيقات التكميم المتعددة لتحقيق استدلال فعال."}, "text-embedding-3-large": {"description": "أقوى نموذج لتضمين النصوص، مناسب للمهام الإنجليزية وغير الإنجليزية."}, "text-embedding-3-small": {"description": "نموذج التضمين من الجيل الجديد، فعال واقتصادي، مناسب لاسترجاع المعرفة وتطبيقات RAG وغيرها."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 هو نموذج لغوي مفتوح الوزن ثنائي اللغة (صيني وإنجليزي) بحجم 32B، تم تحسينه لتوليد الشيفرات، استدعاءات الوظائف، والمهام الوكيلة. تم تدريبه مسبقًا على 15T من البيانات عالية الجودة وإعادة الاستدلال، وتم تحسينه باستخدام توافق تفضيلات البشر، أخذ العينات الرفض، والتعلم المعزز. يظهر هذا النموذج أداءً ممتازًا في الاستدلال المعقد، توليد القطع، ومهام الإخراج الهيكلي، حيث حقق أداءً يعادل GPT-4o وDeepSeek-V3-0324 في عدة اختبارات معيارية."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 هو نموذج لغوي مفتوح الوزن ثنائي اللغة (صيني وإنجليزي) بحجم 32B، تم تحسينه لتوليد الشيفرات، استدعاءات الوظائف، والمهام الوكيلة. تم تدريبه مسبقًا على 15T من البيانات عالية الجودة وإعادة الاستدلال، وتم تحسينه باستخدام توافق تفضيلات البشر، أخذ العينات الرفض، والتعلم المعزز. يظهر هذا النموذج أداءً ممتازًا في الاستدلال المعقد، توليد القطع، ومهام الإخراج الهيكلي، حيث حقق أداءً يعادل GPT-4o وDeepSeek-V3-0324 في عدة اختبارات معيارية."}, "thudm/glm-4-9b-chat": {"description": "الإصدار المفتوح من الجيل الأحدث من نموذج GLM-4 الذي أطلقته Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 هو نموذج لغوي يحتوي على 9 مليار معلمة من سلسلة GLM-4 التي تم تطويرها بواسطة THUDM. يستخدم GLM-4-9B-0414 نفس استراتيجيات تعزيز التعلم والتوافق المستخدمة في النموذج المقابل الأكبر 32B، مما يحقق أداءً عاليًا بالنسبة لحجمه، مما يجعله مناسبًا للنشر في البيئات المحدودة الموارد التي لا تزال تتطلب قدرات قوية في فهم اللغة وتوليدها."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 هو نسخة محسنة من GLM-4-32B، مصممة لحل المشكلات المعقدة في الرياضيات العميقة، المنطق، والشيفرات. يستخدم التعلم المعزز الموسع (المخصص للمهام والمبني على تفضيلات عامة) لتحسين الأداء في المهام المعقدة متعددة الخطوات. مقارنةً بنموذج GLM-4-32B الأساسي، زادت Z1 بشكل ملحوظ من قدرات الاستدلال الهيكلي والمجالات الرسمية.\n\nيدعم هذا النموذج تنفيذ خطوات \"التفكير\" من خلال هندسة التلميحات، ويقدم اتساقًا محسنًا للإخراج الطويل. تم تحسينه لعمليات سير العمل الخاصة بالوكيل، ويدعم السياقات الطويلة (عبر YaRN)، واستدعاءات أدوات JSON، وتكوينات أخذ العينات الدقيقة للاستدلال المستقر. مثالي للحالات التي تتطلب تفكيرًا عميقًا، استدلالًا متعدد الخطوات، أو استنتاجات رسمية."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 هو نسخة محسنة من GLM-4-32B، مصممة لحل المشكلات المعقدة في الرياضيات العميقة، المنطق، والشيفرات. يستخدم التعلم المعزز الموسع (المخصص للمهام والمبني على تفضيلات عامة) لتحسين الأداء في المهام المعقدة متعددة الخطوات. مقارنةً بنموذج GLM-4-32B الأساسي، زادت Z1 بشكل ملحوظ من قدرات الاستدلال الهيكلي والمجالات الرسمية.\n\nيدعم هذا النموذج تنفيذ خطوات \"التفكير\" من خلال هندسة التلميحات، ويقدم اتساقًا محسنًا للإخراج الطويل. تم تحسينه لعمليات سير العمل الخاصة بالوكيل، ويدعم السياقات الطويلة (عبر YaRN)، واستدعاءات أدوات JSON، وتكوينات أخذ العينات الدقيقة للاستدلال المستقر. مثالي للحالات التي تتطلب تفكيرًا عميقًا، استدلالًا متعدد الخطوات، أو استنتاجات رسمية."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 هو نموذج لغوي يحتوي على 9 مليار معلمة من سلسلة GLM-4 التي تم تطويرها بواسطة THUDM. يستخدم تقنيات تم تطبيقها في الأصل على نموذج GLM-Z1 الأكبر، بما في ذلك تعزيز التعلم الموسع، والتوافق القائم على الترتيب الثنائي، والتدريب على المهام التي تتطلب استدلالًا مكثفًا مثل الرياضيات، والترميز، والمنطق. على الرغم من حجمه الأصغر، إلا أنه يظهر أداءً قويًا في المهام العامة للاستدلال، ويتفوق على العديد من النماذج مفتوحة المصدر في مستوى وزنه."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B هو نموذج استدلال عميق يحتوي على 32 مليار معلمة من سلسلة GLM-4-Z1، تم تحسينه للمهام المعقدة والمفتوحة التي تتطلب تفكيرًا طويل الأمد. يعتمد على glm-4-32b-0414، ويضيف مراحل تعزيز التعلم الإضافية واستراتيجيات التوافق متعددة المراحل، ويقدم قدرة \"التفكير\" المصممة لمحاكاة معالجة الإدراك الموسع. يشمل ذلك الاستدلال التكراري، والتحليل متعدد القفزات، وسير العمل المعزز بالأدوات مثل البحث، والاسترجاع، والتوليف المدرك للاقتباسات.\n\nيظهر هذا النموذج أداءً ممتازًا في الكتابة البحثية، والتحليل المقارن، والأسئلة المعقدة. يدعم استدعاء الوظائف المستخدمة في البحث والتنقل (مثل `search`، `click`، `open`، `finish`)، مما يسمح باستخدامه في أنابيب الوكلاء. يتم تشكيل سلوك التفكير من خلال مكافآت قائمة على القواعد وآلية اتخاذ القرار المتأخرة، ويتم قياسه باستخدام أطر بحث عميقة مثل كومة التوافق الداخلية لـ OpenAI. هذا المتغير مناسب للسيناريوهات التي تتطلب عمقًا بدلاً من السرعة."}, "tngtech/deepseek-r1t-chimera:free": {"description": "تم إنشاء DeepSeek-R1T-Chimera من خلال دمج DeepSeek-R1 وDeepSeek-V3 (0324)، حيث يجمع بين قدرات الاستدلال لـ R1 وتحسين كفاءة الرموز لـ V3. يعتمد على هيكل DeepSeek-MoE Transformer، وتم تحسينه لمهام توليد النصوص العامة.\n\nيجمع هذا النموذج بين أوزان ما قبل التدريب من النموذجين المصدرين لتحقيق توازن بين الأداء في الاستدلال، والكفاءة، ومهام الامتثال للتعليمات. يتم إصداره بموجب ترخيص MIT، ويهدف للاستخدام في الأبحاث والأغراض التجارية."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) يوفر قدرة حسابية معززة من خلال استراتيجيات فعالة وهندسة نموذجية."}, "tts-1": {"description": "أحدث نموذج لتحويل النص إلى كلام، تم تحسينه للسرعة في السيناريوهات الحية."}, "tts-1-hd": {"description": "أحدث نموذج لتحويل النص إلى كلام، تم تحسينه للجودة."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) مناسب لمهام التعليمات الدقيقة، يوفر قدرة معالجة لغوية ممتازة."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet يرفع المعايير الصناعية، حيث يتفوق على نماذج المنافسين وClaude 3 Opus، ويظهر أداءً ممتازًا في تقييمات واسعة، مع سرعة وتكلفة تتناسب مع نماذجنا المتوسطة."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "كلود 3.7 سونيت هو أسرع نموذج من الجيل التالي من أنثروبيك. مقارنةً بكلود 3 هايكو، تم تحسين كلود 3.7 سونيت في جميع المهارات، وتجاوز العديد من اختبارات الذكاء لأكبر نموذج من الجيل السابق، كلود 3 أوبس."}, "v0-1.0-md": {"description": "نموذج v0-1.0-md هو نموذج قديم يتم تقديمه من خلال واجهة برمجة التطبيقات v0"}, "v0-1.5-lg": {"description": "نموذج v0-1.5-lg مناسب للمهام المتقدمة في التفكير أو الاستدلال"}, "v0-1.5-md": {"description": "نموذج v0-1.5-md مناسب للمهام اليومية وتوليد واجهات المستخدم (UI)"}, "wan2.2-t2i-flash": {"description": "نسخة Wanxiang 2.2 فائقة السرعة، أحدث نموذج حاليًا. تم تحسين الإبداع، الاستقرار، والواقعية بشكل شامل، مع سرعة توليد عالية وقيمة ممتازة مقابل التكلفة."}, "wan2.2-t2i-plus": {"description": "نسخة Wanxiang 2.2 الاحترافية، أحدث نموذج حاليًا. تم تحسين الإبداع، الاستقرار، والواقعية بشكل شامل، مع تفاصيل توليد غنية."}, "wanx-v1": {"description": "نموذج أساسي لتوليد الصور النصية. يتوافق مع نموذج Tongyi Wanxiang 1.0 الرسمي."}, "wanx2.0-t2i-turbo": {"description": "متخصص في توليد صور بورتريه واقعية، سرعة متوسطة وتكلفة منخفضة. يتوافق مع نموذج Tongyi Wanxiang 2.0 السريع الرسمي."}, "wanx2.1-t2i-plus": {"description": "نسخة مطورة شاملة. توليد صور بتفاصيل أكثر ثراءً، سرعة أقل قليلاً. يتوافق مع نموذج Tongyi Wanxiang 2.1 الاحترافي الرسمي."}, "wanx2.1-t2i-turbo": {"description": "نسخة مطورة شاملة. سرعة توليد عالية، أداء شامل، وقيمة ممتازة مقابل التكلفة. يتوافق مع نموذج Tongyi Wanxiang 2.1 السريع الرسمي."}, "whisper-1": {"description": "نموذج التعرف على الصوت العام، يدعم التعرف على الصوت بعدة لغات، الترجمة الصوتية، والتعرف على اللغة."}, "wizardlm2": {"description": "WizardLM 2 هو نموذج لغوي تقدمه Microsoft AI، يتميز بأداء ممتاز في الحوار المعقد، واللغات المتعددة، والاستدلال، والمساعدين الذكيين."}, "wizardlm2:8x22b": {"description": "WizardLM 2 هو نموذج لغوي تقدمه Microsoft AI، يتميز بأداء ممتاز في الحوار المعقد، واللغات المتعددة، والاستدلال، والمساعدين الذكيين."}, "x1": {"description": "سيتم ترقية نموذج Spark X1 بشكل أكبر، حيث ستحقق المهام العامة مثل الاستدلال، وتوليد النصوص، وفهم اللغة نتائج تتماشى مع OpenAI o1 و DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "يي-1.5 هو إصدار مُحدّث من يي. تم تدريبه بشكل مُسبق باستخدام مكتبة بيانات عالية الجودة تحتوي على 500 مليار علامة (Token) على يي، وتم تحسينه أيضًا باستخدام 3 ملايين مثال متنوع للتدريب الدقيق."}, "yi-large": {"description": "نموذج جديد بمليارات المعلمات، يوفر قدرة قوية على الإجابة وتوليد النصوص."}, "yi-large-fc": {"description": "يدعم ويعزز قدرة استدعاء الأدوات على نموذج yi-large، مناسب لمجموعة متنوعة من سيناريوهات الأعمال التي تتطلب بناء وكيل أو سير عمل."}, "yi-large-preview": {"description": "الإصدار الأولي، يوصى باستخدام yi-large (الإصدار الجديد)."}, "yi-large-rag": {"description": "خدمة متقدمة تعتمد على نموذج yi-large القوي، تجمع بين تقنيات الاسترجاع والتوليد لتوفير إجابات دقيقة، وخدمة استرجاع المعلومات من الإنترنت في الوقت الحقيقي."}, "yi-large-turbo": {"description": "عالية الكفاءة، أداء ممتاز. يتم ضبطها بدقة عالية لتحقيق توازن بين الأداء وسرعة الاستدلال والتكلفة."}, "yi-lightning": {"description": "نموذج جديد عالي الأداء، يضمن إنتاج جودة عالية مع زيادة كبيرة في سرعة الاستدلال."}, "yi-lightning-lite": {"description": "نسخة خفيفة الوزن، يُوصى باستخدام yi-lightning."}, "yi-medium": {"description": "نموذج متوسط الحجم تم تحسينه، يتمتع بقدرات متوازنة، وكفاءة عالية في التكلفة. تم تحسين قدرة اتباع التعليمات بشكل عميق."}, "yi-medium-200k": {"description": "نافذة سياق طويلة تصل إلى 200K، توفر قدرة عميقة على فهم وتوليد النصوص الطويلة."}, "yi-spark": {"description": "نموذج صغير ولكنه قوي، خفيف وسريع. يوفر قدرة معززة على العمليات الرياضية وكتابة الشيفرات."}, "yi-vision": {"description": "نموذج لمهام الرؤية المعقدة، يوفر قدرة عالية على فهم وتحليل الصور."}, "yi-vision-v2": {"description": "نموذج مهام بصرية معقدة، يوفر فهمًا عالي الأداء وقدرات تحليلية بناءً على صور متعددة."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 هو نموذج أساسي مصمم لتطبيقات الوكلاء الذكية، يستخدم بنية Mixture-of-Experts (MoE). تم تحسينه بعمق في مجالات استدعاء الأدوات، تصفح الويب، هندسة البرمجيات، وبرمجة الواجهة الأمامية، ويدعم التكامل السلس مع وكلاء الكود مثل Claude Code وRoo Code. يستخدم وضع استدلال مختلط ليتكيف مع سيناريوهات الاستدلال المعقدة والاستخدام اليومي."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air هو نموذج أساسي مصمم لتطبيقات الوكلاء الذكية، يستخدم بنية Mixture-of-Experts (MoE). تم تحسينه بعمق في مجالات استدعاء الأدوات، تصفح الويب، هندسة البرمجيات، وبرمجة الواجهة الأمامية، ويدعم التكامل السلس مع وكلاء الكود مثل Claude Code وRoo Code. يستخدم وضع استدلال مختلط ليتكيف مع سيناريوهات الاستدلال المعقدة والاستخدام اليومي."}}