{"azure": {"azureApiVersion": {"desc": "نسخة API الخاصة بـ Azure، والتي تتبع تنسيق YYYY-MM-DD، راجع [الإصدارات الأحدث](https://learn.microsoft.com/zh-en/azure/ai-services/openai/reference#chat-completions)", "fetch": "جلب القائمة", "title": "Azure API Version"}, "empty": "الرجاء إدخال معرف النموذج لإضافة أول نموذج", "endpoint": {"desc": "يمكن العثور على هذه القيمة في قسم 'المفاتيح والنقاط النهائية' عند فحص الموارد في بوابة Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "عنوان Azure API"}, "modelListPlaceholder": "ير<PERSON>ى تحديد أو إضافة نموذج OpenAI الذي قمت بنشره", "title": "Azure OpenAI", "token": {"desc": "يمكن العثور على هذه القيمة في قسم 'المفاتيح والنقاط النهائية' عند فحص الموارد في بوابة Azure. يمكن استخدام KEY1 أو KEY2", "placeholder": "Azure API Key", "title": "مفتاح API"}}, "azureai": {"azureApiVersion": {"desc": "إصدار واجهة برمجة التطبيقات Azure، يتبع تنسيق YYYY-MM-DD، راجع [الإصدار الأحدث](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "الحصول على القائمة", "title": "إصدار واجهة برمجة التطبيقات Azure"}, "endpoint": {"desc": "ابحث عن نقطة نهاية استدلال نموذج Azure AI من نظرة عامة على مشروع Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "نقطة نهاية Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "ابحث عن مفتاح واجهة برمجة التطبيقات من نظرة عامة على مشروع Azure AI", "placeholder": "مف<PERSON><PERSON><PERSON>zure", "title": "المفتاح"}}, "bedrock": {"accessKeyId": {"desc": "أدخل AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "اختبر ما إذا كان AccessKeyId / SecretAccessKey مدخلاً بشكل صحيح"}, "region": {"desc": "أدخل AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "أدخل AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "إذا كنت تستخدم AWS SSO/STS، يرجى إدخال رمز جلسة AWS الخاص بك", "placeholder": "رمز جلسة AWS", "title": "رمز جلسة AWS (اختياري)"}, "title": "Bedrock", "unlock": {"customRegion": "منطقة خدمة مخصصة", "customSessionToken": "رمز الجلسة المخصص", "description": "أدخل معرف الوصول / مفتاح الوصول السري الخاص بك في AWS لبدء الجلسة. لن يتم تسجيل تكوين المصادقة الخاص بك من قبل التطبيق", "imageGenerationDescription": "أدخل AWS AccessKeyId / SecretAccessKey الخاص بك للبدء في التوليد. التطبيق لن يقوم بتسجيل إعدادات المصادقة الخاصة بك", "title": "استخدام معلومات المصادقة الخاصة بـ Bedrock المخصصة"}}, "cloudflare": {"apiKey": {"desc": "يرجى ملء Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "أدخل رقم حساب <PERSON>flare أو عنوان URL API المخصص", "placeholder": "رقم حسا<PERSON> / عنوان URL API المخصص", "title": "رقم حساب <PERSON> / عنوان URL API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "ير<PERSON>ى إدخال مفتاح API الخاص بك", "title": "مفتاح API"}, "basicTitle": "المعلومات الأساسية", "configTitle": "معلومات التكوين", "confirm": "إنشاء جديد", "createSuccess": "تم الإنشاء بنجاح", "description": {"placeholder": "نبذة عن مزود الخدمة (اختياري)", "title": "نبذة عن مزود الخدمة"}, "id": {"desc": "معرف فريد لمزود الخدمة، لا يمكن تعديله بعد الإنشاء", "format": "يمكن أن يحتوي فقط على أرقام، أحرف صغيرة، شرطات (-) وشرطات سفلية (_) ", "placeholder": "يفضل أن يكون بالكامل بحروف صغيرة، مثل openai، لن يمكن تعديله بعد الإنشاء", "required": "ير<PERSON>ى إدخال معرف المزود", "title": "معر<PERSON> المزود"}, "logo": {"required": "يرجى تحميل شعار المزود بشكل صحيح", "title": "شعار المزود"}, "name": {"placeholder": "ير<PERSON>ى إدخال اسم العرض لمزود الخدمة", "required": "ير<PERSON>ى إدخال اسم المزود", "title": "اسم المزود"}, "proxyUrl": {"required": "ير<PERSON>ى إدخال عنوان الوكيل", "title": "عنوان الوكيل"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "ير<PERSON>ى اختيار نوع SDK", "title": "تنسيق الطلب"}, "title": "إنشاء مزود AI مخصص"}, "github": {"personalAccessToken": {"desc": "أدخل رمز الوصول الشخصي الخاص بك على Github، انقر [هنا](https://github.com/settings/tokens) لإنشاء واحد", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "أدخل رمز Hu<PERSON>ace الخاص بك، انقر [هنا](https://huggingface.co/settings/tokens) لإنشاء واحد", "placeholder": "hf_xxxxxxxxx", "title": "<PERSON><PERSON><PERSON>"}}, "list": {"title": {"disabled": "مزود الخدمة غير مفعل", "enabled": "مزود الخدمة مفعل"}}, "menu": {"addCustomProvider": "إضافة مزود خدمة مخصص", "all": "الكل", "list": {"disabled": "غير مفعل", "enabled": "م<PERSON>عل"}, "notFound": "لم يتم العثور على نتائج البحث", "searchProviders": "البحث عن مزودين...", "sort": "ترتيب مخصص"}, "ollama": {"checker": {"desc": "اختبر ما إذا تم إدخال عنوان الوكيل بشكل صحيح", "title": "فحص الاتصال"}, "customModelName": {"desc": "أضف نماذج مخصصة، استخدم الفاصلة (،) لفصل عدة نماذج", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "أسماء النماذج المخصصة"}, "download": {"desc": "أولاما يقوم بتنزيل هذا النموذج، يرجى عدم إغلاق هذه الصفحة إذا أمكن. سيتم استئناف التنزيل من النقطة التي تم قطعها عند إعادة التحميل", "failed": "فشل تحميل النموذج، يرجى التحقق من الشبكة أو إعدادات Ollama ثم إعادة المحاولة", "remainingTime": "الوقت المتبقي", "speed": "سرعة التنزيل", "title": "جارٍ تنزيل النموذج {{model}} "}, "endpoint": {"desc": "يجب أن تحتوي على http(s)://، يمكن تركها فارغة إذا لم يتم تحديدها محليًا", "title": "عنوان وكيل الواجهة"}, "title": "Ollama", "unlock": {"cancel": "Cancel Download", "confirm": "Download", "description": "Enter your Ollama model tag to continue the session", "downloaded": "{{completed}} / {{total}}", "starting": "Starting download...", "title": "Download specified Ollama model"}}, "providerModels": {"config": {"aesGcm": "سيتم استخدام خوارزمية التشفير <1>AES-GCM</1> لتشفير مفتاحك وعنوان الوكيل وما إلى ذلك", "apiKey": {"desc": "ير<PERSON>ى إدخال مفتاح API الخاص بك {{name}}", "descWithUrl": "يرجى إدخال مفتاح API الخاص بـ {{name}}، <3>انقر هنا للحصول عليه</3>", "placeholder": "{{name}} مفتاح API", "title": "مفتاح API"}, "baseURL": {"desc": "يجب أن يحتوي على http(s)://", "invalid": "ير<PERSON>ى إدخال عنوان URL صالح", "placeholder": "https://your-proxy-url.com/v1", "title": "عنوان وكيل API"}, "checker": {"button": "تحقق", "desc": "اختبار ما إذا كان مفتاح API وعنوان الوكيل قد تم إدخالهما بشكل صحيح", "pass": "تم التحقق بنجاح", "title": "اختبار الاتصال"}, "fetchOnClient": {"desc": "سيتم بدء طلب الجلسة مباشرة من المتصفح، مما قد يحسن سرعة الاستجابة", "title": "استخدام وضع الطلب من العميل"}, "helpDoc": "دليل التكوين", "responsesApi": {"desc": "استخدام معيار طلبات الجيل الجديد من OpenAI، لفتح ميزات متقدمة مثل سلسلة التفكير", "title": "استخدام معيار Responses API"}, "waitingForMore": "المزيد من النماذج قيد <1>التخطيط للإدماج</1>، يرجى الانتظار"}, "createNew": {"title": "إنشاء نموذج AI مخصص"}, "item": {"config": "تكوين النموذج", "customModelCards": {"addNew": "إنشاء وإضافة نموذج {{id}}", "confirmDelete": "سيتم حذف هذا النموذج المخصص، ولن يمكن استعادته بعد الحذف، يرجى توخي الحذر."}, "delete": {"confirm": "هل تؤكد حذف النموذج {{displayName}}؟", "success": "تم الحذف بنجاح", "title": "حذ<PERSON> النموذج"}, "modelConfig": {"azureDeployName": {"extra": "الحقل المطلوب في Azure OpenAI", "placeholder": "ير<PERSON>ى إدخال اسم نشر النموذج في Azure", "title": "اسم نشر النموذج"}, "deployName": {"extra": "سيتم استخدام هذا الحقل كمعرف نموذج عند إرسال الطلب", "placeholder": "ير<PERSON>ى إدخال اسم أو معرف النشر الفعلي للنموذج", "title": "اسم نشر النموذج"}, "displayName": {"placeholder": "ير<PERSON>ى إدخال اسم العرض للنموذج، مثل ChatGPT، GPT-4، إلخ", "title": "اسم عرض النموذج"}, "files": {"extra": "تنفيذ تحميل الملفات الحالي هو مجرد حل Hack، يقتصر على التجربة الذاتية. يرجى الانتظار حتى يتم تنفيذ القدرة الكاملة لتحميل الملفات لاحقًا", "title": "دعم تحميل الملفات"}, "functionCall": {"extra": "هذا الإعداد سيفتح فقط قدرة النموذج على استخدام الأدوات، مما يسمح بإضافة مكونات إضافية من نوع الأدوات للنموذج. لكن ما إذا كان يمكن استخدام الأدوات فعليًا يعتمد تمامًا على النموذج نفسه، يرجى اختبار مدى قابليته للاستخدام", "title": "دعم استخدام الأدوات"}, "id": {"extra": "لا يمكن تعديله بعد الإنشاء، سيتم استخدامه كمعرف نموذج عند استدعاء الذكاء الاصطناعي", "placeholder": "ير<PERSON>ى إدخال معرف النموذج، مثل gpt-4o أو claude-3.5-sonnet", "title": "معرف النموذج"}, "modalTitle": "تكوين النموذج المخصص", "reasoning": {"extra": "هذا الإعداد سيفتح فقط قدرة النموذج على التفكير العميق، التأثير الفعلي يعتمد بالكامل على النموذج نفسه، يرجى اختبار ما إذا كان هذا النموذج يمتلك القدرة على التفكير العميق القابل للاستخدام", "title": "يدعم التفكير العميق"}, "tokens": {"extra": "تعيين الحد الأقصى لعدد الرموز المدعومة من قبل النموذج", "title": "أقصى نافذة سياق", "unlimited": "<PERSON>ير محدود"}, "vision": {"extra": "سيؤدي هذا التكوين إلى فتح إعدادات تحميل الصور في التطبيق، ما إذا كان يدعم التعرف يعتمد بالكامل على النموذج نفسه، يرجى اختبار قابلية استخدام التعرف البصري لهذا النموذج بنفسك", "title": "دعم التعرف البصري"}}, "pricing": {"image": "${{amount}}/صورة", "inputCharts": "${{amount}}/M حرف", "inputMinutes": "${{amount}}/دقيقة", "inputTokens": "إدخال ${{amount}}/م", "outputTokens": "إخراج ${{amount}}/م"}, "releasedAt": "صدر في {{releasedAt}}"}, "list": {"addNew": "إضافة نموذج", "disabled": "غير مفعل", "disabledActions": {"showMore": "عر<PERSON> الكل"}, "empty": {"desc": "يرجى إنشاء نموذج مخصص أو سحب نموذج للبدء في الاستخدام", "title": "لا توجد نماذج متاحة"}, "enabled": "م<PERSON>عل", "enabledActions": {"disableAll": "تعطيل الكل", "enableAll": "تفعيل الكل", "sort": "ترتيب النموذج حسب التخصيص"}, "enabledEmpty": "لا توجد نماذج مفعلة، يرجى تفعيل النماذج المفضلة لديك من القائمة أدناه~", "fetcher": {"clear": "مسح النماذج المستخرجة", "fetch": "الحصول على قائمة النماذج", "fetching": "جارٍ الحصول على قائمة النماذج...", "latestTime": "آخر تحديث: {{time}}", "noLatestTime": "لم يتم الحصول على القائمة بعد"}, "resetAll": {"conform": "هل أنت متأكد من إعادة تعيين جميع التعديلات على النموذج الحالي؟ بعد إعادة التعيين، ستعود قائمة النماذج الحالية إلى الحالة الافتراضية", "success": "تمت إعادة التعيين بنجاح", "title": "إعادة تعيين جميع التعديلات"}, "search": "ابحث عن نموذج...", "searchResult": "تم العثور على {{count}} نموذج", "title": "قائمة النماذج", "total": "إجمالي {{count}} نموذج متاح"}, "searchNotFound": "لم يتم العثور على نتائج البحث"}, "sortModal": {"success": "تم تحديث الترتيب بنجاح", "title": "ترتيب مخصص", "update": "تحديث"}, "updateAiProvider": {"confirmDelete": "سيتم حذف مزود AI هذا، ولن يمكن استعادته بعد الحذف، هل تؤكد الحذف؟", "deleteSuccess": "تم الحذف بنجاح", "tooltip": "تحديث التكوين الأساسي للمزود", "updateSuccess": "تم التحديث بنجاح"}, "updateCustomAiProvider": {"title": "تحديث إعدادات مزود الذكاء الاصطناعي المخصص"}, "vertexai": {"apiKey": {"desc": "أدخل مفاتيح Vertex AI الخاصة بك", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "مفاتيح Vertex AI"}}, "zeroone": {"title": "01.AI الأشياء الصغرى"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}