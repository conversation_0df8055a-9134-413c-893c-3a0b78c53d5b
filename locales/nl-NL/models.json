{"01-ai/yi-1.5-34b-chat": {"description": "Zero One Everything, het nieuwste open-source fine-tuning model, met 34 miljard parameters, dat fine-tuning ondersteunt voor verschillende dialoogscenario's, met hoogwaardige trainingsdata die zijn afgestemd op menselijke voorkeuren."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One Everything, het nieuwste open-source fine-tuning model, met 9 miljard parameters, dat fine-tuning ondersteunt voor verschillende dialoogscenario's, met hoogwaardige trainingsdata die zijn afgestemd op menselijke voorkeuren."}, "360/deepseek-r1": {"description": "【360 Deployment Version】DeepSeek-R1 maakt op grote schaal gebruik van versterkend leren in de post-training fase, waardoor de modelinferencecapaciteit aanzienlijk is verbeterd met slechts een paar gelabelde gegevens. Het presteert op het gebied van wiskunde, code en natuurlijke taalredenering op een niveau dat vergelijkbaar is met de officiële versie van OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro, als een belangrijk lid van de 360 AI-modelreeks, voldoet aan de diverse natuurlijke taaltoepassingsscenario's met efficiënte tekstverwerkingscapaciteiten en ondersteunt lange tekstbegrip en meerdaagse gesprekken."}, "360gpt-pro-trans": {"description": "Een model speciaal voor vertalingen, geoptimaliseerd door diepgaande afstemming, met toonaangevende vertaalresultaten."}, "360gpt-turbo": {"description": "360GPT Turbo biedt krachtige reken- en gesprekscapaciteiten, met uitstekende semantische begrip en generatie-efficiëntie, en is de ideale intelligente assistentoplossing voor bedrijven en ontwikkelaars."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K legt de nadruk op semantische veiligheid en verantwoordelijkheid, speciaal ontworpen voor toepassingen met hoge eisen aan inhoudsveiligheid, en zorgt voor nauwkeurigheid en robuustheid in de gebruikerservaring."}, "360gpt2-o1": {"description": "360gpt2-o1 bouwt denkketens op met behul<PERSON> van boomzoekmethoden en introduceert een reflectiemechanisme, getraind met versterkend leren, waardoor het model in staat is tot zelfreflectie en foutcorrectie."}, "360gpt2-pro": {"description": "360GPT2 Pro is een geavanceerd natuurlijk taalverwerkingsmodel dat is ontwikkeld door 360, met uitstekende tekstgeneratie- en begripcapaciteiten, vooral in de generatieve en creatieve domeinen, en kan complexe taaltransformaties en rolinterpretatietaken aan."}, "360zhinao2-o1": {"description": "360zhinao2-o1 bouwt een denkketen op met be<PERSON><PERSON> van boomzoekmethoden en introduceert een reflectiemechanisme, waarbij het gebruik maakt van versterkend leren om het model in staat te stellen tot zelfreflectie en foutcorrectie."}, "4.0Ultra": {"description": "Spark4.0 Ultra is de krachtigste versie in de Spark-grootmodelserie, die de netwerkintegratie heeft geüpgraded en de tekstbegrip- en samenvattingscapaciteiten heeft verbeterd. Het is een allesomvattende oplossing voor het verbeteren van de kantoorproductiviteit en het nauwkeurig reageren op behoeften, en is een toonaangevend intelligent product in de industrie."}, "AnimeSharp": {"description": "AnimeSharp (ook bekend als “4x‑AnimeSharp”) is een open-source superresolutiemodel ontwikkeld door Kim2091, gebaseerd op de ESRGAN-architectuur, gericht op het vergroten en verscherpen van afbeeldingen in anime-stijl. Het werd in februari 2022 hernoemd van “4x-TextSharpV1” en was oorspronkelijk ook geschikt voor tekstafbeeldingen, maar de prestaties zijn sterk geoptimaliseerd voor anime-inhoud."}, "Baichuan2-Turbo": {"description": "Maakt gebruik van zoekversterkingstechnologie om een uitgebreide koppeling tussen het grote model en domeinspecifieke kennis en wereldwijde kennis te realiseren. Ondersteunt het uploaden van verschillende documenten zoals PDF en Word, evenals URL-invoer, met tijdige en uitgebreide informatieverzameling en nauwkeurige, professionele output."}, "Baichuan3-Turbo": {"description": "Geoptimaliseerd voor veelvoorkomende zakelijke scenario's, met aanzienlijke verbeteringen en een hoge prijs-kwaliteitverhouding. In vergelijking met het Baichuan2-model is de inhoudsgeneratie met 20% verbeterd, de kennisvraag met 17% en de rolspelcapaciteit met 40%. De algehele prestaties zijn beter dan die van GPT-3.5."}, "Baichuan3-Turbo-128k": {"description": "Met een 128K ultra-lange contextvenster, geoptimaliseerd voor veelvoorkomende zakelijke scenario's, met aanzienlijke verbeteringen en een hoge prijs-kwaliteitverhouding. In vergelijking met het Baichuan2-model is de inhoudsgeneratie met 20% verbeterd, de kennisvraag met 17% en de rolspelcapaciteit met 40%. De algehele prestaties zijn beter dan die van GPT-3.5."}, "Baichuan4": {"description": "Het model heeft de beste prestaties in het binnenland en overtreft buitenlandse mainstream modellen in kennisencyclopedieën, lange teksten en creatieve generaties. Het heeft ook toonaangevende multimodale capaciteiten en presteert uitstekend in verschillende autoritatieve evaluatiebenchmarks."}, "Baichuan4-Air": {"description": "Modelcapaciteiten zijn nationaal de beste, overtreft buitenlandse mainstream modellen in kennisencyclopedie, lange teksten en creatieve generatie in Chinese taken. Besch<PERSON>t ook over toonaangevende multimodale capaciteiten en presteert uitstekend op verschillende autoritatieve evaluatiebenchmarks."}, "Baichuan4-Turbo": {"description": "Modelcapaciteiten zijn nationaal de beste, overtreft buitenlandse mainstream modellen in kennisencyclopedie, lange teksten en creatieve generatie in Chinese taken. Besch<PERSON>t ook over toonaangevende multimodale capaciteiten en presteert uitstekend op verschillende autoritatieve evaluatiebenchmarks."}, "DeepSeek-R1": {"description": "<PERSON>en geavanceerd en efficiënt LLM, gespecialiseerd in redeneren, wiskunde en programmeren."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 - een groter en slimmer model binnen de DeepSeek-suite - is gedistilleerd naar de Llama 70B-architectuur. Op basis van benchmarktests en menselijke evaluaties is dit model slimmer dan het oorspronkelijke Llama 70B, vooral in taken die wiskunde en feitelijke nauwkeurigheid vereisen."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 distillatiemodel gebaseerd op Qwen2.5-Math-1.5B, geoptimaliseerd voor inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 distillatiemodel gebaseerd op Qwen2.5-14B, geoptimaliseerd voor inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "De DeepSeek-R1 serie optimaliseert inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt en de OpenAI-o1-mini niveaus overtreft."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1 distillatiemodel gebaseerd op Qwen2.5-Math-7B, geoptimaliseerd voor inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt."}, "DeepSeek-V3": {"description": "DeepSeek-V3 is een <PERSON><PERSON>-model dat zelf is ontwikkeld door DeepSeek Company. De prestaties van DeepSeek-V3 overtreffen die van andere open-source modellen zoals Qwen2.5-72B en Llama-3.1-405B, en presteert op het gebied van prestaties gelijkwaardig aan de wereldtop gesloten modellen zoals GPT-4o en Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite biedt een ultieme responssnelheid en een betere prijs-kwaliteitverhouding, waardoor het flexibele keuzes biedt voor verschillende klantenscenario's. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON>."}, "Doubao-lite-32k": {"description": "Doubao-lite biedt een ultieme responssnelheid en een betere prijs-kwaliteitverhouding, waardoor het flexibele keuzes biedt voor verschillende klantenscenario's. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite biedt een ultieme responssnelheid en een betere prijs-kwaliteitverhouding, waardoor het flexibele keuzes biedt voor verschillende klantenscenario's. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van 4k."}, "Doubao-pro-128k": {"description": "Het beste hoofdmodel, geschikt voor het verwerken van complexe taken, met uitstekende prestaties in scenario's zoals referentievragen, <PERSON><PERSON><PERSON><PERSON><PERSON>, crea<PERSON><PERSON>, tekstclassificatie en rollenspellen. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van <PERSON>."}, "Doubao-pro-32k": {"description": "Het beste hoofdmodel, geschikt voor het verwerken van complexe taken, met uitstekende prestaties in scenario's zoals referentievragen, <PERSON><PERSON><PERSON><PERSON><PERSON>, crea<PERSON><PERSON>, tekstclassificatie en rollenspellen. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van 32k."}, "Doubao-pro-4k": {"description": "Het beste hoofdmodel, geschikt voor het verwerken van complexe taken, met uitstekende prestaties in scenario's zoals referentievragen, <PERSON><PERSON><PERSON><PERSON><PERSON>, crea<PERSON><PERSON>, tekstclassificatie en rollenspellen. Ondersteunt redeneren en fijn afstemmen met een context<PERSON><PERSON> van 4k."}, "DreamO": {"description": "DreamO is een open-source beeldgeneratiemodel ontwikkeld in samenwerking tussen ByteDance en de Universiteit van Peking, ontworpen om multi-task beeldgeneratie te ondersteunen via een uniforme architectuur. Het maakt gebruik van een efficiënte combinatiemodelmethode om op basis van door de gebruiker gespecificeerde identiteit, onderwerp, stijl, achtergrond en andere voorwaarden zeer consistente en aangepaste beelden te genereren."}, "ERNIE-3.5-128K": {"description": "De door Baidu ontwikkelde vlaggenschip grote taalmodel, dat een enorme hoeveelheid Chinese en Engelse gegeven<PERSON> dekt, met krachtige algemene capaciteiten die voldoen aan de meeste eisen voor dialoogvragen, creatieve generatie en plug-in toepassingsscenario's; ondersteunt automatische integratie met de <PERSON><PERSON> zoekplug-in, wat de actualiteit van vraag- en antwoordinformatie waarborgt."}, "ERNIE-3.5-8K": {"description": "De door Baidu ontwikkelde vlaggenschip grote taalmodel, dat een enorme hoeveelheid Chinese en Engelse gegeven<PERSON> dekt, met krachtige algemene capaciteiten die voldoen aan de meeste eisen voor dialoogvragen, creatieve generatie en plug-in toepassingsscenario's; ondersteunt automatische integratie met de <PERSON><PERSON> zoekplug-in, wat de actualiteit van vraag- en antwoordinformatie waarborgt."}, "ERNIE-3.5-8K-Preview": {"description": "De door Baidu ontwikkelde vlaggenschip grote taalmodel, dat een enorme hoeveelheid Chinese en Engelse gegeven<PERSON> dekt, met krachtige algemene capaciteiten die voldoen aan de meeste eisen voor dialoogvragen, creatieve generatie en plug-in toepassingsscenario's; ondersteunt automatische integratie met de <PERSON><PERSON> zoekplug-in, wat de actualiteit van vraag- en antwoordinformatie waarborgt."}, "ERNIE-4.0-8K-Latest": {"description": "Het door Baidu ontwikkelde vlaggenschip van een ultra-groot taalmodel, dat in vergelijking met ERNIE 3.5 een algehele upgrade van de modelcapaciteiten heeft gerealiseerd, en breed toepasbaar is in complexe taken in verschillende domeinen; ondersteunt automatische integratie met de Baidu-zoekplug-in om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ERNIE-4.0-8K-Preview": {"description": "Het door Baidu ontwikkelde vlaggenschip van een ultra-groot taalmodel, dat in vergelijking met ERNIE 3.5 een algehele upgrade van de modelcapaciteiten heeft gerealiseerd, en breed toepasbaar is in complexe taken in verschillende domeinen; ondersteunt automatische integratie met de Baidu-zoekplug-in om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "De zelfontwikkelde vlaggenschip super-grote taalmodel van Baidu, dat uitmuntend presteert in diverse complexe taakscenario's in verschillende domeinen; ondersteunt automatische integratie met de Baidu-zoekplug-in, waarborgt de actualiteit van vraag-antwoordinformatie. Overtreft in performance ten opzichte van ERNIE 4.0."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Het door Baidu ontwikkelde vlaggenschip van een ultra-groot taalmodel, dat uitstekende algehele prestaties levert en breed toepasbaar is in complexe taken in verschillende domeinen; ondersteunt automatische integratie met de Baidu-zoekplug-in om de actualiteit van vraag- en antwoordinformatie te waarborgen. In vergelijking met ERNIE 4.0 presteert het beter."}, "ERNIE-Character-8K": {"description": "Het door Baidu ontwikkelde verticale taalmodel, geschikt voor toepassingen zoals game NPC's, klantenservice gesprekken en rollenspellen, met een du<PERSON> en consistenter karakterontwerp, sterkere instructievolgcapaciteiten en betere inferentieprestaties."}, "ERNIE-Lite-Pro-128K": {"description": "Het door Baidu ontwikkelde lichte taalmodel, dat zowel uitstekende modelprestaties als inferentieprestaties biedt, met betere resultaten dan ERNIE Lite, en geschikt is voor inferentie op AI-versnelling kaarten met lage rekencapaciteit."}, "ERNIE-Speed-128K": {"description": "Het door Baidu in 2024 gepresenteerde nieuwe hoge-prestatie taalmodel, met uitstekende algemene capaciteiten, geschikt als basis model voor fine-tuning, om beter specifieke probleemstellingen aan te pakken, met uitstekende inferentieprestaties."}, "ERNIE-Speed-Pro-128K": {"description": "Het door Baidu in 2024 gepresenteerde nieuwe hoge-prestatie taalmodel, met uitstekende algemene capaciteiten, betere resultaten dan ERNI<PERSON>, en geschikt als basis model voor fine-tuning, om beter specifieke probleemstellingen aan te pakken, met uitstekende inferentieprestaties."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev is een multimodaal beeldgeneratie- en bewerkingsmodel ontwikkeld door Black Forest Labs, gebaseerd op de Rectified Flow Transformer-architectuur met 12 miljard parameters. Het richt zich op het genereren, reconstrueren, verbeteren of bewerken van beelden onder gegeven contextuele voorwaarden. Dit model combineert de controleerbare generatievoordelen van diffusie-modellen met de contextuele modellering van Transformers en ondersteunt hoogwaardige beeldoutput, breed toepasbaar voor beeldherstel, beeldaanvulling en visuele scèneherconstructie."}, "FLUX.1-dev": {"description": "FLUX.1-dev is een open-source multimodaal taalmodel (Multimodal Language Model, MLLM) ontwikkeld door Black Forest Labs, geoptimaliseerd voor taken met tekst en beeld. Het integreert begrip en generatie van zowel afbeeldingen als tekst. Gebaseerd op geavanceerde grote taalmodellen zoals Mistral-7B, bereikt het door zorgvuldig ontworpen visuele encoders en meervoudige instructiefijnafstelling een vermogen tot gecombineerde tekst-beeldverwerking en complexe taakredenering."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) is een innovatief model, geschikt voor toepassingen in meerdere domeinen en complexe taken."}, "HelloMeme": {"description": "HelloMeme is een AI-tool die automatisch memes, GIF's of korte video's genereert op basis van door jou aangeleverde afbeeldingen of acties. Je hebt geen teken- of programmeerkennis nodig; met alleen referentieafbeeldingen helpt het je om aantrekkeli<PERSON>e, leuke en stijlconsistente content te maken."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full is een open-source multimodaal beeldbewerkingsmodel uitgebracht door HiDream.ai, gebaseerd op de geavanceerde Diffusion Transformer-architectuur en gecombineerd met krachtige taalbegripsmogelijkheden (ingebouwde LLaMA 3.1-8B-Instruct). Het ondersteunt beeldgeneratie, sti<PERSON>ltransfer, lokale bewerking en inhoudshertekening via natuurlijke taalopdrachten en beschikt over uitstekende tekst-beeldbegrip en uitvoeringscapaciteiten."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled is een lichtge<PERSON>t tekst-naar-beeldmodel dat door distillatie is geoptimaliseerd om snel hoogwaardige beelden te genereren, bijzonder geschikt voor omgevingen met beperkte middelen en realtime generatie."}, "InstantCharacter": {"description": "InstantCharacter is een in 2025 door het Tencent AI-team uitgebracht tuning-vrij g<PERSON><PERSON><PERSON><PERSON>d karaktergeneratiemodel, gericht op het realiseren van hoge-fideliteit en consistente karaktergeneratie over verschillende scènes. Het model ondersteunt karaktermodellering op basis van slechts één referentieafbeelding en kan dit karakter flexibel overbrengen naar diverse stijlen, houdingen en achtergronden."}, "InternVL2-8B": {"description": "InternVL2-8B is een krachtig visueel taalmodel dat multimodale verwerking van afbeeldingen en tekst ondersteunt, in staat om afbeeldingsinhoud nauwkeurig te identificeren en relevante beschrijvingen of antwoorden te genereren."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B is een krachtig visueel taalmodel dat multimodale verwerking van afbeeldingen en tekst ondersteunt, in staat om afbeeldingsinhoud nauwkeurig te identificeren en relevante beschrijvingen of antwoorden te genereren."}, "Kolors": {"description": "<PERSON><PERSON><PERSON> is een tekst-naar-beeldmodel ontwikkeld door het Kolors-team van Kuaishou. Het is getraind met miljarden parameters en heeft significante voordelen in visuele kwaliteit, <PERSON><PERSON> semantisch begrip en tekstrendering."}, "Kwai-Kolors/Kolors": {"description": "Kolors is een groots<PERSON>ig tekst-naar-beeldgeneratiemodel gebaseerd op latente diffusie, ontwikkeld door het Kolors-team van Kuaishou. Het model is getraind op miljarden tekst-beeldparen en toont uitstekende prestaties in visuele kwaliteit, complexe semantische nauwkeurigheid en het renderen van Chinese en Engelse karakters. Het ondersteunt zowel Chinese als Engelse invoer en blinkt uit in het begrijpen en genereren van specifieke Chinese inhoud."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Uitstekende beeldredeneringscapaciteiten op hoge resolutie afbeeldingen, geschikt voor visuele begripstoepassingen."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Gea<PERSON>erde beeldredeneringscapaciteiten voor visuele begripstoepassingen."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 instructie-geoptimaliseerd tekstmodel, geoptimaliseerd voor meertalige gesprekstoepassingen, presteert uitstekend op veel beschikbare open-source en gesloten chatmodellen op veelvoorkomende industriële benchmarks."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 instructie-geoptimaliseerd tekstmodel, geoptimaliseerd voor meertalige gesprekstoepassingen, presteert uitstekend op veel beschikbare open-source en gesloten chatmodellen op veelvoorkomende industriële benchmarks."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 instructie-geoptimaliseerd tekstmodel, geoptimaliseerd voor meertalige gesprekstoepassingen, presteert uitstekend op veel beschikbare open-source en gesloten chatmodellen op veelvoorkomende industriële benchmarks."}, "Meta-Llama-3.2-1B-Instruct": {"description": "<PERSON><PERSON>, state-of-the-art klein taal<PERSON><PERSON><PERSON> met ta<PERSON><PERSON><PERSON><PERSON>, uitstekende redeneervaardigheden en tekstgeneratiecapaciteiten."}, "Meta-Llama-3.2-3B-Instruct": {"description": "<PERSON><PERSON>, state-of-the-art klein taal<PERSON><PERSON><PERSON> met ta<PERSON><PERSON><PERSON><PERSON>, uitstekende redeneervaardigheden en tekstgeneratiecapaciteiten."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 is het meest geavanceerde meertalige open-source grote taalmiddel in de Llama-serie, dat prestaties biedt die vergelijkbaar zijn met die van een 405B-model tegen zeer lage kosten. Gebaseerd op de Transformer-structuur en verbeterd door middel van supervisie-fijnstelling (SFT) en versterkend leren met menselijke feedback (RLHF) voor nuttigheid en veiligheid. De instructie-geoptimaliseerde versie is speciaal geoptimaliseerd voor meertalige gesprekken en presteert beter dan veel open-source en gesloten chatmodellen op verschillende industriële benchmarks. Kennisafkapdatum is december 2023."}, "MiniMax-M1": {"description": "<PERSON><PERSON> glo<PERSON>uw zelfontwikkeld redeneermodel. Wereldwijd toonaangevend: 80K denkpatronen x 1M invoer, prestaties verge<PERSON><PERSON><PERSON>ar met topmodellen uit het buitenland."}, "MiniMax-Text-01": {"description": "In de MiniMax-01-serie modellen hebben we gedurfde innovaties doorgevoerd: voor het eerst op grote schaal een lineaire aandachtmechanisme geïmplementeerd, waardoor de traditionele Transformer-architectuur niet langer de enige keuze is. Dit model heeft een parameterhoe<PERSON><PERSON>he<PERSON> van maar liefst 456 miljard, met een enkele activatie van 45,9 miljard. De algehele prestaties van het model zijn verge<PERSON> met die van de beste modellen in het buitenland, terwijl het efficiënt de wereldwijd langste context van 4 miljoen tokens kan verwerken, wat 32 keer de capaciteit van GPT-4o en 20 keer die van Claude-3.5-Sonnet is."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 is een open-source gewichtenschaalmodel met gemengde aandacht, met 456 miljard parameters, waarbij elke token ongeveer 45,9 miljard parameters activeert. Het model ondersteunt native een ultralange context van 1 miljoen tokens en bespaart dankzij het bliksemaandachtmechanisme 75% van de floating-point-bewerkingen bij generatietaken van 100.000 tokens vergeleken met DeepSeek R1. Tegelijkertijd maakt MiniMax-M1 gebruik van een MoE (Mixture of Experts) architectuur, gecombineerd met het CISPO-algoritme en een efficiënt versterkend leermodel met gemengde aandacht, wat leidt tot toonaangevende prestaties bij lange invoerredenering en echte software-engineering scenario's."}, "Moonshot-Kimi-K2-Instruct": {"description": "Met in totaal 1 biljoen parameters en 32 miljard geactiveerde parameters is dit het toonaangevende niet-denkende model op het gebied van geavanceerde kennis, wiskunde en codering, en is het beter geschikt voor algemene agenttaken. Het is zorgvuldig geoptimaliseerd voor agenttaken, kan niet alleen vragen beantwoorden maar ook acties ondernemen. Ideaal voor improvisatie, algemene chat en agentervaringen, het is een reflexniveau model zonder lange denktijd."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) is een hoogprecisie instructiemodel, geschikt voor complexe berekeningen."}, "OmniConsistency": {"description": "OmniConsistency verbetert de stijlconsistentie en generalisatie in image-to-image taken door grootschalige Diffusion Transformers (DiTs) en gepaarde gestileerde data te introduceren, waardoor stijldegradatie wordt voorkomen."}, "Phi-3-medium-128k-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON> Phi-3-medium model, maar met een g<PERSON><PERSON><PERSON><PERSON> voor RAG of few shot prompting."}, "Phi-3-medium-4k-instruct": {"description": "Een model met 14 miljard parameters, biedt betere kwaliteit dan Phi-3-mini, met een focus op hoogwaardige, redeneringsdichte gegevens."}, "Phi-3-mini-128k-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON> Phi-3-mini model, maar met een g<PERSON><PERSON><PERSON><PERSON> voor RAG of few shot prompting."}, "Phi-3-mini-4k-instruct": {"description": "De kleinste lid van de Phi-3 familie. Geoptimaliseerd voor zowel kwaliteit als lage latentie."}, "Phi-3-small-128k-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON> Phi-3-small model, maar met een g<PERSON><PERSON><PERSON><PERSON> voor RAG of few shot prompting."}, "Phi-3-small-8k-instruct": {"description": "Een model met 7 miljard parameters, biedt betere kwaliteit dan Phi-3-mini, met een focus op hoogwaardige, redeneringsdichte gegevens."}, "Phi-3.5-mini-instruct": {"description": "<PERSON><PERSON> ge<PERSON>p<PERSON>te versie van het Phi-3-mini model."}, "Phi-3.5-vision-instrust": {"description": "<PERSON><PERSON> ge<PERSON><PERSON><PERSON><PERSON> versie van het Phi-3-vision model."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct is een instructie-fijn afgesteld groot taalmodel in de Qwen2-serie, met een parameter grootte van 7B. Dit model is geb<PERSON><PERSON> op de Transformer-architectuur en maakt gebruik van technieken zoals de SwiGLU-activeringsfunctie, aandacht QKV-bias en groepsquery-aandacht. Het kan grote invoer verwerken. Dit model presteert uitstekend in taalbegrip, generatie, meertalige capaciteiten, codering, wiskunde en redenering in verschillende benchmarktests, en overtreft de meeste open-source modellen, en toont in sommige taken een concurrentievermogen vergelijkbaar met dat van propriëtaire modellen. Qwen2-7B-Instruct presteert beter dan Qwen1.5-7B-Chat in verschillende evaluaties, wat aanzienlijke prestatieverbeteringen aantoont."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct is een van de nieuwste grote taalmodellen die door Alibaba Cloud is uitgebracht. Dit 7B-model heeft aanzienlijke verbeteringen in coderings- en wiskundige vaardigheden. Het model biedt ook meertalige ondersteuning, met meer dan 29 ondersteunde talen, waaro<PERSON> en Engels. Het model heeft aanzienlijke verbeteringen in het volgen van instructies, het begrijpen van gestructureerde gegevens en het genereren van gestructureerde uitvoer (vooral JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct is de nieuwste versie van de code-specifieke grote taalmodelreeks die door Alibaba Cloud is uitgebracht. Dit model is aanzienlijk verbeterd in codegeneratie, redenering en herstelcapaciteiten door training met 55 biljoen tokens, gebaseerd op Qwen2.5. Het versterkt niet alleen de coderingscapaciteiten, maar behoudt ook de voordelen van wiskundige en algemene vaardigheden. Het model biedt een meer uitgebreide basis voor praktische toepassingen zoals code-agenten."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL is een nieuw <PERSON>serie, met krachtige visuele inzichtscapaciteiten. Het kan tekst, grafieken en lay-outs in afbeeldingen analyseren en langere video's begrijpen en gebeurtenissen vastleggen. Het kan redeneren en tools bedienen, ondersteunt multi-formaat objectlocalisatie en structuuroutput genereren. De video-begripstraining is geoptimaliseerd voor dynamische resolutie en framesnelheid, en de efficiëntie van de visuele encoder is verbeterd."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking is een open source visueel-taalmodel (VLM) dat gezamenlijk is uitgebracht door Zhipu AI en het KEG-laboratorium van de Tsinghua Universiteit. Het is speciaal ontworpen voor het verwerken van complexe multimodale cognitieve taken. Dit model is gebaseerd op het GLM-4-9B-0414 basismodel en verbetert aanzienlijk de crossmodale redeneercapaciteiten en stabiliteit door de introductie van een 'Chain-of-Thought' redeneermethode en het gebruik van versterkend leren."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat is de open-source versie van het GLM-4-serie voorgetrainde model, gelanceerd door Zhipu AI. Dit model presteert uitstekend in semantiek, wiskunde, redenering, code en kennis. Naast ondersteuning voor meerdaagse gesprekken, beschikt GLM-4-9B-<PERSON><PERSON> ook over geavanceerde functies zoals webbrowser, code-uitvoering, aangepaste tool-aanroepen (Function Call) en lange tekstredenering. Het model ondersteunt 26 talen, waaronder <PERSON>, Engels, Japans, Koreaans en Duits. In verschillende benchmarktests toont GLM-4-9B-Chat uitstekende prestaties, zoals AlignBench-v2, MT-Bench, MMLU en C-Eval. Dit model ondersteunt een maximale contextlengte van 128K, geschikt voor academisch onderzoek en commerciële toepassingen."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 is een inferentiemodel aangedreven door versterkend leren (RL), dat de problemen van herhaling en leesbaarheid in modellen aanpakt. Voor RL introduceert DeepSeek-R1 koude startdata, wat de inferentieprestaties verder optimaliseert. Het presteert vergelijkbaar met OpenAI-o1 in wiskunde, code en inferentietaken, en verbetert de algehele effectiviteit door zorgvuldig ontworpen trainingsmethoden."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B is een model dat is a<PERSON><PERSON><PERSON><PERSON> van Qwen2.5-Math-7B door middel van kennisdistillatie. Dit model is fijn afgesteld met 800.000 zorgvuldig geselecteerde voorbeelden die zijn gegenereerd door DeepSeek-R1, waardoor het uitstekende inferentiecapaciteiten vertoont. Het presteert goed op verschillende benchmarks, met een nauwkeurigheid van 92,8% op MATH-500, een doorlooptarief van 55,5% op AIME 2024 en een score van 1189 op CodeForces. Als een model van 7B schaal toont het sterke wiskundige en programmeringvaardigheden."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 is een hybride expert (MoE) taalmodel met 6710 miljard parameters, dat gebru<PERSON><PERSON><PERSON><PERSON> van multi-head latent attention (MLA) en de DeepSeekMoE-architectuur, g<PERSON><PERSON><PERSON><PERSON> met een load balancing-strategie zonder extra verlies, om de inferentie- en trainingsefficiëntie te optimaliseren. Door voorgetraind te worden op 14,8 biljoen hoogwaardige tokens en vervolgens te worden fijngesteld met supervisie en versterkend leren, overtreft DeepSeek-V3 andere open-source modellen in prestaties en komt het dicht in de buurt van toonaangevende gesloten modellen."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 is een <PERSON>E-architectuurbasis model met krachtige codeer- en agentcapaciteiten, met in totaal 1 biljoen parameters en 32 miljard geactiveerde parameters. In benchmarktests voor algemene kennisredenering, programmeren, wiskunde en agenttaken overtreft het K2-model andere toonaangevende open-source modellen."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview is een innovatief natuurlijk taalverwerkingsmodel dat efficiënt complexe dialooggeneratie en contextbegripstaken kan verwerken."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview is een onderzoeksmodel ontwikkeld door het Qwen-team, dat zich richt op visuele redeneervaardigheden en unieke voordelen heeft in het begrijpen van complexe scènes en het oplossen van visueel gerelateerde wiskundige problemen."}, "Qwen/QwQ-32B": {"description": "QwQ is het inferentiemodel van de Qwen-serie. In vergelijking met traditionele instructie-geoptimaliseerde modellen beschikt QwQ over denk- en redeneervaardigheden, waardoor het in staat is om aanzienlijk verbeterde prestaties te leveren in downstream-taken, vooral bij het oplossen van moeilijke problemen. QwQ-32B is een middelgroot inferentiemodel dat concurrerende prestaties kan behalen in vergelijking met de meest geavanceerde inferentiemodellen (zoals DeepSeek-R1, o1-mini). Dit model maakt gebruik van technologieën zoals RoPE, SwiGLU, RMSNorm en Attention QKV bias, en heeft een netwerkstructuur van 64 lagen en 40 Q-aandachtshoofden (met KV van 8 in de GQA-architectuur)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview is het nieuwste experimentele onderzoeksmodel van <PERSON>, gericht op het verbeteren van AI-redeneringscapaciteiten. Door het verkennen van complexe mechanismen zoals taalmixing en recursieve redenering, zijn de belangrijkste voordelen onder andere krachtige redeneringsanalyses, wiskundige en programmeervaardigheden. Tegelijkertijd zijn er ook problemen met taalwisseling, redeneringscycli, veiligheidskwesties en verschillen in andere capaciteiten."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 is een geavanceerd algemeen taalmodel dat verschillende soorten instructies ondersteunt."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct is een instructie-fijn afgesteld groot taalmodel in de Qwen2-serie, met een parameter grootte van 72B. Dit model is gebaseerd op de Transformer-architectuur en maakt gebruik van technieken zoals de SwiGLU-activeringsfunctie, aandacht QKV-bias en groepsquery-aandacht. Het kan grote invoer verwerken. Dit model presteert uitstekend in taalbegrip, generatie, meertalige capaciteiten, codering, wiskunde en redenering in verschillende benchmarktests, en overtreft de meeste open-source modellen, en toont in sommige taken een concurrentievermogen vergelijkbaar met dat van propriëtaire modellen."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL is de nieuwste iteratie van het Qwen-VL-model, dat de toonaangevende prestaties behaalde in benchmarktests voor visueel begrip."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 is een geheel nieuwe serie van grote taalmodellen, ontworpen om de verwerking van instructietaken te optimaliseren."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 is een geheel nieuwe serie van grote taalmodellen, ontworpen om de verwerking van instructietaken te optimaliseren."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "<PERSON><PERSON> groot taalmodel ontwikkeld door het Alibaba Cloud Tongyi Qianwen-team"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 is een geheel ni<PERSON>we serie grote taalmodellen, met sterkere begrip- en generatiecapaciteiten."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 is een geheel nieuwe serie grote taalmodellen, ontworpen om de verwerking van instructietaken te optimaliseren."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 is een geheel nieuwe serie van grote taalmodellen, ontworpen om de verwerking van instructietaken te optimaliseren."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 is een geheel nieuwe serie grote taalmodellen, ontworpen om de verwerking van instructietaken te optimaliseren."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder richt zich op het schrijven van code."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct is de nieuwste versie van de code-specifieke grote taalmodelreeks die door Alibaba Cloud is uitgebracht. Dit model is aanzienlijk verbeterd in codegeneratie, redenering en herstelcapaciteiten door training met 55 biljoen tokens, gebaseerd op Qwen2.5. Het versterkt niet alleen de coderingscapaciteiten, maar behoudt ook de voordelen van wiskundige en algemene vaardigheden. Het model biedt een meer uitgebreide basis voor praktische toepassingen zoals code-agenten."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct is een multimodaal groot model dat is uitgebracht door het team van Qwen2.5-VL. Dit model is niet alleen bedreven in het herkennen van algemene objecten, maar kan ook afbeeldingen analyseren voor tekst, gra<PERSON><PERSON>, pictogrammen, diagrammen en lay-outs. Het kan als een visueel intelligentieagent fungeren, in staat tot redeneren en dynamisch het besturen van tools, met de mogelijkheid om computers en smartphones te gebruiken. Bovendien kan dit model objecten in afbeeldingen nauwkeurig lokaliseren en gestructureerde uitvoer voor facturen, tabellen en dergelijke genereren. Ten opzichte van het vorige model Qwen2-VL is deze versie verder verbeterd in wiskunde en probleemoplossend vermogen door versterkend leren, en het antwoordstijl is meer in lijn met menselijke voorkeuren."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL is een visueel-taalmodel uit de Qwen2.5-reeks. Dit model heeft aanzienlijke verbeteringen op verschillende gebieden: het heeft een betere visuele begripscapaciteit, kan veelvoorkomende objecten herkennen, tekst, grafieken en lay-outs analyseren; als visueel agent kan het redeneren en het gebruik van tools dynamisch begeleiden; het ondersteunt het begrijpen van video's langer dan 1 uur en kan belangrijke gebeurtenissen vastleggen; het kan objecten in afbeeldingen nauwkeurig lokaliseren door bounding boxes of punten te genereren; het ondersteunt de generatie van gestructureerde uitvoer, met name geschikt voor facturen, tabellen en andere gescande gegevens."}, "Qwen/Qwen3-14B": {"description": "Qwen3 is een nieuwe generatie Qwen-model met aanzienlijk verbeterde capaciteiten, die op het gebied van redenering, al<PERSON><PERSON><PERSON> gebruik, agent en meertaligheid op een leidende positie in de industrie staat, en ondersteunt de schakel tussen denkmodi."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 is een nieuwe generatie Qwen-model met aanzienlijk verbeterde capaciteiten, die op het gebied van redenering, al<PERSON><PERSON><PERSON> gebruik, agent en meertaligheid op een leidende positie in de industrie staat, en ondersteunt de schakel tussen denkmodi."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 is een vlag<PERSON>chip hybride-expert (MoE) groot taalmodel uit de Qwen3-serie, ontwikkeld door het Alibaba Cloud Tongyi Qianwen-team. Het model heeft 235 miljard totale parameters en activeert 22 miljard parameters per inferentie. Het is een update van de niet-denkende modus van Qwen3-235B-A22B, met significante verbeteringen in instructienaleving, logische redenering, tekstbegrip, wiskunde, wetenschap, programmeren en toolgebruik. Daarnaast is de dekking van meertalige lange staartkennis versterkt en is het beter afgestemd op gebruikersvoorkeuren in subjectieve en open taken voor het genereren van nuttigere en kwalitatief betere teksten."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 is een lid van de Qwen3-serie grote taalmodellen ontwikkeld door Alibaba Tongyi Qianwen, gericht op complexe en moeilijke redeneertaken. Het model is gebaseerd op een hybride-expert (MoE) architectuur met 235 miljard parameters, waarbij per token ongeveer 22 miljard parameters worden geactiveerd, wat zorgt voor hoge prestaties en efficiëntie. Als speciaal 'denk'-model excelleert het in logische redenering, wiskunde, wetenschap, programmeren en academische benchmarks, en bereikt het topniveau onder open-source denkmodellen. Het model versterkt ook algemene capaciteiten zoals instructienaleving, toolgebruik en tekstgeneratie, ondersteunt native 256K lange contexten en is ideaal voor diepgaande redenering en verwerking van lange documenten."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 is een nieuwe generatie Qwen-model met aanzienlijk verbeterde capaciteiten, die op het gebied van redenering, al<PERSON><PERSON><PERSON> gebruik, agent en meertaligheid op een leidende positie in de industrie staat, en ondersteunt de schakel tussen denkmodi."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 is een bijgewerkte versie van Qwen3-30B-A3B zonder denkmodus. <PERSON><PERSON> is een hybride expert (MoE) model met in totaal 30,5 miljard parameters en 3,3 miljard actieve parameters. Het model heeft belangrijke verbeteringen ondergaan op meerdere gebieden, waaronder een aanzienlijke verbetering van het volgen van instructies, logisch redeneren, tekstbegrip, wiskunde, wetenschap, codering en het gebruik van tools. Tegelijkertijd heeft het substantiële vooruitgang geboekt in de dekking van meertalige long-tail kennis en kan het beter afstemmen op de voorkeuren van gebruikers bij subjectieve en open taken, waardoor het nuttigere antwoorden en tekst van hogere kwaliteit kan genereren. Bovendien is het vermogen van het model om lange teksten te begrijpen uitgebreid tot 256K. Dit model ondersteunt alleen de niet-denkmodus en genereert geen `<think></think>` tags in de output."}, "Qwen/Qwen3-32B": {"description": "Qwen3 is een nieuwe generatie Qwen-model met aanzienlijk verbeterde capaciteiten, die op het gebied van redenering, al<PERSON><PERSON><PERSON> gebruik, agent en meertaligheid op een leidende positie in de industrie staat, en ondersteunt de schakel tussen denkmodi."}, "Qwen/Qwen3-8B": {"description": "Qwen3 is een nieuwe generatie Qwen-model met aanzienlijk verbeterde capaciteiten, die op het gebied van redenering, al<PERSON><PERSON><PERSON> gebruik, agent en meertaligheid op een leidende positie in de industrie staat, en ondersteunt de schakel tussen denkmodi."}, "Qwen2-72B-Instruct": {"description": "Qwen2 is de nieuwste serie van het Qwen-model, dat 128k context ondersteunt. In vergelijking met de huidige beste open-source modellen, overtreft Qwen2-72B op het gebied van natuurlijke taalbegrip, kennis, code, wiskunde en meertaligheid aanzienlijk de huidige toonaangevende modellen."}, "Qwen2-7B-Instruct": {"description": "Qwen2 is de nieuwste serie van het Qwen-model, dat in staat is om de beste open-source modellen van gelijke grootte of zelfs grotere modellen te overtreffen. Qwen2 7B heeft aanzienlijke voordelen behaald in verschillende evaluaties, vooral op het gebied van code en begrip van het Chinees."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B is een krachtig visueel taalmodel dat multimodale verwerking van afbeeldingen en tekst ondersteunt, in staat om afbeeldingsinhoud nauwkeurig te herkennen en relevante beschrijvingen of antwoorden te genereren."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct is een groot taalmodel met 14 miljard parameters, met uitstekende prestaties, geoptimaliseerd voor Chinese en meertalige scenario's, en ondersteunt toepassingen zoals intelligente vraag-en-antwoord en contentgeneratie."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct is een groot taalmodel met 32 miljard parameters, met een evenwichtige prestatie, geoptimaliseerd voor Chinese en meertalige scenario's, en ondersteunt toepassingen zoals intelligente vraag-en-antwoord en contentgeneratie."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct ondersteunt 16k context en genereert lange teksten van meer dan 8K. Het ondersteunt functieaanroepen en naadloze interactie met externe systemen, wat de flexibiliteit en schaalbaarheid aanzienlijk vergroot. De kennis van het model is duidelijk toegenomen en de coderings- en wiskundige vaardigheden zijn sterk verbeterd, met ondersteuning voor meer dan 29 talen."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct is een groot taalmodel met 7 miljard parameters, dat function calls ondersteunt en naadloos kan interageren met externe systemen, wat de flexibiliteit en schaalbaarheid aanzienlijk vergroot. Geoptimaliseerd voor Chinese en meertalige scenario's, ondersteunt het toepassingen zoals intelligente vraag-en-antwoord en contentgeneratie."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct is een op grote schaal voorgetraind programmeerinstructiemodel met krachtige codebegrip- en generatiecapaciteiten, dat efficiënt verschillende programmeertaken kan verwerken, vooral geschikt voor slimme codegeneratie, automatiseringsscripts en het beantwoorden van programmeervragen."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct is een groot taalmodel dat speciaal is ontworpen voor codegeneratie, codebegrip en efficiënte ontwikkelingsscenario's, met een toonaangevende parameteromvang van 32B, dat kan voldoen aan diverse programmeerbehoeften."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B is een <PERSON> (hybride expertmodel) dat de \"hybride redeneermodus\" introduceert, waarmee gebruikers naadloos kunnen schakelen tussen \"denkmodus\" en \"niet-denkmodus\". Het ondersteunt begrip en redenering in 119 talen en dialecten en beschikt over krachtige tool-aanroepmogelijkheden. Op het gebied van algemene vaardigheden, codering en wiskunde, meertalige capaciteiten, kennis en redenering kan het concurreren met toonaangevende grote modellen op de markt zoals DeepSeek R1, OpenAI o1, o3-mini, Grok 3 en Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B is een dense model dat de \"hybride redeneermodus\" introduceert, waarmee gebruikers naadloos kunnen schakelen tussen \"denkmodus\" en \"niet-denkmodus\". Dank<PERSON>j verbeter<PERSON> in de modelarchitectuur, toegenomen trainingsdata en effectievere trainingsmethoden presteert het model in het algemeen vergelijkbaar met Qwen2.5-72B."}, "SenseChat": {"description": "Basis<PERSON><PERSON> van het model (V4), met een <PERSON><PERSON><PERSON><PERSON> van 4K, heeft sterke algemene capaciteiten."}, "SenseChat-128K": {"description": "Basis<PERSON><PERSON> van het model (V4), met een <PERSON><PERSON><PERSON><PERSON> van 128<PERSON>, preste<PERSON> u<PERSON><PERSON> in taken van begrip en generatie van lange teksten."}, "SenseChat-32K": {"description": "Basis<PERSON><PERSON> van het model (V4), met een <PERSON><PERSON><PERSON><PERSON> van 32<PERSON>, flex<PERSON> toe<PERSON><PERSON> in verschillende scenario's."}, "SenseChat-5": {"description": "De nieuwste versie van het model (V5.5), met een context<PERSON><PERSON> van 128<PERSON>, heeft aanzienlijke verbeteringen in wiskundig redeneren, Engelse conversatie, instructievolging en begrip van lange teksten, en kan zich meten met GPT-4o."}, "SenseChat-5-1202": {"description": "Gebaseerd op versie V5.5, met significante verbeteringen ten opzichte van de vorige versie in basisvaardigheden in Chinees en Engels, chatten, exacte wet<PERSON>ppen, gees<PERSON><PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON>, wiskundige logica en woordenaantalcontrole."}, "SenseChat-5-Cantonese": {"description": "Met een contextlengte van 32K overtreft het de conversatiebegrip in het Kantonees van GPT-4 en kan het zich in verschillende domeinen zoals kennis, redeneren, wiskunde en coderen meten met GPT-4 Turbo."}, "SenseChat-5-beta": {"description": "Presteert in sommige opzichten beter dan SenseCat-5-1202"}, "SenseChat-Character": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> van het model, met een <PERSON><PERSON><PERSON><PERSON> van 8K, hoge respons<PERSON>he<PERSON>."}, "SenseChat-Character-Pro": {"description": "Gea<PERSON><PERSON><PERSON> vers<PERSON> van het model, met een <PERSON><PERSON><PERSON><PERSON> van 32<PERSON>, met uitgebreide verbeteringen in capaciteiten, onders<PERSON><PERSON> zowel Chinese als Engelse conversaties."}, "SenseChat-Turbo": {"description": "Geschikt voor snelle vraag-en-antwoord en modelafstemming."}, "SenseChat-Turbo-1202": {"description": "<PERSON><PERSON> is de nieuwste lichte versie van het model, die meer dan 90% van de capaciteiten van het volledige model bereikt en de kosten voor inferentie aanzienlijk verlaagt."}, "SenseChat-Vision": {"description": "De nieuwste versie van het model (V5.5) ondersteunt meerdere afbeeldingen als invoer en heeft aanzienlijke optimalisaties doorgevoerd in de basiscapaciteiten van het model, met verbeteringen in objecteigenschappenherkenning, ruimtelijke relaties, actie-evenementherkenning, scènebegrip, emotieherkenning, logische kennisredenering en tekstbegrip en -generatie."}, "SenseNova-V6-5-Pro": {"description": "Door een uitgebreide update van multimodale, taal- en redeneergegevens en optimalisatie van trainingsstrategieën, heeft het nieuwe model aanzienlijke verbeteringen gerealiseerd in multimodale redenering en generalisatie van instructievolging. Het ondersteunt een contextvenster tot 128k en presteert uitstekend in gespecialiseerde taken zoals OCR en herkenning van toeristische IP."}, "SenseNova-V6-5-Turbo": {"description": "Door een uitgebreide update van multimodale, taal- en redeneergegevens en optimalisatie van trainingsstrategieën, heeft het nieuwe model aanzienlijke verbeteringen gerealiseerd in multimodale redenering en generalisatie van instructievolging. Het ondersteunt een contextvenster tot 128k en presteert uitstekend in gespecialiseerde taken zoals OCR en herkenning van toeristische IP."}, "SenseNova-V6-Pro": {"description": "Realiseert de native integratie van afbeeldingen, tekst en video, doorbreekt de traditionele beperkingen van gescheiden multimodaliteit, en heeft in de OpenCompass en SuperCLUE evaluaties dubbele kampioenstitels behaald."}, "SenseNova-V6-Reasoner": {"description": "Combineert visuele en taaldiepe redenering, <PERSON>ert lang<PERSON>am denken en diepgaande redenering, en presenteert het volledige denkproces."}, "SenseNova-V6-Turbo": {"description": "Realiseert de native integratie van afbeeldingen, tekst en video, doorbreekt de traditionele beperkingen van gescheiden multimodaliteit, en is op alle belangrijke dimensies zoals basisvaardigheden in multimodaliteit en taalvaardigheden toonaangevend, met een even<PERSON><PERSON><PERSON> benadering van tekst en redenering, en heeft in verschillende evaluaties herhaaldelijk de top van binnen- en buitenlandse ranglijsten bereikt."}, "Skylark2-lite-8k": {"description": "De tweede generatie Skylark (Skylark2) model, Skylark2-lite model heeft een hoge responssnelheid, geschikt voor scenario's met hoge realtimevereisten, kostenbewustzijn en lagere modelnauwkeurigheidsvereisten, met een <PERSON><PERSON><PERSON> lengte van 8k."}, "Skylark2-pro-32k": {"description": "De tweede generatie Skylark (Skylark2) model, Skylark2-pro versie heeft een hoge modelna<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d, geschikt voor complexere tekstgeneratiescenario's zoals professionele copywriting, romanproductie, en hoogwaardig vertalen, met een <PERSON><PERSON><PERSON> lengte van 32k."}, "Skylark2-pro-4k": {"description": "De tweede generatie Skylark (Skylark2) model, Skylark2-pro model heeft een hoge model<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, geschikt voor complexere tekstgeneratiescenario's zoals professionele copywriting, romanproductie, en hoogwaardig vertalen, met een <PERSON><PERSON><PERSON> lengte van 4k."}, "Skylark2-pro-character-4k": {"description": "De tweede generatie Skylark (Skylark2) model, Skylark2-pro-character model heeft uitstekende rolspelin en chatmogelijkheden, en is goed in het a<PERSON><PERSON> van verschillende rollen op basis van gebruikersprompt, met een natuurlijk vloeiende conversatie. Ideaal voor het bouwen van chatbot<PERSON>, virtuele assistenten en online klantenservice met hoge responssnelheden."}, "Skylark2-pro-turbo-8k": {"description": "De tweede generatie Skylark (Skylark2) model, Skylark2-pro-turbo-8k biedt snellere inferentie en lagere kosten, met een <PERSON><PERSON><PERSON> lengte van 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 is de nieuwe generatie open source model uit de GLM-serie, met 32 miljard parameters. De prestaties van dit model zi<PERSON> met die van OpenAI's GPT-serie en DeepSeek's V3/R1-serie."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 is een klein model uit de GLM-serie met 9 miljard parameters. Dit model erft de technische kenmerken van de GLM-4-32B-serie, maar biedt een lichtere implementatieoptie. Ondanks de kleinere schaal toont GLM-4-9B-0414 nog steeds uitstekende capaciteiten in taken zoals codegeneratie, webdesign, SVG-graphics generatie en op zoek gebaseerde schrijfopdrachten."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking is een open source visueel-taalmodel (VLM) dat gezamenlijk is uitgebracht door Zhipu AI en het KEG-laboratorium van de Tsinghua Universiteit. Het is speciaal ontworpen voor het verwerken van complexe multimodale cognitieve taken. Dit model is gebaseerd op het GLM-4-9B-0414 basismodel en verbetert aanzienlijk de crossmodale redeneercapaciteiten en stabiliteit door de introductie van een 'Chain-of-Thought' redeneermethode en het gebruik van versterkend leren."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 is een redeneringsmodel met diep denkvermogen. Dit model is ontwikkeld op basis van GLM-4-32B-0414 door middel van koude start en versterkend leren, en is verder getraind op wiskunde, code en logische taken. In vergelijking met het basismodel heeft GLM-Z1-32B-0414 aanzienlijke verbeteringen in wiskundige vaardigheden en het oplossen van complexe taken."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 is een klein model uit de GLM-serie met slechts 9 miljard parameters, maar toont verbazingwekkende capaciteiten terwijl het de open source traditie behoudt. Ondanks de kleinere schaal presteert dit model nog steeds uitstekend in wiskundige redenering en algemene taken, met een algehele prestatie die vooroploopt in de open source modellen van gelijke schaal."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 is een diep redeneringsmodel met reflectievermogen (vergelijkbaar met OpenAI's Deep Research). In tegenstelling tot typische diep denkmodellen, gebruikt het reflectiemodel langere periodes van diep nadenken om meer open en complexe problemen op te lossen."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B is de open-source versie die een geoptimaliseerde gesprekservaring biedt voor gespreksapplicaties."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B is het eerste grote redeneermodel met lange context (LRM) dat getraind is met versterkend leren, geoptimaliseerd voor lange tekstredeneringstaken. Het model bereikt stabiele overdracht van korte naar lange context via een progressief contextuitbreidingsraamwerk. In zeven lange context documentvraag-en-antwoord benchmarks overtreft QwenLong-L1-32B vlaggenschipmodellen zoals OpenAI-o3-mini en Qwen3-235B-A22B, en presteert vergelijkbaar met Claude-3.7-Sonnet-Thinking. Het model blinkt uit in complexe taken zoals wiskundige, logische en multi-hop redenering."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B heeft de uitstekende algemene taalvaardigheden van de oorspronkelijke modelserie behouden en heeft door incrementele training van 500 miljard hoogwaardige tokens de wiskundige logica en codevaardigheden aanzienlijk verbeterd."}, "abab5.5-chat": {"description": "Gericht op productiviteitsscenario's, ondersteunt complexe taakverwerking en efficiënte tekstgeneratie, geschikt voor professionele toepassingen."}, "abab5.5s-chat": {"description": "Speciaal ontworpen voor Chinese personagegesprekken, biedt hoogwaardige Chinese gespreksgeneratiecapaciteiten, geschikt voor diverse toepassingsscenario's."}, "abab6.5g-chat": {"description": "Speciaal ontworpen voor meertalige personagegesprekken, ondersteunt hoogwaardige gespreksgeneratie in het Engels en andere talen."}, "abab6.5s-chat": {"description": "Geschikt voor een breed scala aan natuurlijke taalverwerkingstaken, waaronder tekstgeneratie, conversatiesystemen, enz."}, "abab6.5t-chat": {"description": "Geoptimaliseerd voor Chinese personagegesprekken, biedt vloeiende en cultureel passende gespreksgeneratiecapaciteiten."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 is een gea<PERSON>erd groot taalmodel, geopt<PERSON><PERSON><PERSON><PERSON> met versterkend leren en koude startdata, met uitstekende prestaties in redeneren, wiskunde en programmeren."}, "accounts/fireworks/models/deepseek-v3": {"description": "<PERSON>en krachtige Mixture-of-Experts (MoE) taalmodel van <PERSON>, met een totaal aantal parameters van 671B, waarbij 37B parameters per token worden geactiveerd."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Llama 3 70B instructiemodel, speciaal geoptimaliseerd voor meertalige gesprekken en natuurlijke taalbegrip, presteert beter dan de meeste concurrerende modellen."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Llama 3 8B instructiemodel, geoptimaliseerd voor gesprekken en meertalige taken, presteert uitstekend en efficiënt."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Llama 3 8B instructiemodel (HF-versie), consistent met de officiële implementatieresultaten, biedt hoge consistentie en cross-platform compatibiliteit."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Llama 3.1 405B instructiemodel heeft een enorm aantal parameters, geschikt voor complexe taken en instructies in omgevingen met hoge belasting."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Llama 3.1 70B instructiemodel biedt uitstekende natuurlijke taalbegrip en generatiecapaciteiten, ideaal voor gespreks- en analysetaken."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Llama 3.1 8B instructiemodel, geoptimaliseerd voor meertalige gesprekken, kan de meeste open-source en gesloten-source modellen overtreffen op gangbare industriestandaarden."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Meta's 11B-parameter instructie-geoptimaliseerde beeldredeneringsmodel. Dit model is geoptimaliseerd voor visuele herkenning, beeldredenering, afbeeldingsbeschrijving en het beantwoorden van algemene vragen over afbeeldingen. Dit model kan visuele gegeven<PERSON> begrijpen, zoals diagrammen en grafieken, en overbrugt de kloof tussen visuele informatie en tekst door het genereren van tekstbeschrijvingen van afbeeldingsdetails."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B instructiemodel is een lich<PERSON><PERSON><PERSON><PERSON> meertalig model geïntroduceerd door Meta. Dit model is ontworpen om de efficiëntie te verhogen, met aanzienlijke verbeteringen in latentie en kosten in vergelijking met grotere modellen. Voorbeelden van gebruikssituaties van dit model zijn het herformuleren van vragen en prompts, evenals schrijfondersteuning."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Meta's 90B-parameter instructie-geoptimaliseerde beeldredeneringsmodel. Dit model is geoptimaliseerd voor visuele herkenning, beeldredenering, afbeeldingsbeschrijving en het beantwoorden van algemene vragen over afbeeldingen. Dit model kan visuele gegeven<PERSON> begrijpen, zoals diagrammen en grafieken, en overbrugt de kloof tussen visuele informatie en tekst door het genereren van tekstbeschrijvingen van afbeeldingsdetails."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct is de update van december voor Llama 3.1 70B. Dit model is verbeterd op basis van Llama 3.1 70B (uitgebracht in juli 2024) en biedt verbeterde toolaanroepen, ondersteuning voor meertalige teksten, wiskunde en programmeervaardigheden. Het model heeft een toonaangevende prestatie bereikt op het gebied van redeneren, wiskunde en het volgen van instructies, en kan prestaties bieden die vergelijkbaar zijn met die van 3.1 405B, met aanzienlijke voordelen op het gebied van snelheid en kosten."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Een model met 24B parameters, dat geavanceerde mogelijkheden biedt die vergelijkbaar zijn met grotere modellen."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B instructiemodel, met een groot aantal parameters en een multi-expertarchitectuur, biedt uitgebreide ondersteuning voor de efficiënte verwerking van complexe taken."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B instructiemodel, met een multi-expertarchitectuur die efficiënte instructievolging en uitvoering biedt."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMax L2 13B model, dat gebruik maakt van innovatieve samenvoegtechnologie, is goed in verhalen vertellen en rollenspellen."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision instructiemodel, een licht<PERSON>t multimodaal model dat complexe visuele en tekstuele informatie kan verwerken, met ster<PERSON> redeneercapaciteiten."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "Het QwQ-model is een experimenteel onderzoeksmodel ontwikkeld door het Qwen-team, gericht op het verbeteren van de AI-redeneringscapaciteiten."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "De 72B versie van het Qwen-VL model is het nieuwste resultaa<PERSON> van <PERSON>'s iteraties, dat bijna een jaar aan innovaties vertegenwoordigt."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 is een serie decoder-only taalmodellen ontwikkeld door het Alibaba Qwen-team. Deze modellen zijn be<PERSON> in verschillende groottes, waaronder 0.5B, 1.5B, 3B, 7B, 14B, 32B en 72B, met zowel een basisversie als een instructieversie."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct is de nieuwste versie van de code-specifieke grote taalmodelreeks die door Alibaba Cloud is uitgebracht. Dit model is aanzienlijk verbeterd in codegeneratie, redenering en herstelcapaciteiten door training met 55 biljoen tokens, gebaseerd op Qwen2.5. Het versterkt niet alleen de coderingscapaciteiten, maar behoudt ook de voordelen van wiskundige en algemene vaardigheden. Het model biedt een meer uitgebreide basis voor praktische toepassingen zoals code-agenten."}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Large model, met uitstekende meertalige verwerkingscapaciteiten, geschikt voor verschillende taalgeneratie- en begripstaken."}, "ai21-jamba-1.5-large": {"description": "<PERSON><PERSON> meertalig model met 398 miljard parameters (94 miljard actief), biedt een contextvenster van 256<PERSON>, functieaanroep, gestructureerde output en gegronde generatie."}, "ai21-jamba-1.5-mini": {"description": "<PERSON><PERSON> meertalig model met 52 miljard parameters (12 miljard actief), biedt een contextven<PERSON> van 256<PERSON>, functie<PERSON>roep, gestructureerde output en gegronde generatie."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "<PERSON>en meertalig model met 398 miljard parameters (waarvan 94 miljard actief), biedt een contextvenster van 256K tokens, functieaanroepen, gestructureerde output en feitelijke generatie."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "<PERSON>en meertalig model met 52 miljard parameters (waarvan 12 miljard actief), biedt een contextvenster van 256K tokens, functieaanroepen, gestructureerde output en feitelijke generatie."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Son<PERSON> heeft de industrienormen verbeterd, met prestaties die de concurrentiemodellen en Claude 3 Opus overtreffen, en presteert uitstekend in brede evaluaties, met de snelheid en kosten van ons gemiddelde model."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Son<PERSON> heeft de industrienormen verhoogd, met prestaties die de concurrentiemodellen en Claude 3 Opus overtreffen. Het presteert uitstekend in uitgebreide evaluaties, terwijl het de snelheid en kosten van onze middelgrote modellen behoudt."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku is het snelste en meest compacte model van Anthropic, met bijna onmiddellijke reactietijden. Het kan snel eenvoudige vragen en verzoeken beantwoorden. Klanten kunnen een naadloze AI-ervaring creëren die menselijke interactie nabootst. Claude 3 Haiku kan afbeeldingen verwerken en tekstoutput retourneren, met een context<PERSON>ster van 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus is het krachtigste AI-model <PERSON>, met geavanceerde prestaties op zeer complexe taken. Het kan open prompts en ongeziene scenario's verwer<PERSON>, met uitstekende vloeiendheid en mensachtige begrip. Claude 3 Opus toont de grenzen van de mogelijkheden van generatieve AI. Claude 3 Opus kan afbeeldingen verwerken en tekstoutput retourneren, met een <PERSON><PERSON><PERSON> van 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet van Anthropic bereikt een ideale balans tussen intelligentie en snelheid - bijzonder geschikt voor bedrijfswerkbelasting. Het biedt maximale bruikbaarheid tegen lagere kosten dan concurrenten en is ontworpen als een betrouwbare, duur<PERSON><PERSON> hoofdmachine, geschikt voor grootschalige AI-implementaties. Claude 3 Sonnet kan afbeeldingen verwerken en tekstoutput retourneren, met een <PERSON><PERSON><PERSON> van 200K."}, "anthropic.claude-instant-v1": {"description": "<PERSON><PERSON> s<PERSON>, kosteneffectief en toch zeer capabel model dat een reeks taken kan verwerken, wa<PERSON><PERSON> dagelijkse gesprekken, tekstanalyses, samenvattingen en documentvragen."}, "anthropic.claude-v2": {"description": "An<PERSON><PERSON>'s model toont hoge capaciteiten in een breed scala aan taken, van complexe gesprekken en creatieve inhoudgeneratie tot gedetailleerde instructievolging."}, "anthropic.claude-v2:1": {"description": "De bijgewerkte versie van Claude 2, met een verd<PERSON><PERSON><PERSON> van het contextvenster en verbeteringen in betrouwbaarheid, hallucinatiepercentages en op bewijs gebaseerde nauwkeurigheid in lange documenten en RAG-contexten."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku is het snelste en meest compacte model van Anthropic, ontworpen voor bijna onmiddellijke reacties. Het biedt snelle en nauwkeurige gerichte prestaties."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus is het krachtigste model van Anthropic voor het verwerken van zeer complexe taken. Het excelleert in prestaties, intelligentie, vloeiendheid en begrip."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Hai<PERSON> is het snelste volgende generatie model van Anthropic. In vergelijking met <PERSON> 3 Haiku heeft Claude 3.5 Haiku verbeteringen in verschillende vaardigheden en overtreft het de grootste modellen van de vorige generatie, Claude 3 Opus, in veel intellectuele benchmarktests."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet biedt mogelijkheden die verder gaan dan Opus en een snellere snelheid dan <PERSON>, terwijl het dezelfde prijs als Sonnet behoudt. Sonnet is bijzonder goed in programmeren, datawetenschap, visuele verwerking en agenttaken."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet is het meest geavanceerde model van Anthropic tot nu toe en het eerste hybride redeneermodel op de markt. Claude 3.7 Sonnet kan bijna onmiddellijke reacties of uitgebreide stapsgewijze overpeinzingen genereren, waarbij gebruikers deze processen duidelijk kunnen volgen. Sonnet is bijzon<PERSON> goed in programmeren, datawetenschap, visuele verwerking en agenttaken."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 is het krachtigste model van Anthropic voor het verwerken van zeer complexe taken. Het blinkt uit in prestaties, intelligentie, vloeiendheid en begrip."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 kan bijna onmiddellijke reacties genereren of uitgebreide stapsgewijze overwegingen, waarbij gebruikers deze processen duidelijk kunnen volgen. API-gebruikers kunnen ook de denktijd van het model nauwkeurig regelen."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B is een sparsely activated groot taalmodel met 72 miljard parameters en 16 miljard geactiveerde parameters. Het is gebaseerd op de Group Mixture of Experts (MoGE) architectuur, waarbij experts worden gegroepeerd tijdens de selectie en tokens binnen elke groep een gelijk aantal experts activeren, wat zorgt voor een gebalanceerde expertbelasting en de efficiëntie van modelimplementatie op het Ascend-platform aanzienlijk verbetert."}, "aya": {"description": "Aya 23 is een me<PERSON><PERSON><PERSON> model <PERSON>, ondersteunt 23 talen en biedt gemak voor diverse taaltoepassingen."}, "aya:35b": {"description": "Aya 23 is een me<PERSON><PERSON><PERSON> model <PERSON>, ondersteunt 23 talen en biedt gemak voor diverse taaltoepassingen."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B is een open-source, commercieel bruikbaar groot taalmodel ontwikkeld door Baichuan Intelligent, met 13 miljard parameters, dat de beste prestaties in zijn klasse heeft behaald op gezaghebbende Chinese en Engelse benchmarks."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B is een groot taalmodel ontwikkeld door Baidu, gebaseerd op een hybride expert (MoE) architectuur. Het model heeft in totaal 300 miljard parameters, maar activeert slechts 47 miljard parameters per token tijdens inferentie, wat krachtige prestaties combineert met rekenefficiëntie. Als een kernmodel van de ERNIE 4.5-serie toont het uitstekende capaciteiten in tekstbegrip, generatie, redenering en programmeren. Het model gebruikt een innovatieve multimodale heterogene MoE pre-trainingsmethode, waarbij tekst- en visuele modaliteiten gezamenlijk worden getraind, wat de algehele prestaties verbetert, vooral in instructienaleving en wereldkennis."}, "c4ai-aya-expanse-32b": {"description": "<PERSON><PERSON> is een ho<PERSON>wa<PERSON>ig 32B meertalig model, ontworpen om de prestaties van eentalige modellen uit te dagen door middel van instructietuning, data-arbitrage, voorkeurstraining en modelintegratie. Het ondersteunt 23 talen."}, "c4ai-aya-expanse-8b": {"description": "<PERSON><PERSON> is een ho<PERSON><PERSON>ig 8B meertalig model, ontworpen om de prestaties van eentalige modellen uit te dagen door middel van instructietuning, data-arbitrage, voorkeurstraining en modelintegratie. Het ondersteunt 23 talen."}, "c4ai-aya-vision-32b": {"description": "Aya Vision is een geavanceerd multimodaal model dat uitstekende prestaties levert op meerdere belangrijke benchmarks voor taal-, tekst- en beeldcapaciteiten. <PERSON><PERSON> versie met 32 miljard parameters richt zich op de meest geavanceerde meertalige prestaties."}, "c4ai-aya-vision-8b": {"description": "Aya Vision is een geavanceerd multimodaal model dat uitstekende prestaties levert op meerdere belangrijke benchmarks voor taal-, tekst- en beeldcapaciteiten. <PERSON>ze versie met 8 miljard parameters richt zich op lage latentie en optimale prestaties."}, "charglm-3": {"description": "CharGLM-3 is ontworpen voor rollenspellen en emotionele begeleiding, ondersteunt zeer lange meerdaagse herinneringen en gepersonaliseerde gesprekken, met brede toepassingen."}, "charglm-4": {"description": "CharGLM-4 is ontworpen voor rollenspel en emotionele begeleiding, ondersteunt extreem lange meerdaagse herinneringen en gepersonaliseerde gesprekken, met brede toepassingen."}, "chatglm3": {"description": "ChatGLM3 is een ges<PERSON>en bronmodel dat is uitgebracht door Zhipu AI en de KEG-laboratorium van Tsinghua-universiteit. Het is voorafgetraind met een enorme hoeveelheid Chinese en Engelse identificatoren en getraind om in overeenstemming te zijn met menselijke voorkeuren. In vergelijking met het eerste model, heeft het verbeteringen van respectievelijk 16%, 36% en 280% behaald op MMLU, C-Eval en GSM8K, en staat het bovendruk op de Chinese taaklijst C-Eval. Het is geschikt voor scenario's met hoge eisen aan kennis, redeneringsvermogen en creativiteit, zoals het schrijven van advertentieteksten, romans, kennisgerelateerde teksten en codegeneratie."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base is een open source basismodel van de nieuwste generatie van de ChatGLM-reeks, ontwikkeld door ZhiPu, met een schaal van 6 miljard parameters."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o is een dynamisch model dat in realtime wordt bijgewerkt om de meest actuele versie te behouden. Het combineert krachtige taalbegrip- en generatiecapaciteiten, geschikt voor grootschalige toepassingsscenario's, wa<PERSON><PERSON> klantenservice, onderwijs en technische ondersteuning."}, "claude-2.0": {"description": "Claude 2 biedt belangrijke vooruitgangen in capaciteiten voor bedrijven, wa<PERSON><PERSON> de toonaangevende 200K token context, een aanzienlijke vermindering van de frequentie van modelhallucinaties, systeemprompten en een nieuwe testfunctie: functie-aanroepen."}, "claude-2.1": {"description": "Claude 2 biedt belangrijke vooruitgangen in capaciteiten voor bedrijven, wa<PERSON><PERSON> de toonaangevende 200K token context, een aanzienlijke vermindering van de frequentie van modelhallucinaties, systeemprompten en een nieuwe testfunctie: functie-aanroepen."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Hai<PERSON> is het snelste volgende generatie model van Anthropic. In vergelijking met <PERSON> 3 Haiku heeft Claude 3.5 Haiku verbeteringen in alle vaardigheden en overtreft het de grootste modellen van de vorige generatie, Claude 3 Opus, in veel intellectuele benchmarktests."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet biedt mogelijkheden die verder gaan dan Opus en is sneller dan <PERSON>, terwijl het dezelfde prijs behoudt. Sonnet is bijzonder goed in programmeren, datawetenschap, visuele verwerking en agenttaken."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet biedt mogelijkheden die verder gaan dan Opus en is sneller dan <PERSON>, terwijl het dezelfde prijs als Sonnet behoudt. Sonnet is bijzonder goed in programmeren, datawetenschap, visuele verwerking en agendataken."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Son<PERSON> is een van de ni<PERSON>ws<PERSON> model<PERSON>, met verbeterde prestaties en een groter <PERSON>, waardoor het model beter in staat is om complexe taken uit te voeren."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku is het snelste en meest compacte model van Anthropic, ontworpen voor bijna onmiddellijke reacties. Het heeft snelle en nauwkeurige gerichte prestaties."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus is het krachtigste model van Anthropic voor het verwerken van zeer complexe taken. Het presteert uitstekend op het gebied van prestaties, intelligentie, vloeiendheid en begrip."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet biedt een ideale balans tussen intelligentie en snelheid voor bedrijfswerkbelastingen. Het biedt maximale bruikbaarheid tegen een lagere prijs, betrouwbaar en geschikt voor grootschalige implementatie."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 is het krachtigste model van Anthropic voor het verwerken van zeer complexe taken. Het presteert uitstekend op het gebied van prestaties, intelligentie, vloeiendheid en begrip."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet kan bijna onmiddellijke reacties of verlengde, geleidelijke overpeinzingen genereren, waarbij gebruikers deze processen duidelijk kunnen zien. API-gebruikers kunnen ook gedetailleerde controle uitoefenen over de denktijd van het model."}, "codegeex-4": {"description": "CodeGeeX-4 is een krachtige AI-programmeerassistent die slimme vraag- en antwoordmogelijkheden en code-aanvulling ondersteunt voor verschillende programmeertalen, waardoor de ontwikkelingssnelheid wordt verhoogd."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B is een meertalig codegeneratiemodel dat uitgebreide functionaliteit biedt, waaronder code-aanvulling en -generatie, code-interpreter, webzoekfunctie, functieaanroepen en repository-niveau codevragen, en dekt verschillende scenario's in softwareontwikkeling. Het is een top codegeneratiemodel met minder dan 10B parameters."}, "codegemma": {"description": "CodeGemma is een lichtgewicht taalmodel dat speciaal is ontworpen voor verschillende programmeertaken, ondersteunt snelle iteratie en integratie."}, "codegemma:2b": {"description": "CodeGemma is een lichtgewicht taalmodel dat speciaal is ontworpen voor verschillende programmeertaken, ondersteunt snelle iteratie en integratie."}, "codellama": {"description": "Code Llama is een LLM dat zich richt op codegeneratie en -discussie, met brede ondersteuning voor programmeertalen, geschikt voor ontwikkelaarsomgevingen."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama is een LLM die zich richt op codegeneratie en -discussie, met uitgebreide ondersteuning voor programmeertalen, geschikt voor ontwikkelaarsomgevingen."}, "codellama:13b": {"description": "Code Llama is een LLM dat zich richt op codegeneratie en -discussie, met brede ondersteuning voor programmeertalen, geschikt voor ontwikkelaarsomgevingen."}, "codellama:34b": {"description": "Code Llama is een LLM dat zich richt op codegeneratie en -discussie, met brede ondersteuning voor programmeertalen, geschikt voor ontwikkelaarsomgevingen."}, "codellama:70b": {"description": "Code Llama is een LLM dat zich richt op codegeneratie en -discussie, met brede ondersteuning voor programmeertalen, geschikt voor ontwikkelaarsomgevingen."}, "codeqwen": {"description": "CodeQwen1.5 is een groot taalmodel dat is getraind op een grote hoeveelheid codegegevens, speciaal ontworpen om complexe programmeertaken op te lossen."}, "codestral": {"description": "Codestral is het eerste codemodel van Mistral AI, biedt uitstekende ondersteuning voor codegeneratietaken."}, "codestral-latest": {"description": "Codestral is een geavanceerd generatief model dat zich richt op codegeneratie, geoptimaliseerd voor tussentijdse invulling en code-aanvultaken."}, "codex-mini-latest": {"description": "codex-mini-latest is een fijn afgestemde versie van o4-mini, speciaal ontworpen voor Codex CLI. Voor direct gebruik via de API raden we aan te beginnen met gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B is een model ontworpen voor instructievolging, gesprekken en programmeren."}, "cogview-4": {"description": "CogView-4 is het eerste open-source tekst-naar-beeldmodel van Zhipu dat Chinese karakters ondersteunt. Het biedt een algehele verbetering in semantisch begrip, beeldgeneratiekwaliteit en de mogelijkheid om zowel Chinese als Engelse teksten te genereren. Het ondersteunt tweetalige invoer van willekeurige lengte in het Chinees en Engels en kan afbeeldingen genereren met elke resolutie binnen het opgegeven bereik."}, "cohere-command-r": {"description": "Command R is een scha<PERSON><PERSON> generatief model gericht op RAG en Tool Use om productie-schaal AI voor ondernemingen mogelijk te maken."}, "cohere-command-r-plus": {"description": "Command R+ is een state-of-the-art RAG-geoptimaliseerd model ontworpen om enterprise-grade workloads aan te pakken."}, "cohere/Cohere-command-r": {"description": "Command R is een s<PERSON><PERSON><PERSON> generatief model ontworpen voor RAG en toolgebruik, waarmee bedrijven productieklaar AI kunnen realiseren."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ is een geavanceerd RAG-geoptimaliseerd model, ontworpen voor bedrijfsniveau workloads."}, "command": {"description": "Een instructievolgend dialoogmodel dat hoge kwaliteit en betrouwbaarheid biedt voor taaltaken, met een langere contextlengte dan ons basisgeneratiemodel."}, "command-a-03-2025": {"description": "Command A is ons krachtigste model tot nu toe, met uitstekende prestaties in het gebruik van tools, agenten, retrieval-augmented generation (RAG) en meertalige toepassingen. Command A heeft een contextlengte van 256K, kan draaien op slechts twee GPU's en heeft een doorvoersnelheid die 150% hoger is dan die van Command R+ 08-2024."}, "command-light": {"description": "<PERSON><PERSON> klein<PERSON>, snellere versie van Command, die bijna even krachtig is, maar sneller."}, "command-light-nightly": {"description": "Om de tijd tussen de belangrijkste versies te verkorten, hebben we een nightly versie van het Command-model gelanceerd. Voor de command-light serie wordt deze versie command-light-nightly genoemd. Houd er rekening mee dat command-light-nightly de nieuwste, meest experimentele en (mogelijk) onbetrouwbare versie is. Nightly versies worden regelmatig bijgewerkt zonder voorafgaande kennisgeving, dus het wordt niet aanbevolen om deze in productieomgevingen te gebruiken."}, "command-nightly": {"description": "Om de tijd tussen de belangrijkste versies te verkorten, hebben we een nightly versie van het Command-model gelanceerd. Voor de Command-serie wordt deze versie command-cightly genoemd. Houd er rekening mee dat command-nightly de nieuwste, meest experimentele en (mogelijk) onbetrouwbare versie is. Nightly versies worden regelmatig bijgewerkt zonder voorafgaande kennisgeving, dus het wordt niet aanbevolen om deze in productieomgevingen te gebruiken."}, "command-r": {"description": "Command R is geoptimaliseerd voor conversatie- en lange contexttaken, bijzonder geschikt voor dynamische interactie en kennisbeheer."}, "command-r-03-2024": {"description": "Command R is een instructievolgend dialoogmodel dat hogere kwaliteit en betrouwbaarheid biedt voor taaltaken, met een langere contextlengte dan eerdere modellen. Het kan worden gebruikt voor complexe workflows, zoals codegeneratie, retrieval-augmented generation (RAG), toolgebruik en agenten."}, "command-r-08-2024": {"description": "command-r-08-2024 is een bijgewerkte versie van het Command R-model, uitgebracht in augustus 2024."}, "command-r-plus": {"description": "Command R+ is een ho<PERSON><PERSON><PERSON>erend groot taalmodel, speciaal ontworpen voor echte zakelijke scenario's en complexe toepassingen."}, "command-r-plus-04-2024": {"description": "Command R+ is een instructievolgend dialoogmodel dat hogere kwaliteit en betrouwbaarheid biedt voor taaltaken, met een langere contextlengte dan eerdere modellen. Het is het meest geschikt voor complexe RAG-workflows en meervoudig gebruik van tools."}, "command-r-plus-08-2024": {"description": "Command R+ is een dialoogmodel dat instructies volgt, met hogere kwaliteit en betrouwbaarheid in taalgerelateerde taken, en een langere contextlengte dan eerdere modellen. Het is het meest geschikt voor complexe RAG-workflows en het gebruik van meerdere stappen."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 is een compacte en efficiënte bijgewerkte versie, uitgebracht in december 2024. Het presteert uitstekend in taken die complexe redenering en meervoudige verwerking vereisen, zoals RAG, toolgebruik en agenten."}, "compound-beta": {"description": "Compound-beta is een samengesteld AI-systeem dat wordt ondersteund door meerdere open beschikbare modellen in GroqCloud, en kan intelligent en selectief tools gebruiken om gebruikersvragen te beantwoorden."}, "compound-beta-mini": {"description": "Compound-beta-mini is een samengesteld AI-systeem dat wordt ondersteund door open beschikbare modellen in GroqCloud, en kan intelligent en selectief tools gebruiken om gebruikersvragen te beantwoorden."}, "computer-use-preview": {"description": "Het computer-use-preview model is een speciaal model ontworpen voor 'computergebruiktools', getraind om computergerelateerde taken te begrijpen en uit te voeren."}, "dall-e-2": {"description": "De tweede generatie DALL·E model, ondersteunt realistischere en nauwkeurigere beeldgeneratie, met een resolutie die vier keer zo hoog is als die van de eerste generatie."}, "dall-e-3": {"description": "Het nieuwste DALL·E model, uitgebracht in november 2023. Ondersteunt realistischere en nauwkeurigere beeldgeneratie met een sterkere detailweergave."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct biedt betrouwbare instructieverwerkingscapaciteiten en ondersteunt toepassingen in verschillende sectoren."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 is een op versterkend leren (RL) aangedreven inferentiemodel dat de problemen van herhaling en leesbaarheid in het model oplost. Voor RL introduceerde DeepSeek-R1 koude startdata om de inferentieprestaties verder te optimaliseren. Het presteert vergelijkbaar met OpenAI-o1 in wiskunde, code en inferentietaken, en verbetert de algehele effectiviteit door zorgvuldig ontworpen trainingsmethoden."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 verbetert aanzienlijk de diepte van redeneer- en inferentiecapaciteiten door gebruik te maken van extra rekenkracht en algoritmische optimalisaties tijdens de natrainingsfase. Het model presteert uitstekend op diverse benchmarktests, <PERSON><PERSON><PERSON> wisk<PERSON>, programmeren en algemene logica. De algehele prestaties benaderen nu toonaangevende modellen zoals O3 en Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B is een model verkregen door het destilleren van ketens van gedachten van DeepSeek-R1-0528 naar Qwen3 8B Base. Dit model bereikt state-of-the-art prestaties onder open source modellen, overtreft Qwen3 8B met 10% in de AIME 2024 test en bereikt het prestatieniveau van Qwen3-235B-thinking. Het presteert uitstekend in wiskundige redenering, programmeren en algemene logica benchmarks. De architectuur is identiek aan Qwen3-8B, maar deelt de tokenizerconfiguratie van DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1 distillatiemodel, geoptimaliseerd voor inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 distillatiemodel, geoptimaliseerd voor inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 distillatiemodel, geoptimaliseerd voor inferentieprestaties door versterkend leren en koude startdata, open-source model dat de multi-taak benchmark vernieuwt."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B is een model dat is verkregen door kennisdistillatie van Qwen2.5-32B. Dit model is fijn afgestemd met 800.000 zorgvuldig geselecteerde voorbeelden gegenereerd door DeepSeek-R1 en toont uitstekende prestaties in verschillende domeinen zoals wiskunde, programmeren en redeneren. Het heeft uitstekende resultaten behaald in meerdere benchmarktests, waaronder een nauwkeurigheid van 94,3% op MATH-500, wat sterke wiskundige redeneringscapaciteiten aantoont."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B is een model dat is verkregen door kennisdistillatie van Qwen2.5-Math-7B. Dit model is fijn afgestemd met 800.000 zorgvuldig geselecteerde voorbeelden gegenereerd door DeepSeek-R1 en toont uitstekende inferentiecapaciteiten. Het heeft uitstekende resultaten behaald in verschillende benchmarktests, met een nauwkeurigheid van 92,8% op MATH-500, een slaagpercentage van 55,5% op AIME 2024, en een score van 1189 op CodeForces, wat sterke wiskundige en programmeercapaciteiten aantoont voor een model van 7B schaal."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 combineert de uitstekende kenmerken van eerdere versies en versterkt de algemene en coderingscapaciteiten."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 is een hybride expert (MoE) taalmodel met 6710 miljard parameters, dat gebru<PERSON><PERSON><PERSON><PERSON> van multi-head latent attention (MLA) en de DeepSeekMoE-architectuur, g<PERSON><PERSON><PERSON><PERSON> met een load balancing-strategie zonder extra verlies, om de inferentie- en trainingsefficiëntie te optimaliseren. Door voorgetraind te worden op 14,8 biljoen hoogwaardige tokens en vervolgens te worden fijngetuned met supervisie en versterkend leren, overtreft DeepSeek-V3 andere open-source modellen in prestaties en komt het dicht in de buurt van toonaangevende gesloten modellen."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B is een geavanceerd model dat is getraind voor complexe gesprekken."}, "deepseek-ai/deepseek-r1": {"description": "Gea<PERSON><PERSON> efficiënt LLM, gespecialiseerd in redeneren, wiskunde en programmeren."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 is een hybride expert (MoE) visueel taalmodel dat is ontwikkeld op basis van DeepSeekMoE-27B, met een <PERSON><PERSON>-architectuur met spaarzame activatie, die uitstekende prestaties levert met slechts 4,5 miljard geactiveerde parameters. Dit model presteert uitstekend in verschillende taken, waaronder visuele vraag-antwoord, optische tekenherkenning, document/tabel/grafiekbegrip en visuele positionering."}, "deepseek-chat": {"description": "<PERSON><PERSON> nieuw open-source model dat algemene en code-capaciteiten combineert, behoudt niet alleen de algemene conversatiecapaciteiten van het oorspronkelijke Chat-model en de krachtige codeverwerkingscapaciteiten van het Coder-model, maar is ook beter afgestemd op menselijke voorkeuren. Bovendien heeft DeepSeek-V2.5 aanzienlijke verbeteringen gerealiseerd in schrijfopdrachten, instructievolging en andere gebieden."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B is een code-taalmodel, getraind op 20 biljoen gegeven<PERSON>, waarvan 87% code en 13% in het Chinees en Engels. Het model introduceert een venstergrootte van 16K en invultaken, en biedt projectniveau code-aanvulling en fragmentinvulfunctionaliteit."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 is een open-source hybride expertcode-model, presteert uitstekend in code-taken en is vergelijkbaar met GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 is een open-source hybride expertcode-model, presteert uitstekend in code-taken en is vergelijkbaar met GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 is een op versterkend leren (RL) aangedreven inferentiemodel dat de problemen van herhaling en leesbaarheid in het model oplost. Voor RL introduceerde DeepSeek-R1 koude startdata om de inferentieprestaties verder te optimaliseren. Het presteert vergelijkbaar met OpenAI-o1 in wiskunde, code en inferentietaken, en verbetert de algehele effectiviteit door zorgvuldig ontworpen trainingsmethoden."}, "deepseek-r1-0528": {"description": "685 miljard parameter full-power model, uitgebracht op 28 mei 2025. DeepSeek-R1 maakt uitgebreid gebruik van versterkend leren in de post-trainingsfase, wat de reden is voor de aanzienlijke verbetering van het redeneervermogen van het model ondanks zeer beperkte gelabelde data. Het presteert uitstekend op taken zoals wiskunde, coderen en natuurlijke taalredenering."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B snelle versie, ondersteunt realtime online zoeken en biedt snellere reactietijden zonder in te boeten op modelprestaties."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B standaardversie, ondersteunt realtime online zoeken, geschikt voor dialoog- en tekstverwerkingstaken die actuele informatie vereisen."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama is een model dat is g<PERSON><PERSON><PERSON><PERSON> van Llama op basis van DeepSeek-R1."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - een groter en slimmer model binnen de DeepSeek suite - is gedistilleerd naar de Llama 70B architectuur. Op basis van benchmarktests en menselijke evaluaties is dit model slimmer dan de originele Llama 70B, vooral in taken die wiskundige en feitelijke nauwkeurigheid vereisen."}, "deepseek-r1-distill-llama-8b": {"description": "Het DeepSeek-R1-Distill model is verkregen door middel van kennisdistillatie-technologie, wa<PERSON><PERSON><PERSON> samples gegenereerd door DeepSeek-R1 zijn afgestemd op open-source modellen zoals Qwen en Llama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Eerste release op 14 februari 2025, gedistilleerd door het <PERSON><PERSON><PERSON> grote model ontwikkelteam met Llama3_70B als basis (gebouwd met Meta Llama), <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'s corpora zijn toe<PERSON>vo<PERSON>d aan de gedistilleerde data."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Eerste release op 14 februari 2025, gedistilleerd door het <PERSON><PERSON><PERSON> grote model ontwikkelteam met Llama3_8B als basis (gebouwd met Meta Llama), <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'s corpora zijn toe<PERSON>vo<PERSON>d aan de gedistilleerde data."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen is een model dat is g<PERSON><PERSON><PERSON><PERSON> op basis van DeepSeek-R1."}, "deepseek-r1-distill-qwen-1.5b": {"description": "Het DeepSeek-R1-Distill model is verkregen door middel van kennisdistillatie-technologie, wa<PERSON><PERSON><PERSON> samples gegenereerd door DeepSeek-R1 zijn afgestemd op open-source modellen zoals Qwen en Llama."}, "deepseek-r1-distill-qwen-14b": {"description": "Het DeepSeek-R1-Distill model is verkregen door middel van kennisdistillatie-technologie, wa<PERSON><PERSON><PERSON> samples gegenereerd door DeepSeek-R1 zijn afgestemd op open-source modellen zoals Qwen en Llama."}, "deepseek-r1-distill-qwen-32b": {"description": "Het DeepSeek-R1-Distill model is verkregen door middel van kennisdistillatie-technologie, wa<PERSON><PERSON><PERSON> samples gegenereerd door DeepSeek-R1 zijn afgestemd op open-source modellen zoals Qwen en Llama."}, "deepseek-r1-distill-qwen-7b": {"description": "Het DeepSeek-R1-Distill model is verkregen door middel van kennisdistillatie-technologie, wa<PERSON><PERSON><PERSON> samples gegenereerd door DeepSeek-R1 zijn afgestemd op open-source modellen zoals Qwen en Llama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 volledige snelle versie, ondersteunt realtime online zoeken en combineert de krachtige capaciteiten van 671B parameters met snellere reactietijden."}, "deepseek-r1-online": {"description": "DeepSeek R1 volledige versie, met 671B parameters, ondersteunt realtime online zoeken en heeft krachtige begrip- en generatiecapaciteiten."}, "deepseek-reasoner": {"description": "Het redeneer model van DeepSeek. Voordat het model het uiteindelijke antwoord geeft, genereert het eerst een stuk denkproces om de nauwkeurigheid van het uiteindelijke antwoord te verbeteren."}, "deepseek-v2": {"description": "DeepSeek V2 is een efficiënt Mixture-of-Experts taalmodel, geschikt voor kosteneffectieve verwerkingsbehoeften."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B is het ontwerpcode-model van DeepSeek, biedt krachtige codegeneratiecapaciteiten."}, "deepseek-v3": {"description": "DeepSeek-V3 is een <PERSON>-model dat is ontwikkeld door Hangzhou DeepSeek Artificial Intelligence Technology Research Co., Ltd. Het heeft uitstekende scores in verschillende evaluaties en staat bovenaan de open-source modellen in de mainstream ranglijsten. V3 heeft de generatiesnelheid met 3 keer verbeterd in vergelijking met het V2.5 model, wat zorgt voor een snellere en soepelere gebruikerservaring."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 is een MoE-model met 671 miljard parameters, dat uitblinkt in programmeer- en technische vaardigheden, contextbegrip en het verwerken van lange teksten."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 is een expert gemengd model met 685B parameters, de nieuwste iteratie van de vlaggenschip chatmodelreeks van het DeepSeek-team.\n\nHet is een opvolge<PERSON> van het [DeepSeek V3](/deepseek/deepseek-chat-v3) model en presteert uitstekend in verschillende taken."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 is een expert gemengd model met 685B parameters, de nieuwste iteratie van de vlaggenschip chatmodelreeks van het DeepSeek-team.\n\nHet is een opvolge<PERSON> van het [DeepSeek V3](/deepseek/deepseek-chat-v3) model en presteert uitstekend in verschillende taken."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 heeft de redeneringscapaciteiten van het model a<PERSON>zien<PERSON><PERSON> verbeterd, zelf<PERSON> met zeer weinig gelabelde gegeven<PERSON>. Voordat het model het uiteindelijke antwoord geeft, genereert het eerst een denkproces om de nauwkeurigheid van het uiteindelijke antwoord te verbeteren."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 verbetert de redeneercapaciteit van het model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zelfs met zeer weinig gelabelde data. Voor het geven van het uiteindelijke antwoord genereert het model eerst een keten van gedachten om de nauwkeurigheid van het antwoord te verhogen."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 verbetert de redeneercapaciteit van het model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zelfs met zeer weinig gelabelde data. Voor het geven van het uiteindelijke antwoord genereert het model eerst een keten van gedachten om de nauwkeurigheid van het antwoord te verhogen."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B is een groot taalmodel gebaseerd op Llama3.3 70B, dat gebru<PERSON><PERSON><PERSON><PERSON> van de fine-tuning van DeepSeek R1-output en vergelijkbare concurrentieprestaties bereikt als grote vooraanstaande modellen."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B is een gedistilleerd groot taalmodel gebaseerd op Llama-3.1-8B-Instruct, dat is getraind met behulp van de output van DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B is een gedistilleerd groot taalmodel gebaseerd op Qwen 2.5 14B, dat is getraind met behulp van de output van DeepSeek R1. Dit model heeft in verschillende benchmarktests OpenAI's o1-mini overtroffen en heeft de nieuwste technologische vooruitgang behaald voor dichte modellen (state-of-the-art). Hier zijn enkele resultaten van benchmarktests:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nDit model toont concurrentieprestaties die vergelijkbaar zijn met grotere vooraanstaande modellen door fine-tuning op de output van DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B is een gedistilleerd groot taalmodel gebaseerd op Qwen 2.5 32B, dat is getraind met behulp van de output van DeepSeek R1. Dit model heeft in verschillende benchmarktests OpenAI's o1-mini overtroffen en heeft de nieuwste technologische vooruitgang behaald voor dichte modellen (state-of-the-art). Hier zijn enkele resultaten van benchmarktests:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nDit model toont concurrentieprestaties die vergelijkbaar zijn met grotere vooraanstaande modellen door fine-tuning op de output van DeepSeek R1."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 is het nieuwste open-source model dat door het DeepSeek-team is u<PERSON><PERSON><PERSON><PERSON>, met zeer krachtige inferentieprestaties, vooral op het gebied van wiskunde, programmeren en redeneringstaken, en bereikt een niveau dat verge<PERSON> is met het o1-model van OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 heeft de redeneringscapaciteiten van het model a<PERSON>zien<PERSON><PERSON> verbeterd, zelf<PERSON> met zeer weinig gelabelde gegeven<PERSON>. Voordat het model het uiteindelijke antwoord geeft, genereert het eerst een denkproces om de nauwkeurigheid van het uiteindelijke antwoord te verbeteren."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 heeft een belangrijke doorbraak bereikt in inferentiesnelheid ten opzichte van eerdere modellen. Het staat op de eerste plaats onder open-source modellen en kan zich meten met de meest geavanceerde gesloten modellen ter wereld. DeepSeek-V3 maakt gebruik van Multi-Head Latent Attention (MLA) en de DeepSeekMoE-architectuur, die grondig zijn gevalideerd in DeepSeek-V2. Bovendien introduceert DeepSeek-V3 een aanvullende verliesloze strategie voor load balancing en stelt het multi-label voorspellingsdoelen in om sterkere prestaties te behalen."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 heeft een belangrijke doorbraak bereikt in inferentiesnelheid ten opzichte van eerdere modellen. Het staat op de eerste plaats onder open-source modellen en kan zich meten met de meest geavanceerde gesloten modellen ter wereld. DeepSeek-V3 maakt gebruik van Multi-Head Latent Attention (MLA) en de DeepSeekMoE-architectuur, die grondig zijn gevalideerd in DeepSeek-V2. Bovendien introduceert DeepSeek-V3 een aanvullende verliesloze strategie voor load balancing en stelt het multi-label voorspellingsdoelen in om sterkere prestaties te behalen."}, "deepseek_r1": {"description": "DeepSeek-R1 is een redeneringsmodel aangedreven door versterkend leren (RL), dat de problemen van herhaling en leesba<PERSON>heid in het model oplost. Voor RL introduceerde DeepSeek-R1 koude startgegevens, wat de inferentieprestaties verder optimaliseerde. Het presteert vergelijkbaar met OpenAI-o1 in wiskunde, code en redeneringstaken, en heeft door zorgvuldig ontworpen trainingsmethoden de algehele effectiviteit verbeterd."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B is een model dat is verkregen door distillatie training van Llama-3.3-70B-Instruct. Dit model maakt deel uit van de DeepSeek-R1 serie en toont uitstekende prestaties in verschillende domeinen zoals wiskunde, programmeren en redenering door gebruik te maken van voorbeelden gegenereerd door DeepSeek-R1."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B is een model dat is verkregen door kennisdistillatie van Qwen2.5-14B. Dit model is fijn afgesteld met 800.000 zorgvuldig geselecteerde voorbeelden gegenereerd door DeepSeek-R1, en toont uitstekende redeneringscapaciteiten."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B is een model dat is verkregen door kennisdistillatie van Qwen2.5-32B. Dit model is fijn afgesteld met 800.000 zorgvuldig geselecteerde voorbeelden gegenereerd door DeepSeek-R1, en toont uitstekende prestaties in verschillende domeinen zoals wiskunde, programmeren en redenering."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite is de nieuwste generatie lichtgewicht model, met extreme responssnelheid en prestaties die wereldwijd tot de top behoren."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k is een uitgebreide upgrade van Doubao-1.5-Pro, met een algehele prestatieverbetering van 10%. Ondersteunt redenering met een context<PERSON>ster van 256k en een maximale uitvoerlengte van 12k tokens. <PERSON><PERSON><PERSON> prestaties, g<PERSON><PERSON> venst<PERSON>, uitstekende prijs-kwaliteitverhouding, geschikt voor een breder scala aan toepassingsscenario's."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro is de nieuwste generatie hoofdmachine, met algehele prestatie-upgrades en uitstekende prestaties op het gebied van kennis, code, redenering, enz."}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5 is een gloednieuw diepdenkend model dat uitblinkt in professionele gebieden zoals wiskunde, programmeren en wetenschappelijk redeneren, evenals in algemene taken zoals creatief schrijven. Het heeft op verschillende prestigieuze benchmarks zoals AIME 2024, Codeforces en GPQA niveaus bereikt die gelijk zijn aan of dicht bij de top van de industrie liggen. Ondersteunt een contextvenster van 128k en een output van 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 is een gloednieuw diepdenkend model (m-versie met native multimodale diepe redeneercapaciteiten), dat uitblinkt in vakgebieden zoals wiskunde, programmeren, wetenschappelijke redenering en creatieve schrijfopdrachten. Het beha<PERSON>t of benadert de top van de industrie op gerenommeerde benchmarks zoals AIME 2024, Codeforces en GPQA. Ondersteunt een contextvenster van 128k en output van 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "<PERSON><PERSON> nieuw visueel diepdenkend model met sterkere algemene multimodale begrip- en redeneercapaciteiten, dat op 37 van de 59 openbare benchmarktests SOTA-prestaties behaalt."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS is een native agentmodel gericht op grafische gebruikersinterface (GUI) interactie. Het biedt mensachtige capaciteiten zoals waarnemen, redeneren en handelen voor naadloze interactie met GUI's."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite is een nieuw geüpgradede multimodale groot model, dat ondersteuning biedt voor beeldherkenning met willekeurige resoluties en extreme beeldverhoudingen, en de visuele redenering, documentherkenning, begrip van gedetailleerde informatie en het volgen van instructies verbetert. Ondersteunt een contextvenster van 128k en een maximale outputlengte van 16k tokens."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro is een nieuw geüpgraded multimodaal groot model dat beeldherkenning ondersteunt met elke resolutie en extreme beeldverhoudingen, en verbeterde visuele redenering, documentherkenning, detailbegrip en instructienaleving biedt."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro is een nieuw geüpgraded multimodaal groot model dat beeldherkenning ondersteunt met elke resolutie en extreme beeldverhoudingen, en verbeterde visuele redenering, documentherkenning, detailbegrip en instructienaleving biedt."}, "doubao-lite-128k": {"description": "Biedt ultieme responssnelheid en een betere prijs-kwaliteitverhouding, met flexibele keuzes voor verschillende klantenscenario's. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON>."}, "doubao-lite-32k": {"description": "Biedt ultieme responssnelheid en een betere prijs-kwaliteitverhouding, met flexibele keuzes voor verschillende klantenscenario's. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van 32k."}, "doubao-lite-4k": {"description": "Biedt ultieme responssnelheid en een betere prijs-kwaliteitverhouding, met flexibele keuzes voor verschillende klantenscenario's. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van 4k."}, "doubao-pro-256k": {"description": "Het beste hoofdmodel, geschikt voor het verwerken van complexe taken, met uitstekende prestaties in scenario's zoals referentievragen, <PERSON><PERSON><PERSON><PERSON><PERSON>, crea<PERSON><PERSON>, tekstclassificatie en rollenspellen. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van <PERSON>."}, "doubao-pro-32k": {"description": "Het beste hoofdmodel, geschikt voor het verwerken van complexe taken, met uitstekende prestaties in scenario's zoals referentievragen, <PERSON><PERSON><PERSON><PERSON><PERSON>, crea<PERSON><PERSON>, tekstclassificatie en rollenspellen. Ondersteunt redeneren en fijn afstemmen met een <PERSON><PERSON><PERSON> van 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 is een gloednieuw multimodaal diepdenkend model dat drie denkwijzen ondersteunt: auto, thinking en non-thinking. In de non-thinking modus is de modelprestatie aanzienlijk verbeterd ten opzichte van Doubao-1.5-pro/250115. Ondersteunt een contextvenster van 256k en een maximale uitvoerlengte van 16k tokens."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash is een multimodaal diepdenkend model met extreem snelle inferentiesnelheid, TPOT slechts 10ms; ondersteunt zowel tekst- als visueel begrip, met tekstbegrip dat beter is dan de vorige lite-generatie en visueel begrip dat verge<PERSON>jkbaar is met concurrerende pro-serie modellen. Ondersteunt een contextvenster van 256k en een maximale uitvoerlengte van 16k tokens."}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking model heeft sterk verbeterde denkvermogens, met verdere verbeteringen in basisvaardigheden zoals coderen, wiskunde en logisch redeneren ten opzichte van Doubao-1.5-thinking-pro, en ondersteunt visueel begrip. Ondersteunt een contextvenster van 256k en een maximale uitvoerlengte van 16k tokens."}, "doubao-seedream-3-0-t2i-250415": {"description": "Het <PERSON><PERSON><PERSON> beeldgeneratiemodel is ontwikkeld door het Seed-team van ByteDance en ondersteunt zowel tekst- als beeldinvoer, en biedt een hoog controleerbare en hoogwaardige beeldgeneratie-ervaring. Het genereert beelden op basis van tekstprompts."}, "doubao-vision-lite-32k": {"description": "Het <PERSON>o-vision model is een multimodaal groot model <PERSON> met krachtige beeldbegrip- en redeneercapaciteiten, evenals nauwkeurige instructiebegrip. Het model presteert sterk bij het extraheren van beeld- en tekstinformatie en bij beeldgebaseerde redeneertaken, en is toepasbaar op complexere en bredere visuele vraag-en-antwoord scenario's."}, "doubao-vision-pro-32k": {"description": "Het <PERSON>o-vision model is een multimodaal groot model <PERSON> met krachtige beeldbegrip- en redeneercapaciteiten, evenals nauwkeurige instructiebegrip. Het model presteert sterk bij het extraheren van beeld- en tekstinformatie en bij beeldgebaseerde redeneertaken, en is toepasbaar op complexere en bredere visuele vraag-en-antwoord scenario's."}, "emohaa": {"description": "<PERSON><PERSON><PERSON> is een psychologisch model met professionele adviescapaciteiten, dat gebruikers helpt emotionele problemen te begrijpen."}, "ernie-3.5-128k": {"description": "<PERSON>t vlaggenschip grote ta<PERSON><PERSON><PERSON>, zelf on<PERSON><PERSON><PERSON><PERSON><PERSON>, dekt een enorme hoeveelheid Chinese en Engelse corpora, met sterke algemene capaciteiten die voldoen aan de meeste eisen voor dialoogvragen, creatieve generatie en plug-in toepassingsscenario's; ondersteunt automatische integratie met <PERSON><PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ernie-3.5-8k": {"description": "<PERSON>t vlaggenschip grote ta<PERSON><PERSON><PERSON>, zelf on<PERSON><PERSON><PERSON><PERSON><PERSON>, dekt een enorme hoeveelheid Chinese en Engelse corpora, met sterke algemene capaciteiten die voldoen aan de meeste eisen voor dialoogvragen, creatieve generatie en plug-in toepassingsscenario's; ondersteunt automatische integratie met <PERSON><PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ernie-3.5-8k-preview": {"description": "<PERSON>t vlaggenschip grote ta<PERSON><PERSON><PERSON>, zelf on<PERSON><PERSON><PERSON><PERSON><PERSON>, dekt een enorme hoeveelheid Chinese en Engelse corpora, met sterke algemene capaciteiten die voldoen aan de meeste eisen voor dialoogvragen, creatieve generatie en plug-in toepassingsscenario's; ondersteunt automatische integratie met <PERSON><PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ernie-4.0-8k-latest": {"description": "Het vlaggenschip ultra-grote taalmodel van Baidu, zelf ontwikkeld, heeft een algehele upgrade van modelcapaciteiten in vergelijking met ERNIE 3.5, en is breed toepasbaar in complexe taakscenario's in verschillende domeinen; ondersteunt automatische integratie met Bai<PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ernie-4.0-8k-preview": {"description": "Het vlaggenschip ultra-grote taalmodel van Baidu, zelf ontwikkeld, heeft een algehele upgrade van modelcapaciteiten in vergelijking met ERNIE 3.5, en is breed toepasbaar in complexe taakscenario's in verschillende domeinen; ondersteunt automatische integratie met Bai<PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen."}, "ernie-4.0-turbo-128k": {"description": "Het vlaggenschip ultra-grote taalmodel van <PERSON>du, zelf ontwikkeld, presteert uitstekend in algehele effectiviteit en is breed toepasbaar in complexe taakscenario's in verschillende domeinen; ondersteunt automatische integratie met <PERSON><PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen. Het presteert beter dan ERNIE 4.0."}, "ernie-4.0-turbo-8k-latest": {"description": "Het vlaggenschip ultra-grote taalmodel van <PERSON>du, zelf ontwikkeld, presteert uitstekend in algehele effectiviteit en is breed toepasbaar in complexe taakscenario's in verschillende domeinen; ondersteunt automatische integratie met <PERSON><PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen. Het presteert beter dan ERNIE 4.0."}, "ernie-4.0-turbo-8k-preview": {"description": "Het vlaggenschip ultra-grote taalmodel van <PERSON>du, zelf ontwikkeld, presteert uitstekend in algehele effectiviteit en is breed toepasbaar in complexe taakscenario's in verschillende domeinen; ondersteunt automatische integratie met <PERSON><PERSON> zoekplug-ins om de actualiteit van vraag- en antwoordinformatie te waarborgen. Het presteert beter dan ERNIE 4.0."}, "ernie-4.5-8k-preview": {"description": "Het Wenxin grote model 4.5 is een nieuwe generatie native multimodale basisgrootte model, ontwikkeld door Baidu, dat samenwerking optimaliseert door meerdere modaliteiten gezamenlijk te modelleren, met uitstekende multimodale begripcapaciteiten; het heeft verbeterde taal<PERSON>igheden, met verbeter<PERSON> in begrip, generatie, logica en geheugen, en significante verbeteringen in hallucinatie, logische redenering en codecapaciteiten."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo heeft duidelijke verbeteringen in het verminderen van hallucinaties, logische redenering en codevaardigheden. In vergelijking met Wenxin 4.5 is het sneller en goedkoper. De modelcapaciteiten zijn aanzienlijk verbeterd, waardoor het beter voldoet aan de verwerking van meerdaagse lange historische gesprekken en taken voor het begrijpen van lange documenten."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo heeft duidelijke verbeteringen in het verminderen van hallucinaties, logische redenering en codevaardigheden. In vergelijking met Wenxin 4.5 is het sneller en goedkoper. De capaciteiten voor tekstcreatie en kennisvragen zijn aanzienlijk verbeterd. De uitvoerlengte en de vertraging bij volledige zinnen zijn in vergelijking met ERNIE 4.5 toegenomen."}, "ernie-4.5-turbo-vl-32k": {"description": "De gloednieuwe versie van het <PERSON><PERSON> grote model heeft aanzienlijke verbeteringen in beeldbegrip, creatie, vertaling en code. Het ondersteunt voor het eerst een contextlengte van 32K, en de vertraging bij de eerste token is aanzienlijk verminderd."}, "ernie-char-8k": {"description": "Een door Baidu ontwikkeld groot taalmodel voor verticale scenario's, geschikt voor toepassingen zoals game NPC's, klantenservice dialoog, en rollenspellen, met een duidelijkere en consistentere karakterstijl, sterkere instructievolgcapaciteiten en betere inferentieprestaties."}, "ernie-char-fiction-8k": {"description": "Een door Baidu ontwikkeld groot taalmodel voor verticale scenario's, geschikt voor toepassingen zoals game NPC's, klantenservice dialoog, en rollenspellen, met een duidelijkere en consistentere karakterstijl, sterkere instructievolgcapaciteiten en betere inferentieprestaties."}, "ernie-irag-edit": {"description": "Het door Baidu zelf ontwikkelde ERNIE iRAG Edit beeldbewerkingsmodel ondersteunt bewerkingen zoals wissen (erase), her<PERSON><PERSON><PERSON> (repaint) en variatie (variantie genereren) op basis van afbeeldingen."}, "ernie-lite-8k": {"description": "ERNIE Lite is een lichtge<PERSON>t groot taalmodel dat door Baidu is ontwikkeld, dat uitstekende modelprestaties en inferentiecapaciteiten combineert, geschikt voor gebruik met AI-versnelling kaarten met lage rekencapaciteit."}, "ernie-lite-pro-128k": {"description": "<PERSON><PERSON> lichtgewicht groot taalmodel dat door <PERSON>du is ontwikkeld, dat uitstekende modelprestaties en inferentiecapaciteiten combineert, met betere prestaties dan ERNIE Lite, geschikt voor gebruik met AI-versnelling kaarten met lage rekencapaciteit."}, "ernie-novel-8k": {"description": "<PERSON><PERSON> alge<PERSON>n groot taalmodel dat door <PERSON><PERSON> is ontwikkeld, met duidelijke voordelen in het vervolgschrijven van romans, en ook toepasba<PERSON> in korte toneelstukken, films en andere scenario's."}, "ernie-speed-128k": {"description": "Het nieuwste zelfontwikkelde hoge-prestatie grote taalmodel van Baidu, dat uitstekende algemene capaciteiten heeft en geschikt is als basis model voor afstemming, om beter specifieke scenario's aan te pakken, met uitstekende inferentieprestaties."}, "ernie-speed-pro-128k": {"description": "Het nieuwste zelfontwikkelde hoge-prestatie grote taalmodel van Baidu, dat uitstekende algemene capaciteiten heeft en betere prestaties levert dan ERNIE Speed, geschikt als basis model voor afstemming, om beter specifieke scenario's aan te pak<PERSON>, met uitstekende inferentieprestaties."}, "ernie-tiny-8k": {"description": "ERNIE Tiny is een ultra-presterend groot taalmodel dat de laagste implementatie- en afstemmingskosten heeft binnen de Wenxin modelreeks."}, "ernie-x1-32k": {"description": "Be<PERSON><PERSON>t over sterkere capaciteiten in begrip, planning, reflectie en evolutie. Als een diep<PERSON>and denkmodel met een breder scala aan v<PERSON>, combineert Wenxin X1 nauwkeurigheid, creativiteit en stijl, en excelleert het in Chinese kennisvragen, literaire creatie, te<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dagelijkse gesprekken, logische redenering, complexe berekeningen en het aanroepen van tools."}, "ernie-x1-32k-preview": {"description": "Het Wenxin grote model X1 heeft sterkere capaciteiten in begrip, planning, reflectie en evolutie. Als een dieper denkmodel met bredere mogelijkheden, combineert Wenxin X1 nauwkeurigheid, creativiteit en stijl, en excelleert in Chinese kennisvragen, literaire creatie, te<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dagelijkse gesprekken, logische redenering, complexe berekeningen en het aanroepen van tools."}, "ernie-x1-turbo-32k": {"description": "In vergelijking met ERNIE-X1-32K biedt dit model betere prestaties en effectiviteit."}, "flux-1-schnell": {"description": "<PERSON><PERSON> tekst-naar-beeldmodel met 12 miljard parameters ontwikkeld door Black Forest Labs, gebruikmakend van latente adversariële diffusie-distillatie technologie, dat hoogwaardige beelden kan genereren binnen 1 tot 4 stappen. Dit model pre<PERSON><PERSON> met gesloten bron alternatieven en wordt uitgebracht onder de Apache-2.0 licentie, geschikt voor persoonlijk, wetenschappelijk en commercieel gebruik."}, "flux-dev": {"description": "FLUX.1 [dev] is een open-source gewicht en verfijnd model voor niet-commercieel gebruik. Het behoudt een beeldkwaliteit en instructienaleving vergeli<PERSON><PERSON>ar met de professionele versie van FLUX, maar met een hogere operationele efficiëntie. Vergeleken met standaardmodell<PERSON> van dezelfde grootte is het efficiënter in het gebruik van middelen."}, "flux-kontext/dev": {"description": "Frontier beeldbewerkingsmodel."}, "flux-merged": {"description": "Het FLUX.1-merged model combineert de diepgaande kenmerken verkend tijdens de ontwikkelingsfase van \"DEV\" met de hoge uitvoeringssnelheid van \"Schnell\". Deze combinatie verhoogt niet alleen de prestatiegrenzen van het model, maar breidt ook het toepassingsgebied uit."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] kan tekst en referentieafbeeldingen als invoer verwerken, waardoor doelgerichte lokale bewerkingen en complexe algehele scèneveranderingen naadloos mogelijk zijn."}, "flux-schnell": {"description": "FLUX.1 [schnell] is momenteel het meest geavanceerde open-source model met weinig stappen, dat niet alleen concurrenten overtreft, maar ook krachtige niet-gedistilleerde modellen zoals Midjourney v6.0 en DALL·E 3 (HD). Het model is speciaal fijn afgesteld om de volledige outputdiversiteit van de pre-trainingsfase te behouden. Vergeleken met de meest geavanceerde modellen op de markt verbetert FLUX.1 [schnell] aanzienlijk de visuele kwaliteit, instructienaleving, schaal/verhouding aanpassing, lettertypeverwerking en outputdiversiteit, wat gebruikers een rijkere en gevarieerdere creatieve beeldgeneratie-ervaring biedt."}, "flux.1-schnell": {"description": "Een Rectified Flow Transformer met 12 miljard parameters, in staat om beelden te genereren op basis van tekstbeschrijvingen."}, "flux/schnell": {"description": "FLUX.1 [schnell] is een streaming transformer-model met 12 miljard parameters, dat binnen 1 tot 4 stappen hoogwaardige afbeeldingen uit tekst kan genereren, geschikt voor persoonlijk en commercieel gebruik."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning) biedt stabiele en afstelbare prestaties, ideaal voor oplossingen voor complexe taken."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning) biedt uitstekende multimodale ondersteuning, gericht op effectieve oplossingen voor complexe taken."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro is Google's high-performance AI-model, ontworpen voor brede taakuitbreiding."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 is een efficiënt multimodaal model dat ondersteuning biedt voor brede toepassingsuitbreiding."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 is een efficiënt multimodaal model dat ondersteuning biedt voor een breed scala aan toepassingen."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B is een efficiënte multimodale model dat een breed scala aan toepassingen ondersteunt."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 is het nieuwste experimentele model, met aanzienlijke prestatieverbeteringen in tekst- en multimodale toepassingen."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B is een efficiënte multimodale model dat uitgebreide toepassingen ondersteunt."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 biedt geoptimaliseerde multimodale verwerkingscapaciteiten, geschikt voor verschillende complexe taak scenario's."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash is Google's nieuwste multimodale AI-model, met snelle verwerkingscapaciteiten, ondersteunt tekst-, beeld- en video-invoer, en is geschikt voor efficiënte opschaling van verschillende taken."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 is een schaalbare multimodale AI-oplossing die ondersteuning biedt voor een breed scala aan complexe taken."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 is het nieuwste productieklare model, dat hogere kwaliteit output biedt, met name op het gebied van wiskunde, lange contexten en visuele taken."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 biedt uitstekende multimodale verwerkingscapaciteiten, wat grotere flexibiliteit in applicatieontwikkeling mogelijk maakt."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 combineert de nieuwste optimalisatietechnologieën en biedt efficiëntere multimodale gegevensverwerkingscapaciteiten."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro ondersteunt tot 2 miljoen tokens en is de ideale keuze voor middelgrote multimodale modellen, geschikt voor veelzijdige ondersteuning van complexe taken."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash biedt next-gen functies en verbeteringen, wa<PERSON><PERSON> u<PERSON> s<PERSON>, native toolgebruik, multimodale generatie en een contextvenster van 1M tokens."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash biedt next-gen functies en verbeteringen, wa<PERSON><PERSON> u<PERSON> s<PERSON>, native toolgebruik, multimodale generatie en een contextvenster van 1M tokens."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash modelvariant, geoptimaliseerd voor kosteneffectiviteit en lage latentie."}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash experimenteel model, ondersteunt afbeeldingsgeneratie"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash is een modelvariant die is geoptimaliseerd voor kosteneffectiviteit en lage latentie."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash is een modelvariant die is geoptimaliseerd voor kosteneffectiviteit en lage latentie."}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash previewmodel, ondersteunt beeldgeneratie"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash is het meest kosteneffectieve model van Google en biedt uitgebreide functionaliteiten."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-<PERSON><PERSON> is het kleinste en meest kosteneffectieve model van Google, speciaal ontworpen voor grootschalig gebruik."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview is het kleinste en meest kosteneffectieve model van Google, speciaal ontworpen voor grootschalig gebruik."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview is het meest kosteneffectieve model van Google, dat uitgebreide functionaliteit biedt."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview is het meest kosteneffectieve model van Google en biedt uitgebreide functionaliteiten."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro is het meest geavanceerde denkmodel van Google, in staat om complexe problemen op het gebied van code, wiskunde en STEM te redeneren, en grote datasets, codebases en documenten te analyseren met lange context."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview is Google's meest geavanceerde denkmodel, dat in staat is om te redeneren over complexe problemen in code, wiskunde en STEM-gebieden, en grote datasets, codebases en documenten te analyseren met behulp van lange context."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview is Google's meest geavanceerde denkmodel, in staat om te redeneren over complexe problemen in code, wiskunde en STEM-gebieden, en om grote datasets, codebases en documenten te analyseren met lange context."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview is Google's meest geavanceerde denkwijze-model, in staat om complexe problemen op het gebied van code, wiskunde en STEM te redeneren, en grote datasets, codebases en documenten te analyseren met lange context."}, "gemma-7b-it": {"description": "Gemma 7B is geschikt voor het verwerken van middelgrote taken, met een goede kosteneffectiviteit."}, "gemma2": {"description": "Gemma 2 is een efficiënt model van <PERSON>, dat een breed scala aan toepassingsscenario's dekt, van kleine toepassingen tot complexe gegevensverwerking."}, "gemma2-9b-it": {"description": "Gemma 2 9B is een model dat is geoptimaliseerd voor specifieke taken en toolintegratie."}, "gemma2:27b": {"description": "Gemma 2 is een efficiënt model van <PERSON>, dat een breed scala aan toepassingsscenario's dekt, van kleine toepassingen tot complexe gegevensverwerking."}, "gemma2:2b": {"description": "Gemma 2 is een efficiënt model van <PERSON>, dat een breed scala aan toepassingsscenario's dekt, van kleine toepassingen tot complexe gegevensverwerking."}, "generalv3": {"description": "Spark Pro is een hoogwaardig groot taalmodel dat is geoptimaliseerd voor professionele domeinen, met een focus op wiskunde, programmeren, geneeskunde, onderwijs en meer, en ondersteunt online zoeken en ingebouwde plugins voor weer, datum, enz. Het geoptimaliseerde model toont uitstekende prestaties en efficiëntie in complexe kennisvragen, taalbegrip en hoogwaardig tekstcreatie, en is de ideale keuze voor professionele toepassingsscenario's."}, "generalv3.5": {"description": "Spark3.5 Max is de meest uitgebreide versie, met ondersteuning voor online zoeken en talrijke ingebouwde plugins. De volledig geoptimaliseerde kerncapaciteiten, systeemrolinstellingen en functieaanroepfunctionaliteit zorgen voor uitstekende prestaties in verschillende complexe toepassingsscenario's."}, "glm-4": {"description": "GLM-4 is de oude vlaggenschipversie die in januari 2024 is uitgebracht en is inmiddels vervangen door de krachtigere GLM-4-0520."}, "glm-4-0520": {"description": "GLM-4-0520 is de nieuwste modelversie, speciaal ontworpen voor zeer complexe en diverse taken, met uitstekende prestaties."}, "glm-4-9b-chat": {"description": "GLM-4-9B-<PERSON><PERSON> pre<PERSON> goed op het gebied van semantiek, wiskunde, redeneren, code en kennis. Het beschikt ook over webbrowserfunctionaliteit, code-uitvoering, aangepaste toolaanroepen en lange tekstredenering. Ondersteunt 26 talen, wa<PERSON>nder <PERSON>s, Koreaans en Duits."}, "glm-4-air": {"description": "GLM-4-Air is een kosteneffectieve versie met prestaties die dicht bij GLM-4 liggen, met snelle snelheid en een betaalbare prijs."}, "glm-4-air-250414": {"description": "GLM-4-Air is een kosteneffectieve versie, met prestaties die dicht bij GLM-4 liggen, en biedt snelle snelheid tegen een betaalbare prijs."}, "glm-4-airx": {"description": "GLM-4-AirX biedt een efficiënte versie van GLM-4-Air, met een red<PERSON><PERSON><PERSON><PERSON><PERSON> tot 2,6 keer sneller."}, "glm-4-alltools": {"description": "GLM-4-AllTools is een multifunctioneel intelligent model, geoptimaliseerd om complexe instructieplanning en toolaanroepen te ondersteunen, zoals webbrowser, code-interpretatie en tekstgeneratie, geschikt voor multitasking."}, "glm-4-flash": {"description": "GLM-4-Flash is de ideale keuze voor het verwerken van eenvoudige taken, met de snelste snelheid en de laagste prijs."}, "glm-4-flash-250414": {"description": "GLM-4-Flash is de ideale keuze voor het verwerken van eenvoudige taken, met de snelste snelheid en gratis."}, "glm-4-flashx": {"description": "GLM-4-FlashX is een verbeterde versie van <PERSON> met een super snelle inferentiesnelheid."}, "glm-4-long": {"description": "GLM-4-<PERSON> ondersteunt zeer lange tekstin<PERSON>, geschikt voor geheugenintensieve taken en grootschalige documentverwerking."}, "glm-4-plus": {"description": "GLM-4-Plus, als vlaggenschip van hoge intelligentie, heeft krachtige capaciteiten voor het verwerken van lange teksten en complexe taken, met al<PERSON><PERSON><PERSON> prestatieverbeteringen."}, "glm-4.1v-thinking-flash": {"description": "De GLM-4.1V-Thinking serie modellen zijn momenteel de krachtigste visuele modellen binnen de bekende 10 miljard parameter VLM's. Ze integreren state-of-the-art visuele-taaltaakprestaties op hetzelfde niveau, waaronder videoverwerking, beeldvraag-antwoordsystemen, vakinhoudelijke probleemoplossing, OCR-tekstherkenning, document- en grafiekanalyse, GUI-agenten, frontend webcodering en grounding. De capaciteiten van meerdere taken overtreffen zelfs die van Qwen2.5-VL-72B met acht keer zoveel parameters. Door geavanceerde versterkend leren technologie beheerst het model chain-of-thought redenering om de nauwkeurigheid en rijkdom van antwoorden te verbeteren, wat resulteert in aanzienlijk betere eindresultaten en interpretatie dan traditionele niet-thinking modellen."}, "glm-4.1v-thinking-flashx": {"description": "De GLM-4.1V-Thinking serie modellen zijn momenteel de krachtigste visuele modellen binnen de bekende 10 miljard parameter VLM's. Ze integreren state-of-the-art visuele-taaltaakprestaties op hetzelfde niveau, waaronder videoverwerking, beeldvraag-antwoordsystemen, vakinhoudelijke probleemoplossing, OCR-tekstherkenning, document- en grafiekanalyse, GUI-agenten, frontend webcodering en grounding. De capaciteiten van meerdere taken overtreffen zelfs die van Qwen2.5-VL-72B met acht keer zoveel parameters. Door geavanceerde versterkend leren technologie beheerst het model chain-of-thought redenering om de nauwkeurigheid en rijkdom van antwoorden te verbeteren, wat resulteert in aanzienlijk betere eindresultaten en interpretatie dan traditionele niet-thinking modellen."}, "glm-4.5": {"description": "Het nieuwste vlaggenschipm<PERSON><PERSON>, onderste<PERSON> schakeling tussen den<PERSON>, met een algehele prestatie die het SOTA-niveau van open-source modellen bereikt, en een contextlengte tot 128K."}, "glm-4.5-air": {"description": "<PERSON><PERSON> lichtgewicht versie van GLM-4.5, die zowel prestaties als kosteneffectiviteit combineert en flexibel kan schakelen tussen hybride denkmodellen."}, "glm-4.5-airx": {"description": "De snelle versie van GLM-4.5-Air, met snell<PERSON> reactietijden, speciaal ontworpen voor grootschalige en hoge-snelheidsbehoeften."}, "glm-4.5-flash": {"description": "De gratis versie van GLM-4.5, met uitstekende prestaties in inferentie, codering en agenttaken."}, "glm-4.5-x": {"description": "De snelle versie van GLM-4.5, met krachtige prestaties en een generatie snelheid tot 100 tokens per seconde."}, "glm-4v": {"description": "GLM-4V biedt krachtige beeldbegrip- en redeneercapaciteiten, ondersteunt verschillende visuele taken."}, "glm-4v-flash": {"description": "GLM-4V-Flash richt zich op efficiënte enkele afbeelding begrip, en is geschikt voor scenario's met snelle afbeeldingsanalyse, zoals real-time beeldanalyse of batchafbeeldingsverwerking."}, "glm-4v-plus": {"description": "GLM-4V-Plus heeft de capaciteit om video-inhoud en meerdere afbeeldingen te begrijpen, geschikt voor multimodale taken."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus heeft de capaciteit om video-inhoud en meerdere afbeeldingen te begrijpen, geschikt voor multimodale taken."}, "glm-z1-air": {"description": "Redeneringsmodel: beschikt over krachtige redeneringscapaciteiten, geschikt voor taken die diepgaande redenering vereisen."}, "glm-z1-airx": {"description": "Supersnelle redenering: met een extreem snelle redeneringssnelheid en krachtige redeneringseffecten."}, "glm-z1-flash": {"description": "De GLM-Z1-serie beschikt over sterke capaciteiten voor complexe redenering en presteert uitstekend in logica, wiskunde en programmeren."}, "glm-z1-flashx": {"description": "Snel en betaalbaar: Flash verbeterde versie met ultrahoge inferentiesnelheid en snellere gelijktijdige verwerking."}, "glm-zero-preview": {"description": "GLM-Zero-Preview heeft krachtige complexe redeneercapaciteiten en presteert uitstekend in logische redenering, wiskunde en programmeren."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash biedt next-gen functies en verbeteringen, wa<PERSON><PERSON> u<PERSON> s<PERSON>, native toolgebruik, multimodale generatie en een contextvenster van 1M tokens."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental is Google's nieuwste experimentele multimodale AI-model, met een aanzienlijke kwaliteitsverbetering ten opzichte van eerdere versies, vooral voor wereldkennis, code en lange context."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash is het meest geavanceerde hoofdmodel van Google, speciaal ontworpen voor geavanceerde redenering, codering, wiskunde en wetenschappelijke taken. Het bevat ingebouwde 'denk'-mogelijkheden, waardoor het reacties kan leveren met hogere nauwkeurigheid en gedetailleerdere contextverwerking.\n\nLet op: dit model heeft twee varianten: denkend en niet-denkend. De prijsstelling van de output verschilt aanzienlijk afhankelijk van of de denkcapaciteit is geactiveerd. Als u de standaardvariant kiest (zonder de ':thinking' achtervoegsel), zal het model expliciet vermijden denk-tokens te genereren.\n\nOm gebruik te maken van de denkcapaciteit en denk-tokens te ontvangen, moet u de ':thinking' variant selecteren, wat resulteert in een hogere prijs voor denk-output.\n\nDaarnaast kan Gemini 2.5 Flash worden geconfigureerd via de parameter 'maximale tokens voor redenering', zoals beschreven in de documentatie (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash is Google's meest geavanceerde hoofmodel, ontworpen voor geavanceerde redenering, codering, wiskunde en wetenschappelijke taken. Het bevat ingebouwde 'denkkracht', waardoor het in staat is om antwoorden te geven met een hogere nauwkeurigheid en gedetailleerde contextverwerking.\n\nLet op: dit model heeft twee varianten: denken en niet-denken. De outputprijs verschilt aanzienlijk afhankelijk van of de denkkracht is geactiveerd. Als u de standaardvariant kiest (zonder de ':thinking' suffix), zal het model expliciet vermijden om denk-tokens te genereren.\n\nOm gebruik te maken van de denkkracht en denk-tokens te ontvangen, moet u de ':thinking' variant kiezen, wat resulteert in hogere prijzen voor denk-output.\n\nBovendien kan Gemini 2.5 Flash worden geconfigureerd via de parameter 'max tokens for reasoning', zoals beschreven in de documentatie (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash is Google's meest geavanceerde hoofmodel, ontworpen voor geavanceerde redenering, codering, wiskunde en wetenschappelijke taken. Het bevat ingebouwde 'denkkracht', waardoor het in staat is om antwoorden te geven met een hogere nauwkeurigheid en gedetailleerde contextverwerking.\n\nLet op: dit model heeft twee varianten: denken en niet-denken. De outputprijs verschilt aanzienlijk afhankelijk van of de denkkracht is geactiveerd. Als u de standaardvariant kiest (zonder de ':thinking' suffix), zal het model expliciet vermijden om denk-tokens te genereren.\n\nOm gebruik te maken van de denkkracht en denk-tokens te ontvangen, moet u de ':thinking' variant kiezen, wat resulteert in hogere prijzen voor denk-output.\n\nBovendien kan Gemini 2.5 Flash worden geconfigureerd via de parameter 'max tokens for reasoning', zoals beschreven in de documentatie (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro is het meest geavanceerde denkmodel van Google, in staat om complexe problemen op het gebied van code, wiskunde en STEM te redeneren, en om met lange context grote datasets, codebases en documentatie te analyseren."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview is het meest geavanceerde denkkader van Google, dat in staat is om complexe problemen op het gebied van code, wiskunde en STEM te redeneren, en grote datasets, codebases en documenten te analyseren met behulp van lange context."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash biedt geoptimaliseerde multimodale verwerkingscapaciteiten, geschikt voor verschillende complexe taakscenario's."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro combineert de nieuwste optimalisatietechnologieën en biedt efficiëntere multimodale gegevensverwerkingscapaciteiten."}, "google/gemma-2-27b": {"description": "Gemma 2 is een efficiënt model van <PERSON>, dat een breed scala aan toepassingen dekt, van kleine toepassingen tot complexe gegevensverwerking."}, "google/gemma-2-27b-it": {"description": "Gemma 2 behoudt het ontwerpprincipe van lichtgewicht en efficiëntie."}, "google/gemma-2-2b-it": {"description": "Google's licht<PERSON><PERSON><PERSON> instructieafstemmingsmodel"}, "google/gemma-2-9b": {"description": "Gemma 2 is een efficiënt model van <PERSON>, dat een breed scala aan toepassingen dekt, van kleine toepassingen tot complexe gegevensverwerking."}, "google/gemma-2-9b-it": {"description": "Gemma 2 is een lichtgewicht open-source tekstmodelserie van Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 is een lichtgewicht open-source tekstmodelserie van Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) biedt basis instructieverwerkingscapaciteiten, geschikt voor lichte toepassingen."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B is een open source taalmodel van Google dat nieuwe standaarden zet op het gebied van efficiëntie en prestaties."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B is een open-source taalmodel van Google dat nieuwe normen heeft gesteld op het gebied van efficiëntie en prestaties."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo, geschikt voor verschillende tekstgeneratie- en begrijptaken, wijst momenteel naar gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo, geschikt voor verschillende tekstgeneratie- en begrijptaken, wijst momenteel naar gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo, geschikt voor verschillende tekstgeneratie- en begrijptaken, wijst momenteel naar gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo, geschikt voor verschillende tekstgeneratie- en begrijptaken, wijst momenteel naar gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo, een efficiënt model aangeboden door OpenAI, geschikt voor chat- en tekstgeneratietaken, met ondersteuning voor parallelle functieaanroepen."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k, een tekstgeneratiemodel met hoge capaciteit, geschikt voor complexe taken."}, "gpt-4": {"description": "GPT-4 biedt een groter contextvenster en kan langere tekstinvoer verwerken, geschikt voor scenario's die uitgebreide informatie-integratie en data-analyse vereisen."}, "gpt-4-0125-preview": {"description": "Het nieuwste GPT-4 Turbo-model heeft visuele functies. Nu kunnen visuele verzoeken worden gedaan met be<PERSON><PERSON> van JSON-indeling en functieaanroepen. GPT-4 Turbo is een verbeterde versie die kosteneffectieve ondersteuning biedt voor multimodale taken. Het vindt een balans tussen nauwkeurigheid en efficiëntie, geschikt voor toepassingen die realtime interactie vereisen."}, "gpt-4-0613": {"description": "GPT-4 biedt een groter contextvenster en kan langere tekstinvoer verwerken, geschikt voor scenario's die uitgebreide informatie-integratie en data-analyse vereisen."}, "gpt-4-1106-preview": {"description": "Het nieuwste GPT-4 Turbo-model heeft visuele functies. Nu kunnen visuele verzoeken worden gedaan met be<PERSON><PERSON> van JSON-indeling en functieaanroepen. GPT-4 Turbo is een verbeterde versie die kosteneffectieve ondersteuning biedt voor multimodale taken. Het vindt een balans tussen nauwkeurigheid en efficiëntie, geschikt voor toepassingen die realtime interactie vereisen."}, "gpt-4-32k": {"description": "GPT-4 biedt een groter contextvenster en kan langere tekstinvoer verwerken, geschikt voor scenario's die uitgebreide informatie-integratie en data-analyse vereisen."}, "gpt-4-32k-0613": {"description": "GPT-4 biedt een groter contextvenster en kan langere tekstinvoer verwerken, geschikt voor scenario's die uitgebreide informatie-integratie en data-analyse vereisen."}, "gpt-4-turbo": {"description": "Het nieuwste GPT-4 Turbo-model heeft visuele functies. Nu kunnen visuele verzoeken worden gedaan met be<PERSON><PERSON> van JSON-indeling en functieaanroepen. GPT-4 Turbo is een verbeterde versie die kosteneffectieve ondersteuning biedt voor multimodale taken. Het vindt een balans tussen nauwkeurigheid en efficiëntie, geschikt voor toepassingen die realtime interactie vereisen."}, "gpt-4-turbo-2024-04-09": {"description": "Het nieuwste GPT-4 Turbo-model heeft visuele functies. Nu kunnen visuele verzoeken worden gedaan met be<PERSON><PERSON> van JSON-indeling en functieaanroepen. GPT-4 Turbo is een verbeterde versie die kosteneffectieve ondersteuning biedt voor multimodale taken. Het vindt een balans tussen nauwkeurigheid en efficiëntie, geschikt voor toepassingen die realtime interactie vereisen."}, "gpt-4-turbo-preview": {"description": "Het nieuwste GPT-4 Turbo-model heeft visuele functies. Nu kunnen visuele verzoeken worden gedaan met be<PERSON><PERSON> van JSON-indeling en functieaanroepen. GPT-4 Turbo is een verbeterde versie die kosteneffectieve ondersteuning biedt voor multimodale taken. Het vindt een balans tussen nauwkeurigheid en efficiëntie, geschikt voor toepassingen die realtime interactie vereisen."}, "gpt-4-vision-preview": {"description": "Het nieuwste GPT-4 Turbo-model heeft visuele functies. Nu kunnen visuele verzoeken worden gedaan met be<PERSON><PERSON> van JSON-indeling en functieaanroepen. GPT-4 Turbo is een verbeterde versie die kosteneffectieve ondersteuning biedt voor multimodale taken. Het vindt een balans tussen nauwkeurigheid en efficiëntie, geschikt voor toepassingen die realtime interactie vereisen."}, "gpt-4.1": {"description": "GPT-4.1 is ons vlaggenschipmodel voor complexe taken. Het is zeer geschikt voor het oplossen van problemen over verschillende domeinen."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini biedt een balans tussen intelligentie, snelheid en kosten, waardoor het een aantrekkelijk model is voor veel gebruikssituaties."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini biedt een balans tussen intelligentie, snelheid en kosten, waardoor het een aantrekkelijk model is voor veel gebruikssituaties."}, "gpt-4.5-preview": {"description": "De onderzoekspreview van GPT-4.5, ons grootste en krachtigste GPT-model tot nu toe. Het heeft een uitgebreide wereldkennis en kan de intenties van gebruikers beter begrijpen, waardoor het uitblinkt in creatieve taken en autonome planning. GPT-4.5 accepteert tekst- en afbeeldingsinvoer en genereert tekstuitvoer (inclusief gestructureerde uitvoer). Het ondersteunt belangrijke ontwikkelaarsfuncties zoals functieaanroepen, batch-API's en streaminguitvoer. In taken die creativiteit, open denken en dialoog vereisen (zoa<PERSON> schrij<PERSON>, leren of het verkennen van nieuwe ideeën), presteert GPT-4.5 bijzonder goed. De kennis is bijgewerkt tot oktober 2023."}, "gpt-4o": {"description": "ChatGPT-4o is een dynamisch model dat in realtime wordt bijgewerkt om de meest actuele versie te behouden. Het combineert krachtige taalbegrip- en generatiecapaciteiten, geschikt voor grootschalige toepassingsscenario's, wa<PERSON><PERSON> klantenservice, onderwijs en technische ondersteuning."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o is een dynamisch model dat in realtime wordt bijgewerkt om de meest actuele versie te behouden. Het combineert krachtige taalbegrip- en generatiecapaciteiten, geschikt voor grootschalige toepassingsscenario's, wa<PERSON><PERSON> klantenservice, onderwijs en technische ondersteuning."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o is een dynamisch model dat in realtime wordt bijgewerkt om de meest actuele versie te behouden. Het combineert krachtige taalbegrip- en generatiecapaciteiten, geschikt voor grootschalige toepassingsscenario's, wa<PERSON><PERSON> klantenservice, onderwijs en technische ondersteuning."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o is een dynamisch model dat in real-time wordt bijgewerkt om de meest actuele versie te behouden. Het combineert krachtige taalbegrip en generatiemogelijkheden, geschikt voor grootschalige toepassingen zoals klantenservice, onderwijs en technische ondersteuning."}, "gpt-4o-audio-preview": {"description": "GPT-4o Audio model, ondersteunt audio-invoer en -uitvoer."}, "gpt-4o-mini": {"description": "GPT-4o mini is het nieuwste model van OpenAI, gelanceerd na GPT-4 Omni, en ondersteunt zowel tekst- als bee<PERSON><PERSON><PERSON><PERSON> met teks<PERSON><PERSON><PERSON><PERSON>. Als hun meest geavanceerde kleine model is het veel goedkoper dan andere recente toonaangevende modellen en meer dan 60% goedkoper dan GPT-3.5 Turbo. Het behoudt de meest geavanceerde intelligentie met een aanzienlijke prijs-kwaliteitverhouding. GPT-4o mini behaalde 82% op de MMLU-test en staat momenteel hoger in chatvoorkeuren dan GPT-4."}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Audio model, ondersteunt audio-invoer en -uitvoer."}, "gpt-4o-mini-realtime-preview": {"description": "GPT-4o-mini realtime versie, ondersteunt audio en tekst realtime invoer en uitvoer."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini zoekpreview is een model dat speciaal is getraind om webzoekopdrachten te begrijpen en uit te voeren, gebruikmakend van de Chat Completions API. Naast tokenkosten worden webzoekopdrachten ook per toolaanroep in rekening gebracht."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe is een spraak-naar-tekstmodel dat GPT-4o gebruikt voor audiotranscriptie. <PERSON><PERSON><PERSON><PERSON><PERSON> met het originele Whisper-model verbetert het de woordfoutpercentage en verhoogt het de taalherkenning en nauwkeurigheid. Gebruik het voor nauwkeurigere transcripties."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS is een tekst-naar-spraak model dat is gebaseerd op GPT-4o mini, en biedt hoge kwaliteit spraakgeneratie tegen een lagere prijs."}, "gpt-4o-realtime-preview": {"description": "GPT-4o realtime versie, ondersteunt audio en tekst realtime invoer en uitvoer."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "GPT-4o realtime versie, ondersteunt audio en tekst realtime invoer en uitvoer."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "GPT-4o realtime versie, ondersteunt realtime audio- en tekstinvoer en -uitvoer."}, "gpt-4o-search-preview": {"description": "GPT-4o zoekpreview is een model dat speciaal is getraind om webzoekopdrachten te begrijpen en uit te voeren, gebruikmakend van de Chat Completions API. Naast tokenkosten worden webzoekopdrachten ook per toolaanroep in rekening gebracht."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe is een spraak-naar-tekstmodel dat GPT-4o gebruikt voor audiotranscriptie. <PERSON><PERSON><PERSON><PERSON><PERSON> met het originele Whisper-model verbetert het de woordfoutpercentage en verhoogt het de taalherkenning en nauwkeurigheid. Gebruik het voor nauwkeurigere transcripties."}, "gpt-image-1": {"description": "ChatGPT native multimodaal afbeeldingsgeneratiemodel"}, "grok-2-1212": {"description": "Dit model heeft verbeteringen aangebracht in nauwkeurigheid, instructievolging en meertalige capaciteiten."}, "grok-2-image-1212": {"description": "Ons nieuwste beeldgeneratiemodel kan levendige en realistische beelden genereren op basis van tekstprompts. Het presteert uitstekend in beeldgeneratie voor marketing, sociale media en entertainment."}, "grok-2-vision-1212": {"description": "Dit model heeft verbeteringen aangebracht in nauwkeurigheid, instructievolging en meertalige capaciteiten."}, "grok-3": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON> in data-extractie, programmeren en tekstsamenvatting voor bedrijfsapplicaties, met diepgaande kennis in financiën, gezondheidszorg, recht en wetenschap."}, "grok-3-fast": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON> in data-extractie, programmeren en tekstsamenvatting voor bedrijfsapplicaties, met diepgaande kennis in financiën, gezondheidszorg, recht en wetenschap."}, "grok-3-mini": {"description": "Lichtgewicht model dat eerst nadenkt voor het reageren. Snel en intelligent, geschikt voor logische taken zonder diepgaande domeinkennis en kan de oorspronkelijke denkprocessen vastleggen."}, "grok-3-mini-fast": {"description": "Lichtgewicht model dat eerst nadenkt voor het reageren. Snel en intelligent, geschikt voor logische taken zonder diepgaande domeinkennis en kan de oorspronkelijke denkprocessen vastleggen."}, "grok-4": {"description": "Ons nieuwste en krachtigste vlaggenschipmodel, uitmuntend in natuurlijke taalverwerking, wiskundige berekeningen en redeneren — een perfecte allrounder."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B is een taalmodel dat creativiteit en intelligentie combineert door meerdere topmodellen te integreren."}, "hunyuan-a13b": {"description": "Hunyuan's eerste hybride redeneermodel, een upgrade van hun hunyuan-standard-256K, met in totaal 80 miljard parameters en 13 miljard geactiveerde parameters. Standaard werkt het in een langzame denkwijze, maar ondersteunt schakeling tussen snelle en langzame denkwijzen via parameters of instructies, waarbij de snelle/langzame wisseling wordt geactiveerd door het toevoegen van 'query' of 'no_think' vooraf. De algehele capaciteiten zijn aanzienlijk verbeterd ten opzichte van de vorige generatie, vooral op het gebied van wiskunde, wetenschap, lange tekstbegrip en agentfunctionaliteit."}, "hunyuan-code": {"description": "Het nieuwste codegeneratiemo<PERSON> van Hu<PERSON>, getraind op 200B hoogwaardige codegegevens, met een half jaar training op hoogwaardige SFT-gegevens, met een vergroot contextvenster van 8K, en staat bovenaan de automatische evaluatie-indicatoren voor codegeneratie in vijf grote programmeertalen; presteert in de eerste divisie op basis van handmatige kwaliteitsbeoordelingen van 10 aspecten van code-taken in vijf grote talen."}, "hunyuan-functioncall": {"description": "<PERSON>t nieuwste MOE-architectuur FunctionCall-model <PERSON>, getraind op hoogwaardige FunctionCall-gegevens, met een <PERSON><PERSON><PERSON> van 32K, en staat voorop in meerdere dimensies van evaluatie-indicatoren."}, "hunyuan-large": {"description": "Het Hunyuan-large model heeft een totaal aantal parameters van ongeveer 389B, met ongeveer 52B actieve parameters, en is het grootste en beste open-source MoE-model met Transformer-architectuur in de industrie."}, "hunyuan-large-longcontext": {"description": "Uitstekend in het verwerken van lange teksttaken zoals document samenvattingen en documentvragen, en heeft ook de capaciteit om algemene tekstgeneratietaken uit te voeren. Het presteert uitstekend in de analyse en generatie van lange teksten en kan effectief omgaan met complexe en gedetailleerde lange inhoudsverwerkingsbehoeften."}, "hunyuan-large-vision": {"description": "Dit model is ges<PERSON><PERSON> voor scenario's met beeld- en tekstbegrip. Het is een visueel-taal groot model geb<PERSON><PERSON> op Hunyuan Large training, ondersteunt meerdere afbeeldingen met willekeurige resoluties plus te<PERSON><PERSON><PERSON><PERSON>, genereert tekstinhoud en richt zich op taken gerelateerd aan beeld-tekstbegrip, met significante verbeteringen in meertalige beeld-tekstbegrip."}, "hunyuan-lite": {"description": "Geüpgraded naar een MOE-structuur, met een <PERSON><PERSON><PERSON>, en leidt in verschillende evaluatiesets op het gebied van NLP, code, wiskunde en industrie ten opzichte van vele open-source modellen."}, "hunyuan-lite-vision": {"description": "De nieuwste 7B multimodale Hunyuan-model, met een <PERSON><PERSON><PERSON> van 32<PERSON>, ondersteunt multimodale gesprekken in het Chinees en het Engels, objectherkenning in afbeeldingen, document- en tabelbegrip, multimodale wiskunde, enz., en scoort op meerdere dimensies beter dan 7B concurrentiemodellen."}, "hunyuan-pro": {"description": "Een MOE-32K lange tekstmodel met triljoenen parameters. Bereikt een absoluut leidend niveau op verschillende benchmarks, met complexe instructies en redenering, beschikt over complexe wiskundige capaciteiten, ondersteunt function calls, en is geoptimaliseerd voor toepassingen in meertalige vertaling, financiële, juridische en medische gebieden."}, "hunyuan-role": {"description": "Het nieuwste rolspelmodel van Hunyuan, een rolspelmodel dat is ontwikkeld door de officiële fine-tuning training van Hunyuan, dat is getraind op basis van rolspel-scenario datasets, en betere basisprestaties biedt in rolspel-scenario's."}, "hunyuan-standard": {"description": "Maakt gebruik van een betere routeringsstrategie en verlicht tegelijkertijd de problemen van load balancing en expert convergentie. Voor lange teksten bereikt de naald in een hooiberg-index 99,9%. MOE-32K biedt een relatief betere prijs-kwaliteitverhouding, en kan lange tekstinvoer verwerken terwijl het effect en prijs in balans houdt."}, "hunyuan-standard-256K": {"description": "Maakt gebruik van een betere routeringsstrategie en verlicht tegelijkertijd de problemen van load balancing en expert convergentie. Voor lange teksten bereikt de naald in een hooiberg-index 99,9%. MOE-256K doorbreekt verder in lengte en effectiviteit, waardoor de invoerlengte aanzienlijk wordt vergroot."}, "hunyuan-standard-vision": {"description": "De nieuwste multimodale Hunyuan-model, ondersteunt meertalige antwoorden, met <PERSON><PERSON><PERSON><PERSON> capaciteiten in het Chinees en het Engels."}, "hunyuan-t1-20250321": {"description": "Biedt een uitgebreide opbouw van modelcapaciteiten in zowel exacte als sociale wetenschappen, met sterke mogelijkheden voor het vastleggen van lange tekstinformatie. Ondersteunt redenering en antwoorden op wetenschappelijke vragen van verschillende moeilijkheidsgraden, zoals wiskunde/logica/wetenschap/code."}, "hunyuan-t1-20250403": {"description": "Verbeter de codegeneratie op projectniveau; verhoog de kwaliteit van tekstgeneratie en schrijfvaardigheid; verbeter het begrip van tekstonderwerpen, multi-turn en to-the-point instructies en woordbegrip; optimaliseer problemen met gemengde traditionele en vereenvoudigde karakters en gemengde Chinese en Engelse output."}, "hunyuan-t1-20250529": {"description": "Geoptimalis<PERSON>d voor tekstcreatie en essay schrij<PERSON>, verbeterde vaardigheden in frontend codering, wiskunde en logisch redeneren, en verbeterde instructievolging."}, "hunyuan-t1-20250711": {"description": "Significante verbetering van geavanceerde wiskundige, logische en codeervaardigheden, optimalisatie van modeloutputstabiliteit en verbetering van lange-tekstcapaciteiten."}, "hunyuan-t1-latest": {"description": "De eerste ultra-grote Hybrid-Transformer-Mamba inferentiemodel in de industrie, dat de inferentiemogelijkheden uitbreidt, met een superieure decodesnelheid en verder afgestemd op menselijke voorkeuren."}, "hunyuan-t1-vision": {"description": "Hunyuan multimodaal begrip en diepdenkend model, ondersteunt native multimodale lange-denk-ketens, excelleert in diverse beeldredeneerscenario's en verbetert aanzienlijk ten opzichte van snelle denkers bij exacte wetenschappen."}, "hunyuan-t1-vision-20250619": {"description": "De nieuwste versie <PERSON>'s t1-vision multimodale diepdenkende model, ondersteunt native lange chain-of-thought in multimodale contexten en biedt een algehele verbetering ten opzichte van de vorige standaardversie."}, "hunyuan-turbo": {"description": "<PERSON><PERSON> <PERSON><PERSON><PERSON> van het nieuwe generatie grote taalmodel van Hu<PERSON>uan, met een nieuwe hybride expertmodel (MoE) structuur, die sneller inferentie-efficiëntie biedt en betere prestaties levert dan hunyan-pro."}, "hunyuan-turbo-20241223": {"description": "Deze versie optimaliseert: gege<PERSON><PERSON>st<PERSON><PERSON><PERSON><PERSON><PERSON>, wat de algemene generalisatiecapaciteit van het model aanzienlijk verbetert; aanzienlijke verbetering van wiskunde-, code- en logische redeneervaardigheden; optimalisatie van tekstbegrip en woordbegrip gerelateerde capaciteiten; optimalisatie van de kwaliteit van tekstcreatie en inhoudsgeneratie."}, "hunyuan-turbo-latest": {"description": "Algemene ervaring optimalisatie, inclusief NLP-begrip, tekst<PERSON>atie, casual gesprekken, kennisvragen, vertalingen, en domeinspecifieke toepassingen; verbetering van de <PERSON>, optimalisatie van de emotionele intelligentie van het model; verbetering van het vermogen van het model om actief te verduidelijken bij vage intenties; verbetering van de verwerking van vragen over woord- en zinsanalyse; verbetering van de kwaliteit en interactie van creaties; verbetering van de ervaring in meerdere rondes."}, "hunyuan-turbo-vision": {"description": "De nieuwe generatie visuele taal vlaggens<PERSON><PERSON><PERSON><PERSON>, met een geheel nieuwe hybride expertmodel (MoE) structuur, biedt aanzienlijke verbeteringen in basisherkenning, inhoudcreatie, kennisvragen, en analytische redeneervaardigheden in vergelijking met de vorige generatie modellen."}, "hunyuan-turbos-20250313": {"description": "Uniformeer de stijl van wiskundige oplossingsstappen en versterk multi-turn wiskundige vraag-en-antwoord sessies. Optimaliseer de schrijfstij<PERSON> van tekstcreatie, verwijder AI-achtige kenmerken en voeg literaire flair toe."}, "hunyuan-turbos-20250416": {"description": "Upgrade van het pre-trainingsfundament, versterkt het begrip en de naleving van instructies; verbetert wiskundige, codeer-, logische en wetenschappelijke vaardigheden tijdens de afstemmingsfase; verhoogt de kwaliteit van creatieve teksten, tekstbegrip, vertaalnauwkeurigheid en kennisvragen; versterkt de capaciteiten van agenten in diverse domeinen, met speciale aandacht voor het begrip van multi-turn dialogen."}, "hunyuan-turbos-20250604": {"description": "Upgrade van de pre-training<PERSON><PERSON><PERSON>, verbeterde schrijf- en leesbegripvaardigheden, aanzienlijke verbetering van codeer- en wetenschappelijke vaardigheden, en voortdurende verbetering in het volgen van complexe instructies."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS is de nieuwste versie van het Hunyuan vlaggenschipmodel, met verbeterde denkcapaciteiten en een betere gebruikerservaring."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Uitstekend in het verwerken van lange teksttaken zoals documentsamenvattingen en documentvragen, en heeft ook de capaciteit om algemene tekstgeneratietaken uit te voeren. Het presteert uitstekend in de analyse en generatie van lange teksten en kan effectief omgaan met complexe en gedetailleerde lange inhoud."}, "hunyuan-turbos-role-plus": {"description": "De nieuwste versie van het Hunyuan rollenspelmodel, officieel fijngetuned door Hunyuan, getraind met datasets voor rollenspelscenario's, biedt betere basisprestaties in rollenspelsituaties."}, "hunyuan-turbos-vision": {"description": "Dit model is geschikt voor beeld- en tekstbegripsscenario's en is gebase<PERSON> op Hunyuan's nieuwste turbos, een nieuwe generatie vlaggenschip visueel-taalmodel dat zich richt op taken zoals entiteitsherkenning op basis van afbeeldingen, kennisvraag-antwoordsystemen, copywriting en foto-gebaseerde probleemoplossing. Het biedt een algehele verbetering ten opzichte van de vorige generatie modellen."}, "hunyuan-turbos-vision-20250619": {"description": "De nieuwste versie van <PERSON>'s turbos-vision vlaggenschi<PERSON> visueel-taalmodel, met al<PERSON>hel<PERSON> verbeter<PERSON> in taken gerelateerd aan beeld- en tekstbegrip, waaronder entiteitsherkenning op basis van afbeeldingen, kennisvraag-antwoordsystemen, copywriting en foto-gebaseerde probleemoplossing, verge<PERSON><PERSON> met de vorige standaardversie."}, "hunyuan-vision": {"description": "Het nieuwste multimodale model van <PERSON>, ondersteunt het genereren van tekstinhoud op basis van afbeelding + tekstinvoer."}, "image-01": {"description": "<PERSON><PERSON> nieuw beeldgenerat<PERSON><PERSON><PERSON> met fijne beeldweergave, ondersteunt tekst-naar-beeld en beeld-naar-beeld."}, "image-01-live": {"description": "<PERSON><PERSON>generat<PERSON><PERSON><PERSON> met fijne bee<PERSON><PERSON><PERSON><PERSON><PERSON>, ondersteunt tekst-naar-beeld en stijlinstellingen."}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 4e generatie tekst-naar-beeld modelserie"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 4e generatie tekst-naar-beeld modelserie Ultra versie"}, "imagen4/preview": {"description": "Google's hoogste kwaliteit afbeeldingsgeneratiemodel"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 biedt intelligente gespreksoplossingen voor meerdere scenario's."}, "internlm2.5-latest": {"description": "Onze nieuwste modelreeks met uitstekende redeneervaardigheden, ondersteunt een contextlengte van 1M en heeft verbeterde instructievolging en toolaanroepmogelijkheden."}, "internlm3-latest": {"description": "Onze nieuwste modelreeks heeft uitstekende inferentieprestaties en leidt de open-source modellen in dezelfde klasse. Standaard gericht op ons recentste InternLM3 model."}, "internvl2.5-latest": {"description": "De InternVL2.5 versie die we nog steeds onderhouden, biedt uitstekende en stabiele prestaties. Standaard gericht op ons recentste InternVL2.5-se<PERSON><PERSON><PERSON>, momenteel gericht op internvl2.5-78b."}, "internvl3-latest": {"description": "Ons nieuwste multimodale grote model, met verbeterde beeld- en tekstbegripcapaciteiten en lange termijn beeldbegrip, presteert op het niveau van toonaangevende gesloten modellen. Standaard gericht op ons recentste InternVL-seriemodel, momenteel gericht op internvl3-78b."}, "irag-1.0": {"description": "Baidu's zelfontwikkelde iRAG (image based RAG) is een doorzoekversterkte tekst-naar-beeldtechnologie die Baidu's miljarden afbeeldingen combineert met krachtige basismodelcapaciteiten om diverse ultra-realistische beelden te genereren. Het overtreft native tekst-naar-beeldsystemen aanzienlijk, zonder AI-achtige uitstraling en met lage kosten. iRAG kenmerkt zich door geen hallucinaties, ultra-realistische beelden en directe beschikbaarheid."}, "jamba-large": {"description": "Ons krachtigste en meest geavanceerde model, speciaal ontworpen voor het verwerken van complexe taken op bedrijfsniveau, met uitstekende prestaties."}, "jamba-mini": {"description": "Het meest efficiënte model in zijn klasse, met een balans tussen snelheid en kwaliteit, en een compact formaat."}, "jina-deepsearch-v1": {"description": "Diepe zoekopdrachten combineren webzoekopdrachten, lezen en redeneren voor een uitgebreide verkenning. Je kunt het beschouwen als een agent die jouw onderzoeksopdracht aanneemt - het zal een uitgebreide zoektocht uitvoeren en meerdere iteraties doorlopen voordat het een antwoord geeft. Dit proces omvat voortdurende onderzoek, redeneren en het oplossen van problemen vanuit verschillende invalshoeken. Dit is fundamenteel anders dan het rechtstreeks genereren van antwoorden uit voorgetrainde gegevens door standaard grote modellen en het vertrouwen op eenmalige oppervlakkige zoekopdrachten van traditionele RAG-systemen."}, "kimi-k2": {"description": "Kimi-K2 is een <PERSON>-architectuurbasis model met krachtige codeer- en agentcapaciteiten, uitgebracht door Moonshot AI, met in totaal 1 biljoen parameters en 32 miljard geactiveerde parameters. In benchmarktests voor algemene kennisredenering, programmer<PERSON>, wiskunde en agenttaken overtreft het K2-model andere toonaangevende open-source modellen."}, "kimi-k2-0711-preview": {"description": "kimi-k2 is een MoE-architectuurbasis model met krachtige codeer- en agentcapaciteiten, met in totaal 1 biljoen parameters en 32 miljard geactiveerde parameters. In benchmarktests voor algemene kennisredenering, programmeren, wiskunde en agenttaken overtreft het K2-model andere toonaangevende open-source modellen."}, "kimi-latest": {"description": "Kimi slimme assistent product maakt gebruik van het nieuwste <PERSON><PERSON> grote model, dat mogelijk nog niet stabiele functies bevat. Ondersteunt beeldbegrip en kiest automatisch het 8k/32k/128k model als factureringsmodel op basis van de lengte van de context van het verzoek."}, "kimi-thinking-preview": {"description": "kimi-thinking-preview model is een multimodaal denkmodel met multimodale en algemene redeneervaardigheden, aangeboden door de donkere zijde van de maan. Het blinkt uit in diep redeneren en helpt bij het oplossen van complexere problemen."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM is een experimenteel, taak-specifiek taalmodel dat is getraind volgens de principes van de leerwetenschap, en kan systeeminstructies volgen in onderwijs- en leeromgevingen, en fungeert als een expertmentor."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM is een experimenteel, taakger<PERSON>t taalmodel, getraind om te voldoen aan de principes van leerwetenschap, kan systeeminstructies volgen in onderwijs- en leerscenario's, en fungeert als een expertmentor."}, "lite": {"description": "Spark Lite is een lichtgewicht groot taalmodel met extreem lage latentie en efficiënte verwerkingscapaciteit. Het is volledig gratis en open, en ondersteunt realtime online zoekfunctionaliteit. De snelle respons maakt het uitermate geschikt voor inferentie op apparaten met lage rekenkracht en modelafstemming, wat gebruikers uitstekende kosteneffectiviteit en een slimme ervaring biedt, vooral in kennisvragen, contentgeneratie en zoekscenario's."}, "llama-2-7b-chat": {"description": "Llama2 is een reeks grote taalmodellen (LLM's) ontwikkeld en open-gebruik gemaakt door Meta. Deze reeks omvat generatieve tekstmodellen met verschillende groottes, vari<PERSON><PERSON><PERSON> van 7 miljard tot 70 miljard parameters, die zijn voorgetraind en fijn afgesteld. Op architectuurniveau is Llama2 een automatisch regressief taalmodel dat gebruik maakt van een geoptimaliseerde transformer-architectuur. Aangepaste versies maken gebruik van toezichtsfijnafstelling (SFT) en versterkingsleren met menselijke feedback (RLHF) om de voorkeuren van mensen met betrekking tot nuttigheid en veiligheid te aligneren. Llama2 presteert opmerkelijk goed op verschillende academische datasets en biedt inspiratie voor de ontwerp- en ontwikkeling van veel andere modellen."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B biedt krachtigere AI-inferentiecapaciteiten, geschikt voor complexe toepassingen, ondersteunt een enorme rekenverwerking en garandeert efficiëntie en nauwkeurigheid."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B is een hoogpresterend model dat snelle tekstgeneratiecapaciteiten biedt, zeer geschikt voor toepassingen die grootschalige efficiëntie en kosteneffectiviteit vereisen."}, "llama-3.1-instruct": {"description": "Llama 3.1 instructie-fijnafstemmodel is geoptimaliseerd voor gesprekssituaties en overtreft vele bestaande open-source chatmodellen op veelvoorkomende branchebenchmarks."}, "llama-3.2-11b-vision-instruct": {"description": "Uitstekende beeldredeneringscapaciteiten op hoge resolutie-afbeeldingen, geschikt voor visuele begrijptoepassingen."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 is ontworpen om taken te verwerken die visuele en tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraag-en-antwoord, en overbrugt de kloof tussen taalgeneratie en visuele redeneervaardigheden."}, "llama-3.2-90b-vision-instruct": {"description": "Geavanceerde beeldredeneringscapaciteiten voor visuele begrijppoort toepassingen."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 is ontworpen om taken te verwerken die visuele en tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraag-en-antwoord, en overbrugt de kloof tussen taalgeneratie en visuele redeneervaardigheden."}, "llama-3.2-vision-instruct": {"description": "Llama 3.2-Vision instructie-fijnafstemmodel is geoptimaliseerd voor visuele herkenning, afbeeldingsredenering, afbeeldingsbeschrijving en het beantwoorden van algemene vragen over afbeeldingen."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 is het meest geavanceerde meertalige open-source grote taalmodel in de Llama-serie, dat prestaties biedt die vergelijkbaar zijn met die van het 405B-model tegen zeer lage kosten. Gebaseerd op de Transformer-structuur en verbeterd door middel van supervisie-fijnstelling (SFT) en versterkend leren met menselijke feedback (RLHF) voor gebruiksvriendelijkheid en veiligheid. De instructie-geoptimaliseerde versie is speciaal ontworpen voor meertalige dialogen en presteert beter dan veel open-source en gesloten chatmodellen op verschillende industriële benchmarks. Kennisafkapdatum is december 2023."}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 is een meertalige grote taalmodel (LLM) met 70B (tekstinvoer/tekstuitvoer) dat is voorgetraind en aangepast voor instructies. Het pure tekstmodel van Llama 3.3 is geoptimaliseerd voor meertalige gespreksgebruik en scoort beter dan veel beschikbare open-source en gesloten chatmodellen op gangbare industrie benchmarks."}, "llama-3.3-instruct": {"description": "Het Llama 3.3 instructie-fijnafstemmodel is geoptimaliseerd voor gesprekssituaties en overtreft vele bestaande open-source chatmodellen op veelvoorkomende industriebenchmarks."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B biedt ongeëvenaarde complexiteitsverwerkingscapaciteiten, op maat gemaakt voor veeleisende projecten."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B biedt hoogwaardige inferentieprestaties, geschikt voor diverse toepassingsbehoeften."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use biedt krachtige tool-aanroepcapaciteiten en ondersteunt efficiënte verwerking van complexe taken."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use is een model dat is geoptimaliseerd voor efficiënt gebruik van tools, ondersteunt snelle parallelle berekeningen."}, "llama3.1": {"description": "Llama 3.1 is een too<PERSON><PERSON> model van <PERSON>, ondersteunt tot 405B parameters en kan worden toegepast in complexe gesprekken, meertalige vertalingen en data-analyse."}, "llama3.1:405b": {"description": "Llama 3.1 is een too<PERSON><PERSON> model van <PERSON>, ondersteunt tot 405B parameters en kan worden toegepast in complexe gesprekken, meertalige vertalingen en data-analyse."}, "llama3.1:70b": {"description": "Llama 3.1 is een too<PERSON><PERSON> model van <PERSON>, ondersteunt tot 405B parameters en kan worden toegepast in complexe gesprekken, meertalige vertalingen en data-analyse."}, "llava": {"description": "LLaVA is een multimodaal model dat visuele encoder en Vicuna combineert, voor krachtige visuele en taalbegrip."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B biedt visuele verwerkingscapaciteiten, genereert complexe output via visuele informatie-invoer."}, "llava:13b": {"description": "LLaVA is een multimodaal model dat visuele encoder en Vicuna combineert, voor krachtige visuele en taalbegrip."}, "llava:34b": {"description": "LLaVA is een multimodaal model dat visuele encoder en Vicuna combineert, voor krachtige visuele en taalbegrip."}, "mathstral": {"description": "MathΣtral is ontworpen voor wetenschappelijk onderzoek en wiskundige inferentie, biedt effectieve rekencapaciteiten en resultaatinterpretatie."}, "max-32k": {"description": "Spark Max 32K is uitgerust met een grote contextverwerkingscapaciteit, met verbeterd begrip van context en logische redeneervaardigheden. Het ondersteunt tekstinvoer van 32K tokens en is geschikt voor het lezen van lange documenten, privé kennisvragen en andere scenario's."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct is een grote taalmodel dat volledig zelf is getraind door Wúwèn Xīnqióng. Megrez-3B-Instruct is ontworpen om middels de concepten van zowel hardware als software, een oplossing te bieden die snelle inferentie, compact en krachtig is, en gemakkelijk in gebruik is voor edge-applicaties."}, "meta-llama-3-70b-instruct": {"description": "<PERSON><PERSON> krachtig model met 70 miljard parameters dat uitblinkt in redeneren, coderen en brede taaltoepassingen."}, "meta-llama-3-8b-instruct": {"description": "<PERSON><PERSON> veelzijdig model met 8 miljard parameters, geoptimaliseerd voor dialoog- en tekstgeneratietaken."}, "meta-llama-3.1-405b-instruct": {"description": "De Llama 3.1 instructie-geoptimaliseerde tekstmodellen zijn geoptimaliseerd voor meertalige dialoogtoepassingen en presteren beter dan veel beschikbare open source en gesloten chatmodellen op gangbare industriële benchmarks."}, "meta-llama-3.1-70b-instruct": {"description": "De Llama 3.1 instructie-geoptimaliseerde tekstmodellen zijn geoptimaliseerd voor meertalige dialoogtoepassingen en presteren beter dan veel beschikbare open source en gesloten chatmodellen op gangbare industriële benchmarks."}, "meta-llama-3.1-8b-instruct": {"description": "De Llama 3.1 instructie-geoptimaliseerde tekstmodellen zijn geoptimaliseerd voor meertalige dialoogtoepassingen en presteren beter dan veel beschikbare open source en gesloten chatmodellen op gangbare industriële benchmarks."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) biedt uitstekende taalverwerkingscapaciteiten en een geweldige interactie-ervaring."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 biedt uitstekende taalverwerkingscapaciteiten en een geweldige interactieve ervaring."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) is een krachtig chatmodel dat complexe gespreksbehoeften ondersteunt."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) biedt meertalige ondersteuning en dekt een breed scala aan domeinkennis."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 is ontworpen voor taken die zowel visuele als tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraagstukken, en overbrugt de kloof tussen taalgeneratie en visuele redenering."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 is ontworpen voor taken die zowel visuele als tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraagstukken, en overbrugt de kloof tussen taalgeneratie en visuele redenering."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 is ontworpen voor taken die zowel visuele als tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraagstukken, en overbrugt de kloof tussen taalgeneratie en visuele redenering."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 meertalige grote taalmodel (LLM) is een voorgetraind en instructie-aangepast generatief model van 70B (tekstinvoer/tekstuitvoer). Het Llama 3.3 instructie-aangepaste pure tekstmodel is geoptimaliseerd voor meertalige dialoogtoepassingen en presteert beter dan veel beschikbare open-source en gesloten chatmodellen op gangbare industriële benchmarks."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 is ontworpen voor taken die zowel visuele als tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraagstukken, en overbrugt de kloof tussen taalgeneratie en visuele redenering."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite is geschikt voor omgevingen die hoge prestaties en lage latentie vereisen."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo biedt uitstekende taalbegrip en generatiecapaciteiten, geschikt voor de meest veeleisende rekenkundige taken."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite is geschikt voor omgevingen met beperkte middelen en biedt een uitstekende balans in prestaties."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo is een krachtige grote taalmodel, geschikt voor een breed scala aan toepassingsscenario's."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B is een krachtig model voor voortraining en instructiefijnafstemming."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405B Llama 3.1 Turbo model biedt enorme contextondersteuning voor big data verwerking en presteert uitstekend in grootschalige AI-toepassingen."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 is een too<PERSON><PERSON> model van <PERSON>, ondersteunt tot 405B parameters en kan worden toegepast in complexe gesprekken, meertalige vertalingen en data-analyse."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70B model is fijn afgesteld voor toepassingen met hoge belasting, gekwantiseerd naar FP8 voor efficiëntere rekenkracht en nauwkeurigheid, en zorgt voor uitstekende prestaties in complexe scenario's."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8B model maakt gebruik van FP8-kwan<PERSON>ring en ondersteunt tot 131.072 contexttokens, en is een van de beste open-source modellen, geschikt voor complexe taken en presteert beter dan veel industriestandaarden."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct is geoptimaliseerd voor hoogwaardige gespreksscenario's en presteert uitstekend in verschillende menselijke evaluaties."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct is geoptimaliseerd voor hoogwaardige gespreksscenario's en presteert beter dan veel gesloten modellen."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct is ontworpen voor hoogwaardige gesprekken en presteert uitstekend in menselijke evaluaties, vooral in interactieve scenario's."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct is de nieuwste vers<PERSON> van <PERSON>, geoptimaliseerd voor hoogwaardige gespreksscenario's en presteert beter dan veel toonaangevende gesloten modellen."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 biedt ondersteuning voor meerdere talen en is een van de toonaangevende generatiemodellen in de industrie."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 is ontworpen voor taken die visuele en tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraag-en-antwoord, en overbrugt de kloof tussen taalgeneratie en visuele redenering."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 is ontworpen voor taken die visuele en tekstuele gegevens combineren. Het presteert uitstekend in taken zoals afbeeldingsbeschrijving en visuele vraag-en-antwoord, en overbrugt de kloof tussen taalgeneratie en visuele redenering."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 is het meest geavanceerde meertalige open-source grote taalmodel in de Llama-serie, dat prestaties biedt die vergelijkbaar zijn met die van het 405B-model tegen zeer lage kosten. Gebaseerd op de Transformer-structuur en verbeterd door middel van supervisie-fijnstelling (SFT) en versterkend leren met menselijke feedback (RLHF) voor gebruiksvriendelijkheid en veiligheid. De instructie-geoptimaliseerde versie is speciaal ontworpen voor meertalige dialogen en presteert beter dan veel open-source en gesloten chatmodellen op verschillende industriële benchmarks. Kennisafkapdatum is december 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 is het meest geavanceerde meertalige open-source grote taalmodel in de Llama-serie, dat prestaties biedt die vergelijkbaar zijn met die van het 405B-model tegen zeer lage kosten. Gebaseerd op de Transformer-structuur en verbeterd door middel van supervisie-fijnstelling (SFT) en versterkend leren met menselijke feedback (RLHF) voor gebruiksvriendelijkheid en veiligheid. De instructie-geoptimaliseerde versie is speciaal ontworpen voor meertalige dialogen en presteert beter dan veel open-source en gesloten chatmodellen op verschillende industriële benchmarks. Kennisafkapdatum is december 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct is het grootste en krachtigste model binnen het Llama 3.1 Instruct-model, een geavanceerd model voor conversatie-inferentie en synthetische datageneratie, dat ook kan worden gebruikt als basis voor gespecialiseerde continue pre-training of fine-tuning in specifieke domeinen. De meertalige grote taalmodellen (LLMs) die Llama 3.1 biedt, zijn een set van voorgetrainde, instructie-geoptimaliseerde generatieve modellen, waaronder 8B, 70B en 405B in grootte (tekstinvoer/uitvoer). De tekstmodellen van Llama 3.1, die zijn geoptimaliseerd voor meertalige conversatiegebruik, overtreffen veel beschikbare open-source chatmodellen in gangbare industriële benchmarktests. Llama 3.1 is ontworpen voor commercieel en onderzoeksgebruik in meerdere talen. De instructie-geoptimaliseerde tekstmodellen zijn geschikt voor assistentachtige chats, terwijl de voorgetrainde modellen zich kunnen aanpassen aan verschillende taken voor natuurlijke taalgeneratie. Het Llama 3.1-model ondersteunt ook het verbeteren van andere modellen door gebruik te maken van de output van zijn modellen, inclusief synthetische datageneratie en verfijning. Llama 3.1 is een autoregressief taalmodel dat gebruikmaakt van een geoptimaliseerde transformer-architectuur. De afgestelde versies gebruiken supervisie-finetuning (SFT) en versterkend leren met menselijke feedback (RLHF) om te voldoen aan menselijke voorkeuren voor behulpzaamheid en veiligheid."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "De bijgewerkte versie van Meta Llama 3.1 70B Instruct, met een uitgebreid contextlengte van 128K, meertaligheid en verbeterde redeneercapaciteiten. De meertalige grote taalmodellen (LLMs) die door Llama 3.1 worden aangeboden, zijn een set voorgetrainde, instructie-aangepaste generatieve modellen, inclusief 8B, 70B en 405B in grootte (tekstinvoer/uitvoer). De instructie-aangepaste tekstmodellen (8B, 70B, 405B) zijn geoptimaliseerd voor meertalige dialoogtoepassingen en hebben veel beschikbare open-source chatmodellen overtroffen in gangbare industriële benchmarktests. Llama 3.1 is bedoeld voor commerciële en onderzoeksdoeleinden in meerdere talen. De instructie-aangepaste tekstmodellen zijn geschikt voor assistentachtige chats, terwijl de voorgetrainde modellen kunnen worden aangepast voor verschillende natuurlijke taalgeneratietaken. Llama 3.1-modellen ondersteunen ook het gebruik van hun output om andere modellen te verbeteren, inclusief synthetische gegevensgeneratie en verfijning. Llama 3.1 is een autoregressief taalmodel dat gebruikmaakt van een geoptimaliseerde transformerarchitectuur. De aangepaste versies maken gebruik van supervisie-fijnstelling (SFT) en versterkend leren met menselijke feedback (RLHF) om te voldoen aan menselijke voorkeuren voor behulpzaamheid en veiligheid."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "De bijgewerkte versie van Meta Llama 3.1 8B Instruct, met een uitgebreid contextlengte van 128K, meertaligheid en verbeterde redeneercapaciteiten. De meertalige grote taalmodellen (LLMs) die door Llama 3.1 worden aangeboden, zijn een set voorgetrainde, instructie-aangepaste generatieve modellen, inclusief 8B, 70B en 405B in grootte (tekstinvoer/uitvoer). De instructie-aangepaste tekstmodellen (8B, 70B, 405B) zijn geoptimaliseerd voor meertalige dialoogtoepassingen en hebben veel beschikbare open-source chatmodellen overtroffen in gangbare industriële benchmarktests. Llama 3.1 is bedoeld voor commerciële en onderzoeksdoeleinden in meerdere talen. De instructie-aangepaste tekstmodellen zijn geschikt voor assistentachtige chats, terwijl de voorgetrainde modellen kunnen worden aangepast voor verschillende natuurlijke taalgeneratietaken. Llama 3.1-modellen ondersteunen ook het gebruik van hun output om andere modellen te verbeteren, inclusief synthetische gegevensgeneratie en verfijning. Llama 3.1 is een autoregressief taalmodel dat gebruikmaakt van een geoptimaliseerde transformerarchitectuur. De aangepaste versies maken gebruik van supervisie-fijnstelling (SFT) en versterkend leren met menselijke feedback (RLHF) om te voldoen aan menselijke voorkeuren voor behulpzaamheid en veiligheid."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 is een open groot taalmodel (LLM) gericht op ontwikkelaars, onderzoekers en bedrijven, ontworpen om hen te helpen bij het bouwen, experimenteren en verantwoordelijk opschalen van hun generatieve AI-ideeën. Als onderdeel van het basis systeem voor wereldwijde gemeenschapsinnovatie is het zeer geschikt voor contentcreatie, conversatie-AI, taalbegrip, R&D en zakelijke toepassingen."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 is een open groot taalmodel (LLM) gericht op ontwikkelaars, onderzoekers en bedrijven, ontworpen om hen te helpen bij het bouwen, experimenteren en verantwoordelijk opschalen van hun generatieve AI-ideeën. Als onderdeel van het basis systeem voor wereldwijde gemeenschapsinnovatie is het zeer geschikt voor apparaten met beperkte rekenkracht en middelen, edge-apparaten en snellere trainingstijden."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Uitstekende beeldredeneercapaciteiten op hoge resolutie afbeeldingen, geschikt voor visuele begripstoepassingen."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "G<PERSON><PERSON><PERSON><PERSON>eneercapaciteiten voor visuele begripagenttoepassingen."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 is het meest geavanceerde meertalige open-source grote taalmodel in de Llama-serie, biedt prestaties vergelijkbaar met een 405B model tegen zeer lage kosten. Gebaseerd op de Transformer-architectuur en verbeterd via supervised fine-tuning (SFT) en reinforcement learning met menselijke feedback (RLHF) voor bruikbaarheid en veiligheid. De instructie-geoptimaliseerde versie is geoptimaliseerd voor meertalige dialogen en presteert beter dan veel open-source en gesloten chatmodellen op diverse industriële benchmarks. Kennisafkapdatum is december 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "<PERSON><PERSON> krachtig model met 70 miljard parameters, uitmuntend in redeneren, coderen en brede taaltoepassingen."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "<PERSON><PERSON> veelzijdig model met 8 miljard parameters, geoptimaliseerd voor dialoog- en tekstgeneratietaken."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 tekstmodel met instructie-finetuning, geoptimaliseerd voor meertalige dialoogtoepassingen, presteert uitstekend op veelgebruikte industriële benchmarks vergeleken met beschikbare open-source en gesloten chatmodellen."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 tekstmodel met instructie-finetuning, geoptimaliseerd voor meertalige dialoogtoepassingen, presteert uitstekend op veelgebruikte industriële benchmarks vergeleken met beschikbare open-source en gesloten chatmodellen."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 tekstmodel met instructie-finetuning, geoptimaliseerd voor meertalige dialoogtoepassingen, presteert uitstekend op veelgebruikte industriële benchmarks vergeleken met beschikbare open-source en gesloten chatmodellen."}, "meta/llama-3.1-405b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON>, ondersteunt synthetische gegevensgeneratie, kennisdistillatie en redeneren, geschikt voor chatbots, programmeren en specifieke domeintaken."}, "meta/llama-3.1-70b-instruct": {"description": "In staat om complexe gesprekken te ondersteunen, met uitstekende contextbegrip, redeneringsvaardigheden en tekstgeneratiecapaciteiten."}, "meta/llama-3.1-8b-instruct": {"description": "Gea<PERSON><PERSON>, state-of-the-art model met <PERSON><PERSON><PERSON><PERSON><PERSON>, uitstekende redeneringsvaardigheden en tekstgeneratiecapaciteiten."}, "meta/llama-3.2-11b-vision-instruct": {"description": "State-of-the-art visueel-taalmodel, gespecialiseerd in hoogwaardige redeneringen vanuit afbeeldingen."}, "meta/llama-3.2-1b-instruct": {"description": "<PERSON>ea<PERSON><PERSON>, state-of-the-art klein taalmodel met ta<PERSON><PERSON>g<PERSON>, uitstekende redeneringsvaardigheden en tekstgeneratiecapaciteiten."}, "meta/llama-3.2-3b-instruct": {"description": "<PERSON>ea<PERSON><PERSON>, state-of-the-art klein taalmodel met ta<PERSON><PERSON>g<PERSON>, uitstekende redeneringsvaardigheden en tekstgeneratiecapaciteiten."}, "meta/llama-3.2-90b-vision-instruct": {"description": "State-of-the-art visueel-taalmodel, gespecialiseerd in hoogwaardige redeneringen vanuit afbeeldingen."}, "meta/llama-3.3-70b-instruct": {"description": "Geavanceerd LLM, gespecialiseerd in redeneren, wiskunde, algemene kennis en functieaanroepen."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "<PERSON>tzelfde Phi-3-medium model, maar met een grote<PERSON>, geschikt voor RAG of weinig prompts."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Een model met 14 miljard parameters, kwalitatief beter dan Phi-3-mini, gericht op hoogwaardige, redeneerrijke data."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "<PERSON>tzelfde Phi-3-mini model, maar met een grote<PERSON>, geschikt voor RAG of weinig prompts."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Het kleinste lid van de Phi-3 familie, geoptimaliseerd voor kwaliteit en lage latentie."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Hetzelfde Phi-3-small model, maar met een grote<PERSON>, geschikt voor RAG of weinig prompts."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Een model met 7 miljard parameters, kwalitatief beter dan Phi-3-mini, gericht op hoogwaardige, redeneerrijke data."}, "microsoft/Phi-3.5-mini-instruct": {"description": "<PERSON>en bijgewerkte versie van het Phi-3-mini model."}, "microsoft/Phi-3.5-vision-instruct": {"description": "<PERSON><PERSON> bijgewerkte versie van het Phi-3-vision model."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 is een ta<PERSON>model van Microsoft AI dat uitblinkt in complexe gesprekken, meertaligheid, redenering en intelligente assistenttoepassingen."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B is het meest geavanceerde Wizard-model van Microsoft AI, met een uiterst competitieve prestatie."}, "minicpm-v": {"description": "MiniCPM-V is de nieuwe generatie multimodale grote modellen van OpenBMB, met uitstekende OCR-herkenning en multimodaal begrip, geschikt voor een breed scala aan toepassingsscenario's."}, "ministral-3b-latest": {"description": "Ministral 3B is het toonaangevende edge-model <PERSON>."}, "ministral-8b-latest": {"description": "Ministral 8B is een zeer kosteneffectief edge-model <PERSON>."}, "mistral": {"description": "<PERSON><PERSON><PERSON> is het 7B-model van Mistral AI, geschikt voor variabele taalverwerkingsbehoeften."}, "mistral-ai/Mistral-Large-2411": {"description": "<PERSON>t vlaggenschi<PERSON><PERSON><PERSON>, geschikt voor grootschalige redeneertaken of sterk gespecialiseerde complexe taken (zoals synthetische tekstgeneratie, codegeneratie, RAG of agenten)."}, "mistral-ai/Mistral-Nemo": {"description": "Mi<PERSON>l Nemo is een gea<PERSON><PERSON> ta<PERSON>model (LLM) met toonaangevende redeneercapaciteiten, wereldkennis en codeervaardigheden binnen zijn grootteklasse."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small is geschikt voor elke taalgebaseerde taak die hoge efficiëntie en lage latentie vereist."}, "mistral-large": {"description": "Mixtral Large is het v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dat de capaciteiten van codegeneratie, wiskunde en inferentie combineert, ondersteunt een <PERSON><PERSON> van 128<PERSON>."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 is een geavanceerd dicht grote taalmodel (LLM) met 123 miljard parameters, dat beschikt over state-of-the-art redenerings-, kennis- en coderingcapaciteiten."}, "mistral-large-latest": {"description": "Mistral Large is het vlaggenschipmodel, dat uitblinkt in meertalige taken, complexe inferentie en codegeneratie, ideaal voor high-end toepassingen."}, "mistral-medium-latest": {"description": "Mistral Medium 3 biedt geavanceerde prestaties tegen 8 keer de kosten en vereenvoudigt de implementatie voor bedrijven fundamenteel."}, "mistral-nemo": {"description": "Mistral Nemo is een 12B-model dat is ontwikkeld in samenwerking met Mistral AI en NVIDIA, biedt efficiënte prestaties."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 is een groot taalmodel (LLM) dat een instructie-finetuned versie is van Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small kan worden gebruikt voor elke taalkundige taak die hoge efficiëntie en lage latentie vereist."}, "mistral-small-latest": {"description": "Mistral Small is een kostene<PERSON>ve, snelle en betrouwbare optie voor gebruikscases zoals vertaling, samenvatting en sentimentanalyse."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct staat bekend om zijn hoge prestaties en is geschikt voor verschillende taalgerelateerde taken."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B is een model dat op aanvraag is fijn afgesteld om geoptimaliseerde antwoorden voor taken te bieden."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 biedt efficiënte rekenkracht en natuurlijke taalbegrip, geschikt voor een breed scala aan toepassingen."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B is een compact maar hoogwa<PERSON>ig model, dat goed presteert in batchverwerking en eenvoudige taken zoals classificatie en tekstgeneratie, met goede redeneringscapaciteiten."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) is een supergroot taalmodel dat extreem hoge verwerkingsbehoeften ondersteunt."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B is een voorgetraind spaarzaam mengexpertmodel, gebruikt voor algemene teksttaken."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B is een spaarzaam expert-model dat meerdere parameters gebruikt om de redeneringssnelheid te verhogen, ideaal voor meertalige en codegeneratietaken."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct is een hoogwaardig industrieel standaardmodel met snelheidoptimalisatie en ondersteuning voor lange contexten."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo is een model met 7,3 miljard parameters dat meertalige ondersteuning en hoge prestaties biedt."}, "mixtral": {"description": "Mixtral is het expertmodel <PERSON> Mi<PERSON>l AI, met open-source gewichten en biedt ondersteuning voor codegeneratie en taalbegrip."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B biedt hoge fouttolerantie en parallelle verwerkingscapaciteiten, geschikt voor complexe taken."}, "mixtral:8x22b": {"description": "Mixtral is het expertmodel <PERSON> Mi<PERSON>l AI, met open-source gewichten en biedt ondersteuning voor codegeneratie en taalbegrip."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K is een model met een superlange contextverwerkingscapaciteit, geschikt voor het genereren van zeer lange teksten, voldoet aan de behoeften van complexe generatietaken en kan tot 128.000 tokens verwerken, zeer geschikt voor onderzoek, academische en grote documentgeneratie."}, "moonshot-v1-128k-vision-preview": {"description": "Het <PERSON>i visuele model (inclusief moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, enz.) kan de inhoud van afbeeldingen begrijpen, inclusief afbeeldingstekst, kleuren en vormen van objecten."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K biedt een gemiddelde contextverwerkingscapaciteit, kan 32.768 tokens verwerken, bijzonder geschikt voor het genereren van verschillende lange documenten en complexe gesprekken, toegepast in contentcreatie, rapportgeneratie en conversatiesystemen."}, "moonshot-v1-32k-vision-preview": {"description": "Het <PERSON>i visuele model (inclusief moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, enz.) kan de inhoud van afbeeldingen begrijpen, inclusief afbeeldingstekst, kleuren en vormen van objecten."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K is speciaal ontworpen voor het genereren van korte teksttaken, met efficiënte verwerkingsprestaties, kan 8.192 tokens verwerken, zeer geschikt voor korte gesprekken, notities en snelle contentgeneratie."}, "moonshot-v1-8k-vision-preview": {"description": "Het <PERSON>i visuele model (inclusief moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, enz.) kan de inhoud van afbeeldingen begrijpen, inclusief afbeeldingstekst, kleuren en vormen van objecten."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto kan het geschikte model kiezen op basis van het aantal Tokens dat momenteel door de context wordt gebruikt."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B is een open source code groot model, geoptimaliseerd door grootschalige versterkte leerprocessen, dat robuuste en direct inzetbare patches kan genereren. Dit model behaalde een nieuwe recordscore van 60,4% op SWE-bench Verified en vestigde daarmee een nieuw hoogtepunt voor open source modellen bij geautomatiseerde software engineering taken zoals defectherstel en code review."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 is een <PERSON>E-architectuurbasis model met krachtige codeer- en agentcapaciteiten, met in totaal 1 biljoen parameters en 32 miljard geactiveerde parameters. In benchmarktests voor algemene kennisredenering, programmeren, wiskunde en agenttaken overtreft het K2-model andere toonaangevende open-source modellen."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 is een MoE-architectuurbasis model met krachtige codeer- en agentmogelijkheden, met in totaal 1 biljoen parameters en 32 miljard geactiveerde parameters. In benchmarktests voor algemene kennisredenering, programmeren, wiskunde en agent-gerelateerde categorieën presteert het K2-model beter dan andere gangbare open-source modellen."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B is een upgrade van Nous Hermes 2, met de nieuwste intern ontwikkelde datasets."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B is een op maat gemaakt groot taalmodel van NVIDIA, ontworpen om de hulp van LLM-gegenereerde reacties op gebruikersvragen te verbeteren. Dit model presteert uitstekend in benchmarktests zoals Arena Hard, AlpacaEval 2 LC en GPT-4-Turbo MT-Bench, en staat per 1 oktober 2024 op de eerste plaats in alle drie de automatische afstemmingsbenchmarktests. Het model is getraind met RLHF (met name REINFORCE), Llama-3.1-Nemotron-70B-Reward en HelpSteer2-Preference prompts op basis van het Llama-3.1-70B-Instruct model."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Uniek taalmodel dat ongeëvenaarde nauwkeurigheid en efficiëntie biedt."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct is een op maat gemaakt groot taalmodel van NVIDIA, ontworpen om de nuttigheid van de door LLM gegenereerde reacties te verbeteren."}, "o1": {"description": "Gefocust op geavanceerd redeneren en het oplossen van complexe problemen, inclusief wiskunde en wetenschappelijke taken. Zeer geschikt voor toepassingen die diepgaand begrip van context en agentwerkstromen vereisen."}, "o1-mini": {"description": "o1-mini is een snel en kosteneffectief redeneermodel dat is ontworpen voor programmeer-, wiskunde- en wetenschappelijke toepassingen. Dit model heeft een context van 128K en een kennisafkapdatum van oktober 2023."}, "o1-preview": {"description": "o1 is het nieuwe redeneermodel van OpenAI, geschikt voor complexe taken die uitgebreide algemene kennis vereisen. Dit model heeft een context van 128K en een kennisafkapdatum van oktober 2023."}, "o1-pro": {"description": "De o1-serie modellen zijn getraind met verster<PERSON><PERSON> leren, kunnen nadenken voordat ze antwoorden en complexe redeneertaken uitvoeren. Het o1-pro model gebru<PERSON>t meer rekenkracht voor diepgaander denken, waardoor het continu betere antwoorden levert."}, "o3": {"description": "o3 is een veelzi<PERSON>di<PERSON> en krachtig model dat uitblinkt in verschillende domeinen. Het stelt nieuwe normen voor wiskunde, wetenschap, programmeren en visuele redeneringstaken. Het is ook bedreven in technische schrijfvaardigheid en het volgen van instructies. Gebruikers kunnen het gebruiken om teksten, code en afbeeldingen te analyseren en complexe meerstapsproblemen op te lossen."}, "o3-deep-research": {"description": "o3-deep-research is ons meest geavanceerde diepgaand onderzoeksmodel, speciaal ontworpen voor het verwerken van complexe, meerstaps onderzoeksopdrachten. Het kan informatie zoeken en samenvoegen van het internet, en kan ook via de MCP-connector toegang krijgen tot en gebruikmaken van jouw eigen gegevens."}, "o3-mini": {"description": "o3-mini is ons nieuwste kleine inferentiemodel dat hoge intelligentie biedt met dezelfde kosten- en vertragingdoelen als o1-mini."}, "o3-pro": {"description": "Het o3-pro model geb<PERSON><PERSON><PERSON> meer rekenkracht om dieper na te denken en altijd betere antwoorden te bieden, alleen te gebruiken onder de Responses API."}, "o4-mini": {"description": "o4-mini is ons nieuwste compacte model uit de o-serie. Het is geoptimaliseerd voor snelle en efficiënte inferentie, met een hoge efficiëntie en prestaties in codering en visuele taken."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research is ons snellere en betaalbaardere diepgaand onderzoeksmodel — perfect voor het verwerken van complexe, meerstaps onderzoeksopdrachten. Het kan informatie zoeken en samenvoegen van het internet, en kan ook via de MCP-connector toegang krijgen tot en gebruikmaken van jouw eigen gegevens."}, "open-codestral-mamba": {"description": "Codestral Mamba is een <PERSON> 2-taalmodel dat zich richt op codegeneratie en krachtige ondersteuning biedt voor geavanceerde code- en inferentietaken."}, "open-mistral-7b": {"description": "Mistral 7B is een compact maar hoogpresterend model, dat uitblinkt in batchverwerking en eenvoudige taken zoals classificatie en tekstgeneratie, met goede inferentiecapaciteiten."}, "open-mistral-nemo": {"description": "Mistral Nemo is een 12B-model ontwikkeld in samenwerking met Nvidia, biedt uitstekende inferentie- en coderingsprestaties, gemakkelijk te integreren en te vervangen."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B is een groter expertmodel dat zich richt op complexe taken, biedt uitstekende inferentiecapaciteiten en een hogere doorvoer."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B is een spaarzaam expertmodel dat meerdere parameters benut om de inferentiesnelheid te verhogen, geschikt voor het verwerken van meertalige en codegeneratietaken."}, "openai/gpt-4.1": {"description": "GPT-4.1 is ons vlaggenschipmodel voor complexe taken. Het is zeer geschikt voor het oplossen van problemen over verschillende domeinen."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini biedt een balans tussen intelligentie, snelhe<PERSON> en kosten, waardoor het een aantrekkelijke keuze is voor veel gebruiksscenario's."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano is het snelste en meest kosteneffectieve GPT-4.1 model."}, "openai/gpt-4o": {"description": "ChatGPT-4o is een dynamisch model dat in realtime wordt bijgewerkt om de meest actuele versie te behouden. Het combineert krachtige taalbegrip- en generatiecapaciteiten, geschikt voor grootschalige toepassingsscenario's, wa<PERSON><PERSON> klantenservice, onderwijs en technische ondersteuning."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini is het nieuwste model van OpenAI, gelanceerd na GPT-4 Omni, dat tekst- en afbeeldingsinvoer ondersteunt en tekstuitvoer genereert. Als hun meest geavanceerde kleine model is het veel goedkoper dan andere recente toonaangevende modellen en meer dan 60% goedkoper dan GPT-3.5 Turbo. Het behoudt de meest geavanceerde intelligentie met een aanzienlijke prijs-kwaliteitverhouding. GPT-4o mini behaalde 82% op de MMLU-test en staat momenteel hoger in chatvoorkeuren dan GPT-4."}, "openai/o1": {"description": "o1 is het nieuwe redeneermodel van OpenAI, ondersteunt tekst- en beeldinvoer en genereert tekstuitvoer, geschikt voor complexe taken die brede algemene kennis vereisen. Dit model heeft een context van 200K en een kennisafkapdatum van oktober 2023."}, "openai/o1-mini": {"description": "o1-mini is een snel en kosteneffectief redeneermodel dat is ontworpen voor programmeer-, wiskunde- en wetenschappelijke toepassingen. Dit model heeft een context van 128K en een kennisafkapdatum van oktober 2023."}, "openai/o1-preview": {"description": "o1 is het nieuwe redeneermodel van OpenAI, geschikt voor complexe taken die uitgebreide algemene kennis vereisen. Dit model heeft een context van 128K en een kennisafkapdatum van oktober 2023."}, "openai/o3": {"description": "o3 is een kracht<PERSON>, veelzij<PERSON>ge model dat uitblinkt in verschillende domeinen. Het stelt nieuwe normen voor wiskunde, wet<PERSON><PERSON><PERSON>, programmeren en visuele redeneertaken. Het is ook bedreven in technische schrijfvaardigheid en het opvolgen van instructies. Gebruikers kunnen het gebruiken om teksten, code en afbeeldingen te analyseren en complexe problemen met meerdere stappen op te lossen."}, "openai/o3-mini": {"description": "o3-mini biedt hoge intelligentie met dezelfde kosten- en vertragingdoelen als o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini high is een versie met een hoog redeneerniveau die hoge intelligentie biedt met dezelfde kosten- en vertragingdoelen als o1-mini."}, "openai/o4-mini": {"description": "o4-mini is geoptimaliseerd voor snelle en efficiënte inferentie, met hoge efficiëntie en prestaties in codering en visuele taken."}, "openai/o4-mini-high": {"description": "o4-mini high inference level versie, geoptimaliseerd voor snelle en efficiënte inferentie, met hoge efficiëntie en prestaties in codering en visuele taken."}, "openrouter/auto": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> contextlengte, het onderwerp en de complexiteit, wordt uw verzoek verzonden naar Llama 3 70B Instruct, Claude 3.5 Sonnet (zelfregulerend) of GPT-4o."}, "phi3": {"description": "Phi-3 is een licht<PERSON>t open model van Microsoft, geschikt voor efficiënte integratie en grootschalige kennisinferentie."}, "phi3:14b": {"description": "Phi-3 is een licht<PERSON>t open model van Microsoft, geschikt voor efficiënte integratie en grootschalige kennisinferentie."}, "pixtral-12b-2409": {"description": "Het Pixtral model toont sterke capaciteiten in taken zoals grafiek- en beeldbegrip, documentvraag-en-antwoord, multimodale redenering en instructievolging, en kan afbeeldingen met natuurlijke resolutie en beeldverhouding verwerken, evenals een onbeperkt aantal afbeeldingen in een lange contextvenster van maximaal 128K tokens."}, "pixtral-large-latest": {"description": "Pixtral Large is een open-source multimodaal model met 124 miljard parameters, gebaseerd op Mistral Large 2. Dit is ons tweede model in de multimodale familie en toont geavanceerde beeldbegripcapaciteiten."}, "pro-128k": {"description": "Spark Pro 128K is uitgerust met een zeer grote contextverwerkingscapaciteit, in staat om tot 128K contextinformatie te verwerken. Het is bijzonder geschikt voor lange teksten die een volledige analyse en langdurige logische verbanden vereisen, en biedt een vloeiende en consistente logica met diverse ondersteuningen voor citaten in complexe tekstcommunicatie."}, "qvq-72b-preview": {"description": "Het QVQ-model is een experimenteel onderzoeksmodel ontwikkeld door het Qwen-team, gericht op het verbeteren van visuele redeneervaardigheden, vooral in het domein van wiskundige redenering."}, "qvq-max": {"description": "<PERSON><PERSON>wen QVQ visueel redeneermodel, ondersteunt visuele input en keten van gedachten output, toont sterkere capaciteiten in wiskunde, programmer<PERSON>, visuele analyse, creatie en algemene taken."}, "qvq-plus": {"description": "Visueel redeneermodel. Ondersteunt visuele input en keten van gedachten output. De plus-versie, uitgebracht na het qvq-max model, biedt snellere redeneersnelheid en een betere balans tussen effectiviteit en kosten in vergelijking met het qvq-max model."}, "qwen-coder-plus": {"description": "<PERSON>yi Qianwen codeermodel."}, "qwen-coder-turbo": {"description": "<PERSON>yi Qianwen codeermodel."}, "qwen-coder-turbo-latest": {"description": "Het <PERSON>yi Qianwen codeermodel."}, "qwen-long": {"description": "<PERSON><PERSON> is een groots<PERSON>ig taalmodel dat lange tekstcontexten ondersteunt, evenals dialoogfunctionaliteit op basis van lange documenten en meerdere documenten."}, "qwen-math-plus": {"description": "<PERSON><PERSON> wiskund<PERSON>l, speciaal ontworpen voor het oplossen van wiskundige problemen."}, "qwen-math-plus-latest": {"description": "<PERSON>t <PERSON>yi <PERSON>wen wiskundemodel is speciaal ontworpen voor het oplossen van wiskundige problemen."}, "qwen-math-turbo": {"description": "<PERSON><PERSON> wiskund<PERSON>l, speciaal ontworpen voor het oplossen van wiskundige problemen."}, "qwen-math-turbo-latest": {"description": "<PERSON>t <PERSON>yi <PERSON>wen wiskundemodel is speciaal ontworpen voor het oplossen van wiskundige problemen."}, "qwen-max": {"description": "<PERSON><PERSON> is een enorme versie van het grootschalige taalmodel, dat ondersteuning biedt voor verschillende taalinputs zoals Chinees en Engels en momenteel de API-modellen achter de Qwen 2.5-productversie vertegenwoordigt."}, "qwen-omni-turbo": {"description": "<PERSON>-Omni serie modellen ondersteunen multimodale input, waaronder video, audio, afbeeldingen en tekst, en genereren audio en tekst als output."}, "qwen-plus": {"description": "<PERSON><PERSON> is een verbeterde versie van het grootschalige taalmodel dat ondersteuning biedt voor verschillende taalinputs zoals Chinees en Engels."}, "qwen-turbo": {"description": "<PERSON><PERSON> is een groots<PERSON>ig taalmodel dat ondersteuning biedt voor verschillende taalinputs zoals Chinees en Engels."}, "qwen-vl-chat-v1": {"description": "Qwen VL ondersteunt flexibele interactiemethoden, inclusief meerdere afbeeldingen, meerdere rondes van vraag en antwoord, en creatiecapaciteiten."}, "qwen-vl-max": {"description": "<PERSON><PERSON> Qi<PERSON>wen ultra-groots<PERSON>ig visueel-taalmodel. <PERSON><PERSON><PERSON><PERSON><PERSON> met de verbeterde versie, verbetert het opnieuw de visuele redeneercapaciteit en instructienaleving, en biedt een hoger niveau van visuele perceptie en cognitie."}, "qwen-vl-max-latest": {"description": "<PERSON>t <PERSON>yi Qi<PERSON>wen ultra-grootschalige visuele taalmodel. In verge<PERSON><PERSON>ing met de verbeterde versie, verhoogt het opnieuw de visuele redeneervaardigheden en de naleving van instructies, en biedt het een hoger niveau van visuele waarneming en cognitie."}, "qwen-vl-ocr": {"description": "<PERSON><PERSON>wen OCR is een gespecialiseerd model voor teksterkenning, gericht op het extraheren van tekst uit documenten, tabellen, examenvragen, handgeschreven tekst en andere beeldtypen. Het kan meerdere talen herkennen, <PERSON><PERSON><PERSON>, <PERSON>gels, Frans, Japans, Koreaans, Duits, Russisch, Italiaans, Vietnamees en Arabisch."}, "qwen-vl-plus": {"description": "Verbeterde versie van het Tongyi Qianwen grootschalige visueel-taalmodel. Verbetert aanzienlijk de detailherkenning en tekstherkenning, onders<PERSON><PERSON> af<PERSON> met meer dan een miljoen pixels en afbeeldingen met willekeurige verhoudingen."}, "qwen-vl-plus-latest": {"description": "De verbeterde versie van het Tongyi Qianwen grootschalige visuele taalmodel. Het verbetert aanzienlijk de detailherkenning en tekstherkenning, ondersteunt resoluties van meer dan een miljoen pixels en afbeeldingen met el<PERSON> verhouding."}, "qwen-vl-v1": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met het Qwen-7B taalmodel, voegt het een afbeeldingsmodel toe, met een invo<PERSON><PERSON>ol<PERSON><PERSON> van 448 voor het voorgetrainde model."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 is de gloednieuwe serie van grote taalmodellen van Qwen. Qwen2 7B is een transformer-gebaseerd model dat uitblinkt in taalbegrip, meertalige capaciteiten, programmeren, wiskunde en redenering."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 is een gloednieuwe serie grote taalmodellen met sterkere begrip- en generatiecapaciteiten."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL is de nieuwste iteratie van het Qwen-VL-model en heeft geavanceerde prestaties behaald in visuele begrip benchmarktests, waaronder MathVista, DocVQA, RealWorldQA en MTVQA. Qwen2-VL kan video's van meer dan 20 minuten begrijpen voor hoogwaardige video-gebaseerde vraag-en-antwoord, dialoog en contentcreatie. Het heeft ook complexe redenerings- en besluitvormingscapaciteiten en kan worden geïntegreerd met mobiele apparaten, robots, enzovoort, voor automatische operaties op basis van visuele omgevingen en tekstinstructies. Naast Engels en Chinees ondersteunt Qwen2-VL nu ook het begrijpen van tekst in verschillende talen in afbeeldingen, waaronder de meeste Europese talen, Japans, Koreaans, Arabisch en Vietnamees."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct is een van de nieuwste grote taalmodellen die door Alibaba Cloud is uitgebracht. Dit 72B-model heeft aanzienlijke verbeteringen in codering en wiskunde. Het model biedt ook ondersteuning voor meerdere talen, met meer dan 29 talen, wa<PERSON><PERSON> en Engels. Het model heeft aanzienlijke verbeteringen in het volgen van instructies, het begrijpen van gestructureerde gegevens en het genereren van gestructureerde output (vooral JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct is een van de nieuwste grote taalmodellen die door Alibaba Cloud is uitgebracht. Dit 32B-model heeft aanzienlijke verbeteringen in codering en wiskunde. Het model biedt ook ondersteuning voor meerdere talen, met meer dan 29 talen, wa<PERSON><PERSON> en Engels. Het model heeft aanzienlijke verbeteringen in het volgen van instructies, het begrijpen van gestructureerde gegevens en het genereren van gestructureerde output (vooral JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM gericht op zowel Chinees als Engels, gericht op taal, <PERSON>en, wiskunde, redeneren en meer."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "<PERSON><PERSON><PERSON>erd <PERSON>, ondersteunt codegeneratie, redeneren en reparatie, dekt gangbare programmeertalen."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "<PERSON>rac<PERSON>ig middelgroot codeermodel, ondersteunt 32K contextlengte, gespecialiseerd in meertalige programmering."}, "qwen/qwen3-14b": {"description": "Qwen3-14B is een dichte causale taalmodel met 14 miljard parameters in de Qwen3 serie, speciaal ontworpen voor complexe redenering en efficiënte gesprekken. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor wiskunde, programmeren en logische redenering en de 'niet-denk' modus voor algemene gesprekken. Dit model is fijn afgesteld voor het volgen van instructies, het gebruik van agenttools, creatieve schrijfopdrachten en meertalige taken in meer dan 100 talen en dialecten. Het verwerkt van nature 32K tokens context en kan worden uitgebreid tot 131K tokens met YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B is een dichte causale taalmodel met 14 miljard parameters in de Qwen3 serie, speciaal ontworpen voor complexe redenering en efficiënte gesprekken. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor wiskunde, programmeren en logische redenering en de 'niet-denk' modus voor algemene gesprekken. Dit model is fijn afgesteld voor het volgen van instructies, het gebruik van agenttools, creatieve schrijfopdrachten en meertalige taken in meer dan 100 talen en dialecten. Het verwerkt van nature 32K tokens context en kan worden uitgebreid tot 131K tokens met YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B is een 235B parameters expert-meng (MoE) model ontwikkeld door Qwen, dat 22B parameters activeert bij elke voorwaartse doorgang. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor complexe redenering, wiskunde en code taken en de 'niet-denk' modus voor algemene gespreks efficiëntie. Dit model toont krachtige redeneringscapaciteiten, meertalige ondersteuning (meer dan 100 talen en dialecten), geavanceerd volgen van instructies en het aanroepen van agenttools. Het verwerkt van nature een contextvenster van 32K tokens en kan worden uitgebreid tot 131K tokens met YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B is een 235B parameters expert-meng (MoE) model ontwikkeld door Qwen, dat 22B parameters activeert bij elke voorwaartse doorgang. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor complexe redenering, wiskunde en code taken en de 'niet-denk' modus voor algemene gespreks efficiëntie. Dit model toont krachtige redeneringscapaciteiten, meertalige ondersteuning (meer dan 100 talen en dialecten), geavanceerd volgen van instructies en het aanroepen van agenttools. Het verwerkt van nature een contextvenster van 32K tokens en kan worden uitgebreid tot 131K tokens met YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 is de nieuwste generatie van de Qwen grote taalmodelserie, met een dichte en expert-meng (MoE) architectuur, die uitblinkt in redeneren, meertalige ondersteuning en geavanceerde agenttaken. De unieke mogelijkheid om naadloos over te schakelen tussen de denkmodus voor complexe redenering en de niet-denkmodus voor efficiënte gesprekken zorgt voor veelzijdige en hoogwaardige prestaties.\n\nQwen3 overtreft aanzienlijk eerdere modellen zoals QwQ en Qwen2.5, en biedt uitstekende vaardigheden in wiskunde, codering, gezond verstand redenering, creatieve schrijfvaardigheden en interactieve gesprekken. De Qwen3-30B-A3B variant bevat 30,5 miljard parameters (3,3 miljard actieve parameters), 48 lagen, 128 experts (waarvan 8 per taak geactiveerd), en ondersteunt tot 131K tokens context (met YaRN), waarmee het een nieuwe standaard voor open-source modellen stelt."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 is de nieuwste generatie van de Qwen grote taalmodelserie, met een dichte en expert-meng (MoE) architectuur, die uitblinkt in redeneren, meertalige ondersteuning en geavanceerde agenttaken. De unieke mogelijkheid om naadloos over te schakelen tussen de denkmodus voor complexe redenering en de niet-denkmodus voor efficiënte gesprekken zorgt voor veelzijdige en hoogwaardige prestaties.\n\nQwen3 overtreft aanzienlijk eerdere modellen zoals QwQ en Qwen2.5, en biedt uitstekende vaardigheden in wiskunde, codering, gezond verstand redenering, creatieve schrijfvaardigheden en interactieve gesprekken. De Qwen3-30B-A3B variant bevat 30,5 miljard parameters (3,3 miljard actieve parameters), 48 lagen, 128 experts (waarvan 8 per taak geactiveerd), en ondersteunt tot 131K tokens context (met YaRN), waarmee het een nieuwe standaard voor open-source modellen stelt."}, "qwen/qwen3-32b": {"description": "Qwen3-32B is een dichte causale taalmodel met 32,8 miljard parameters in de Qwen3 serie, geoptimaliseerd voor complexe redenering en efficiënte gesprekken. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor wiskunde, codering en logische redenering en de 'niet-denk' modus voor snellere, algemene gesprekken. Dit model presteert krachtig in het volgen van instructies, het gebruik van agenttools, creatieve schrijfopdrachten en meertalige taken in meer dan 100 talen en dialecten. Het verwerkt van nature 32K tokens context en kan worden uitgebreid tot 131K tokens met YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B is een dichte causale taalmodel met 32,8 miljard parameters in de Qwen3 serie, geoptimaliseerd voor complexe redenering en efficiënte gesprekken. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor wiskunde, codering en logische redenering en de 'niet-denk' modus voor snellere, algemene gesprekken. Dit model presteert krachtig in het volgen van instructies, het gebruik van agenttools, creatieve schrijfopdrachten en meertalige taken in meer dan 100 talen en dialecten. Het verwerkt van nature 32K tokens context en kan worden uitgebreid tot 131K tokens met YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B is een dichte causale taalmodel met 8 miljard parameters in de Qwen3 serie, speciaal ontworpen voor redeneringsintensieve taken en efficiënte gesprekken. Het ondersteunt naadloze overgangen tussen de 'denk' modus voor wiskunde, codering en logische redenering en de 'niet-denk' modus voor algemene gesprekken. Dit model is fijn afgesteld voor het volgen van instructies, agentintegratie, creatieve schrijfopdrachten en meertalig gebruik in meer dan 100 talen en dialecten. Het ondersteunt van nature een contextvenster van 32K tokens en kan worden uitgebreid tot 131K tokens via YaRN."}, "qwen2": {"description": "Qwen2 is Alibaba's nieuwe generatie grootschalig taalmodel, ondersteunt diverse toepassingsbehoeften met uitstekende prestaties."}, "qwen2-72b-instruct": {"description": "Qwen2 is een nieuwe generatie van grote taalmodellen die is ontwikkeld door het Qwen-team. Het is geb<PERSON><PERSON> op de Transformer-architectuur en maakt gebruik van SwiGLU-activatiefuncties, aandacht-QKV-bias, groepsquery-aandacht, een mix van schuifraam-aandacht en volledige aandacht, en andere technieken. Bovendien heeft het Qwen-team de tokenizer verbeterd om aan te passen aan meerdere natuurlijke talen en code."}, "qwen2-7b-instruct": {"description": "Qwen2 is een nieuwe generatie van grote taalmodellen die is ontwikkeld door het Qwen-team. Het is geb<PERSON><PERSON> op de Transformer-architectuur en maakt gebruik van technieken zoals de SwiGLU-activatiefunctie, aandacht QKV-bias, groepsquery-aanda<PERSON>, een mengs<PERSON> van schuifraam-aandacht en volledige aandacht. Bovendien heeft het Qwen-team de tokenizer verbeterd om aan verschillende natuurlijke talen en code te kunnen wennen."}, "qwen2.5": {"description": "Qwen2.5 is de nieuwe generatie grootschalig taalmodel van <PERSON>, dat uitstekende prestaties levert ter ondersteuning van diverse toepassingsbehoeften."}, "qwen2.5-14b-instruct": {"description": "Het 14B model van <PERSON>wen 2.5 is open source beschikbaar."}, "qwen2.5-14b-instruct-1m": {"description": "Qwen2.5 is een open-source model van 72B schaal."}, "qwen2.5-32b-instruct": {"description": "Het 32B model van <PERSON> Qi<PERSON>wen 2.5 is open source beschikbaar."}, "qwen2.5-72b-instruct": {"description": "Het 72B model van <PERSON> Qi<PERSON>wen 2.5 is open source beschikbaar."}, "qwen2.5-7b-instruct": {"description": "Het 7B model van <PERSON>wen 2.5 is open source beschikbaar."}, "qwen2.5-coder-1.5b-instruct": {"description": "Qwen-code model open source versie."}, "qwen2.5-coder-14b-instruct": {"description": "Open source vers<PERSON> van het Tongyi Qianwen codeermodel."}, "qwen2.5-coder-32b-instruct": {"description": "Open source versie van het Tongyi Qianwen code model."}, "qwen2.5-coder-7b-instruct": {"description": "De open source versie van het Tongyi Qianwen codeermodel."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder is het nieuwste grote taalmodel voor code in de Qwen-reeks (vroeger bekend als CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 is de nieuwste reeks van het Qwen-groottaalmodel. Voor Qwen2.5 hebben we verschillende basis-taalmodellen en instructie-finetuning-taalmodellen uitgebracht, met parameters die variëren van 500 miljoen tot 7,2 miljard."}, "qwen2.5-math-1.5b-instruct": {"description": "Qwen-Math model beschikt over krachtige wiskundige probleemoplossende mogelijkheden."}, "qwen2.5-math-72b-instruct": {"description": "<PERSON><PERSON>-Math model heeft krachtige capaciteiten voor het oplossen van wiskundige problemen."}, "qwen2.5-math-7b-instruct": {"description": "<PERSON><PERSON>-Math model heeft krachtige capaciteiten voor het oplossen van wiskundige problemen."}, "qwen2.5-omni-7b": {"description": "<PERSON><PERSON>-Omni serie model ondersteunt het invoeren van verschillende modaliteiten van gegevens, waaronder video, audio, afbeeldingen en tekst, en kan audio en tekst als output genereren."}, "qwen2.5-vl-32b-instruct": {"description": "De Qwen2.5-VL-serie modellen verbeteren het intelligentieniveau, de praktisch toepasbaarheid en de bruikba<PERSON>heid, waardoor ze beter presteren in natuurlijke conversaties, inhoudscREATie, specialistische kennisdiensten en codeontwikkeling. De 32B-versie maakt gebruik van versterkingsleertechnieken om het model te optimaliseren, waardoor het in vergelijking met andere modellen uit de Qwen2.5 VL-serie een uitvoerstijl biedt die meer voldoet aan menselijke voorkeuren, een betere redeneringscapaciteit voor complexe wiskundige problemen, en een fijnere begrip en redenering van afbeeldingen."}, "qwen2.5-vl-72b-instruct": {"description": "Verbeterde instructievolging, wiskunde, probleemoplossing en code, met verbeterde herkenningscapaciteiten voor verschillende formaten, directe en nauwkeurige lokalisatie van visuele elementen, ondersteuning voor lange videobestanden (maximaal 10 minuten) en seconde-niveau gebeurtenislocatie, kan tijdsvolgorde en snelheid begrijpen, en ondersteunt het bedienen van OS of mobiele agenten op basis van analyse- en lokalisatiecapaciteiten, sterke capaciteiten voor het extraheren van belangrijke informatie en JSON-formaat uitvoer, deze versie is de 72B versie, de krachtigste versie in deze serie."}, "qwen2.5-vl-7b-instruct": {"description": "Verbeterde instructievolging, wiskunde, probleemoplossing en code, met verbeterde herkenningscapaciteiten voor verschillende formaten, directe en nauwkeurige lokalisatie van visuele elementen, ondersteuning voor lange videobestanden (maximaal 10 minuten) en seconde-niveau gebeurtenislocatie, kan tijdsvolgorde en snelheid begrijpen, en ondersteunt het bedienen van OS of mobiele agenten op basis van analyse- en lokalisatiecapaciteiten, sterke capaciteiten voor het extraheren van belangrijke informatie en JSON-formaat uitvoer, deze versie is de 72B versie, de krachtigste versie in deze serie."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL is de nieuwste versie van het visueel-taalmodel in de Qwen-modelserie."}, "qwen2.5:0.5b": {"description": "Qwen2.5 is de nieuwe generatie grootschalig taalmodel van <PERSON>, dat uitstekende prestaties levert ter ondersteuning van diverse toepassingsbehoeften."}, "qwen2.5:1.5b": {"description": "Qwen2.5 is de nieuwe generatie grootschalig taalmodel van <PERSON>, dat uitstekende prestaties levert ter ondersteuning van diverse toepassingsbehoeften."}, "qwen2.5:72b": {"description": "Qwen2.5 is de nieuwe generatie grootschalig taalmodel van <PERSON>, dat uitstekende prestaties levert ter ondersteuning van diverse toepassingsbehoeften."}, "qwen2:0.5b": {"description": "Qwen2 is Alibaba's nieuwe generatie grootschalig taalmodel, ondersteunt diverse toepassingsbehoeften met uitstekende prestaties."}, "qwen2:1.5b": {"description": "Qwen2 is Alibaba's nieuwe generatie grootschalig taalmodel, ondersteunt diverse toepassingsbehoeften met uitstekende prestaties."}, "qwen2:72b": {"description": "Qwen2 is Alibaba's nieuwe generatie grootschalig taalmodel, ondersteunt diverse toepassingsbehoeften met uitstekende prestaties."}, "qwen3": {"description": "Qwen3 is het nieuwe generatie grootschalige taalmodel van Alibaba, dat uitstekende prestaties levert ter ondersteuning van diverse applicatiebehoeften."}, "qwen3-0.6b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-1.7b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-14b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-235b-a22b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-235b-a22b-instruct-2507": {"description": "Open-source model in niet-denkende modus gebaseerd op Qwen3, met lichte verbeteringen in subjectieve creativiteit en modelveiligheid ten opzichte van de vorige versie (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Open-source model in denkmodus gebaseerd op Qwen3, met aanzienlijke verbeteringen in logische vaardigheden, algemene capaciteiten, kennisversterking en creativiteit ten opzichte van de vorige versie (Tongyi Qianwen 3-235B-A22B), geschikt voor complexe en veeleisende redeneerscenario's."}, "qwen3-30b-a3b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-30b-a3b-instruct-2507": {"description": "In vergelijking met de vorige versie (Qwen3-30B-A3B) is de algemene meertalige en Chinese en Engelse vaardigheid aanzienlijk verbeterd. Er is speciale optimalisatie voor subjectieve en open taken, waardoor het veel beter aansluit bij gebruikersvoorkeuren en nuttigere antwoorden kan bieden."}, "qwen3-30b-a3b-thinking-2507": {"description": "Gebaseerd op het open source denkmodusmodel van Qwen3, heeft deze versie ten opzichte van de vorige (Tongyi Qianwen 3-30B-A3B) aanzienlijke verbeteringen in logisch vermogen, algemene vaardigheden, kennisverrijking en creativiteit. Het is geschikt voor complexe scenario's met sterke redeneervaardigheden."}, "qwen3-32b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-4b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-8b": {"description": "Qwen3 is een nieuwe generatie van het Q<PERSON> grote model met aanzienlijk verbeterde capaciteiten, die de industrie leidende niveaus bereikt in redeneren, alge<PERSON>n gebruik, agent en meertalige ondersteuning, en ondersteunt de schakeling tussen denkmodi."}, "qwen3-coder-480b-a35b-instruct": {"description": "Open-source codeermodel <PERSON>. De nieuwste qwen3-coder-480b-a35b-instruct is g<PERSON><PERSON><PERSON> op Qwen3, met krachtige Coding Agent-capaciteiten, bedreven in toolaanroepen en omgevingsinteractie, en kan zelfstandig programmeren met uitstekende codeervaardigheden en algemene capaciteiten."}, "qwen3-coder-plus": {"description": "Codeer<PERSON><PERSON> van <PERSON>. De nieuwste Qwen3-Coder-Plus serie is gebaseerd op Qwen3, met krachtige Coding Agent-capaciteiten, bedreven in toolaanroepen en omgevingsinteractie, en kan zelfstandig programmeren met uitstekende codeervaardigheden en algemene capaciteiten."}, "qwq": {"description": "QwQ is een experimenteel onderzoeksmodel dat zich richt op het verbeteren van de AI-redeneringscapaciteiten."}, "qwq-32b": {"description": "De QwQ-inferentiemodel, getraind op het Qwen2.5-32B-model, heeft zijn inferentievermogen aanzienlijk verbeterd door middel van versterkend leren. De kernindicatoren van het model, zoals wiskundige code (AIME 24/25, LiveCodeBench) en enkele algemene indicatoren (IFEval, LiveBench, enz.), bereiken het niveau van de DeepSeek-R1 volwaardige versie, waarbij alle indicatoren aanzienlijk beter presteren dan de DeepSeek-R1-Distill-Qwen-32B, die ook op Qwen2.5-32B is gebase<PERSON>."}, "qwq-32b-preview": {"description": "Het QwQ-model is een experimenteel onderzoeksmodel ontwikkeld door het Qwen-team, gericht op het verbeteren van de AI-redeneringscapaciteiten."}, "qwq-plus": {"description": "QwQ redeneermodel gebaseerd op het Qwen2.5 model, met versterkt leren om de redeneercapaciteit aanzienlijk te verbeteren. De kernindicatoren voor wiskunde en code (AIME 24/25, LiveCodeBench) en enkele algemene indicatoren (IFEval, LiveBench, enz.) bereiken het volledige DeepSeek-R1 prestatieniveau."}, "qwq_32b": {"description": "<PERSON><PERSON> gemiddeld schaal redeneringsmodel uit de Qwen-serie. In vergelijking met traditionele instructie-geoptimaliseerde modellen, kan <PERSON>w<PERSON>, dat over denk- en redeneringscapaciteiten beschikt, de prestaties in downstream-taken, vooral bij het oplossen van moeilijke problemen, aanzienlijk verbeteren."}, "r1-1776": {"description": "R1-1776 is een vers<PERSON> van het DeepSeek R1-model, dat is bijgetraind om ongecensureerde, onpartijdige feitelijke informatie te bieden."}, "solar-mini": {"description": "Solar Mini is een compacte LLM die beter presteert dan GPT-3.5, met sterke meertalige capaciteiten, ondersteunt Engels en Koreaans, en biedt een efficiënte en compacte oplossing."}, "solar-mini-ja": {"description": "Solar Mini (Ja) breidt de capaciteiten van Solar Mini uit, met een focus op het Japans, terwijl het efficiënt en uitstekend presteert in het gebruik van Engels en Koreaans."}, "solar-pro": {"description": "Solar Pro is een zeer intelligent LLM dat is uitgebracht door Upstage, gericht op instructievolging met één GPU, met een IFEval-score van boven de 80. <PERSON><PERSON> ondersteunt het <PERSON>, met een officiële versie die gepland staat voor november 2024, die de taalondersteuning en contextlengte zal uitbreiden."}, "sonar": {"description": "<PERSON><PERSON> lichtgewicht zoekproduct op basis van contextuele zoekopdrachten, sneller en goedkoper dan Sonar Pro."}, "sonar-deep-research": {"description": "Deep Research voert uitgebreide expertstudies uit en bundelt deze in toegankelijke, bruikbare rapporten."}, "sonar-pro": {"description": "<PERSON><PERSON> geavanceerd zoekproduct dat contextuele zoekopdrachten ondersteunt, met geavance<PERSON>e query's en vervolgacties."}, "sonar-reasoning": {"description": "<PERSON>en nieuw API-product ondersteund door het DeepSeek redeneringsmodel."}, "sonar-reasoning-pro": {"description": "<PERSON>en nieuw API-product ondersteund door het DeepSeek redeneringsmodel."}, "stable-diffusion-3-medium": {"description": "Het nieuwste tekst-naar-beeld groot model uitgebracht door Stability AI. Deze versie bouwt voort op de voordelen van eerdere generaties en verbetert aanzienlijk de beeldkwaliteit, tekstbegrip en stijlvariëteit. Het kan complexe natuurlijke taal prompts nauwkeuriger interpreteren en genereert preciezere en gevarieerdere beelden."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large is een multimodale diffusie-transformer (MMDiT) tekst-naar-beeldgeneratiemodel met 800 miljoen parameters, met uitstekende beeldkwaliteit en promptmatching. Het ondersteunt het genereren van hoge-resolutie beelden tot 1 miljoen pixels en kan efficiënt draaien op standaard consumentenhardware."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo is een model gebaseerd op stable-diffusion-3.5-large, met adversariële diffusie-distillatie (ADD) technologie voor snellere snelheid."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 is geïnitialiseerd met de stable-diffusion-v1.2 checkpoint gewichten en fijn afgesteld met 595k stappen op \"laion-aesthetics v2 5+\" dataset bij 512x512 resolutie, met 10% minder tekstconditionering om classifier-v<PERSON><PERSON> begeleiding te verbeteren."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl heeft aanzienlijke verbeteringen ten opzichte van v1.5 en levert vergelijkbare resultaten als het huidige open-source SOTA tekst-naar-beeld model Midjourney. Verbeteringen omvatten een drie keer grotere UNet backbone, een refinement module voor betere beeldkwaliteit en efficiëntere trainingstechnieken."}, "stable-diffusion-xl-base-1.0": {"description": "Een door Stability AI ontwikkeld en open-source groot tekst-naar-beeld model met toonaangevende creatieve beeldgeneratiecapaciteiten. Het beschikt over uitstekende instructiebegrip en ondersteunt omgekeerde promptdefinities voor nauwkeurige inhoudsgeneratie."}, "step-1-128k": {"description": "Biedt een balans tussen prestaties en kosten, geschikt voor algemene scenario's."}, "step-1-256k": {"description": "Heeft ultra-lange contextverwerkingscapaciteiten, vooral geschikt voor lange documentanalyse."}, "step-1-32k": {"description": "Ondersteunt gesprekken van gemiddelde lengte, geschikt voor verschillende toepassingsscenario's."}, "step-1-8k": {"description": "<PERSON> model, geschikt voor lichte taken."}, "step-1-flash": {"description": "Hogesnelheidsmodel, geschikt voor realtime gesprekken."}, "step-1.5v-mini": {"description": "Dit model heeft krachtige video begrip capaciteiten."}, "step-1o-turbo-vision": {"description": "Dit model heeft krachtige beeldbegripcapaciteiten en presteert beter dan 1o in wiskunde en codering. Het model is kleiner dan 1o en heeft een snellere uitvoersnelheid."}, "step-1o-vision-32k": {"description": "Dit model heeft krachtige beeldbegripcapaciteiten. In vergelijking met de step-1v serie modellen heeft het een sterkere visuele prestatie."}, "step-1v-32k": {"description": "Ondersteunt visuele invoer, verbetert de multimodale interactie-ervaring."}, "step-1v-8k": {"description": "<PERSON> visueel model, geschikt voor basis tekst- en afbeeldingtaken."}, "step-1x-edit": {"description": "Dit model is gespecialiseerd in beeldbewerkingsopdrachten en kan afbeeldingen aanpassen en verbeteren op basis van door gebruikers aangeleverde afbeeldingen en tekstbeschrijvingen. Het ondersteunt diverse invoerformaten, waaronder tekstbeschrijvingen en voorbeeldafbeeldingen. Het model begrijpt de intentie van de gebruiker en genereert beeldbewerkingsresultaten die aan de eisen voldoen."}, "step-1x-medium": {"description": "Dit model heeft krachtige beeldgeneratiecapaciteiten en ondersteunt tekstbeschrijvingen als invoer. Het biedt native ondersteuning voor het Chinees, waardoor het Chinese tekstbeschrijvingen beter kan begrijpen en verwerken. Het kan semantische informatie nauwkeuriger vastleggen en omzetten in beeldkenmerken voor preciezere beeldgeneratie. Het model genereert hoge-resolutie, hoogwaardige beelden en heeft enige stijltransfercapaciteit."}, "step-2-16k": {"description": "Ondersteunt grootschalige contextinteracties, geschikt voor complexe gespreksscenario's."}, "step-2-16k-exp": {"description": "De experimentele versie van het step-2 model, met de nieuwste functies, in een rollende update. Niet aanbevolen voor gebruik in een productieomgeving."}, "step-2-mini": {"description": "<PERSON>en razendsnel groot model gebaseerd op de nieuwe generatie zelfontwikkelde Attention-architectuur MFA, dat met zeer lage kosten vergelijkbare resultaten als step1 behaalt, terwijl het een hogere doorvoer en snellere responstijd behoudt. Het kan algemene taken verwerken en heeft speciale vaardigheden op het gebied van codering."}, "step-2x-large": {"description": "De nieuwe generatie Step Star beeldgeneratiemodel, gespecialiseerd in beeldgeneratie. Het kan op basis van door gebruikers aangeleverde tekstbeschrijvingen hoogwaardige beelden genereren. Het nieuwe model produceert realistischere texturen en heeft sterkere Chinese en Engelse tekstgeneratiecapaciteiten."}, "step-r1-v-mini": {"description": "Dit model is een krachtig redeneringsmodel met sterke beeldbegripcapaciteiten, in staat om beeld- en tekstinformatie te verwerken en tekstinhoud te genereren na diep nadenken. Dit model presteert uitstekend in visuele redenering en heeft eersteklas wiskundige, code- en tekstredeneringscapaciteiten. De contextlengte is 100k."}, "taichu_llm": {"description": "Het Zido Tai Chu-taalmodel heeft een sterke taalbegripcapaciteit en kan tekstcreatie, kennisvragen, codeprogrammering, wiskundige berekeningen, logische redenering, sentimentanalyse, tekstsamenvattingen en meer aan. Het combineert innovatief grote data voortraining met rijke kennis uit meerdere bronnen, door algoritmische technologie continu te verfijnen en voortdurend nieuwe kennis op te nemen uit enorme tekstdata op het gebied van vocabulaire, structuur, grammatica en semantiek, waardoor de modelprestaties voortdurend evolueren. Het biedt gebruikers gemakkelijkere informatie en diensten en een meer intelligente ervaring."}, "taichu_o1": {"description": "taichu_o1 is een nieuw generatie redeneringsmodel dat mensachtige denkprocessen mogelijk maakt door multimodale interactie en versterkend leren, en ondersteunt complexe besluitvorming, terwijl het een hoge precisie-output behoudt en de denkpaden van modelinference toont, geschikt voor strategische analyse en diep nadenken."}, "taichu_vl": {"description": "Integ<PERSON><PERSON> beeldbegrip, kennisoverdracht en logische toerekening, en presteert uitstekend in het domein van vraag-en-antwoord met tekst en afbeeldingen."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct heeft 80 miljard parameters, waarbij het activeren van 13 miljard parameters al vergelijkbare prestaties levert als grotere modellen. Het ondersteunt een hybride redeneermethode van 'snelle denkwijze/langzame denkwijze'; stabiele lange tekstbegrip; geverifieerd door BFCL-v3 en τ-Bench, met leidende agentcapaciteiten; gecombineerd met GQA en meerdere kwantisatieformaten voor efficiënte inferentie."}, "text-embedding-3-large": {"description": "Het krachtigste vectorisatie model, geschikt voor Engelse en niet-Engelse taken."}, "text-embedding-3-small": {"description": "<PERSON>en efficiënte en kosteneffectieve nieuwe generatie Embedding model, geschikt voor kennisretrieval, RAG-toepassingen en andere scenario's."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 is een 32B tweetalig (Chinees en Engels) open gewichten taalmodel, geoptimaliseerd voor codegeneratie, functieaanroepen en agenttaken. Het is voorgetraind op 15T hoogwaardige en herredeneringsdata en verder verfijnd met afstemming op menselijke voorkeuren, afwijzingssampling en versterkingsleren. Dit model presteert uitstekend in complexe redenering, artefactgeneratie en gestructureerde outputtaken, en heeft vergelijkbare prestaties behaald als GPT-4o en DeepSeek-V3-0324 in meerdere benchmarktests."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 is een 32B tweetalig (Chinees en Engels) open gewichten taalmodel, geoptimaliseerd voor codegeneratie, functieaanroepen en agenttaken. Het is voorgetraind op 15T hoogwaardige en herredeneringsdata en verder verfijnd met afstemming op menselijke voorkeuren, afwijzingssampling en versterkingsleren. Dit model presteert uitstekend in complexe redenering, artefactgeneratie en gestructureerde outputtaken, en heeft vergelijkbare prestaties behaald als GPT-4o en DeepSeek-V3-0324 in meerdere benchmarktests."}, "thudm/glm-4-9b-chat": {"description": "De open-source versie van de nieuwste generatie voorgetrainde modellen van de GLM-4-serie, uitgebracht door Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 is een taalmodel met 9B parameters in de GLM-4 serie, ontwikkeld door THUDM. GLM-4-9B-0414 wordt getraind met dezelfde versterkingsleer- en afstemmingsstrategieën als het grotere 32B tegenhanger, en bereikt hoge prestaties in verhouding tot zijn formaat, waardoor het geschikt is voor implementaties met beperkte middelen die nog steeds sterke taalbegrip en generatiecapaciteiten vereisen."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 is een verbeterde redeneringsvariant van GLM-4-32B, speciaal gebouwd voor diepgaande wiskunde, logica en codegerichte probleemoplossing. Het past uitgebreide versterkingsleren toe (taakspecifiek en op basis van algemene parenvoorkeuren) om de prestaties van complexe meerstaps taken te verbeteren. In vergelijking met het basis GLM-4-32B-model heeft Z1 de mogelijkheden voor gestructureerde redenering en formele domeinen aanzienlijk verbeterd.\n\nDit model ondersteunt het afdwingen van 'denkstappen' via prompt-engineering en biedt verbeterde coherentie voor lange outputformaten. Het is geoptimaliseerd voor agentwerkstromen en ondersteunt lange context (via YaRN), JSON-toolaanroepen en fijnmazige samplingconfiguraties voor stabiele redenering. Zeer geschikt voor gebruikscases die diepgaand nadenken, meerstaps redenering of formele afleiding vereisen."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 is een verbeterde redeneringsvariant van GLM-4-32B, speciaal gebouwd voor diepgaande wiskunde, logica en codegerichte probleemoplossing. Het past uitgebreide versterkingsleren toe (taakspecifiek en op basis van algemene parenvoorkeuren) om de prestaties van complexe meerstaps taken te verbeteren. In vergelijking met het basis GLM-4-32B-model heeft Z1 de mogelijkheden voor gestructureerde redenering en formele domeinen aanzienlijk verbeterd.\n\nDit model ondersteunt het afdwingen van 'denkstappen' via prompt-engineering en biedt verbeterde coherentie voor lange outputformaten. Het is geoptimaliseerd voor agentwerkstromen en ondersteunt lange context (via YaRN), JSON-toolaanroepen en fijnmazige samplingconfiguraties voor stabiele redenering. Zeer geschikt voor gebruikscases die diepgaand nadenken, meerstaps redenering of formele afleiding vereisen."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 is een taalmodel met 9B parameters in de GLM-4 serie, ontwikkeld door THUDM. Het maakt gebruik van technieken die oorspronkelijk zijn toegepast op het grotere GLM-Z1 model, waaronder uitgebre<PERSON> versterkingsleer, parenrangschikking afstemming en training voor redeneringsintensieve taken zoals wiskunde, codering en logica. Ondanks zijn kleinere formaat, presteert het krachtig in algemene redeneringstaken en overtreft het veel open-source modellen op zijn gewichtsniveau."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B is een diep redeneringsmodel met 32B parameters in de GLM-4-Z1 serie, geoptimaliseerd voor complexe, open taken die langdurig nadenken vereisen. Het is gebaseerd op glm-4-32b-0414, met extra versterkingsleerfasen en meerfasige afstemmingsstrategieën, en introduceert de 'reflectieve' capaciteit die is ontworpen om uitgebreide cognitieve verwerking te simuleren. Dit omvat iteratieve redenering, multi-hop analyse en tool-versterkte workflows, zoals zoeken, ophalen en citatie-bewuste synthese.\n\nDit model presteert uitstekend in onderzoeksgericht schrijven, vergelijkende analyses en complexe vraag-en-antwoord situaties. Het ondersteunt functie-aanroepen voor zoek- en navigatiecommando's (`search`, `click`, `open`, `finish`), waardoor het kan worden gebruikt in agent-gebaseerde pipelines. Reflectief gedrag wordt gevormd door een multi-rondencycluscontrole met op regels gebaseerde beloningen en een vertraagd besluitvormingsmechanisme, en is gebaseerd op diepgaande onderzoeksframeworks zoals de interne afstemmingsstack van OpenAI. Deze variant is geschikt voor scenario's die diepgang in plaats van snelheid vereisen."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera is g<PERSON><PERSON><PERSON><PERSON> door de combinatie van DeepSeek-R1 en DeepSeek-V3 (0324), en combineert de redeneringscapaciteiten van R1 met de verbeteringen in token efficiëntie van V3. Het is gebaseerd op de DeepSeek-MoE Transformer architectuur en is geoptimaliseerd voor algemene tekstgeneratietaken.\n\nDit model combineert de voorgetrainde gewichten van de twee bronmodellen om de prestaties in redeneren, efficiëntie en het volgen van instructies in balans te brengen. Het is vrijgegeven onder de MIT-licentie en is bedoeld voor onderzoek en commerciële doeleinden."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena <PERSON> (7B) biedt verbeterde rekenkracht door middel van efficiënte strategieën en modelarchitectuur."}, "tts-1": {"description": "Het nieuwste tekst-naar-spraak model, geoptima<PERSON><PERSON><PERSON> voor snelheid in realtime scenario's."}, "tts-1-hd": {"description": "Het nieuwste tekst-naar-spraak model, geoptimaliseerd voor kwaliteit."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) is geschikt voor verfijnde instructietaken en biedt uitstekende taalverwerkingscapaciteiten."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 <PERSON><PERSON> heeft de industrienormen verbeterd, met prestaties die de concurrentiemodellen en Claude 3 Opus overtreffen, en excelleert in uitgebreide evaluaties, terwijl het de snelheid en kosten van onze middelgrote modellen behoudt."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet is het snelste volgende generatie model van Anthropic. In vergelijking met <PERSON> 3 Haiku heeft Claude 3.7 Sonnet verbeteringen in verschillende vaardigheden en overtreft het de grootste modellen van de vorige generatie, Claude 3 Opus, in veel intellectuele benchmarktests."}, "v0-1.0-md": {"description": "Het v0-1.0-md model is een oudere versie die via de v0 API wordt aangeboden"}, "v0-1.5-lg": {"description": "Het v0-1.5-lg model is geschikt voor geavanceerde denk- of redeneertaken"}, "v0-1.5-md": {"description": "Het v0-1.5-md model is geschikt voor dagelijkse taken en het genereren van gebruikersinterfaces (UI)"}, "wan2.2-t2i-flash": {"description": "Wanxiang 2.2 Flash-versie, het nieuwste model. Volledige upgrades in creativiteit, stabiliteit en realistische textuur, met snelle generatie en hoge kosteneffectiviteit."}, "wan2.2-t2i-plus": {"description": "Wanxiang 2.2 professionele versie, het nieuwste model. Volledige upgrades in creativiteit, stabiliteit en realistische textuur, met rijke details in de gegenereerde beelden."}, "wanx-v1": {"description": "<PERSON><PERSON> tekst-naar-beeld model, <PERSON><PERSON><PERSON><PERSON><PERSON> met het <PERSON> officiële 1.0 algemene model."}, "wanx2.0-t2i-turbo": {"description": "Gespec<PERSON><PERSON>erd in realistische portretten, met gemid<PERSON><PERSON> snelheid en lage kosten. <PERSON><PERSON><PERSON><PERSON><PERSON> met het <PERSON><PERSON> officiële 2.0 Turbo model."}, "wanx2.1-t2i-plus": {"description": "Volledig geüpgraded versie. <PERSON><PERSON><PERSON> met rijk<PERSON> details, iets langzamere snelheid. <PERSON><PERSON><PERSON><PERSON><PERSON> met het <PERSON><PERSON> Wanxiang officiële 2.1 professionele model."}, "wanx2.1-t2i-turbo": {"description": "Volledig geüpgraded versie. Snelle generatie, uitgebreide effecten en hoge algehele kosteneffectiviteit. Overeenkomend met het Tongyi Wanxiang officiële 2.1 Turbo model."}, "whisper-1": {"description": "<PERSON><PERSON><PERSON><PERSON> spraakherkenning<PERSON>del, ondersteunt meertalige spraakherkenning, spraakvertaling en taalherkenning."}, "wizardlm2": {"description": "WizardLM 2 is een ta<PERSON>model van Microsoft AI dat uitblinkt in complexe gesprekken, meertaligheid, inferentie en intelligente assistentie."}, "wizardlm2:8x22b": {"description": "WizardLM 2 is een ta<PERSON>model van Microsoft AI dat uitblinkt in complexe gesprekken, meertaligheid, inferentie en intelligente assistentie."}, "x1": {"description": "Het Spark X1-model zal verder worden geüpgraded, met verbeterde prestaties in redenering, tekstgeneratie en taalbegrip, ter vergelijking met OpenAI o1 en DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 is een ge<PERSON><PERSON><PERSON><PERSON><PERSON> versie van <PERSON>. Het wordt voortdurend voorgetraind met een hoge-kwaliteitscorpus van 500B tokens op basis van <PERSON>, en fijn afgesteld op 3M diverse fijnafstemmingssamples."}, "yi-large": {"description": "<PERSON><PERSON> model met honderden miljarden parameters, biedt superieure vraag- en tekstgeneratiecapaciteiten."}, "yi-large-fc": {"description": "Bouwt voort op het yi-large model en versterkt de mogelijkheden voor functie-aanroepen, geschikt voor verschillende zakelijke scenario's die agent- of workflowopbouw vereisen."}, "yi-large-preview": {"description": "<PERSON><PERSON><PERSON><PERSON> versie, aanbevolen om yi-large (nieuwe versie) te gebruiken."}, "yi-large-rag": {"description": "Een geavanceerde service op basis van het yi-large model, die retrieval en generatietechnologie combineert om nauwkeurige antwoorden te bieden en realtime informatie van het hele web te doorzoeken."}, "yi-large-turbo": {"description": "Biedt een uitstekende prijs-kwaliteitverhouding en prestaties. Voert een nauwkeurige afstemming uit op basis van prestaties, redeneersnelheid en kosten."}, "yi-lightning": {"description": "Het nieuwste high-performance model, dat zorgt voor hoogwaardige output met aanzienlijke versnelling van de redeneringssnelheid."}, "yi-lightning-lite": {"description": "<PERSON><PERSON><PERSON> versie, aanbevolen voor gebruik met yi-lightning."}, "yi-medium": {"description": "Gemiddeld formaat model met geoptimaliseer<PERSON> afstem<PERSON>, biedt een evenwichtige prijs-kwaliteitverhouding. Diep geoptimaliseerde instructievolgcapaciteiten."}, "yi-medium-200k": {"description": "200K ultra-lange contextvenster, biedt diepgaand begrip en generatiecapaciteiten voor lange teksten."}, "yi-spark": {"description": "<PERSON> maar krachtig, een lichtge<PERSON>t en snelle model. Biedt versterkte wiskundige berekeningen en codeercapaciteiten."}, "yi-vision": {"description": "Model voor complexe visuele taken, biedt hoge prestaties in beeldbegrip en analyse."}, "yi-vision-v2": {"description": "Complex visietakenmodel dat hoge prestaties biedt in begrip en analyse op basis van meerdere afbeeldingen."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 is een basis model speciaal ontworpen voor agenttoepassingen, gebruikmakend van een Mixture-of-Experts (MoE) architectuur. Het is diep geoptimaliseerd voor toolaanroepen, web browsing, software engineering en frontend programmeren, en ondersteunt naadloze integratie met code-agents zoals <PERSON> en Roo Code. GLM-4.5 gebruikt een hybride redeneermodus en is geschikt voor complexe redenering en dagelijks gebruik."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air is een basis model speciaal ontworpen voor agenttoepassingen, gebruikmakend van een Mixture-of-Experts (MoE) architectuur. Het is diep geoptimaliseerd voor toolaanroepen, web browsing, software engineering en frontend programmeren, en ondersteunt naadloze integratie met code-agents zoals <PERSON> en Roo Code. GLM-4.5 gebruikt een hybride redeneermodus en is geschikt voor complexe redenering en dagelijks gebruik."}}