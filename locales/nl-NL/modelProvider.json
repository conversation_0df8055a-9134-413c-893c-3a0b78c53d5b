{"azure": {"azureApiVersion": {"desc": "De API-versie van <PERSON>, volgt het formaat YYYY-MM-DD, raadpleeg [de nieuwste versie](https://learn.microsoft.com/nl-nl/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> ophale<PERSON>", "title": "Azure API Versie"}, "empty": "<PERSON><PERSON><PERSON> een model-ID in om het eerste model toe te voegen", "endpoint": {"desc": "Dit waarde kan gevonden worden in de 'Sleutels en eindpunt' sectie wanneer je een bron in Azure Portal controleert", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API Adres"}, "modelListPlaceholder": "Selecteer of voeg het OpenAI-model toe dat u hebt ingezet", "title": "Azure OpenAI", "token": {"desc": "Dit waarde kan gevonden worden in de 'Sleutels en eindpunt' sectie wanneer je een bron in Azure Portal controleert. Je kunt KEY1 of KEY2 gebruiken", "placeholder": "Azure API Key", "title": "API Key"}}, "azureai": {"azureApiVersion": {"desc": "De API-vers<PERSON> van <PERSON>, volgens het formaat YYYY-MM-DD. Raadpleeg de [laatste versie](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> ophale<PERSON>", "title": "Azure API-versie"}, "endpoint": {"desc": "Vind het Azure AI-model inferentie-eindpunt in het overzicht van het Azure AI-project", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Azure AI-eindpunt"}, "title": "Azure OpenAI", "token": {"desc": "Vind de API-sleutel in het overzicht van het Azure AI-project", "placeholder": "Azure-sleutel", "title": "<PERSON><PERSON><PERSON><PERSON>"}}, "bedrock": {"accessKeyId": {"desc": "Voer AWS Access Key Id in", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "Test of AccessKeyId / SecretAccessKey correct zijn ingevuld"}, "region": {"desc": "Voer AWS Region in", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Voer AWS Secret Access Key in", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Als je AWS SSO/STS gebru<PERSON>t, voer dan je AWS <PERSON><PERSON> in", "placeholder": "A<PERSON>", "title": "A<PERSON> (optioneel)"}, "title": "Bedrock", "unlock": {"customRegion": "Aangepaste regio", "customSessionToken": "Aangepast<PERSON> se<PERSON>", "description": "Voer uw AWS AccessKeyId / SecretAccessKey in om een sessie te starten. De app zal uw verificatiegegevens niet opslaan", "imageGenerationDescription": "Voer je AWS AccessKeyId / SecretAccessKey in om te beginnen met genereren. De applicatie slaat je authenticatiegegevens niet op", "title": "Gebruik aangepaste Bedrock-verificatiegegevens"}}, "cloudflare": {"apiKey": {"desc": "Voer Cloudflare API Key in", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "<PERSON><PERSON>r uw Cloudflare-account ID of een custom API-URL in", "placeholder": "Cloudflare-account ID / custom API-URL", "title": "Cloudflare-account ID / API-URL"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Vul je API-sleutel in", "title": "API-sleutel"}, "basicTitle": "Basisinformatie", "configTitle": "Configuratie-informatie", "confirm": "Nieuw <PERSON>", "createSuccess": "Succesvol aangemaakt", "description": {"placeholder": "Beschrijving van de provider (optioneel)", "title": "Beschrijving van de provider"}, "id": {"desc": "<PERSON>en unieke identificatie voor de dienstverlener, kan na creatie niet meer worden gewijzigd", "format": "Mag alleen cijfers, kleine letters, koppeltekens (-) en onderstrepingstekens (_) bevatten", "placeholder": "Gebruik alleen kleine letters, bijvoorbeeld openai, kan niet worden gewijzigd na a<PERSON>ak", "required": "Vul de provider ID in", "title": "Provider ID"}, "logo": {"required": "Upload een correcte provider-logo", "title": "Provider-logo"}, "name": {"placeholder": "Voer de weergavenaam van de provider in", "required": "Vul de naam van de provider in", "title": "<PERSON><PERSON>"}, "proxyUrl": {"required": "Vul het proxyadres in", "title": "Proxy-adres"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Selecteer het SDK-type", "title": "Aanvraagformaat"}, "title": "Maak een aangepaste AI-provider"}, "github": {"personalAccessToken": {"desc": "Vul je Github PAT in, klik [hier](https://github.com/settings/tokens) om er een te maken", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Vul je Hugging<PERSON><PERSON> in, klik [hier](https://huggingface.co/settings/tokens) om er een te maken", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "list": {"title": {"disabled": "Dienstverlener niet ingeschakeld", "enabled": "Dienstverlener ingeschakeld"}}, "menu": {"addCustomProvider": "Voeg aangepaste provider toe", "all": "Alles", "list": {"disabled": "<PERSON><PERSON> ing<PERSON>", "enabled": "Ingeschakeld"}, "notFound": "Geen z<PERSON>kresultaten gevonden", "searchProviders": "Zoek providers...", "sort": "Aangepaste sortering"}, "ollama": {"checker": {"desc": "Test of het proxyadres correct is ingevuld", "title": "Connectiviteitscontrole"}, "customModelName": {"desc": "Voeg aangepaste modellen toe, gebruik een komma (,) om meerdere modellen te scheiden", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Aangepaste Modelnamen"}, "download": {"desc": "<PERSON><PERSON><PERSON> is het model aan het downloaden, sluit deze pagina alstublieft niet af. Bij een herstart zal het downloaden op de onderbroken plaats verdergaan.", "failed": "Het <PERSON>en van het model is mislukt. Controleer uw netwerk of de Ollama-instellingen en probeer het opnieuw.", "remainingTime": "Overge<PERSON><PERSON> tijd", "speed": "Downloadsnelheid", "title": "Model {{model}} wordt gedownload"}, "endpoint": {"desc": "Moet http(s):// bevatten, kan leeg gelaten worden als lokaal niet specifiek opgegeven", "title": "Interface Proxyadres"}, "title": "Ollama", "unlock": {"cancel": "Annuleer download", "confirm": "Downloaden", "description": "<PERSON><PERSON>r je Ollama model label in om door te gaan met de sessie", "downloaded": "{{completed}} / {{total}}", "starting": "Downloaden starten...", "title": "Download het opgegeven Ollama model"}}, "providerModels": {"config": {"aesGcm": "Je sleutel en proxy-adres worden versleuteld met <1>AES-GCM</1> encryptie-algoritme", "apiKey": {"desc": "Vul je {{name}} API-sleutel in", "descWithUrl": "Vul je {{name}} API-sleutel in, <3>klik hier om deze te verkrijgen</3>", "placeholder": "{{name}} API-sleutel", "title": "API-sleutel"}, "baseURL": {"desc": "Moet http(s):// bevatten", "invalid": "<PERSON><PERSON><PERSON> een geldige URL in", "placeholder": "https://your-proxy-url.com/v1", "title": "API-proxy-adres"}, "checker": {"button": "Controleer", "desc": "Test of de API-sleutel en proxy-adres correct zijn ingevuld", "pass": "<PERSON>e geslaagd", "title": "Connectiviteitstest"}, "fetchOnClient": {"desc": "Clientaanvraagmodus zal sessieaanvragen rechtstreeks vanuit de browser initiëren, wat de responssnelheid kan verbeteren", "title": "Geb<PERSON><PERSON>"}, "helpDoc": "Configuratiehandleiding", "responsesApi": {"desc": "Gebruik de nieuwe generatie OpenAI-aanvraagformaatstandaard om geavanceerde functies zoals keten van gedachten te ontgrendelen", "title": "Gebruik Responses API-standaard"}, "waitingForMore": "<PERSON><PERSON> modellen zijn in <1>planning voor integratie</1>, blijf op de hoogte"}, "createNew": {"title": "Maak een aangepast AI-model"}, "item": {"config": "Configureer model", "customModelCards": {"addNew": "Maak en voeg {{id}} model toe", "confirmDelete": "Je staat op het punt dit aangepaste model te verwijderen, na verwijdering kan het niet worden hersteld, wees voorzi<PERSON>."}, "delete": {"confirm": "Bevestig ver<PERSON><PERSON><PERSON> van model {{displayName}}?", "success": "Ver<PERSON>jdering geslaagd", "title": "Verwijder model"}, "modelConfig": {"azureDeployName": {"extra": "Het veld dat daadwerkelijk wordt aangevraagd in Azure OpenAI", "placeholder": "V<PERSON>r de modelimplementatienaam in Azure in", "title": "Modelimplementatienaam"}, "deployName": {"extra": "Dit veld wordt als model-ID verzonden bij het indienen van een verzoek", "placeholder": "<PERSON><PERSON><PERSON> of ID van het daadwerkelijk gedeployde model in", "title": "Modeldeploynaam"}, "displayName": {"placeholder": "<PERSON><PERSON><PERSON> de we<PERSON><PERSON><PERSON><PERSON><PERSON> van het model in, bijvoorbeeld ChatGPT, GPT-4, enz.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van het model"}, "files": {"extra": "De huidige bestandsuploadimplementatie is slechts een hackoplossing, alleen voor eigen gebruik. Volledige bestandsuploadcapaciteit komt later beschikbaar.", "title": "Ondersteuning voor bestandsupload"}, "functionCall": {"extra": "Deze configuratie schakelt alleen de mogelijkheid in voor het model om tools te gebruiken, waardoor het mogelijk is om plug-ins voor tools aan het model toe te voegen. Of het model daadwerkelijk tools kan gebruiken, hangt echter volledig af van het model zelf; test de bruikbaarheid zelf.", "title": "Ondersteuning voor het gebruik van tools"}, "id": {"extra": "Kan niet worden gewij<PERSON>d na creatie, wordt gebruikt als model-id bij het aan<PERSON><PERSON><PERSON> van AI", "placeholder": "<PERSON><PERSON>r model-id in, bijvoorbeeld gpt-4o of claude-3.5-sonnet", "title": "Model ID"}, "modalTitle": "Configurat<PERSON> model", "reasoning": {"extra": "Deze configuratie schakelt alleen de mogelijkheid voor diepgaand denken van het model in. Het specifieke effect hangt volledig af van het model zelf, test zelf of dit model in staat is tot bruikbaar diepgaand denken.", "title": "Ondersteuning voor diepgaand denken"}, "tokens": {"extra": "Stel het maximale aantal tokens in dat door het model wordt ondersteund", "title": "Maximale contextvenster", "unlimited": "Onbeperkt"}, "vision": {"extra": "Deze configuratie zal alleen de afbeeldinguploadcapaciteit in de applicatie inschakelen, of herkenning wordt ondersteund hangt volledig af van het model zelf, test de beschikbaarheid van de visuele herkenningscapaciteit van dit model zelf.", "title": "Ondersteuning voor visuele herkenning"}}, "pricing": {"image": "${{amount}}/Afbeelding", "inputCharts": "${{amount}}/M <PERSON>s", "inputMinutes": "${{amount}}/Minuten", "inputTokens": "Invoer ${{amount}}/M", "outputTokens": "Uitvoer ${{amount}}/M"}, "releasedAt": "Uitgebracht op {{releasedAt}}"}, "list": {"addNew": "<PERSON> toe<PERSON>egen", "disabled": "<PERSON><PERSON> ing<PERSON>", "disabledActions": {"showMore": "Toon alles"}, "empty": {"desc": "Maak een aangepast model of haal een model op om te beginnen met g<PERSON><PERSON><PERSON><PERSON>.", "title": "<PERSON><PERSON> be<PERSON> modellen"}, "enabled": "Ingeschakeld", "enabledActions": {"disableAll": "Alle uitschakelen", "enableAll": "Alle inschakelen", "sort": "Aangepaste model sortering"}, "enabledEmpty": "<PERSON><PERSON> ing<PERSON><PERSON><PERSON><PERSON> modellen, schakel de modellen hieronder in die je leuk vindt~", "fetcher": {"clear": "Verwijder de opgehaalde modellen", "fetch": "Haal modellenlijst op", "fetching": "<PERSON><PERSON> met het op<PERSON><PERSON> van de modellenlijst...", "latestTime": "Laatste update tijd: {{time}}", "noLatestTime": "Lijst nog niet op<PERSON>"}, "resetAll": {"conform": "Weet je zeker dat je alle wijzigingen van het huidige model wilt resetten? Na de reset zal de huidige modellenlijst terugkeren naar de standaardstatus", "success": "Resetten geslaagd", "title": "Reset alle wijzigingen"}, "search": "Zoek modellen...", "searchResult": "Gevonden {{count}} modellen", "title": "Modellenlijst", "total": "In totaal {{count}} model<PERSON> be<PERSON>"}, "searchNotFound": "Geen z<PERSON>kresultaten gevonden"}, "sortModal": {"success": "Sortering succesvol bijgewerkt", "title": "Aangepaste sortering", "update": "Bijwerken"}, "updateAiProvider": {"confirmDelete": "Je staat op het punt deze AI-provider te verwijderen, na verwijdering kan deze niet worden hersteld, bevestig je verwijdering?", "deleteSuccess": "Ver<PERSON>jdering geslaagd", "tooltip": "Werk basisconfiguratie van provider bij", "updateSuccess": "Bijwerking geslaagd"}, "updateCustomAiProvider": {"title": "Bijwerken van de configuratie van de aangepaste AI-provider"}, "vertexai": {"apiKey": {"desc": "Vul je Vertex AI-sleutels in", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Vertex AI-sleutels"}}, "zeroone": {"title": "01.<PERSON> <PERSON>"}, "zhipu": {"title": "Intelligent Spectrum"}}