{"ai21": {"description": "AI21 Labs bouwt basismodellen en AI-systemen voor bedrijven, en versnelt de toepassing van generatieve AI in de productie."}, "ai360": {"description": "360 AI is een AI-model- en serviceplatform gelanceerd door het bedrijf 360, dat verschillende geavanceerde modellen voor natuurlijke taalverwerking biedt, waaronder 360GPT2 Pro, 360GPT Pro, 360GPT Turbo en 360GPT Turbo Responsibility 8K. Deze modellen combineren grootschalige parameters en multimodale capaciteiten, en worden breed toegepast in tekstgeneratie, seman<PERSON><PERSON> begrip, dialoogsystemen en codegeneratie. Met flexibele prijsstrategieën voldoet 360 AI aan diverse gebruikersbehoeften, ondersteunt het ontwikkelaars bij integratie en bevordert het de innovatie en ontwikkeling van intelligente toepassingen."}, "aihubmix": {"description": "AiHubMix biedt via een uniforme API-toegang tot diverse AI-modellen."}, "anthropic": {"description": "Anthropic is een bedrijf dat zich richt op onderzoek en ontwikkeling van kunstmatige intelligentie, en biedt een reeks geavanceerde taalmodellen aan, zoals <PERSON> 3.5 Sonnet, <PERSON> 3 Son<PERSON>, Claude 3 Opus en Claude 3 Haiku. Deze modellen bereiken een ideale balans tussen intelligentie, snelheid en kosten, en zijn geschikt voor een breed scala aan toepassingen, van bedrijfswerkbelasting tot snelle respons. <PERSON> 3.5 <PERSON><PERSON>, als hun nieuwste model, preste<PERSON> uitstekend in verschillende evaluaties, terwijl het een hoge kosteneffectiviteit behoudt."}, "azure": {"description": "Azure biedt een scala aan geavanceerde AI-modellen, waaronder GPT-3.5 en de nieuwste GPT-4-serie, die verschillende datatypes en complexe taken on<PERSON>teunen, met een focus op veilige, betrouwbare en duurzame AI-oplossingen."}, "azureai": {"description": "Azure biedt een verscheidenheid aan geavanceerde AI-modellen, waaronder GPT-3.5 en de nieuwste GPT-4-serie, die verschillende datatypes en complexe taken ondersteunt, met een focus op veilige, betrouwbare en duurzame AI-oplossingen."}, "baichuan": {"description": "Baichuan Intelligent is een bedrij<PERSON> dat zich richt op de ontwikkeling van grote modellen voor kunstmatige intelligentie, wiens modellen uitblinken in Chinese taken zoals kennisencyclopedieën, lange tekstverwerking en generatieve creatie, en de mainstream modellen uit het buitenland overtreffen. Baichuan Intelligent heeft ook toonaangevende multimodale capaciteiten en presteert uitstekend in verschillende autoritatieve evaluaties. Hun modellen omvatten Baichuan 4, Baichuan 3 Turbo en Baichuan 3 Turbo 128k, die zijn geoptimaliseerd voor verschillende toepassingsscenario's en kosteneffectieve oplossingen bieden."}, "bedrock": {"description": "Bedrock is een dienst van <PERSON> AWS die zich richt op het bieden van geavanceerde AI-taalmodellen en visuele modellen voor bedrijven. De modellenfamilie omvat de Claude-se<PERSON> van An<PERSON>ic, de Llama 3.1-<PERSON><PERSON> van <PERSON>, en meer, met opties variërend van lichtgewicht tot hoge prestaties, en ondersteunt tekstgeneratie, dialogen, beeldverwerking en meer, geschikt voor bedrijfsapplicaties van verschillende schalen en behoeften."}, "cloudflare": {"description": "Voer machine learning-<PERSON><PERSON> a<PERSON>, aangedreven door serverloze GPU's, uit op het wereldwijde netwerk van Cloudflare."}, "cohere": {"description": "Cohere biedt u de meest geavanceerde meertalige modellen, geavanceerde zoekfunctionaliteit en een op maat gemaakt AI-werkruimte voor moderne bedrijven - alles geïntegreerd in een veilig platform."}, "deepseek": {"description": "DeepSeek is een bedri<PERSON><PERSON> dat zich richt op onderzoek en toepassing van kunstmatige intelligentietechnologie, en hun nieuwste model DeepSeek-V2.5 combineert algemene dialoog- en codeverwerkingscapaciteiten, met significante verbeteringen in het afstemmen op menselijke voorkeuren, schrijfopdrachten en het volgen van instructies."}, "fal": {"description": "Generatief mediaplatform voor ontwikkelaars"}, "fireworksai": {"description": "Fireworks AI is een too<PERSON><PERSON>evende aanbieder van geavanceerde taalmodellen, met een focus op functionele aanroepen en multimodale verwerking. Hun nieuwste model Firefunction V2 is gebaseerd op Llama-3 en geoptimaliseerd voor functieaanroepen, dialogen en het volgen van instructies. Het visuele taalmodel FireLLaVA-13B ondersteunt gemengde invoer van afbeeldingen en tekst. Andere opmerkelijke modellen zijn de Llama-serie en de Mixtral-serie, die efficiënte ondersteuning bieden voor meertalig volgen van instructies en genereren."}, "giteeai": {"description": "Gitee AI's Serverless API biedt AI ontwikkelaars een out of the box grote model inference API service."}, "github": {"description": "Met GitHub-modellen kunnen ontwikkelaars AI-ingenieurs worden en bouwen met de toonaangevende AI-modellen in de industrie."}, "google": {"description": "<PERSON> Gemini-serie van Google is hun meest geavance<PERSON><PERSON>, algemene AI-modellen, ontwikkeld door Google DeepMind, speciaal ontworpen voor multimodale toepassingen, en ondersteunt naadloze begrip en verwerking van tekst, code, afbeeldingen, audio en video. Geschikt voor verschillende omgevingen, van datacenters tot mobiele apparaten, verhoogt het de efficiëntie en toepasbaarheid van AI-modellen aanzienlijk."}, "groq": {"description": "De LPU-inferentie-engine van Groq presteert uitstekend in de nieuwste onafhankelijke benchmarktests voor grote taalmodellen (LLM), en herdefinieert de normen voor AI-oplossingen met zijn verbazingwekkende snelheid en efficiëntie. Groq is een vertegenwoordiger van onmiddellijke inferentiesnelheid en toont goede prestaties in cloudgebaseerde implementaties."}, "higress": {"description": "Higress is een cloud-native API-gateway, ontwikkeld binnen Alibaba om de nadelige effecten van Tengine reload op langdurige verbindingen en de onvoldoende load balancing capaciteiten van gRPC/Dubbo aan te pakken."}, "huggingface": {"description": "HuggingFace Inference API biedt een snelle en gratis manier om duizenden modellen te verkennen voor verschillende taken. Of u nu prototypes voor nieuwe applicaties ontwerpt of de mogelijkheden van machine learning uitprobeert, deze API geeft u directe toegang tot hoogpresterende modellen in meerdere domeinen."}, "hunyuan": {"description": "Een door Tencent ontwikkeld groot taalmodel, dat beschikt over krachtige Chinese creatiecapaciteiten, logische redeneervaardigheden in complexe contexten, en betrouwbare taakuitvoeringscapaciteiten."}, "infiniai": {"description": "Hoogwaardige, gebruiksvriendelijke en veilige grote modelservices voor app-ontwikkelaars, die de hele processtroom van het ontwikkelen tot het implementeren van grote modellen dekken."}, "internlm": {"description": "Een open-source organisatie die zich richt op onderzoek en ontwikkeling van tools voor grote modellen. Biedt een efficiënt en gebruiksvriendelijk open-source platform voor alle AI-ontwikkelaars, zodat de meest geavanceerde modellen en algoritmische technologieën binnen handbereik zijn."}, "jina": {"description": "Jina AI, opgericht in 2020, is een too<PERSON><PERSON>evend zoek-AI-bedrij<PERSON>. Ons zoekplatform bevat vectormodellen, herschikkers en kleine taalmodellen, die bedrijven helpen betrouwbare en hoogwaardige generatieve AI- en multimodale zoektoepassingen te bouwen."}, "lmstudio": {"description": "LM Studio is een desktopapplicatie voor het ontwikkelen en experimenteren met LLM's op uw computer."}, "minimax": {"description": "MiniMax is een algemeen kunstmatige intelligentietechnologie<PERSON><PERSON><PERSON><PERSON> dat in 2021 is op<PERSON><PERSON><PERSON>, en zich richt op co-creatie van intelligentie met gebruikers. MiniMax heeft verschillende multimodale algemene grote modellen ontwikkeld, waaronder een MoE-tekstgrootmodel met triljoenen parameters, een spraakgrootmodel en een afbeeldingsgrootmodel. Ze hebben ook toepassingen zoals Conch AI gelanceerd."}, "mistral": {"description": "Mistral biedt geavanceerde algemene, professionele en onderzoeksmodellen, die breed worden toegepast in complexe redenering, meertalige taken, codegeneratie en meer. Via functionele aanroepinterfaces kunnen gebruikers aangepaste functies integreren voor specifieke toepassingen."}, "modelscope": {"description": "ModelScope is een door Alibaba Cloud gelanceerd platform voor model-als-een-service, dat een breed scala aan AI-modellen en inferentiediensten biedt."}, "moonshot": {"description": "Moonshot is een open platform gelanceerd door Beijing Dark Side Technology Co., Ltd., dat verschillende modellen voor natuurlijke taalverwerking biedt, met een breed toepassingsgebied, waaronder maar niet beperkt tot contentcreatie, academisch onderzoek, slimme aanbevelingen, medische diagnose, en ondersteunt lange tekstverwerking en complexe generatietaken."}, "novita": {"description": "Novita AI is een platform dat API-diensten biedt voor verschillende grote taalmodellen en AI-beeldgeneratie, flexibel, betrouwbaar en kosteneffectief. Het ondersteunt de nieuwste open-source modellen zoals Llama3 en Mistral, en biedt een uitgebreide, gebruiksvriendelijke en automatisch schaalbare API-oplossing voor de ontwikkeling van generatieve AI-toepassingen, geschikt voor de snelle groei van AI-startups."}, "nvidia": {"description": "NVIDIA NIM™ biedt containers voor zelf-gehoste GPU-versnelde inferentie-microservices, die de implementatie van voorgetrainde en aangepaste AI-modellen in de cloud, datacenters, RTX™ AI-pc's en werkstations ondersteunen."}, "ollama": {"description": "De modellen van Ollama bestrijken een breed scala aan geb<PERSON>en, wa<PERSON><PERSON> codegeneratie, wiskundige berekeningen, meertalige verwerking en interactieve dialogen, en voldoen aan de diverse behoeften van bedrijfs- en lokale implementaties."}, "openai": {"description": "OpenAI is 's werelds toonaangevende onderzoeksinstituut op het gebied van kunstmatige intelligentie, wiens ontwikkelde modellen zoals de GPT-serie de grenzen van natuurlijke taalverwerking verleggen. OpenAI streeft ernaar verschillende industrieën te transformeren door middel van innovatieve en efficiënte AI-oplossingen. Hun producten bieden opmerkelijke prestaties en kosteneffectiviteit, en worden op grote schaal gebruikt in onderzoek, commercie en innovatieve toepassingen."}, "openrouter": {"description": "OpenRouter is een serviceplatform dat verschillende vooraanstaande grote modelinterfaces biedt, ondersteunt OpenAI, Anthropic, LLaMA en meer, en is geschikt voor diverse ontwikkelings- en toepassingsbehoeften. Gebruikers kunnen flexibel het optimale model en de prijs kiezen op basis van hun behoeften, wat de AI-ervaring verbetert."}, "perplexity": {"description": "Perplexity is een toonaangevende aanbieder van dialooggeneratiemodellen, die verschillende geavanceerde Llama 3.1-model<PERSON> aanbiedt, die zowel online als offline toepassingen ondersteunen, en bijzonder geschikt zijn voor complexe natuurlijke taalverwerkingstaken."}, "ppio": {"description": "PPIO biedt stabiele en kosteneffectieve open source model API-diensten, die ondersteuning bieden voor de volledige DeepSeek-serie, Llama, Qwen en andere toonaangevende grote modellen in de industrie."}, "qiniu": {"description": "<PERSON><PERSON> is een leidende cloudserviceprovider die hoogwaardige, stabiele en kosteneffectieve real-time en batch AI-inferentie-API's biedt, inclusief model<PERSON> van Alibaba, met flexibele opties voor het bouwen en toepassen van AI-toepassingen."}, "qwen": {"description": "<PERSON><PERSON> is een door Alibaba Cloud zelf ontwikkeld grootschalig taalmodel met krachtige mogelijkheden voor natuurlijke taalbegrip en -generatie. Het kan verschillende vragen beantwoorden, te<PERSON><PERSON><PERSON><PERSON>, meningen uiten, code schrij<PERSON>, en speelt een rol in verschillende domeinen."}, "sambanova": {"description": "SambaNova Cloud stelt ontwikkelaars in staat om eenvoudig gebruik te maken van de beste open-source modellen en te profiteren van de snelste inferentiesnelheden."}, "search1api": {"description": "Search1API biedt toegang tot de DeepSeek-serie modellen die naar behoefte zelf kunnen worden verbonden, inclusief de standaard- en snelle versies, met ondersteuning voor verschillende modelgroottes."}, "sensenova": {"description": "SenseNova, ondersteund door de krachtige infrastructuur van SenseTime, biedt efficiënte en gebruiksvriendelijke full-stack modelservices."}, "siliconcloud": {"description": "SiliconFlow streeft ernaar AGI te versnellen ten behoeve van de men<PERSON>, door de efficiëntie van grootschalige AI te verbeteren met een gebruiksvriendelijke en kosteneffectieve GenAI-stack."}, "spark": {"description": "iFlytek's Xinghuo-grootmodel biedt krachtige AI-capaciteiten in meerdere domeinen en talen, en maakt gebruik van geavanceerde natuurlijke taalverwerkingstechnologie om innovatieve toepassingen te bouwen die geschikt zijn voor slimme hardware, slimme gezondheidszorg, slimme financiën en andere verticale scenario's."}, "stepfun": {"description": "De Class Star-grootmodel heeft toonaangevende multimodale en complexe redeneringscapaciteiten, ondersteunt het begrijpen van zeer lange teksten en beschikt over krachtige autonome zoekmachinefunctionaliteit."}, "taichu": {"description": "Het Instituut voor Automatisering van de Chinese Academie van Wetenschappen en het Wuhan Instituut voor Kunstmatige Intelligentie hebben een nieuwe generatie multimodale grote modellen gelanceerd, die ondersteuning bieden voor meerdaagse vraag-en-antwoord, tekstcreatie, beeldgeneratie, 3D-begrip, signaalanalyse en andere uitgebreide vraag-en-antwoordtaken, met ster<PERSON><PERSON> cognitieve, begrip en creatiecapaciteiten, wat zorgt voor een geheel nieuwe interactie-ervaring."}, "tencentcloud": {"description": "De atomische capaciteiten van de kennisengine (LLM Knowledge Engine Atomic Power) zijn geb<PERSON>erd op de ontwikkeling van de kennisengine en bieden een volledige keten van kennisvraag- en antwoordmogelijkheden, gericht op bedrijven en ontwikkelaars. U kunt verschillende atomische capaciteiten gebruiken om uw eigen modelservice samen te stellen, door gebruik te maken van diensten zoals documentanalyse, splitsing, embedding en meervoudige herschrijving, om een op maat gemaakte AI-oplossing voor uw bedrijf te creëren."}, "togetherai": {"description": "Together AI streeft ernaar toonaangevende prestaties te bereiken door middel van innovatieve AI-modellen, en biedt uitgebreide aanpassingsmogelijkheden, wa<PERSON><PERSON> ondersteuning voor snelle schaling en intuïtieve implementatieprocessen, om aan de verschillende behoeften van bedrijven te voldoen."}, "upstage": {"description": "Upstage richt zich op het ontwikkelen van AI-modellen voor verschillende zakelijke behoeften, waaronder Solar LLM en document AI, met als doel het realiseren van kunstmatige algemene intelligentie (AGI). Het creëert eenvoudige dialoogagenten via de Chat API en ondersteunt functionele aanroepen, vertalingen, insluitingen en specifieke domeintoepassingen."}, "v0": {"description": "v0 is een pair programming-assistent; je hoeft alleen je ideeën in natuurlijke taal te beschrijven, en het genereert code en gebruikersinterfaces (UI) voor je project."}, "vertexai": {"description": "<PERSON> Gemini-serie van Google is zijn meest gea<PERSON>, algemene AI-modellen, ontwikkeld door Google DeepMind. Deze modellen zijn ontworpen voor multimodale toepassingen en ondersteunen naadloze begrip en verwerking van tekst, code, afbeeldingen, audio en video. Ze zijn geschikt voor verschillende omgevingen, van datacenters tot mobiele apparaten, en verbeteren aanzienlijk de efficiëntie en toepasbaarheid van AI-modellen."}, "vllm": {"description": "vLLM is een snelle en gebruiksvriendelijke bibliotheek voor LLM-inferentie en -diensten."}, "volcengine": {"description": "Het ontwikkelingsplatform voor de grote modellenservice van ByteDance, dat een breed scala aan functies biedt, veilig is en concurrerende prijzen heeft voor modelaanroepdiensten. Het biedt ook end-to-end functionaliteiten zoals modelgegevens, fine-tuning, inferentie en evaluatie, om de ontwikkeling van uw AI-toepassingen volledig te ondersteunen."}, "wenxin": {"description": "Een enterprise-grade, alles-in-één platform voor de ontwikkeling en service van grote modellen en AI-native applicaties, dat de meest uitgebreide en gebruiksvriendelijke toolchain biedt voor de ontwikkeling van generatieve kunstmatige intelligentiemodellen en applicaties."}, "xai": {"description": "xAI is ein bedrijf dat zich richt op het bouwen van kunstmatige intelligentie om menselijke wetenschappelijke ontdekkingen te versnellen. Onze missie is om onze gezamenlijke begrip van het universum te bevorderen."}, "xinference": {"description": "Xorbits Inference (Xinference) is een open-source platform dat is ontworpen om de uitvoering en integratie van verschillende AI-modellen te vereenvoudigen. Met Xinference kunt u inferentie uitvoeren met behul<PERSON> van <PERSON> open-source LLM, embeddingsmodel of multimodaal model in een cloud- of lokale omgeving, en krachtige AI-toepassingen creëren."}, "zeroone": {"description": "01.<PERSON> richt zich op kunstmatige intelligentietechnologie in het tijdperk van AI 2.0, en bevordert sterk de innovatie en toepassing van 'mens + kunstmatige intelligentie', met be<PERSON><PERSON> van krachtige modellen en geavanceerde AI-technologie om de productiviteit van de mens te verbeteren en technologische capaciteiten te realiseren."}, "zhipu": {"description": "Zhipu AI biedt een open platform voor multimodale en taalmodellen, dat een breed scala aan AI-toepassingsscenario's ondersteunt, waaronder tekstverwerking, beeldbegrip en programmeerondersteuning."}}