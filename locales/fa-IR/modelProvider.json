{"azure": {"azureApiVersion": {"desc": "نسخه API Azure، با فرمت YYYY-MM-DD، برای مشاهده [آخرین نسخه](https://learn.microsoft.com/fa-ir/azure/ai-services/openai/reference#chat-completions)", "fetch": "دریافت لیست", "title": "نسخه API Azure"}, "empty": "لطفاً شناسه مدل را وارد کنید تا اولین مدل را اضافه کنید", "endpoint": {"desc": "هنگام بررسی منابع از پورتال Azure، این مقدار را می‌توانید در بخش «کلیدها و نقاط پایانی» پیدا کنید", "placeholder": "https://docs-test-001.openai.azure.com", "title": "آدرس API Azure"}, "modelListPlaceholder": "لطفاً مدل OpenAI مستقر شده خود را انتخاب یا اضافه کنید", "title": "Azure OpenAI", "token": {"desc": "هنگام بررسی منابع از پورتال Azure، این مقدار را می‌توانید در بخش «کلیدها و نقاط پایانی» پیدا کنید. می‌توانید از KEY1 یا KEY2 استفاده کنید", "placeholder": "کلید API Azure", "title": "کلید API"}}, "azureai": {"azureApiVersion": {"desc": "نسخه API آژور، با فرمت YYYY-MM-DD، برای مشاهده [آخرین نسخه](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "دریافت لیست", "title": "نسخه API آژور"}, "endpoint": {"desc": "نقطه پایانی استنتاج مدل آژور AI را از نمای کلی پروژه آژور AI پیدا کنید", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "نقطه پایانی آژور AI"}, "title": "آژور OpenAI", "token": {"desc": "کلید API را از نمای کلی پروژه آژور AI پیدا کنید", "placeholder": "کلید آژور", "title": "<PERSON><PERSON><PERSON><PERSON>"}}, "bedrock": {"accessKeyId": {"desc": "AWS Access Key Id را وارد کنید", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "بررسی کنید که آیا AccessKeyId / SecretAccessKey به درستی وارد شده است"}, "region": {"desc": "AWS Region را وارد کنید", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "AWS Secret Access Key را وارد کنید", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "اگر از AWS SSO/STS استفاده می‌کنید، لطفاً AWS Session Token خود را وارد کنید", "placeholder": "AWS Session Token", "title": "AWS Session Token (اختیاری)"}, "title": "Bedrock", "unlock": {"customRegion": "منط<PERSON><PERSON> خدمات سفارشی", "customSessionToken": "توکن نشست سفارشی", "description": "برای شروع جلسه، AWS AccessKeyId / SecretAccessKey خود را وارد کنید. برنامه تنظیمات احراز هویت شما را ذخیره نخواهد کرد", "imageGenerationDescription": "برای شروع تولید، AWS AccessKeyId / SecretAccessKey خود را وارد کنید. برنامه پیکربندی احراز هویت شما را ذخیره نمی‌کند.", "title": "استفاده از اطلاعات احراز هویت سفارشی Bedrock"}}, "cloudflare": {"apiKey": {"desc": "لطفاً کلید API Cloudflare را وارد کنید", "placeholder": "کلید API Cloudflare", "title": "کلید API Cloudflare"}, "baseURLOrAccountID": {"desc": "شناسه حساب Cloudflare یا آدرس API سفارشی را وارد کنید", "placeholder": "شناسه حسا<PERSON> / آدرس API سفارشی", "title": "شناسه حساب <PERSON> / آدرس API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "لطفاً کلید API خود را وارد کنید", "title": "کلید API"}, "basicTitle": "اطلاعات پایه", "configTitle": "اطلاعات پیکربندی", "confirm": "ای<PERSON><PERSON> جدید", "createSuccess": "ایجاد با موفقیت انجام شد", "description": {"placeholder": "توضیحات ارائه‌دهنده (اختیاری)", "title": "توضیحات ارائه‌دهنده"}, "id": {"desc": "به عنوان شناسه منحصر به فرد ارائه‌دهنده خدمات، پس از ایجاد قابل ویرایش نخواهد بود", "format": "فقط می‌تواند شامل اعداد، حروف کوچک، خط تیره (-) و زیرخط (_) باشد", "placeholder": "توصیه می‌شود تماماً با حروف کوچک باشد، مانند openai، پس از ایجاد قابل ویرایش نخواهد بود", "required": "لطفاً شناسه ارائه‌دهنده را وارد کنید", "title": "شناسه ارائه‌دهنده"}, "logo": {"required": "لطفاً لوگوی صحیح ارائه‌دهنده را بارگذاری کنید", "title": "لوگوی ارائه‌دهنده"}, "name": {"placeholder": "لطفاً نام نمایشی ارائه‌دهنده را وارد کنید", "required": "لطفاً نام ارائه‌دهنده را وارد کنید", "title": "نام ارائه‌دهنده"}, "proxyUrl": {"required": "لطفاً آدرس پروکسی را وارد کنید", "title": "آدرس پروکسی"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "لطفاً نوع SDK را انتخاب کنید", "title": "فرمت درخواست"}, "title": "ایجاد ارائه‌دهنده AI سفارشی"}, "github": {"personalAccessToken": {"desc": "توکن دسترسی شخصی Github خود را وارد کنید، برای ایجاد [اینجا](https://github.com/settings/tokens) کلیک کنید", "placeholder": "ghp_xxxxxx", "title": "توکن دسترسی شخصی <PERSON>ub"}}, "huggingface": {"accessToken": {"desc": "توکن HuggingFace خود را وارد کنید، برای ایجاد [اینجا](https://huggingface.co/settings/tokens) کلیک کنید", "placeholder": "hf_xxxxxxxxx", "title": "توکن HuggingFace"}}, "list": {"title": {"disabled": "سرویس‌دهنده غیرفعال", "enabled": "سرویس‌دهنده فعال"}}, "menu": {"addCustomProvider": "اضافه کردن ارائه‌دهنده سفارشی", "all": "همه", "list": {"disabled": "غیرفعال", "enabled": "فعال"}, "notFound": "نتیجه‌ای برای جستجو پیدا نشد", "searchProviders": "جستجوی ارائه‌دهندگان...", "sort": "مرتب‌سازی سفارشی"}, "ollama": {"checker": {"desc": "آزمایش کنید که آیا آدرس پروکسی به درستی وارد شده است", "title": "بررسی اتصال"}, "customModelName": {"desc": "مدل‌های سفارشی را اضافه کنید، چندین مدل را با کاما (,) جدا کنید", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "نام مدل سفارشی"}, "download": {"desc": "Ollama در حال دانلود این مدل است، لطفاً تا حد امکان این صفحه را نبندید. در صورت دانلود مجدد، از نقطه قطع شده ادامه خواهد یافت", "failed": "دانلود مدل ناموفق بود، لطفاً شبکه یا تنظیمات Ollama را بررسی کرده و دوباره تلاش کنید", "remainingTime": "ز<PERSON>ان باقی‌مانده", "speed": "سرعت دانلود", "title": "در حال دانلود مدل {{model}} "}, "endpoint": {"desc": "بای<PERSON> شامل http(s):// باشد، اگر محلی به طور اضافی مشخص نشده باشد می‌توان خالی گذاشت", "title": "آدرس سرویس <PERSON>"}, "title": "Ollama", "unlock": {"cancel": "لغو دانلود", "confirm": "د<PERSON><PERSON><PERSON>د", "description": "برچ<PERSON><PERSON> مد<PERSON><PERSON>ma خود را وارد کنید تا بتوانید به مکالمه ادامه دهید", "downloaded": "{{completed}} / {{total}}", "starting": "شروع دانلود...", "title": "د<PERSON><PERSON>ود مدل مشخص شده Ollama"}}, "providerModels": {"config": {"aesGcm": "کلید شما و آدرس پروکسی و غیره با استفاده از <1>AES-GCM</1> رمزگذاری خواهد شد", "apiKey": {"desc": "لطفاً کلید API {{name}} خود را وارد کنید", "descWithUrl": "لطفاً کلید API {{name}} خود را وارد کنید، <3>برای دریافت اینجا کلیک کنید</3>", "placeholder": "{{name}} کلید API", "title": "کلید API"}, "baseURL": {"desc": "بای<PERSON> شامل http(s):// باشد", "invalid": "لطفاً یک URL معتبر وارد کنید", "placeholder": "https://your-proxy-url.com/v1", "title": "آدرس پروکسی API"}, "checker": {"button": "بررسی", "desc": "آزمون کلید API و آدرس پروکسی برای صحت", "pass": "بررسی موفقیت‌آمیز", "title": "بررسی اتصال"}, "fetchOnClient": {"desc": "مدل درخواست کلاینت به طور مستقیم از مرورگر درخواست جلسه را آغاز می‌کند و می‌تواند سرعت پاسخ را افزایش دهد", "title": "استفاده از مدل درخواست کلاینت"}, "helpDoc": "راهنمای پیکربندی", "responsesApi": {"desc": "استفاده از قالب درخواست نسل جدید OpenAI برای باز کردن ویژگی‌های پیشرفته مانند زنجیره تفکر", "title": "استفاده از استاندارد Responses API"}, "waitingForMore": "مدل‌های بیشتری در حال <1>برنامه‌ریزی برای اتصال</1> هستند، لطفاً منتظر بمانید"}, "createNew": {"title": "ای<PERSON><PERSON> مدل AI سفارشی"}, "item": {"config": "پیکربندی مدل", "customModelCards": {"addNew": "ایجاد و افزودن مدل {{id}}", "confirmDelete": "در حال حذف این مدل سفارشی هستید، پس از حذف قابل بازیابی نخواهد بود، لطفاً با احتیاط عمل کنید."}, "delete": {"confirm": "آیا مطمئن هستید که می‌خواهید مدل {{displayName}} را حذف کنید؟", "success": "حذف با موفقیت انجام شد", "title": "<PERSON><PERSON><PERSON> مدل"}, "modelConfig": {"azureDeployName": {"extra": "فیلدی که در Azure OpenAI درخواست واقعی می‌شود", "placeholder": "لطفاً نام استقرار مدل در Azure را وارد کنید", "title": "نام استقرار مدل"}, "deployName": {"extra": "این فیلد به عنوان شناسه مدل هنگام ارسال درخواست استفاده می‌شود", "placeholder": "لطفاً نام یا شناسه واقعی مدل را وارد کنید", "title": "نام مدل برای استقرار"}, "displayName": {"placeholder": "لطفاً نام نمایشی مدل را وارد کنید، مانند ChatGPT، GPT-4 و غیره", "title": "نام نمایشی مدل"}, "files": {"extra": "پیاده‌سازی بارگذاری فایل فعلی تنها یک راه‌حل Hack است و فقط برای آزمایش شخصی محدود است. لطفاً منتظر پیاده‌سازی کامل قابلیت بارگذاری فایل باشید", "title": "پشتیبانی از بارگذاری فایل"}, "functionCall": {"extra": "این پیکربندی تنها قابلیت استفاده از ابزارها را برای مدل فعال می‌کند و به این ترتیب می‌توان افزونه‌های نوع ابزار را به مدل اضافه کرد. اما اینکه آیا واقعاً از ابزارها استفاده می‌شود به خود مدل بستگی دارد، لطفاً قابلیت استفاده را خودتان آزمایش کنید", "title": "پشتیبانی از استفاده از ابزار"}, "id": {"extra": "پس از ایجاد قابل ویرایش نیست و در هنگام فراخوانی AI به عنوان شناسه مدل استفاده خواهد شد", "placeholder": "لطفاً شناسه مدل را وارد کنید، مانند gpt-4o یا claude-3.5-sonnet", "title": "شناسه مدل"}, "modalTitle": "پیکربندی مدل سفارشی", "reasoning": {"extra": "این تنظیم فقط قابلیت تفکر عمیق مدل را فعال می‌کند و تأثیر دقیق آن کاملاً به خود مدل بستگی دارد، لطفاً خودتان آزمایش کنید که آیا این مدل قابلیت تفکر عمیق قابل استفاده را دارد یا خیر", "title": "پشتیبانی از تفکر عمیق"}, "tokens": {"extra": "حداکثر تعداد توکن‌های پشتیبانی شده توسط مدل را تنظیم کنید", "title": "حداکثر پنجره زمینه", "unlimited": "بدون محدودیت"}, "vision": {"extra": "این پیکربندی تنها قابلیت بارگذاری تصویر در برنامه را فعال می‌کند، اینکه آیا شناسایی پشتیبانی می‌شود به خود مدل بستگی دارد، لطفاً قابلیت استفاده از شناسایی بصری این مدل را آزمایش کنید", "title": "پشتیبانی از شناسایی بصری"}}, "pricing": {"image": "${{amount}}/تصویر", "inputCharts": "${{amount}}/M کارا<PERSON>تر", "inputMinutes": "${{amount}}/دقیقه", "inputTokens": "ورودی ${{amount}}/M", "outputTokens": "خروجی ${{amount}}/M"}, "releasedAt": "منتشر شده در {{releasedAt}}"}, "list": {"addNew": "مد<PERSON> جدید اضافه کنید", "disabled": "غیرفعال", "disabledActions": {"showMore": "نمایش همه"}, "empty": {"desc": "لطفاً یک مدل سفارشی ایجاد کنید یا پس از بارگذاری مدل‌ها، شروع به استفاده کنید", "title": "مدل قابل استفاده‌ای وجود ندارد"}, "enabled": "فعال", "enabledActions": {"disableAll": "غیرفعال کردن همه", "enableAll": "فعال کردن همه", "sort": "مرتب‌سازی مدل‌های سفارشی"}, "enabledEmpty": "مدل فعال وجود ندارد، لطفاً از لیست زیر مدل مورد نظر خود را فعال کنید~", "fetcher": {"clear": "پاک کردن مدل‌های دریافت شده", "fetch": "دریافت لیست مدل‌ها", "fetching": "در حال دریافت لیست مدل‌ها...", "latestTime": "آخرین زمان به‌روزرسانی: {{time}}", "noLatestTime": "لیست هنوز دریافت نشده است"}, "resetAll": {"conform": "آیا مطمئن هستید که می‌خواهید تمام تغییرات مدل فعلی را بازنشانی کنید؟ پس از بازنشانی، لیست مدل‌های فعلی به حالت پیش‌فرض باز خواهد گشت", "success": "بازنشانی با موفقیت انجام شد", "title": "بازنشانی تمام تغییرات"}, "search": "جستجوی مدل...", "searchResult": "{{count}} مدل پیدا شد", "title": "لیست مدل‌ها", "total": "در مجموع {{count}} مدل در دسترس است"}, "searchNotFound": "نتیجه‌ای برای جستجو پیدا نشد"}, "sortModal": {"success": "به‌روزرسانی مرتب‌سازی با موفقیت انجام شد", "title": "مرتب‌سازی سفارشی", "update": "به‌روزرسانی"}, "updateAiProvider": {"confirmDelete": "در حال حذف این ارائه‌دهنده AI هستید، پس از حذف قابل بازیابی نخواهد بود، آیا مطمئن هستید که می‌خواهید حذف کنید؟", "deleteSuccess": "حذف با موفقیت انجام شد", "tooltip": "به‌روزرسانی پیکربندی پایه ارائه‌دهنده", "updateSuccess": "به‌روزرسانی با موفقیت انجام شد"}, "updateCustomAiProvider": {"title": "به‌روزرسانی تنظیمات ارائه‌دهنده AI سفارشی"}, "vertexai": {"apiKey": {"desc": "کلیدهای Vertex AI خود را وارد کنید", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "کلیدهای Vertex AI"}}, "zeroone": {"title": "01.AI صفر و یک همه چیز"}, "zhipu": {"title": "ژھیپو"}}