{"ai21": {"description": "AI21 Labs مدل‌های پایه و سیستم‌های هوش مصنوعی را برای کسب‌وکارها ایجاد می‌کند و به تسریع کاربرد هوش مصنوعی تولیدی در تولید کمک می‌کند."}, "ai360": {"description": "360 AI پلتفرم مدل‌ها و خدمات هوش مصنوعی شرکت 360 است که مدل‌های پیشرفته پردازش زبان طبیعی متعددی از جمله 360GPT2 Pro، 360GPT Pro، 360GPT Turbo و 360GPT Turbo Responsibility 8K را ارائه می‌دهد. این مدل‌ها با ترکیب پارامترهای بزرگ‌مقیاس و قابلیت‌های چندوجهی، به طور گسترده در زمینه‌های تولید متن، درک معنایی، سیستم‌های مکالمه و تولید کد به کار می‌روند. با استفاده از استراتژی‌های قیمت‌گذاری انعطاف‌پذیر، 360 AI نیازهای متنوع کاربران را برآورده کرده و از یکپارچه‌سازی توسعه‌دهندگان پشتیبانی می‌کند و به نوآوری و توسعه کاربردهای هوشمند کمک می‌کند."}, "aihubmix": {"description": "AiHubMix دسترسی به مدل‌های مختلف هوش مصنوعی را از طریق یک رابط برنامه‌نویسی کاربردی (API) یکپارچه فراهم می‌کند."}, "anthropic": {"description": "Anthropic یک شرکت متمرکز بر تحقیق و توسعه هوش مصنوعی است که مجموعه‌ای از مدل‌های پیشرفته زبان مانند Claude 3.5 Sonnet، Claude 3 Sonnet، Claude 3 Opus و Claude 3 Haiku را ارائه می‌دهد. این مدل‌ها تعادلی ایده‌آل بین هوشمندی، سرعت و هزینه برقرار می‌کنند و برای انواع کاربردها از بارهای کاری در سطح سازمانی تا پاسخ‌های سریع مناسب هستند. Claude 3.5 Sonnet به عنوان جدیدترین مدل آن، در ارزیابی‌های متعدد عملکرد برجسته‌ای داشته و در عین حال نسبت هزینه به عملکرد بالایی را حفظ کرده است."}, "azure": {"description": "Azure انواع مدل‌های پیشرفته AI را ارائه می‌دهد، از جمله GPT-3.5 و جدیدترین سری GPT-4، که از انواع داده‌ها و وظایف پیچیده پشتیبانی می‌کند و به ارائه راه‌حل‌های AI ایمن، قابل اعتماد و پایدار متعهد است."}, "azureai": {"description": "Azure مجموعه‌ای از مدل‌های پیشرفته AI را ارائه می‌دهد، از جمله GPT-3.5 و جدیدترین سری GPT-4، که از انواع مختلف داده‌ها و وظایف پیچیده پشتیبانی می‌کند و به دنبال راه‌حل‌های AI ایمن، قابل اعتماد و پایدار است."}, "baichuan": {"description": "بایچوان هوش مصنوعی یک شرکت متمرکز بر توسعه مدل‌های بزرگ هوش مصنوعی است. مدل‌های این شرکت در وظایف چینی مانند دانشنامه، پردازش متون طولانی و تولید محتوا عملکرد برجسته‌ای دارند و از مدل‌های اصلی خارجی پیشی گرفته‌اند. بایچوان هوش مصنوعی همچنین دارای توانایی‌های چندوجهی پیشرو در صنعت است و در چندین ارزیابی معتبر عملکرد عالی داشته است. مدل‌های آن شامل Baichuan 4، Baichuan 3 Turbo و Baichuan 3 Turbo 128k هستند که برای سناریوهای مختلف بهینه‌سازی شده‌اند و راه‌حل‌های مقرون‌به‌صرفه‌ای ارائه می‌دهند."}, "bedrock": {"description": "Bedrock یک سرویس ارائه شده توسط آمازون AWS است که بر ارائه مدل‌های پیشرفته زبان AI و مدل‌های بصری برای شرکت‌ها تمرکز دارد. خانواده مدل‌های آن شامل سری Claude از Anthropic، سری Llama 3.1 از Meta و غیره است که از مدل‌های سبک تا مدل‌های با عملکرد بالا را پوشش می‌دهد و از وظایفی مانند تولید متن، مکالمه و پردازش تصویر پشتیبانی می‌کند. این سرویس برای برنامه‌های شرکتی با مقیاس‌ها و نیازهای مختلف مناسب است."}, "cloudflare": {"description": "مدل‌های یادگیری ماشین مبتنی بر GPU بدون سرور را در شبکه جهانی Cloudflare اجرا کنید."}, "cohere": {"description": "Cohere جدیدترین مدل‌های چند زبانه، قابلیت‌های پیشرفته جستجو و فضای کاری هوش مصنوعی سفارشی برای کسب‌وکارهای مدرن را به شما ارائه می‌دهد - همه این‌ها در یک پلتفرم امن یکپارچه شده‌اند."}, "deepseek": {"description": "DeepSeek یک شرکت متمرکز بر تحقیق و کاربرد فناوری هوش مصنوعی است. مدل جدید آن، DeepSeek-V2.5، توانایی‌های مکالمه عمومی و پردازش کد را ترکیب کرده و در زمینه‌هایی مانند هم‌ترازی با ترجیحات انسانی، وظایف نوشتاری و پیروی از دستورات بهبود قابل توجهی داشته است."}, "fal": {"description": "پلتفرم رسانه‌ای تولیدی برای توسعه‌دهندگان"}, "fireworksai": {"description": "Fireworks AI یک ارائه‌دهنده پیشرو در خدمات مدل‌های زبان پیشرفته است که بر فراخوانی توابع و پردازش چندوجهی تمرکز دارد. جدیدترین مدل آن، Firefunction V2، بر اساس Llama-3 ساخته شده و برای فراخوانی توابع، مکالمه و پیروی از دستورات بهینه‌سازی شده است. مدل زبان تصویری FireLLaVA-13B از ورودی‌های ترکیبی تصویر و متن پشتیبانی می‌کند. سایر مدل‌های قابل توجه شامل سری Llama و سری Mixtral هستند که پشتیبانی کارآمدی از پیروی دستورات چندزبانه و تولید ارائه می‌دهند."}, "giteeai": {"description": "API بی خدمتکار Gitee AI به توسعه‌کنندگان AI با یک از خدمت API بزرگ مدل آلودگی را از جعبه می‌دهد."}, "github": {"description": "با استفاده از مدل GitHub، توسعه‌دهندگان می‌توانند به مهندسین هوش مصنوعی تبدیل شوند و با استفاده از مدل‌های پیشرو در صنعت، ساخت و ساز کنند."}, "google": {"description": "سری Gemini گوگل پیشرفته‌ترین و عمومی‌ترین مدل هوش مصنوعی آن است که توسط Google DeepMind ساخته شده و به‌طور خاص برای چندوجهی طراحی شده است. این مدل از درک و پردازش بی‌وقفه متن، کد، تصویر، صدا و ویدئو پشتیبانی می‌کند. این مدل در محیط‌های مختلف از مراکز داده تا دستگاه‌های همراه قابل استفاده است و به‌طور قابل توجهی کارایی و گستردگی کاربرد مدل‌های هوش مصنوعی را افزایش می‌دهد."}, "groq": {"description": "موتور استنتاج LPU شرکت Groq در آخرین آزمون‌های معیار مدل‌های زبانی بزرگ (LLM) مستقل عملکرد برجسته‌ای داشته و با سرعت و کارایی شگفت‌انگیز خود، استانداردهای راه‌حل‌های هوش مصنوعی را بازتعریف کرده است. Groq نمادی از سرعت استنتاج فوری است و در استقرارهای مبتنی بر ابر عملکرد خوبی از خود نشان داده است."}, "higress": {"description": "Higress یک دروازه API ابری است که برای حل مشکلات مربوط به بارگذاری مجدد Tengine در کسب و کارهای با اتصالات طولانی و همچنین کمبود قابلیت‌های تعادل بار gRPC/Dubbo در داخل علی ایجاد شده است."}, "huggingface": {"description": "API استنتاج HuggingFace یک روش سریع و رایگان برای کاوش هزاران مدل برای وظایف مختلف ارائه می‌دهد. چه در حال طراحی نمونه اولیه برای یک برنامه جدید باشید و چه در حال آزمایش قابلیت‌های یادگیری ماشین، این API به شما امکان دسترسی فوری به مدل‌های با عملکرد بالا در چندین حوزه را می‌دهد."}, "hunyuan": {"description": "مدل زبان بزرگ توسعه‌یافته توسط تنسنت، با توانایی‌های قدرتمند در خلق محتوای چینی، توانایی استدلال منطقی در زمینه‌های پیچیده، و قابلیت اجرای وظایف به‌صورت قابل اعتماد"}, "infiniai": {"description": "خدمات مدل‌های بزرگ با عملکرد بالا، راحت برای استفاده و امن برای توسعه‌دهندگان اپلیکیشن، که شامل مراحل از توسعه مدل‌های بزرگ تا پیشگیری از نصب خدمات مدل‌های بزرگ می‌شود."}, "internlm": {"description": "سازمان متن باز متعهد به تحقیق و توسعه ابزارهای مدل‌های بزرگ. ارائه یک پلتفرم متن باز کارآمد و آسان برای تمام توسعه‌دهندگان هوش مصنوعی، تا جدیدترین مدل‌ها و تکنیک‌های الگوریتمی در دسترس باشد."}, "jina": {"description": "Jina AI در سال 2020 تأسیس شد و یک شرکت پیشرو در زمینه AI جستجو است. پلتفرم پایه جستجوی ما شامل مدل‌های برداری، بازچینش‌گرها و مدل‌های زبانی کوچک است که به کسب‌وکارها کمک می‌کند تا برنامه‌های جستجوی تولیدی و چندرسانه‌ای قابل اعتماد و با کیفیت بالا بسازند."}, "lmstudio": {"description": "LM Studio یک برنامه دسکتاپ برای توسعه و آزمایش LLM ها بر روی رایانه شما است."}, "minimax": {"description": "MiniMax یک شرکت فناوری هوش مصنوعی عمومی است که در سال 2021 تأسیس شد و به همکاری با کاربران برای ایجاد هوش مصنوعی متعهد است. MiniMax به‌طور مستقل مدل‌های بزرگ عمومی چندگانه‌ای را توسعه داده است، از جمله مدل متنی MoE با تریلیون‌ها پارامتر، مدل صوتی و مدل تصویری. همچنین برنامه‌هایی مانند حلزون AI را معرفی کرده است."}, "mistral": {"description": "Mistral مدل‌های پیشرفته عمومی، تخصصی و پژوهشی را ارائه می‌دهد که به طور گسترده در زمینه‌های استدلال پیچیده، وظایف چندزبانه، تولید کد و غیره کاربرد دارند. از طریق رابط فراخوانی عملکرد، کاربران می‌توانند قابلیت‌های سفارشی را برای تحقق برنامه‌های خاص ادغام کنند."}, "modelscope": {"description": "ModelScope یک پلتفرم مدل به عنوان سرویس است که توسط علی‌بابا کلود ارائه شده و مدل‌های هوش مصنوعی متنوع و خدمات استنتاج را فراهم می‌کند."}, "moonshot": {"description": "Moonshot یک پلتفرم متن‌باز است که توسط شرکت فناوری Beijing Dark Side of the Moon ارائه شده است. این پلتفرم مدل‌های مختلف پردازش زبان طبیعی را ارائه می‌دهد و در زمینه‌های گسترده‌ای از جمله، اما نه محدود به، تولید محتوا، تحقیقات علمی، توصیه‌های هوشمند، تشخیص پزشکی و غیره کاربرد دارد و از پردازش متون طولانی و وظایف پیچیده تولید پشتیبانی می‌کند."}, "novita": {"description": "Novita AI یک پلتفرم ارائه‌دهنده خدمات API برای مدل‌های بزرگ زبانی و تولید تصاویر هوش مصنوعی است که انعطاف‌پذیر، قابل‌اعتماد و مقرون‌به‌صرفه می‌باشد. این پلتفرم از جدیدترین مدل‌های متن‌باز مانند Llama3 و Mistral پشتیبانی می‌کند و راه‌حل‌های API جامع، کاربرپسند و خودکار برای توسعه برنامه‌های هوش مصنوعی مولد ارائه می‌دهد که مناسب رشد سریع استارتاپ‌های هوش مصنوعی است."}, "nvidia": {"description": "NVIDIA NIM™ کانتینرهایی را ارائه می‌دهد که می‌توانند برای استنتاج میکروسرویس‌های GPU تسریع شده خود میزبان استفاده شوند و از استقرار مدل‌های AI پیش‌آموزش‌دیده و سفارشی در ابر، مراکز داده، رایانه‌های شخصی RTX™ AI و ایستگاه‌های کاری پشتیبانی می‌کند."}, "ollama": {"description": "مدل‌های ارائه‌شده توسط Ollama طیف گسترده‌ای از تولید کد، محاسبات ریاضی، پردازش چندزبانه و تعاملات گفتگویی را پوشش می‌دهند و از نیازهای متنوع استقرار در سطح سازمانی و محلی پشتیبانی می‌کنند."}, "openai": {"description": "OpenAI یک موسسه پیشرو در تحقیقات هوش مصنوعی در سطح جهان است که مدل‌هایی مانند سری GPT را توسعه داده و مرزهای پردازش زبان طبیعی را پیش برده است. OpenAI متعهد به تغییر صنایع مختلف از طریق راه‌حل‌های نوآورانه و کارآمد هوش مصنوعی است. محصولات آن‌ها دارای عملکرد برجسته و اقتصادی بوده و به طور گسترده در تحقیقات، تجارت و کاربردهای نوآورانه استفاده می‌شوند."}, "openrouter": {"description": "OpenRouter یک پلتفرم خدماتی است که رابط‌های مدل‌های پیشرفته مختلفی مانند OpenAI، Anthropic، LLaMA و بیشتر را ارائه می‌دهد و برای نیازهای متنوع توسعه و کاربرد مناسب است. کاربران می‌توانند بر اساس نیازهای خود، بهترین مدل و قیمت را به‌صورت انعطاف‌پذیر انتخاب کنند و به بهبود تجربه AI کمک کنند."}, "perplexity": {"description": "Perplexity یک ارائه‌دهنده پیشرو در مدل‌های تولید مکالمه است که انواع مدل‌های پیشرفته Llama 3.1 را ارائه می‌دهد و از برنامه‌های آنلاین و آفلاین پشتیبانی می‌کند. این مدل‌ها به‌ویژه برای وظایف پیچیده پردازش زبان طبیعی مناسب هستند."}, "ppio": {"description": "PPIO پایو کلود خدمات API مدل‌های متن باز با ثبات و با قیمت مناسب را ارائه می‌دهد و از تمام سری‌های DeepSeek، Llama، Qwen و سایر مدل‌های بزرگ پیشرو در صنعت پشتیبانی می‌کند."}, "qiniu": {"description": "Qiniu یک شرکت پیشرو در خدمات سحابی است که API های سریع و کارآمد برای فراخوانی مدل‌های بزرگ، از جمله مدل‌های Alibaba، را ارائه می‌دهد و با امکانات پیشرفته برای ساخت و استفاده از برنامه‌های AI پشتیبانی می‌کند."}, "qwen": {"description": "چوان یی چیان ون یک مدل زبان بسیار بزرگ است که توسط علی‌کلود به‌طور مستقل توسعه یافته و دارای توانایی‌های قدرتمند درک و تولید زبان طبیعی است. این مدل می‌تواند به انواع سوالات پاسخ دهد، محتوای متنی خلق کند، نظرات و دیدگاه‌ها را بیان کند، کد بنویسد و در حوزه‌های مختلف نقش ایفا کند."}, "sambanova": {"description": "SambaNova Cloud به توسعه‌دهندگان این امکان را می‌دهد که به راحتی از بهترین مدل‌های متن‌باز استفاده کنند و از سریع‌ترین سرعت استنتاج بهره‌مند شوند."}, "search1api": {"description": "Search1API دسترسی به مجموعه مدل‌های DeepSeek را که می‌توانند به صورت خودکار به اینترنت متصل شوند، ارائه می‌دهد، شامل نسخه استاندارد و نسخه سریع، که از انتخاب مدل با مقیاس‌های مختلف پارامتر پشتیبانی می‌کند."}, "sensenova": {"description": "سنسنووا، با تکیه بر زیرساخت‌های قوی سنس‌تک، خدمات مدل‌های بزرگ تمام‌پشته‌ای را به‌صورت کارآمد و آسان ارائه می‌دهد."}, "siliconcloud": {"description": "SiliconCloud، یک سرویس ابری GenAI با کارایی بالا و مقرون‌به‌صرفه بر اساس مدل‌های منبع‌باز برجسته"}, "spark": {"description": "مدل بزرگ اسپارک iFLYTEK توانایی‌های قدرتمند AI را در حوزه‌های مختلف و زبان‌های متعدد ارائه می‌دهد و با استفاده از فناوری پیشرفته پردازش زبان طبیعی، برنامه‌های نوآورانه‌ای را برای سخت‌افزارهای هوشمند، بهداشت هوشمند، مالی هوشمند و سایر سناریوهای عمودی ایجاد می‌کند."}, "stepfun": {"description": "مدل بزرگ ستاره‌ای طبقاتی دارای توانایی‌های پیشرو در صنعت برای چندحالته و استدلال پیچیده است و از درک متون بسیار طولانی و قابلیت جستجوی خودکار قدرتمند پشتیبانی می‌کند."}, "taichu": {"description": "موسسه اتوماسیون آکادمی علوم چین و موسسه هوش مصنوعی ووهان نسل جدیدی از مدل‌های چندوجهی را معرفی کرده‌اند که از پرسش و پاسخ چندمرحله‌ای، تولید متن، تولید تصویر، درک سه‌بعدی، تحلیل سیگنال و سایر وظایف جامع پرسش و پاسخ پشتیبانی می‌کند. این مدل دارای توانایی‌های شناختی، درک و خلاقیت قوی‌تری است و تجربه تعاملی جدیدی را به ارمغان می‌آورد."}, "tencentcloud": {"description": "قدرت اتمی موتور دانش (LLM Knowledge Engine Atomic Power) بر اساس موتور دانش توسعه یافته و قابلیت کامل پرسش و پاسخ را برای شرکت‌ها و توسعه‌دهندگان ارائه می‌دهد. شما می‌توانید با استفاده از چندین قدرت اتمی، خدمات مدل اختصاصی خود را بسازید و از خدماتی مانند تجزیه و تحلیل اسناد، تقسیم، جاسازی، بازنویسی چند دور و غیره برای سفارشی‌سازی کسب و کار هوش مصنوعی اختصاصی خود استفاده کنید."}, "togetherai": {"description": "Together AI متعهد به دستیابی به عملکرد پیشرو از طریق مدل‌های نوآورانه هوش مصنوعی است و قابلیت‌های سفارشی‌سازی گسترده‌ای را ارائه می‌دهد، از جمله پشتیبانی از مقیاس‌پذیری سریع و فرآیندهای استقرار شهودی، که نیازهای مختلف شرکت‌ها را برآورده می‌کند."}, "upstage": {"description": "Upstage بر توسعه مدل‌های هوش مصنوعی برای نیازهای مختلف تجاری تمرکز دارد، از جمله Solar LLM و هوش مصنوعی اسناد، که هدف آن دستیابی به هوش عمومی مصنوعی (AGI) برای کار است. با استفاده از Chat API، می‌توانید نمایندگان مکالمه ساده ایجاد کنید و از قابلیت‌های فراخوانی عملکرد، ترجمه، تعبیه و کاربردهای خاص حوزه پشتیبانی کنید."}, "v0": {"description": "v0 یک دستیار برنامه‌نویسی جفتی است که تنها با توصیف ایده‌ها به زبان طبیعی، می‌تواند کد و رابط کاربری (UI) را برای پروژه شما تولید کند"}, "vertexai": {"description": "سری Gemini گوگل پیشرفته‌ترین و عمومی‌ترین مدل‌های هوش مصنوعی است که توسط Google DeepMind طراحی شده و به‌طور خاص برای چندرسانه‌ای طراحی شده است و از درک و پردازش بی‌وقفه متن، کد، تصویر، صدا و ویدیو پشتیبانی می‌کند. این مدل‌ها برای محیط‌های مختلف از مراکز داده تا دستگاه‌های همراه مناسب هستند و به‌طور قابل توجهی کارایی و کاربردهای مدل‌های هوش مصنوعی را افزایش می‌دهند."}, "vllm": {"description": "vLLM یک کتابخانه سریع و آسان برای استفاده است که برای استنتاج و خدمات LLM طراحی شده است."}, "volcengine": {"description": "پلتفرم توسعه خدمات مدل‌های بزرگ که توسط بایت‌دANCE راه‌اندازی شده است، خدمات فراوان، ایمن و با قیمت رقابتی برای فراخوانی مدل‌ها را ارائه می‌دهد. همچنین امکاناتی از جمله داده‌های مدل، تنظیم دقیق، استنتاج و ارزیابی را به صورت end-to-end فراهم می‌کند و به طور جامع از توسعه و پیاده‌سازی برنامه‌های هوش مصنوعی شما حمایت می‌کند."}, "wenxin": {"description": "پلتفرم جامع توسعه و خدمات مدل‌های بزرگ و برنامه‌های بومی هوش مصنوعی در سطح سازمانی، ارائه‌دهنده کامل‌ترین و کاربرپسندترین زنجیره ابزارهای توسعه مدل‌های هوش مصنوعی مولد و توسعه برنامه‌ها"}, "xai": {"description": "xAI یک شرکت است که به ساخت هوش مصنوعی برای تسریع کشفیات علمی بشر اختصاص دارد. مأموریت ما پیشبرد درک مشترک ما از جهان است."}, "xinference": {"description": "Xorbits Inference (Xinference) یک پلتفرم اپن‌سورس برای ساده‌سازی اجرای و ادغام انواع مدل‌های هوش مصنوعی است. با کمک Xinference، شما می‌توانید هر مدل زبانی اپن‌سورس، مدل‌های مبتنی بر بردار و مدل‌های چندمدیا را در محیط‌های ابری یا محلی اجرا کرده و برنامه‌های AI قدرتمند ایجاد کنید."}, "zeroone": {"description": "صفر و یک متعهد به پیشبرد انقلاب فناوری AI 2.0 با محوریت انسان است و هدف آن ایجاد ارزش اقتصادی و اجتماعی عظیم از طریق مدل‌های زبانی بزرگ و همچنین ایجاد اکوسیستم جدید هوش مصنوعی و مدل‌های تجاری است."}, "zhipu": {"description": "پلتفرم باز هوش مصنوعی Zhipu خدمات مدل‌های چندرسانه‌ای و زبانی را ارائه می‌دهد و از کاربردهای گسترده‌ای در زمینه‌های مختلف هوش مصنوعی مانند پردازش متن، درک تصویر و کمک به برنامه‌نویسی پشتیبانی می‌کند."}}