{"01-ai/yi-1.5-34b-chat": {"description": "Zero One Everything، جدیدترین مدل متن باز تنظیم شده با 34 میلیارد پارامتر، که تنظیمات آن از چندین سناریوی گفتگویی پشتیبانی می‌کند و داده‌های آموزشی با کیفیت بالا را برای هم‌راستایی با ترجیحات انسانی فراهم می‌کند."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One Everything، جدیدترین مدل متن باز تنظیم شده با 9 میلیارد پارامتر، که تنظیمات آن از چندین سناریوی گفتگویی پشتیبانی می‌کند و داده‌های آموزشی با کیفیت بالا را برای هم‌راستایی با ترجیحات انسانی فراهم می‌کند."}, "360/deepseek-r1": {"description": "مدل DeepSeek-R1 نسخه 360، که در مرحله پس از آموزش به‌طور گسترده‌ای از تکنیک‌های یادگیری تقویتی استفاده کرده و توانایی استدلال مدل را به‌طور قابل توجهی افزایش داده است. در وظایف ریاضی، کدنویسی و استدلال زبان طبیعی، عملکردی مشابه نسخه رسمی OpenAI o1 دارد."}, "360gpt-pro": {"description": "360GPT Pro به عنوان یکی از اعضای مهم سری مدل‌های 360 AI، با توانایی پردازش متون به‌صورت کارآمد، نیازهای متنوع در زمینه‌های مختلف کاربردهای زبان طبیعی را برآورده می‌کند و از قابلیت‌هایی مانند درک متون طولانی و مکالمات چندمرحله‌ای پشتیبانی می‌کند."}, "360gpt-pro-trans": {"description": "مدل مخصوص ترجمه، به‌طور عمیق بهینه‌سازی شده و دارای عملکرد پیشرفته در ترجمه است."}, "360gpt-turbo": {"description": "360GPT Turbo توانایی‌های محاسباتی و مکالمه‌ای قدرتمندی ارائه می‌دهد و دارای کارایی بالایی در درک و تولید معنا است. این یک راه‌حل ایده‌آل برای دستیار هوشمند برای شرکت‌ها و توسعه‌دهندگان است."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K بر امنیت معنایی و مسئولیت‌پذیری تأکید دارد و به‌طور ویژه برای سناریوهایی طراحی شده است که نیاز بالایی به امنیت محتوا دارند، تا دقت و پایداری تجربه کاربری را تضمین کند."}, "360gpt2-o1": {"description": "360gpt2-o1 از جستجوی درخت برای ساخت زنجیره‌های تفکر استفاده می‌کند و مکانیزم بازتاب را معرفی کرده است و با استفاده از یادگیری تقویتی آموزش دیده است، این مدل توانایی خودبازتابی و اصلاح خطا را دارد."}, "360gpt2-pro": {"description": "360GPT2 Pro مدل پیشرفته پردازش زبان طبیعی است که توسط شرکت 360 ارائه شده است. این مدل دارای توانایی‌های برجسته‌ای در تولید و درک متن است و به ویژه در زمینه تولید و خلاقیت عملکرد فوق‌العاده‌ای دارد. همچنین قادر به انجام وظایف پیچیده تبدیل زبان و ایفای نقش می‌باشد."}, "360zhinao2-o1": {"description": "مدل 360zhinao2-o1 با استفاده از جستجوی درختی زنجیره تفکر را ایجاد کرده و مکانیزم بازتاب را معرفی کرده است و با استفاده از یادگیری تقویتی آموزش دیده است، این مدل توانایی خودبازتابی و اصلاح خطا را دارد."}, "4.0Ultra": {"description": "Spark Ultra قدرتمندترین نسخه از سری مدل‌های بزرگ Spark است که با ارتقاء مسیر جستجوی متصل به شبکه، توانایی درک و خلاصه‌سازی محتوای متنی را بهبود می‌بخشد. این یک راه‌حل جامع برای افزایش بهره‌وری در محیط کار و پاسخگویی دقیق به نیازها است و به عنوان یک محصول هوشمند پیشرو در صنعت شناخته می‌شود."}, "AnimeSharp": {"description": "AnimeSharp (که با نام \"4x‑AnimeSharp\" نیز شناخته می‌شود) یک مدل ابررزولوشن متن‌باز است که توسط Kim2091 بر اساس معماری ESRGAN توسعه یافته است و بر بزرگ‌نمایی و تیزکردن تصاویر با سبک انیمه تمرکز دارد. این مدل در فوریه ۲۰۲۲ از \"4x-TextSharpV1\" تغییر نام داد و در ابتدا برای تصاویر متنی نیز کاربرد داشت اما عملکرد آن به طور قابل توجهی برای محتوای انیمه بهینه شده است."}, "Baichuan2-Turbo": {"description": "با استفاده از فناوری تقویت جستجو، مدل بزرگ را به دانش حوزه‌ای و دانش کل وب متصل می‌کند. از آپلود انواع اسناد مانند PDF، Word و همچنین وارد کردن آدرس‌های وب پشتیبانی می‌کند. اطلاعات به‌موقع و جامع دریافت می‌شود و نتایج خروجی دقیق و حرفه‌ای هستند."}, "Baichuan3-Turbo": {"description": "بهینه‌سازی شده برای سناریوهای پرتکرار سازمانی، با بهبود قابل توجه و نسبت عملکرد به هزینه بالا. در مقایسه با مدل Baichuan2، تولید محتوا ۲۰٪ بهبود یافته، پاسخ به سوالات ۱۷٪ بهتر شده و توانایی نقش‌آفرینی ۴۰٪ افزایش یافته است. عملکرد کلی بهتر از GPT3.5 است."}, "Baichuan3-Turbo-128k": {"description": "دارای پنجره متنی فوق‌العاده طولانی ۱۲۸K، بهینه‌سازی شده برای سناریوهای پرتکرار سازمانی، با بهبود قابل توجه در عملکرد و مقرون به صرفه بودن. در مقایسه با مدل Baichuan2، ۲۰٪ بهبود در تولید محتوا، ۱۷٪ بهبود در پرسش و پاسخ دانش، و ۴۰٪ بهبود در توانایی نقش‌آفرینی. عملکرد کلی بهتر از GPT3.5 است."}, "Baichuan4": {"description": "این مدل از نظر توانایی در داخل کشور رتبه اول را دارد و در وظایف چینی مانند دانشنامه، متون طولانی و تولید محتوا از مدل‌های اصلی خارجی پیشی می‌گیرد. همچنین دارای توانایی چندوجهی پیشرو در صنعت است و در چندین معیار ارزیابی معتبر عملکرد برجسته‌ای دارد."}, "Baichuan4-Air": {"description": "توانایی مدل در کشور اول است و در وظایف چینی مانند دانشنامه، متن‌های طولانی و تولید خلاقانه از مدل‌های اصلی خارجی پیشی می‌گیرد. همچنین دارای قابلیت‌های چندرسانه‌ای پیشرفته در صنعت است و در چندین معیار ارزیابی معتبر عملکرد عالی دارد."}, "Baichuan4-Turbo": {"description": "توانایی مدل در کشور اول است و در وظایف چینی مانند دانشنامه، متن‌های طولانی و تولید خلاقانه از مدل‌های اصلی خارجی پیشی می‌گیرد. همچنین دارای قابلیت‌های چندرسانه‌ای پیشرفته در صنعت است و در چندین معیار ارزیابی معتبر عملکرد عالی دارد."}, "DeepSeek-R1": {"description": "مدل LLM پیشرفته و کارآمد که در استدلال، ریاضیات و برنامه‌نویسی تخصص دارد."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 - مدل بزرگتر و هوشمندتر در مجموعه DeepSeek - به ساختار لاما 70B تقطیر شده است. بر اساس آزمون‌های معیار و ارزیابی‌های انسانی، این مدل نسبت به لاما 70B اصلی هوشمندتر است و به ویژه در وظایفی که نیاز به دقت ریاضی و واقعیات دارند، عملکرد عالی دارد."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "مدل تقطیر DeepSeek-R1 مبتنی بر Qwen2.5-Math-1.5B است که با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده است."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "مدل تقطیر DeepSeek-R1 مبتنی بر Qwen2.5-14B است که با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده است."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "سری DeepSeek-R1 با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده و از سطح OpenAI-o1-mini فراتر رفته است."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "مدل تقطیر DeepSeek-R1 مبتنی بر Qwen2.5-Math-7B است که با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده است."}, "DeepSeek-V3": {"description": "DeepSeek-V3 یک مدل MoE است که توسط شرکت DeepSeek توسعه یافته است. نتایج ارزیابی‌های متعدد DeepSeek-V3 از مدل‌های متن باز دیگر مانند Qwen2.5-72B و Llama-3.1-405B فراتر رفته و از نظر عملکرد با مدل‌های بسته جهانی برتر مانند GPT-4o و Claude-3.5-Sonnet برابری می‌کند."}, "Doubao-lite-128k": {"description": "Doubao-lite دارای سرعت پاسخگویی بی‌نظیر و نسبت قیمت به کارایی بهتر است و گزینه‌های انعطاف‌پذیرتری را برای سناریوهای مختلف مشتریان ارائه می‌دهد. از پنجره متنی 128k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "Doubao-lite-32k": {"description": "Doubao-lite دارای سرعت پاسخگویی بی‌نظیر و نسبت قیمت به کارایی بهتر است و گزینه‌های انعطاف‌پذیرتری را برای سناریوهای مختلف مشتریان ارائه می‌دهد. از پنجره متنی 32k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "Doubao-lite-4k": {"description": "Doubao-lite دارای سرعت پاسخگویی بی‌نظیر و نسبت قیمت به کارایی بهتر است و گزینه‌های انعطاف‌پذیرتری را برای سناریوهای مختلف مشتریان ارائه می‌دهد. از پنجره متنی 4k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "Doubao-pro-128k": {"description": "مدل اصلی با بهترین عملکرد، مناسب برای انجام وظایف پیچیده است و در زمینه‌هایی مانند پاسخ به سوالات مرجع، خلاصه‌سازی، خلق محتوا، دسته‌بندی متن و نقش‌آفرینی عملکرد بسیار خوبی دارد. از پنجره متنی 128k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "Doubao-pro-32k": {"description": "مدل اصلی با بهترین عملکرد، مناسب برای انجام وظایف پیچیده است و در زمینه‌هایی مانند پاسخ به سوالات مرجع، خلاصه‌سازی، خلق محتوا، دسته‌بندی متن و نقش‌آفرینی عملکرد بسیار خوبی دارد. از پنجره متنی 32k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "Doubao-pro-4k": {"description": "مدل اصلی با بهترین عملکرد، مناسب برای انجام وظایف پیچیده است و در زمینه‌هایی مانند پاسخ به سوالات مرجع، خلاصه‌سازی، خلق محتوا، دسته‌بندی متن و نقش‌آفرینی عملکرد بسیار خوبی دارد. از پنجره متنی 4k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "DreamO": {"description": "DreamO یک مدل تولید تصویر سفارشی متن‌باز است که توسط شرکت بایت‌دنس و دانشگاه پکن به صورت مشترک توسعه یافته است و هدف آن پشتیبانی از تولید چندوظیفه‌ای تصویر از طریق معماری یکپارچه است. این مدل از روش مدل‌سازی ترکیبی کارآمد استفاده می‌کند و می‌تواند تصاویر بسیار سازگار و سفارشی‌شده‌ای را بر اساس شرایطی مانند هویت، موضوع، سبک و پس‌زمینه که توسط کاربر تعیین می‌شود، تولید کند."}, "ERNIE-3.5-128K": {"description": "مدل زبان بزرگ پرچمدار توسعه‌یافته توسط بایدو، که حجم عظیمی از متون چینی و انگلیسی را پوشش می‌دهد و دارای توانایی‌های عمومی قدرتمندی است. این مدل می‌تواند نیازهای اکثر سناریوهای پرسش و پاسخ، تولید محتوا و استفاده از افزونه‌ها را برآورده کند؛ همچنین از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا به‌روز بودن اطلاعات پرسش و پاسخ را تضمین کند."}, "ERNIE-3.5-8K": {"description": "مدل زبان بزرگ پرچمدار توسعه‌یافته توسط بایدو، که حجم عظیمی از متون چینی و انگلیسی را پوشش می‌دهد و دارای توانایی‌های عمومی قدرتمندی است. این مدل می‌تواند نیازهای اکثر سناریوهای پرسش و پاسخ، تولید محتوا و استفاده از افزونه‌ها را برآورده کند؛ همچنین از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا به‌روز بودن اطلاعات پرسش و پاسخ را تضمین نماید."}, "ERNIE-3.5-8K-Preview": {"description": "مدل زبان بزرگ پرچمدار توسعه‌یافته توسط بایدو، که حجم عظیمی از متون چینی و انگلیسی را پوشش می‌دهد و دارای توانایی‌های عمومی قدرتمندی است. این مدل می‌تواند نیازهای اکثر سناریوهای پرسش و پاسخ، تولید محتوا و استفاده از افزونه‌ها را برآورده کند؛ همچنین از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا به‌روز بودن اطلاعات پرسش و پاسخ را تضمین کند."}, "ERNIE-4.0-8K-Latest": {"description": "مدل زبان بزرگ مقیاس پرچمدار توسعه‌یافته توسط بایدو، که نسبت به ERNIE 3.5 ارتقاء کامل در توانایی‌های مدل را به ارمغان آورده است و برای وظایف پیچیده در حوزه‌های مختلف مناسب است؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند و به‌روزرسانی اطلاعات پرسش و پاسخ را تضمین می‌نماید."}, "ERNIE-4.0-8K-Preview": {"description": "مدل زبان بزرگ مقیاس پرچمدار توسعه‌یافته توسط بایدو، در مقایسه با ERNIE 3.5 ارتقاء کامل توانایی‌های مدل را به ارمغان آورده و برای وظایف پیچیده در حوزه‌های مختلف مناسب است؛ از افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به‌روز بماند."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "مدل زبان بزرگ و پیشرفته‌ای که توسط بایدو توسعه یافته است، با عملکرد برجسته در زمینه‌های مختلف و مناسب برای وظایف پیچیده؛ از افزونه جستجوی بایدو به‌طور خودکار پشتیبانی می‌کند تا اطلاعات به‌روز را در پاسخ‌ها تضمین کند. در مقایسه با ERNIE 4.0، عملکرد بهتری دارد."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "مدل زبان بزرگ و پرچمدار با مقیاس فوق‌العاده که توسط بایدو توسعه یافته است، با عملکرد برجسته در زمینه‌های مختلف و مناسب برای وظایف پیچیده؛ پشتیبانی از اتصال خودکار به افزونه جستجوی بایدو برای اطمینان از به‌روز بودن اطلاعات پرسش و پاسخ. در مقایسه با ERNIE 4.0، عملکرد بهتری دارد."}, "ERNIE-Character-8K": {"description": "مدل زبان بزرگ عمودی توسعه‌یافته توسط بایدو، مناسب برای صحنه‌های کاربردی مانند NPCهای بازی، مکالمات پشتیبانی مشتری، و نقش‌آفرینی در مکالمات. سبک شخصیت‌ها برجسته‌تر و یکپارچه‌تر است، توانایی پیروی از دستورات قوی‌تر و عملکرد استدلالی بهینه‌تر است."}, "ERNIE-Lite-Pro-128K": {"description": "مدل زبان بزرگ سبک‌وزن توسعه‌یافته توسط بایدو، که تعادل بین عملکرد مدل عالی و کارایی استنتاج را حفظ می‌کند. عملکرد آن بهتر از ERNIE Lite است و برای استفاده در کارت‌های شتاب‌دهنده AI با قدرت محاسباتی پایین مناسب است."}, "ERNIE-Speed-128K": {"description": "مدل زبان بزرگ با عملکرد بالا که در سال 2024 توسط بایدو توسعه یافته است. این مدل دارای توانایی‌های عمومی برجسته‌ای است و به عنوان یک مدل پایه برای تنظیم دقیق در سناریوهای خاص مناسب است و همچنین از عملکرد استنتاجی بسیار خوبی برخوردار است."}, "ERNIE-Speed-Pro-128K": {"description": "مدل زبان بزرگ با عملکرد بالا که در سال 2024 توسط بایدو به‌طور مستقل توسعه یافته است. این مدل دارای توانایی‌های عمومی برجسته‌ای است و عملکرد بهتری نسبت به ERNIE Speed دارد. مناسب برای استفاده به عنوان مدل پایه برای تنظیم دقیق و حل بهتر مسائل در سناریوهای خاص، همچنین دارای عملکرد استنتاجی بسیار عالی است."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev یک مدل تولید و ویرایش تصویر چندرسانه‌ای است که توسط Black Forest Labs توسعه یافته و بر اساس معماری Rectified Flow Transformer ساخته شده است. این مدل با 12 میلیارد پارامتر، بر تولید، بازسازی، تقویت یا ویرایش تصاویر تحت شرایط متنی تمرکز دارد. این مدل ترکیبی از مزایای تولید کنترل‌شده مدل‌های انتشار و قابلیت مدل‌سازی زمینه‌ای ترنسفورمر است و از خروجی تصاویر با کیفیت بالا پشتیبانی می‌کند و در وظایفی مانند ترمیم تصویر، تکمیل تصویر و بازسازی صحنه‌های بصری کاربرد گسترده دارد."}, "FLUX.1-dev": {"description": "FLUX.1-dev یک مدل زبان چندرسانه‌ای متن‌باز است که توسط Black Forest Labs توسعه یافته و برای وظایف ترکیبی تصویر و متن بهینه شده است. این مدل بر پایه مدل‌های زبان بزرگ پیشرفته مانند Mistral-7B ساخته شده و با استفاده از رمزگذار بصری طراحی‌شده و تنظیم دقیق چندمرحله‌ای دستوری، توانایی پردازش همزمان تصویر و متن و استدلال در وظایف پیچیده را دارد."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) یک مدل نوآورانه است که برای کاربردهای چندرشته‌ای و وظایف پیچیده مناسب است."}, "HelloMeme": {"description": "HelloMeme یک ابزار هوش مصنوعی است که می‌تواند بر اساس تصاویر یا حرکاتی که شما ارائه می‌دهید، به طور خودکار میم، گیف یا ویدیوهای کوتاه تولید کند. این ابزار نیازی به دانش نقاشی یا برنامه‌نویسی ندارد و تنها با داشتن تصاویر مرجع، می‌تواند محتوایی زیبا، سرگرم‌کننده و با سبک یکپارچه برای شما بسازد."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full یک مدل بزرگ ویرایش تصویر چندرسانه‌ای متن‌باز است که توسط HiDream.ai توسعه یافته است. این مدل بر پایه معماری پیشرفته Diffusion Transformer ساخته شده و با توانایی قوی درک زبان (با LLaMA 3.1-8B-Instruct داخلی) از طریق دستورات زبان طبیعی، تولید تصویر، انتقال سبک، ویرایش موضعی و بازنقاشی محتوا را پشتیبانی می‌کند و دارای قابلیت‌های برجسته در درک و اجرای ترکیب تصویر و متن است."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled یک مدل سبک تولید تصویر از متن است که با استفاده از تکنیک تقطیر بهینه شده و قادر است به سرعت تصاویر با کیفیت بالا تولید کند، به ویژه مناسب محیط‌های با منابع محدود و وظایف تولید در زمان واقعی است."}, "InstantCharacter": {"description": "InstantCharacter یک مدل تولید شخصیت شخصی‌سازی شده بدون نیاز به تنظیم دقیق است که توسط تیم هوش مصنوعی Tencent در سال ۲۰۲۵ منتشر شده است. هدف این مدل تولید شخصیت‌های با وفاداری بالا و سازگار در صحنه‌های مختلف است. این مدل تنها با یک تصویر مرجع قادر به مدل‌سازی شخصیت است و می‌تواند آن را به سبک‌ها، حرکات و پس‌زمینه‌های مختلف به طور انعطاف‌پذیر منتقل کند."}, "InternVL2-8B": {"description": "InternVL2-8B یک مدل زبان بصری قدرتمند است که از پردازش چند حالتی تصویر و متن پشتیبانی می‌کند و قادر است محتوای تصویر را به دقت شناسایی کرده و توصیف یا پاسخ‌های مرتبط تولید کند."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B یک مدل زبان بصری قدرتمند است که از پردازش چند حالتی تصویر و متن پشتیبانی می‌کند و قادر است محتوای تصویر را به دقت شناسایی کرده و توصیف یا پاسخ‌های مرتبط تولید کند."}, "Kolors": {"description": "Kolors یک مدل تولید تصویر از متن است که توسط تیم Kolors شرکت Kuaishou توسعه یافته است. این مدل با میلیاردها پارامتر آموزش دیده و در کیفیت بصری، درک معنایی زبان چینی و رندر متن عملکرد برجسته‌ای دارد."}, "Kwai-Kolors/Kolors": {"description": "Kolors یک مدل بزرگ تولید تصویر از متن مبتنی بر انتشار نهفته است که توسط تیم Kolors شرکت Kuaishou توسعه یافته است. این مدل با آموزش روی میلیاردها جفت متن-تصویر، در کیفیت بصری، دقت معنایی پیچیده و رندر کاراکترهای چینی و انگلیسی عملکرد برجسته‌ای دارد. این مدل نه تنها از ورودی‌های چینی و انگلیسی پشتیبانی می‌کند بلکه در درک و تولید محتوای خاص زبان چینی نیز بسیار توانمند است."}, "Llama-3.2-11B-Vision-Instruct": {"description": "توانایی استدلال تصویری عالی در تصاویر با وضوح بالا، مناسب برای برنامه‌های درک بصری."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "توانایی استدلال تصویری پیشرفته برای برنامه‌های نمایندگی درک بصری."}, "Meta-Llama-3.1-405B-Instruct": {"description": "مدل متنی تنظیم شده لاما 3.1 که برای موارد مکالمه چند زبانه بهینه‌سازی شده و در بسیاری از مدل‌های چت متن باز و بسته موجود، در معیارهای صنعتی رایج عملکرد عالی دارد."}, "Meta-Llama-3.1-70B-Instruct": {"description": "مدل متنی تنظیم شده لاما 3.1 که برای موارد مکالمه چند زبانه بهینه‌سازی شده و در بسیاری از مدل‌های چت متن باز و بسته موجود، در معیارهای صنعتی رایج عملکرد عالی دارد."}, "Meta-Llama-3.1-8B-Instruct": {"description": "مدل متنی تنظیم شده لاما 3.1 که برای موارد مکالمه چند زبانه بهینه‌سازی شده و در بسیاری از مدل‌های چت متن باز و بسته موجود، در معیارهای صنعتی رایج عملکرد عالی دارد."}, "Meta-Llama-3.2-1B-Instruct": {"description": "مدل زبان کوچک پیشرفته و پیشرفته، با قابلیت درک زبان، توانایی استدلال عالی و توانایی تولید متن."}, "Meta-Llama-3.2-3B-Instruct": {"description": "مدل زبان کوچک پیشرفته و پیشرفته، با قابلیت درک زبان، توانایی استدلال عالی و توانایی تولید متن."}, "Meta-Llama-3.3-70B-Instruct": {"description": "لاما 3.3 پیشرفته‌ترین مدل زبان چند زبانه و متن باز در سری لاما است که با هزینه‌ای بسیار کم، عملکردی مشابه مدل 405B را ارائه می‌دهد. این مدل بر اساس ساختار ترنسفورمر طراحی شده و از طریق تنظیم دقیق نظارتی (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) بهبود یافته است تا کارایی و ایمنی آن افزایش یابد. نسخه تنظیم شده آن به طور خاص برای مکالمات چند زبانه بهینه‌سازی شده و در چندین معیار صنعتی، عملکردی بهتر از بسیاری از مدل‌های چت متن باز و بسته دارد. تاریخ قطع دانش آن تا دسامبر 2023 است."}, "MiniMax-M1": {"description": "مدل استنتاج کاملاً توسعه‌یافته داخلی. پیشرو در جهان: ۸۰ هزار زنجیره فکری در برابر ۱ میلیون ورودی، عملکردی برابر با مدل‌های برتر خارجی."}, "MiniMax-Text-01": {"description": "در سری مدل‌های MiniMax-01، ما نوآوری‌های جسورانه‌ای انجام داده‌ایم: برای اولین بار مکانیزم توجه خطی را به طور وسیع پیاده‌سازی کرده‌ایم و معماری سنتی Transformer دیگر تنها گزینه نیست. این مدل دارای 456 میلیارد پارامتر است که در یک بار فعال‌سازی 45.9 میلیارد است. عملکرد کلی این مدل با بهترین مدل‌های خارجی برابری می‌کند و در عین حال می‌تواند به طور مؤثر به متن‌های طولانی جهانی با 4 میلیون توکن رسیدگی کند، که 32 برابر GPT-4o و 20 برابر Claude-3.5-Sonnet است."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 یک مدل استنتاج بزرگ با وزن‌های متن‌باز و توجه ترکیبی است که دارای ۴۵۶ میلیارد پارامتر است و هر توکن می‌تواند حدود ۴۵.۹ میلیارد پارامتر را فعال کند. این مدل به طور بومی از زمینه بسیار طولانی ۱ میلیون توکن پشتیبانی می‌کند و با مکانیزم توجه سریع، در وظایف تولید ۱۰۰ هزار توکن نسبت به DeepSeek R1، ۷۵٪ از محاسبات نقطه شناور را صرفه‌جویی می‌کند. همچنین، MiniMax-M1 از معماری MoE (متخصصان ترکیبی) بهره می‌برد و با ترکیب الگوریتم CISPO و طراحی توجه ترکیبی در آموزش تقویتی کارآمد، عملکرد پیشرو در صنعت را در استنتاج ورودی‌های طولانی و سناریوهای واقعی مهندسی نرم‌افزار ارائه می‌دهد."}, "Moonshot-Kimi-K2-Instruct": {"description": "مدل با 1 تریلیون پارامتر کل و 32 میلیارد پارامتر فعال. در میان مدل‌های غیرتفکری، در دانش پیشرفته، ریاضیات و برنامه‌نویسی در سطح برتر قرار دارد و در وظایف عامل عمومی تخصص دارد. به طور ویژه برای وظایف نمایندگی بهینه شده است، نه تنها قادر به پاسخگویی به سوالات بلکه قادر به انجام اقدامات است. بهترین گزینه برای گفتگوهای بداهه، چت عمومی و تجربه‌های نمایندگی است و یک مدل واکنشی بدون نیاز به تفکر طولانی مدت محسوب می‌شود."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) یک مدل دستورالعمل با دقت بالا است که برای محاسبات پیچیده مناسب است."}, "OmniConsistency": {"description": "OmniConsistency با معرفی مدل‌های بزرگ Diffusion Transformers (DiTs) و داده‌های سبک‌دار جفت‌شده، انسجام سبک و قابلیت تعمیم در وظایف تصویر به تصویر (Image-to-Image) را بهبود می‌بخشد و از افت کیفیت سبک جلوگیری می‌کند."}, "Phi-3-medium-128k-instruct": {"description": "همان مدل Phi-3-medium، اما با اندازه بزرگتر زمینه، مناسب برای RAG یا تعداد کمی از دستورات."}, "Phi-3-medium-4k-instruct": {"description": "یک مدل با ۱۴ میلیارد پارامتر که کیفیت آن بهتر از Phi-3-mini است و تمرکز آن بر داده‌های با کیفیت بالا و فشرده‌سازی استدلالی است."}, "Phi-3-mini-128k-instruct": {"description": "مدل مشابه Phi-3-mini، اما با اندازه بزرگتر زمینه، مناسب برای RAG یا تعداد کمی از دستورات."}, "Phi-3-mini-4k-instruct": {"description": "کوچک‌ترین عضو خانواده Phi-3، بهینه‌سازی شده برای کیفیت و تأخیر کم."}, "Phi-3-small-128k-instruct": {"description": "همان مدل Phi-3-small، اما با اندازه بزرگتر زمینه، مناسب برای RAG یا تعداد کمی از دستورات."}, "Phi-3-small-8k-instruct": {"description": "یک مدل با ۷ میلیارد پارامتر که کیفیت آن بهتر از Phi-3-mini است و تمرکز آن بر داده‌های با کیفیت بالا و فشرده‌سازی استدلالی است."}, "Phi-3.5-mini-instruct": {"description": "نسخه به‌روزرسانی‌شده مدل Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "نسخه به‌روزرسانی‌شده مدل Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct یک مدل زبانی بزرگ با تنظیم دقیق دستوری در سری Qwen2 است که اندازه پارامتر آن 7B است. این مدل بر اساس معماری Transformer ساخته شده و از تکنیک‌های SwiGLU،偏置 QKV توجه و توجه گروهی استفاده می‌کند. این مدل قادر به پردازش ورودی‌های بزرگ مقیاس است. این مدل در درک زبان، تولید، توانایی چند زبانه، کدنویسی، ریاضی و استدلال در چندین آزمون معیار عملکرد عالی دارد و از اکثر مدل‌های متن باز پیشی گرفته و در برخی وظایف رقابت قابل توجهی با مدل‌های اختصاصی نشان می‌دهد. Qwen2-7B-Instruct در چندین ارزیابی از Qwen1.5-7B-Chat پیشی گرفته و بهبود قابل توجهی در عملکرد نشان داده است."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct یکی از جدیدترین سری مدل‌های زبانی بزرگ منتشر شده توسط Alibaba Cloud است. این مدل 7B در زمینه‌های کدنویسی و ریاضی دارای توانایی‌های بهبود یافته قابل توجهی است. این مدل همچنین از پشتیبانی چند زبانه برخوردار است و بیش از 29 زبان از جمله چینی و انگلیسی را پوشش می‌دهد. این مدل در پیروی از دستورات، درک داده‌های ساختاری و تولید خروجی‌های ساختاری (به ویژه JSON) به طور قابل توجهی بهبود یافته است."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct جدیدترین نسخه از سری مدل‌های زبانی بزرگ خاص کد است که توسط Alibaba Cloud منتشر شده است. این مدل بر اساس Qwen2.5 و با آموزش 5.5 تریلیون توکن، توانایی تولید کد، استدلال و اصلاح را به طور قابل توجهی افزایش داده است. این مدل نه تنها توانایی کدنویسی را تقویت کرده بلکه مزایای ریاضی و عمومی را نیز حفظ کرده است. این مدل پایه‌ای جامع‌تر برای کاربردهای عملی مانند عامل‌های کد فراهم می‌کند."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL عضو جدید سری Qwen است که توانایی قدرتمند درک بصری دارد. این مدل می‌تواند متن، نمودارها و طرح‌بندی‌های درون تصاویر را تحلیل کند و همچنین قادر به درک ویدیوهای بلند و گرفتن رویدادهاست. این مدل می‌تواند استدلال کند، ابزارها را عملیاتی کند، و از چندین فرمت برای تعیین موقعیت اشیا و تولید خروجی ساختاری پشتیبانی می‌کند. همچنین، آن از رزولوشن و نرخ فریم پویا برای درک ویدیو بهینه‌سازی شده است و کارایی کدگذار بصری آن نیز افزایش یافته است."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking یک مدل زبان تصویری متن‌باز (VLM) است که به‌طور مشترک توسط Zhizhu AI و آزمایشگاه KEG دانشگاه تسینگ‌هوا منتشر شده است و به‌طور خاص برای پردازش وظایف شناختی چندرسانه‌ای پیچیده طراحی شده است. این مدل بر اساس مدل پایه GLM-4-9B-0414 ساخته شده و با معرفی مکانیزم استدلال «زنجیره تفکر» (Chain-of-Thought) و استفاده از استراتژی یادگیری تقویتی، به‌طور قابل توجهی توانایی استدلال چندرسانه‌ای و پایداری آن را بهبود بخشیده است."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat نسخه متن باز از مدل‌های پیش‌آموزش شده سری GLM-4 است که توسط AI Zhizhu ارائه شده است. این مدل در زمینه‌های معنایی، ریاضی، استدلال، کد و دانش عملکرد عالی دارد. علاوه بر پشتیبانی از گفتگوی چند دور، GLM-4-9B-Chat همچنین دارای قابلیت‌های پیشرفته‌ای مانند مرور وب، اجرای کد، فراخوانی ابزارهای سفارشی (Function Call) و استدلال متن طولانی است. این مدل از 26 زبان پشتیبانی می‌کند، از جمله چینی، انگلیسی، ژاپنی، کره‌ای و آلمانی. در چندین آزمون معیار، GLM-4-9B-Chat عملکرد عالی نشان داده است، مانند AlignBench-v2، MT-Bench، MMLU و C-Eval. این مدل از حداکثر طول زمینه 128K پشتیبانی می‌کند و برای تحقیقات علمی و کاربردهای تجاری مناسب است."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 یک مدل استنتاجی مبتنی بر یادگیری تقویتی (RL) است که مشکلات تکرار و خوانایی را در مدل حل می‌کند. قبل از RL، DeepSeek-R1 داده‌های شروع سرد را معرفی کرده و عملکرد استنتاج را بهینه‌سازی کرده است. این مدل در وظایف ریاضی، کد و استنتاج با OpenAI-o1 عملکرد مشابهی دارد و از طریق روش‌های آموزشی به دقت طراحی شده، عملکرد کلی را بهبود می‌بخشد."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B مد<PERSON>ی است که بر اساس Qwen2.5-Math-7B از طریق دستیابی به دانش (Knowledge Distillation) ساخته شده است. این مدل با استفاده از 800,000 نمونه انتخابی تولید شده توسط DeepSeek-R1 آموزش داده شده و توانایی استنتاج ممتازی نشان می‌دهد. این مدل در چندین تست استاندارد عملکرد خوبی داشته است، از جمله دقت 92.8٪ در MATH-500، نرخ موفقیت 55.5٪ در AIME 2024 و نمره 1189 در CodeForces، که نشان‌دهنده توانایی‌های قوی ریاضی و برنامه‌نویسی برای یک مدل با حجم 7B است."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 یک مدل زبان با 671 میلیارد پارامتر است که از معماری متخصصان ترکیبی (MoE) و توجه چندسر (MLA) استفاده می‌کند و با استراتژی تعادل بار بدون ضرر کمکی بهینه‌سازی کارایی استنتاج و آموزش را انجام می‌دهد. این مدل با پیش‌آموزش بر روی 14.8 تریلیون توکن با کیفیت بالا و انجام تنظیم دقیق نظارتی و یادگیری تقویتی، در عملکرد از سایر مدل‌های متن‌باز پیشی می‌گیرد و به مدل‌های بسته پیشرو نزدیک می‌شود."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 یک مدل پایه با معماری MoE است که دارای توانایی‌های بسیار قوی در کدنویسی و عامل است، با 1 تریلیون پارامتر کل و 32 میلیارد پارامتر فعال. در آزمون‌های معیار عملکرد در حوزه‌های دانش عمومی، برنامه‌نویسی، ریاضیات و عامل، مدل K2 عملکردی فراتر از سایر مدل‌های متن‌باز اصلی دارد."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview یک مدل پردازش زبان طبیعی نوآورانه است که قادر به پردازش کارآمد مکالمات پیچیده و درک زمینه است."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview یک مدل تحقیقاتی است که توسط تیم Qwen توسعه یافته و بر روی توانایی‌های استنتاج بصری تمرکز دارد و در درک صحنه‌های پیچیده و حل مسائل ریاضی مرتبط با بصری دارای مزیت‌های منحصر به فردی است."}, "Qwen/QwQ-32B": {"description": "QwQ مدل استنتاجی از سری Qwen است. در مقایسه با مدل‌های سنتی بهینه‌سازی دستورالعمل، QwQ دارای توانایی تفکر و استنتاج است و می‌تواند در وظایف پایین‌دستی عملکرد قابل توجهی را به ویژه در حل مسائل دشوار ارائه دهد. QwQ-32B یک مدل استنتاجی متوسط است که می‌تواند در مقایسه با مدل‌های استنتاجی پیشرفته (مانند DeepSeek-R1، o1-mini) عملکرد رقابتی را به دست آورد. این مدل از تکنیک‌هایی مانند RoPE، SwiGLU، RMSNorm و Attention QKV bias استفاده می‌کند و دارای ساختار شبکه 64 لایه و 40 سر توجه Q (در معماری GQA، KV برابر با 8 است) می‌باشد."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview جدیدترین مدل تحقیقاتی تجربی Qwen است که بر بهبود توانایی استدلال AI تمرکز دارد. با کاوش در مکانیزم‌های پیچیده‌ای مانند ترکیب زبان و استدلال بازگشتی، مزایای اصلی شامل توانایی تحلیل استدلال قوی، توانایی ریاضی و برنامه‌نویسی است. در عین حال، مشکلاتی مانند تغییر زبان، حلقه‌های استدلال، ملاحظات ایمنی و تفاوت‌های دیگر در توانایی‌ها وجود دارد."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen 2 Instruct (72B) دستورالعمل‌های دقیق برای کاربردهای سازمانی ارائه می‌دهد و به درستی به آن‌ها پاسخ می‌دهد."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct یک مدل زبانی بزرگ با تنظیم دقیق دستوری در سری Qwen2 است که اندازه پارامتر آن 72B است. این مدل بر اساس معماری Transformer ساخته شده و از تکنیک‌های SwiGLU،偏置 QKV توجه و توجه گروهی استفاده می‌کند. این مدل قادر به پردازش ورودی‌های بزرگ مقیاس است. این مدل در درک زبان، تولید، توانایی چند زبانه، کدنویسی، ریاضی و استدلال در چندین آزمون معیار عملکرد عالی دارد و از اکثر مدل‌های متن باز پیشی گرفته و در برخی وظایف رقابت قابل توجهی با مدل‌های اختصاصی نشان می‌دهد."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL جدیدترین نسخه از مدل Qwen-VL است که در آزمون‌های معیار درک بصری به پیشرفته‌ترین عملکرد دست یافته است."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 یک سری جدید از مدل‌های زبانی بزرگ است که با هدف بهینه‌سازی پردازش وظایف دستوری طراحی شده است."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 یک سری جدید از مدل‌های زبانی بزرگ است که با هدف بهینه‌سازی پردازش وظایف دستوری طراحی شده است."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "مدل زبانی بزرگ توسعه یافته توسط تیم علی‌بابا، تونگ‌yi چن‌وِن."}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 یک سری جدید از مدل‌های زبان بزرگ است که دارای توانایی‌های قوی‌تر در درک و تولید می‌باشد."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 یک سری جدید از مدل‌های زبانی بزرگ است که با هدف بهینه‌سازی پردازش وظایف دستوری طراحی شده است."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 یک سری جدید از مدل‌های زبان بزرگ است که با هدف بهینه‌سازی پردازش وظایف دستوری طراحی شده است."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 یک سری جدید از مدل‌های زبانی بزرگ است که با هدف بهینه‌سازی پردازش وظایف دستوری طراحی شده است."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder بر نوشتن کد تمرکز دارد."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct جدیدترین نسخه از سری مدل‌های زبانی بزرگ خاص کد است که توسط Alibaba Cloud منتشر شده است. این مدل بر اساس Qwen2.5 و با آموزش 5.5 تریلیون توکن، توانایی تولید کد، استدلال و اصلاح را به طور قابل توجهی افزایش داده است. این مدل نه تنها توانایی کدنویسی را تقویت کرده بلکه مزایای ریاضی و عمومی را نیز حفظ کرده است. این مدل پایه‌ای جامع‌تر برای کاربردهای عملی مانند عامل‌های کد فراهم می‌کند."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct یک مدل چند حالتی از تیم Thousand Questions است که بخشی از سری Qwen2.5-VL می‌باشد. این مدل علاوه بر توانایی شناسایی اشیاء رایج، قادر به تحلیل متن، نمودار، نمادها، شکل‌ها و طرح‌بندی‌های درون تصاویر است. این مدل به عنوان یک هوش مصنوعی بصری عمل می‌کند، قادر به استدلال و کنترل ابزارها به صورت پویا است و توانایی استفاده از کامپیوتر و موبایل را دارد. علاوه بر این، این مدل می‌تواند اشیاء درون تصویر را با دقت بالا مکان‌یابی کند و برای فاکتورها، جداول و غیره خروجی‌های ساختاریجادی تولید کند. نسبت به نسخه قبلی Qwen2-VL، این نسخه در توانایی‌های ریاضی و حل مسئله از طریق یادگیری تقویتی پیشرفت کرده است و سبک پاسخ‌گویی آن نیز بیشتر با ترجیحات انسان‌ها هماهنگ است."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL مدل زبان و تصویر از سری Qwen2.5 است. این مدل در جنبه‌های مختلف بهبود یافته است: دارای توانایی تحلیل بصری قوی‌تر، قادر به تشخیص اشیاء رایج، تحلیل متن، نمودارها و طرح‌بندی است؛ به عنوان یک عامل بصری می‌تواند استدلال کند و به طور پویا ابزارها را هدایت کند؛ از توانایی درک ویدیوهای طولانی‌تر از یک ساعت و شناسایی رویدادهای کلیدی برخوردار است؛ قادر به مکان‌یابی دقیق اشیاء در تصویر با تولید جعبه‌های مرزی یا نقاط است؛ و توانایی تولید خروجی‌های ساختاریافته، به ویژه برای داده‌های اسکن شده مانند فاکتورها و جداول را دارد."}, "Qwen/Qwen3-14B": {"description": "Qwen3 یک مدل بزرگ جدید با توانایی‌های بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانی به سطح پیشرفته صنعت دست یافته و از تغییر حالت تفکر پشتیبانی می‌کند."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 یک مدل بزرگ جدید با توانایی‌های بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانی به سطح پیشرفته صنعت دست یافته و از تغییر حالت تفکر پشتیبانی می‌کند."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 یک مدل زبان بزرگ ترکیبی (MoE) پرچمدار از سری Qwen3 است که توسط تیم Tongyi Qianwen شرکت علی‌بابا توسعه یافته است. این مدل دارای 235 میلیارد پارامتر کل و 22 میلیارد پارامتر فعال در هر استنتاج است. نسخه به‌روزشده‌ای از حالت غیرتفکری Qwen3-235B-A22B است که تمرکز بر بهبود قابل توجه در پیروی از دستورالعمل‌ها، استدلال منطقی، درک متن، ریاضیات، علوم، برنامه‌نویسی و استفاده از ابزارها دارد. همچنین پوشش دانش چندزبانه و ترجیحات کاربر در وظایف ذهنی و باز را بهبود بخشیده تا متن‌های مفیدتر و با کیفیت بالاتری تولید کند."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 عضوی از سری مدل‌های بزرگ زبان Qwen3 است که توسط تیم Tongyi Qianwen شرکت علی‌بابا توسعه یافته و بر وظایف استدلال پیچیده و دشوار تمرکز دارد. این مدل بر پایه معماری MoE با 235 میلیارد پارامتر کل ساخته شده و در هر توکن حدود 22 میلیارد پارامتر فعال می‌کند که باعث افزایش کارایی محاسباتی در عین حفظ قدرت عملکرد می‌شود. به عنوان یک مدل اختصاصی \"تفکر\"، در استدلال منطقی، ریاضیات، علوم، برنامه‌نویسی و آزمون‌های علمی که نیازمند تخصص انسانی هستند، عملکرد برجسته‌ای دارد و در میان مدل‌های تفکری متن‌باز در سطح برتر قرار دارد. همچنین توانایی‌های عمومی مانند پیروی از دستورالعمل‌ها، استفاده از ابزار و تولید متن را تقویت کرده و به طور بومی از درک متن‌های طولانی تا 256 هزار توکن پشتیبانی می‌کند که برای سناریوهای نیازمند استدلال عمیق و پردازش اسناد طولانی بسیار مناسب است."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 یک مدل بزرگ جدید با توانایی‌های بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانی به سطح پیشرفته صنعت دست یافته و از تغییر حالت تفکر پشتیبانی می‌کند."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 نسخه به‌روزرسانی شده مدل غیرتفکری Qwen3-30B-A3B است. این یک مدل متخصص ترکیبی (MoE) با مجموع ۳۰.۵ میلیارد پارامتر و ۳.۳ میلیارد پارامتر فعال است. این مدل در جنبه‌های مختلف بهبودهای کلیدی داشته است، از جمله افزایش قابل توجه در پیروی از دستورالعمل‌ها، استدلال منطقی، درک متن، ریاضیات، علوم، برنامه‌نویسی و استفاده از ابزارها. همچنین، پیشرفت قابل توجهی در پوشش دانش چندزبانه و تطابق بهتر با ترجیحات کاربران در وظایف ذهنی و باز دارد، که منجر به تولید پاسخ‌های مفیدتر و متون با کیفیت بالاتر می‌شود. علاوه بر این، توانایی درک متن‌های بلند این مدل تا ۲۵۶ هزار توکن افزایش یافته است. این مدل فقط از حالت غیرتفکری پشتیبانی می‌کند و خروجی آن شامل برچسب‌های `<think></think>` نخواهد بود."}, "Qwen/Qwen3-32B": {"description": "Qwen3 یک مدل بزرگ جدید با توانایی‌های بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانی به سطح پیشرفته صنعت دست یافته و از تغییر حالت تفکر پشتیبانی می‌کند."}, "Qwen/Qwen3-8B": {"description": "Qwen3 یک مدل بزرگ جدید با توانایی‌های بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانی به سطح پیشرفته صنعت دست یافته و از تغییر حالت تفکر پشتیبانی می‌کند."}, "Qwen2-72B-Instruct": {"description": "Qwen2 جدیدترین سری مدل‌های Qwen است که از 128k زمینه پشتیبانی می‌کند. در مقایسه با بهترین مدل‌های متن‌باز فعلی، Qwen2-72B در درک زبان طبیعی، دانش، کد، ریاضی و چندزبانگی به طور قابل توجهی از مدل‌های پیشرو فعلی فراتر رفته است."}, "Qwen2-7B-Instruct": {"description": "Qwen2 جدیدترین سری مدل‌های Qwen است که می‌تواند از مدل‌های متن‌باز با مقیاس مشابه و حتی بزرگتر فراتر رود. Qwen2 7B در چندین ارزیابی برتری قابل توجهی به دست آورده است، به ویژه در درک کد و زبان چینی."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B <PERSON><PERSON> مدل زبان بصری قدرتمند است که از پردازش چندرسانه‌ای تصویر و متن پشتیبانی می‌کند و می‌تواند محتوای تصویر را به دقت شناسایی کرده و توصیف یا پاسخ‌های مرتبط تولید کند."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct یک مدل زبان بزرگ با 140 میلیارد پارامتر است که عملکرد عالی دارد و بهینه‌سازی شده برای سناریوهای چینی و چند زبانه، از کاربردهایی مانند پرسش و پاسخ هوشمند و تولید محتوا پشتیبانی می‌کند."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct یک مدل زبان بزرگ با 320 میلیارد پارامتر است که عملکرد متوازن دارد و بهینه‌سازی شده برای سناریوهای چینی و چند زبانه، از کاربردهایی مانند پرسش و پاسخ هوشمند و تولید محتوا پشتیبانی می‌کند."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct از 16k زمینه پشتیبانی می‌کند و قادر به تولید متن‌های طولانی بیش از 8K است. این مدل از تماس‌های تابع و تعامل بدون درز با سیستم‌های خارجی پشتیبانی می‌کند و به طور قابل توجهی انعطاف‌پذیری و گسترش‌پذیری را افزایش می‌دهد. دانش مدل به وضوح افزایش یافته و توانایی‌های کدنویسی و ریاضی به طور چشمگیری بهبود یافته است و از بیش از 29 زبان پشتیبانی می‌کند."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct یک مدل زبان بزرگ با 70 میلیارد پارامتر است که از تماس‌های تابع و تعامل بی‌نقص با سیستم‌های خارجی پشتیبانی می‌کند و به طور قابل توجهی انعطاف‌پذیری و مقیاس‌پذیری را افزایش می‌دهد. این مدل بهینه‌سازی شده برای سناریوهای چینی و چند زبانه، از کاربردهایی مانند پرسش و پاسخ هوشمند و تولید محتوا پشتیبانی می‌کند."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct یک مدل دستور برنامه‌نویسی مبتنی بر پیش‌آموزش وسیع است که دارای توانایی‌های قوی در درک و تولید کد است و می‌تواند به طور مؤثر به انواع وظایف برنامه‌نویسی رسیدگی کند، به ویژه برای نوشتن کد هوشمند، تولید اسکریپت‌های خودکار و پاسخ به مسائل برنامه‌نویسی مناسب است."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct یک مدل زبان بزرگ است که به طور خاص برای تولید کد، درک کد و سناریوهای توسعه کارآمد طراحی شده است و از مقیاس 32B پارامتر پیشرفته در صنعت بهره می‌برد و می‌تواند نیازهای متنوع برنامه‌نویسی را برآورده کند."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B، مدل MoE (متخصص ترکیبی)، حالت «استدلال ترکیبی» را معرفی کرده است که به کاربران امکان می‌دهد به‌طور یکپارچه بین «حالت تفکر» و «حالت غیرتفکر» جابجا شوند. این مدل از درک و استدلال در ۱۱۹ زبان و گویش پشتیبانی می‌کند و دارای قابلیت‌های قدرتمند فراخوانی ابزار است. در آزمون‌های معیار مختلف از جمله توانایی‌های جامع، کد نویسی و ریاضیات، چندزبانه، دانش و استدلال، این مدل می‌تواند با مدل‌های پیشرو بازار مانند DeepSeek R1، OpenAI o1، o3-mini، Grok 3 و Google Gemini 2.5 Pro رقابت کند."}, "Qwen3-32B": {"description": "Qwen3-32B، مدل متراکم (Dense Model)، حالت «استدلال ترکیبی» را معرفی کرده است که به کاربران امکان می‌دهد به‌طور یکپارچه بین «حالت تفکر» و «حالت غیرتفکر» جابجا شوند. به دلیل بهبود ساختار مدل، افزایش داده‌های آموزشی و روش‌های مؤثرتر آموزش، عملکرد کلی این مدل با Qwen2.5-72B قابل مقایسه است."}, "SenseChat": {"description": "نسخه پایه مدل (V4)، طول متن ۴K، با توانایی‌های عمومی قوی"}, "SenseChat-128K": {"description": "نسخه پایه مدل (V4)، با طول زمینه ۱۲۸K، در وظایف درک و تولید متون طولانی عملکرد برجسته‌ای دارد"}, "SenseChat-32K": {"description": "مدل نسخه پایه (V4)، طول زمینه 32K، قابل استفاده در انواع سناریوها"}, "SenseChat-5": {"description": "جدیدترین نسخه مدل (V5.5)، با طول زمینه 128K، بهبود قابل توجه در زمینه‌های استدلال ریاضی، مکالمه انگلیسی، پیروی از دستورات و درک متون طولانی، قابل مقایسه با GPT-4o"}, "SenseChat-5-1202": {"description": "نسخه جدید مبتنی بر V5.5 که نسبت به نسخه قبلی در توانایی‌های پایه‌ای زبان‌های چینی و انگلیسی، گفتگو، دانش علوم پایه، دانش علوم انسانی، نوشتار، منطق ریاضی و کنترل تعداد کلمات بهبود قابل توجهی داشته است."}, "SenseChat-5-Cantonese": {"description": "طول متن 32K، در درک مکالمات به زبان کانتونی از GPT-4 پیشی می‌گیرد و در زمینه‌های مختلفی مانند دانش، استدلال، ریاضیات و برنامه‌نویسی با GPT-4 Turbo قابل مقایسه است."}, "SenseChat-5-beta": {"description": "برخی از عملکردها بهتر از SenseCat-5-1202 است"}, "SenseChat-Character": {"description": "نسخه استاندارد مدل، طول متن ۸۰۰۰ کاراکتر، سرعت پاسخ‌دهی بالا"}, "SenseChat-Character-Pro": {"description": "مدل پیشرفته، طول متن 32K، بهبود کامل قابلیت‌ها، پشتیبانی از مکالمه به زبان‌های چینی/انگلیسی"}, "SenseChat-Turbo": {"description": "مناسب برای پرسش و پاسخ سریع و تنظیم دقیق مدل"}, "SenseChat-Turbo-1202": {"description": "این نسخه جدید مدل سبک است که به بیش از ۹۰٪ توانایی‌های مدل کامل دست یافته و هزینه استنتاج را به طور قابل توجهی کاهش می‌دهد."}, "SenseChat-Vision": {"description": "مدل جدیدترین نسخه (V5.5) است که از ورودی چند تصویر پشتیبانی می‌کند و به طور جامع به بهینه‌سازی توانایی‌های پایه مدل پرداخته و در شناسایی ویژگی‌های اشیاء، روابط فضایی، شناسایی رویدادهای حرکتی، درک صحنه، شناسایی احساسات، استدلال منطقی و درک و تولید متن بهبودهای قابل توجهی داشته است."}, "SenseNova-V6-5-Pro": {"description": "با به‌روزرسانی جامع داده‌های چندرسانه‌ای، زبانی و استدلالی و بهینه‌سازی استراتژی‌های آموزش، مدل جدید پیشرفت قابل توجهی در استدلال چندرسانه‌ای و توانایی پیروی از دستورالعمل‌های تعمیم‌یافته داشته است. این مدل از پنجره متنی تا ۱۲۸ هزار توکن پشتیبانی می‌کند و در وظایف تخصصی مانند OCR و شناسایی IP گردشگری و فرهنگی عملکرد برجسته‌ای دارد."}, "SenseNova-V6-5-Turbo": {"description": "با به‌روزرسانی جامع داده‌های چندرسانه‌ای، زبانی و استدلالی و بهینه‌سازی استراتژی‌های آموزش، مدل جدید پیشرفت قابل توجهی در استدلال چندرسانه‌ای و توانایی پیروی از دستورالعمل‌های تعمیم‌یافته داشته است. این مدل از پنجره متنی تا ۱۲۸ هزار توکن پشتیبانی می‌کند و در وظایف تخصصی مانند OCR و شناسایی IP گردشگری و فرهنگی عملکرد برجسته‌ای دارد."}, "SenseNova-V6-Pro": {"description": "تحقق یکپارچگی بومی قابلیت‌های تصویر، متن و ویدیو، عبور از محدودیت‌های سنتی چندمدلی، و کسب دو قهرمانی در ارزیابی‌های OpenCompass و SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "توجه به استدلال عمیق بصری و زبانی، تحقق تفکر کند و استدلال عمیق، ارائه فرآیند کامل زنجیره تفکر."}, "SenseNova-V6-Turbo": {"description": "تحقق یکپارچگی بومی قابلیت‌های تصویر، متن و ویدیو، عبور از محدودیت‌های سنتی چندمدلی، پیشی گرفتن در ابعاد اصلی مانند توانایی‌های چندمدلی و زبانی، و در چندین ارزیابی در سطح اول داخلی و خارجی قرار گرفتن."}, "Skylark2-lite-8k": {"description": "مدل نسل دوم Skylark، مدل Skylark2-lite دارای سرعت پاسخ‌دهی بالایی است و برای سناریوهایی که نیاز به زمان واقعی بالایی دارند و حساس به هزینه هستند و نیاز به دقت مدلی کمتری دارند مناسب است. طول پنجره متنی این مدل 8k است."}, "Skylark2-pro-32k": {"description": "مدل نسل دوم Skylark، مدل Skylark2-pro دارای دقت بالای مدلی است و برای سناریوهای پیچیده‌تر تولید متن مانند تولید متن تخصصی، نوشتن رمان، ترجمه باکیفیت و غیره مناسب است. طول پنجره متنی این مدل 32k است."}, "Skylark2-pro-4k": {"description": "مدل نسل دوم Skylark، مدل Skylark2-pro دارای دقت بالای مدلی است و برای سناریوهای پیچیده‌تر تولید متن مانند تولید متن تخصصی، نوشتن رمان، ترجمه باکیفیت و غیره مناسب است. طول پنجره متنی این مدل 4k است."}, "Skylark2-pro-character-4k": {"description": "مدل نسل دوم Skylark، مدل Skylark2-pro-character دارای قابلیت‌های برجسته بازی نقش و چت است و می‌تواند به‌طور طبیعی طبق خواسته‌های کاربر مختلف نقش‌ها را ایفا کند. این مدل برای ساخت ربات‌های چت، دستیاران مجازی و خدمات مشتری آنلاین مناسب است و دارای سرعت پاسخ‌دهی بالایی است."}, "Skylark2-pro-turbo-8k": {"description": "مدل نسل دوم Skylark، مدل Skylark2-pro-turbo-8k دارای استنتاج سریعتر و هزینه کمتر است و طول پنجره متنی آن 8k است."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 نسل جدید مدل‌های متن‌باز سری GLM است که دارای 320 میلیارد پارامتر است. عملکرد این مدل می‌تواند با سری GPT OpenAI و سری V3/R1 DeepSeek مقایسه شود."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 یک مدل کوچک از سری GLM است که دارای 90 میلیارد پارامتر است. این مدل ویژگی‌های فنی سری GLM-4-32B را به ارث می‌برد، اما گزینه‌های استقرار سبک‌تری را ارائه می‌دهد. با وجود اندازه کوچک، GLM-4-9B-0414 در تولید کد، طراحی وب، تولید گرافیک SVG و نوشتن مبتنی بر جستجو عملکرد فوق‌العاده‌ای دارد."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking یک مدل زبان تصویری متن‌باز (VLM) است که به‌طور مشترک توسط Zhizhu AI و آزمایشگاه KEG دانشگاه تسینگ‌هوا منتشر شده است و به‌طور خاص برای پردازش وظایف شناختی چندرسانه‌ای پیچیده طراحی شده است. این مدل بر اساس مدل پایه GLM-4-9B-0414 ساخته شده و با معرفی مکانیزم استدلال «زنجیره تفکر» (Chain-of-Thought) و استفاده از استراتژی یادگیری تقویتی، به‌طور قابل توجهی توانایی استدلال چندرسانه‌ای و پایداری آن را بهبود بخشیده است."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 یک مدل استدلال با توانایی تفکر عمیق است. این مدل بر اساس GLM-4-32B-0414 از طریق راه‌اندازی سرد و یادگیری تقویتی توسعه یافته و در وظایف ریاضی، کدنویسی و منطقی آموزش بیشتری دیده است. نسبت به مدل پایه، GLM-Z1-32B-0414 توانایی‌های ریاضی و حل مسائل پیچیده را به‌طور قابل توجهی افزایش داده است."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 یک مدل کوچک از سری GLM است که تنها 90 میلیارد پارامتر دارد، اما در عین حال توانایی‌های شگفت‌انگیزی را در کنار حفظ سنت متن‌باز نشان می‌دهد. با وجود اندازه کوچک، این مدل در استدلال ریاضی و وظایف عمومی عملکرد عالی دارد و عملکرد کلی آن در میان مدل‌های متن‌باز با اندازه مشابه در سطح بالایی قرار دارد."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 یک مدل استدلال عمیق با توانایی تفکر است (که با Deep Research OpenAI مقایسه می‌شود). برخلاف مدل‌های تفکر عمیق معمولی، این مدل از تفکر عمیق طولانی‌مدت برای حل مسائل باز و پیچیده استفاده می‌کند."}, "THUDM/glm-4-9b-chat": {"description": "نسخه منبع باز GLM-4 9B، تجربه گفتگوی بهینه‌شده برای برنامه‌های مکالمه را ارائه می‌دهد."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B نخستین مدل بزرگ استدلال با زمینه طولانی است که با یادگیری تقویتی آموزش دیده و به طور خاص برای وظایف استدلال متون طولانی بهینه شده است. این مدل با چارچوب یادگیری تقویتی توسعه تدریجی زمینه، انتقال پایدار از زمینه کوتاه به بلند را محقق ساخته است. در هفت آزمون معیار پرسش و پاسخ اسناد طولانی، QwenLong-L1-32B از مدل‌های پیشرو مانند OpenAI-o3-mini و Qwen3-235B-A22B پیشی گرفته و عملکردی مشابه Claude-3.7-Sonnet-Thinking دارد. این مدل در استدلال ریاضی، استدلال منطقی و استدلال چندمرحله‌ای مهارت ویژه‌ای دارد."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B با حفظ توانایی‌های زبان عمومی عالی مدل‌های قبلی خود، از طریق آموزش افزایشی 500 میلیارد توکن با کیفیت بالا، به طور قابل توجهی توانایی‌های منطقی ریاضی و کدنویسی را افزایش داده است."}, "abab5.5-chat": {"description": "برای سناریوهای بهره‌وری طراحی شده است، از پردازش وظایف پیچیده و تولید متن کارآمد پشتیبانی می‌کند و برای کاربردهای حرفه‌ای مناسب است."}, "abab5.5s-chat": {"description": "طراحی شده برای سناریوهای مکالمه با شخصیت‌های چینی، ارائه توانایی تولید مکالمات با کیفیت بالا به زبان چینی، مناسب برای انواع کاربردها."}, "abab6.5g-chat": {"description": "طراحی شده برای مکالمات چندزبانه با شخصیت‌های مختلف، پشتیبانی از تولید مکالمات با کیفیت بالا به زبان انگلیسی و سایر زبان‌ها."}, "abab6.5s-chat": {"description": "مناسب برای طیف گسترده‌ای از وظایف پردازش زبان طبیعی، از جمله تولید متن، سیستم‌های گفتگو و غیره."}, "abab6.5t-chat": {"description": "بهینه‌سازی شده برای سناریوهای مکالمه با شخصیت‌های چینی، ارائه توانایی تولید مکالمات روان و مطابق با عادات بیانی چینی."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 یک مدل زبان بزرگ پیشرفته است که با یادگیری تقویتی و بهینه‌سازی داده‌های راه‌اندازی سرد، عملکرد استدلال، ریاضیات و برنامه‌نویسی فوق‌العاده‌ای دارد."}, "accounts/fireworks/models/deepseek-v3": {"description": "مدل زبان قدرتمند Mixture-of-Experts (MoE) ارائه شده توسط Deepseek، با مجموع پارامترها به میزان 671B و فعال‌سازی 37B پارامتر برای هر نشانه."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "مدل Llama 3 70B دستورالعمل، به‌طور ویژه برای مکالمات چندزبانه و درک زبان طبیعی بهینه‌سازی شده است و عملکردی بهتر از اکثر مدل‌های رقیب دارد."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "مدل Llama 3 8B دستورالعمل، بهینه‌سازی شده برای مکالمه و وظایف چندزبانه، با عملکرد برجسته و کارآمد."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "مدل Llama 3 8B دستورالعمل (نسخه HF)، با نتایج پیاده‌سازی رسمی سازگار است و از سازگاری بالا و قابلیت همکاری بین پلتفرمی برخوردار است."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "مدل Llama 3.1 405B دستورالعمل، با پارامترهای بسیار بزرگ، مناسب برای وظایف پیچیده و سناریوهای با بار سنگین در پیروی از دستورالعمل‌ها."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "مدل Llama 3.1 70B دستورالعمل، با توانایی برجسته در درک و تولید زبان طبیعی، انتخابی ایده‌آل برای وظایف مکالمه و تحلیل است."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "مدل Llama 3.1 8B دستورالعمل، بهینه‌سازی شده برای مکالمات چندزبانه، قادر به پیشی گرفتن از اکثر مدل‌های متن‌باز و بسته در معیارهای صنعتی رایج."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "مدل استنتاج تصویر با ۱۱ میلیارد پارامتر از Meta که برای دستورالعمل‌ها تنظیم شده است. این مدل برای تشخیص بصری، استنتاج تصویر، توصیف تصویر و پاسخ به سوالات عمومی درباره تصاویر بهینه‌سازی شده است. این مدل قادر به درک داده‌های بصری مانند نمودارها و گراف‌ها است و با تولید توضیحات متنی از جزئیات تصاویر، فاصله بین دیداری و زبانی را پر می‌کند."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "مدل Llama 3.2 3B دستورالعمل یک مدل چندزبانه سبک است که توسط Meta ارائه شده است. این مدل با هدف بهبود کارایی طراحی شده و در مقایسه با مدل‌های بزرگ‌تر، بهبودهای قابل توجهی در تأخیر و هزینه ارائه می‌دهد. نمونه‌های کاربردی این مدل شامل بازنویسی پرسش‌ها و دستورات و همچنین کمک به نوشتن است."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "مدل استنتاج تصویر با 90 میلیارد پارامتر از Meta که برای دستورالعمل‌ها تنظیم شده است. این مدل برای تشخیص بصری، استنتاج تصویر، توصیف تصویر و پاسخ به سوالات عمومی در مورد تصاویر بهینه‌سازی شده است. این مدل قادر است داده‌های بصری مانند نمودارها و گراف‌ها را درک کند و با تولید توضیحات متنی از جزئیات تصویر، فاصله بین دیداری و زبانی را پر کند."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "مدل Llama 3.3 70B Instruct نسخه به‌روزرسانی شده Llama 3.1 70B در دسامبر است. این مدل بر اساس Llama 3.1 70B (منتشر شده در ژوئیه 2024) بهبود یافته و قابلیت‌های فراخوانی ابزار، پشتیبانی از متن چند زبانه، ریاضیات و برنامه‌نویسی را تقویت کرده است. این مدل در استدلال، ریاضیات و پیروی از دستورات به سطح پیشرفته‌ای در صنعت رسیده و می‌تواند عملکردی مشابه با 3.1 405B ارائه دهد، در حالی که از نظر سرعت و هزینه مزایای قابل توجهی دارد."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "مدل 24B با پارامترهایی که قابلیت‌های پیشرفته‌ای مشابه مدل‌های بزرگتر را داراست."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "مدل Mixtral MoE 8x22B دستوری، با پارامترهای بزرگ و معماری چندین متخصص، پشتیبانی کامل از پردازش کارآمد وظایف پیچیده."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "مدل Mixtral MoE 8x7B، معماری چندین متخصص برای پیروی و اجرای دستورات به‌صورت کارآمد ارائه می‌دهد."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "مدل MythoMax L2 13B، با استفاده از تکنیک‌های ترکیبی نوآورانه، در روایت داستان و نقش‌آفرینی مهارت دارد."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi-3-Vision-128K-Instruct یک مدل چندوجهی پیشرفته و سبک است که بر اساس مجموعه داده‌هایی شامل داده‌های مصنوعی و وب‌سایت‌های عمومی فیلتر شده ساخته شده است. این مدل بر داده‌های بسیار باکیفیت و متمرکز بر استدلال، که شامل متن و تصویر هستند، تمرکز دارد. این مدل بخشی از سری مدل‌های Phi-3 است و نسخه چندوجهی آن از طول زمینه 128K (بر حسب توکن) پشتیبانی می‌کند. این مدل از یک فرآیند تقویت دقیق عبور کرده است که ترکیبی از تنظیم دقیق تحت نظارت و بهینه‌سازی مستقیم ترجیحات را شامل می‌شود تا از پیروی دقیق از دستورات و اقدامات امنیتی قوی اطمینان حاصل شود."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "مدل QwQ یک مدل تحقیقاتی تجربی است که توسط تیم Qwen توسعه یافته و بر تقویت توانایی استدلال AI تمرکز دارد."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "نسخه 72B مدل Qwen-VL نتیجه جدیدترین به‌روزرسانی‌های علی‌بابا است که نمایانگر نوآوری‌های نزدیک به یک سال اخیر است."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 مجموعه‌ای از مدل‌های زبانی است که تنها شامل رمزگشاها می‌باشد و توسط تیم Qwen علی‌بابا کلود توسعه یافته است. این مدل‌ها در اندازه‌های مختلف از جمله 0.5B، 1.5B، 3B، 7B، 14B، 32B و 72B ارائه می‌شوند و دارای دو نوع پایه (base) و دستوری (instruct) هستند."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct جدیدترین نسخه از سری مدل‌های زبانی بزرگ خاص کد است که توسط Alibaba Cloud منتشر شده است. این مدل بر اساس Qwen2.5 و با آموزش 5.5 تریلیون توکن، توانایی تولید کد، استدلال و اصلاح را به طور قابل توجهی افزایش داده است. این مدل نه تنها توانایی کدنویسی را تقویت کرده بلکه مزایای ریاضی و عمومی را نیز حفظ کرده است. این مدل پایه‌ای جامع‌تر برای کاربردهای عملی مانند عامل‌های کد فراهم می‌کند."}, "accounts/yi-01-ai/models/yi-large": {"description": "مدل Yi-Large، با توانایی برجسته در پردازش چندزبانه، مناسب برای انواع وظایف تولید و درک زبان."}, "ai21-jamba-1.5-large": {"description": "یک مدل چندزبانه با 398 میلیارد پارامتر (94 میلیارد فعال) که پنجره متنی طولانی 256 هزار توکن، فراخوانی توابع، خروجی ساختاریافته و تولید مبتنی بر واقعیت را ارائه می‌دهد."}, "ai21-jamba-1.5-mini": {"description": "یک مدل چندزبانه با 52 میلیارد پارامتر (12 میلیارد فعال) که پنجره متنی طولانی 256K، فراخوانی توابع، خروجی ساختاریافته و تولید مبتنی بر واقعیت را ارائه می‌دهد."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "یک مدل چندزبانه با ۳۹۸ میلیارد پارامتر (۹۴ میلیارد فعال) که پنجره زمینه ۲۵۶ هزار توکنی، فراخوانی توابع، خروجی ساختاریافته و تولید مبتنی بر واقعیت را ارائه می‌دهد."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "یک مدل چندزبانه با ۵۲ میلیارد پارامتر (۱۲ میلیارد فعال) که پنجره زمینه ۲۵۶ هزار توکنی، فراخوانی توابع، خروجی ساختاریافته و تولید مبتنی بر واقعیت را ارائه می‌دهد."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet استانداردهای صنعت را ارتقا داده است، عملکردی بهتر از مدل‌های رقیب و Claude 3 Opus دارد، در ارزیابی‌های گسترده به خوبی عمل کرده و در عین حال سرعت و هزینه مدل‌های سطح متوسط ما را حفظ می‌کند."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet استانداردهای صنعت را ارتقا داده است، عملکردی بهتر از مدل‌های رقیب و Claude 3 Opus دارد، در ارزیابی‌های گسترده به خوبی عمل کرده و در عین حال سرعت و هزینه مدل‌های سطح متوسط ما را حفظ می‌کند."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku سریع‌ترین و فشرده‌ترین مدل Anthropic است که پاسخ‌های تقریباً فوری ارائه می‌دهد. این مدل می‌تواند به سرعت به پرسش‌ها و درخواست‌های ساده پاسخ دهد. مشتریان قادر خواهند بود تجربه‌های هوش مصنوعی یکپارچه‌ای را که تعاملات انسانی را تقلید می‌کند، ایجاد کنند. Claude 3 Haiku می‌تواند تصاویر را پردازش کرده و خروجی متنی ارائه دهد و دارای پنجره متنی 200K است."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus قدرتمندترین مدل هوش مصنوعی Anthropic است که عملکرد پیشرفته‌ای در وظایف بسیار پیچیده دارد. این مدل می‌تواند با درخواست‌های باز و سناریوهای ناآشنا کار کند و دارای روانی و درک شبه‌انسانی برجسته‌ای است. Claude 3 Opus مرزهای جدیدی از امکانات هوش مصنوعی مولد را به نمایش می‌گذارد. Claude 3 Opus می‌تواند تصاویر را پردازش کرده و خروجی متنی ارائه دهد و دارای پنجره متنی 200K است."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet از Anthropic به تعادلی ایده‌آل بین هوش و سرعت دست یافته است—به‌ویژه برای بارهای کاری سازمانی مناسب است. این مدل با قیمتی کمتر از رقبا، بیشترین بهره‌وری را ارائه می‌دهد و به‌عنوان یک ماشین اصلی قابل اعتماد و با دوام بالا طراحی شده است که برای استقرارهای مقیاس‌پذیر AI مناسب است. Claude 3 Sonnet می‌تواند تصاویر را پردازش کرده و خروجی متنی ارائه دهد و دارای پنجره متنی 200K است."}, "anthropic.claude-instant-v1": {"description": "مدلی سریع، اقتصادی و همچنان بسیار توانمند که می‌تواند طیف وسیعی از وظایف از جمله مکالمات روزمره، تحلیل متن، خلاصه‌سازی و پاسخ به سوالات اسناد را انجام دهد."}, "anthropic.claude-v2": {"description": "Anthropic مدلی است که در انجام وظایف گسترده‌ای از مکالمات پیچیده و تولید محتوای خلاقانه تا پیروی دقیق از دستورات، توانایی بالایی از خود نشان می‌دهد."}, "anthropic.claude-v2:1": {"description": "نسخه به‌روزرسانی شده Claude 2، با دو برابر پنجره متنی و بهبود در قابلیت اطمینان، کاهش توهمات و دقت مبتنی بر شواهد در اسناد طولانی و زمینه‌های RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku سریع‌ترین و فشرده‌ترین مدل Anthropic است که برای ارائه پاسخ‌های تقریباً فوری طراحی شده است. این مدل دارای عملکرد سریع و دقیق جهت‌دار است."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus قدرتمندترین مدل Anthropic برای انجام وظایف بسیار پیچیده است. این مدل در عملکرد، هوش، روانی و درک عالی عمل می‌کند."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku سریع‌ترین مدل نسل بعدی Anthropic است. در مقایسه با Claude 3 Haiku، Claude 3.5 Haiku در تمام مهارت‌ها بهبود یافته و در بسیاری از آزمون‌های هوش از بزرگترین مدل نسل قبلی، Claude 3 Opus پیشی گرفته است."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet توانایی‌هایی فراتر از Opus ارائه می‌دهد و سرعتی سریع‌تر از Sonnet دارد، در حالی که قیمت آن با Sonnet یکسان است. Sonnet به‌ویژه در برنامه‌نویسی، علم داده، پردازش بصری و وظایف نمایندگی مهارت دارد."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet هو هوش مصنوعی پیشرفته‌ترین مدل Anthropic است و همچنین اولین مدل استدلال ترکیبی در بازار به شمار می‌رود. Claude 3.7 Sonnet می‌تواند پاسخ‌های تقریباً آنی یا تفکر تدریجی و طولانی‌تری تولید کند که کاربران می‌توانند این فرآیندها را به وضوح مشاهده کنند. Sonnet به‌ویژه در برنامه‌نویسی، علم داده، پردازش بصری و وظایف نمایندگی مهارت دارد."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 قوی‌ترین مدل Anthropic برای انجام وظایف بسیار پیچیده است. این مدل در عملکرد، هوش، روانی و درک برتری چشمگیری دارد."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 می‌تواند پاسخ‌های تقریباً فوری یا تفکر گام به گام طولانی‌مدت تولید کند که کاربران می‌توانند این فرآیندها را به وضوح مشاهده کنند. کاربران API همچنین می‌توانند زمان تفکر مدل را به دقت کنترل کنند."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B یک مدل زبان بزرگ پراکنده با 72 میلیارد پارامتر و 16 میلیارد پارامتر فعال است که بر اساس معماری متخصصان ترکیبی گروه‌بندی شده (MoGE) ساخته شده است. در مرحله انتخاب متخصص، متخصصان به گروه‌هایی تقسیم می‌شوند و توکن‌ها در هر گروه به تعداد مساوی متخصصان فعال می‌شوند تا تعادل بار متخصصان حفظ شود، که به طور قابل توجهی کارایی استقرار مدل را در پلتفرم Ascend افزایش می‌دهد."}, "aya": {"description": "Aya 23 یک مدل چندزبانه است که توسط Cohere ارائه شده و از 23 زبان پشتیبانی می‌کند و برای برنامه‌های چندزبانه تسهیلات فراهم می‌آورد."}, "aya:35b": {"description": "Aya 23 یک مدل چندزبانه است که توسط Cohere ارائه شده و از 23 زبان پشتیبانی می‌کند و استفاده از برنامه‌های چندزبانه را تسهیل می‌نماید."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B یک مدل زبان بزرگ متن باز و قابل تجاری با 130 میلیارد پارامتر است که در آزمون‌های معتبر چینی و انگلیسی بهترین عملکرد را در اندازه مشابه به دست آورده است."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B یک مدل زبان بزرگ مبتنی بر معماری متخصصان ترکیبی (MoE) است که توسط شرکت بایدو توسعه یافته است. این مدل دارای 300 میلیارد پارامتر کل است، اما در زمان استنتاج تنها 47 میلیارد پارامتر برای هر توکن فعال می‌شود، که ضمن حفظ عملکرد قدرتمند، کارایی محاسباتی را نیز تضمین می‌کند. به عنوان یکی از مدل‌های اصلی سری ERNIE 4.5، این مدل در وظایف درک متن، تولید، استدلال و برنامه‌نویسی عملکرد برجسته‌ای دارد. این مدل از یک روش پیش‌آموزش نوآورانه چندرسانه‌ای ناهمگن MoE استفاده می‌کند که با آموزش مشترک متن و مدیا تصویری، توانایی کلی مدل را بهبود می‌بخشد، به‌ویژه در زمینه پیروی از دستورالعمل‌ها و حافظه دانش جهانی."}, "c4ai-aya-expanse-32b": {"description": "<PERSON>ya Expanse یک مدل چندزبانه با عملکرد بالا و 32B است که با هدف به چالش کشیدن عملکرد مدل‌های تک‌زبانه از طریق بهینه‌سازی دستور، آربیتراژ داده‌ها، آموزش ترجیحات و نوآوری در ادغام مدل‌ها طراحی شده است. این مدل از 23 زبان پشتیبانی می‌کند."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse یک مدل چندزبانه با عملکرد بالا و 8B است که با هدف به چالش کشیدن عملکرد مدل‌های تک‌زبانه از طریق بهینه‌سازی دستور، آربیتراژ داده‌ها، آموزش ترجیحات و نوآوری در ادغام مدل‌ها طراحی شده است. این مدل از 23 زبان پشتیبانی می‌کند."}, "c4ai-aya-vision-32b": {"description": "Aya Vision یک مدل چندرسانه‌ای پیشرفته است که در چندین معیار کلیدی در زمینه زبان، متن و تصویر عملکرد فوق‌العاده‌ای دارد. این نسخه با 320 میلیارد پارامتر بر روی عملکرد چندزبانه پیشرفته تمرکز دارد."}, "c4ai-aya-vision-8b": {"description": "Aya Vision یک مدل چندرسانه‌ای پیشرفته است که در چندین معیار کلیدی در زمینه زبان، متن و تصویر عملکرد فوق‌العاده‌ای دارد. این نسخه با 80 میلیارد پارامتر بر روی تأخیر کم و بهترین عملکرد تمرکز دارد."}, "charglm-3": {"description": "CharGLM-3 به‌طور ویژه برای نقش‌آفرینی و همراهی عاطفی طراحی شده است، از حافظه طولانی‌مدت و مکالمات شخصی‌سازی‌شده پشتیبانی می‌کند و کاربردهای گسترده‌ای دارد."}, "charglm-4": {"description": "CharGLM-4 به‌طور خاص برای نقش‌آفرینی و همراهی عاطفی طراحی شده است و از حافظه چند دور طولانی و گفتگوی شخصی‌سازی شده پشتیبانی می‌کند و کاربردهای گسترده‌ای دارد."}, "chatglm3": {"description": "ChatGLM3 یک مدل بسته‌شده است که توسط هوش مصنوعی Zhima و آزمایشگاه KEG دانشگاه Tsinghua منتشر شده است. این مدل با پیش‌آموزش بر روی مجموعه‌ای وسیع از نمادهای چینی و انگلیسی و همچنین آموزش مطابق با ترجیحات انسانی، نسبت به نسل اول مدل، بهبود‌های 16٪، 36٪ و 280٪ در MMLU، C-Eval و GSM8K به دست آورده است و در رتبه‌بندی وظایف چینی C-Eval رتبه اول را کسب کرده است. این مدل برای صحنه‌هایی که نیاز به مقدار زیادی دانش، توانایی استدلال و خلاقیت دارند، مانند نوشتن متن تبلیغاتی، نویسندگی داستان، نوشتن محتوای دانشگاهی و تولید کد مناسب است."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base یک مدل پایه منبع باز با مقیاس ۶ میلیارد پارامتر از نسل جدید سری ChatGLM است که توسط شرکت Zhizhu (智谱) توسعه یافته است."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o یک مدل پویا است که به‌صورت زنده به‌روزرسانی می‌شود تا همیشه نسخه‌ی جدید و به‌روز باشد. این مدل ترکیبی از توانایی‌های قوی در درک و تولید زبان است و برای کاربردهای گسترده مانند خدمات مشتری، آموزش و پشتیبانی فنی مناسب است."}, "claude-2.0": {"description": "Claude 2 پیشرفت‌های کلیدی را برای کسب‌وکارها ارائه می‌دهد، از جمله زمینه 200K توکن پیشرو در صنعت، کاهش قابل توجه نرخ خطاهای مدل، اعلان‌های سیستمی و یک ویژگی جدید آزمایشی: فراخوانی ابزار."}, "claude-2.1": {"description": "Claude 2 پیشرفت‌های کلیدی را برای کسب‌وکارها فراهم می‌کند، از جمله زمینه 200K توکن پیشرو در صنعت، کاهش قابل توجه در نرخ توهم مدل، اعلان‌های سیستمی و یک ویژگی آزمایشی جدید: فراخوانی ابزار."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku سریع‌ترین مدل نسل بعدی Anthropic است. در مقایسه با Claude 3 Haiku، Claude 3.5 Haiku در تمام مهارت‌ها بهبود یافته و در بسیاری از آزمون‌های استاندارد هوش، از بزرگ‌ترین مدل نسل قبلی یعنی Claude 3 Opus پیشی گرفته است."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet توانایی‌هایی فراتر از Opus ارائه می‌دهد و سرعتی سریع‌تر از Sonnet دارد، در حالی که قیمت آن با Sonnet یکسان است. Sonnet به‌ویژه در برنامه‌نویسی، علم داده، پردازش بصری و وظایف نمایندگی مهارت دارد."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet توانایی‌هایی فراتر از Opus ارائه می‌دهد و سرعتی سریع‌تر از Sonnet دارد، در حالی که قیمت آن با Sonnet یکسان است. Sonnet به‌ویژه در برنامه‌نویسی، علم داده، پردازش بصری و وظایف نمایندگی مهارت دارد."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet توانایی‌هایی فراتر از Opus ارائه می‌دهد و سرعتی سریع‌تر از Sonnet دارد، در حالی که قیمت آن با Sonnet یکسان است. Sonnet به‌ویژه در برنامه‌نویسی، علم داده، پردازش بصری و وظایف نمایندگی مهارت دارد."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku سریع‌ترین و فشرده‌ترین مدل Anthropic است که برای ارائه پاسخ‌های تقریباً فوری طراحی شده است. این مدل دارای عملکرد سریع و دقیق جهت‌گیری است."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus قدرتمندترین مدل Anthropic برای انجام وظایف بسیار پیچیده است. این مدل در عملکرد، هوش، روانی و درک عالی عمل می‌کند."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet تعادلی ایده‌آل بین هوش و سرعت برای بارهای کاری سازمانی فراهم می‌کند. این محصول با قیمتی پایین‌تر حداکثر بهره‌وری را ارائه می‌دهد، قابل اعتماد است و برای استقرار در مقیاس بزرگ مناسب می‌باشد."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 قدرتمندترین مدل Anthropic برای پردازش وظایف بسیار پیچیده است. این مدل در زمینه‌های عملکرد، هوش، روانی و درک فوق‌العاده عمل می‌کند."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet می‌تواند پاسخ‌های تقریباً آنی یا تفکر تدریجی و طولانی‌مدت را تولید کند، کاربران می‌توانند این فرآیندها را به وضوح مشاهده کنند. کاربران API همچنین می‌توانند زمان تفکر مدل را به دقت کنترل کنند."}, "codegeex-4": {"description": "CodeGeeX-4 یک دستیار برنامه‌نویسی قدرتمند مبتنی بر هوش مصنوعی است که از پرسش و پاسخ هوشمند و تکمیل کد در زبان‌های برنامه‌نویسی مختلف پشتیبانی می‌کند و بهره‌وری توسعه را افزایش می‌دهد."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B یک مدل تولید کد چندزبانگی است که از قابلیت‌های جامع شامل تکمیل و تولید کد، مفسر کد، جستجوی وب، تماس با توابع و پرسش و پاسخ کد در سطح مخزن پشتیبانی می‌کند و تمام سناریوهای توسعه نرم‌افزار را پوشش می‌دهد. این مدل یکی از بهترین مدل‌های تولید کد با پارامترهای کمتر از 10B است."}, "codegemma": {"description": "CodeGemma یک مدل زبانی سبک برای وظایف مختلف برنامه‌نویسی است که از تکرار سریع و یکپارچه‌سازی پشتیبانی می‌کند."}, "codegemma:2b": {"description": "CodeGemma یک مدل زبان سبک برای وظایف مختلف برنامه‌نویسی است که از تکرار سریع و یکپارچه‌سازی پشتیبانی می‌کند."}, "codellama": {"description": "Code Llama یک مدل زبانی بزرگ (LLM) است که بر تولید و بحث در مورد کد تمرکز دارد و از زبان‌های برنامه‌نویسی گسترده‌ای پشتیبانی می‌کند و برای محیط‌های توسعه‌دهندگان مناسب است."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama یک LLM است که بر تولید و بحث کد تمرکز دارد و از پشتیبانی گسترده زبان‌های برنامه‌نویسی برخوردار است و برای محیط‌های توسعه‌دهنده مناسب است."}, "codellama:13b": {"description": "Code Llama یک مدل زبانی بزرگ (LLM) است که بر تولید و بحث در مورد کد تمرکز دارد و از زبان‌های برنامه‌نویسی گسترده‌ای پشتیبانی می‌کند و برای محیط‌های توسعه‌دهندگان مناسب است."}, "codellama:34b": {"description": "Code Llama یک مدل زبانی بزرگ (LLM) است که بر تولید و بحث در مورد کد تمرکز دارد و از زبان‌های برنامه‌نویسی گسترده‌ای پشتیبانی می‌کند و برای محیط‌های توسعه‌دهندگان مناسب است."}, "codellama:70b": {"description": "Code Llama یک مدل زبانی بزرگ (LLM) است که بر تولید و بحث در مورد کد تمرکز دارد و با پشتیبانی گسترده از زبان‌های برنامه‌نویسی، برای محیط‌های توسعه‌دهندگان مناسب است."}, "codeqwen": {"description": "CodeQwen1.5 یک مدل زبان بزرگ است که بر اساس حجم زیادی از داده‌های کد آموزش دیده و به‌طور خاص برای حل وظایف پیچیده برنامه‌نویسی طراحی شده است."}, "codestral": {"description": "Codestral اولین مدل کد از Mistral AI است که پشتیبانی عالی برای وظایف تولید کد ارائه می‌دهد."}, "codestral-latest": {"description": "Codestral یک مدل پیشرفته تولید کد است که بر تولید کد تمرکز دارد و برای وظایف تکمیل کد و پر کردن میان‌متن بهینه‌سازی شده است."}, "codex-mini-latest": {"description": "codex-mini-latest نسخه‌ای تنظیم‌شده از o4-mini است که به‌طور خاص برای Codex CLI طراحی شده است. برای استفاده مستقیم از طریق API، ما توصیه می‌کنیم از gpt-4.1 شروع کنید."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B یک مدل طراحی شده برای پیروی از دستورات، مکالمه و برنامه‌نویسی است."}, "cogview-4": {"description": "CogView-4 نخستین مدل متن به تصویر متن‌باز Zhizhu است که از تولید حروف چینی پشتیبانی می‌کند. این مدل در درک معنایی، کیفیت تولید تصویر و توانایی تولید متون چینی و انگلیسی به طور جامع بهبود یافته است، از ورودی دوزبانه چینی و انگلیسی با طول دلخواه پشتیبانی می‌کند و قادر است تصاویر با هر وضوحی در محدوده داده شده تولید کند."}, "cohere-command-r": {"description": "Command R یک مدل تولیدی قابل گسترش است که برای RAG و استفاده از ابزارها طراحی شده است و به شرکت‌ها امکان می‌دهد تا به هوش مصنوعی در سطح تولید دست یابند."}, "cohere-command-r-plus": {"description": "Command R+ یک مدل پیشرفته بهینه‌سازی RAG است که برای مدیریت بارهای کاری در سطح سازمانی طراحی شده است."}, "cohere/Cohere-command-r": {"description": "Command R یک مدل تولیدی مقیاس‌پذیر است که برای استفاده در RAG و ابزارها طراحی شده است تا به کسب‌وکارها امکان پیاده‌سازی هوش مصنوعی در سطح تولید را بدهد."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ یک مدل بهینه‌سازی شده پیشرفته برای RAG است که برای بارهای کاری سازمانی طراحی شده است."}, "command": {"description": "یک مدل گفتگوی پیروی از دستور که در وظایف زبانی کیفیت بالاتر و قابلیت اطمینان بیشتری را ارائه می‌دهد و نسبت به مدل‌های تولید پایه ما دارای طول زمینه بیشتری است."}, "command-a-03-2025": {"description": "Command A قوی‌ترین مدل ما تا به امروز است که در استفاده از ابزارها، نمایندگی، تولید تقویت‌شده با جستجو (RAG) و سناریوهای چندزبانه عملکرد فوق‌العاده‌ای دارد. Command A دارای طول زمینه 256K است و تنها به دو واحد GPU نیاز دارد و نسبت به Command R+ 08-2024، توان عملیاتی آن 150% افزایش یافته است."}, "command-light": {"description": "یک نسخه کوچک‌تر و سریع‌تر از Command که تقریباً به همان اندازه قوی است اما سریع‌تر عمل می‌کند."}, "command-light-nightly": {"description": "برای کاهش فاصله زمانی بین انتشار نسخه‌های اصلی، ما نسخه‌های شبانه مدل Command را معرفی کرده‌ایم. برای سری command-light، این نسخه به نام command-light-nightly شناخته می‌شود. لطفاً توجه داشته باشید که command-light-nightly جدیدترین، آزمایشی‌ترین و (احتمالاً) ناپایدارترین نسخه است. نسخه‌های شبانه به‌طور منظم به‌روزرسانی می‌شوند و بدون اطلاع قبلی منتشر می‌شوند، بنابراین استفاده از آن در محیط‌های تولیدی توصیه نمی‌شود."}, "command-nightly": {"description": "برای کاهش فاصله زمانی بین انتشار نسخه‌های اصلی، ما نسخه‌های شبانه مدل Command را معرفی کرده‌ایم. برای سری Command، این نسخه به نام command-cightly شناخته می‌شود. لطفاً توجه داشته باشید که command-nightly جدیدترین، آزمایشی‌ترین و (احتمالاً) ناپایدارترین نسخه است. نسخه‌های شبانه به‌طور منظم به‌روزرسانی می‌شوند و بدون اطلاع قبلی منتشر می‌شوند، بنابراین استفاده از آن در محیط‌های تولیدی توصیه نمی‌شود."}, "command-r": {"description": "Command R یک LLM بهینه‌سازی شده برای مکالمات و وظایف با متن طولانی است که به‌ویژه برای تعاملات پویا و مدیریت دانش مناسب است."}, "command-r-03-2024": {"description": "Command R یک مدل گفتگوی پیروی از دستور است که در وظایف زبانی کیفیت بالاتری را ارائه می‌دهد و نسبت به مدل‌های قبلی دارای طول زمینه بیشتری است. این مدل می‌تواند در جریان‌های کاری پیچیده مانند تولید کد، تولید تقویت‌شده با جستجو (RAG)، استفاده از ابزارها و نمایندگی استفاده شود."}, "command-r-08-2024": {"description": "command-r-08-2024 نسخه به‌روزرسانی شده مدل Command R است که در آگوست 2024 منتشر شد."}, "command-r-plus": {"description": "Command R+ یک مدل زبان بزرگ با عملکرد بالا است که برای سناریوهای واقعی کسب‌وکار و کاربردهای پیچیده طراحی شده است."}, "command-r-plus-04-2024": {"description": "Command R+ یک مدل گفتگوی پیروی از دستور است که در وظایف زبانی کیفیت بالاتری را ارائه می‌دهد و نسبت به مدل‌های قبلی دارای طول زمینه بیشتری است. این مدل برای جریان‌های کاری پیچیده RAG و استفاده از ابزارهای چند مرحله‌ای مناسب‌ترین است."}, "command-r-plus-08-2024": {"description": "Command R+ یک مدل گفتگوی پیرو دستورات است که در وظایف زبانی کیفیت بالاتری را ارائه می‌دهد و نسبت به مدل‌های قبلی دارای طول متن زمینه‌ای بیشتری است. این مدل برای جریان‌های کاری پیچیده RAG و استفاده از ابزارهای چند مرحله‌ای مناسب‌ترین است."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 یک نسخه کوچک و کارآمد به‌روزرسانی شده است که در دسامبر 2024 منتشر شد. این مدل در RAG، استفاده از ابزارها، نمایندگی و سایر وظایفی که نیاز به استدلال پیچیده و پردازش چند مرحله‌ای دارند، عملکرد فوق‌العاده‌ای دارد."}, "compound-beta": {"description": "Compound-beta یک سیستم هوش مصنوعی ترکیبی است که توسط چندین مدل قابل دسترس و پشتیبانی شده در GroqCloud پشتیبانی می‌شود و می‌تواند به‌طور هوشمند و انتخابی از ابزارها برای پاسخ به پرسش‌های کاربران استفاده کند."}, "compound-beta-mini": {"description": "Compound-beta-mini یک سیستم هوش مصنوعی ترکیبی است که توسط مدل‌های عمومی قابل دسترس در GroqCloud پشتیبانی می‌شود و می‌تواند به‌طور هوشمند و انتخابی از ابزارها برای پاسخ به پرسش‌های کاربران استفاده کند."}, "computer-use-preview": {"description": "مدل computer-use-preview به‌طور اختصاصی برای «ابزارهای استفاده از کامپیوتر» طراحی شده و آموزش دیده است تا وظایف مرتبط با کامپیوتر را درک و اجرا کند."}, "dall-e-2": {"description": "مدل نسل دوم DALL·E، پشتیبانی از تولید تصاویر واقعی‌تر و دقیق‌تر، با وضوح 4 برابر نسل اول."}, "dall-e-3": {"description": "جدید<PERSON><PERSON><PERSON>ن مدل DALL·E، منتشر شده در نوامبر 2023. پشتیبانی از تولید تصاویر واقعی‌تر و دقیق‌تر، با جزئیات بیشتر."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct قابلیت پردازش دستورات با قابلیت اطمینان بالا را فراهم می‌کند و از کاربردهای چندین صنعت پشتیبانی می‌کند."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 یک مدل استنتاجی مبتنی بر یادگیری تقویتی (RL) است که به مشکلات تکرار و خوانایی در مدل پرداخته است. قبل از RL، DeepSeek-R1 داده‌های شروع سرد را معرفی کرد و عملکرد استنتاج را بهینه‌تر کرد. این مدل در وظایف ریاضی، کدنویسی و استنتاج با OpenAI-o1 عملکرد مشابهی دارد و با استفاده از روش‌های آموزشی به دقت طراحی شده، کیفیت کلی را بهبود بخشیده است."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 با بهره‌گیری از منابع محاسباتی افزوده و مکانیزم‌های بهینه‌سازی الگوریتمی در فرایند پس‌آموزش، عمق توانایی استدلال و استنتاج خود را به طور قابل توجهی افزایش داده است. این مدل در ارزیابی‌های معیار مختلف از جمله ریاضیات، برنامه‌نویسی و منطق عمومی عملکرد برجسته‌ای دارد. عملکرد کلی آن اکنون به مدل‌های پیشرو مانند O3 و Gemini 2.5 Pro نزدیک شده است."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B مد<PERSON>ی است که از تقطیر زنجیره فکری مدل DeepSeek-R1-0528 به Qwen3 8B Base به دست آمده است. این مدل در میان مدل‌های متن‌باز به عملکرد پیشرفته (SOTA) دست یافته و در آزمون AIME 2024، 10٪ بهتر از Qwen3 8B عمل کرده و به سطح عملکرد Qwen3-235B-thinking رسیده است. این مدل در استدلال ریاضی، برنامه‌نویسی و منطق عمومی در چندین آزمون معیار عملکرد برجسته‌ای دارد. ساختار آن مشابه Qwen3-8B است اما از پیکربندی توکنایزر DeepSeek-R1-0528 بهره می‌برد."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "مدل تقطیر DeepSeek-R1 که با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده است."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "مدل تقطیر DeepSeek-R1 که با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده است."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "مدل تقطیر DeepSeek-R1 که با استفاده از یادگیری تقویتی و داده‌های شروع سرد عملکرد استدلال را بهینه‌سازی کرده و مدل‌های متن‌باز را به روز کرده است."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B مد<PERSON>ی است که از تقطیر دانش بر اساس Qwen2.5-32B به دست آمده است. این مدل با استفاده از 800000 نمونه منتخب تولید شده توسط DeepSeek-R1 برای تنظیم دقیق، در زمینه‌های مختلفی از جمله ریاضیات، برنامه‌نویسی و استدلال عملکرد برجسته‌ای را نشان می‌دهد. در چندین آزمون معیار از جمله AIME 2024، MATH-500 و GPQA Diamond نتایج عالی کسب کرده است، به طوری که در MATH-500 به دقت 94.3% دست یافته و توانایی استدلال ریاضی قوی را نشان می‌دهد."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B مد<PERSON>ی است که از تقطیر دانش بر اساس Qwen2.5-Math-7B به دست آمده است. این مدل با استفاده از 800000 نمونه منتخب تولید شده توسط DeepSeek-R1 برای تنظیم دقیق، توانایی استدلال عالی را نشان می‌دهد. در چندین آزمون معیار عملکرد برجسته‌ای داشته است، به طوری که در MATH-500 به دقت 92.8% و در AIME 2024 به نرخ قبولی 55.5% دست یافته و در CodeForces امتیاز 1189 را کسب کرده است و به عنوان مدلی با مقیاس 7B توانایی‌های ریاضی و برنامه‌نویسی قوی را نشان می‌دهد."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 ویژگی‌های برجسته نسخه‌های قبلی را گرد هم آورده و توانایی‌های عمومی و کدنویسی را تقویت کرده است."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 یک مدل زبانی ترکیبی از متخصصان (MoE) با 671 میلیارد پارامتر است که از توجه چندسر (MLA) و معماری DeepSeekMoE استفاده می‌کند و با ترکیب استراتژی تعادل بار بدون ضرر کمکی، کارایی استنتاج و آموزش را بهینه می‌کند. با پیش‌آموزش بر روی 14.8 تریلیون توکن با کیفیت بالا و انجام تنظیم دقیق نظارتی و یادگیری تقویتی، DeepSeek-V3 در عملکرد از سایر مدل‌های متن‌باز پیشی می‌گیرد و به مدل‌های بسته پیشرو نزدیک می‌شود."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek LLM Chat (67B) یک مدل نوآورانه هوش مصنوعی است که توانایی درک عمیق زبان و تعامل را فراهم می‌کند."}, "deepseek-ai/deepseek-r1": {"description": "مدل LLM پیشرفته و کارآمد که در استدلال، ریاضیات و برنامه‌نویسی مهارت دارد."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 یک مدل زبانی بصری مبتنی بر DeepSeekMoE-27B است که از معماری MoE با فعال‌سازی پراکنده استفاده می‌کند و در حالی که تنها 4.5 میلیارد پارامتر فعال است، عملکرد فوق‌العاده‌ای را ارائه می‌دهد. این مدل در چندین وظیفه از جمله پرسش و پاسخ بصری، شناسایی کاراکتر نوری، درک اسناد/جدول‌ها/نمودارها و مکان‌یابی بصری عملکرد عالی دارد."}, "deepseek-chat": {"description": "مدل متن‌باز جدیدی که توانایی‌های عمومی و کدنویسی را ترکیب می‌کند. این مدل نه تنها توانایی گفتگوی عمومی مدل Chat و توانایی قدرتمند پردازش کد مدل Coder را حفظ کرده است، بلکه به ترجیحات انسانی نیز بهتر همسو شده است. علاوه بر این، DeepSeek-V2.5 در وظایف نوشتاری، پیروی از دستورات و سایر جنبه‌ها نیز بهبودهای قابل توجهی داشته است."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B یک مدل زبان کد است که بر اساس 20 تریلیون داده آموزش دیده است، که 87% آن کد و 13% آن زبان‌های چینی و انگلیسی است. این مدل اندازه پنجره 16K و وظایف پر کردن جا را معرفی می‌کند و قابلیت تکمیل کد و پر کردن قطعات در سطح پروژه را ارائه می‌دهد."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 یک مدل کد نویسی ترکیبی و متن‌باز است که در وظایف کدنویسی عملکرد عالی دارد و با GPT4-Turbo قابل مقایسه است."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 یک مدل کد نویسی ترکیبی و متن‌باز است که در وظایف کدنویسی عملکرد بسیار خوبی دارد و با GPT4-Turbo قابل مقایسه است."}, "deepseek-r1": {"description": "DeepSeek-R1 یک مدل استنتاجی مبتنی بر یادگیری تقویتی (RL) است که به مشکلات تکرار و خوانایی در مدل پرداخته است. قبل از RL، DeepSeek-R1 داده‌های شروع سرد را معرفی کرد و عملکرد استنتاج را بهینه‌تر کرد. این مدل در وظایف ریاضی، کدنویسی و استنتاج با OpenAI-o1 عملکرد مشابهی دارد و با استفاده از روش‌های آموزشی به دقت طراحی شده، کیفیت کلی را بهبود بخشیده است."}, "deepseek-r1-0528": {"description": "مدل کامل 685 میلیارد پارامتری، منتشر شده در ۲۸ مه ۲۰۲۵. DeepSeek-R1 در مرحله پس‌آموزش به طور گسترده از تکنیک‌های یادگیری تقویتی استفاده کرده است و با داده‌های برچسب‌خورده بسیار کم، توانایی استدلال مدل را به طور قابل توجهی افزایش داده است. این مدل در وظایف ریاضی، کدنویسی و استدلال زبان طبیعی عملکرد و توانایی بالایی دارد."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B نسخه سریع است که از جستجوی آنلاین زنده پشتیبانی می‌کند و در عین حفظ عملکرد مدل، سرعت پاسخ‌دهی سریع‌تری را ارائه می‌دهد."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B نسخه استاندارد است که از جستجوی آنلاین زنده پشتیبانی می‌کند و برای گفتگوها و وظایف پردازش متنی که به اطلاعات جدید نیاز دارند، مناسب است."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama مد<PERSON>ی است که بر اساس Llama از DeepSeek-R1 استخراج شده است."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - مدل بزرگتر و هوشمندتر در مجموعه DeepSeek - به معماری Llama 70B تقطیر شده است. بر اساس آزمون‌های معیار و ارزیابی‌های انسانی، این مدل از Llama 70B اصلی هوشمندتر است، به ویژه در وظایفی که نیاز به دقت ریاضی و واقعی دارند."}, "deepseek-r1-distill-llama-8b": {"description": "مدل‌های سری DeepSeek-R1-Distill از طریق تکنیک تقطیر دانش، نمونه‌های تولید شده توسط DeepSeek-R1 را برای تنظیم دقیق بر روی مدل‌های متن‌باز مانند Qwen و Llama به کار می‌برند."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "این مدل در تاریخ 14 فوریه 2025 برای اولین بار منتشر شد و توسط تیم توسعه مدل بزرگ Qianfan با استفاده از Llama3_70B به عنوان مدل پایه (ساخته شده با متا لاما) تقطیر شده است و داده‌های تقطیر شده همچنین شامل متون Qianfan است."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "این مدل در تاریخ 14 فوریه 2025 برای اولین بار منتشر شد و توسط تیم توسعه مدل بزرگ Qianfan با استفاده از Llama3_8B به عنوان مدل پایه (ساخته شده با متا لاما) تقطیر شده است و داده‌های تقطیر شده همچنین شامل متون Qianfan است."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen مدلی است که بر اساس Qwen از DeepSeek-R1 استخراج شده است."}, "deepseek-r1-distill-qwen-1.5b": {"description": "مدل‌های سری DeepSeek-R1-Distill از طریق تکنیک تقطیر دانش، نمونه‌های تولید شده توسط DeepSeek-R1 را برای تنظیم دقیق بر روی مدل‌های متن‌باز مانند Qwen و Llama به کار می‌برند."}, "deepseek-r1-distill-qwen-14b": {"description": "مدل‌های سری DeepSeek-R1-Distill از طریق تکنیک تقطیر دانش، نمونه‌های تولید شده توسط DeepSeek-R1 را برای تنظیم دقیق بر روی مدل‌های متن‌باز مانند Qwen و Llama به کار می‌برند."}, "deepseek-r1-distill-qwen-32b": {"description": "مدل‌های سری DeepSeek-R1-Distill از طریق تکنیک تقطیر دانش، نمونه‌های تولید شده توسط DeepSeek-R1 را برای تنظیم دقیق بر روی مدل‌های متن‌باز مانند Qwen و Llama به کار می‌برند."}, "deepseek-r1-distill-qwen-7b": {"description": "مدل‌های سری DeepSeek-R1-Distill از طریق تکنیک تقطیر دانش، نمونه‌های تولید شده توسط DeepSeek-R1 را برای تنظیم دقیق بر روی مدل‌های متن‌باز مانند Qwen و Llama به کار می‌برند."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 نسخه سریع کامل است که از جستجوی آنلاین زنده پشتیبانی می‌کند و ترکیبی از توانایی‌های قوی 671B پارامتر و سرعت پاسخ‌دهی سریع‌تر است."}, "deepseek-r1-online": {"description": "DeepSeek R1 نسخه کامل است که دارای 671B پارامتر است و از جستجوی آنلاین زنده پشتیبانی می‌کند و دارای توانایی‌های درک و تولید قوی‌تری است."}, "deepseek-reasoner": {"description": "مدل استدلالی ارائه شده توسط DeepSeek. قبل از ارائه پاسخ نهایی، مدل ابتدا یک زنجیره تفکر را تولید می‌کند تا دقت پاسخ نهایی را افزایش دهد."}, "deepseek-v2": {"description": "DeepSeek V2 یک مدل زبانی Mixture-of-Experts کارآمد است که برای پردازش نیازهای اقتصادی و کارآمد مناسب می‌باشد."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B مدل طراحی کد DeepSeek است که توانایی‌های قدرتمندی در تولید کد ارائه می‌دهد."}, "deepseek-v3": {"description": "DeepSeek-V3 مدل MoE توسعه یافته توسط شرکت تحقیقاتی فناوری هوش مصنوعی DeepSeek در هانگژو است که در چندین ارزیابی عملکرد برجسته‌ای دارد و در لیست‌های اصلی در صدر مدل‌های متن‌باز قرار دارد. V3 نسبت به مدل V2.5 سرعت تولید را 3 برابر افزایش داده و تجربه کاربری سریع‌تر و روان‌تری را برای کاربران فراهم می‌کند."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 یک مدل MoE با ۶۷۱ میلیارد پارامتر است که در زمینه‌های برنامه‌نویسی و توانایی‌های فنی، درک زمینه و پردازش متن‌های طولانی برتری دارد."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 یک مدل ترکیبی متخصص با 685B پارامتر است و جدیدترین نسخه از سری مدل‌های چت پرچمدار تیم DeepSeek می‌باشد.\n\nاین مدل از [DeepSeek V3](/deepseek/deepseek-chat-v3) به ارث برده و در انواع وظایف عملکرد عالی از خود نشان می‌دهد."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 یک مدل ترکیبی متخصص با 685B پارامتر است و جدیدترین نسخه از سری مدل‌های چت پرچمدار تیم DeepSeek می‌باشد.\n\nاین مدل از [DeepSeek V3](/deepseek/deepseek-chat-v3) به ارث برده و در انواع وظایف عملکرد عالی از خود نشان می‌دهد."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 با وجود داده‌های برچسب‌گذاری شده بسیار کم، توانایی استدلال مدل را به طرز چشمگیری افزایش می‌دهد. قبل از ارائه پاسخ نهایی، مدل ابتدا یک زنجیره تفکر را تولید می‌کند تا دقت پاسخ نهایی را افزایش دهد."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 با داشتن داده‌های برچسب‌خورده بسیار محدود، توانایی استدلال مدل را به طور چشمگیری افزایش داده است. قبل از ارائه پاسخ نهایی، مدل ابتدا یک زنجیره فکری را تولید می‌کند تا دقت پاسخ نهایی را بهبود بخشد."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 با داشتن داده‌های برچسب‌خورده بسیار محدود، توانایی استدلال مدل را به طور چشمگیری افزایش داده است. قبل از ارائه پاسخ نهایی، مدل ابتدا یک زنجیره فکری را تولید می‌کند تا دقت پاسخ نهایی را بهبود بخشد."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B یک مدل زبان بزرگ مبتنی بر Llama3.3 70B است که با استفاده از تنظیمات DeepSeek R1 به عملکرد رقابتی معادل مدل‌های پیشرفته بزرگ دست یافته است."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B یک مدل زبان بزرگ تقطیر شده مبتنی بر Llama-3.1-8B-Instruct است که با استفاده از خروجی DeepSeek R1 آموزش دیده است."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B یک مدل زبان بزرگ تقطیر شده مبتنی بر Qwen 2.5 14B است که با استفاده از خروجی DeepSeek R1 آموزش دیده است. این مدل در چندین آزمون معیار از o1-mini OpenAI پیشی گرفته و به آخرین دستاوردهای فناوری مدل‌های متراکم (dense models) دست یافته است. نتایج برخی از آزمون‌های معیار به شرح زیر است:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nاین مدل با تنظیمات خروجی DeepSeek R1، عملکرد رقابتی معادل مدل‌های پیشرفته بزرگتر را نشان می‌دهد."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B یک مدل زبان بزرگ تقطیر شده مبتنی بر Qwen 2.5 32B است که با استفاده از خروجی DeepSeek R1 آموزش دیده است. این مدل در چندین آزمون معیار از o1-mini OpenAI پیشی گرفته و به آخرین دستاوردهای فناوری مدل‌های متراکم (dense models) دست یافته است. نتایج برخی از آزمون‌های معیار به شرح زیر است:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nاین مدل با تنظیمات خروجی DeepSeek R1، عملکرد رقابتی معادل مدل‌های پیشرفته بزرگتر را نشان می‌دهد."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 جدیدترین مدل متن باز منتشر شده توسط تیم DeepSeek است که دارای عملکرد استدلال بسیار قوی است و به ویژه در وظایف ریاضی، برنامه‌نویسی و استدلال به سطحی معادل مدل o1 OpenAI رسیده است."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 با وجود داده‌های برچسب‌گذاری شده بسیار کم، توانایی استدلال مدل را به طرز چشمگیری افزایش می‌دهد. قبل از ارائه پاسخ نهایی، مدل ابتدا یک زنجیره تفکر را تولید می‌کند تا دقت پاسخ نهایی را افزایش دهد."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 در سرعت استدلال به یک پیشرفت عمده نسبت به مدل‌های قبلی دست یافته است. این مدل در بین مدل‌های متن باز رتبه اول را دارد و می‌تواند با پیشرفته‌ترین مدل‌های بسته جهانی رقابت کند. DeepSeek-V3 از معماری توجه چندسر (MLA) و DeepSeekMoE استفاده می‌کند که این معماری‌ها در DeepSeek-V2 به طور کامل تأیید شده‌اند. علاوه بر این، DeepSeek-V3 یک استراتژی کمکی بدون ضرر برای تعادل بار معرفی کرده و اهداف آموزشی پیش‌بینی چند برچسبی را برای بهبود عملکرد تعیین کرده است."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 در سرعت استدلال به یک پیشرفت عمده نسبت به مدل‌های قبلی دست یافته است. این مدل در بین مدل‌های متن باز رتبه اول را دارد و می‌تواند با پیشرفته‌ترین مدل‌های بسته جهانی رقابت کند. DeepSeek-V3 از معماری توجه چندسر (MLA) و DeepSeekMoE استفاده می‌کند که این معماری‌ها در DeepSeek-V2 به طور کامل تأیید شده‌اند. علاوه بر این، DeepSeek-V3 یک استراتژی کمکی بدون ضرر برای تعادل بار معرفی کرده و اهداف آموزشی پیش‌بینی چند برچسبی را برای بهبود عملکرد تعیین کرده است."}, "deepseek_r1": {"description": "DeepSeek-R1 یک مدل استدلالی است که توسط یادگیری تقویتی (RL) هدایت می‌شود و مشکلات تکراری و خوانایی را در مدل حل می‌کند. قبل از RL، DeepSeek-R1 داده‌های راه‌اندازی سرد را معرفی کرد و عملکرد استدلال را به‌طور بیشتری بهینه‌سازی کرد. این مدل در وظایف ریاضی، کدنویسی و استدلال با OpenAI-o1 عملکرد مشابهی دارد و از طریق روش‌های آموزشی طراحی‌شده به‌دقت، عملکرد کلی را بهبود بخشیده است."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B <PERSON><PERSON><PERSON><PERSON> است که بر اساس Llama-3.3-70B-Instruct از طریق آموزش تقطیر به‌دست آمده است. این مدل بخشی از سری DeepSeek-R1 است و با استفاده از نمونه‌های تولید شده توسط DeepSeek-R1 برای بهینه‌سازی، در چندین حوزه از جمله ریاضی، برنامه‌نویسی و استدلال عملکرد فوق‌العاده‌ای دارد."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B مد<PERSON><PERSON> است که بر اساس Qwen2.5-14B از طریق تقطیر دانش به‌دست آمده است. این مدل از 800000 نمونه منتخب تولید شده توسط DeepSeek-R1 برای بهینه‌سازی استفاده می‌کند و توانایی استدلال فوق‌العاده‌ای را نشان می‌دهد."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B مد<PERSON>ی است که بر اساس Qwen2.5-32B از طریق تقطیر دانش به‌دست آمده است. این مدل از 800000 نمونه منتخب تولید شده توسط DeepSeek-R1 برای بهینه‌سازی استفاده می‌کند و در چندین حوزه از جمله ریاضی، برنامه‌نویسی و استدلال عملکرد فوق‌العاده‌ای دارد."}, "doubao-1.5-lite-32k": {"description": "مدل سبک نسل جدید Do<PERSON>o-1.5-lite، با سرعت پاسخ‌دهی فوق‌العاده، عملکرد و تأخیر در سطح جهانی را ارائه می‌دهد."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k نسخه ارتقاء یافته Doubao-1.5-Pro است که به طور کلی عملکرد را 10% بهبود می‌بخشد. از استدلال با پنجره زمینه 256k پشتیبانی می‌کند و طول خروجی حداکثر 12k توکن را پشتیبانی می‌کند. عملکرد بالاتر، پنجره بزرگتر و قیمت فوق‌العاده، مناسب برای کاربردهای گسترده‌تر."}, "doubao-1.5-pro-32k": {"description": "مدل اصلی نسل جدید Doubao-1.5-pro، با ارتقاء کامل عملکرد، در زمینه‌های دانش، کد، استدلال و غیره عملکرد برجسته‌ای دارد."}, "doubao-1.5-thinking-pro": {"description": "مدل تفکر عمیق جدید Doubao-1.5، در زمینه‌های تخصصی مانند ریاضیات، برنامه‌نویسی، استدلال علمی و همچنین در وظایف عمومی مانند نوشتن خلاقانه عملکرد برجسته‌ای دارد و در معیارهای معتبر مانند AIME 2024، Codeforces و GPQA به سطح اول صنعت نزدیک یا در آن قرار دارد. از پنجره زمینه 128k و خروجی 16k پشتیبانی می‌کند."}, "doubao-1.5-thinking-pro-m": {"description": "مدل تفکر عمیق جدید Doubao-1.5 (نسخه m دارای قابلیت استدلال چندرسانه‌ای بومی) است که در حوزه‌های تخصصی مانند ریاضیات، برنامه‌نویسی، استدلال علمی و همچنین وظایف عمومی مانند نوشتن خلاقانه عملکرد برجسته‌ای دارد و در معیارهای معتبر AIME 2024، Codeforces، GPQA و غیره به سطح اول صنعت نزدیک یا در آن قرار دارد. از پنجره متنی 128k و خروجی 16k پشتیبانی می‌کند."}, "doubao-1.5-thinking-vision-pro": {"description": "مدل جدید تفکر عمیق بصری با توانایی‌های قوی‌تر در درک و استدلال چندرسانه‌ای عمومی، که در 37 مورد از 59 معیار ارزیابی عمومی به عملکرد برتر (SOTA) دست یافته است."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS یک مدل عامل بومی برای تعامل با رابط‌های گرافیکی کاربری (GUI) است. با توانایی‌های انسانی مانند ادراک، استدلال و اقدام، تعامل بی‌وقفه با GUI را فراهم می‌کند."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite مدل بزرگ چندرسانه‌ای به‌روز شده است که از شناسایی تصاویر با هر وضوح و نسبت ابعاد بسیار طولانی پشتیبانی می‌کند و توانایی‌های استدلال بصری، شناسایی مستندات، درک اطلاعات جزئی و پیروی از دستورات را تقویت می‌کند. از پنجره متن 128k و حداکثر طول خروجی 16k توکن پشتیبانی می‌کند."}, "doubao-1.5-vision-pro": {"description": "مدل چندرسانه‌ای بزرگ Doubao-1.5-vision-pro به‌روزرسانی شده که از شناسایی تصاویر با هر وضوح و نسبت ابعاد بسیار طولانی پشتیبانی می‌کند و توانایی‌های استدلال بصری، شناسایی اسناد، درک جزئیات و پیروی از دستورات را تقویت می‌کند."}, "doubao-1.5-vision-pro-32k": {"description": "مدل چندرسانه‌ای بزرگ Doubao-1.5-vision-pro به‌روزرسانی شده که از شناسایی تصاویر با هر وضوح و نسبت ابعاد بسیار طولانی پشتیبانی می‌کند و توانایی‌های استدلال بصری، شناسایی اسناد، درک جزئیات و پیروی از دستورات را تقویت می‌کند."}, "doubao-lite-128k": {"description": "دارای سرعت پاسخگویی بی‌نظیر و نسبت قیمت به کارایی بهتر است و گزینه‌های انعطاف‌پذیرتری را برای سناریوهای مختلف مشتریان ارائه می‌دهد. از پنجره متنی 128k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "doubao-lite-32k": {"description": "دارای سرعت پاسخگویی بی‌نظیر و نسبت قیمت به کارایی بهتر است و گزینه‌های انعطاف‌پذیرتری را برای سناریوهای مختلف مشتریان ارائه می‌دهد. از پنجره متنی 32k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "doubao-lite-4k": {"description": "دارای سرعت پاسخگویی بی‌نظیر و نسبت قیمت به کارایی بهتر است و گزینه‌های انعطاف‌پذیرتری را برای سناریوهای مختلف مشتریان ارائه می‌دهد. از پنجره متنی 4k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "doubao-pro-256k": {"description": "مدل اصلی با بهترین عملکرد، مناسب برای انجام وظایف پیچیده است و در زمینه‌هایی مانند پاسخ به سوالات مرجع، خلاصه‌سازی، خلق محتوا، دسته‌بندی متن و نقش‌آفرینی عملکرد بسیار خوبی دارد. از پنجره متنی 256k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "doubao-pro-32k": {"description": "مدل اصلی با بهترین عملکرد، مناسب برای انجام وظایف پیچیده است و در زمینه‌هایی مانند پاسخ به سوالات مرجع، خلاصه‌سازی، خلق محتوا، دسته‌بندی متن و نقش‌آفرینی عملکرد بسیار خوبی دارد. از پنجره متنی 32k برای استدلال و تنظیم دقیق پشتیبانی می‌کند."}, "doubao-seed-1.6": {"description": "مدل تفکر عمیق چندرسانه‌ای جدید Doubao-Seed-1.6 که از سه حالت تفکر auto/thinking/non-thinking پشتیبانی می‌کند. در حالت non-thinking، عملکرد مدل نسبت به Doubao-1.5-pro/250115 به‌طور قابل توجهی بهبود یافته است. از پنجره متنی ۲۵۶ هزار توکنی پشتیبانی می‌کند و طول خروجی تا ۱۶ هزار توکن را امکان‌پذیر می‌سازد."}, "doubao-seed-1.6-flash": {"description": "مدل تفکر عمیق چندرسانه‌ای Doubao-Seed-1.6-flash با سرعت استنتاج بسیار بالا، TPOT تنها ۱۰ میلی‌ثانیه است؛ همچنین از درک متن و تصویر پشتیبانی می‌کند، توانایی درک متنی آن از نسل قبلی lite بهتر است و درک تصویری آن با مدل‌های pro رقبا برابری می‌کند. از پنجره متنی ۲۵۶ هزار توکنی پشتیبانی می‌کند و طول خروجی تا ۱۶ هزار توکن را امکان‌پذیر می‌سازد."}, "doubao-seed-1.6-thinking": {"description": "مدل Doubao-Seed-1.6-thinking با توانایی تفکر به‌طور قابل توجهی تقویت شده است، نسبت به Doubao-1.5-thinking-pro در مهارت‌های پایه‌ای مانند برنامه‌نویسی، ریاضیات و استدلال منطقی پیشرفت داشته و از درک تصویری پشتیبانی می‌کند. از پنجره متنی ۲۵۶ هزار توکنی پشتیبانی می‌کند و طول خروجی تا ۱۶ هزار توکن را امکان‌پذیر می‌سازد."}, "doubao-seedream-3-0-t2i-250415": {"description": "مدل تولید تصویر Doubao توسط تیم Seed شرکت بایت‌دنس توسعه یافته است و از ورودی‌های متن و تصویر پشتیبانی می‌کند و تجربه تولید تصویر با کنترل بالا و کیفیت عالی را ارائه می‌دهد. تصاویر بر اساس متن توصیفی تولید می‌شوند."}, "doubao-vision-lite-32k": {"description": "مدل Doubao-vision یک مدل چندرسانه‌ای بزرگ است که توسط Doubao ارائه شده و دارای توانایی‌های قوی در درک و استدلال تصاویر و همچنین درک دقیق دستورات است. این مدل در استخراج اطلاعات متنی از تصاویر و وظایف استدلال مبتنی بر تصویر عملکرد قدرتمندی نشان داده و می‌تواند در وظایف پیچیده‌تر و گسترده‌تر پرسش و پاسخ بصری به کار رود."}, "doubao-vision-pro-32k": {"description": "مدل Doubao-vision یک مدل چندرسانه‌ای بزرگ است که توسط Doubao ارائه شده و دارای توانایی‌های قوی در درک و استدلال تصاویر و همچنین درک دقیق دستورات است. این مدل در استخراج اطلاعات متنی از تصاویر و وظایف استدلال مبتنی بر تصویر عملکرد قدرتمندی نشان داده و می‌تواند در وظایف پیچیده‌تر و گسترده‌تر پرسش و پاسخ بصری به کار رود."}, "emohaa": {"description": "Emohaa یک مدل روان‌شناختی است که دارای توانایی مشاوره حرفه‌ای بوده و به کاربران در درک مسائل احساسی کمک می‌کند."}, "ernie-3.5-128k": {"description": "مدل زبان بزرگ پرچمدار خود توسعه یافته توسط بایدو، که شامل حجم وسیعی از متون چینی و انگلیسی است و دارای توانایی‌های عمومی قوی است که می‌تواند نیازهای اکثر موارد پرسش و پاسخ، تولید خلاقانه و کاربردهای افزونه را برآورده کند؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد."}, "ernie-3.5-8k": {"description": "مدل زبان بزرگ پرچمدار خود توسعه یافته توسط بایدو، که شامل حجم وسیعی از متون چینی و انگلیسی است و دارای توانایی‌های عمومی قوی است که می‌تواند نیازهای اکثر موارد پرسش و پاسخ، تولید خلاقانه و کاربردهای افزونه را برآورده کند؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد."}, "ernie-3.5-8k-preview": {"description": "مدل زبان بزرگ پرچمدار خود توسعه یافته توسط بایدو، که شامل حجم وسیعی از متون چینی و انگلیسی است و دارای توانایی‌های عمومی قوی است که می‌تواند نیازهای اکثر موارد پرسش و پاسخ، تولید خلاقانه و کاربردهای افزونه را برآورده کند؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد."}, "ernie-4.0-8k-latest": {"description": "مدل زبان بزرگ فوق‌العاده پرچمدار خود توسعه یافته توسط بایدو، که نسبت به ERNIE 3.5 به‌روزرسانی‌های جامع‌تری در توانایی‌های مدل داشته و به طور گسترده‌ای در زمینه‌های مختلف برای وظایف پیچیده کاربرد دارد؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد."}, "ernie-4.0-8k-preview": {"description": "مدل زبان بزرگ فوق‌العاده پرچمدار خود توسعه یافته توسط بایدو، که نسبت به ERNIE 3.5 به‌روزرسانی‌های جامع‌تری در توانایی‌های مدل داشته و به طور گسترده‌ای در زمینه‌های مختلف برای وظایف پیچیده کاربرد دارد؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد."}, "ernie-4.0-turbo-128k": {"description": "مدل زبان بزرگ فوق‌العاده پرچمدار خود توسعه یافته توسط بایدو، که عملکرد کلی آن بسیار خوب است و به طور گسترده‌ای در زمینه‌های مختلف برای وظایف پیچیده کاربرد دارد؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد. نسبت به ERNIE 4.0 در عملکرد بهتر است."}, "ernie-4.0-turbo-8k-latest": {"description": "مدل زبان بزرگ فوق‌العاده پرچمدار خود توسعه یافته توسط بایدو، که عملکرد کلی آن بسیار خوب است و به طور گسترده‌ای در زمینه‌های مختلف برای وظایف پیچیده کاربرد دارد؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد. نسبت به ERNIE 4.0 در عملکرد بهتر است."}, "ernie-4.0-turbo-8k-preview": {"description": "مدل زبان بزرگ فوق‌العاده پرچمدار خود توسعه یافته توسط بایدو، که عملکرد کلی آن بسیار خوب است و به طور گسترده‌ای در زمینه‌های مختلف برای وظایف پیچیده کاربرد دارد؛ از اتصال خودکار به افزونه جستجوی بایدو پشتیبانی می‌کند تا اطلاعات پرسش و پاسخ به روز باشد. نسبت به ERNIE 4.0 در عملکرد بهتر است."}, "ernie-4.5-8k-preview": {"description": "مدل بزرگ 4.5 Ernie یک مدل پایه چندرسانه‌ای نسل جدید است که توسط بایدو به‌طور مستقل توسعه یافته و از طریق مدل‌سازی مشترک چندین حالت به بهینه‌سازی هم‌زمان دست می‌یابد و توانایی درک چندرسانه‌ای فوق‌العاده‌ای دارد؛ دارای توانایی‌های زبانی پیشرفته‌تر، درک، تولید، منطق و حافظه به‌طور کلی بهبود یافته و توانایی‌های حذف توهم، استدلال منطقی و کد به‌طور قابل توجهی افزایش یافته است."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo در زمینه‌های کاهش توهم، استدلال منطقی و توانایی کدنویسی به‌طور قابل توجهی بهبود یافته است. در مقایسه با Wenxin 4.5، سرعت بالاتر و قیمت کمتری دارد. توانایی‌های مدل به‌طور کلی افزایش یافته و بهتر می‌تواند به پردازش مکالمات طولانی با تاریخچه چند دور و وظایف درک پرسش و پاسخ متون طولانی پاسخ دهد."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo در زمینه‌های کاهش توهم، استدلال منطقی و توانایی کدنویسی به‌طور قابل توجهی بهبود یافته است. در مقایسه با Wenxin 4.5، سرعت بالاتر و قیمت کمتری دارد. توانایی‌های خلق متن و پرسش و پاسخ به‌طور قابل توجهی افزایش یافته است. طول خروجی و تأخیر در جملات کامل نسبت به ERNIE 4.5 افزایش یافته است."}, "ernie-4.5-turbo-vl-32k": {"description": "نسخه جدید مدل بزرگ Wenxin، توانایی‌های درک تصویر، خلق، ترجمه و کدنویسی به‌طور قابل توجهی افزایش یافته است و برای اولین بار از طول زمینه 32K پشتیبانی می‌کند، تأخیر در اولین توکن به‌طور قابل توجهی کاهش یافته است."}, "ernie-char-8k": {"description": "مدل زبان بزرگ با کاربرد خاص که توسط بایدو توسعه یافته است و برای کاربردهایی مانند NPCهای بازی، مکالمات خدمات مشتری، و نقش‌آفرینی در مکالمات مناسب است، سبک شخصیت آن واضح‌تر و یکدست‌تر است و توانایی پیروی از دستورات و عملکرد استدلال بهتری دارد."}, "ernie-char-fiction-8k": {"description": "مدل زبان بزرگ با کاربرد خاص که توسط بایدو توسعه یافته است و برای کاربردهایی مانند NPCهای بازی، مکالمات خدمات مشتری، و نقش‌آفرینی در مکالمات مناسب است، سبک شخصیت آن واضح‌تر و یکدست‌تر است و توانایی پیروی از دستورات و عملکرد استدلال بهتری دارد."}, "ernie-irag-edit": {"description": "مدل ویرایش تصویر ERNIE iRAG که توسط بایدو توسعه یافته است، از عملیات‌هایی مانند حذف (erase)، بازنقاشی (repaint) و تولید واریاسیون (variation) بر اساس تصویر پشتیبانی می‌کند."}, "ernie-lite-8k": {"description": "ERNIE Lite مدل زبان بزرگ سبک خود توسعه یافته توسط بایدو است که تعادل خوبی بین عملکرد مدل و عملکرد استدلال دارد و برای استفاده در کارت‌های تسریع AI با توان محاسباتی پایین مناسب است."}, "ernie-lite-pro-128k": {"description": "مدل زبان بزرگ سبک خود توسعه یافته توسط بایدو که تعادل خوبی بین عملکرد مدل و عملکرد استدلال دارد و عملکرد بهتری نسبت به ERNIE Lite دارد و برای استفاده در کارت‌های تسریع AI با توان محاسباتی پایین مناسب است."}, "ernie-novel-8k": {"description": "مدل زبان بزرگ عمومی خود توسعه یافته توسط بایدو که در توانایی ادامه نوشتن رمان مزیت قابل توجهی دارد و همچنین می‌تواند در صحنه‌های کوتاه‌نمایش و فیلم‌ها استفاده شود."}, "ernie-speed-128k": {"description": "مدل زبان بزرگ با عملکرد بالا که به تازگی در سال 2024 توسط بایدو منتشر شده است، دارای توانایی‌های عمومی عالی است و برای تنظیم دقیق به عنوان مدل پایه مناسب است و می‌تواند به خوبی مسائل خاص را مدیریت کند و همچنین دارای عملکرد استدلال بسیار خوبی است."}, "ernie-speed-pro-128k": {"description": "مدل زبان بزرگ با عملکرد بالا که به تازگی در سال 2024 توسط بایدو منتشر شده است، دارای توانایی‌های عمومی عالی است و عملکرد بهتری نسبت به ERNIE Speed دارد و برای تنظیم دقیق به عنوان مدل پایه مناسب است و می‌تواند به خوبی مسائل خاص را مدیریت کند و همچنین دارای عملکرد استدلال بسیار خوبی است."}, "ernie-tiny-8k": {"description": "ERNIE Tiny مدل زبان بزرگ با عملکرد فوق‌العاده بالا است که هزینه‌های استقرار و تنظیم آن در بین مدل‌های سری Wenxin کمترین است."}, "ernie-x1-32k": {"description": "دارای توانایی‌های قوی‌تر در درک، برنامه‌ریزی، تفکر و تکامل. به عنوان یک مدل تفکر عمیق با قابلیت‌های جامع‌تر، Wenxin X1 دقت، خلاقیت و بلاغت را در کنار هم دارد و در زمینه‌های پرسش و پاسخ دانش چینی، خلق ادبیات، نوشتن متون، گفتگوهای روزمره، استدلال منطقی، محاسبات پیچیده و استفاده از ابزارها به‌ویژه عملکرد برجسته‌ای دارد."}, "ernie-x1-32k-preview": {"description": "مدل بزرگ ERNIE X1 دارای توانایی‌های قوی‌تری در درک، برنامه‌ریزی، تفکر و تکامل است. به عنوان یک مدل تفکر عمیق با قابلیت‌های جامع‌تر، ERNIE X1 دقت، خلاقیت و بلاغت را در زمینه‌های پرسش و پاسخ دانش چینی، خلق ادبیات، نوشتن متون، گفتگوهای روزمره، استدلال منطقی، محاسبات پیچیده و فراخوانی ابزارها به نمایش می‌گذارد."}, "ernie-x1-turbo-32k": {"description": "مدل نسبت به ERNIE-X1-32K از نظر عملکرد و کارایی بهتر است."}, "flux-1-schnell": {"description": "مدل تولید تصویر از متن با 12 میلیارد پارامتر که توسط Black Forest Labs توسعه یافته است و از تکنولوژی تقطیر انتشار متخاصم نهفته استفاده می‌کند و قادر است در 1 تا 4 مرحله تصاویر با کیفیت بالا تولید کند. این مدل عملکردی مشابه نمونه‌های بسته دارد و تحت مجوز Apache-2.0 برای استفاده شخصی، تحقیقاتی و تجاری منتشر شده است."}, "flux-dev": {"description": "FLUX.1 [dev] یک مدل وزن باز و پالایش شده متن‌باز برای کاربردهای غیرتجاری است. این مدل کیفیت تصویر و پیروی از دستورالعمل را نزدیک به نسخه حرفه‌ای FLUX حفظ کرده و در عین حال کارایی اجرایی بالاتری دارد. نسبت به مدل‌های استاندارد با اندازه مشابه، بهره‌وری منابع بهتری دارد."}, "flux-kontext/dev": {"description": "مدل ویرایش تصویر Frontier."}, "flux-merged": {"description": "مدل FLUX.1-merged ترکیبی از ویژگی‌های عمیق کشف شده در مرحله توسعه \"DEV\" و مزایای اجرای سریع \"Schnell\" است. این اقدام باعث افزایش مرزهای عملکرد مدل و گسترش دامنه کاربردهای آن شده است."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] قادر است متن و تصاویر مرجع را به عنوان ورودی پردازش کند و ویرایش‌های موضعی هدفمند و تغییرات پیچیده در کل صحنه را به‌صورت یکپارچه انجام دهد."}, "flux-schnell": {"description": "FLUX.1 [schnell] به عنوان پیشرفته‌ترین مدل متن‌باز با گام‌های کم، نه تنها از رقبا پیشی گرفته بلکه از مدل‌های غیرتقطیر قدرتمندی مانند Midjourney v6.0 و DALL·E 3 (HD) نیز بهتر است. این مدل به طور خاص تنظیم شده تا تنوع کامل خروجی‌های پیش‌آموزش را حفظ کند و نسبت به مدل‌های پیشرفته بازار، بهبودهای قابل توجهی در کیفیت بصری، پیروی از دستورالعمل، تغییر اندازه/نسبت، پردازش فونت و تنوع خروجی ارائه می‌دهد و تجربه تولید تصاویر خلاقانه و متنوع‌تری را برای کاربران فراهم می‌کند."}, "flux.1-schnell": {"description": "ترنسفورمر جریان اصلاح‌شده با 12 میلیارد پارامتر که قادر است تصاویر را بر اساس توصیف متنی تولید کند."}, "flux/schnell": {"description": "FLUX.1 [schnell] یک مدل تبدیل جریانی با 12 میلیارد پارامتر است که می‌تواند در 1 تا 4 مرحله تصاویر با کیفیت بالا را از متن تولید کند و برای استفاده شخصی و تجاری مناسب است."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (تنظی<PERSON>) عملکردی پایدار و قابل تنظیم ارائه می‌دهد و انتخابی ایده‌آل برای راه‌حل‌های وظایف پیچیده است."}, "gemini-1.0-pro-002": {"description": "جمینی 1.0 پرو 002 (تنظی<PERSON>) پشتیبانی چندوجهی عالی ارائه می‌دهد و بر حل مؤثر وظایف پیچیده تمرکز دارد."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro مدل هوش مصنوعی با عملکرد بالای Google است که برای گسترش وظایف گسترده طراحی شده است."}, "gemini-1.5-flash-001": {"description": "جمینی 1.5 فلش 001 یک مدل چندوجهی کارآمد است که از گسترش کاربردهای گسترده پشتیبانی می‌کند."}, "gemini-1.5-flash-002": {"description": "جمینی 1.5 فلش 002 یک مدل چندوجهی کارآمد است که از گسترش کاربردهای گسترده پشتیبانی می‌کند."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B یک مدل چندرسانه‌ای کارآمد است که از گسترش کاربردهای وسیع پشتیبانی می‌کند."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 جدیدترین مدل آزمایشی است که در موارد استفاده متنی و چندوجهی بهبود عملکرد قابل توجهی دارد."}, "gemini-1.5-flash-8b-latest": {"description": "جیمنی ۱.۵ فلاش ۸ب یک مدل چند حالتی کارآمد است که پشتیبانی از گستره‌ای وسیع از کاربردها را فراهم می‌کند."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 دارای توانایی‌های بهینه‌شده پردازش چندرسانه‌ای است و مناسب برای انواع سناریوهای پیچیده است."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash جدیدترین مدل چندوجهی AI گوگل است که دارای قابلیت پردازش سریع بوده و از ورودی‌های متن، تصویر و ویدئو پشتیبانی می‌کند و برای گسترش کارآمد در وظایف مختلف مناسب است."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 یک راه‌حل هوش مصنوعی چندوجهی قابل گسترش است که از طیف گسترده‌ای از وظایف پیچیده پشتیبانی می‌کند."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 جدیدترین مدل آماده تولید است که خروجی با کیفیت بالاتری ارائه می‌دهد و به ویژه در زمینه‌های ریاضی، متن‌های طولانی و وظایف بصری بهبود قابل توجهی دارد."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 توانایی‌های برجسته پردازش چندرسانه‌ای را ارائه می‌دهد و انعطاف‌پذیری بیشتری برای توسعه برنامه‌ها به ارمغان می‌آورد."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 با تکنولوژی‌های بهینه‌سازی جدید ترکیب شده و توانایی پردازش داده‌های چندرسانه‌ای را بهینه می‌کند."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro از حداکثر ۲ میلیون توکن پشتیبانی می‌کند و انتخابی ایده‌آل برای مدل‌های چندوجهی متوسط است که برای پشتیبانی از وظایف پیچیده مناسب می‌باشد."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash ویژگی‌ها و بهبودهای نسل بعدی را ارائه می‌دهد، از جمله سرعت عالی، استفاده از ابزارهای بومی، تولید چندرسانه‌ای و پنجره متن 1M توکن."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash ویژگی‌ها و بهبودهای نسل بعدی را ارائه می‌دهد، از جمله سرعت عالی، استفاده از ابزارهای بومی، تولید چندرسانه‌ای و پنجره متن 1M توکن."}, "gemini-2.0-flash-exp": {"description": "مدل متغیر Gemini 2.0 Flash که برای بهینه‌سازی هزینه و تأخیر کم طراحی شده است."}, "gemini-2.0-flash-exp-image-generation": {"description": "مدل آزمای<PERSON>ی Gemini 2.0 Flash، از تولید تصویر پشتیبانی می‌کند"}, "gemini-2.0-flash-lite": {"description": "مدل متغیر Gemini 2.0 Flash برای بهینه‌سازی هزینه و تأخیر کم طراحی شده است."}, "gemini-2.0-flash-lite-001": {"description": "مدل متغیر Gemini 2.0 Flash برای بهینه‌سازی هزینه و تأخیر کم طراحی شده است."}, "gemini-2.0-flash-preview-image-generation": {"description": "مدل پیش‌نمایش Gemini 2.0 Flash، از تولید تصویر پشتیبانی می‌کند"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash مدل با بهترین نسبت قیمت به کارایی گوگل است که امکانات جامع را ارائه می‌دهد."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite کوچک‌ترین و مقرون‌به‌صرفه‌ترین مدل گوگل است که برای استفاده در مقیاس وسیع طراحی شده است."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview کوچک‌ترین و مقرون‌به‌صرفه‌ترین مدل گوگل است که برای استفاده در مقیاس بزرگ طراحی شده است."}, "gemini-2.5-flash-preview-04-17": {"description": "پیش‌نمایش فلش Gemini 2.5 مدل با بهترین قیمت و کیفیت گوگل است که امکانات جامع و کاملی را ارائه می‌دهد."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview مقرون‌به‌صرفه‌ترین مدل گوگل است که امکانات جامع ارائه می‌دهد."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro پیشرفته‌ترین مدل تفکر گوگل است که قادر به استنتاج مسائل پیچیده در حوزه کد، ریاضیات و STEM بوده و با استفاده از زمینه طولانی، تحلیل مجموعه داده‌ها، کدها و مستندات بزرگ را انجام می‌دهد."}, "gemini-2.5-pro-preview-03-25": {"description": "پیش‌نمایش Gemini 2.5 Pro مدل پیشرفته تفکر گوگل است که قادر به استدلال در مورد کد، ریاضیات و مسائل پیچیده در زمینه STEM می‌باشد و همچنین می‌تواند با استفاده از تحلیل زمینه‌ای طولانی، مجموعه‌های داده بزرگ، کتابخانه‌های کد و مستندات را بررسی کند."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview مدل پیشرفته تفکر گوگل است که قادر به استدلال در مورد کد، ریاضیات و مسائل پیچیده در زمینه STEM می‌باشد و می‌تواند با استفاده از تحلیل زمینه‌ای طولانی، مجموعه‌های داده بزرگ، کتابخانه‌های کد و مستندات را بررسی کند."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview پیشرفته‌ترین مدل تفکر گوگل است که قادر به استدلال درباره مسائل پیچیده در حوزه کد، ریاضیات و STEM است و می‌تواند با استفاده از زمینه طولانی، داده‌های بزرگ، مخازن کد و مستندات را تحلیل کند."}, "gemma-7b-it": {"description": "Gemma 7B برای پردازش وظایف کوچک و متوسط مناسب است و از نظر هزینه مؤثر است."}, "gemma2": {"description": "Gemma 2 یک مدل کارآمد است که توسط Google ارائه شده و شامل طیف گسترده‌ای از کاربردها از برنامه‌های کوچک تا پردازش داده‌های پیچیده می‌باشد."}, "gemma2-9b-it": {"description": "Gemma 2 9B یک مدل بهینه‌سازی شده برای وظایف خاص و ادغام ابزارها است."}, "gemma2:27b": {"description": "Gemma 2 یک مدل کارآمد از Google است که طیف گسترده‌ای از کاربردها را از برنامه‌های کوچک تا پردازش داده‌های پیچیده پوشش می‌دهد."}, "gemma2:2b": {"description": "Gemma 2 یک مدل کارآمد است که توسط Google ارائه شده و شامل طیف گسترده‌ای از کاربردها از برنامه‌های کوچک تا پردازش داده‌های پیچیده می‌باشد."}, "generalv3": {"description": "Spark Pro یک مدل زبان بزرگ با عملکرد بالا است که برای حوزه‌های حرفه‌ای بهینه‌سازی شده است و بر ریاضیات، برنامه‌نویسی، پزشکی، آموزش و سایر حوزه‌ها تمرکز دارد. این مدل از جستجوی آنلاین و افزونه‌های داخلی مانند وضعیت آب‌وهوا و تاریخ پشتیبانی می‌کند. مدل بهینه‌شده آن در پرسش و پاسخ‌های پیچیده، درک زبان و تولید متون سطح بالا عملکرد برجسته و کارآمدی از خود نشان می‌دهد و انتخابی ایده‌آل برای کاربردهای حرفه‌ای است."}, "generalv3.5": {"description": "Spark Max جامع‌ترین نسخه است که از جستجوی آنلاین و تعداد زیادی افزونه داخلی پشتیبانی می‌کند. قابلیت‌های هسته‌ای بهینه‌سازی‌شده و تنظیمات نقش‌های سیستمی و عملکرد فراخوانی توابع، آن را در انواع سناریوهای پیچیده بسیار برجسته و کارآمد می‌سازد."}, "glm-4": {"description": "GLM-4 نسخه قدیمی پرچمدار است که در ژانویه 2024 منتشر شد و اکنون با نسخه قوی‌تر GLM-4-0520 جایگزین شده است."}, "glm-4-0520": {"description": "GLM-4-0520 جدیدترین نسخه مدل است که برای وظایف بسیار پیچیده و متنوع طراحی شده و عملکردی عالی دارد."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat در زمینه‌های معنایی، ریاضی، استدلال، کد و دانش عملکرد بالایی از خود نشان می‌دهد. همچنین دارای قابلیت مرور وب، اجرای کد، تماس با ابزارهای سفارشی و استدلال متن‌های طولانی است. از 26 زبان از جمله ژاپنی، کره‌ای و آلمانی پشتیبانی می‌کند."}, "glm-4-air": {"description": "GLM-4-Air نسخه‌ای با صرفه اقتصادی است که عملکردی نزدیک به GLM-4 دارد و سرعت بالا و قیمت مناسبی را ارائه می‌دهد."}, "glm-4-air-250414": {"description": "GLM-4-Air نسخه‌ای با قیمت مناسب است که عملکردی نزدیک به GLM-4 ارائه می‌دهد و سرعت بالا و قیمت مقرون به صرفه‌ای دارد."}, "glm-4-airx": {"description": "GLM-4-AirX نسخه‌ای کارآمد از GLM-4-Air ارائه می‌دهد که سرعت استنتاج آن تا ۲.۶ برابر بیشتر است."}, "glm-4-alltools": {"description": "GLM-4-AllTools یک مدل چندمنظوره هوشمند است که برای پشتیبانی از برنامه‌ریزی دستورات پیچیده و فراخوانی ابزارها بهینه‌سازی شده است، مانند مرور وب، تفسیر کد و تولید متن، و برای اجرای چندوظیفه‌ای مناسب است."}, "glm-4-flash": {"description": "GLM-4-<PERSON> انتخابی ایده‌آل برای انجام وظایف ساده است، سریع‌ترین و رایگان."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON> انتخاب ایده‌آلی برای پردازش وظایف ساده است، سریع‌ترین و رایگان است."}, "glm-4-flashx": {"description": "GLM-4-<PERSON><PERSON> نسخه بهبود یافته Flash است که سرعت استنتاج فوق‌العاده سریعی دارد."}, "glm-4-long": {"description": "GLM-4-Long از ورودی‌های متنی بسیار طولانی پشتیبانی می‌کند و برای وظایف حافظه‌ای و پردازش اسناد بزرگ مناسب است."}, "glm-4-plus": {"description": "GLM-4-Plus به عنوان پرچمدار هوشمند پیشرفته، دارای توانایی پردازش متون طولانی و وظایف پیچیده است و عملکرد آن به طور کامل بهبود یافته است."}, "glm-4.1v-thinking-flash": {"description": "سری مدل‌های GLM-4.1V-Thinking قوی‌ترین مدل‌های زبان تصویری (VLM) در سطح 10 میلیارد پارامتر شناخته شده تا کنون هستند که وظایف زبان تصویری پیشرفته هم‌رده SOTA را شامل می‌شوند، از جمله درک ویدئو، پرسش و پاسخ تصویری، حل مسائل علمی، شناسایی متن OCR، تفسیر اسناد و نمودارها، عامل‌های رابط کاربری گرافیکی، کدنویسی صفحات وب فرانت‌اند، و گراندینگ. توانایی‌های این مدل‌ها حتی از مدل Qwen2.5-VL-72B با 8 برابر پارامتر بیشتر نیز فراتر رفته است. با استفاده از فناوری پیشرفته یادگیری تقویتی، مدل توانسته است با استدلال زنجیره تفکر دقت و غنای پاسخ‌ها را افزایش دهد و از نظر نتایج نهایی و قابلیت تبیین به طور قابل توجهی از مدل‌های غیرتفکری سنتی پیشی بگیرد."}, "glm-4.1v-thinking-flashx": {"description": "سری مدل‌های GLM-4.1V-Thinking قوی‌ترین مدل‌های زبان تصویری (VLM) در سطح 10 میلیارد پارامتر شناخته شده تا کنون هستند که وظایف زبان تصویری پیشرفته هم‌رده SOTA را شامل می‌شوند، از جمله درک ویدئو، پرسش و پاسخ تصویری، حل مسائل علمی، شناسایی متن OCR، تفسیر اسناد و نمودارها، عامل‌های رابط کاربری گرافیکی، کدنویسی صفحات وب فرانت‌اند، و گراندینگ. توانایی‌های این مدل‌ها حتی از مدل Qwen2.5-VL-72B با 8 برابر پارامتر بیشتر نیز فراتر رفته است. با استفاده از فناوری پیشرفته یادگیری تقویتی، مدل توانسته است با استدلال زنجیره تفکر دقت و غنای پاسخ‌ها را افزایش دهد و از نظر نتایج نهایی و قابلیت تبیین به طور قابل توجهی از مدل‌های غیرتفکری سنتی پیشی بگیرد."}, "glm-4.5": {"description": "جدیدترین مدل پرچمدار Z<PERSON>zhu که از حالت تفکر پشتیبانی می‌کند و توانایی‌های جامع آن به سطح SOTA مدل‌های متن‌باز رسیده است و طول زمینه تا 128 هزار توکن را پشتیبانی می‌کند."}, "glm-4.5-air": {"description": "نسخه سبک GLM-4.5 که تعادل بین عملکرد و هزینه را حفظ می‌کند و امکان تغییر انعطاف‌پذیر بین مدل‌های تفکر ترکیبی را فراهم می‌آورد."}, "glm-4.5-airx": {"description": "نسخه فوق‌العاده سریع GLM-4.5-Air که پاسخگویی سریع‌تری دارد و برای نیازهای بزرگ و سرعت بالا طراحی شده است."}, "glm-4.5-flash": {"description": "نسخه رایگان GLM-4.5 که در وظایفی مانند استنتاج، کدنویسی و عامل‌ها عملکرد خوبی دارد."}, "glm-4.5-x": {"description": "نسخه فوق‌العاده سریع GLM-4.5 که در کنار قدرت عملکرد، سرعت تولید تا 100 توکن در ثانیه را ارائه می‌دهد."}, "glm-4v": {"description": "GLM-4V قابلیت‌های قدرتمندی در درک و استدلال تصویری ارائه می‌دهد و از وظایف مختلف بصری پشتیبانی می‌کند."}, "glm-4v-flash": {"description": "GLM-4V-Flash بر روی درک کارآمد تصویر واحد تمرکز دارد و برای سناریوهای تحلیل سریع تصویر، مانند تحلیل تصویر در زمان واقعی یا پردازش دسته‌ای تصاویر مناسب است."}, "glm-4v-plus": {"description": "GLM-4V-Plus توانایی درک محتوای ویدئویی و تصاویر متعدد را دارد و برای وظایف چندرسانه‌ای مناسب است."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus دارای توانایی درک محتوای ویدئویی و چندین تصویر است و برای وظایف چندرسانه‌ای مناسب است."}, "glm-z1-air": {"description": "مدل استدلال: دارای توانایی استدلال قوی و مناسب برای وظایفی که نیاز به استدلال عمیق دارند."}, "glm-z1-airx": {"description": "استدلال فوق‌العاده سریع: دارای سرعت استدلال بسیار بالا و عملکرد قوی است."}, "glm-z1-flash": {"description": "سری GLM-Z1 دارای توانایی‌های قوی در استدلال پیچیده است و در زمینه‌های استدلال منطقی، ریاضیات و برنامه‌نویسی عملکرد برجسته‌ای دارد."}, "glm-z1-flashx": {"description": "سرعت بالا و قیمت پایین: نسخه تقویت‌شده Flash با سرعت استنتاج بسیار سریع‌تر و تضمین همزمانی بالاتر."}, "glm-zero-preview": {"description": "GLM-Zero-Preview دارای توانایی‌های پیچیده استدلال است و در زمینه‌های استدلال منطقی، ریاضیات، برنامه‌نویسی و غیره عملکرد عالی دارد."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash ویژگی‌ها و بهبودهای نسل بعدی را ارائه می‌دهد، از جمله سرعت عالی، استفاده از ابزارهای بومی، تولید چندرسانه‌ای و پنجره متن 1M توکن."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental جدیدترین مدل هوش مصنوعی چندرسانه‌ای آزمایشی گوگل است که نسبت به نسخه‌های قبلی خود بهبود کیفیت قابل توجهی دارد، به ویژه در زمینه دانش جهانی، کد و زمینه‌های طولانی."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash پیشرفته‌ترین مدل اصلی گوگل است که به‌طور خاص برای استدلال پیشرفته، کدنویسی، ریاضیات و وظایف علمی طراحی شده است. این مدل دارای قابلیت «تفکر» داخلی است که به آن امکان می‌دهد پاسخ‌هایی با دقت بالاتر و پردازش دقیق‌تر زمینه ارائه دهد.\n\nتوجه: این مدل دو نسخه دارد: تفکری و غیرتفکری. قیمت‌گذاری خروجی به طور قابل توجهی بسته به فعال بودن قابلیت تفکر متفاوت است. اگر نسخه استاندارد (بدون پسوند «:thinking») را انتخاب کنید، مدل به‌طور صریح از تولید توکن‌های تفکر خودداری می‌کند.\n\nبرای بهره‌مندی از قابلیت تفکر و دریافت توکن‌های تفکر، باید نسخه «:thinking» را انتخاب کنید که منجر به قیمت‌گذاری بالاتر برای خروجی تفکر می‌شود.\n\nعلاوه بر این، Gemini 2.5 Flash را می‌توان از طریق پارامتر «حداکثر توکن‌های استدلال» پیکربندی کرد، همان‌طور که در مستندات آمده است (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash مدل اصلی پیشرفته گوگل است که به طور خاص برای استدلال پیشرفته، کدنویسی، ریاضیات و وظایف علمی طراحی شده است. این مدل دارای قابلیت «تفکر» داخلی است که به آن اجازه می‌دهد پاسخ‌هایی با دقت بالاتر و پردازش زمینه‌ای دقیق‌تری ارائه دهد.\n\nتوجه: این مدل دارای دو واریانت است: تفکر و غیرتفکر. قیمت‌گذاری خروجی بسته به فعال بودن قابلیت تفکر به طور قابل توجهی متفاوت است. اگر شما واریانت استاندارد (بدون پسوند «:thinking») را انتخاب کنید، مدل به وضوح از تولید توکن‌های تفکر اجتناب خواهد کرد.\n\nبرای استفاده از قابلیت تفکر و دریافت توکن‌های تفکر، شما باید واریانت «:thinking» را انتخاب کنید که منجر به قیمت‌گذاری بالاتر خروجی تفکر خواهد شد.\n\nعلاوه بر این، Gemini 2.5 Flash می‌تواند از طریق پارامتر «حداکثر تعداد توکن‌های استدلال» پیکربندی شود، همانطور که در مستندات توضیح داده شده است (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash مدل اصلی پیشرفته گوگل است که به طور خاص برای استدلال پیشرفته، کدنویسی، ریاضیات و وظایف علمی طراحی شده است. این مدل دارای قابلیت «تفکر» داخلی است که به آن اجازه می‌دهد پاسخ‌هایی با دقت بالاتر و پردازش زمینه‌ای دقیق‌تری ارائه دهد.\n\nتوجه: این مدل دارای دو واریانت است: تفکر و غیرتفکر. قیمت‌گذاری خروجی بسته به فعال بودن قابلیت تفکر به طور قابل توجهی متفاوت است. اگر شما واریانت استاندارد (بدون پسوند «:thinking») را انتخاب کنید، مدل به وضوح از تولید توکن‌های تفکر اجتناب خواهد کرد.\n\nبرای استفاده از قابلیت تفکر و دریافت توکن‌های تفکر، شما باید واریانت «:thinking» را انتخاب کنید که منجر به قیمت‌گذاری بالاتر خروجی تفکر خواهد شد.\n\nعلاوه بر این، Gemini 2.5 Flash می‌تواند از طریق پارامتر «حداکثر تعداد توکن‌های استدلال» پیکربندی شود، همانطور که در مستندات توضیح داده شده است (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro پیشرفته‌ترین مدل تفکری گوگل است که قادر به استدلال درباره مسائل پیچیده در حوزه کد، ریاضیات و STEM بوده و می‌تواند با استفاده از زمینه طولانی، داده‌های بزرگ، مخازن کد و مستندات را تحلیل کند."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview پیشرفته‌ترین مدل فکری گوگل است که قادر به استدلال درباره مسائل پیچیده در زمینه کد، ریاضیات و حوزه‌های STEM بوده و همچنین می‌تواند با استفاده از متن‌های طولانی، مجموعه‌های داده بزرگ، کدها و مستندات را تحلیل کند."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash قابلیت پردازش چندوجهی بهینه‌شده را ارائه می‌دهد و برای انواع سناریوهای پیچیده مناسب است."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro با ترکیب آخرین فناوری‌های بهینه‌سازی، توانایی پردازش داده‌های چندحالته را با کارایی بالاتر ارائه می‌دهد."}, "google/gemma-2-27b": {"description": "Gemma 2 مدل کارآمدی است که توسط Google ارائه شده و شامل طیف وسیعی از کاربردها از برنامه‌های کوچک تا پردازش داده‌های پیچیده است."}, "google/gemma-2-27b-it": {"description": "جمما ۲ ادامه‌دهنده‌ی ایده طراحی سبک و کارآمد است."}, "google/gemma-2-2b-it": {"description": "مدل بهینه‌سازی دستورات سبک گوگل"}, "google/gemma-2-9b": {"description": "Gemma 2 مدل کارآمدی است که توسط Google ارائه شده و شامل طیف وسیعی از کاربردها از برنامه‌های کوچک تا پردازش داده‌های پیچیده است."}, "google/gemma-2-9b-it": {"description": "Gemma 2 یک سری مدل‌های متنی سبک و متن‌باز از Google است."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 یک سری مدل‌های متن سبک و متن‌باز از Google است."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) توانایی پردازش دستورات پایه را فراهم می‌کند و برای برنامه‌های سبک مناسب است."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B یک مدل زبان متن‌باز از گوگل است که استانداردهای جدیدی در کارایی و عملکرد ایجاد کرده است."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B یک مدل زبان متن باز از گوگل است که استانداردهای جدیدی را در زمینه کارایی و عملکرد تعیین کرده است."}, "gpt-3.5-turbo": {"description": "GPT 3.5 توربو، مناسب برای انواع وظایف تولید و درک متن، در حال حاضر به gpt-3.5-turbo-0125 اشاره می‌کند"}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 توربو، مناسب برای انواع وظایف تولید و درک متن، در حال حاضر به gpt-3.5-turbo-0125 اشاره می‌کند"}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 توربو، مناسب برای انواع وظایف تولید و درک متن، در حال حاضر به gpt-3.5-turbo-0125 اشاره می‌کند"}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 توربو، مناسب برای انواع وظایف تولید و درک متن، در حال حاضر به gpt-3.5-turbo-0125 اشاره می‌کند"}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo، مدلی کارآمد از OpenAI، مناسب برای چت و وظایف تولید متن است و از فراخوانی توابع به صورت موازی پشتیبانی می‌کند."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k، مدل تولید متن با ظرفیت بالا، مناسب برای وظایف پیچیده است."}, "gpt-4": {"description": "GPT-4 یک پنجره متنی بزرگتر ارائه می‌دهد که قادر به پردازش ورودی‌های متنی طولانی‌تر است و برای سناریوهایی که نیاز به ادغام گسترده اطلاعات و تحلیل داده‌ها دارند، مناسب است."}, "gpt-4-0125-preview": {"description": "جدیدترین مدل GPT-4 Turbo دارای قابلیت‌های بصری است. اکنون درخواست‌های بصری می‌توانند از حالت JSON و فراخوانی توابع استفاده کنند. GPT-4 Turbo یک نسخه بهبود یافته است که پشتیبانی مقرون‌به‌صرفه‌ای برای وظایف چندوجهی ارائه می‌دهد. این مدل بین دقت و کارایی تعادل برقرار می‌کند و برای سناریوهای کاربردی که نیاز به تعاملات بلادرنگ دارند، مناسب است."}, "gpt-4-0613": {"description": "GPT-4 یک پنجره متنی بزرگتر ارائه می‌دهد که قادر به پردازش ورودی‌های متنی طولانی‌تر است و برای سناریوهایی که نیاز به ادغام گسترده اطلاعات و تحلیل داده‌ها دارند، مناسب است."}, "gpt-4-1106-preview": {"description": "جدیدترین مدل GPT-4 Turbo دارای قابلیت‌های بصری است. اکنون درخواست‌های بصری می‌توانند از حالت JSON و فراخوانی توابع استفاده کنند. GPT-4 Turbo یک نسخه بهبود یافته است که پشتیبانی مقرون‌به‌صرفه‌ای برای وظایف چندوجهی ارائه می‌دهد. این مدل بین دقت و کارایی تعادل برقرار می‌کند و برای سناریوهای کاربردی که نیاز به تعاملات بلادرنگ دارند، مناسب است."}, "gpt-4-32k": {"description": "GPT-4 یک پنجره متنی بزرگتر ارائه می‌دهد که قادر به پردازش ورودی‌های متنی طولانی‌تر است و برای سناریوهایی که نیاز به ادغام گسترده اطلاعات و تحلیل داده‌ها دارند، مناسب است."}, "gpt-4-32k-0613": {"description": "GPT-4 یک پنجره متنی بزرگتر ارائه می‌دهد که قادر به پردازش ورودی‌های متنی طولانی‌تر است و برای سناریوهایی که نیاز به ادغام گسترده اطلاعات و تحلیل داده‌ها دارند، مناسب است."}, "gpt-4-turbo": {"description": "جدیدترین مدل GPT-4 Turbo دارای قابلیت‌های بصری است. اکنون درخواست‌های بصری می‌توانند از حالت JSON و فراخوانی توابع استفاده کنند. GPT-4 Turbo نسخه‌ای بهبود یافته است که پشتیبانی مقرون‌به‌صرفه‌ای برای وظایف چندوجهی ارائه می‌دهد. این مدل بین دقت و کارایی تعادل برقرار می‌کند و برای سناریوهای کاربردی که نیاز به تعاملات بلادرنگ دارند، مناسب است."}, "gpt-4-turbo-2024-04-09": {"description": "جدیدترین مدل GPT-4 Turbo دارای قابلیت‌های بصری است. اکنون درخواست‌های بصری می‌توانند از حالت JSON و فراخوانی توابع استفاده کنند. GPT-4 Turbo نسخه‌ای بهبود یافته است که پشتیبانی مقرون‌به‌صرفه‌ای برای وظایف چندوجهی ارائه می‌دهد. این مدل تعادلی بین دقت و کارایی برقرار می‌کند و برای سناریوهای کاربردی که نیاز به تعاملات بلادرنگ دارند، مناسب است."}, "gpt-4-turbo-preview": {"description": "جدیدترین مدل GPT-4 Turbo دارای قابلیت‌های بصری است. اکنون درخواست‌های بصری می‌توانند از حالت JSON و فراخوانی توابع استفاده کنند. GPT-4 Turbo یک نسخه بهبود یافته است که پشتیبانی مقرون‌به‌صرفه‌ای برای وظایف چندرسانه‌ای ارائه می‌دهد. این مدل بین دقت و کارایی تعادل برقرار می‌کند و برای سناریوهای کاربردی که نیاز به تعاملات بلادرنگ دارند، مناسب است."}, "gpt-4-vision-preview": {"description": "جدیدترین مدل GPT-4 Turbo دارای قابلیت‌های بصری است. اکنون درخواست‌های بصری می‌توانند از حالت JSON و فراخوانی توابع استفاده کنند. GPT-4 Turbo نسخه‌ای پیشرفته است که پشتیبانی مقرون‌به‌صرفه‌ای برای وظایف چندوجهی ارائه می‌دهد. این مدل بین دقت و کارایی تعادل برقرار می‌کند و برای سناریوهای کاربردی که نیاز به تعاملات بلادرنگ دارند، مناسب است."}, "gpt-4.1": {"description": "GPT-4.1 مدل پرچمدار ما برای وظایف پیچیده است. این مدل برای حل مسائل در زمینه‌های مختلف بسیار مناسب است."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini تعادلی بین هوش، سرعت و هزینه ارائه می‌دهد و آن را به مدلی جذاب در بسیاری از موارد استفاده تبدیل می‌کند."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini تعادلی بین هوش، سرعت و هزینه ارائه می‌دهد و آن را به مدلی جذاب در بسیاری از موارد استفاده تبدیل می‌کند."}, "gpt-4.5-preview": {"description": "نسخه پیش‌نمایش تحقیقاتی GPT-4.5، بزرگ‌ترین و قدرتمندترین مدل GPT ما تا به امروز است. این مدل دارای دانش وسیع جهانی است و می‌تواند بهتر از قبل نیت‌های کاربران را درک کند، که باعث می‌شود در وظایف خلاقانه و برنامه‌ریزی مستقل عملکرد فوق‌العاده‌ای داشته باشد. GPT-4.5 قادر به پذیرش ورودی‌های متنی و تصویری است و خروجی‌های متنی (شامل خروجی‌های ساختاریافته) تولید می‌کند. از ویژگی‌های کلیدی توسعه‌دهندگان مانند فراخوانی توابع، API دسته‌ای و خروجی جریانی پشتیبانی می‌کند. در وظایفی که نیاز به تفکر خلاق، تفکر باز و گفتگو دارند (مانند نوشتن، یادگیری یا کاوش ایده‌های جدید)، GPT-4.5 به‌ویژه عملکرد خوبی دارد. تاریخ قطع دانش در اکتبر 2023 است."}, "gpt-4o": {"description": "پیشرفته‌ترین مدل چندوجهی در سری GPT-4 OpenAI که می‌تواند ورودی‌های متنی و تصویری را پردازش کند."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o یک مدل پویا است که به‌صورت زنده به‌روزرسانی می‌شود تا همیشه نسخه‌ی جدید و به‌روز باشد. این مدل ترکیبی از توانایی‌های قوی در درک و تولید زبان است و برای کاربردهای گسترده مانند خدمات مشتری، آموزش و پشتیبانی فنی مناسب است."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o یک مدل پویا است که به‌صورت لحظه‌ای به‌روزرسانی می‌شود تا همیشه نسخه‌ی جدید و به‌روز باشد. این مدل ترکیبی از توانایی‌های قوی در درک و تولید زبان است و برای کاربردهای گسترده مانند خدمات مشتری، آموزش و پشتیبانی فنی مناسب است."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o یک مدل پویا است که به طور مداوم به‌روز رسانی می‌شود تا نسخه فعلی و جدیدی را حفظ کند. این مدل قدرت فهم و تولید زبان را ترکیب کرده و مناسب برای کاربردهای مقیاس بزرگ مانند خدمات مشتری، آموزش و پشتیبانی فنی است."}, "gpt-4o-audio-preview": {"description": "مدل صوتی GPT-4o، پشتیبانی از ورودی و خروجی صوتی."}, "gpt-4o-mini": {"description": "یک راه‌حل هوش مصنوعی مقرون‌به‌صرفه که برای انواع وظایف متنی و تصویری مناسب است."}, "gpt-4o-mini-audio-preview": {"description": "مدل GPT-4o mini Audio که از ورودی و خروجی صوتی پشتیبانی می‌کند."}, "gpt-4o-mini-realtime-preview": {"description": "نسخه زنده GPT-4o-mini، پشتیبانی از ورودی و خروجی صوتی و متنی به صورت زنده."}, "gpt-4o-mini-search-preview": {"description": "نسخه پیش‌نمایش جستجوی GPT-4o mini مدلی است که به طور خاص برای درک و اجرای پرسش‌های جستجوی وب آموزش دیده است و از API تکمیل چت استفاده می‌کند. علاوه بر هزینه توکن‌ها، هر پرسش جستجوی وب بر اساس هر بار فراخوانی ابزار هزینه دریافت می‌کند."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe یک مدل تبدیل گفتار به متن است که از GPT-4o برای رونویسی صوت استفاده می‌کند. نسبت به مدل اصلی Whisper، نرخ خطای کلمات را کاهش داده و دقت و شناسایی زبان را بهبود بخشیده است. از آن برای دریافت رونویسی دقیق‌تر استفاده کنید."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS یک مدل تبدیل متن به گفتار است که بر اساس GPT-4o mini ساخته شده است و با قیمت پایین تری از GPT-4o mini ارائه می‌دهد."}, "gpt-4o-realtime-preview": {"description": "نسخه زنده GPT-4o، پشتیبانی از ورودی و خروجی صوتی و متنی به صورت زنده."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "نسخه زنده GPT-4o، پشتیبانی از ورودی و خروجی صوتی و متنی به صورت زنده."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "نسخه بلادرنگ GPT-4o که از ورودی و خروجی همزمان صوت و متن پشتیبانی می‌کند."}, "gpt-4o-search-preview": {"description": "نسخه پیش‌نمایش جستجوی GPT-4o مدلی است که به طور خاص برای درک و اجرای پرسش‌های جستجوی وب آموزش دیده است و از API تکمیل چت استفاده می‌کند. علاوه بر هزینه توکن‌ها، هر پرسش جستجوی وب بر اساس هر بار فراخوانی ابزار هزینه دریافت می‌کند."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe یک مدل تبدیل گفتار به متن است که از GPT-4o برای رونویسی صوت استفاده می‌کند. نسبت به مدل اصلی Whisper، نرخ خطای کلمات را کاهش داده و دقت و شناسایی زبان را بهبود بخشیده است. از آن برای دریافت رونویسی دقیق‌تر استفاده کنید."}, "gpt-image-1": {"description": "مدل تولید تصویر چندرسانه‌ای بومی ChatGPT"}, "grok-2-1212": {"description": "این مدل در دقت، پیروی از دستورات و توانایی چند زبانه بهبود یافته است."}, "grok-2-image-1212": {"description": "جدیدترین مدل تولید تصویر ما قادر است تصاویر زنده و واقعی را بر اساس متن توصیفی تولید کند. این مدل در زمینه تولید تصویر برای بازاریابی، رسانه‌های اجتماعی و سرگرمی عملکرد برجسته‌ای دارد."}, "grok-2-vision-1212": {"description": "این مدل در دقت، پیروی از دستورات و توانایی چند زبانه بهبود یافته است."}, "grok-3": {"description": "مدل پرچمدار که در استخراج داده، برنامه‌نویسی و خلاصه‌سازی متن برای کاربردهای سازمانی مهارت دارد و دانش عمیقی در حوزه‌های مالی، پزشکی، حقوقی و علمی دارد."}, "grok-3-fast": {"description": "مدل پرچمدار که در استخراج داده، برنامه‌نویسی و خلاصه‌سازی متن برای کاربردهای سازمانی مهارت دارد و دانش عمیقی در حوزه‌های مالی، پزشکی، حقوقی و علمی دارد."}, "grok-3-mini": {"description": "مدل سبک‌وزن که قبل از پاسخگویی تفکر می‌کند. سریع و هوشمند اجرا می‌شود، مناسب برای وظایف منطقی که نیاز به دانش عمیق حوزه ندارند و می‌تواند مسیر تفکر اصلی را ارائه دهد."}, "grok-3-mini-fast": {"description": "مدل سبک‌وزن که قبل از پاسخگویی تفکر می‌کند. سریع و هوشمند اجرا می‌شود، مناسب برای وظایف منطقی که نیاز به دانش عمیق حوزه ندارند و می‌تواند مسیر تفکر اصلی را ارائه دهد."}, "grok-4": {"description": "جدیدترین و قدرتمندترین مدل پرچمدار ما که در پردازش زبان طبیعی، محاسبات ریاضی و استدلال عملکردی برجسته دارد — یک انتخاب همه‌کاره بی‌نظیر است."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B یک مدل زبانی است که خلاقیت و هوش را با ترکیب چندین مدل برتر به هم پیوند می‌دهد."}, "hunyuan-a13b": {"description": "اولین مدل استدلال ترکیبی Hunyuan، نسخه ارتقا یافته hunyuan-standard-256K با 80 میلیارد پارامتر کل و 13 میلیارد پارامتر فعال است. حالت پیش‌فرض آن حالت تفکر کند است و از طریق پارامتر یا دستور می‌توان بین حالت‌های تفکر سریع و کند جابجا شد؛ روش جابجایی با افزودن /no_think قبل از پرسش انجام می‌شود. توانایی کلی نسبت به نسل قبلی بهبود یافته است، به‌ویژه در ریاضیات، علوم، درک متون بلند و قابلیت‌های عامل."}, "hunyuan-code": {"description": "مدل تولید کد جدید Hunyuan، که با استفاده از 200 میلیارد داده کد با کیفیت بالا آموزش داده شده است. این مدل پایه پس از شش ماه آموزش با داده‌های SFT با کیفیت بالا به‌روزرسانی شده است. طول پنجره متن به ۸ هزار کاراکتر افزایش یافته و در شاخص‌های ارزیابی خودکار تولید کد در پنج زبان اصلی در رتبه‌های برتر قرار دارد. در ارزیابی‌های دستی با کیفیت بالا برای ۱۰ معیار مختلف در پنج زبان اصلی، عملکرد این مدل در رده اول قرار دارد."}, "hunyuan-functioncall": {"description": "مدل FunctionCall با معماری MOE جدید <PERSON>، آموزش‌دیده با داده‌های باکیفیت FunctionCall، با پنجره متنی تا 32K و پیشرو در چندین شاخص ارزیابی."}, "hunyuan-large": {"description": "مدل Hunyuan-large دارای مجموع پارامترها حدود 389B و پارامترهای فعال حدود 52B است، که بزرگترین و بهترین مدل MoE با ساختار Transformer در صنعت به شمار می‌رود."}, "hunyuan-large-longcontext": {"description": "متخصص در پردازش وظایف متنی طولانی مانند خلاصه‌سازی اسناد و پرسش و پاسخ اسنادی، همچنین توانایی پردازش وظایف تولید متن عمومی را دارد. در تحلیل و تولید متن‌های طولانی عملکرد فوق‌العاده‌ای دارد و می‌تواند به‌طور مؤثر به نیازهای پیچیده و دقیق پردازش محتوای طولانی پاسخ دهد."}, "hunyuan-large-vision": {"description": "این مدل برای سناریوهای درک تصویر و متن مناسب است، یک مدل بزرگ زبان-بینایی مبتنی بر Hunyuan Large است که از ورودی چند تصویر با هر رزولوشن به همراه متن پشتیبانی می‌کند و محتوای متنی تولید می‌کند. تمرکز بر وظایف مرتبط با درک تصویر و متن دارد و در توانایی درک چندزبانه تصویر و متن بهبود قابل توجهی یافته است."}, "hunyuan-lite": {"description": "به ساختار MOE ارتقا یافته است، پنجره متنی 256k دارد و در چندین مجموعه ارزیابی در زمینه‌های NLP، کد، ریاضیات و صنایع از بسیاری از مدل‌های متن‌باز پیشی گرفته است."}, "hunyuan-lite-vision": {"description": "مدل چندرسانه‌ای 7B جدید Hunyuan، با پنجره زمینه 32K، از گفتگوی چندرسانه‌ای در صحنه‌های چینی و انگلیسی، شناسایی اشیاء در تصاویر، درک جداول اسناد و ریاضیات چندرسانه‌ای پشتیبانی می‌کند و در چندین بعد، معیارهای ارزیابی را نسبت به مدل‌های رقیب 7B بهبود می‌بخشد."}, "hunyuan-pro": {"description": "مدل MOE-32K با مقیاس پارامتر تریلیون‌ها. در انواع بنچمارک‌ها به سطح پیشرو مطلق دست یافته است، توانایی پردازش دستورالعمل‌ها و استدلال‌های پیچیده، دارای قابلیت‌های ریاضی پیچیده، پشتیبانی از functioncall، و به‌طور ویژه در حوزه‌های ترجمه چندزبانه، مالی، حقوقی و پزشکی بهینه‌سازی شده است."}, "hunyuan-role": {"description": "جدیدترین مدل نقش‌آفرینی HunYuan، مدل نقش‌آفرینی به‌دقت تنظیم‌شده توسط تیم رسمی HunYuan، که بر اساس مدل HunYuan و با استفاده از مجموعه داده‌های صحنه‌های نقش‌آفرینی آموزش بیشتری دیده است و در صحنه‌های نقش‌آفرینی عملکرد بهتری دارد."}, "hunyuan-standard": {"description": "استفاده از استراتژی مسیریابی بهینه‌تر، در حالی که مشکلات توازن بار و همگرایی متخصصان را کاهش می‌دهد. در زمینه متون طولانی، شاخص «یافتن سوزن در انبار کاه» به ۹۹.۹٪ می‌رسد. MOE-32K از نظر هزینه و عملکرد نسبتاً بهینه‌تر است و در عین حال که تعادل بین اثر و قیمت را حفظ می‌کند، می‌تواند پردازش ورودی‌های متون طولانی را نیز انجام دهد."}, "hunyuan-standard-256K": {"description": "با استفاده از استراتژی مسیریابی بهینه‌تر، در عین حال مشکلات توازن بار و همگرایی کارشناسان را کاهش داده است. در زمینه متون طولانی، شاخص «یافتن سوزن در انبار کاه» به ۹۹.۹٪ رسیده است. MOE-256K در طول و عملکرد پیشرفت بیشتری داشته و به طور قابل توجهی طول ورودی قابل قبول را گسترش داده است."}, "hunyuan-standard-vision": {"description": "مدل چندرسانه‌ا<PERSON> جدید <PERSON>، از پاسخگویی به چند زبان پشتیبانی می‌کند و توانایی‌های چینی و انگلیسی را به‌طور متوازن ارائه می‌دهد."}, "hunyuan-t1-20250321": {"description": "مدل‌های توانایی‌های علمی و انسانی را به طور کامل ایجاد می‌کند و توانایی بالایی در ضبط اطلاعات متنی طولانی دارد. از استدلال برای پاسخ به مسائل علمی مختلف با درجات سختی متفاوت در ریاضیات/منطق/علم/کد و غیره پشتیبانی می‌کند."}, "hunyuan-t1-20250403": {"description": "افزایش توانایی تولید کد در سطح پروژه؛ بهبود کیفیت نوشتار تولید متن؛ ارتقاء توانایی درک موضوعات چندمرحله‌ای، پیروی از دستورات tob و درک واژگان؛ بهینه‌سازی مشکلات خروجی ترکیبی از زبان‌های ساده و سنتی و همچنین ترکیب چینی و انگلیسی."}, "hunyuan-t1-20250529": {"description": "بهینه‌سازی تولید متن، نوشتن مقاله، بهبود توانایی‌های کدنویسی فرانت‌اند، ریاضیات، استدلال منطقی و علوم پایه، و ارتقاء توانایی پیروی از دستورالعمل‌ها."}, "hunyuan-t1-20250711": {"description": "افزایش قابل توجه در توانایی‌های ریاضی، منطقی و کدنویسی پیچیده، بهینه‌سازی پایداری خروجی مدل و ارتقاء توانایی مدل در پردازش متون طولانی."}, "hunyuan-t1-latest": {"description": "اولین مدل استدلال هیبریدی-ترنسفورمر-مامبا با مقیاس فوق‌العاده بزرگ در صنعت، که توانایی استدلال را گسترش می‌دهد و سرعت رمزگشایی فوق‌العاده‌ای دارد و به طور بیشتری با ترجیحات انسانی هم‌راستا می‌شود."}, "hunyuan-t1-vision": {"description": "مدل تفکر عمیق چندرسانه‌ای Hunyuan که از زنجیره تفکر بلند بومی چندرسانه‌ای پشتیبانی می‌کند، در پردازش انواع سناریوهای استدلال تصویری مهارت دارد و در مسائل علمی نسبت به مدل تفکر سریع بهبود قابل توجهی دارد."}, "hunyuan-t1-vision-20250619": {"description": "جدیدترین مدل تفکر عمیق چندرسانه‌ای t1-vision از Hunyuan که از زنجیره تفکر بلند چندرسانه‌ای بومی پشتیبانی می‌کند و نسبت به نسخه پیش‌فرض نسل قبلی به طور کامل بهبود یافته است."}, "hunyuan-turbo": {"description": "نسخه پیش‌نمایش مدل زبان بزرگ نسل جدید HunYuan که از ساختار مدل متخصص ترکیبی (MoE) جدید استفاده می‌کند. در مقایسه با hunyuan-pro، کارایی استنتاج سریع‌تر و عملکرد بهتری دارد."}, "hunyuan-turbo-20241223": {"description": "بهینه‌سازی‌های این نسخه: مقیاس‌دهی دستورات داده، به‌طور قابل توجهی توانایی تعمیم عمومی مدل را افزایش می‌دهد؛ به‌طور قابل توجهی توانایی‌های ریاضی، کدنویسی و استدلال منطقی را بهبود می‌بخشد؛ بهینه‌سازی توانایی‌های درک متن و کلمات مرتبط با آن؛ بهینه‌سازی کیفیت تولید محتوای خلق متن."}, "hunyuan-turbo-latest": {"description": "بهینه‌سازی تجربه عمومی، شامل درک NLP، خلق متن، گپ‌زنی، پرسش و پاسخ دانش، ترجمه و حوزه‌های مختلف؛ افزایش انسان‌نمایی، بهینه‌سازی هوش عاطفی مدل؛ افزایش توانایی مدل در روشن‌سازی فعال زمانی که نیت مبهم است؛ افزایش توانایی پردازش مسائل مربوط به تجزیه و تحلیل کلمات؛ افزایش کیفیت و قابلیت تعامل در خلق محتوا؛ بهبود تجربه چند دور."}, "hunyuan-turbo-vision": {"description": "مدل بزرگ زبان بصری نسل جدید <PERSON>، با استفاده از ساختار جدید مدل‌های متخصص ترکیبی (MoE)، در توانایی‌های مربوط به درک تصویر و متن، خلق محتوا، پرسش و پاسخ دانش و تحلیل استدلال نسبت به مدل‌های نسل قبلی به‌طور جامع بهبود یافته است."}, "hunyuan-turbos-20250313": {"description": "یکسان‌سازی سبک مراحل حل مسائل ریاضی، تقویت پرسش و پاسخ چندمرحله‌ای ریاضی. بهینه‌سازی سبک پاسخ در تولید متن، حذف حس مصنوعی هوش مصنوعی و افزودن زیبایی ادبی."}, "hunyuan-turbos-20250416": {"description": "ارتقاء پایه پیش‌آموزش، تقویت توانایی درک و پیروی از دستورات پایه؛ تقویت مهارت‌های علوم پایه مانند ریاضیات، کد نویسی، منطق و علوم؛ بهبود کیفیت نوشتار خلاقانه، درک متن، دقت ترجمه و پاسخ به سوالات دانش؛ تقویت توانایی‌های عامل‌های حوزه‌های مختلف، با تمرکز ویژه بر درک گفتگوی چندمرحله‌ای."}, "hunyuan-turbos-20250604": {"description": "ارتقاء پایه پیش‌آموزش، بهبود توانایی‌های نوشتن و درک مطلب، افزایش قابل توجه توانایی‌های کدنویسی و علوم پایه، و بهبود مستمر در پیروی از دستورات پیچیده."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS آخرین نسخه مدل بزرگ پرچمدار مختلط است که دارای توانایی تفکر قوی‌تر و تجربه بهتری است."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "این مدل در پردازش وظایف متنی طولانی مانند خلاصه‌سازی و پرسش و پاسخ مستندات مهارت دارد و همچنین توانایی پردازش وظایف تولید متن عمومی را دارد. در تحلیل و تولید متن‌های طولانی عملکرد فوق‌العاده‌ای دارد و می‌تواند به‌طور مؤثر به نیازهای پیچیده و دقیق پردازش محتوای طولانی پاسخ دهد."}, "hunyuan-turbos-role-plus": {"description": "جدیدترین مدل نقش‌آفرینی Hunyuan، مدل نقش‌آفرینی تنظیم‌شده رسمی Hunyuan است که بر اساس مدل Hunyuan و داده‌های سناریوی نقش‌آفرینی آموزش افزایشی دیده است و در سناریوهای نقش‌آفرینی عملکرد پایه بهتری دارد."}, "hunyuan-turbos-vision": {"description": "این مدل برای سناریوهای درک تصویر و متن مناسب است و بر اساس جدیدترین مدل turbos از Hunyuan ساخته شده است. این مدل پرچمدار زبان تصویری نسل جدید است که بر وظایف مرتبط با درک تصویر و متن تمرکز دارد، از جمله شناسایی موجودیت‌های مبتنی بر تصویر، پرسش و پاسخ دانش، خلق متن تبلیغاتی و حل مسائل با عکس‌برداری. نسبت به نسل قبلی به طور کامل بهبود یافته است."}, "hunyuan-turbos-vision-20250619": {"description": "جدیدترین مدل پرچمدار زبان تصویری turbos-vision از Hunyuan که در وظایف مرتبط با درک تصویر و متن، از جمله شناسایی موجودیت‌های مبتنی بر تصویر، پرسش و پاسخ دانش، خلق متن تبلیغاتی و حل مسائل با عکس‌برداری، نسبت به نسخه پیش‌فرض نسل قبلی به طور کامل بهبود یافته است."}, "hunyuan-vision": {"description": "جدید<PERSON>رین مدل چندوجهی هون‌یوان، پشتیبانی از ورودی تصویر + متن برای تولید محتوای متنی."}, "image-01": {"description": "مدل جدید تولید تصویر با نمایش ظریف و پشتیبانی از تولید تصویر از متن و تصویر."}, "image-01-live": {"description": "مدل تولید تصویر با نمایش ظریف که از تولید تصویر از متن پشتیبانی می‌کند و امکان تنظیم سبک نقاشی را دارد."}, "imagen-4.0-generate-preview-06-06": {"description": "سری مدل متن به تصویر نسل چهارم Imagen"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "نسخه اولترا سری مدل متن به تصویر نسل چهارم Imagen"}, "imagen4/preview": {"description": "مدل تولید تصویر با بالاترین کیفیت گوگل"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 راه‌حل‌های گفتگوی هوشمند در چندین سناریو ارائه می‌دهد."}, "internlm2.5-latest": {"description": "جدیدترین سری مدل‌های ما با عملکرد استدلال عالی، از طول متن ۱M پشتیبانی می‌کند و توانایی‌های قوی‌تری در پیروی از دستورات و فراخوانی ابزارها دارد."}, "internlm3-latest": {"description": "سری جدیدترین مدل‌های ما با عملکرد استدلال برجسته، پیشتاز مدل‌های متن‌باز در همان سطح هستند. به طور پیش‌فرض به جدیدترین مدل‌های سری InternLM3 ما اشاره دارد."}, "internvl2.5-latest": {"description": "ما هنوز در حال نگهداری نسخه InternVL2.5 هستیم که دارای عملکرد عالی و پایدار است. به طور پیش‌فرض به جدیدترین مدل‌های سری InternVL2.5 ما اشاره دارد که در حال حاضر به internvl2.5-78b اشاره دارد."}, "internvl3-latest": {"description": "ما جدیدترین مدل بزرگ چندرسانه‌ای خود را منتشر کرده‌ایم که دارای توانایی‌های قوی‌تر در درک متن و تصویر و درک تصاویر در زمان‌های طولانی است و عملکرد آن با مدل‌های برتر بسته به منبع قابل مقایسه است. به طور پیش‌فرض به جدیدترین مدل‌های سری InternVL ما اشاره دارد که در حال حاضر به internvl3-78b اشاره دارد."}, "irag-1.0": {"description": "iRAG (image based RAG) که توسط بایدو توسعه یافته، فناوری تولید تصویر تقویت‌شده با بازیابی است که منابع میلیاردی تصاویر جستجوی بایدو را با توانایی‌های مدل پایه قدرتمند ترکیب می‌کند تا تصاویر بسیار واقعی تولید کند. این سیستم به طور قابل توجهی از سیستم‌های تولید تصویر بومی بهتر است، بدون حس مصنوعی بودن و با هزینه پایین. iRAG ویژگی‌هایی مانند بدون توهم، فوق‌العاده واقعی و آماده تحویل فوری دارد."}, "jamba-large": {"description": "قدرت‌مندترین و پیشرفته‌ترین مدل ما، که به‌طور خاص برای پردازش وظایف پیچیده در سطح سازمانی طراحی شده و دارای عملکرد فوق‌العاده‌ای است."}, "jamba-mini": {"description": "مدل کارآمدترین در این سطح، که سرعت و کیفیت را با هم ترکیب می‌کند و دارای ابعاد کوچکتری است."}, "jina-deepsearch-v1": {"description": "جستجوی عمیق ترکیبی از جستجوی اینترنتی، خواندن و استدلال است که می‌تواند تحقیقات جامع را انجام دهد. می‌توانید آن را به عنوان یک نماینده در نظر بگیرید که وظایف تحقیق شما را می‌پذیرد - این نماینده جستجوی گسترده‌ای انجام می‌دهد و پس از چندین بار تکرار، پاسخ را ارائه می‌دهد. این فرآیند شامل تحقیق مداوم، استدلال و حل مسئله از زوایای مختلف است. این با مدل‌های بزرگ استاندارد که مستقیماً از داده‌های پیش‌آموزش شده پاسخ تولید می‌کنند و سیستم‌های RAG سنتی که به جستجوی سطحی یک‌باره وابسته‌اند، تفاوت اساسی دارد."}, "kimi-k2": {"description": "Kimi-K2 یک مدل پایه با معماری MoE است که توسط Moonshot AI ارائه شده و دارای توانایی‌های بسیار قوی در کدنویسی و عامل است، با 1 تریلیون پارامتر کل و 32 میلیارد پارامتر فعال. در آزمون‌های معیار عملکرد در حوزه‌های دانش عمومی، برنامه‌نویسی، ریاضیات و عامل، مدل K2 عملکردی فراتر از سایر مدل‌های متن‌باز اصلی دارد."}, "kimi-k2-0711-preview": {"description": "kimi-k2 یک مدل پایه با معماری MoE است که دارای توانایی‌های بسیار قوی در کدنویسی و عامل‌سازی است، با مجموع یک تریلیون پارامتر و 32 میلیارد پارامتر فعال. در تست‌های معیار عملکرد در حوزه‌های دانش عمومی، برنامه‌نویسی، ریاضیات و عامل‌ها، مدل K2 عملکردی فراتر از سایر مدل‌های متن‌باز اصلی دارد."}, "kimi-latest": {"description": "محصول دستیار هوشمند کیمی از جدیدترین مدل بزرگ کیمی استفاده می‌کند و ممکن است شامل ویژگی‌های ناپایدار باشد. از درک تصویر پشتیبانی می‌کند و به‌طور خودکار بر اساس طول متن درخواست، مدل‌های 8k/32k/128k را به‌عنوان مدل محاسبه انتخاب می‌کند."}, "kimi-thinking-preview": {"description": "مدل kimi-thinking-preview که توسط Moon’s Dark Side ارائه شده است، مدلی چندرسانه‌ای با توانایی استدلال چندوجهی و استدلال عمومی است که در استدلال عمیق مهارت دارد و به حل مسائل پیچیده‌تر کمک می‌کند."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM یک مدل زبانی تجربی و خاص برای وظایف است که برای مطابقت با اصول علم یادگیری آموزش دیده است و می‌تواند در سناریوهای آموزشی و یادگیری از دستورات سیستم پیروی کند و به عنوان مربی متخصص عمل کند."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM یک مدل زبانی تجربی و خاص برای وظایف است که برای تطابق با اصول علم یادگیری آموزش دیده است و می‌تواند در سناریوهای آموزشی و یادگیری دستورات سیستم را دنبال کند و به عنوان یک مربی متخصص عمل کند."}, "lite": {"description": "Spark Lite یک مدل زبان بزرگ سبک است که دارای تأخیر بسیار کم و توانایی پردازش کارآمد می‌باشد. به‌طور کامل رایگان و باز است و از قابلیت جستجوی آنلاین در زمان واقعی پشتیبانی می‌کند. ویژگی پاسخ‌دهی سریع آن باعث می‌شود که در کاربردهای استنتاجی و تنظیم مدل در دستگاه‌های با توان محاسباتی پایین عملکرد برجسته‌ای داشته باشد و تجربه‌ای هوشمند و مقرون‌به‌صرفه برای کاربران فراهم کند. به‌ویژه در زمینه‌های پرسش و پاسخ دانش، تولید محتوا و جستجو عملکرد خوبی دارد."}, "llama-2-7b-chat": {"description": "سری مدل‌های زبانی بزرگ (LLM) Llama2 توسط Meta توسعه یافته و به صورت متن‌باز منتشر شده است. این مجموعه شامل مدل‌های متنی تولیدی با مقیاس‌های مختلف از 7 میلیارد تا 70 میلیارد پارامتر است که پیش‌آموزش و ری‌آموزش داده شده‌اند. از نظر معماری، Llama2 یک مدل زبانی خودرگرسیو با استفاده از معماری تبدیل‌کننده بهینه‌شده است. نسخه‌های تنظیم‌شده از این مدل با استفاده از ری‌آموزش نظارت‌شده (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) برای همگرایی با ترجیحات انسانی در مورد مفیدیت و ایمنی تنظیم شده‌اند. Llama2 نسبت به سری Llama در مجموعه‌های داده علمی مختلف عملکرد بهتری دارد و الهام بخش طراحی و توسعه مدل‌های دیگر بسیاری بوده است."}, "llama-3.1-70b-versatile": {"description": "لاما 3.1 70B توانایی استدلال هوش مصنوعی قوی‌تری را ارائه می‌دهد، مناسب برای برنامه‌های پیچیده، پشتیبانی از پردازش‌های محاسباتی فراوان و تضمین کارایی و دقت بالا."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B یک مدل با کارایی بالا است که توانایی تولید سریع متن را فراهم می‌کند و برای کاربردهایی که به بهره‌وری و صرفه‌جویی در هزینه در مقیاس بزرگ نیاز دارند، بسیار مناسب است."}, "llama-3.1-instruct": {"description": "مدل آموزشی لاما 3.1 برای بهینه‌سازی در صحنه‌های گفت‌وگو طراحی شده است و در معیارهای صنعتی معمول، بسیاری از مدل‌های چت منبع باز موجود را در برابر گذاشته است."}, "llama-3.2-11b-vision-instruct": {"description": "توانایی استدلال تصویری عالی در تصاویر با وضوح بالا، مناسب برای برنامه‌های درک بصری."}, "llama-3.2-11b-vision-preview": {"description": "لاما 3.2 برای انجام وظایفی که شامل داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "llama-3.2-90b-vision-instruct": {"description": "قابلیت‌های پیشرفته استدلال تصویری برای برنامه‌های نماینده درک بصری."}, "llama-3.2-90b-vision-preview": {"description": "لاما 3.2 برای انجام وظایفی که ترکیبی از داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصاویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "llama-3.2-vision-instruct": {"description": "مدل میکروآموزش Llama 3.2-Vision برای شناسایی بصری، استدلال تصویری، توصیف تصویر و پاسخ به سوالات مربوط به تصویر بهینه‌سازی شده است."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 پیشرفته‌ترین مدل زبان چندزبانه و متن‌باز در سری Llama است که تجربه‌ای با هزینه بسیار پایین مشابه عملکرد مدل 405B را ارائه می‌دهد. این مدل بر اساس ساختار Transformer طراحی شده و از طریق تنظیم دقیق نظارتی (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) بهبود کارایی و ایمنی یافته است. نسخه بهینه‌سازی شده آن برای مکالمات چندزبانه طراحی شده و در چندین معیار صنعتی از بسیاری از مدل‌های چت متن‌باز و بسته بهتر عمل می‌کند. تاریخ قطع دانش آن دسامبر 2023 است."}, "llama-3.3-70b-versatile": {"description": "مدل زبان بزرگ چند زبانه Meta Llama 3.3 (LLM) یک مدل تولیدی پیش‌آموزش دیده و تنظیم‌شده در 70B (ورودی متن/خروجی متن) است. مدل متن خالص Llama 3.3 برای کاربردهای گفتگوی چند زبانه بهینه‌سازی شده و در معیارهای صنعتی معمول در مقایسه با بسیاری از مدل‌های چت متن‌باز و بسته عملکرد بهتری دارد."}, "llama-3.3-instruct": {"description": "مدل آموزشی لاما ۳.۳ برای صحنه‌های گفت‌وگو بهینه‌سازی شده است و در معیارهای صنعتی معمول، بسیاری از مدل‌های چت منبع باز موجود را در برمی‌آید."}, "llama3-70b-8192": {"description": "متا لاما ۳ ۷۰B توانایی پردازش پیچیدگی بی‌نظیری را ارائه می‌دهد و برای پروژه‌های با نیازهای بالا طراحی شده است."}, "llama3-8b-8192": {"description": "متا لاما ۳ ۸B عملکرد استدلالی با کیفیت بالا را ارائه می‌دهد و برای نیازهای کاربردی در چندین سناریو مناسب است."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use قابلیت فراخوانی ابزارهای قدرتمند را فراهم می‌کند و از پردازش کارهای پیچیده به‌صورت کارآمد پشتیبانی می‌کند."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "لاما 3 Groq 8B Tool Use مدلی است که برای استفاده بهینه از ابزارها طراحی شده و از محاسبات سریع و موازی پشتیبانی می‌کند."}, "llama3.1": {"description": "Llama 3.1 مدل پیشرو ارائه شده توسط Meta است که از حداکثر 405 میلیارد پارامتر پشتیبانی می‌کند و می‌تواند در زمینه‌های مکالمات پیچیده، ترجمه چندزبانه و تحلیل داده‌ها به کار گرفته شود."}, "llama3.1:405b": {"description": "Llama 3.1 مدل پیشرو ارائه شده توسط Meta است که از 405 میلیارد پارامتر پشتیبانی می‌کند و می‌تواند در زمینه‌های مکالمات پیچیده، ترجمه چندزبانه و تحلیل داده‌ها به کار گرفته شود."}, "llama3.1:70b": {"description": "لاما 3.1 مدل پیشرو ارائه شده توسط متا است که از حداکثر 405 میلیارد پارامتر پشتیبانی می‌کند و می‌تواند در زمینه‌های مکالمات پیچیده، ترجمه چندزبانه و تحلیل داده‌ها به کار گرفته شود."}, "llava": {"description": "LLaVA یک مدل چندوجهی است که رمزگذار بصری و Vicuna را برای درک قدرتمند زبان و تصویر ترکیب می‌کند."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B قابلیت پردازش بصری را با هم ترکیب می‌کند و از طریق ورودی اطلاعات بصری خروجی‌های پیچیده تولید می‌کند."}, "llava:13b": {"description": "LLaVA یک مدل چندوجهی است که رمزگذار بصری و Vicuna را برای درک قدرتمند زبان و تصویر ترکیب می‌کند."}, "llava:34b": {"description": "LLaVA یک مدل چندوجهی است که رمزگذار بصری و Vicuna را برای درک قدرتمند زبان و تصویر ترکیب می‌کند."}, "mathstral": {"description": "MathΣtral به‌طور ویژه برای تحقیقات علمی و استدلال‌های ریاضی طراحی شده است و توانایی محاسباتی مؤثر و تفسیر نتایج را ارائه می‌دهد."}, "max-32k": {"description": "Spark Max 32K با قابلیت پردازش متن با زمینه بزرگ‌تر، توانایی درک و استدلال منطقی قوی‌تری دارد و از ورودی متنی تا 32K توکن پشتیبانی می‌کند. مناسب برای خواندن اسناد طولانی، پرسش و پاسخ با دانش خصوصی و موارد مشابه."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct یک مدل زبانی بزرگ است که به طور کامل توسط شرکت ووونگ شیونگ آموزش داده شده است. هدف از Megrez-3B-Instruct ایجاد یک راه‌حل هوشمند از طریق هماهنگی سخت‌افزار و نرم‌افزار است که دارای استنتاج سریع، حجم کوچک و آسانی در استفاده باشد."}, "meta-llama-3-70b-instruct": {"description": "یک مدل قدرتمند با ۷۰ میلیارد پارامتر که در استدلال، کدنویسی و کاربردهای گسترده زبانی عملکرد برجسته‌ای دارد."}, "meta-llama-3-8b-instruct": {"description": "یک مدل چندمنظوره با ۸ میلیارد پارامتر که برای وظایف مکالمه و تولید متن بهینه‌سازی شده است."}, "meta-llama-3.1-405b-instruct": {"description": "مدل متنی Llama 3.1 که برای تنظیم دستورات بهینه‌سازی شده و برای موارد استفاده مکالمه چندزبانه بهینه شده است. در بسیاری از مدل‌های چت منبع باز و بسته موجود، در معیارهای صنعتی رایج عملکرد برجسته‌ای دارد."}, "meta-llama-3.1-70b-instruct": {"description": "مدل متنی Llama 3.1 با تنظیمات دستوری، بهینه‌سازی شده برای موارد استفاده در مکالمات چندزبانه، که در بسیاری از مدل‌های چت منبع باز و بسته موجود، در معیارهای صنعتی رایج عملکرد برجسته‌ای دارد."}, "meta-llama-3.1-8b-instruct": {"description": "مدل متنی Llama 3.1 که برای تنظیم دستورالعمل‌ها بهینه‌سازی شده و برای موارد استفاده مکالمه چندزبانه بهینه شده است. در بسیاری از مدل‌های چت منبع باز و بسته موجود، در معیارهای صنعتی رایج عملکرد برجسته‌ای دارد."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) توانایی‌های پردازش زبان عالی و تجربه تعاملی بی‌نظیری را ارائه می‌دهد."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 توانایی‌های پردازش زبان عالی و تجربه تعاملی بی‌نظیری را ارائه می‌دهد."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "Llama 3 70B Instruct Reference یک مدل چت قدرتمند است که از نیازهای پیچیده مکالمه پشتیبانی می‌کند."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "Llama 3 8B Instruct Reference پشتیبانی چندزبانه ارائه می‌دهد و شامل دانش گسترده‌ای در زمینه‌های مختلف است."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 برای انجام وظایفی که ترکیبی از داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 برای انجام وظایفی که ترکیبی از داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 برای انجام وظایفی که ترکیبی از داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "مدل بزرگ زبان چند زبانه Meta Llama 3.3 (LLM) یک مدل تولیدی پیش‌آموزش و تنظیم دستوری در 70B (ورودی متن/خروجی متن) است. مدل تنظیم دستوری Llama 3.3 به طور خاص برای موارد استفاده مکالمه چند زبانه بهینه‌سازی شده و در معیارهای صنعتی رایج از بسیاری از مدل‌های چت متن‌باز و بسته موجود بهتر عمل می‌کند."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 برای انجام وظایفی که ترکیبی از داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite مناسب برای محیط‌هایی که به عملکرد بالا و تأخیر کم نیاز دارند."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo توانایی‌های برجسته‌ای در درک و تولید زبان ارائه می‌دهد و برای سخت‌ترین وظایف محاسباتی مناسب است."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite برای محیط‌های با منابع محدود مناسب است و عملکرد متعادلی را ارائه می‌دهد."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo یک مدل زبان بزرگ با کارایی بالا است که از طیف گسترده‌ای از کاربردها پشتیبانی می‌کند."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "مدل LLaMA 3.1 405B که برای تنظیمات دستوری بهینه‌سازی شده است، برای سناریوهای مکالمه چندزبانه بهینه شده است."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "مدل Llama 3.1 Turbo با ظرفیت 405B، پشتیبانی از زمینه‌های بسیار بزرگ برای پردازش داده‌های عظیم را فراهم می‌کند و در کاربردهای هوش مصنوعی در مقیاس بسیار بزرگ عملکرد برجسته‌ای دارد."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 مدل پیشرو ارائه شده توسط Meta است که از حداکثر 405B پارامتر پشتیبانی می‌کند و می‌تواند در زمینه‌های گفتگوهای پیچیده، ترجمه چند زبانه و تحلیل داده‌ها استفاده شود."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "مدل Llama 3.1 70B به‌طور دقیق تنظیم شده است و برای برنامه‌های با بار سنگین مناسب است. با کمیت‌سازی به FP8، توان محاسباتی و دقت بیشتری ارائه می‌دهد و عملکرد برتری را در سناریوهای پیچیده تضمین می‌کند."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "مدل Llama 3.1 8B از کوانتیزاسیون FP8 استفاده می‌کند و از حداکثر 131,072 توکن متنی پشتیبانی می‌کند. این مدل یکی از بهترین‌ها در میان مدل‌های متن‌باز است و برای وظایف پیچیده مناسب بوده و در بسیاری از معیارهای صنعتی عملکرد برتری دارد."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct برای بهینه‌سازی در سناریوهای مکالمه با کیفیت بالا طراحی شده و در ارزیابی‌های مختلف انسانی عملکرد برجسته‌ای دارد."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct برای بهینه‌سازی سناریوهای مکالمه با کیفیت بالا طراحی شده و عملکردی بهتر از بسیاری از مدل‌های بسته دارد."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct به‌طور ویژه برای مکالمات با کیفیت بالا طراحی شده است و در ارزیابی‌های انسانی عملکرد برجسته‌ای دارد. این مدل به‌ویژه برای سناریوهای تعامل بالا مناسب است."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct جدیدترین نسخه ارائه شده توسط Meta است که برای بهینه‌سازی سناریوهای مکالمه با کیفیت بالا طراحی شده و عملکرد بهتری نسبت به بسیاری از مدل‌های بسته پیشرو دارد."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 پشتیبانی چندزبانه ارائه می‌دهد و یکی از مدل‌های پیشرو در صنعت تولید محتوا است."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 برای انجام وظایفی که ترکیبی از داده‌های بصری و متنی هستند طراحی شده است. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 برای انجام وظایفی طراحی شده است که داده‌های بصری و متنی را با هم ترکیب می‌کند. این مدل در وظایفی مانند توصیف تصویر و پرسش و پاسخ بصری عملکرد بسیار خوبی دارد و فاصله بین تولید زبان و استدلال بصری را پر می‌کند."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 پیشرفته‌ترین مدل زبان چندزبانه و متن‌باز در سری Llama است که تجربه‌ای با هزینه بسیار پایین مشابه عملکرد مدل 405B را ارائه می‌دهد. این مدل بر اساس ساختار Transformer طراحی شده و از طریق تنظیم دقیق نظارتی (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) بهبود کارایی و ایمنی یافته است. نسخه بهینه‌سازی شده آن برای مکالمات چندزبانه طراحی شده و در چندین معیار صنعتی از بسیاری از مدل‌های چت متن‌باز و بسته بهتر عمل می‌کند. تاریخ قطع دانش آن دسامبر 2023 است."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 پیشرفته‌ترین مدل زبان چندزبانه و متن‌باز در سری Llama است که تجربه‌ای با هزینه بسیار پایین مشابه عملکرد مدل 405B را ارائه می‌دهد. این مدل بر اساس ساختار Transformer طراحی شده و از طریق تنظیم دقیق نظارتی (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) بهبود کارایی و ایمنی یافته است. نسخه بهینه‌سازی شده آن برای مکالمات چندزبانه طراحی شده و در چندین معیار صنعتی از بسیاری از مدل‌های چت متن‌باز و بسته بهتر عمل می‌کند. تاریخ قطع دانش آن دسامبر 2023 است."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct بزرگترین و قدرتمندترین مدل در میان مدل‌های Llama 3.1 Instruct است. این یک مدل بسیار پیشرفته برای استدلال مکالمه‌ای و تولید داده‌های مصنوعی است و همچنین می‌تواند به عنوان پایه‌ای برای پیش‌تمرین یا تنظیم دقیق مداوم در حوزه‌های خاص استفاده شود. Llama 3.1 مجموعه‌ای از مدل‌های زبان بزرگ چندزبانه (LLMs) است که از پیش آموزش دیده و برای دستورالعمل‌ها تنظیم شده‌اند و شامل اندازه‌های 8B، 70B و 405B (ورودی/خروجی متنی) می‌باشد. مدل‌های متنی تنظیم‌شده بر اساس دستورالعمل‌های Llama 3.1 (8B، 70B، 405B) به‌طور خاص برای موارد استفاده مکالمه چندزبانه بهینه‌سازی شده‌اند و در بسیاری از معیارهای استاندارد صنعتی از مدل‌های چت منبع باز موجود پیشی گرفته‌اند. Llama 3.1 برای استفاده‌های تجاری و تحقیقاتی در زبان‌های مختلف طراحی شده است. مدل‌های متنی تنظیم‌شده بر اساس دستورالعمل‌ها برای چت‌های مشابه دستیار مناسب هستند، در حالی که مدل‌های پیش‌آموزش‌دیده می‌توانند برای انواع وظایف تولید زبان طبیعی سازگار شوند. مدل‌های Llama 3.1 همچنین از خروجی‌های خود برای بهبود سایر مدل‌ها، از جمله تولید داده‌های مصنوعی و پالایش، پشتیبانی می‌کنند. Llama 3.1 یک مدل زبان خودبازگشتی است که از معماری بهینه‌شده ترانسفورمر استفاده می‌کند. نسخه‌های تنظیم‌شده از تنظیم دقیق نظارت‌شده (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) برای تطابق با ترجیحات انسانی در مورد کمک‌رسانی و ایمنی استفاده می‌کنند."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "نسخه به‌روزرسانی‌شده Meta Llama 3.1 70B Instruct، شامل طول زمینه 128K توسعه‌یافته، چندزبانه بودن و بهبود توانایی استدلال. مدل‌های زبان بزرگ چندزبانه (LLMs) ارائه‌شده توسط Llama 3.1 مجموعه‌ای از مدل‌های تولیدی پیش‌تمرین‌شده و تنظیم‌شده با دستورالعمل هستند که شامل اندازه‌های 8B، 70B و 405B (ورودی/خروجی متنی) می‌باشند. مدل‌های متنی تنظیم‌شده با دستورالعمل Llama 3.1 (8B، 70B، 405B) به‌طور خاص برای موارد استفاده مکالمه چندزبانه بهینه‌سازی شده‌اند و در بسیاری از معیارهای استاندارد صنعتی از مدل‌های چت منبع‌باز موجود پیشی گرفته‌اند. Llama 3.1 برای استفاده‌های تجاری و تحقیقاتی در زبان‌های مختلف طراحی شده است. مدل‌های متنی تنظیم‌شده با دستورالعمل برای چت‌های مشابه دستیار مناسب هستند، در حالی که مدل‌های پیش‌تمرین‌شده می‌توانند برای انواع وظایف تولید زبان طبیعی سازگار شوند. مدل‌های Llama 3.1 همچنین از خروجی‌های خود برای بهبود سایر مدل‌ها، از جمله تولید داده‌های مصنوعی و پالایش، پشتیبانی می‌کنند. Llama 3.1 یک مدل زبان خودبازگشتی است که از معماری بهینه‌شده ترانسفورمر استفاده می‌کند. نسخه تنظیم‌شده از تنظیم دقیق نظارت‌شده (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) برای هم‌راستایی با ترجیحات انسانی در مورد کمک‌رسانی و ایمنی استفاده می‌کند."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "نسخه به‌روزرسانی شده Meta Llama 3.1 8B Instruct، شامل طول زمینه 128K توسعه‌یافته، چندزبانه بودن و بهبود توانایی استدلال. Llama 3.1 مدل‌های زبانی بزرگ چندزبانه (LLMs) را ارائه می‌دهد که مجموعه‌ای از مدل‌های تولیدی پیش‌تمرین‌شده و تنظیم‌شده با دستورالعمل هستند و شامل اندازه‌های 8B، 70B و 405B (ورودی/خروجی متنی) می‌باشند. مدل‌های متنی تنظیم‌شده با دستورالعمل Llama 3.1 (8B، 70B، 405B) به‌طور خاص برای موارد استفاده مکالمه چندزبانه بهینه‌سازی شده‌اند و در معیارهای صنعتی رایج از بسیاری از مدل‌های چت متن‌باز موجود پیشی گرفته‌اند. Llama 3.1 برای استفاده‌های تجاری و تحقیقاتی در زبان‌های مختلف طراحی شده است. مدل‌های متنی تنظیم‌شده با دستورالعمل برای چت‌های مشابه دستیار مناسب هستند، در حالی که مدل‌های پیش‌تمرین‌شده می‌توانند برای انواع وظایف تولید زبان طبیعی سازگار شوند. مدل‌های Llama 3.1 همچنین از خروجی‌های خود برای بهبود سایر مدل‌ها، از جمله تولید داده‌های مصنوعی و پالایش، پشتیبانی می‌کنند. Llama 3.1 یک مدل زبانی خودبازگشتی است که از معماری بهینه‌شده ترانسفورمر استفاده می‌کند. نسخه تنظیم‌شده از تنظیم دقیق نظارت‌شده (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) برای هم‌راستا شدن با ترجیحات انسانی در مورد کمک‌رسانی و ایمنی استفاده می‌کند."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 یک مدل زبان بزرگ (LLM) باز برای توسعه‌دهندگان، پژوهشگران و شرکت‌ها است که به آن‌ها کمک می‌کند تا ایده‌های هوش مصنوعی تولیدی خود را بسازند، آزمایش کنند و به‌طور مسئولانه گسترش دهند. به‌عنوان بخشی از سیستم پایه نوآوری جامعه جهانی، این مدل برای تولید محتوا، هوش مصنوعی مکالمه‌ای، درک زبان، تحقیق و توسعه و کاربردهای شرکتی بسیار مناسب است."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 یک مدل زبان بزرگ باز (LLM) است که برای توسعه‌دهندگان، پژوهشگران و شرکت‌ها طراحی شده است تا به آن‌ها در ساخت، آزمایش و گسترش مسئولانه ایده‌های هوش مصنوعی مولد کمک کند. به عنوان بخشی از سیستم پایه نوآوری جامعه جهانی، این مدل برای دستگاه‌های با توان محاسباتی و منابع محدود، دستگاه‌های لبه و زمان‌های آموزش سریع‌تر بسیار مناسب است."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "توانایی استدلال تصویری برجسته در تصاویر با وضوح بالا، مناسب برای برنامه‌های درک بصری."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "توانایی استدلال تصویری پیشرفته برای برنامه‌های عامل درک بصری."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 پیشرفته‌ترین مدل زبان بزرگ چندزبانه متن‌باز در سری Llama است که عملکردی مشابه مدل ۴۰۵ میلیارد پارامتری را با هزینه بسیار پایین ارائه می‌دهد. مبتنی بر ساختار ترنسفورمر و با بهبودهای نظارت شده (SFT) و یادگیری تقویتی با بازخورد انسانی (RLHF) برای افزایش کارایی و ایمنی. نسخه تنظیم شده برای دستورالعمل بهینه شده برای گفتگوهای چندزبانه است و در معیارهای صنعتی متعدد از بسیاری از مدل‌های چت متن‌باز و بسته بهتر عمل می‌کند. تاریخ قطع دانش: دسا<PERSON>بر ۲۰۲۳."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "یک مدل قدرتمند با ۷۰ میلیارد پارامتر که در استدلال، کدنویسی و کاربردهای گسترده زبانی عملکرد برجسته‌ای دارد."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "یک مدل چندمنظوره با ۸ میلیارد پارامتر که برای وظایف گفتگو و تولید متن بهینه شده است."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "مدل متنی تنظیم شده برای دستورالعمل Llama 3.1 که برای موارد استفاده گفتگوهای چندزبانه بهینه شده و در بسیاری از مدل‌های چت متن‌باز و بسته موجود، در معیارهای صنعتی رایج عملکرد برجسته‌ای دارد."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "مدل متنی تنظیم شده برای دستورالعمل Llama 3.1 که برای موارد استفاده گفتگوهای چندزبانه بهینه شده و در بسیاری از مدل‌های چت متن‌باز و بسته موجود، در معیارهای صنعتی رایج عملکرد برجسته‌ای دارد."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "مدل متنی تنظیم شده برای دستورالعمل Llama 3.1 که برای موارد استفاده گفتگوهای چندزبانه بهینه شده و در بسیاری از مدل‌های چت متن‌باز و بسته موجود، در معیارهای صنعتی رایج عملکرد برجسته‌ای دارد."}, "meta/llama-3.1-405b-instruct": {"description": "مدل LLM پیشرفته که از تولید داده‌های ترکیبی، تقطیر دانش و استدلال پشتیبانی می‌کند و برای ربات‌های چت، برنامه‌نویسی و وظایف خاص مناسب است."}, "meta/llama-3.1-70b-instruct": {"description": "توانمندسازی گفتگوهای پیچیده با درک زمینه‌ای عالی، توانایی استدلال و قابلیت تولید متن."}, "meta/llama-3.1-8b-instruct": {"description": "مدل پیشرفته و پیشرفته که دارای درک زبان، توانایی استدلال عالی و قابلیت تولید متن است."}, "meta/llama-3.2-11b-vision-instruct": {"description": "مدل بینایی-ز<PERSON><PERSON> پیشرفته که در استدلال با کیفیت بالا از تصاویر مهارت دارد."}, "meta/llama-3.2-1b-instruct": {"description": "مدل زبان کوچک پیشرفته و پیشرفته که دارای درک زبان، توانایی استدلال عالی و قابلیت تولید متن است."}, "meta/llama-3.2-3b-instruct": {"description": "مدل زبان کوچک پیشرفته و پیشرفته که دارای درک زبان، توانایی استدلال عالی و قابلیت تولید متن است."}, "meta/llama-3.2-90b-vision-instruct": {"description": "مدل بینایی-ز<PERSON><PERSON> پیشرفته که در استدلال با کیفیت بالا از تصاویر مهارت دارد."}, "meta/llama-3.3-70b-instruct": {"description": "مدل LLM پیشرفته که در استدلال، ریاضیات، دانش عمومی و فراخوانی توابع مهارت دارد."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "همان مدل Phi-3-medium با اندازه زمینه بزرگ‌تر، مناسب برای RAG یا تعداد کمی از پرامپت‌ها."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "مدلی با ۱۴ میلیارد پارامتر که کیفیت آن از Phi-3-mini بالاتر است و تمرکز بر داده‌های با کیفیت و استدلالی دارد."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "همان مدل Phi-3-mini با اندازه زمینه بزرگ‌تر، مناسب برای RAG یا تعداد کمی از پرامپت‌ها."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "کوچک‌ترین عضو خانواده Phi-3 که برای کیفیت و تأخیر کم بهینه شده است."}, "microsoft/Phi-3-small-128k-instruct": {"description": "همان مدل Phi-3-small با اندازه زمینه بزرگ‌تر، مناسب برای RAG یا تعداد کمی از پرامپت‌ها."}, "microsoft/Phi-3-small-8k-instruct": {"description": "مدلی با ۷ میلیارد پارامتر که کیفیت آن از Phi-3-mini بالاتر است و تمرکز بر داده‌های با کیفیت و استدلالی دارد."}, "microsoft/Phi-3.5-mini-instruct": {"description": "نسخه به‌روزشده مدل Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "نسخه به‌روزشده مدل Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 یک مدل زبانی است که توسط AI مایکروسافت ارائه شده و در زمینه‌های گفتگوی پیچیده، چند زبانه، استدلال و دستیار هوشمند به ویژه عملکرد خوبی دارد."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B پیشرفته‌ترین مدل Wizard از مایکروسافت AI است که عملکردی بسیار رقابتی از خود نشان می‌دهد."}, "minicpm-v": {"description": "MiniCPM-V نسل جدید مدل چندوجهی است که توسط OpenBMB ارائه شده و دارای توانایی‌های برجسته در تشخیص OCR و درک چندوجهی است و از طیف گسترده‌ای از کاربردها پشتیبانی می‌کند."}, "ministral-3b-latest": {"description": "Ministral 3B مدل پیشرفته و برتر Mistral در سطح جهانی است."}, "ministral-8b-latest": {"description": "Ministral 8B یک مدل لبه‌ای با صرفه اقتصادی بالا از Mistral است."}, "mistral": {"description": "Mistral یک مدل 7B است که توسط Mistral AI منتشر شده و برای نیازهای متنوع پردازش زبان مناسب است."}, "mistral-ai/Mistral-Large-2411": {"description": "مدل پرچمدار Mistral که برای وظایف پیچیده‌ای که نیاز به توان استدلال در مقیاس بزرگ یا تخصصی بالا دارند (تولید متن ترکیبی، تولید کد، RAG یا عامل‌ها) مناسب است."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo یک مدل زبان پیشرفته (LLM) است که در دسته اندازه خود دارای بهترین توانایی‌های استدلال، دانش جهانی و کدنویسی است."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small برای هر وظیفه مبتنی بر زبان که نیاز به کارایی بالا و تأخیر کم دارد، قابل استفاده است."}, "mistral-large": {"description": "Mixtral Large مدل پرچمدار Mistral است که توانایی تولید کد، ریاضیات و استدلال را ترکیب می‌کند و از پنجره متنی ۱۲۸k پشتیبانی می‌کند."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 یک مدل زبانی بزرگ و پیشرفته (LLM) است که ۱۲۳ میلیارد پارامتر دارد و توانایی استدلال، دانش و برنامه‌نویسی مدرن را در خود جمع آوری کرده است."}, "mistral-large-latest": {"description": "Mistral Large یک مدل بزرگ پرچمدار است که در انجام وظایف چندزبانه، استدلال پیچیده و تولید کد مهارت دارد و انتخابی ایده‌آل برای کاربردهای سطح بالا است."}, "mistral-medium-latest": {"description": "Mistral Medium 3 با هزینه 8 برابری، عملکرد پیشرفته‌ای را ارائه می‌دهد و به طور اساسی استقرارهای شرکتی را ساده‌تر می‌کند."}, "mistral-nemo": {"description": "Mistral Nemo توسط Mistral AI و NVIDIA به‌طور مشترک عرضه شده است و یک مدل ۱۲ میلیاردی با کارایی بالا می‌باشد."}, "mistral-nemo-instruct": {"description": "مدل زبانی بزرگ (LLM) میسترال-نیمو-آموزش-۲۴۰۷ نسخه‌ای از میسترال-نیمو-پایه-۲۴۰۷ است که برای اجرای دستورالعمل‌ها آموزش داده شده است."}, "mistral-small": {"description": "Mistral Small می‌تواند برای هر وظیفه‌ای که نیاز به کارایی بالا و تأخیر کم دارد، مبتنی بر زبان استفاده شود."}, "mistral-small-latest": {"description": "Mistral Small یک گزینه مقرون‌به‌صرفه، سریع و قابل‌اعتماد است که برای موارد استفاده‌ای مانند ترجمه، خلاصه‌سازی و تحلیل احساسات مناسب است."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct به دلیل عملکرد بالا شناخته شده است و برای وظایف مختلف زبانی مناسب است."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "مدل تنظیم دستور Mistral AI"}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 توانایی محاسباتی بالا و درک زبان طبیعی را ارائه می‌دهد و برای کاربردهای گسترده مناسب است."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B یک مدل فشرده اما با عملکرد بالا است که در پردازش دسته‌ای و وظایف ساده مانند طبقه‌بندی و تولید متن مهارت دارد و دارای توانایی استدلال خوبی است."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) یک مدل زبان بسیار بزرگ است که از نیازهای پردازشی بسیار بالا پشتیبانی می‌کند."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral-8x7B Instruct (46.7B) یک چارچوب محاسباتی با ظرفیت بالا ارائه می‌دهد که برای پردازش داده‌های بزرگ مقیاس مناسب است."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B یک مدل متخصص پراکنده است که با استفاده از پارامترهای متعدد سرعت استنتاج را افزایش می‌دهد و برای انجام وظایف چندزبانه و تولید کد مناسب است."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct یک مدل استاندارد صنعتی با عملکرد بالا است که بهینه‌سازی سرعت و پشتیبانی از متن طولانی را ترکیب می‌کند."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo یک مدل با 7.3 میلیارد پارامتر است که از برنامه‌نویسی با عملکرد بالا و پشتیبانی چندزبانه برخوردار است."}, "mixtral": {"description": "Mixtral مدل تخصصی Mistral AI است که دارای وزن‌های متن‌باز بوده و در زمینه تولید کد و درک زبان پشتیبانی ارائه می‌دهد."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B قابلیت محاسبات موازی با تحمل خطای بالا را ارائه می‌دهد و برای وظایف پیچیده مناسب است."}, "mixtral:8x22b": {"description": "Mixtral مدل تخصصی Mistral AI است که دارای وزن‌های متن‌باز بوده و در تولید کد و درک زبان پشتیبانی ارائه می‌دهد."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K یک مدل با قابلیت پردازش متن طولانی است که برای تولید متون بسیار طولانی مناسب است. این مدل می‌تواند تا 128,000 توکن را پردازش کند و برای کاربردهایی مانند پژوهش، علمی و تولید اسناد بزرگ بسیار مناسب است."}, "moonshot-v1-128k-vision-preview": {"description": "مدل بصر<PERSON> (شام<PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview و غیره) قادر به درک محتوای تصویر است، از جمله متن تصویر، رنگ تصویر و شکل اشیاء."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K توانایی پردازش متن با طول متوسط را فراهم می‌کند و قادر به پردازش 32,768 توکن است. این مدل به‌ویژه برای تولید اسناد طولانی و مکالمات پیچیده مناسب است و در زمینه‌هایی مانند تولید محتوا، ایجاد گزارش و سیستم‌های مکالمه کاربرد دارد."}, "moonshot-v1-32k-vision-preview": {"description": "مدل بصر<PERSON> (شام<PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview و غیره) قادر به درک محتوای تصویر است، از جمله متن تصویر، رنگ تصویر و شکل اشیاء."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K به‌طور ویژه برای تولید متن‌های کوتاه طراحی شده است و دارای عملکرد پردازشی کارآمدی است که می‌تواند ۸,۱۹۲ توکن را پردازش کند. این مدل برای مکالمات کوتاه، یادداشت‌برداری سریع و تولید محتوای سریع بسیار مناسب است."}, "moonshot-v1-8k-vision-preview": {"description": "مدل بصر<PERSON> (شام<PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview و غیره) قادر به درک محتوای تصویر است، از جمله متن تصویر، رنگ تصویر و شکل اشیاء."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto می‌تواند بر اساس تعداد توکن‌های اشغال شده در متن فعلی، مدل مناسب را انتخاب کند."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B یک مدل بزرگ کد منبع باز است که با یادگیری تقویتی گسترده بهینه شده است و قادر به تولید پچ‌های پایدار و قابل استفاده مستقیم در تولید می‌باشد. این مدل در SWE-bench Verified امتیاز جدید ۶۰.۴٪ را کسب کرده و رکورد مدل‌های منبع باز را در وظایف مهندسی نرم‌افزار خودکار مانند رفع اشکال و بازبینی کد شکسته است."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 یک مدل پایه با معماری MoE است که دارای توانایی‌های بسیار قوی در کدنویسی و عامل است، با 1 تریلیون پارامتر کل و 32 میلیارد پارامتر فعال. در آزمون‌های معیار عملکرد در حوزه‌های دانش عمومی، برنامه‌نویسی، ریاضیات و عامل، مدل K2 عملکردی فراتر از سایر مدل‌های متن‌باز اصلی دارد."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 یک مدل پایه با معماری MoE است که دارای توانایی‌های بسیار قوی در کدنویسی و عامل‌ها می‌باشد، با مجموع پارامتر ۱ تریلیون و پارامترهای فعال ۳۲ میلیارد. در آزمون‌های معیار عملکرد در دسته‌های اصلی مانند استدلال دانش عمومی، برنامه‌نویسی، ریاضیات و عامل‌ها، مدل K2 عملکردی فراتر از سایر مدل‌های متن‌باز رایج دارد."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "هرمس ۲ پرو لاما ۳ ۸B نسخه ارتقاء یافته Nous Hermes 2 است که شامل جدیدترین مجموعه داده‌های توسعه‌یافته داخلی می‌باشد."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B یک مدل زبانی بزرگ سفارشی شده توسط NVIDIA است که به منظور افزایش کمک به پاسخ‌های تولید شده توسط LLM برای پرسش‌های کاربران طراحی شده است. این مدل در آزمون‌های معیار مانند Arena Hard، AlpacaEval 2 LC و GPT-4-Turbo MT-Bench عملکرد عالی داشته و تا تاریخ 1 اکتبر 2024 در تمامی سه آزمون خودکار هم‌راستایی در رتبه اول قرار دارد. این مدل با استفاده از RLHF (به ویژه REINFORCE)، Llama-3.1-Nemotron-70B-Reward و HelpSteer2-Preference در مدل Llama-3.1-70B-Instruct آموزش دیده است."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "مدل زبان منحصر به فرد که دقت و کارایی بی‌نظیری را ارائه می‌دهد."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B یک مدل زبان بزرگ سفارشی از NVIDIA است که به منظور افزایش کمک‌پذیری پاسخ‌های تولید شده توسط LLM طراحی شده است."}, "o1": {"description": "متمرکز بر استدلال پیشرفته و حل مسائل پیچیده، از جمله وظایف ریاضی و علمی. بسیار مناسب برای برنامه‌هایی که به درک عمیق زمینه و مدیریت جریان‌های کاری نیاز دارند."}, "o1-mini": {"description": "کوچکتر و سریعتر از o1-preview، با ۸۰٪ هزینه کمتر، و عملکرد خوب در تولید کد و عملیات با زمینه‌های کوچک."}, "o1-preview": {"description": "تمرکز بر استدلال پیشرفته و حل مسائل پیچیده، از جمله وظایف ریاضی و علمی. بسیار مناسب برای برنامه‌هایی که نیاز به درک عمیق از زمینه و جریان کاری خودمختار دارند."}, "o1-pro": {"description": "مدل‌های سری o1 با آموزش تقویت یادگیری قادرند پیش از پاسخ‌دهی تفکر کنند و وظایف استدلال پیچیده را انجام دهند. مدل o1-pro از منابع محاسباتی بیشتری استفاده می‌کند تا تفکر عمیق‌تری داشته باشد و پاسخ‌های با کیفیت‌تری ارائه دهد."}, "o3": {"description": "o3 یک مدل همه‌کاره و قدرتمند است که در چندین حوزه عملکرد عالی دارد. این مدل استاندارد جدیدی برای وظایف ریاضی، علمی، برنامه‌نویسی و استدلال بصری تعیین کرده است. همچنین در نوشتن فنی و پیروی از دستورات نیز مهارت دارد. کاربران می‌توانند از آن برای تحلیل متن، کد و تصاویر و حل مسائل پیچیده چند مرحله‌ای استفاده کنند."}, "o3-deep-research": {"description": "o3-deep-research پیشرفته‌ترین مدل تحقیق عمیق ما است که به‌طور خاص برای انجام وظایف تحقیقاتی پیچیده و چندمرحله‌ای طراحی شده است. این مدل می‌تواند از اینترنت جستجو و اطلاعات را ترکیب کند و همچنین از طریق اتصال MCP به داده‌های اختصاصی شما دسترسی پیدا کرده و از آن‌ها بهره‌برداری کند."}, "o3-mini": {"description": "o3-mini جدیدترین مدل استنتاج کوچک ماست که هوش بالایی را با هزینه و هدف تأخیر مشابه o1-mini ارائه می‌دهد."}, "o3-pro": {"description": "مدل o3-pro با استفاده از محاسبات بیشتر، تفکر عمیق‌تری انجام می‌دهد و همواره پاسخ‌های بهتری ارائه می‌کند. فقط در API پاسخ‌ها قابل استفاده است."}, "o4-mini": {"description": "o4-mini جدیدترین مدل کوچک از سری o ما است. این مدل به‌طور خاص برای استدلال سریع و مؤثر بهینه‌سازی شده و در وظایف کدنویسی و بصری عملکرد بسیار بالایی دارد."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research مدل تحقیق عمیق سریع‌تر و مقرون‌به‌صرفه‌تر ما است که برای انجام وظایف تحقیقاتی پیچیده و چندمرحله‌ای بسیار مناسب است. این مدل می‌تواند از اینترنت جستجو و اطلاعات را ترکیب کند و همچنین از طریق اتصال MCP به داده‌های اختصاصی شما دسترسی پیدا کرده و از آن‌ها بهره‌برداری کند."}, "open-codestral-mamba": {"description": "Codestral Mamba یک مدل زبان Mamba 2 است که بر تولید کد تمرکز دارد و پشتیبانی قدرتمندی برای وظایف پیشرفته کدنویسی و استدلال ارائه می‌دهد."}, "open-mistral-7b": {"description": "Mistral 7B یک مدل فشرده اما با عملکرد بالا است که در پردازش دسته‌ای و وظایف ساده مانند طبقه‌بندی و تولید متن مهارت دارد و دارای توانایی استدلال خوبی است."}, "open-mistral-nemo": {"description": "Mistral Nemo یک مدل 12 میلیاردی است که با همکاری Nvidia توسعه یافته و عملکرد عالی در استدلال و کدنویسی ارائه می‌دهد و به راحتی قابل ادغام و جایگزینی است."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B یک مدل تخصصی بزرگتر است که بر روی وظایف پیچیده تمرکز دارد و توانایی استدلال عالی و توان عملیاتی بالاتری را ارائه می‌دهد."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B یک مدل متخصص پراکنده است که با استفاده از پارامترهای متعدد سرعت استنتاج را افزایش می‌دهد و برای پردازش وظایف چندزبانه و تولید کد مناسب است."}, "openai/gpt-4.1": {"description": "GPT-4.1 پرچمدار مدل‌های ما برای وظایف پیچیده است. این مدل برای حل مسائل بین‌رشته‌ای بسیار مناسب است."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini تعادلی بین هوش، سرعت و هزینه ارائه می‌دهد و آن را به مدلی جذاب در بسیاری از موارد استفاده تبدیل می‌کند."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano سریع‌ترین و مقرون به صرفه‌ترین مدل GPT-4.1 است."}, "openai/gpt-4o": {"description": "ChatGPT-4o یک مدل پویا است که به‌صورت زنده به‌روزرسانی می‌شود تا همیشه نسخه‌ی جدید و به‌روز باشد. این مدل ترکیبی از توانایی‌های قدرتمند درک و تولید زبان را ارائه می‌دهد و برای کاربردهای گسترده مانند خدمات مشتری، آموزش و پشتیبانی فنی مناسب است."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini جدیدترین مدل OpenAI است که پس از GPT-4 Omni عرضه شده و از ورودی‌های تصویری و متنی پشتیبانی می‌کند و خروجی متنی ارائه می‌دهد. به عنوان پیشرفته‌ترین مدل کوچک آن‌ها، این مدل بسیار ارزان‌تر از سایر مدل‌های پیشرفته اخیر است و بیش از ۶۰٪ ارزان‌تر از GPT-3.5 Turbo می‌باشد. این مدل هوشمندی پیشرفته را حفظ کرده و در عین حال از نظر اقتصادی بسیار مقرون به صرفه است. GPT-4o mini در آزمون MMLU امتیاز ۸۲٪ را کسب کرده و در حال حاضر در ترجیحات چت بالاتر از GPT-4 رتبه‌بندی شده است."}, "openai/o1": {"description": "o1 مدل استدلال جدید OpenAI است که از ورودی‌های تصویری و متنی پشتیبانی می‌کند و خروجی متنی ارائه می‌دهد، مناسب برای وظایف پیچیده‌ای که نیاز به دانش عمومی گسترده دارند. این مدل دارای زمینه ۲۰۰ هزار توکنی و تاریخ قطع دانش در اکتبر ۲۰۲۳ است."}, "openai/o1-mini": {"description": "o1-mini یک مدل استنتاج سریع و مقرون‌به‌صرفه است که برای برنامه‌نویسی، ریاضیات و کاربردهای علمی طراحی شده است. این مدل دارای ۱۲۸ هزار بایت زمینه و تاریخ قطع دانش تا اکتبر ۲۰۲۳ می‌باشد."}, "openai/o1-preview": {"description": "o1 مدل جدید استنتاج OpenAI است که برای وظایف پیچیده‌ای که به دانش عمومی گسترده نیاز دارند، مناسب است. این مدل دارای 128K زمینه و تاریخ قطع دانش تا اکتبر 2023 است."}, "openai/o3": {"description": "o3 یک مدل قدرتمند و چندمنظوره است که در زمینه‌های مختلف عملکرد عالی دارد. این مدل استانداردهای جدیدی را برای وظایف ریاضی، علمی، برنامه‌نویسی و استدلال بصری تعیین کرده است. همچنین در نوشتن فنی و پیروی از دستورالعمل‌ها مهارت دارد. کاربران می‌توانند از آن برای تحلیل متن، کد و تصویر و حل مسائل پیچیده چند مرحله‌ای استفاده کنند."}, "openai/o3-mini": {"description": "o3-mini هوش بالایی را در همان هزینه و هدف تأخیر o1-mini ارائه می‌دهد."}, "openai/o3-mini-high": {"description": "نسخه o3-mini با سطح استدلال بالا، هوش بالایی را در همان هزینه و هدف تأخیر o1-mini ارائه می‌دهد."}, "openai/o4-mini": {"description": "o4-mini به‌طور خاص برای استدلال سریع و مؤثر بهینه‌سازی شده و در وظایف کدنویسی و بصری عملکرد بسیار بالایی دارد."}, "openai/o4-mini-high": {"description": "نسخه با سطح استدلال بالا o4-mini، که به‌طور خاص برای استدلال سریع و مؤثر بهینه‌سازی شده و در وظایف کدنویسی و بصری عملکرد بسیار بالایی دارد."}, "openrouter/auto": {"description": "با توجه به طول متن، موضوع و پیچیدگی، درخواست شما به Llama 3 70B Instruct، Claude 3.5 Sonnet (تنظیم خودکار) یا GPT-4o ارسال خواهد شد."}, "phi3": {"description": "Phi-3 یک مدل سبک و باز از مایکروسافت است که برای یکپارچه‌سازی کارآمد و استدلال دانش در مقیاس بزرگ مناسب است."}, "phi3:14b": {"description": "Phi-3 یک مدل سبک و باز از مایکروسافت است که برای یکپارچه‌سازی کارآمد و استدلال دانش در مقیاس بزرگ طراحی شده است."}, "pixtral-12b-2409": {"description": "مدل Pixtral در وظایفی مانند نمودار و درک تصویر، پرسش و پاسخ اسناد، استدلال چندوجهی و پیروی از دستورات، توانایی‌های قدرتمندی از خود نشان می‌دهد. این مدل قادر است تصاویر را با وضوح طبیعی و نسبت ابعاد دریافت کند و همچنین می‌تواند هر تعداد تصویری را در یک پنجره متنی طولانی تا ۱۲۸ هزار توکن پردازش کند."}, "pixtral-large-latest": {"description": "Pixtral Large یک مدل چندرسانه‌ای متن‌باز با ۱۲۴۰ میلیارد پارامتر است که بر اساس Mistral Large 2 ساخته شده است. این دومین مدل در خانواده چندرسانه‌ای ماست که توانایی‌های پیشرفته‌ای در درک تصویر را به نمایش می‌گذارد."}, "pro-128k": {"description": "Spark Pro 128K با قابلیت پردازش متن بسیار بزرگ، قادر به پردازش تا 128K اطلاعات متنی است. این ویژگی به‌ویژه برای تحلیل کامل و پردازش ارتباطات منطقی طولانی‌مدت در محتوای متنی طولانی مناسب است و می‌تواند در ارتباطات متنی پیچیده، پشتیبانی از منطق روان و یکپارچه و ارجاعات متنوع را فراهم کند."}, "qvq-72b-preview": {"description": "مدل QVQ یک مدل تحقیقاتی تجربی است که توسط تیم Qwen توسعه یافته و بر بهبود توانایی استدلال بصری، به‌ویژه در زمینه استدلال ریاضی تمرکز دارد."}, "qvq-max": {"description": "مدل استدلال بینایی QVQ Tongyi Qianwen که از ورودی‌های بینایی و خروجی زنجیره فکری پشتیبانی می‌کند و در ریاضیات، برنامه‌نویسی، تحلیل بینایی، خلاقیت و وظایف عمومی توانایی‌های قوی‌تری نشان می‌دهد."}, "qvq-plus": {"description": "مدل استدلال بصری. پشتیبانی از ورودی‌های بصری و خروجی زنجیره تفکر، نسخه پلاس پس از مدل qvq-max، که نسبت به مدل qvq-max سرعت استدلال بالاتر و تعادل بهتری بین عملکرد و هزینه دارد."}, "qwen-coder-plus": {"description": "مد<PERSON> کد نویسی <PERSON>."}, "qwen-coder-turbo": {"description": "مد<PERSON> کد نویسی <PERSON>."}, "qwen-coder-turbo-latest": {"description": "مدل کدنویسی تونگی چیان‌ون."}, "qwen-long": {"description": "مدل زبانی بسیار بزرگ Tongyi Qianwen که از متن‌های طولانی و همچنین قابلیت مکالمه در چندین سناریو مانند اسناد طولانی و چندین سند پشتیبانی می‌کند."}, "qwen-math-plus": {"description": "مدل ریاضی Tongyi Qianwen که به طور خاص برای حل مسائل ریاضی طراحی شده است."}, "qwen-math-plus-latest": {"description": "مدل ریاضی Qwen یک مدل زبانی است که به طور خاص برای حل مسائل ریاضی طراحی شده است."}, "qwen-math-turbo": {"description": "مدل ریاضی Tongyi Qianwen که به طور خاص برای حل مسائل ریاضی طراحی شده است."}, "qwen-math-turbo-latest": {"description": "مدل ریاضی Qwen Math Turbo یک مدل زبانی است که به طور خاص برای حل مسائل ریاضی طراحی شده است."}, "qwen-max": {"description": "مدل زبان بسیار بزرگ و با ظرفیت Qwen با توانایی پشتیبانی از ورودی زبان‌های مختلف مانند چینی و انگلیسی، در حال حاضر مدل API پشت نسخه محصول Qwen 2.5 است."}, "qwen-omni-turbo": {"description": "مدل‌های سری Qwen-Omni از ورودی‌های چندرسانه‌ای مختلف از جمله ویدئو، صدا، تصویر و متن پشتیبانی می‌کنند و خروجی صوتی و متنی ارائه می‌دهند."}, "qwen-plus": {"description": "مدل زبان بسیار بزرگ Qwen در نسخه تقویت شده، از ورودی زبان‌های مختلف مانند چینی و انگلیسی پشتیبانی می‌کند."}, "qwen-turbo": {"description": "مدل زبان بسیار بزرگ Qwen، از ورودی زبان‌های مختلف مانند چینی و انگلیسی پشتیبانی می‌کند."}, "qwen-vl-chat-v1": {"description": "مدل Qwen-VL از روش‌های تعاملی انعطاف‌پذیر پشتیبانی می‌کند، از جمله قابلیت‌های چندتصویری، پرسش و پاسخ چندمرحله‌ای و خلاقیت."}, "qwen-vl-max": {"description": "مدل بزرگ زبان-بینایی فوق‌العاده بزرگ Tongyi Qianwen. نسبت به نسخه تقویت‌شده، توانایی استدلال بینایی و پیروی از دستورات را مجدداً ارتقاء داده و سطح بالاتری از ادراک و شناخت بینایی را ارائه می‌دهد."}, "qwen-vl-max-latest": {"description": "مدل زبان بصری فوق‌العاده بزرگ Qwen-VL. در مقایسه با نسخه تقویت‌شده، توانایی استدلال بصری و پیروی از دستورات را دوباره بهبود می‌بخشد و سطح بالاتری از ادراک و شناخت بصری را ارائه می‌دهد."}, "qwen-vl-ocr": {"description": "مدل اختصاصی استخراج متن Tongyi Qianwen OCR که بر استخراج متن از تصاویر اسناد، جداول، سوالات و دست‌نوشته‌ها تمرکز دارد. این مدل قادر به شناسایی چندین زبان است که شامل چینی، انگلیسی، فرانسوی، ژاپنی، کره‌ای، آلمانی، روسی، ایتالیایی، ویتنامی و عربی می‌باشد."}, "qwen-vl-plus": {"description": "نسخه تقویت‌شده مدل بزرگ زبان-بینایی Tongyi Qianwen. توانایی شناسایی جزئیات و تشخیص متن را به طور چشمگیری افزایش داده و از تصاویر با رزولوشن بیش از یک میلیون پیکسل و نسبت ابعاد دلخواه پشتیبانی می‌کند."}, "qwen-vl-plus-latest": {"description": "نسخه تقویت‌شده مدل زبان تصویری بزرگ تونگی چیان‌ون. بهبود قابل توجه در توانایی تشخیص جزئیات و شناسایی متن، پشتیبانی از وضوح بیش از یک میلیون پیکسل و تصاویر با هر نسبت طول به عرض."}, "qwen-vl-v1": {"description": "مدل زبان Qwen-7B با اضافه کردن مدل تصویر و وضوح ورودی تصویر 448، به عنوان یک مدل پیش‌آموزش‌شده، اولیه‌سازی شده است."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 یک سری جدید از مدل‌های زبان بزرگ Qwen است. Qwen2 7B یک مدل مبتنی بر ترنسفورمر است که در درک زبان، قابلیت‌های چند زبانه، برنامه‌نویسی، ریاضی و استدلال عملکرد عالی دارد."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 یک سری جدید از مدل‌های زبان بزرگ است که دارای توانایی‌های درک و تولید قوی‌تری می‌باشد."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL جدیدترین نسخه از مدل Qwen-VL است که در آزمون‌های معیار درک بصری به عملکرد پیشرفته‌ای دست یافته است، از جمله MathVista، DocVQA، RealWorldQA و MTVQA. Qwen2-VL قادر به درک ویدیوهای بیش از 20 دقیقه است و برای پرسش و پاسخ، گفتگو و تولید محتوا مبتنی بر ویدیو با کیفیت بالا استفاده می‌شود. این مدل همچنین دارای قابلیت‌های پیچیده استدلال و تصمیم‌گیری است و می‌تواند با دستگاه‌های موبایل، ربات‌ها و غیره ادغام شود و بر اساس محیط بصری و دستورات متنی به طور خودکار عمل کند. علاوه بر انگلیسی و چینی، Qwen2-VL اکنون از درک متن‌های مختلف زبان در تصاویر نیز پشتیبانی می‌کند، از جمله بیشتر زبان‌های اروپایی، ژاپنی، کره‌ای، عربی و ویتنامی."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct یکی از جدیدترین سری مدل‌های زبان بزرگ منتشر شده توسط Alibaba Cloud است. این مدل 72B در زمینه‌های کدنویسی و ریاضی دارای قابلیت‌های بهبود یافته قابل توجهی است. این مدل همچنین از چندین زبان پشتیبانی می‌کند و بیش از 29 زبان از جمله چینی و انگلیسی را پوشش می‌دهد. این مدل در پیروی از دستورات، درک داده‌های ساختاری و تولید خروجی‌های ساختاری (به ویژه JSON) بهبودهای قابل توجهی داشته است."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct یکی از جدیدترین سری مدل‌های زبان بزرگ منتشر شده توسط Alibaba Cloud است. این مدل 32B در زمینه‌های کدنویسی و ریاضی دارای قابلیت‌های بهبود یافته قابل توجهی است. این مدل از چندین زبان پشتیبانی می‌کند و بیش از 29 زبان از جمله چینی و انگلیسی را پوشش می‌دهد. این مدل در پیروی از دستورات، درک داده‌های ساختاری و تولید خروجی‌های ساختاری (به ویژه JSON) بهبودهای قابل توجهی داشته است."}, "qwen/qwen2.5-7b-instruct": {"description": "مدل LLM برای زبان‌های چینی و انگلیسی که در زمینه‌های زبان، برنامه‌نویسی، ریاضیات و استدلال تخصص دارد."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "مدل LLM پیشرفته که از تولید کد، استدلال و اصلاح پشتیبانی می‌کند و شامل زبان‌های برنامه‌نویسی اصلی است."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "مدل کد قدرتمند و متوسط که از طول زمینه 32K پشتیبانی می‌کند و در برنامه‌نویسی چند زبانه مهارت دارد."}, "qwen/qwen3-14b": {"description": "Qwen3-14B یک مدل زبان علّی با ۱۴.۸ میلیارد پارامتر در سری Qwen3 است که به طور خاص برای استدلال پیچیده و مکالمات کارآمد طراحی شده است. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای وظایف ریاضی، برنامه‌نویسی و استدلال منطقی و حالت «غیرتفکری» برای مکالمات عمومی پشتیبانی می‌کند. این مدل به طور خاص برای پیروی از دستورات، استفاده از ابزارهای نمایندگی، نوشتن خلاق و انجام وظایف چند زبانه در بیش از ۱۰۰ زبان و گویش مختلف تنظیم شده است. این مدل به طور بومی از ۳۲K توکن زمینه پشتیبانی می‌کند و می‌تواند با استفاده از گسترش مبتنی بر YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B یک مدل زبان علّی با ۱۴.۸ میلیارد پارامتر در سری Qwen3 است که به طور خاص برای استدلال پیچیده و مکالمات کارآمد طراحی شده است. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای وظایف ریاضی، برنامه‌نویسی و استدلال منطقی و حالت «غیرتفکری» برای مکالمات عمومی پشتیبانی می‌کند. این مدل به طور خاص برای پیروی از دستورات، استفاده از ابزارهای نمایندگی، نوشتن خلاق و انجام وظایف چند زبانه در بیش از ۱۰۰ زبان و گویش مختلف تنظیم شده است. این مدل به طور بومی از ۳۲K توکن زمینه پشتیبانی می‌کند و می‌تواند با استفاده از گسترش مبتنی بر YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B یک مدل متخصص ترکیبی (MoE) با ۲۳۵B پارامتر است که توسط Qwen توسعه یافته و در هر بار انتقال رو به جلو ۲۲B پارامتر فعال می‌شود. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای استدلال پیچیده، ریاضیات و وظایف کدنویسی و حالت «غیرتفکری» برای کارایی مکالمات عمومی پشتیبانی می‌کند. این مدل توانایی‌های استدلال قوی، پشتیبانی چند زبانه (بیش از ۱۰۰ زبان و گویش)، پیروی از دستورات پیشرفته و توانایی فراخوانی ابزارهای نمایندگی را نشان می‌دهد. این مدل به طور بومی از پنجره زمینه ۳۲K توکن پشتیبانی می‌کند و می‌تواند با استفاده از گسترش مبتنی بر YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B یک مدل متخصص ترکیبی (MoE) با ۲۳۵B پارامتر است که توسط Qwen توسعه یافته و در هر بار انتقال رو به جلو ۲۲B پارامتر فعال می‌شود. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای استدلال پیچیده، ریاضیات و وظایف کدنویسی و حالت «غیرتفکری» برای کارایی مکالمات عمومی پشتیبانی می‌کند. این مدل توانایی‌های استدلال قوی، پشتیبانی چند زبانه (بیش از ۱۰۰ زبان و گویش)، پیروی از دستورات پیشرفته و توانایی فراخوانی ابزارهای نمایندگی را نشان می‌دهد. این مدل به طور بومی از پنجره زمینه ۳۲K توکن پشتیبانی می‌کند و می‌تواند با استفاده از گسترش مبتنی بر YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 نسل جدیدی از سری مدل‌های زبان بزرگ Qwen است که دارای معماری ترکیبی فشرده و متخصص (MoE) می‌باشد و در زمینه استدلال، پشتیبانی چند زبانه و وظایف پیشرفته نمایشی عالی دارد. توانایی منحصر به فرد آن در جابجایی بی‌وقفه بین حالت‌های تفکر برای استدلال پیچیده و حالت‌های غیرتفکری برای مکالمات کارآمد، عملکرد چندمنظوره و با کیفیت بالا را تضمین می‌کند.\n\nQwen3 به طور قابل توجهی از مدل‌های قبلی مانند QwQ و Qwen2.5 برتر است و توانایی‌های فوق‌العاده‌ای در ریاضیات، کدنویسی، استدلال عمومی، نوشتن خلاق و مکالمات تعاملی ارائه می‌دهد. واریانت Qwen3-30B-A3B شامل ۳۰.۵ میلیارد پارامتر (۳.۳ میلیارد پارامتر فعال)، ۴۸ لایه، ۱۲۸ متخصص (که هر کدام ۸ مورد را فعال می‌کنند) است و از زمینه ۱۳۱K توکن پشتیبانی می‌کند (با استفاده از YaRN) و استاندارد جدیدی برای مدل‌های متن‌باز تعیین می‌کند."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 نسل جدیدی از سری مدل‌های زبان بزرگ Qwen است که دارای معماری ترکیبی فشرده و متخصص (MoE) می‌باشد و در زمینه استدلال، پشتیبانی چند زبانه و وظایف پیشرفته نمایشی عالی دارد. توانایی منحصر به فرد آن در جابجایی بی‌وقفه بین حالت‌های تفکر برای استدلال پیچیده و حالت‌های غیرتفکری برای مکالمات کارآمد، عملکرد چندمنظوره و با کیفیت بالا را تضمین می‌کند.\n\nQwen3 به طور قابل توجهی از مدل‌های قبلی مانند QwQ و Qwen2.5 برتر است و توانایی‌های فوق‌العاده‌ای در ریاضیات، کدنویسی، استدلال عمومی، نوشتن خلاق و مکالمات تعاملی ارائه می‌دهد. واریانت Qwen3-30B-A3B شامل ۳۰.۵ میلیارد پارامتر (۳.۳ میلیارد پارامتر فعال)، ۴۸ لایه، ۱۲۸ متخصص (که هر کدام ۸ مورد را فعال می‌کنند) است و از زمینه ۱۳۱K توکن پشتیبانی می‌کند (با استفاده از YaRN) و استاندارد جدیدی برای مدل‌های متن‌باز تعیین می‌کند."}, "qwen/qwen3-32b": {"description": "Qwen3-32B یک مدل زبان علّی با ۳۲.۸ میلیارد پارامتر در سری Qwen3 است که به طور خاص برای استدلال پیچیده و مکالمات کارآمد بهینه‌سازی شده است. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای وظایف ریاضی، کدنویسی و استدلال منطقی و حالت «غیرتفکری» برای مکالمات سریع و عمومی پشتیبانی می‌کند. این مدل در پیروی از دستورات، استفاده از ابزارهای نمایندگی، نوشتن خلاق و انجام وظایف چند زبانه در بیش از ۱۰۰ زبان و گویش مختلف عملکرد قوی دارد. این مدل به طور بومی از ۳۲K توکن زمینه پشتیبانی می‌کند و می‌تواند با استفاده از گسترش مبتنی بر YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B یک مدل زبان علّی با ۳۲.۸ میلیارد پارامتر در سری Qwen3 است که به طور خاص برای استدلال پیچیده و مکالمات کارآمد بهینه‌سازی شده است. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای وظایف ریاضی، کدنویسی و استدلال منطقی و حالت «غیرتفکری» برای مکالمات سریع و عمومی پشتیبانی می‌کند. این مدل در پیروی از دستورات، استفاده از ابزارهای نمایندگی، نوشتن خلاق و انجام وظایف چند زبانه در بیش از ۱۰۰ زبان و گویش مختلف عملکرد قوی دارد. این مدل به طور بومی از ۳۲K توکن زمینه پشتیبانی می‌کند و می‌تواند با استفاده از گسترش مبتنی بر YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B یک مدل زبان علّی با ۸.۲ میلیارد پارامتر در سری Qwen3 است که به طور خاص برای وظایف استدلال فشرده و مکالمات کارآمد طراحی شده است. این مدل از جابجایی بی‌وقفه بین حالت «تفکر» برای ریاضیات، کدنویسی و استدلال منطقی و حالت «غیرتفکری» برای مکالمات عمومی پشتیبانی می‌کند. این مدل به طور خاص برای پیروی از دستورات، ادغام نمایندگی، نوشتن خلاق و استفاده چند زبانه در بیش از ۱۰۰ زبان و گویش مختلف تنظیم شده است. این مدل به طور بومی از پنجره زمینه ۳۲K توکن پشتیبانی می‌کند و می‌تواند از طریق YaRN به ۱۳۱K توکن گسترش یابد."}, "qwen2": {"description": "Qwen2 مدل زبان بزرگ نسل جدید علی‌بابا است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2-72b-instruct": {"description": "Qwen2، سری جدیدی از مدل‌های زبانی بزرگ توسط تیم Qwen ارائه شده است. این مدل بر اساس معماری Transformer ساخته شده و از توابع فعال‌سازی SwiGLU، بایاس QKV توجه (attention QKV bias)، توجه سؤال گروهی (group query attention)، ترکیب توجه پنجره‌ای لغزشی و توجه کامل (mixture of sliding window attention and full attention) استفاده می‌کند. علاوه بر این، تیم Qwen بهبودی در تجزیه‌کننده‌هایی که برای تجزیه متن‌های طبیعی و کد مناسب هستند ایجاد کرده‌اند."}, "qwen2-7b-instruct": {"description": "Qwen2، سری جدیدی از مدل‌های زبانی بزرگ توسط تیم Qwen ارائه شده است. این مدل بر اساس معماری Transformer ساخته شده و از توابع فعال‌سازی SwiGLU، بایاس QKV توجه (attention QKV bias)، توجه سرویس‌گروهی (group query attention)، ترکیب توجه پنجره‌ای لغزشی و توجه کامل (mixture of sliding window attention and full attention) استفاده می‌کند. علاوه بر این، تیم Qwen بهبودی در تجزیه‌کننده‌هایی ارائه کرده‌اند که برای تجزیه متن‌های طبیعی و کد مناسب هستند."}, "qwen2.5": {"description": "Qwen2.5 نسل جدید مدل زبانی مقیاس بزرگ Alibaba است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2.5-14b-instruct": {"description": "مدل 14B مق<PERSON><PERSON><PERSON> Qwen 2.5 که به صورت منبع باز ارائه شده است."}, "qwen2.5-14b-instruct-1m": {"description": "مدل 72B مقیاس Qwen2.5 که به صورت متن‌باز ارائه شده است."}, "qwen2.5-32b-instruct": {"description": "مدل 32B مقی<PERSON><PERSON> Qwen 2.5 که به صورت منبع باز ارائه شده است."}, "qwen2.5-72b-instruct": {"description": "مدل 72B مقیاس بازمتن Qwen 2.5 برای استفاده عمومی."}, "qwen2.5-7b-instruct": {"description": "مدل 7B متن‌<PERSON><PERSON><PERSON>wen 2.5 برای استفاده عمومی."}, "qwen2.5-coder-1.5b-instruct": {"description": "نسخه متن‌باز مدل کد <PERSON>."}, "qwen2.5-coder-14b-instruct": {"description": "نسخه متن‌باز مدل کد نویسی <PERSON>."}, "qwen2.5-coder-32b-instruct": {"description": "نسخه متن باز مدل کد <PERSON>."}, "qwen2.5-coder-7b-instruct": {"description": "نسخه متن‌باز مدل کدنویسی تونگی چیان‌ون."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder جدیدترین مدل زبانی بزرگ مخصوص کد نویسی از سری Qwen (که قبلاً با نام CodeQwen شناخته می‌شد) است."}, "qwen2.5-instruct": {"description": "Qwen2.5 جدیدترین سری مدل‌های زبانی بزرگ Qwen است. برای Qwen2.5، ما چندین مدل زبانی پایه و مدل‌های زبانی با تنظیم دستورالعمل‌های میکرو منتشر کرده‌ایم که تعداد پارامتر آن‌ها از 500 میلیون تا 7.2 میلیارد متفاوت است."}, "qwen2.5-math-1.5b-instruct": {"description": "مد<PERSON> Qwen-Math دارای قابلیت‌های قوی حل مسئله ریاضی است."}, "qwen2.5-math-72b-instruct": {"description": "مد<PERSON>wen-Math دارای توانایی قوی در حل مسائل ریاضی است."}, "qwen2.5-math-7b-instruct": {"description": "مد<PERSON>wen-Math دارای توانایی قوی در حل مسائل ریاضی است."}, "qwen2.5-omni-7b": {"description": "مدل‌های سری Qwen-Omni از ورودی‌های چندگانه شامل ویدیو، صدا، تصویر و متن پشتیبانی می‌کنند و خروجی‌هایی به صورت صدا و متن ارائه می‌دهند."}, "qwen2.5-vl-32b-instruct": {"description": "سری مدل‌های Qwen2.5-VL سطح هوش، کاربردی بودن و مناسب بودن مدل را افزایش داده است تا عملکرد بهتری در مکالمات طبیعی، خلق محتوا، ارائه خدمات دانش تخصصی و توسعه کد ارائه دهد. نسخه 32B با استفاده از تکنیک‌های یادگیری تقویتی مدل را بهینه کرده است و نسبت به سایر مدل‌های سری Qwen2.5 VL، سبک خروجی مطابق با ترجیحات انسانی، توانایی استدلال در مسائل ریاضی پیچیده و درک و استدلال دقیق تصاویر را فراهم می‌کند."}, "qwen2.5-vl-72b-instruct": {"description": "پیروی از دستورات، ریاضیات، حل مسائل، بهبود کلی کد، بهبود توانایی شناسایی همه چیز، پشتیبانی از فرمت‌های مختلف برای شناسایی دقیق عناصر بصری، پشتیبانی از درک فایل‌های ویدیویی طولانی (حداکثر 10 دقیقه) و شناسایی لحظات رویداد در سطح ثانیه، توانایی درک زمان و سرعت، بر اساس توانایی تجزیه و تحلیل و شناسایی، پشتیبانی از کنترل عامل‌های OS یا Mobile، توانایی استخراج اطلاعات کلیدی و خروجی به فرمت Json قوی، این نسخه 72B است و قوی‌ترین نسخه در این سری است."}, "qwen2.5-vl-7b-instruct": {"description": "پیروی از دستورات، ریاضیات، حل مسائل، بهبود کلی کد، بهبود توانایی شناسایی همه چیز، پشتیبانی از فرمت‌های مختلف برای شناسایی دقیق عناصر بصری، پشتیبانی از درک فایل‌های ویدیویی طولانی (حداکثر 10 دقیقه) و شناسایی لحظات رویداد در سطح ثانیه، توانایی درک زمان و سرعت، بر اساس توانایی تجزیه و تحلیل و شناسایی، پشتیبانی از کنترل عامل‌های OS یا Mobile، توانایی استخراج اطلاعات کلیدی و خروجی به فرمت Json قوی، این نسخه 72B است و قوی‌ترین نسخه در این سری است."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL نسخه جدید مدل زبانی و بصری از خانواده مدل‌های Qwen است."}, "qwen2.5:0.5b": {"description": "Qwen2.5 نسل جدید مدل زبانی مقیاس بزرگ Alibaba است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2.5:1.5b": {"description": "Qwen2.5 نسل جدید مدل زبانی مقیاس بزرگ Alibaba است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2.5:72b": {"description": "Qwen2.5 نسل جدید مدل زبانی مقیاس بزرگ Alibaba است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2:0.5b": {"description": "Qwen2 مدل زبان بزرگ نسل جدید علی‌بابا است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2:1.5b": {"description": "Qwen2 مدل زبان بزرگ نسل جدید علی‌بابا است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen2:72b": {"description": "Qwen2 مدل زبان بزرگ نسل جدید علی‌بابا است که با عملکرد عالی از نیازهای متنوع کاربردی پشتیبانی می‌کند."}, "qwen3": {"description": "Qwen3 مدل زبان نسل جدید علی‌بابا است که با عملکرد عالی، نیازهای متنوع کاربردی را پشتیبانی می‌کند."}, "qwen3-0.6b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-1.7b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-14b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-235b-a22b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-235b-a22b-instruct-2507": {"description": "مدل متن‌باز حالت غیرتفکری مبتنی بر Qwen3 که نسبت به نسخه قبلی (Tongyi Qianwen 3-235B-A22B) در توانایی خلاقیت ذهنی و ایمنی مدل بهبودهای جزئی داشته است."}, "qwen3-235b-a22b-thinking-2507": {"description": "مدل متن‌باز حالت تفکری مبتنی بر Qwen3 که نسبت به نسخه قبلی (Tongyi Qianwen 3-235B-A22B) در توانایی‌های منطقی، عمومی، تقویت دانش و خلاقیت بهبودهای قابل توجهی داشته و برای سناریوهای استدلال پیچیده و دشوار مناسب است."}, "qwen3-30b-a3b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-30b-a3b-instruct-2507": {"description": "در مقایسه با نسخه قبلی (Qwen3-30B-A3B)، توانایی‌های کلی چندزبانه و انگلیسی به طور قابل توجهی بهبود یافته است. بهینه‌سازی ویژه برای وظایف ذهنی و باز، که به طور قابل توجهی با ترجیحات کاربران هماهنگ‌تر است و پاسخ‌های مفیدتری ارائه می‌دهد."}, "qwen3-30b-a3b-thinking-2507": {"description": "مدل متن‌باز حالت تفکر مبتنی بر Qwen3، که نسبت به نسخه قبلی (Tongyi Qianwen 3-30B-A3B) بهبودهای قابل توجهی در توانایی‌های منطقی، عمومی، دانش و خلاقیت دارد و برای سناریوهای دشوار و استدلال قوی مناسب است."}, "qwen3-32b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-4b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-8b": {"description": "Qwen3 یک مدل جدید نسل جدید با توانایی‌های به طور قابل توجهی بهبود یافته است که در استدلال، عمومی، نمایندگی و چند زبانه در چندین توانایی کلیدی به سطح پیشرفته صنعت دست یافته و از جابجایی حالت تفکر پشتیبانی می‌کند."}, "qwen3-coder-480b-a35b-instruct": {"description": "نسخه متن‌باز مدل کدنویسی Tongyi Qianwen. جدیدترین مدل qwen3-coder-480b-a35b-instruct مبتنی بر Qwen3 است و دارای توانایی‌های قوی عامل کدنویسی، مهارت در فراخوانی ابزارها و تعامل با محیط است و قادر به برنامه‌نویسی خودکار با توانایی کدنویسی برجسته و همچنین توانایی‌های عمومی است."}, "qwen3-coder-plus": {"description": "مدل کدنویسی Tongyi Qianwen. جدیدترین سری مدل‌های Qwen3-Coder-Plus مبتنی بر Qwen3 است و دارای توانایی‌های قوی عامل کدنویسی، مهارت در فراخوانی ابزارها و تعامل با محیط است و قادر به برنامه‌نویسی خودکار با توانایی کدنویسی برجسته و همچنین توانایی‌های عمومی است."}, "qwq": {"description": "QwQ یک مدل تحقیقاتی تجربی است که بر بهبود توانایی استدلال AI تمرکز دارد."}, "qwq-32b": {"description": "مدل استنتاج QwQ مبتنی بر مدل Qwen2.5-32B است که از طریق یادگیری تقویتی به طور قابل توجهی توانایی استنتاج مدل را افزایش داده است. شاخص‌های اصلی مدل مانند کد ریاضی (AIME 24/25، LiveCodeBench) و برخی از شاخص‌های عمومی (IFEval، LiveBench و غیره) به سطح DeepSeek-R1 نسخه کامل رسیده‌اند و تمامی شاخص‌ها به طور قابل توجهی از DeepSeek-R1-Distill-Qwen-32B که نیز مبتنی بر Qwen2.5-32B است، پیشی گرفته‌اند."}, "qwq-32b-preview": {"description": "مدل QwQ یک مدل تحقیقاتی تجربی است که توسط تیم Qwen توسعه یافته و بر تقویت توانایی استدلال AI تمرکز دارد."}, "qwq-plus": {"description": "مدل استدلال QwQ مبتنی بر مدل Qwen2.5 است که با یادگیری تقویتی توانایی استدلال مدل را به طور قابل توجهی افزایش داده است. شاخص‌های اصلی مدل در ریاضیات و کد نویسی (AIME 24/25، LiveCodeBench) و برخی شاخص‌های عمومی (IFEval، LiveBench و غیره) به سطح نسخه کامل DeepSeek-R1 رسیده‌اند."}, "qwq_32b": {"description": "مدل استدلالی با اندازه متوسط از سری Qwen. نسبت به مدل‌های معمولی تنظیم‌شده بر اساس دستورات، QwQ که دارای توانایی‌های تفکر و استدلال است، در وظایف پایین‌دستی، به‌ویژه در حل مسائل دشوار، می‌تواند عملکرد را به‌طور قابل توجهی افزایش دهد."}, "r1-1776": {"description": "R1-1776 نسخه‌ای از مدل DeepSeek R1 است که پس از آموزش مجدد، اطلاعات واقعی بدون سانسور و بدون تعصب را ارائه می‌دهد."}, "solar-mini": {"description": "Solar Mini یک LLM فشرده است که عملکردی بهتر از GPT-3.5 دارد و دارای توانایی‌های چند زبانه قوی است و از انگلیسی و کره‌ای پشتیبانی می‌کند و راه‌حل‌های کارآمد و کوچکی را ارائه می‌دهد."}, "solar-mini-ja": {"description": "Solar Mini (Ja) توانایی‌های Solar Mini را گسترش می‌دهد و بر روی زبان ژاپنی تمرکز دارد و در استفاده از انگلیسی و کره‌ای نیز کارایی و عملکرد عالی را حفظ می‌کند."}, "solar-pro": {"description": "Solar Pro یک مدل هوش مصنوعی پیشرفته از Upstage است که بر توانایی پیروی از دستورات با استفاده از یک GPU تمرکز دارد و امتیاز IFEval بالای 80 را کسب کرده است. در حال حاضر از زبان انگلیسی پشتیبانی می‌کند و نسخه رسمی آن برای نوامبر 2024 برنامه‌ریزی شده است که پشتیبانی از زبان‌های بیشتر و طول زمینه را گسترش خواهد داد."}, "sonar": {"description": "محصول جستجوی سبک بر اساس زمینه جستجو که سریع‌تر و ارزان‌تر از Sonar Pro است."}, "sonar-deep-research": {"description": "تحقیق عمیق، تحقیقاتی جامع و تخصصی را انجام می‌دهد و آن را به گزارش‌های قابل دسترسی و قابل استفاده تبدیل می‌کند."}, "sonar-pro": {"description": "محصول جستجوی پیشرفته که از جستجوی زمینه پشتیبانی می‌کند و قابلیت‌های پیشرفته‌ای برای پرسش و پیگیری دارد."}, "sonar-reasoning": {"description": "محصول جدید API که توسط مدل استدلال DeepSeek پشتیبانی می‌شود."}, "sonar-reasoning-pro": {"description": "محصول جدید API که توسط مدل استدلال DeepSeek پشتیبانی می‌شود."}, "stable-diffusion-3-medium": {"description": "جدیدترین مدل بزرگ تولید تصویر از متن که توسط Stability AI ارائه شده است. این نسخه با حفظ مزایای نسل‌های قبلی، بهبودهای قابل توجهی در کیفیت تصویر، درک متن و تنوع سبک‌ها دارد و قادر است دستورات پیچیده زبان طبیعی را دقیق‌تر تفسیر کرده و تصاویر دقیق‌تر و متنوع‌تری تولید کند."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large یک مدل مولد تصویر از متن مبتنی بر ترنسفورمر انتشار چندرسانه‌ای (MMDiT) با 800 میلیون پارامتر است که کیفیت تصویر عالی و تطابق بالا با دستورات متنی دارد، قادر به تولید تصاویر با وضوح بالا تا 1 میلیون پیکسل است و می‌تواند به طور کارآمد روی سخت‌افزارهای مصرفی معمول اجرا شود."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo مدلی است که بر پایه stable-diffusion-3.5-large ساخته شده و با استفاده از تکنولوژی تقطیر انتشار متخاصم (ADD) سرعت بالاتری دارد."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 با وزن‌های نقطه بررسی stable-diffusion-v1.2 آغاز شده و با 595 هزار مرحله تنظیم دقیق روی مجموعه \"laion-aesthetics v2 5+\" با وضوح 512x512 انجام شده است. این مدل 10٪ کاهش شرط‌بندی متنی دارد تا نمونه‌برداری هدایت‌شده بدون طبقه‌بندی‌کننده را بهبود بخشد."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl نسبت به نسخه v1.5 بهبودهای قابل توجهی داشته و با مدل‌های متن‌باز پیشرفته مانند midjourney قابل مقایسه است. بهبودها شامل: شبکه اصلی unet بزرگ‌تر که سه برابر نسخه قبلی است؛ افزودن ماژول پالایش برای بهبود کیفیت تصاویر تولید شده؛ و تکنیک‌های آموزش بهینه‌تر."}, "stable-diffusion-xl-base-1.0": {"description": "مدل بزرگ تولید تصویر از متن که توسط Stability AI توسعه یافته و متن‌باز است و در تولید تصاویر خلاقانه در صنعت پیشرو است. دارای توانایی درک دقیق دستورات و پشتیبانی از تعریف معکوس Prompt برای تولید دقیق محتوا است."}, "step-1-128k": {"description": "تعادل بین عملکرد و هزینه، مناسب برای سناریوهای عمومی."}, "step-1-256k": {"description": "دارای توانایی پردازش متن طولانی، به‌ویژه مناسب برای تحلیل اسناد بلند."}, "step-1-32k": {"description": "پشتیبانی از مکالمات با طول متوسط، مناسب برای انواع مختلف کاربردها."}, "step-1-8k": {"description": "مدل کوچک، مناسب برای وظایف سبک."}, "step-1-flash": {"description": "مدل پرسرعت، مناسب برای مکالمات در لحظه."}, "step-1.5v-mini": {"description": "این مدل دارای توانایی‌های قوی در درک ویدیو است."}, "step-1o-turbo-vision": {"description": "این مدل دارای توانایی‌های قوی در درک تصویر است و در زمینه‌های ریاضی و کدنویسی از 1o قوی‌تر است. این مدل کوچکتر از 1o است و سرعت خروجی بیشتری دارد."}, "step-1o-vision-32k": {"description": "این مدل دارای توانایی‌های قوی در درک تصویر است. در مقایسه با مدل‌های سری step-1v، عملکرد بصری بهتری دارد."}, "step-1v-32k": {"description": "پشتیبانی از ورودی بصری، تقویت تجربه تعامل چندحالته."}, "step-1v-8k": {"description": "مدل بصری کوچک، مناسب برای وظایف پایه‌ای تصویر و متن."}, "step-1x-edit": {"description": "این مدل بر وظایف ویرایش تصویر تمرکز دارد و قادر است بر اساس تصویر و توصیف متنی ارائه شده توسط کاربر، تصویر را اصلاح و بهبود بخشد. از فرمت‌های ورودی مختلف از جمله توصیف متنی و تصاویر نمونه پشتیبانی می‌کند. مدل قادر به درک نیت کاربر و تولید نتایج ویرایش تصویر مطابق با خواسته‌ها است."}, "step-1x-medium": {"description": "این مدل دارای توانایی قوی در تولید تصویر است و از توصیف متنی به عنوان ورودی پشتیبانی می‌کند. پشتیبانی بومی از زبان چینی دارد و می‌تواند توصیف‌های متنی چینی را بهتر درک و پردازش کند و معنای دقیق‌تر را به ویژگی‌های تصویری تبدیل کند تا تولید تصویر دقیق‌تری داشته باشد. مدل قادر است تصاویر با وضوح و کیفیت بالا تولید کند و توانایی انتقال سبک نیز دارد."}, "step-2-16k": {"description": "پشتیبانی از تعاملات متنی گسترده، مناسب برای سناریوهای مکالمه پیچیده."}, "step-2-16k-exp": {"description": "نسخه آزمایشی مدل step-2 که شامل جدیدترین ویژگی‌ها است و به‌طور مداوم به‌روزرسانی می‌شود. استفاده در محیط‌های تولیدی رسمی توصیه نمی‌شود."}, "step-2-mini": {"description": "مدل بزرگ فوق‌العاده سریع مبتنی بر معماری توجه MFA که به‌طور خودجوش توسعه یافته است، با هزینه بسیار کم به نتایجی مشابه با مرحله ۱ دست می‌یابد و در عین حال توانایی پردازش بالاتر و زمان پاسخ سریع‌تری را حفظ می‌کند. این مدل قادر به انجام وظایف عمومی است و در توانایی‌های کدنویسی تخصص دارد."}, "step-2x-large": {"description": "مدل نسل جدید Step Star برای تولید تصویر است که بر تولید تصویر بر اساس توصیف متنی کاربر تمرکز دارد و تصاویر با کیفیت بالا تولید می‌کند. مدل جدید تصاویر با بافت واقعی‌تر و توانایی تولید متن‌های چینی و انگلیسی قوی‌تر دارد."}, "step-r1-v-mini": {"description": "این مدل یک مدل استدلال بزرگ با توانایی‌های قوی در درک تصویر است که می‌تواند اطلاعات تصویری و متنی را پردازش کند و پس از تفکر عمیق، متن تولید کند. این مدل در زمینه استدلال بصری عملکرد برجسته‌ای دارد و همچنین دارای توانایی‌های ریاضی، کدنویسی و استدلال متنی در سطح اول است. طول متن زمینه‌ای 100k است."}, "taichu_llm": {"description": "Taichu 2.0 بر اساس حجم زیادی از داده‌های با کیفیت بالا آموزش دیده است و دارای توانایی‌های قوی‌تری در درک متن، تولید محتوا، پرسش و پاسخ در مکالمه و غیره می‌باشد."}, "taichu_o1": {"description": "taichu_o1 نسل جدید مدل‌های استدلال بزرگ است که از طریق تعامل چندرسانه‌ای و یادگیری تقویتی زنجیره‌های تفکر شبیه به انسان را ایجاد می‌کند و از تصمیم‌گیری‌های پیچیده پشتیبانی می‌کند و در عین حفظ خروجی با دقت بالا، مسیرهای تفکر قابل مدلسازی را نشان می‌دهد و برای تحلیل استراتژی و تفکر عمیق مناسب است."}, "taichu_vl": {"description": "توانایی‌های درک تصویر، انتقال دانش، و استدلال منطقی را ترکیب کرده و در زمینه پرسش و پاسخ تصویری و متنی عملکرد برجسته‌ای دارد."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct با ۸۰ میلیارد پارامتر، با فعال‌سازی ۱۳ میلیارد پارامتر قادر به رقابت با مدل‌های بزرگ‌تر است و از استدلال ترکیبی «تفکر سریع/تفکر کند» پشتیبانی می‌کند؛ درک متون بلند به صورت پایدار؛ توانایی عامل با تأیید BFCL-v3 و τ-Bench پیشرو است؛ با ترکیب GQA و چندین فرمت کوانتیزه‌سازی، استدلال کارآمد را محقق می‌سازد."}, "text-embedding-3-large": {"description": "قدرت‌مندترین مدل وکتور سازی، مناسب برای وظایف انگلیسی و غیرانگلیسی."}, "text-embedding-3-small": {"description": "مد<PERSON> جدید و کارآمد Embedding، مناسب برای جستجوی دانش، کاربردهای RAG و سایر سناریوها."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 یک مدل زبان با وزن‌های باز 32B دو زبانه (چینی و انگلیسی) است که برای تولید کد، فراخوانی توابع و وظایف نمایندگی بهینه‌سازی شده است. این مدل بر روی 15T داده‌های با کیفیت بالا و داده‌های استدلال مجدد پیش‌آموزش شده و با هم‌راستایی ترجیحات انسانی، نمونه‌برداری رد و یادگیری تقویتی بهبود یافته است. این مدل در استدلال پیچیده، تولید آثار و وظایف خروجی ساختاری عملکرد عالی از خود نشان می‌دهد و در چندین آزمون معیار به عملکردی معادل با GPT-4o و DeepSeek-V3-0324 دست یافته است."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 یک مدل زبان با وزن‌های باز 32B دو زبانه (چینی و انگلیسی) است که برای تولید کد، فراخوانی توابع و وظایف نمایندگی بهینه‌سازی شده است. این مدل بر روی 15T داده‌های با کیفیت بالا و داده‌های استدلال مجدد پیش‌آموزش شده و با هم‌راستایی ترجیحات انسانی، نمونه‌برداری رد و یادگیری تقویتی بهبود یافته است. این مدل در استدلال پیچیده، تولید آثار و وظایف خروجی ساختاری عملکرد عالی از خود نشان می‌دهد و در چندین آزمون معیار به عملکردی معادل با GPT-4o و DeepSeek-V3-0324 دست یافته است."}, "thudm/glm-4-9b-chat": {"description": "نسخه متن باز جدیدترین نسل مدل‌های پیش‌آموزش GLM-4 منتشر شده توسط Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 یک مدل زبان با ۹۰ میلیارد پارامتر در سری GLM-4 است که توسط THUDM توسعه یافته است. GLM-4-9B-0414 از همان استراتژی‌های تقویت یادگیری و هم‌راستایی که برای مدل بزرگ‌تر ۳۲B خود استفاده می‌شود، استفاده می‌کند و نسبت به اندازه خود عملکرد بالایی را ارائه می‌دهد و برای استقرار در منابع محدود که هنوز به توانایی‌های قوی در درک و تولید زبان نیاز دارند، مناسب است."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 یک واریانت تقویت‌شده استدلال GLM-4-32B است که به طور خاص برای حل مسائل عمیق ریاضی، منطقی و کد محور طراحی شده است. این مدل از یادگیری تقویتی گسترش‌یافته (وظیفه‌محور و مبتنی بر ترجیحات جفتی عمومی) برای بهبود عملکرد در وظایف پیچیده چند مرحله‌ای استفاده می‌کند. نسبت به مدل پایه GLM-4-32B، Z1 به طور قابل توجهی توانایی‌های استدلال ساختاری و حوزه‌های رسمی را افزایش می‌دهد.\n\nاین مدل از طریق مهندسی نشانه‌گذاری، مراحل «تفکر» را تحمیل می‌کند و برای خروجی‌های طولانی، انسجام بهبودیافته‌ای را فراهم می‌کند. این مدل برای جریان‌های کاری نمایندگی بهینه‌سازی شده و از زمینه‌های طولانی (از طریق YaRN)، فراخوانی ابزار JSON و پیکربندی نمونه‌برداری دقیق برای استدلال پایدار پشتیبانی می‌کند. این مدل برای مواردی که نیاز به تفکر عمیق، استدلال چند مرحله‌ای یا استنتاج رسمی دارند، بسیار مناسب است."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 یک واریانت تقویت‌شده استدلال GLM-4-32B است که به طور خاص برای حل مسائل عمیق ریاضی، منطقی و کد محور طراحی شده است. این مدل از یادگیری تقویتی گسترش‌یافته (وظیفه‌محور و مبتنی بر ترجیحات جفتی عمومی) برای بهبود عملکرد در وظایف پیچیده چند مرحله‌ای استفاده می‌کند. نسبت به مدل پایه GLM-4-32B، Z1 به طور قابل توجهی توانایی‌های استدلال ساختاری و حوزه‌های رسمی را افزایش می‌دهد.\n\nاین مدل از طریق مهندسی نشانه‌گذاری، مراحل «تفکر» را تحمیل می‌کند و برای خروجی‌های طولانی، انسجام بهبودیافته‌ای را فراهم می‌کند. این مدل برای جریان‌های کاری نمایندگی بهینه‌سازی شده و از زمینه‌های طولانی (از طریق YaRN)، فراخوانی ابزار JSON و پیکربندی نمونه‌برداری دقیق برای استدلال پایدار پشتیبانی می‌کند. این مدل برای مواردی که نیاز به تفکر عمیق، استدلال چند مرحله‌ای یا استنتاج رسمی دارند، بسیار مناسب است."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 یک مدل زبان با ۹B پارامتر در سری GLM-4 است که توسط THUDM توسعه یافته است. این مدل از تکنیک‌هایی که در ابتدا برای مدل بزرگ‌تر GLM-Z1 استفاده شده بود، شامل تقویت یادگیری گسترش‌یافته، هم‌راستایی رتبه‌بندی جفت و آموزش برای وظایف استدلال فشرده مانند ریاضیات، کدنویسی و منطق استفاده می‌کند. با وجود اندازه کوچکتر، این مدل در وظایف استدلال عمومی عملکرد قوی دارد و در سطح وزن خود از بسیاری از مدل‌های متن‌باز برتر است."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B یک مدل عمیق استدلال با ۳۲B پارامتر در سری GLM-4-Z1 است که برای وظایف پیچیده و باز که نیاز به تفکر طولانی دارند بهینه‌سازی شده است. این مدل بر اساس glm-4-32b-0414 ساخته شده و مراحل تقویت یادگیری اضافی و استراتژی‌های هم‌راستایی چند مرحله‌ای را اضافه کرده است و توانایی «تفکر» را که به شبیه‌سازی پردازش شناختی گسترش یافته طراحی شده است، معرفی می‌کند. این شامل استدلال تکراری، تحلیل چندپرش و جریان‌های کاری تقویت‌شده با ابزارهایی مانند جستجو، بازیابی و ترکیب آگاهانه است.\n\nاین مدل در نوشتن تحقیقاتی، تحلیل مقایسه‌ای و پرسش و پاسخ پیچیده عملکرد عالی دارد. این مدل از فراخوانی توابع برای جستجو و ناوبری (جستجو، کلیک، باز کردن، اتمام) پشتیبانی می‌کند و می‌تواند در لوله‌های نمایندگی استفاده شود. رفتار تفکری توسط کنترل چند دوری با پاداش‌های مبتنی بر قوانین و مکانیزم تصمیم‌گیری تأخیری شکل می‌گیرد و به عنوان مرجع از چارچوب‌های عمیق تحقیقاتی مانند انباشت هم‌راستایی داخلی OpenAI استفاده می‌شود. این واریانت برای صحنه‌هایی که نیاز به عمق به جای سرعت دارند مناسب است."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chi<PERSON>a با ترکیب DeepSeek-R1 و DeepSeek-V3 (۰۳۲۴) ایجاد شده است و توانایی استدلال R1 و بهبود کارایی توکن V3 را ترکیب می‌کند. این مدل بر اساس معماری DeepSeek-MoE Transformer ساخته شده و برای وظایف تولید متن عمومی بهینه‌سازی شده است.\n\nاین مدل وزن‌های پیش‌آموزش دو مدل منبع را ترکیب می‌کند تا عملکرد استدلال، کارایی و پیروی از دستورات را متعادل کند. این مدل تحت مجوز MIT منتشر شده و برای استفاده‌های تحقیقاتی و تجاری طراحی شده است."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) با استفاده از استراتژی‌ها و معماری مدل کارآمد، توان محاسباتی بهبودیافته‌ای را ارائه می‌دهد."}, "tts-1": {"description": "جدیدترین مدل تبدیل متن به گفتار، بهینه‌سازی شده برای سرعت در سناریوهای زنده."}, "tts-1-hd": {"description": "جدید<PERSON>رین مدل تبدیل متن به گفتار، بهینه‌سازی شده برای کیفیت."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) مناسب برای وظایف دقیق دستوری، ارائه‌دهنده توانایی‌های برجسته در پردازش زبان."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet استانداردهای صنعتی را ارتقا داده و عملکردی فراتر از مدل‌های رقیب و Claude 3 Opus دارد و در ارزیابی‌های گسترده‌ای عملکرد عالی از خود نشان می‌دهد، در حالی که سرعت و هزینه مدل‌های سطح متوسط ما را نیز داراست."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet سریع‌ترین مدل نسل بعدی Anthropic است. در مقایسه با Claude 3 Haiku، Claude 3.7 Sonnet در تمام مهارت‌ها بهبود یافته و در بسیاری از آزمون‌های استاندارد هوش از بزرگ‌ترین مدل نسل قبلی، Claude 3 Opus، پیشی گرفته است."}, "v0-1.0-md": {"description": "مدل v0-1.0-md نسخه قدیمی مدلی است که از طریق API نسخه v0 ارائه می‌شود"}, "v0-1.5-lg": {"description": "مدل v0-1.5-lg برای وظایف پیشرفته تفکر یا استدلال مناسب است"}, "v0-1.5-md": {"description": "مدل v0-1.5-md برای وظایف روزمره و تولید رابط کاربری (UI) مناسب است"}, "wan2.2-t2i-flash": {"description": "نسخه سریع Wanxiang 2.2، جدیدترین مدل فعلی. در خلاقیت، پایداری و واقع‌گرایی به طور کامل ارتقا یافته، سرعت تولید بالا و نسبت قیمت به کیفیت عالی دارد."}, "wan2.2-t2i-plus": {"description": "نسخه حرفه‌ای Wanxiang 2.2، جدیدترین مدل فعلی. در خلاقیت، پایداری و واقع‌گرایی به طور کامل ارتقا یافته و جزئیات تولید شده غنی‌تر است."}, "wanx-v1": {"description": "مدل پایه تولید تصویر از متن. معادل مدل عمومی 1.0 در وب‌سایت رسمی Tongyi Wanxiang."}, "wanx2.0-t2i-turbo": {"description": "متخصص در پرتره‌های با بافت، سرعت متوسط و هزینه پایین. معادل مدل سریع 2.0 در وب‌سایت رسمی Tongyi Wanxiang."}, "wanx2.1-t2i-plus": {"description": "نسخه ارتقا یافته کامل. جزئیات تصاویر تولید شده غنی‌تر و سرعت کمی کندتر است. معادل مدل حرفه‌ای 2.1 در وب‌سایت رسمی Tongyi Wanxiang."}, "wanx2.1-t2i-turbo": {"description": "نسخه ارتقا یافته کامل. سرعت تولید بالا، عملکرد جامع و نسبت قیمت به کیفیت عالی. معادل مدل سریع 2.1 در وب‌سایت رسمی Tongyi Wanxiang."}, "whisper-1": {"description": "مدل شناسایی گفتار عمومی که از شناسایی گفتار چندزبانه، ترجمه گفتار و شناسایی زبان پشتیبانی می‌کند."}, "wizardlm2": {"description": "WizardLM 2 یک مدل زبانی ارائه شده توسط هوش مصنوعی مایکروسافت است که در مکالمات پیچیده، چندزبانه، استدلال و دستیارهای هوشمند عملکرد برجسته‌ای دارد."}, "wizardlm2:8x22b": {"description": "WizardLM 2 یک مدل زبانی ارائه شده توسط مایکروسافت AI است که در زمینه‌های مکالمات پیچیده، چندزبانه، استدلال و دستیارهای هوشمند عملکرد برجسته‌ای دارد."}, "x1": {"description": "مدل Spark X1 به‌زودی ارتقا خواهد یافت و در زمینه وظایف ریاضی که در کشور پیشرو است، عملکردهای استدلال، تولید متن و درک زبان را با OpenAI o1 و DeepSeek R1 مقایسه خواهد کرد."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 نسخه‌ی به‌روزرسانی شده‌ی Yi است. این مدل با استفاده از یک مجموعه داده با کیفیت بالا شامل 500 میلیارد توکن برای پیش‌آموزی و 3 میلیون نمونه متنوع برای آموزش ریزی مجدداً آموزش داده شده است."}, "yi-large": {"description": "مدل جدید با میلیاردها پارامتر، ارائه‌دهنده توانایی‌های فوق‌العاده در پاسخ‌گویی و تولید متن."}, "yi-large-fc": {"description": "بر اساس مدل yi-large، قابلیت استفاده از ابزارها را پشتیبانی و تقویت کرده است و برای انواع سناریوهای کسب‌وکاری که نیاز به ساخت agent یا workflow دارند، مناسب است."}, "yi-large-preview": {"description": "نسخه اولیه، توصیه می‌شود از yi-large (نسخه جدید) استفاده کنید."}, "yi-large-rag": {"description": "خدمات پیشرفته مبتنی بر مدل فوق‌العاده yi-large، که با ترکیب فناوری‌های جستجو و تولید، پاسخ‌های دقیقی ارائه می‌دهد و خدمات جستجوی اطلاعات در سراسر وب به صورت لحظه‌ای فراهم می‌کند."}, "yi-large-turbo": {"description": "عملکرد عالی با صرفه‌جویی بالا. بهینه‌سازی دقت بالا با توجه به تعادل بین عملکرد، سرعت استنتاج و هزینه."}, "yi-lightning": {"description": "جدید<PERSON>رین مدل با عملکرد بالا که ضمن تضمین خروجی با کیفیت بالا، سرعت استنتاج را به طور قابل توجهی افزایش می‌دهد."}, "yi-lightning-lite": {"description": "نسخه سبک، استفاده از yi-lightning توصیه می‌شود."}, "yi-medium": {"description": "ارتقاء مدل با اندازه متوسط، با توانایی‌های متعادل و مقرون‌به‌صرفه. بهینه‌سازی عمیق در توانایی پیروی از دستورات."}, "yi-medium-200k": {"description": "پنجره متنی بسیار طولانی ۲۰۰ هزار کلمه‌ای، با قابلیت درک و تولید متون طولانی و پیچیده."}, "yi-spark": {"description": "کوچک و قدرتمند، مدلی سبک و فوق‌العاده سریع. قابلیت‌های تقویت‌شده برای محاسبات ریاضی و نوشتن کد ارائه می‌دهد."}, "yi-vision": {"description": "مدل وظایف پیچیده بینایی، ارائه دهنده قابلیت‌های درک و تحلیل تصویر با عملکرد بالا."}, "yi-vision-v2": {"description": "مدل‌های پیچیده بصری که قابلیت‌های درک و تحلیل با عملکرد بالا را بر اساس چندین تصویر ارائه می‌دهند."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 یک مدل پایه طراحی شده برای کاربردهای عامل هوشمند است که از معماری Mixture-of-Experts استفاده می‌کند. این مدل در زمینه‌های فراخوانی ابزار، مرور وب، مهندسی نرم‌افزار و برنامه‌نویسی فرانت‌اند بهینه‌سازی عمیق شده و از ادغام بی‌وقفه با عامل‌های کد مانند Claude Code و Roo Code پشتیبانی می‌کند. GLM-4.5 از حالت استدلال ترکیبی بهره می‌برد و می‌تواند در سناریوهای استدلال پیچیده و استفاده روزمره به خوبی عمل کند."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air یک مدل پایه طراحی شده برای کاربردهای عامل هوشمند است که از معماری Mixture-of-Experts استفاده می‌کند. این مدل در زمینه‌های فراخوانی ابزار، مرور وب، مهندسی نرم‌افزار و برنامه‌نویسی فرانت‌اند بهینه‌سازی عمیق شده و از ادغام بی‌وقفه با عامل‌های کد مانند Claude Code و Roo Code پشتیبانی می‌کند. GLM-4.5 از حالت استدلال ترکیبی بهره می‌برد و می‌تواند در سناریوهای استدلال پیچیده و استفاده روزمره به خوبی عمل کند."}}