{"01-ai/yi-1.5-34b-chat": {"description": "零一万物，最新开源微调模型，340亿参数，微调支持多种对话场景，高质量训练数据，对齐人类偏好。"}, "01-ai/yi-1.5-9b-chat": {"description": "零一万物，最新开源微调模型，90亿参数，微调支持多种对话场景，高质量训练数据，对齐人类偏好。"}, "360/deepseek-r1": {"description": "【360部署版】DeepSeek-R1在后训练阶段大规模使用了强化学习技术，在仅有极少标注数据的情况下，极大提升了模型推理能力。在数学、代码、自然语言推理等任务上，性能比肩 OpenAI o1 正式版。"}, "360gpt-pro": {"description": "360GPT Pro 作为 360 AI 模型系列的重要成员，以高效的文本处理能力满足多样化的自然语言应用场景，支持长文本理解和多轮对话等功能。"}, "360gpt-pro-trans": {"description": "翻译专用模型，深度微调优化，翻译效果领先。"}, "360gpt-turbo": {"description": "360GPT Turbo 提供强大的计算和对话能力，具备出色的语义理解和生成效率，是企业和开发者理想的智能助理解决方案。"}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K 强调语义安全和责任导向，专为对内容安全有高度要求的应用场景设计，确保用户体验的准确性与稳健性。"}, "360gpt2-o1": {"description": "360gpt2-o1 使用树搜索构建思维链，并引入了反思机制，使用强化学习训练，模型具备自我反思与纠错的能力。"}, "360gpt2-pro": {"description": "360GPT2 Pro 是 360 公司推出的高级自然语言处理模型，具备卓越的文本生成和理解能力，尤其在生成与创作领域表现出色，能够处理复杂的语言转换和角色演绎任务。"}, "360zhinao2-o1": {"description": "360zhinao2-o1 使用树搜索构建思维链，并引入了反思机制，使用强化学习训练，模型具备自我反思与纠错的能力。"}, "4.0Ultra": {"description": "Spark Ultra 是星火大模型系列中最为强大的版本，在升级联网搜索链路同时，提升对文本内容的理解和总结能力。它是用于提升办公生产力和准确响应需求的全方位解决方案，是引领行业的智能产品。"}, "AnimeSharp": {"description": "AnimeSharp（又名 “4x‑AnimeSharp”） 是 Kim2091 基于 ESRGAN 架构开发的开源超分辨率模型，专注于动漫风格图像的放大与锐化。它于 2022 年 2 月重命名自 “4x-TextSharpV1”，原本也适用于文字图像但性能针对动漫内容进行了大幅优化"}, "Baichuan2-Turbo": {"description": "采用搜索增强技术实现大模型与领域知识、全网知识的全面链接。支持PDF、Word等多种文档上传及网址输入，信息获取及时、全面，输出结果准确、专业。"}, "Baichuan3-Turbo": {"description": "针对企业高频场景优化，效果大幅提升，高性价比。相对于Baichuan2模型，内容创作提升20%，知识问答提升17%， 角色扮演能力提升40%。整体效果比GPT3.5更优。"}, "Baichuan3-Turbo-128k": {"description": "具备 128K 超长上下文窗口，针对企业高频场景优化，效果大幅提升，高性价比。相对于Baichuan2模型，内容创作提升20%，知识问答提升17%， 角色扮演能力提升40%。整体效果比GPT3.5更优。"}, "Baichuan4": {"description": "模型能力国内第一，在知识百科、长文本、生成创作等中文任务上超越国外主流模型。还具备行业领先的多模态能力，多项权威评测基准表现优异。"}, "Baichuan4-Air": {"description": "模型能力国内第一，在知识百科、长文本、生成创作等中文任务上超越国外主流模型。还具备行业领先的多模态能力，多项权威评测基准表现优异。"}, "Baichuan4-Turbo": {"description": "模型能力国内第一，在知识百科、长文本、生成创作等中文任务上超越国外主流模型。还具备行业领先的多模态能力，多项权威评测基准表现优异。"}, "DeepSeek-R1": {"description": "DeepSeek-R1 在后训练阶段大规模使用了强化学习技术，在仅有极少标注数据的情况下，极大提升了模型推理能力。在数学、代码、自然语言推理等任务上，性能比肩 OpenAI o1 正式版。"}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1——DeepSeek 套件中更大更智能的模型——被蒸馏到 Llama 70B 架构中。基于基准测试和人工评估，该模型比原始 Llama 70B 更智能，尤其在需要数学和事实精确性的任务上表现出色。"}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "基于 Qwen2.5-Math-1.5B 的 DeepSeek-R1 蒸馏模型，通过强化学习与冷启动数据优化推理性能，开源模型刷新多任务标杆。"}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1-Distill 模型是在开源模型的基础上通过微调训练得到的，训练过程中使用了由 DeepSeek-R1 生成的样本数据。"}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill 模型是在开源模型的基础上通过微调训练得到的，训练过程中使用了由 DeepSeek-R1 生成的样本数据。"}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "基于 Qwen2.5-Math-7B 的 DeepSeek-R1 蒸馏模型，通过强化学习与冷启动数据优化推理性能，开源模型刷新多任务标杆。"}, "DeepSeek-V3": {"description": "DeepSeek-V3 是一款由深度求索公司自研的MoE模型。DeepSeek-V3 多项评测成绩超越了 Qwen2.5-72B 和 Llama-3.1-405B 等其他开源模型，并在性能上和世界顶尖的闭源模型 GPT-4o 以及 Claude-3.5-Sonnet 不分伯仲。"}, "Doubao-lite-128k": {"description": "Doubao-lite 拥有极致的响应速度，更好的性价比，为客户不同场景提供更灵活的选择。支持128k上下文窗口的推理和精调。"}, "Doubao-lite-32k": {"description": "Doubao-lite拥有极致的响应速度，更好的性价比，为客户不同场景提供更灵活的选择。支持32k上下文窗口的推理和精调。"}, "Doubao-lite-4k": {"description": "Doubao-lite拥有极致的响应速度，更好的性价比，为客户不同场景提供更灵活的选择。支持4k上下文窗口的推理和精调。"}, "Doubao-pro-128k": {"description": "效果最好的主力模型，适合处理复杂任务，在参考问答、总结摘要、创作、文本分类、角色扮演等场景都有很好的效果。支持128k上下文窗口的推理和精调。"}, "Doubao-pro-32k": {"description": "效果最好的主力模型，适合处理复杂任务，在参考问答、总结摘要、创作、文本分类、角色扮演等场景都有很好的效果。支持32k上下文窗口的推理和精调。"}, "Doubao-pro-4k": {"description": "效果最好的主力模型，适合处理复杂任务，在参考问答、总结摘要、创作、文本分类、角色扮演等场景都有很好的效果。支持4k上下文窗口的推理和精调。"}, "DreamO": {"description": "DreamO 是由字节跳动与北京大学联合研发的开源图像定制生成模型，旨在通过统一架构支持多任务图像生成。它采用高效的组合建模方法，可根据用户指定的身份、主体、风格、背景等多个条件生成高度一致且定制化的图像。"}, "ERNIE-3.5-128K": {"description": "百度自研的旗舰级大规模⼤语⾔模型，覆盖海量中英文语料，具有强大的通用能力，可满足绝大部分对话问答、创作生成、插件应用场景要求；支持自动对接百度搜索插件，保障问答信息时效。"}, "ERNIE-3.5-8K": {"description": "百度自研的旗舰级大规模⼤语⾔模型，覆盖海量中英文语料，具有强大的通用能力，可满足绝大部分对话问答、创作生成、插件应用场景要求；支持自动对接百度搜索插件，保障问答信息时效。"}, "ERNIE-3.5-8K-Preview": {"description": "百度自研的旗舰级大规模⼤语⾔模型，覆盖海量中英文语料，具有强大的通用能力，可满足绝大部分对话问答、创作生成、插件应用场景要求；支持自动对接百度搜索插件，保障问答信息时效。"}, "ERNIE-4.0-8K-Latest": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，相较ERNIE 3.5实现了模型能力全面升级，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。"}, "ERNIE-4.0-8K-Preview": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，相较ERNIE 3.5实现了模型能力全面升级，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。"}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，综合效果表现出色，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。相较于ERNIE 4.0在性能表现上更优秀"}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，综合效果表现出色，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。相较于ERNIE 4.0在性能表现上更优秀"}, "ERNIE-Character-8K": {"description": "百度自研的垂直场景大语言模型，适合游戏NPC、客服对话、对话角色扮演等应用场景，人设风格更为鲜明、一致，指令遵循能力更强，推理性能更优。"}, "ERNIE-Lite-Pro-128K": {"description": "百度自研的轻量级大语言模型，兼顾优异的模型效果与推理性能，效果比ERNIE Lite更优，适合低算力AI加速卡推理使用。"}, "ERNIE-Speed-128K": {"description": "百度2024年最新发布的自研高性能大语言模型，通用能力优异，适合作为基座模型进行精调，更好地处理特定场景问题，同时具备极佳的推理性能。"}, "ERNIE-Speed-Pro-128K": {"description": "百度2024年最新发布的自研高性能大语言模型，通用能力优异，效果比ERNIE Speed更优，适合作为基座模型进行精调，更好地处理特定场景问题，同时具备极佳的推理性能。"}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev 是由 Black Forest Labs 开发的一款基于 Rectified Flow Transformer 架构 的多模态图像生成与编辑模型，拥有 12B（120 亿）参数规模，专注于在给定上下文条件下生成、重构、增强或编辑图像。该模型结合了扩散模型的可控生成优势与 Transformer 的上下文建模能力，支持高质量图像输出，广泛适用于图像修复、图像补全、视觉场景重构等任务。"}, "FLUX.1-dev": {"description": "FLUX.1-dev 是由 Black Forest Labs 开发的一款开源 多模态语言模型（Multimodal Language Model, MLLM），专为图文任务优化，融合了图像和文本的理解与生成能力。它建立在先进的大语言模型（如 Mistral-7B）基础上，通过精心设计的视觉编码器与多阶段指令微调，实现了图文协同处理与复杂任务推理的能力。"}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) 是一种创新模型，适合多领域应用和复杂任务。"}, "HelloMeme": {"description": "HelloMeme 是一个可以根据你提供的图片或动作，自动生成表情包、动图或短视频的 AI 工具。它不需要你有任何绘画或编程基础，只需要准备好参考图片，它就能帮你做出好看、有趣、风格一致的内容。"}, "HiDream-I1-Full": {"description": "HiDream-E1-Full 是由智象未来（HiDream.ai）推出的一款 开源多模态图像编辑大模型，基于先进的 Diffusion Transformer 架构，并结合强大的语言理解能力（内嵌 LLaMA 3.1-8B-Instruct），支持通过自然语言指令进行图像生成、风格迁移、局部编辑和内容重绘，具备出色的图文理解与执行能力。"}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled 是一款轻量级的文生图模型，经过蒸馏优化，能够快速生成高质量的图像，特别适用于低资源环境和实时生成任务。"}, "InstantCharacter": {"description": "InstantCharacter 是由腾讯 AI 团队在 2025 年发布的一款 无需微调（tuning-free） 的个性化角色生成模型，旨在实现高保真、跨场景的一致角色生成。该模型支持仅基于 一张参考图像 对角色进行建模，并能够将该角色灵活迁移到各种风格、动作和背景中。"}, "InternVL2-8B": {"description": "InternVL2-8B 是一款强大的视觉语言模型，支持图像与文本的多模态处理，能够精确识别图像内容并生成相关描述或回答。"}, "InternVL2.5-26B": {"description": "InternVL2.5-26B 是一款强大的视觉语言模型，支持图像与文本的多模态处理，能够精确识别图像内容并生成相关描述或回答。"}, "Kolors": {"description": "Kolors 是由快手 Kolors 团队开发的文生图模型。由数十亿的参数训练，在视觉质量、中文语义理解和文本渲染方面有显著优势。"}, "Kwai-Kolors/Kolors": {"description": "Kolors 是由快手 Kolors 团队开发的基于潜在扩散的大规模文本到图像生成模型。该模型通过数十亿文本-图像对的训练，在视觉质量、复杂语义准确性以及中英文字符渲染方面展现出显著优势。它不仅支持中英文输入，在理解和生成中文特定内容方面也表现出色"}, "Llama-3.2-11B-Vision-Instruct": {"description": "在高分辨率图像上表现出色的图像推理能力，适用于视觉理解应用。"}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "适用于视觉理解代理应用的高级图像推理能力。"}, "Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "Meta-Llama-3.2-1B-Instruct": {"description": "先进的最尖端小型语言模型，具备语言理解、卓越的推理能力和文本生成能力。"}, "Meta-Llama-3.2-3B-Instruct": {"description": "先进的最尖端小型语言模型，具备语言理解、卓越的推理能力和文本生成能力。"}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 是 Llama 系列最先进的多语言开源大型语言模型，以极低成本体验媲美 405B 模型的性能。基于 Transformer 结构，并通过监督微调（SFT）和人类反馈强化学习（RLHF）提升有用性和安全性。其指令调优版本专为多语言对话优化，在多项行业基准上表现优于众多开源和封闭聊天模型。知识截止日期为 2023 年 12 月"}, "MiniMax-M1": {"description": "全新自研推理模型。全球领先：80K思维链 x 1M输入，效果比肩海外顶尖模型。"}, "MiniMax-Text-01": {"description": "在 MiniMax-01系列模型中，我们做了大胆创新：首次大规模实现线性注意力机制，传统 Transformer架构不再是唯一的选择。这个模型的参数量高达4560亿，其中单次激活459亿。模型综合性能比肩海外顶尖模型，同时能够高效处理全球最长400万token的上下文，是GPT-4o的32倍，Claude-3.5-Sonnet的20倍。"}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 是开源权重的大规模混合注意力推理模型，拥有 4560 亿参数，每个 Token 可激活约 459 亿参数。模型原生支持 100 万 Token 的超长上下文，并通过闪电注意力机制，在 10 万 Token 的生成任务中相比 DeepSeek R1 节省 75% 的浮点运算量。同时，MiniMax-M1 采用 MoE（混合专家）架构，结合 CISPO 算法与混合注意力设计的高效强化学习训练，在长输入推理与真实软件工程场景中实现了业界领先的性能。"}, "Moonshot-Kimi-K2-Instruct": {"description": "总参数 1T，激活参数 32B。 非思维模型中，在前沿知识、数学和编码方面达到了顶尖水平，更擅长通用 Agent 任务。 针对代理任务进行了精心优化，不仅能回答问题，还能采取行动。 最适用于即兴、通用聊天和代理体验，是一款无需长时间思考的反射级模型。"}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) 是高精度的指令模型，适用于复杂计算。"}, "OmniConsistency": {"description": "OmniConsistency 通过引入大规模 Diffusion Transformers（DiTs）和配对风格化数据，提升图像到图像（Image-to-Image）任务中的风格一致性和泛化能力，避免风格退化。"}, "Phi-3-medium-128k-instruct": {"description": "相同的Phi-3-medium模型，但具有更大的上下文大小，适用于RAG或少量提示。"}, "Phi-3-medium-4k-instruct": {"description": "一个140亿参数模型，质量优于Phi-3-mini，重点关注高质量、推理密集型数据。"}, "Phi-3-mini-128k-instruct": {"description": "相同的Phi-3-mini模型，但具有更大的上下文大小，适用于RAG或少量提示。"}, "Phi-3-mini-4k-instruct": {"description": "Phi-3家族中最小的成员，针对质量和低延迟进行了优化。"}, "Phi-3-small-128k-instruct": {"description": "相同的Phi-3-small模型，但具有更大的上下文大小，适用于RAG或少量提示。"}, "Phi-3-small-8k-instruct": {"description": "一个70亿参数模型，质量优于Phi-3-mini，重点关注高质量、推理密集型数据。"}, "Phi-3.5-mini-instruct": {"description": "Phi-3-mini模型的更新版。"}, "Phi-3.5-vision-instrust": {"description": "Phi-3-vision模型的更新版。"}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct 是 Qwen2 系列中的指令微调大语言模型，参数规模为 7B。该模型基于 Transformer 架构，采用了 SwiGLU 激活函数、注意力 QKV 偏置和组查询注意力等技术。它能够处理大规模输入。该模型在语言理解、生成、多语言能力、编码、数学和推理等多个基准测试中表现出色，超越了大多数开源模型，并在某些任务上展现出与专有模型相当的竞争力。Qwen2-7B-Instruct 在多项评测中均优于 Qwen1.5-7B-Chat，显示出显著的性能提升"}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct 是阿里云发布的最新大语言模型系列之一。该 7B 模型在编码和数学等领域具有显著改进的能力。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升"}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct 是阿里云发布的代码特定大语言模型系列的最新版本。该模型在 Qwen2.5 的基础上，通过 5.5 万亿个 tokens 的训练，显著提升了代码生成、推理和修复能力。它不仅增强了编码能力，还保持了数学和通用能力的优势。模型为代码智能体等实际应用提供了更全面的基础"}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL 是 Qwen 系列的新成员，具备强大的视觉理解能力，能分析图像中的文本、图表和布局，并能理解长视频和捕捉事件，它可以进行推理、操作工具，支持多格式物体定位和生成结构化输出，优化了视频理解的动态分辨率与帧率训练，并提升了视觉编码器效率。"}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking 是由智谱 AI 和清华大学 KEG 实验室联合发布的一款开源视觉语言模型（VLM），专为处理复杂的多模态认知任务而设计。该模型基于 GLM-4-9B-0414 基础模型，通过引入“思维链”（Chain-of-Thought）推理机制和采用强化学习策略，显著提升了其跨模态的推理能力和稳定性。"}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat 是智谱 AI 推出的 GLM-4 系列预训练模型中的开源版本。该模型在语义、数学、推理、代码和知识等多个方面表现出色。除了支持多轮对话外，GLM-4-9B-Chat 还具备网页浏览、代码执行、自定义工具调用（Function Call）和长文本推理等高级功能。模型支持 26 种语言，包括中文、英文、日语、韩语和德语等。在多项基准测试中，GLM-4-9B-Chat 展现了优秀的性能，如 AlignBench-v2、MT-Bench、MMLU 和 C-Eval 等。该模型支持最大 128K 的上下文长度，适用于学术研究和商业应用"}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 是一款强化学习（RL）驱动的推理模型，解决了模型中的重复性和可读性问题。在 RL 之前，DeepSeek-R1 引入了冷启动数据，进一步优化了推理性能。它在数学、代码和推理任务中与 OpenAI-o1 表现相当，并且通过精心设计的训练方法，提升了整体效果。"}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B 是基于 Qwen2.5-Math-7B 通过知识蒸馏得到的模型。该模型使用 DeepSeek-R1 生成的 80 万个精选样本进行微调，展现出优秀的推理能力。在多个基准测试中表现出色，其中在 MATH-500 上达到了 92.8% 的准确率，在 AIME 2024 上达到了 55.5% 的通过率，在 CodeForces 上获得了 1189 的评分，作为 7B 规模的模型展示了较强的数学和编程能力。"}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 是一款拥有 6710 亿参数的混合专家（MoE）语言模型，采用多头潜在注意力（MLA）和 DeepSeekMoE 架构，结合无辅助损失的负载平衡策略，优化推理和训练效率。通过在 14.8 万亿高质量tokens上预训练，并进行监督微调和强化学习，DeepSeek-V3 在性能上超越其他开源模型，接近领先闭源模型。"}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 是一款具备超强代码和 Agent 能力的 MoE 架构基础模型，总参数 1T，激活参数 32B。在通用知识推理、编程、数学、Agent 等主要类别的基准性能测试中，K2 模型的性能超过其他主流开源模型。"}, "QwQ-32B-Preview": {"description": "<PERSON>wen QwQ 是由 Qwen 团队开发的实验研究模型，专注于提升AI推理能力。"}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview 是由 Qwen 团队开发的专注于视觉推理能力的研究型模型，其在复杂场景理解和解决视觉相关的数学问题方面具有独特优势。"}, "Qwen/QwQ-32B": {"description": "QwQ 是 Qwen 系列的推理模型。与传统的指令调优模型相比，QwQ 具备思考和推理能力，能够在下游任务中实现显著增强的性能，尤其是在解决困难问题方面。QwQ-32B 是中型推理模型，能够在与最先进的推理模型（如 DeepSeek-R1、o1-mini）的对比中取得有竞争力的性能。该模型采用 RoPE、SwiGLU、RMSNorm 和 Attention QKV bias 等技术，具有 64 层网络结构和 40 个 Q 注意力头（GQA 架构中 KV 为 8 个）。"}, "Qwen/QwQ-32B-Preview": {"description": "<PERSON>wen QwQ 是由 Qwen 团队开发的实验研究模型，专注于提升AI推理能力。"}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen 2 Instruct (72B) 为企业级应用提供精准的指令理解和响应。"}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct 是 Qwen2 系列中的指令微调大语言模型，参数规模为 7B。该模型基于 Transformer 架构，采用了 SwiGLU 激活函数、注意力 QKV 偏置和组查询注意力等技术。它能够处理大规模输入。该模型在语言理解、生成、多语言能力、编码、数学和推理等多个基准测试中表现出色，超越了大多数开源模型，并在某些任务上展现出与专有模型相当的竞争力。Qwen2-7B-Instruct 在多项评测中均优于 Qwen1.5-7B-Chat，显示出显著的性能提升"}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL 是 Qwen-VL 模型的最新迭代版本，在视觉理解基准测试中达到了最先进的性能，包括 MathVista、DocVQA、RealWorldQA 和 MTVQA 等。Qwen2-VL 能够理解超过 20 分钟的视频，用于高质量的基于视频的问答、对话和内容创作。它还具备复杂推理和决策能力，可以与移动设备、机器人等集成，基于视觉环境和文本指令进行自动操作。除了英语和中文，Qwen2-VL 现在还支持理解图像中不同语言的文本，包括大多数欧洲语言、日语、韩语、阿拉伯语和越南语等"}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct 是阿里云发布的最新大语言模型系列之一。该 14B 模型在编码和数学等领域具有显著改进的能力。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升"}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct 是阿里云发布的最新大语言模型系列之一。该 32B 模型在编码和数学等领域具有显著改进的能力。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升"}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct 是阿里云发布的最新大语言模型系列之一。该 72B 模型在编码和数学等领域具有显著改进的能力。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5-72B-Instruct 是阿里云发布的最新大语言模型系列之一。该 72B 模型在编码和数学等领域具有显著改进的能力。它支持长达 128K tokens 的输入，可以生成超过 8K tokens 的长文本。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升"}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 是全新的大型语言模型系列，旨在优化指令式任务的处理。"}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct 是阿里云发布的最新大语言模型系列之一。该 7B 模型在编码和数学等领域具有显著改进的能力。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升"}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 是全新的大型语言模型系列，旨在优化指令式任务的处理。"}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5 Coder 32B Instruct 是阿里云发布的代码特定大语言模型系列的最新版本。该模型在 Qwen2.5 的基础上，通过 5.5 万亿个 tokens 的训练，显著提升了代码生成、推理和修复能力。它不仅增强了编码能力，还保持了数学和通用能力的优势。模型为代码智能体等实际应用提供了更全面的基础"}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct 是阿里云发布的代码特定大语言模型系列的最新版本。该模型在 Qwen2.5 的基础上，通过 5.5 万亿个 tokens 的训练，显著提升了代码生成、推理和修复能力。它不仅增强了编码能力，还保持了数学和通用能力的优势。模型为代码智能体等实际应用提供了更全面的基础"}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct 是通义千问团队推出的多模态大模型，是 Qwen2.5-VL 系列的一部分。该模型不仅精通识别常见物体，还能分析图像中的文本、图表、图标、图形和布局。它可作为视觉智能体，能够推理并动态操控工具，具备使用电脑和手机的能力。此外，这个模型可以精确定位图像中的对象，并为发票、表格等生成结构化输出。相比前代模型 Qwen2-VL，该版本在数学和问题解决能力方面通过强化学习得到了进一步提升，响应风格也更符合人类偏好。"}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL 是 Qwen2.5 系列中的视觉语言模型。该模型在多方面有显著提升：具备更强的视觉理解能力，能够识别常见物体、分析文本、图表和布局；作为视觉代理能够推理并动态指导工具使用；支持理解超过 1 小时的长视频并捕捉关键事件；能够通过生成边界框或点准确定位图像中的物体；支持生成结构化输出，尤其适用于发票、表格等扫描数据。"}, "Qwen/Qwen3-14B": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 是由阿里云通义千问团队开发的 Qwen3 系列中的一款旗舰级混合专家（MoE）大语言模型。该模型拥有 2350 亿总参数，每次推理激活 220 亿参数。它是作为 Qwen3-235B-A22B 非思考模式的更新版本发布的，专注于在指令遵循、逻辑推理、文本理解、数学、科学、编程及工具使用等通用能力上实现显著提升。此外，模型增强了对多语言长尾知识的覆盖，并能更好地对齐用户在主观和开放性任务上的偏好，以生成更有帮助和更高质量的文本。"}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 是由阿里巴巴通义千问团队开发的 Qwen3 系列大型语言模型中的一员，专注于高难度的复杂推理任务。该模型基于混合专家（MoE）架构，总参数量达 2350 亿，而在处理每个 token 时仅激活约 220 亿参数，从而在保持强大性能的同时提高了计算效率。作为一个专门的“思考”模型，它在逻辑推理、数学、科学、编程和学术基准测试等需要人类专业知识的任务上表现显著提升，达到了开源思考模型中的顶尖水平。此外，模型还增强了通用能力，如指令遵循、工具使用和文本生成，并原生支持 256K 的长上下文理解能力，非常适合用于需要深度推理和处理长文档的场景。"}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 是 Qwen3-30B-A3B 非思考模式的更新版本。这是一个拥有 305 亿总参数和 33 亿激活参数的混合专家（MoE）模型。该模型在多个方面进行了关键增强，包括显著提升了指令遵循、逻辑推理、文本理解、数学、科学、编码和工具使用等通用能力。同时，它在多语言的长尾知识覆盖范围上取得了实质性进展，并能更好地与用户在主观和开放式任务中的偏好对齐，从而能够生成更有帮助的回复和更高质量的文本。此外，该模型的长文本理解能力也增强到了 256K。此模型仅支持非思考模式，其输出中不会生成 `<think></think>` 标签。"}, "Qwen/Qwen3-32B": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "Qwen/Qwen3-8B": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "Qwen2-72B-Instruct": {"description": "Qwen2 是 Qwen 模型的最新系列，支持 128k 上下文，对比当前最优的开源模型，Qwen2-72B 在自然语言理解、知识、代码、数学及多语言等多项能力上均显著超越当前领先的模型。"}, "Qwen2-7B-Instruct": {"description": "Qwen2 是 Qwen 模型的最新系列，能够超越同等规模的最优开源模型甚至更大规模的模型，Qwen2 7B 在多个评测上取得显著的优势，尤其是代码及中文理解上。"}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B是一款强大的视觉语言模型，支持图像与文本的多模态处理，能够精确识别图像内容并生成相关描述或回答。"}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct 是一款 140 亿参数的大语言模型，性能表现优秀，优化中文和多语言场景，支持智能问答、内容生成等应用。"}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct 是一款 320 亿参数的大语言模型，性能表现均衡，优化中文和多语言场景，支持智能问答、内容生成等应用。"}, "Qwen2.5-72B-Instruct": {"description": "面向中文和英文的 LLM，针对语言、编程、数学、推理等领域。"}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct 是一款 70 亿参数的大语言模型，支持 function call 与外部系统无缝交互，极大提升了灵活性和扩展性。优化中文和多语言场景，支持智能问答、内容生成等应用。"}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct 是一款基于大规模预训练的编程指令模型，具备强大的代码理解和生成能力，能够高效地处理各种编程任务，特别适合智能代码编写、自动化脚本生成和编程问题解答。"}, "Qwen2.5-Coder-32B-Instruct": {"description": "高级 LLM，支持代码生成、推理和修复，涵盖主流编程语言。"}, "Qwen3-235B": {"description": "Qwen3-235B-A22B，MoE（混合专家模型）模型,引入了“混合推理模式”，支持用户在“思考模式”和“非思考模式”之间无缝切换，支持119种语言和方言理解与推理，并具备强大的工具调用能力，在综合能力、代码与数学、多语言能力、知识与推理等多项基准测试中，都能与DeepSeek R1、OpenAI o1、o3-mini、Grok 3和谷歌Gemini 2.5 Pro等目前市场上的主流大模型竞争。"}, "Qwen3-32B": {"description": "Qwen3-32B，稠密模型（Dense Model）,引入了“混合推理模式”，支持用户在“思考模式”和“非思考模式”之间无缝切换，由于模型架构改进、训练数据增加以及更有效的训练方法，整体性能与Qwen2.5-72B表现相当。"}, "SenseChat": {"description": "基础版本模型 (V4)，4K上下文长度，通用能力强大"}, "SenseChat-128K": {"description": "基础版本模型 (V4)，128K上下文长度，在长文本理解及生成等任务中表现出色"}, "SenseChat-32K": {"description": "基础版本模型 (V4)，32K上下文长度，灵活应用于各类场景"}, "SenseChat-5": {"description": "最新版本模型 (V5.5)，128K上下文长度，在数学推理、英文对话、指令跟随以及长文本理解等领域能力显著提升，比肩GPT-4o。"}, "SenseChat-5-1202": {"description": "是基于V5.5的最新版本，较上版本在中英文基础能力，聊天，理科知识， 文科知识，写作，数理逻辑，字数控制 等几个维度的表现有显著提升。"}, "SenseChat-5-Cantonese": {"description": "专门为适应香港地区的对话习惯、俚语及本地知识而设计，在粤语的对话理解上超越了GPT-4，在知识、推理、数学及代码编写等多个领域均能与GPT-4 Turbo相媲美。"}, "SenseChat-5-beta": {"description": "部分性能优于 SenseCat-5-1202"}, "SenseChat-Character": {"description": "拟人对话标准版模型，8K上下文长度，高响应速度"}, "SenseChat-Character-Pro": {"description": "拟人对话高级版模型，32K上下文长度，能力全面提升，支持中/英文对话"}, "SenseChat-Turbo": {"description": "适用于快速问答、模型微调场景"}, "SenseChat-Turbo-1202": {"description": "是最新的轻量版本模型，达到全量模型90%以上能力，显著降低推理成本。"}, "SenseChat-Vision": {"description": "最新版本模型 (V5.5)，支持多图的输入，全面实现模型基础能力优化，在对象属性识别、空间关系、动作事件识别、场景理解、情感识别、逻辑常识推理和文本理解生成上都实现了较大提升。"}, "SenseNova-V6-5-Pro": {"description": "通过对多模态、语言及推理数据的全面更新与训练策略的优化，新模型在多模态推理和泛化指令跟随能力上实现了显著提升，支持高达128k的上下文窗口，并在OCR与文旅IP识别等专项任务中表现卓越。"}, "SenseNova-V6-5-Turbo": {"description": "通过对多模态、语言及推理数据的全面更新与训练策略的优化，新模型在多模态推理和泛化指令跟随能力上实现了显著提升，支持高达128k的上下文窗口，并在OCR与文旅IP识别等专项任务中表现卓越。"}, "SenseNova-V6-Pro": {"description": "实现图片、文本、视频能力的原生统一，突破传统多模态分立局限，在OpenCompass和SuperCLUE评测中斩获双冠军。"}, "SenseNova-V6-Reasoner": {"description": "兼顾视觉、语言深度推理，实现慢思考和深度推理，呈现完整的思维链过程。"}, "SenseNova-V6-Turbo": {"description": "实现图片、文本、视频能力的原生统一，突破传统多模态分立局限，在多模基础能力、语言基础能力等核心维度全面领先，文理兼修，在多项测评中多次位列国内外第一梯队水平。"}, "Skylark2-lite-8k": {"description": "云雀（Skylark）第二代模型，Skylark2-lite模型有较高的响应速度，适用于实时性要求高、成本敏感、对模型精度要求不高的场景，上下文窗口长度为8k。"}, "Skylark2-pro-32k": {"description": "云雀（Skylark）第二代模型，Skylark2-pro版本有较高的模型精度，适用于较为复杂的文本生成场景，如专业领域文案生成、小说创作、高质量翻译等，上下文窗口长度为32k。"}, "Skylark2-pro-4k": {"description": "云雀（Skylark）第二代模型，Skylark2-pro模型有较高的模型精度，适用于较为复杂的文本生成场景，如专业领域文案生成、小说创作、高质量翻译等，上下文窗口长度为4k。"}, "Skylark2-pro-character-4k": {"description": "云雀（Skylark）第二代模型，Skylark2-pro-character模型具有优秀的角色扮演和聊天能力，擅长根据用户prompt要求扮演不同角色与用户展开聊天，角色风格突出，对话内容自然流畅，适用于构建聊天机器人、虚拟助手和在线客服等场景，有较高的响应速度。"}, "Skylark2-pro-turbo-8k": {"description": "云雀（Skylark）第二代模型，Skylark2-pro-turbo-8k推理更快，成本更低，上下文窗口长度为8k。"}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 是 GLM 系列的新一代开源模型，拥有 320 亿参数。该模型性能可与 OpenAI 的 GPT 系列和 DeepSeek 的 V3/R1 系列相媲美。"}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 是 GLM 系列的小型模型，拥有 90 亿参数。该模型继承了 GLM-4-32B 系列的技术特点，但提供了更轻量级的部署选择。尽管规模较小，GLM-4-9B-0414 仍在代码生成、网页设计、SVG 图形生成和基于搜索的写作等任务上展现出色能力。"}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking 是由智谱 AI 和清华大学 KEG 实验室联合发布的一款开源视觉语言模型（VLM），专为处理复杂的多模态认知任务而设计。该模型基于 GLM-4-9B-0414 基础模型，通过引入“思维链”（Chain-of-Thought）推理机制和采用强化学习策略，显著提升了其跨模态的推理能力和稳定性。"}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 是一个具有深度思考能力的推理模型。该模型基于 GLM-4-32B-0414 通过冷启动和扩展强化学习开发，并在数学、代码和逻辑任务上进行了进一步训练。与基础模型相比，GLM-Z1-32B-0414 显著提升了数学能力和解决复杂任务的能力。"}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 是 GLM 系列的小型模型，仅有 90 亿参数，但保持了开源传统的同时展现出惊人的能力。尽管规模较小，该模型在数学推理和通用任务上仍表现出色，其总体性能在同等规模的开源模型中已处于领先水平。"}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 是一个具有沉思能力的深度推理模型（与 OpenAI 的 Deep Research 对标）。与典型的深度思考模型不同，沉思模型采用更长时间的深度思考来解决更开放和复杂的问题。"}, "THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat 是智谱 AI 推出的 GLM-4 系列预训练模型中的开源版本。该模型在语义、数学、推理、代码和知识等多个方面表现出色。除了支持多轮对话外，GLM-4-9B-Chat 还具备网页浏览、代码执行、自定义工具调用（Function Call）和长文本推理等高级功能。模型支持 26 种语言，包括中文、英文、日语、韩语和德语等。在多项基准测试中，GLM-4-9B-Chat 展现了优秀的性能，如 AlignBench-v2、MT-Bench、MMLU 和 C-Eval 等。该模型支持最大 128K 的上下文长度，适用于学术研究和商业应用"}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B 是首个使用强化学习训练的长上下文大型推理模型（LRM），专门针对长文本推理任务进行优化。该模型通过渐进式上下文扩展的强化学习框架，实现了从短上下文到长上下文的稳定迁移。在七个长上下文文档问答基准测试中，QwenLong-L1-32B 超越了 OpenAI-o3-mini 和 Qwen3-235B-A22B 等旗舰模型，性能可媲美 Claude-3.7-Sonnet-Thinking。该模型特别擅长数学推理、逻辑推理和多跳推理等复杂任务。"}, "Yi-34B-Chat": {"description": "Yi-1.5-34B 在保持原系列模型优秀的通用语言能力的前提下，通过增量训练 5 千亿高质量 token，大幅提高了数学逻辑、代码能力。"}, "abab5.5-chat": {"description": "面向生产力场景，支持复杂任务处理和高效文本生成，适用于专业领域应用。"}, "abab5.5s-chat": {"description": "专为中文人设对话场景设计，提供高质量的中文对话生成能力，适用于多种应用场景。"}, "abab6.5g-chat": {"description": "专为多语种人设对话设计，支持英文及其他多种语言的高质量对话生成。"}, "abab6.5s-chat": {"description": "适用于广泛的自然语言处理任务，包括文本生成、对话系统等。"}, "abab6.5t-chat": {"description": "针对中文人设对话场景优化，提供流畅且符合中文表达习惯的对话生成能力。"}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 是一款最先进的大型语言模型，经过强化学习和冷启动数据的优化，具有出色的推理、数学和编程性能。"}, "accounts/fireworks/models/deepseek-v3": {"description": "Deepseek 提供的强大 Mixture-of-Experts (MoE) 语言模型，总参数量为 671B，每个标记激活 37B 参数。"}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Meta 开发并发布了 Meta Llama 3 系列大语言模型（LLM），该系列包含 8B 和 70B 参数规模的预训练和指令微调生成文本模型。Llama 3 指令微调模型专为对话应用场景优化，并在常见的行业基准测试中优于许多现有的开源聊天模型。"}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Meta 开发并发布了 Meta Llama 3 系列大语言模型（LLM），这是一个包含 8B 和 70B 参数规模的预训练和指令微调生成文本模型的集合。Llama 3 指令微调模型专为对话应用场景优化，并在常见的行业基准测试中优于许多现有的开源聊天模型。"}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Meta Llama 3 指令微调模型专为对话应用场景优化，并在常见的行业基准测试中优于许多现有的开源聊天模型。Llama 3 8B Instruct（HF 版本）是 Llama 3 8B Instruct 的原始 FP16 版本，其结果应与官方 Hugging Face 实现一致。"}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Meta Llama 3.1 系列是多语言大语言模型（LLM）集合，包含 8B、70B 和 405B 参数规模的预训练和指令微调生成模型。Llama 3.1 指令微调文本模型（8B、70B、405B）专为多语言对话场景优化，在常见的行业基准测试中优于许多现有的开源和闭源聊天模型。405B 是 Llama 3.1 家族中能力最强的模型。该模型采用 FP8 进行推理，与参考实现高度匹配。"}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Meta Llama 3.1 系列是多语言大语言模型（LLM）集合，包含 8B、70B 和 405B 三种参数规模的预训练和指令微调生成模型。Llama 3.1 指令微调文本模型（8B、70B、405B）专为多语言对话应用优化，并在常见的行业基准测试中优于许多现有的开源和闭源聊天模型。"}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Meta Llama 3.1 系列是多语言大语言模型（LLM）集合，包含 8B、70B 和 405B 三种参数规模的预训练和指令微调生成模型。Llama 3.1 指令微调文本模型（8B、70B、405B）专为多语言对话应用优化，并在常见的行业基准测试中优于许多现有的开源和闭源聊天模型。"}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Meta 推出的指令微调图像推理模型，拥有 110 亿参数。该模型针对视觉识别、图像推理、图片字幕生成以及图片相关的常规问答进行了优化。它能够理解视觉数据，如图表和图形，并通过生成文本描述图像细节，弥合视觉与语言之间的鸿沟。"}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B Instruct 是 Meta 推出的轻量级多语言模型。该模型专为高效运行而设计，相较于更大型的模型，具有显著的延迟和成本优势。其典型应用场景包括查询和提示重写，以及写作辅助。"}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Meta 推出的指令微调图像推理模型，拥有 900 亿参数。该模型针对视觉识别、图像推理、图片字幕生成以及图片相关的常规问答进行了优化。它能够理解视觉数据，如图表和图形，并通过生成文本描述图像细节，弥合视觉与语言之间的鸿沟。注意：该模型目前作为无服务器模型进行实验性提供。如果用于生产环境，请注意 Fireworks 可能会在短时间内取消部署该模型。"}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct 是 Llama 3.1 70B 的 12 月更新版本。该模型在 Llama 3.1 70B（于 2024 年 7 月发布）的基础上进行了改进，增强了工具调用、多语言文本支持、数学和编程能力。该模型在推理、数学和指令遵循方面达到了行业领先水平，并且能够提供与 3.1 405B 相似的性能，同时在速度和成本上具有显著优势。"}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "24B 参数模型，具备与更大型模型相当的最先进能力。"}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B Instruct v0.1 是 Mixtral MoE 8x22B v0.1 的指令微调版本，已启用聊天完成功能 API。"}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B Instruct 是 Mixtral MoE 8x7B 的指令微调版本，已启用聊天完成功能 API。"}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "MythoMix 的改进版，可能是其更为完善的变体，是 MythoLogic-L2 和 Huginn 的合并，采用了高度实验性的张量类型合并技术。由于其独特的性质，该模型在讲故事和角色扮演方面表现出色。"}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi-3-Vision-128K-Instruct 是一个轻量级的、最先进的开放多模态模型，基于包括合成数据和筛选后的公开网站数据集构建，重点关注文本和视觉方面的高质量、推理密集型数据。该模型属于 Phi-3 模型家族，其多模态版本支持 128K 上下文长度（以标记为单位）。该模型经过严格的增强过程，包括监督微调和直接偏好优化，以确保精确的指令遵循和强大的安全措施。"}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "Qwen QwQ 模型专注于推动 AI 推理，并展示了开放模型在推理能力上与闭源前沿模型匹敌的力量。QwQ-32B-Preview 是一个实验性发布版本，在 GPQA、AIME、MATH-500 和 LiveCodeBench 基准测试中，在分析和推理能力上可与 o1 相媲美，并超越 GPT-4o 和 Claude 3.5 Sonnet。注意：该模型目前作为无服务器模型进行实验性提供。如果用于生产环境，请注意 Fireworks 可能会在短时间内取消部署该模型。"}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Qwen-VL 模型的 72B 版本是阿里巴巴最新迭代的成果，代表了近一年的创新。"}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 是由 Qwen 团队和阿里云开发的一系列仅解码语言模型，提供 0.5B、1.5B、3B、7B、14B、32B 和 72B 不同参数规模，并包含基础版和指令微调版。"}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5-Coder 是最新一代专为代码设计的 Qwen 大型语言模型（前称为 CodeQwen）。注意：该模型目前作为无服务器模型进行实验性提供。如果用于生产环境，请注意 Fireworks 可能会在短时间内取消部署该模型。"}, "accounts/yi-01-ai/models/yi-large": {"description": "Yi-Large 是顶尖的大型语言模型之一，在 LMSYS 基准测试排行榜上，其表现仅次于 GPT-4、Gemini 1.5 Pro 和 Claude 3 Opus。它在多语言能力方面表现卓越，特别是在西班牙语、中文、日语、德语和法语方面。Yi-Large 还具有用户友好性，采用与 OpenAI 相同的 API 定义，便于集成。"}, "ai21-jamba-1.5-large": {"description": "一个398B参数（94B活跃）的多语言模型，提供256K长上下文窗口、函数调用、结构化输出和基于事实的生成。"}, "ai21-jamba-1.5-mini": {"description": "一个52B参数（12B活跃）的多语言模型，提供256K长上下文窗口、函数调用、结构化输出和基于事实的生成。"}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "一个398B参数（94B活跃）的多语言模型，提供256K长上下文窗口、函数调用、结构化输出和基于事实的生成。"}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "一个52B参数（12B活跃）的多语言模型，提供256K长上下文窗口、函数调用、结构化输出和基于事实的生成。"}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet 提升了行业标准，性能超过竞争对手模型和 Claude 3 Opus，在广泛的评估中表现出色，同时具有我们中等层级模型的速度和成本。"}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet 提升了行业标准，性能超过竞争对手模型和 Claude 3 Opus，在广泛的评估中表现出色，同时具有我们中等层级模型的速度和成本。"}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku 是 Anthropic 最快、最紧凑的模型，提供近乎即时的响应速度。它可以快速回答简单的查询和请求。客户将能够构建模仿人类互动的无缝 AI 体验。Claude 3 Haiku 可以处理图像并返回文本输出，具有 200K 的上下文窗口。"}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus 是 Anthropic 最强大的 AI 模型，具有在高度复杂任务上的最先进性能。它可以处理开放式提示和未见过的场景，具有出色的流畅性和类人的理解能力。Claude 3 Opus 展示了生成 AI 可能性的前沿。Claude 3 Opus 可以处理图像并返回文本输出，具有 200K 的上下文窗口。"}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Anthropic 的 Claude 3 Sonnet 在智能和速度之间达到了理想的平衡——特别适合企业工作负载。它以低于竞争对手的价格提供最大的效用，并被设计成为可靠的、高耐用的主力机，适用于规模化的 AI 部署。Claude 3 Sonnet 可以处理图像并返回文本输出，具有 200K 的上下文窗口。"}, "anthropic.claude-instant-v1": {"description": "一款快速、经济且仍然非常有能力的模型，可以处理包括日常对话、文本分析、总结和文档问答在内的一系列任务。"}, "anthropic.claude-v2": {"description": "Anthropic 在从复杂对话和创意内容生成到详细指令跟随的广泛任务中都表现出高度能力的模型。"}, "anthropic.claude-v2:1": {"description": "Claude 2 的更新版，具有双倍的上下文窗口，以及在长文档和 RAG 上下文中的可靠性、幻觉率和基于证据的准确性的改进。"}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku 是 Anthropic 的最快且最紧凑的模型，旨在实现近乎即时的响应。它具有快速且准确的定向性能。"}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus 是 Anthropic 用于处理高度复杂任务的最强大模型。它在性能、智能、流畅性和理解力方面表现卓越。"}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku 是 Anthropic 最快的下一代模型。与 Claude 3 Haiku 相比，Claude 3.5 Haiku 在各项技能上都有所提升，并在许多智力基准测试中超越了上一代最大的模型 Claude 3 Opus。"}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet 提供了超越 Opus 的能力和比 Sonnet 更快的速度，同时保持与 Sonnet 相同的价格。Sonnet 特别擅长编程、数据科学、视觉处理、代理任务。"}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet 是 Anthropic 迄今为止最智能的模型，也是市场上首个混合推理模型。Claude 3.7 Sonnet 可以产生近乎即时的响应或延长的逐步思考，用户可以清晰地看到这些过程。Sonnet 特别擅长编程、数据科学、视觉处理、代理任务。"}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 是 Anthropic 用于处理高度复杂任务的最强大模型。它在性能、智能、流畅性和理解力方面表现卓越。"}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 可以产生近乎即时的响应或延长的逐步思考，用户可以清晰地看到这些过程。API 用户还可以对模型思考的时间进行细致的控制"}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B 是一款 720 亿参数、激活 160 亿参的稀疏大语言模型，它基于分组混合专家（MoGE）架构，它在专家选择阶段对专家进行分组，并约束 token 在每个组内激活等量专家，从而实现专家负载均衡，显著提升模型在昇腾平台的部署效率。"}, "aya": {"description": "Aya 23 是 Cohere 推出的多语言模型，支持 23 种语言，为多元化语言应用提供便利。"}, "aya:35b": {"description": "Aya 23 是 Cohere 推出的多语言模型，支持 23 种语言，为多元化语言应用提供便利。"}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B 百川智能开发的包含 130 亿参数的开源可商用的大规模语言模型，在权威的中文和英文 benchmark 上均取得同尺寸最好的效果"}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B 是由百度公司开发的一款基于混合专家（MoE）架构的大语言模型。该模型总参数量为 3000 亿，但在推理时每个 token 仅激活 470 亿参数，从而在保证强大性能的同时兼顾了计算效率。作为 ERNIE 4.5 系列的核心模型之一，在文本理解、生成、推理和编程等任务上展现出卓越的能力。该模型采用了一种创新的多模态异构 MoE 预训练方法，通过文本与视觉模态的联合训练，有效提升了模型的综合能力，尤其在指令遵循和世界知识记忆方面效果突出。"}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse 是一款高性能的 32B 多语言模型，旨在通过指令调优、数据套利、偏好训练和模型合并的创新，挑战单语言模型的表现。它支持 23 种语言。"}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse 是一款高性能的 8B 多语言模型，旨在通过指令调优、数据套利、偏好训练和模型合并的创新，挑战单语言模型的表现。它支持 23 种语言。"}, "c4ai-aya-vision-32b": {"description": "Aya Vision 是一款最先进的多模态模型，在语言、文本和图像能力的多个关键基准上表现出色。它支持 23 种语言。这个 320 亿参数的版本专注于最先进的多语言表现。"}, "c4ai-aya-vision-8b": {"description": "Aya Vision 是一款最先进的多模态模型，在语言、文本和图像能力的多个关键基准上表现出色。这个 80 亿参数的版本专注于低延迟和最佳性能。"}, "charglm-3": {"description": "CharGLM-3 专为角色扮演与情感陪伴设计，支持超长多轮记忆与个性化对话，应用广泛。"}, "charglm-4": {"description": "CharGLM-4 专为角色扮演与情感陪伴设计，支持超长多轮记忆与个性化对话，应用广泛。"}, "chatglm3": {"description": "ChatGLM3 是智谱 AI 与清华 KEG 实验室发布的闭源模型，经过海量中英标识符的预训练与人类偏好对齐训练，相比一代模型在 MMLU、C-Eval、GSM8K 分别取得了 16%、36%、280% 的提升，并登顶中文任务榜单 C-Eval。适用于对知识量、推理能力、创造力要求较高的场景，比如广告文案、小说写作、知识类写作、代码生成等。"}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base 是由智谱开发的 ChatGLM 系列最新一代的 60 亿参数规模的开源的基础模型。"}, "chatgpt-4o-latest": {"description": "ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。"}, "claude-2.0": {"description": "Claude 2 为企业提供了关键能力的进步，包括业界领先的 200K token 上下文、大幅降低模型幻觉的发生率、系统提示以及一个新的测试功能：工具调用。"}, "claude-2.1": {"description": "Claude 2 为企业提供了关键能力的进步，包括业界领先的 200K token 上下文、大幅降低模型幻觉的发生率、系统提示以及一个新的测试功能：工具调用。"}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku 是 Anthropic 最快的下一代模型。与 Claude 3 Haiku 相比，Claude 3.5 Haiku 在各项技能上都有所提升，并在许多智力基准测试中超越了上一代最大的模型 Claude 3 Opus。"}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet 提供了超越 Opus 的能力和比 Sonnet 更快的速度，同时保持与 Sonnet 相同的价格。Sonnet 特别擅长编程、数据科学、视觉处理、代理任务。"}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet 提供了超越 Opus 的能力和比 Sonnet 更快的速度，同时保持与 Sonnet 相同的价格。Sonnet 特别擅长编程、数据科学、视觉处理、代理任务。"}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet 是 Anthropic 迄今为止最智能的模型，也是市场上首个混合推理模型。Claude 3.7 Sonnet 可以产生近乎即时的响应或延长的逐步思考，用户可以清晰地看到这些过程。Sonnet 特别擅长编程、数据科学、视觉处理、代理任务。"}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku 是 Anthropic 的最快且最紧凑的模型，旨在实现近乎即时的响应。它具有快速且准确的定向性能。"}, "claude-3-opus-20240229": {"description": "Claude 3 Opus 是 Anthropic 用于处理高度复杂任务的最强大模型。它在性能、智能、流畅性和理解力方面表现卓越。"}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet 在智能和速度方面为企业工作负载提供了理想的平衡。它以更低的价格提供最大效用，可靠且适合大规模部署。"}, "claude-opus-4-20250514": {"description": "Claude Opus 4 是 Anthropic 用于处理高度复杂任务的最强大模型。它在性能、智能、流畅性和理解力方面表现卓越。"}, "claude-sonnet-4-20250514": {"description": "Claude Sonnet 4 可以产生近乎即时的响应或延长的逐步思考，用户可以清晰地看到这些过程。API 用户还可以对模型思考的时间进行细致的控制"}, "codegeex-4": {"description": "CodeGeeX-4 是强大的AI编程助手，支持多种编程语言的智能问答与代码补全，提升开发效率。"}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B 是一个多语言代码生成模型，支持包括代码补全和生成、代码解释器、网络搜索、函数调用、仓库级代码问答在内的全面功能，覆盖软件开发的各种场景。是参数少于 10B 的顶尖代码生成模型。"}, "codegemma": {"description": "CodeGemma 专用于不同编程任务的轻量级语言模型，支持快速迭代和集成。"}, "codegemma:2b": {"description": "CodeGemma 专用于不同编程任务的轻量级语言模型，支持快速迭代和集成。"}, "codellama": {"description": "Code Llama 是一款专注于代码生成和讨论的 LLM，结合广泛的编程语言支持，适用于开发者环境。"}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama 是一款专注于代码生成和讨论的 LLM，结合广泛的编程语言支持，适用于开发者环境。"}, "codellama:13b": {"description": "Code Llama 是一款专注于代码生成和讨论的 LLM，结合广泛的编程语言支持，适用于开发者环境。"}, "codellama:34b": {"description": "Code Llama 是一款专注于代码生成和讨论的 LLM，结合广泛的编程语言支持，适用于开发者环境。"}, "codellama:70b": {"description": "Code Llama 是一款专注于代码生成和讨论的 LLM，结合广泛的编程语言支持，适用于开发者环境。"}, "codeqwen": {"description": "CodeQwen1.5 是基于大量代码数据训练的大型语言模型，专为解决复杂编程任务。"}, "codestral": {"description": "Codestral 是 Mistral AI 的首款代码模型，为代码生成任务提供优异支持。"}, "codestral-latest": {"description": "Codestral 是我们最先进的编码语言模型，第二个版本于2025年1月发布，专门从事低延迟、高频任务如中间填充（RST）、代码纠正和测试生成。"}, "codex-mini-latest": {"description": "codex-mini-latest 是 o4-mini 的微调版本，专门用于 Codex CLI。对于直接通过 API 使用，我们推荐从 gpt-4.1 开始。"}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B 是一款为指令遵循、对话和编程设计的模型。"}, "cogview-4": {"description": "CogView-4 是智谱首个支持生成汉字的开源文生图模型，在语义理解、图像生成质量、中英文字生成能力等方面全面提升，支持任意长度的中英双语输入，能够生成在给定范围内的任意分辨率图像。"}, "cohere-command-r": {"description": "Command R是一个可扩展的生成模型，旨在针对RAG和工具使用，使企业能够实现生产级AI。"}, "cohere-command-r-plus": {"description": "Command R+是一个最先进的RAG优化模型，旨在应对企业级工作负载。"}, "cohere/Cohere-command-r": {"description": "Command R是一个可扩展的生成模型，旨在针对RAG和工具使用，使企业能够实现生产级AI。"}, "cohere/Cohere-command-r-plus": {"description": "Command R+是一个最先进的RAG优化模型，旨在应对企业级工作负载。"}, "command": {"description": "一个遵循指令的对话模型，在语言任务中表现出高质量、更可靠，并且相比我们的基础生成模型具有更长的上下文长度。"}, "command-a-03-2025": {"description": "Command A 是我们迄今为止性能最强的模型，在工具使用、代理、检索增强生成（RAG）和多语言应用场景方面表现出色。Command A 具有 256K 的上下文长度，仅需两块 GPU 即可运行，并且相比于 Command R+ 08-2024，吞吐量提高了 150%。"}, "command-light": {"description": "一个更小、更快的 Command 版本，几乎同样强大，但速度更快。"}, "command-light-nightly": {"description": "为了缩短主要版本发布之间的时间间隔，我们推出了 Command 模型的每夜版本。对于 command-light 系列，这一版本称为 command-light-nightly。请注意，command-light-nightly 是最新、最具实验性且（可能）不稳定的版本。每夜版本会定期更新，且不会提前通知，因此不建议在生产环境中使用。"}, "command-nightly": {"description": "为了缩短主要版本发布之间的时间间隔，我们推出了 Command 模型的每夜版本。对于 Command 系列，这一版本称为 command-cightly。请注意，command-nightly 是最新、最具实验性且（可能）不稳定的版本。每夜版本会定期更新，且不会提前通知，因此不建议在生产环境中使用。"}, "command-r": {"description": "Command R 是优化用于对话和长上下文任务的LLM，特别适合动态交互与知识管理。"}, "command-r-03-2024": {"description": "Command R 是一个遵循指令的对话模型，在语言任务方面表现出更高的质量、更可靠，并且相比以往模型具有更长的上下文长度。它可用于复杂的工作流程，如代码生成、检索增强生成（RAG）、工具使用和代理。"}, "command-r-08-2024": {"description": "command-r-08-2024 是 Command R 模型的更新版本，于 2024 年 8 月发布。"}, "command-r-plus": {"description": "Command R+ 是一款高性能的大型语言模型，专为真实企业场景和复杂应用而设计。"}, "command-r-plus-04-2024": {"description": "command-r-plus 是 command-r-plus-04-2024 的别名，因此如果您在 API 中使用 command-r-plus，实际上指向的就是该模型。"}, "command-r-plus-08-2024": {"description": "Command R+ 是一个遵循指令的对话模型，在语言任务方面表现出更高的质量、更可靠，并且相比以往模型具有更长的上下文长度。它最适用于复杂的 RAG 工作流和多步工具使用。"}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 是一个小型且高效的更新版本，于 2024 年 12 月发布。它在 RAG、工具使用、代理等需要复杂推理和多步处理的任务中表现出色。"}, "compound-beta": {"description": "Compound-beta 是一个复合 AI 系统，由 GroqCloud 中已经支持的多个开放可用的模型提供支持，可以智能地、有选择地使用工具来回答用户查询。"}, "compound-beta-mini": {"description": "Compound-beta-mini 是一个复合 AI 系统，由 GroqCloud 中已经支持的公开可用模型提供支持，可以智能地、有选择地使用工具来回答用户查询。"}, "computer-use-preview": {"description": "computer-use-preview 模型是专为“计算机使用工具”设计的专用模型，经过训练以理解并执行计算机相关任务。"}, "dall-e-2": {"description": "第二代 DALL·E 模型，支持更真实、准确的图像生成，分辨率是第一代的4倍"}, "dall-e-3": {"description": "最新的 DALL·E 模型，于2023年11月发布。支持更真实、准确的图像生成，具有更强的细节表现力"}, "databricks/dbrx-instruct": {"description": "DBRX Instruct 提供高可靠性的指令处理能力，支持多行业应用。"}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 系列通过强化学习与冷启动数据优化推理性能，开源模型刷新多任务标杆，超越 OpenAI-o1-mini 水平。"}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 通过利用增加的计算资源和在后训练过程中引入算法优化机制，显著提高了其推理和推断能力的深度。该模型在各种基准评估中表现出色，包括数学、编程和一般逻辑方面。其整体性能现已接近领先模型，如 O3 和 Gemini 2.5 Pro。"}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B 是通过从 DeepSeek-R1-0528 模型蒸馏思维链到 Qwen3 8B Base 获得的模型。该模型在开源模型中达到了最先进（SOTA）的性能，在 AIME 2024 测试中超越了 Qwen3 8B 10%，并达到了 Qwen3-235B-thinking 的性能水平。该模型在数学推理、编程和通用逻辑等多个基准测试中表现出色，其架构与 Qwen3-8B 相同，但共享 DeepSeek-R1-0528 的分词器配置。"}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek-R1 蒸馏模型，通过强化学习与冷启动数据优化推理性能，开源模型刷新多任务标杆。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "DeepSeek-R1 蒸馏模型，通过强化学习与冷启动数据优化推理性能，开源模型刷新多任务标杆。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "DeepSeek-R1 蒸馏模型，通过强化学习与冷启动数据优化推理性能，开源模型刷新多任务标杆。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B 是基于 Qwen2.5-32B 通过知识蒸馏得到的模型。该模型使用 DeepSeek-R1 生成的 80 万个精选样本进行微调，在数学、编程和推理等多个领域展现出卓越的性能。在 AIME 2024、MATH-500、GPQA Diamond 等多个基准测试中都取得了优异成绩，其中在 MATH-500 上达到了 94.3% 的准确率，展现出强大的数学推理能力。"}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B 是基于 Qwen2.5-Math-7B 通过知识蒸馏得到的模型。该模型使用 DeepSeek-R1 生成的 80 万个精选样本进行微调，展现出优秀的推理能力。在多个基准测试中表现出色，其中在 MATH-500 上达到了 92.8% 的准确率，在 AIME 2024 上达到了 55.5% 的通过率，在 CodeForces 上获得了 1189 的评分，作为 7B 规模的模型展示了较强的数学和编程能力。"}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek-V2.5 是 DeepSeek-V2-Chat 和 DeepSeek-Coder-V2-Instruct 的升级版本，集成了两个先前版本的通用和编码能力。该模型在多个方面进行了优化，包括写作和指令跟随能力，更好地与人类偏好保持一致。DeepSeek-V2.5 在各种评估基准上都取得了显著的提升，如 AlpacaEval 2.0、ArenaHard、AlignBench 和 MT-Bench 等。"}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 是一款拥有 6710 亿参数的混合专家（MoE）语言模型，采用多头潜在注意力（MLA）和 DeepSeekMoE 架构，结合无辅助损失的负载平衡策略，优化推理和训练效率。通过在 14.8 万亿高质量tokens上预训练，并进行监督微调和强化学习，DeepSeek-V3 在性能上超越其他开源模型，接近领先闭源模型。"}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek LLM Chat (67B) 是创新的 AI 模型 提供深度语言理解和互动能力。"}, "deepseek-ai/deepseek-r1": {"description": "最先进的高效 LLM，擅长推理、数学和编程。"}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 是一个基于 DeepSeekMoE-27B 开发的混合专家（MoE）视觉语言模型，采用稀疏激活的 MoE 架构，在仅激活 4.5B 参数的情况下实现了卓越性能。该模型在视觉问答、光学字符识别、文档/表格/图表理解和视觉定位等多个任务中表现优异。"}, "deepseek-chat": {"description": "融合通用与代码能力的全新开源模型, 不仅保留了原有 Chat 模型的通用对话能力和 Coder 模型的强大代码处理能力，还更好地对齐了人类偏好。此外，DeepSeek-V2.5 在写作任务、指令跟随等多个方面也实现了大幅提升。"}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B 是一个代码语言模型， 基于 2 万亿数据训练而成，其中 87% 为代码， 13% 为中英文语言。模型引入 16K 窗口大小和填空任务，提供项目级别的代码补全和片段填充功能。"}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 是开源的混合专家代码模型，在代码任务方面表现优异，与 GPT4-Turbo 相媲美。"}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 是开源的混合专家代码模型，在代码任务方面表现优异，与 GPT4-Turbo 相媲美。"}, "deepseek-r1": {"description": "DeepSeek-R1 在强化学习（RL）之前引入了冷启动数据，在数学、代码和推理任务上表现可与 OpenAI-o1 相媲美。"}, "deepseek-r1-0528": {"description": "685B 满血版模型，2025年5月28日发布。DeepSeek-R1 在后训练阶段大规模使用了强化学习技术，在仅有极少标注数据的情况下，极大提升了模型推理能力。在数学、代码、自然语言推理等任务上，性能较高，能力较强。"}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B 快速版，支持实时联网搜索，在保持模型性能的同时提供更快的响应速度。"}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B 标准版，支持实时联网搜索，适合需要最新信息的对话和文本处理任务。"}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama 是基于 Llama 从 DeepSeek-R1 蒸馏而来的模型。"}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek-R1-<PERSON><PERSON>ill-Llama-70B是DeepSeek-R1基于Llama3.3-70B-Instruct的蒸馏模型。"}, "deepseek-r1-distill-llama-8b": {"description": "DeepSeek-R1-<PERSON>still-Llama-8B是DeepSeek-R1基于Llama3.1-8B-Base的蒸馏模型。"}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "2025年2月14日首次发布，由千帆大模型研发团队以 Llama3_70B为base模型（Built with Meta Llama）蒸馏所得，蒸馏数据中也同步添加了千帆的语料。"}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "2025年2月14日首次发布，由千帆大模型研发团队以 Llama3_8B为base模型（Built with Meta Llama）蒸馏所得，蒸馏数据中也同步添加了千帆的语料。"}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen 是基于 Qwen 从 DeepSeek-R1 蒸馏而来的模型。"}, "deepseek-r1-distill-qwen-1.5b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-1.5B是DeepSeek-R1基于Qwen-2.5系列的蒸馏模型。"}, "deepseek-r1-distill-qwen-14b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-14B是DeepSeek-R1基于Qwen-2.5系列的蒸馏模型。"}, "deepseek-r1-distill-qwen-32b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-32B是DeepSeek-R1基于Qwen-2.5系列的蒸馏模型。"}, "deepseek-r1-distill-qwen-7b": {"description": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>wen-7B是DeepSeek-R1基于Qwen-2.5系列的蒸馏模型。"}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 满血快速版，支持实时联网搜索，结合了 671B 参数的强大能力和更快的响应速度。"}, "deepseek-r1-online": {"description": "DeepSeek R1 满血版，拥有 671B 参数，支持实时联网搜索，具有更强大的理解和生成能力。"}, "deepseek-reasoner": {"description": "DeepSeek 推出的推理模型。在输出最终回答之前，模型会先输出一段思维链内容，以提升最终答案的准确性。"}, "deepseek-v2": {"description": "DeepSeek V2 是高效的 Mixture-of-Experts 语言模型，适用于经济高效的处理需求。"}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B 是 DeepSeek 的设计代码模型，提供强大的代码生成能力。"}, "deepseek-v3": {"description": "DeepSeek-V3 是一个强大的专家混合（MoE）语言模型，拥有总计 6710 亿参数，每个 token 激活 370 亿参数。"}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 为671B 参数 MoE 模型，在编程与技术能力、上下文理解与长文本处理等方面优势突出。"}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 是一个 685B 参数的专家混合模型，是 DeepSeek 团队旗舰聊天模型系列的最新迭代。\n\n它继承了 [DeepSeek V3](/deepseek/deepseek-chat-v3) 模型，并在各种任务上表现出色。"}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 是一个 685B 参数的专家混合模型，是 DeepSeek 团队旗舰聊天模型系列的最新迭代。\n\n它继承了 [DeepSeek V3](/deepseek/deepseek-chat-v3) 模型，并在各种任务上表现出色。"}, "deepseek/deepseek-r1": {"description": "DeepSeek R1是DeepSeek团队发布的最新开源模型，具备非常强悍的推理性能，尤其在数学、编程和推理任务上达到了与OpenAI的o1模型相当的水平。"}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 在仅有极少标注数据的情况下，极大提升了模型推理能力。在输出最终回答之前，模型会先输出一段思维链内容，以提升最终答案的准确性。"}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 在仅有极少标注数据的情况下，极大提升了模型推理能力。在输出最终回答之前，模型会先输出一段思维链内容，以提升最终答案的准确性。"}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B是基于Llama3.3  70B的大型语言模型，该模型利用DeepSeek R1输出的微调，实现了与大型前沿模型相当的竞争性能。"}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B 是一种基于 Llama-3.1-8B-Instruct 的蒸馏大语言模型，通过使用 DeepSeek R1 的输出进行训练而得。"}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B 是一种基于 Qwen 2.5 14B 的蒸馏大语言模型，通过使用 DeepSeek R1 的输出进行训练而得。该模型在多个基准测试中超越了 OpenAI 的 o1-mini，取得了密集模型（dense models）的最新技术领先成果（state-of-the-art）。以下是一些基准测试的结果：\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\n该模型通过从 DeepSeek R1 的输出中进行微调，展现了与更大规模的前沿模型相当的竞争性能。"}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B 是一种基于 Qwen 2.5 32B 的蒸馏大语言模型，通过使用 DeepSeek R1 的输出进行训练而得。该模型在多个基准测试中超越了 OpenAI 的 o1-mini，取得了密集模型（dense models）的最新技术领先成果（state-of-the-art）。以下是一些基准测试的结果：\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\n该模型通过从 DeepSeek R1 的输出中进行微调，展现了与更大规模的前沿模型相当的竞争性能。"}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1是DeepSeek团队发布的最新开源模型，具备非常强悍的推理性能，尤其在数学、编程和推理任务上达到了与OpenAI的o1模型相当的水平。"}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 在仅有极少标注数据的情况下，极大提升了模型推理能力。在输出最终回答之前，模型会先输出一段思维链内容，以提升最终答案的准确性。"}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3在推理速度方面实现了比之前模型的重大突破。在开源模型中排名第一，并可与全球最先进的闭源模型相媲美。DeepSeek-V3 采用了多头潜在注意力 （MLA） 和 DeepSeekMoE 架构，这些架构在 DeepSeek-V2 中得到了全面验证。此外，DeepSeek-V3 开创了一种用于负载均衡的辅助无损策略，并设定了多标记预测训练目标以获得更强的性能。"}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3在推理速度方面实现了比之前模型的重大突破。在开源模型中排名第一，并可与全球最先进的闭源模型相媲美。DeepSeek-V3 采用了多头潜在注意力 （MLA） 和 DeepSeekMoE 架构，这些架构在 DeepSeek-V2 中得到了全面验证。此外，DeepSeek-V3 开创了一种用于负载均衡的辅助无损策略，并设定了多标记预测训练目标以获得更强的性能。"}, "deepseek_r1": {"description": "DeepSeek-R1 是一款强化学习（RL）驱动的推理模型，解决了模型中的重复性和可读性问题。在 RL 之前，DeepSeek-R1 引入了冷启动数据，进一步优化了推理性能。它在数学、代码和推理任务中与 OpenAI-o1 表现相当，并且通过精心设计的训练方法，提升了整体效果。"}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B 是基于 Llama-3.3-70B-Instruct 经过蒸馏训练得到的模型。该模型是 DeepSeek-R1 系列的一部分，通过使用 DeepSeek-R1 生成的样本进行微调，在数学、编程和推理等多个领域展现出优秀的性能。"}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B 是基于 Qwen2.5-14B 通过知识蒸馏得到的模型。该模型使用 DeepSeek-R1 生成的 80 万个精选样本进行微调，展现出优秀的推理能力。"}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B 是基于 Qwen2.5-32B 通过知识蒸馏得到的模型。该模型使用 DeepSeek-R1 生成的 80 万个精选样本进行微调，在数学、编程和推理等多个领域展现出卓越的性能。"}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite 全新一代轻量版模型，极致响应速度，效果与时延均达到全球一流水平。"}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k 基于 Doubao-1.5-Pro 全面升级版，整体效果大幅提升 10%。支持 256k 上下文窗口的推理，输出长度支持最大 12k tokens。更高性能、更大窗口、超高性价比，适用于更广泛的应用场景。"}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro 全新一代主力模型，性能全面升级，在知识、代码、推理、等方面表现卓越。"}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5全新深度思考模型，在数学、编程、科学推理等专业领域及创意写作等通用任务中表现突出，在AIME 2024、Codeforces、GPQA等多项权威基准上达到或接近业界第一梯队水平。支持128k上下文窗口，16k输出。"}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5全新深度思考模型 (m 版本自带原生多模态深度推理能力)，在数学、编程、科学推理等专业领域及创意写作等通用任务中表现突出，在AIME 2024、Codeforces、GPQA等多项权威基准上达到或接近业界第一梯队水平。支持128k上下文窗口，16k输出。"}, "doubao-1.5-thinking-vision-pro": {"description": "全新视觉深度思考模型，具备更强的通用多模态理解和推理能力，在 59 个公开评测基准中的 37 个上取得 SOTA 表现。"}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS 是一款原生面向图形界面交互（GUI）的Agent模型。通过感知、推理和行动等类人的能力，与 GUI 进行无缝交互。"}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite 全新升级的多模态大模型，支持任意分辨率和极端长宽比图像识别，增强视觉推理、文档识别、细节信息理解和指令遵循能力。支持 128k 上下文窗口，输出长度支持最大 16k tokens。"}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro 全新升级的多模态大模型，支持任意分辨率和极端长宽比图像识别，增强视觉推理、文档识别、细节信息理解和指令遵循能力。"}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro 全新升级的多模态大模型，支持任意分辨率和极端长宽比图像识别，增强视觉推理、文档识别、细节信息理解和指令遵循能力。"}, "doubao-lite-128k": {"description": "拥有极致的响应速度，更好的性价比，为客户不同场景提供更灵活的选择。支持 128k 上下文窗口的推理和精调。"}, "doubao-lite-32k": {"description": "拥有极致的响应速度，更好的性价比，为客户不同场景提供更灵活的选择。支持 32k 上下文窗口的推理和精调。"}, "doubao-lite-4k": {"description": "拥有极致的响应速度，更好的性价比，为客户不同场景提供更灵活的选择。支持 4k 上下文窗口的推理和精调。"}, "doubao-pro-256k": {"description": "效果最好的主力模型，适合处理复杂任务，在参考问答、总结摘要、创作、文本分类、角色扮演等场景都有很好的效果。支持 256k 上下文窗口的推理和精调。"}, "doubao-pro-32k": {"description": "效果最好的主力模型，适合处理复杂任务，在参考问答、总结摘要、创作、文本分类、角色扮演等场景都有很好的效果。支持 32k 上下文窗口的推理和精调。"}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6全新多模态深度思考模型，同时支持auto/thinking/non-thinking三种思考模式。 non-thinking模式下，模型效果对比Doubao-1.5-pro/250115大幅提升。支持 256k 上下文窗口，输出长度支持最大 16k tokens。"}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash推理速度极致的多模态深度思考模型，TPOT仅需10ms； 同时支持文本和视觉理解，文本理解能力超过上一代lite，视觉理解比肩友商pro系列模型。支持 256k 上下文窗口，输出长度支持最大 16k tokens。"}, "doubao-seed-1.6-thinking": {"description": "Doubao-Seed-1.6-thinking模型思考能力大幅强化， 对比Doubao-1.5-thinking-pro，在Coding、Math、 逻辑推理等基础能力上进一步提升， 支持视觉理解。 支持 256k 上下文窗口，输出长度支持最大 16k tokens。"}, "doubao-seedream-3-0-t2i-250415": {"description": "Doubao图片生成模型由字节跳动 Seed 团队研发，支持文字与图片输入，提供高可控、高质量的图片生成体验。基于文本提示词生成图片。"}, "doubao-vision-lite-32k": {"description": "Doubao-vision 模型是豆包推出的多模态大模型，具备强大的图片理解与推理能力，以及精准的指令理解能力。模型在图像文本信息抽取、基于图像的推理任务上有展现出了强大的性能，能够应用于更复杂、更广泛的视觉问答任务。"}, "doubao-vision-pro-32k": {"description": "Doubao-vision 模型是豆包推出的多模态大模型，具备强大的图片理解与推理能力，以及精准的指令理解能力。模型在图像文本信息抽取、基于图像的推理任务上有展现出了强大的性能，能够应用于更复杂、更广泛的视觉问答任务。"}, "emohaa": {"description": "Emohaa 是心理模型，具备专业咨询能力，帮助用户理解情感问题。"}, "ernie-3.5-128k": {"description": "百度自研的旗舰级大规模⼤语⾔模型，覆盖海量中英文语料，具有强大的通用能力，可满足绝大部分对话问答、创作生成、插件应用场景要求；支持自动对接百度搜索插件，保障问答信息时效。"}, "ernie-3.5-8k": {"description": "百度自研的旗舰级大规模⼤语⾔模型，覆盖海量中英文语料，具有强大的通用能力，可满足绝大部分对话问答、创作生成、插件应用场景要求；支持自动对接百度搜索插件，保障问答信息时效。"}, "ernie-3.5-8k-preview": {"description": "百度自研的旗舰级大规模⼤语⾔模型，覆盖海量中英文语料，具有强大的通用能力，可满足绝大部分对话问答、创作生成、插件应用场景要求；支持自动对接百度搜索插件，保障问答信息时效。"}, "ernie-4.0-8k-latest": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，相较ERNIE 3.5实现了模型能力全面升级，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。"}, "ernie-4.0-8k-preview": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，相较ERNIE 3.5实现了模型能力全面升级，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。"}, "ernie-4.0-turbo-128k": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，综合效果表现出色，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。相较于ERNIE 4.0在性能表现上更优秀"}, "ernie-4.0-turbo-8k-latest": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，综合效果表现出色，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。相较于ERNIE 4.0在性能表现上更优秀"}, "ernie-4.0-turbo-8k-preview": {"description": "百度自研的旗舰级超大规模⼤语⾔模型，综合效果表现出色，广泛适用于各领域复杂任务场景；支持自动对接百度搜索插件，保障问答信息时效。相较于ERNIE 4.0在性能表现上更优秀"}, "ernie-4.5-8k-preview": {"description": "文心大模型4.5是百度自主研发的新一代原生多模态基础大模型，通过多个模态联合建模实现协同优化，多模态理解能力优秀；具备更精进的语言能力，理解、生成、逻辑、记忆能力全面提升，去幻觉、逻辑推理、代码能力显著提升。"}, "ernie-4.5-turbo-128k": {"description": "文心4.5 Turbo在去幻觉、逻辑推理和代码能力等方面也有着明显增强。对比文心4.5，速度更快、价格更低。模型能力全面提升，更好满足多轮长历史对话处理、长文档理解问答任务。"}, "ernie-4.5-turbo-32k": {"description": "文心4.5 Turbo在去幻觉、逻辑推理和代码能力等方面也有着明显增强。对比文心4.5，速度更快、价格更低。文本创作、知识问答等能力提升显著。输出长度及整句时延相较ERNIE 4.5有所增加。"}, "ernie-4.5-turbo-vl-32k": {"description": "文心一言大模型全新版本，图片理解、创作、翻译、代码等能力显著提升，首次支持32K上下文长度，首Token时延显著降低。"}, "ernie-char-8k": {"description": "百度自研的垂直场景大语言模型，适合游戏NPC、客服对话、对话角色扮演等应用场景，人设风格更为鲜明、一致，指令遵循能力更强，推理性能更优。"}, "ernie-char-fiction-8k": {"description": "百度自研的垂直场景大语言模型，适合游戏NPC、客服对话、对话角色扮演等应用场景，人设风格更为鲜明、一致，指令遵循能力更强，推理性能更优。"}, "ernie-irag-edit": {"description": "百度自研的ERNIE iRAG Edit图像编辑模型支持基于图片进行erase（消除对象）、repaint（重绘对象）、variation（生成变体）等操作。"}, "ernie-lite-8k": {"description": "ERNIE Lite是百度自研的轻量级大语言模型，兼顾优异的模型效果与推理性能，适合低算力AI加速卡推理使用。"}, "ernie-lite-pro-128k": {"description": "百度自研的轻量级大语言模型，兼顾优异的模型效果与推理性能，效果比ERNIE Lite更优，适合低算力AI加速卡推理使用。"}, "ernie-novel-8k": {"description": "百度自研通用大语言模型，在小说续写能力上有明显优势，也可用在短剧、电影等场景。"}, "ernie-speed-128k": {"description": "百度2024年最新发布的自研高性能大语言模型，通用能力优异，适合作为基座模型进行精调，更好地处理特定场景问题，同时具备极佳的推理性能。"}, "ernie-speed-pro-128k": {"description": "百度2024年最新发布的自研高性能大语言模型，通用能力优异，效果比ERNIE Speed更优，适合作为基座模型进行精调，更好地处理特定场景问题，同时具备极佳的推理性能。"}, "ernie-tiny-8k": {"description": "ERNIE Tiny是百度自研的超高性能大语言模型，部署与精调成本在文心系列模型中最低。"}, "ernie-x1-32k": {"description": "具备更强的理解、规划、反思、进化能力。作为能力更全面的深度思考模型，文心X1兼备准确、创意和文采，在中文知识问答、文学创作、文稿写作、日常对话、逻辑推理、复杂计算及工具调用等方面表现尤为出色。"}, "ernie-x1-32k-preview": {"description": "文心大模型X1具备更强的理解、规划、反思、进化能力。作为能力更全面的深度思考模型，文心X1兼备准确、创意和文采，在中文知识问答、文学创作、文稿写作、日常对话、逻辑推理、复杂计算及工具调用等方面表现尤为出色。"}, "ernie-x1-turbo-32k": {"description": "与ERNIE-X1-32K相比，模型效果和性能更好。"}, "flux-1-schnell": {"description": "由 Black Forest Labs 开发的 120 亿参数文生图模型，采用潜在对抗扩散蒸馏技术，能够在 1 到 4 步内生成高质量图像。该模型性能媲美闭源替代品，并在 Apache-2.0 许可证下发布，适用于个人、科研和商业用途。"}, "flux-dev": {"description": "FLUX.1 [dev] 是一款面向非商业应用的开源权重、精炼模型。FLUX.1 [dev] 在保持了与FLUX专业版相近的图像质量和指令遵循能力的同时，具备更高的运行效率。相较于同尺寸的标准模型，它在资源利用上更为高效。"}, "flux-kontext/dev": {"description": "Frontier image editing model."}, "flux-merged": {"description": "FLUX.1-merged 模型结合了 \"DEV\" 在开发阶段探索的深度特性和 \"Schnell\" 所代表的高速执行优势。通过这一举措，FLUX.1-merged 不仅提升了模型的性能界限，还拓宽了其应用范围。"}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] 能够处理文本和参考图像作为输入，无缝实现目标性的局部编辑和复杂的整体场景变换。"}, "flux-schnell": {"description": "FLUX.1 [schnell] 作为目前开源最先进的少步模型，不仅超越了同类竞争者，甚至还优于诸如 Midjourney v6.0 和 DALL·E 3 (HD) 等强大的非精馏模型。该模型经过专门微调，以保留预训练阶段的全部输出多样性，相较于当前市场上的最先进模型，FLUX.1 [schnell] 显著提升了在视觉质量、指令遵从、尺寸/比例变化、字体处理及输出多样性等方面的可能，为用户带来更为丰富多样的创意图像生成体验。"}, "flux.1-schnell": {"description": "具有120亿参数的修正流变换器，能够根据文本描述生成图像。"}, "flux/schnell": {"description": "FLUX.1 [schnell] 是一个拥有120亿参数的流式转换器模型，能够在1到4步内从文本生成高质量图像，适合个人和商业用途。"}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning) 提供稳定并可调优的性能，是复杂任务解决方案的理想选择。"}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning) 提供出色的多模态支持，专注于复杂任务的有效解决。"}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro 是Google的高性能AI模型，专为广泛任务扩展而设计。"}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 是一款高效的多模态模型，支持广泛应用的扩展。"}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 是一款高效的多模态模型，支持广泛应用的扩展。"}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B 是一款高效的多模态模型，支持广泛应用的扩展。"}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 是最新的实验性模型，在文本和多模态用例中都有显著的性能提升。"}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B 是一款高效的多模态模型，支持广泛应用的扩展。"}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 提供了优化后的多模态处理能力，适用多种复杂任务场景。"}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash 是Google最新的多模态AI模型，具备快速处理能力，支持文本、图像和视频输入，适用于多种任务的高效扩展。"}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 是可扩展的多模态AI解决方案，支持广泛的复杂任务。"}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 是最新的生产就绪模型，提供更高质量的输出，特别在数学、长上下文和视觉任务方面有显著提升。"}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 提供出色的多模态处理能力，为应用开发带来更大灵活性。"}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 结合最新优化技术，带来更高效的多模态数据处理能力。"}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro 支持高达200万个tokens，是中型多模态模型的理想选择，适用于复杂任务的多方面支持。"}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。"}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。"}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。"}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash 实验模型，支持图像生成"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。"}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。"}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash 预览模型，支持图像生成"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash 是 Google 性价比最高的模型，提供全面的功能。"}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite 是 Google 最小、性价比最高的模型，专为大规模使用而设计。"}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview 是 Google 最小、性价比最高的模型，专为大规模使用而设计。"}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。"}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。"}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。"}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。"}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。"}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。"}, "gemma-7b-it": {"description": "Gemma 7B 适合中小规模任务处理，兼具成本效益。"}, "gemma2": {"description": "Gemma 2 是 Google 推出的高效模型，涵盖从小型应用到复杂数据处理的多种应用场景。"}, "gemma2-9b-it": {"description": "Gemma 2 9B 是一款优化用于特定任务和工具整合的模型。"}, "gemma2:27b": {"description": "Gemma 2 是 Google 推出的高效模型，涵盖从小型应用到复杂数据处理的多种应用场景。"}, "gemma2:2b": {"description": "Gemma 2 是 Google 推出的高效模型，涵盖从小型应用到复杂数据处理的多种应用场景。"}, "generalv3": {"description": "Spark Pro 是一款为专业领域优化的高性能大语言模型，专注数学、编程、医疗、教育等多个领域，并支持联网搜索及内置天气、日期等插件。其优化后模型在复杂知识问答、语言理解及高层次文本创作中展现出色表现和高效性能，是适合专业应用场景的理想选择。"}, "generalv3.5": {"description": "Spark Max 为功能最为全面的版本，支持联网搜索及众多内置插件。其全面优化的核心能力以及系统角色设定和函数调用功能，使其在各种复杂应用场景中的表现极为优异和出色。"}, "glm-4": {"description": "GLM-4 是发布于2024年1月的旧旗舰版本，目前已被更强的 GLM-4-0520 取代。"}, "glm-4-0520": {"description": "GLM-4-0520 是最新模型版本，专为高度复杂和多样化任务设计，表现卓越。"}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat 是智谱 AI 推出的最新一代预训练模型 GLM-4-9B 的人类偏好对齐版本。"}, "glm-4-air": {"description": "GLM-4-Air 是性价比高的版本，性能接近GLM-4，提供快速度和实惠的价格。"}, "glm-4-air-250414": {"description": "GLM-4-Air 是性价比高的版本，性能接近GLM-4，提供快速度和实惠的价格。"}, "glm-4-airx": {"description": "GLM-4-AirX 提供 GLM-4-Air 的高效版本，推理速度可达其2.6倍。"}, "glm-4-alltools": {"description": "GLM-4-AllTools 是一个多功能智能体模型，优化以支持复杂指令规划与工具调用，如网络浏览、代码解释和文本生成，适用于多任务执行。"}, "glm-4-flash": {"description": "GLM-4-Flash 是处理简单任务的理想选择，速度最快且免费。"}, "glm-4-flash-250414": {"description": "GLM-4-Flash 是处理简单任务的理想选择，速度最快且免费。"}, "glm-4-flashx": {"description": "GLM-4-FlashX 是Flash的增强版本，超快推理速度。"}, "glm-4-long": {"description": "GLM-4-Long 支持超长文本输入，适合记忆型任务与大规模文档处理。"}, "glm-4-plus": {"description": "GLM-4-Plus 作为高智能旗舰，具备强大的处理长文本和复杂任务的能力，性能全面提升。"}, "glm-4.1v-thinking-flash": {"description": "GLM-4.1V-Thinking 系列模型是目前已知10B级别的VLM模型中性能最强的视觉模型，融合了同级别SOTA的各项视觉语言任务，包括视频理解、图片问答、学科解题、OCR文字识别、文档和图表解读、GUI Agent、前端网页Coding、Grounding等，多项任务能力甚至超过8倍参数量的Qwen2.5-VL-72B。通过领先的强化学习技术，模型掌握了通过思维链推理的方式提升回答的准确性和丰富度，从最终效果和可解释性等维度都显著超过传统的非thinking模型。"}, "glm-4.1v-thinking-flashx": {"description": "GLM-4.1V-Thinking 系列模型是目前已知10B级别的VLM模型中性能最强的视觉模型，融合了同级别SOTA的各项视觉语言任务，包括视频理解、图片问答、学科解题、OCR文字识别、文档和图表解读、GUI Agent、前端网页Coding、Grounding等，多项任务能力甚至超过8倍参数量的Qwen2.5-VL-72B。通过领先的强化学习技术，模型掌握了通过思维链推理的方式提升回答的准确性和丰富度，从最终效果和可解释性等维度都显著超过传统的非thinking模型。"}, "glm-4.5": {"description": "智谱最新旗舰模型，支持思考模式切换，综合能力达到开源模型的 SOTA 水平，上下文长度可达128K。"}, "glm-4.5-air": {"description": "GLM-4.5 的轻量版，兼顾性能与性价比，可灵活切换混合思考模型。"}, "glm-4.5-airx": {"description": "GLM-4.5-Air 的极速版，响应速度更快，专为大规模高速度需求打造。"}, "glm-4.5-flash": {"description": "GLM-4.5 的免费版，推理、代码、智能体等任务表现出色。"}, "glm-4.5-x": {"description": "GLM-4.5 的极速版，在性能强劲的同时，生成速度可达 100 tokens/秒。"}, "glm-4v": {"description": "GLM-4V 提供强大的图像理解与推理能力，支持多种视觉任务。"}, "glm-4v-flash": {"description": "GLM-4V-Flash 专注于高效的单一图像理解，适用于快速图像解析的场景，例如实时图像分析或批量图像处理。"}, "glm-4v-plus": {"description": "GLM-4V-Plus 具备对视频内容及多图片的理解能力，适合多模态任务。"}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus 具备对视频内容及多图片的理解能力，适合多模态任务。"}, "glm-z1-air": {"description": "推理模型: 具备强大推理能力，适用于需要深度推理的任务。"}, "glm-z1-airx": {"description": "极速推理：具有超快的推理速度和强大的推理效果。"}, "glm-z1-flash": {"description": "GLM-Z1 系列具备强大的复杂推理能力，在逻辑推理、数学、编程等领域表现优异。"}, "glm-z1-flashx": {"description": "高速低价：Flash增强版本，超快推理速度，更快并发保障。"}, "glm-zero-preview": {"description": "GLM-Zero-Preview具备强大的复杂推理能力，在逻辑推理、数学、编程等领域表现优异。"}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。"}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental 是 Google 最新的实验性多模态AI模型，与历史版本相比有一定的质量提升，特别是对于世界知识、代码和长上下文。"}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash 是 Google 最先进的主力模型，专为高级推理、编码、数学和科学任务而设计。它包含内置的“思考”能力，使其能够提供具有更高准确性和细致上下文处理的响应。\n\n注意：此模型有两个变体：思考和非思考。输出定价根据思考能力是否激活而有显著差异。如果您选择标准变体（不带“:thinking”后缀），模型将明确避免生成思考令牌。\n\n要利用思考能力并接收思考令牌，您必须选择“:thinking”变体，这将产生更高的思考输出定价。\n\n此外，Gemini 2.5 Flash 可通过“推理最大令牌数”参数进行配置，如文档中所述 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash 是 Google 最先进的主力模型，专为高级推理、编码、数学和科学任务而设计。它包含内置的“思考”能力，使其能够提供具有更高准确性和细致上下文处理的响应。\n\n注意：此模型有两个变体：思考和非思考。输出定价根据思考能力是否激活而有显著差异。如果您选择标准变体（不带“:thinking”后缀），模型将明确避免生成思考令牌。\n\n要利用思考能力并接收思考令牌，您必须选择“:thinking”变体，这将产生更高的思考输出定价。\n\n此外，Gemini 2.5 Flash 可通过“推理最大令牌数”参数进行配置，如文档中所述 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash 是 Google 最先进的主力模型，专为高级推理、编码、数学和科学任务而设计。它包含内置的“思考”能力，使其能够提供具有更高准确性和细致上下文处理的响应。\n\n注意：此模型有两个变体：思考和非思考。输出定价根据思考能力是否激活而有显著差异。如果您选择标准变体（不带“:thinking”后缀），模型将明确避免生成思考令牌。\n\n要利用思考能力并接收思考令牌，您必须选择“:thinking”变体，这将产生更高的思考输出定价。\n\n此外，Gemini 2.5 Flash 可通过“推理最大令牌数”参数进行配置，如文档中所述 (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)。"}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。"}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。"}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash 提供了优化后的多模态处理能力，适用多种复杂任务场景。"}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro 结合最新优化技术，带来更高效的多模态数据处理能力。"}, "google/gemma-2-27b": {"description": "Gemma 2 是 Google 推出的高效模型，涵盖从小型应用到复杂数据处理的多种应用场景。"}, "google/gemma-2-27b-it": {"description": "Gemma 2 27B 是一款通用大语言模型，具有优异的性能和广泛的应用场景。"}, "google/gemma-2-2b-it": {"description": "面向边缘应用的高级小型语言生成 AI 模型。"}, "google/gemma-2-9b": {"description": "Gemma 2 是 Google 推出的高效模型，涵盖从小型应用到复杂数据处理的多种应用场景。"}, "google/gemma-2-9b-it": {"description": "Gemma 2 9B 由Google开发，提供高效的指令响应和综合能力。"}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 是Google轻量化的开源文本模型系列。"}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) 提供基本的指令处理能力，适合轻量级应用。"}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B 是谷歌的一款开源语言模型，以其在效率和性能方面设立了新的标准。"}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B 是谷歌的一款开源语言模型，以其在效率和性能方面设立了新的标准。"}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125"}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125"}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125"}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，对指令遵循的优化"}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo，OpenAI提供的高效模型，适用于聊天和文本生成任务，支持并行函数调用。"}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k，高容量文本生成模型，适合复杂任务。"}, "gpt-4": {"description": "GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。"}, "gpt-4-0125-preview": {"description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。"}, "gpt-4-0613": {"description": "GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。"}, "gpt-4-1106-preview": {"description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。"}, "gpt-4-32k": {"description": "GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。"}, "gpt-4-32k-0613": {"description": "GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。"}, "gpt-4-turbo": {"description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。"}, "gpt-4-turbo-2024-04-09": {"description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。"}, "gpt-4-turbo-preview": {"description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。"}, "gpt-4-vision-preview": {"description": "GPT-4 视觉预览版，专为图像分析和处理任务设计。"}, "gpt-4.1": {"description": "GPT-4.1 是我们用于复杂任务的旗舰模型。它非常适合跨领域解决问题。"}, "gpt-4.1-mini": {"description": "GPT-4.1 mini 提供了智能、速度和成本之间的平衡，使其成为许多用例中有吸引力的模型。"}, "gpt-4.1-nano": {"description": "GPT-4.1 nano 是最快，最具成本效益的GPT-4.1模型。"}, "gpt-4.5-preview": {"description": "GPT-4.5 的研究预览版，它是我们迄今为止最大、最强大的 GPT 模型。它拥有广泛的世界知识，并能更好地理解用户意图，使其在创造性任务和自主规划方面表现出色。GPT-4.5 可接受文本和图像输入，并生成文本输出（包括结构化输出）。支持关键的开发者功能，如函数调用、批量 API 和流式输出。在需要创造性、开放式思考和对话的任务（如写作、学习或探索新想法）中，GPT-4.5 表现尤为出色。知识截止日期为 2023 年 10 月。"}, "gpt-4o": {"description": "ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。"}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。"}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。"}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。"}, "gpt-4o-audio-preview": {"description": "GPT-4o Audio 模型，支持音频输入输出"}, "gpt-4o-mini": {"description": "GPT-4o mini是OpenAI在GPT-4 Omni之后推出的最新模型，支持图文输入并输出文本。作为他们最先进的小型模型，它比其他近期的前沿模型便宜很多，并且比GPT-3.5 Turbo便宜超过60%。它保持了最先进的智能，同时具有显著的性价比。GPT-4o mini在MMLU测试中获得了 82% 的得分，目前在聊天偏好上排名高于 GPT-4。"}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Audio 模型，支持音频输入输出"}, "gpt-4o-mini-realtime-preview": {"description": "GPT-4o-mini 实时版本，支持音频和文本实时输入输出"}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini 搜索预览版是一个专门训练用于理解和执行网页搜索查询的模型，使用的是 Chat Completions API。除了令牌费用之外，网页搜索查询还会按每次工具调用收取费用。"}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe 是一种使用 GPT-4o 转录音频的语音转文本模型。与原始 Whisper 模型相比，它提高了单词错误率，并提高了语言识别和准确性。使用它来获得更准确的转录。"}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS 是一个基于 GPT-4o mini 构建的文本转语音模型，这是一种快速且强大的语言模型。使用它可以将文本转换为自然听起来的语音文本。最大输入标记数为 2000。"}, "gpt-4o-realtime-preview": {"description": "GPT-4o 实时版本，支持音频和文本实时输入输出"}, "gpt-4o-realtime-preview-2024-10-01": {"description": "GPT-4o 实时版本，支持音频和文本实时输入输出"}, "gpt-4o-realtime-preview-2025-06-03": {"description": "GPT-4o 实时版本，支持音频和文本实时输入输出"}, "gpt-4o-search-preview": {"description": "GPT-4o 搜索预览版是一个专门训练用于理解和执行网页搜索查询的模型，使用的是 Chat Completions API。除了令牌费用之外，网页搜索查询还会按每次工具调用收取费用。"}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe 是一种使用 GPT-4o 转录音频的语音转文本模型。与原始 Whisper 模型相比，它提高了单词错误率，并提高了语言识别和准确性。使用它来获得更准确的转录。"}, "gpt-image-1": {"description": "ChatGPT 原生多模态图片生成模型"}, "grok-2-1212": {"description": "该模型在准确性、指令遵循和多语言能力方面有所改进。"}, "grok-2-image-1212": {"description": "我们最新的图像生成模型可以根据文本提示生成生动逼真的图像。它在营销、社交媒体和娱乐等领域的图像生成方面表现出色。"}, "grok-2-vision-1212": {"description": "该模型在准确性、指令遵循和多语言能力方面有所改进。"}, "grok-3": {"description": "旗舰级模型，擅长数据提取、编程和文本摘要等企业级应用，拥有金融、医疗、法律和科学等领域的深厚知识。"}, "grok-3-fast": {"description": "旗舰级模型，擅长数据提取、编程和文本摘要等企业级应用，拥有金融、医疗、法律和科学等领域的深厚知识。"}, "grok-3-mini": {"description": "轻量级模型，回话前会先思考。运行快速、智能，适用于不需要深层领域知识的逻辑任务，并能获取原始的思维轨迹。"}, "grok-3-mini-fast": {"description": "轻量级模型，回话前会先思考。运行快速、智能，适用于不需要深层领域知识的逻辑任务，并能获取原始的思维轨迹。"}, "grok-4": {"description": "我们最新最强大的旗舰模型，在自然语言处理、数学计算和推理方面表现卓越 —— 是一款完美的全能型选手。"}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B 是一款合并了多个顶尖模型的创意与智能相结合的语言模型。"}, "hunyuan-a13b": {"description": "混元第一个混合推理模型，hunyuan-standard-256K 的升级版本，总参数80B，激活13B，默认是慢思考模式，支持通过参数或者指令进行快慢思考模式切换，慢快思考切换方式为 query 前加/ no_think；整体能力相对上一代全面提升，特别数学、科学、长文理解和 Agent 能力提升显著。"}, "hunyuan-code": {"description": "混元最新代码生成模型，经过 200B 高质量代码数据增训基座模型，迭代半年高质量 SFT 数据训练，上下文长窗口长度增大到 8K，五大语言代码生成自动评测指标上位居前列；五大语言10项考量各方面综合代码任务人工高质量评测上，性能处于第一梯队"}, "hunyuan-functioncall": {"description": "混元最新 MOE 架构 FunctionCall 模型，经过高质量的 FunctionCall 数据训练，上下文窗口达 32K，在多个维度的评测指标上处于领先。"}, "hunyuan-large": {"description": "Hunyuan-large 模型总参数量约 389B，激活参数量约 52B，是当前业界参数规模最大、效果最好的 Transformer 架构的开源 MoE 模型。"}, "hunyuan-large-longcontext": {"description": "擅长处理长文任务如文档摘要和文档问答等，同时也具备处理通用文本生成任务的能力。在长文本的分析和生成上表现优异，能有效应对复杂和详尽的长文内容处理需求。"}, "hunyuan-large-vision": {"description": "此模型适用于图文理解场景，是基于混元Large训练的视觉语言大模型，支持任意分辨率多张图片+文本输入，生成文本内容，聚焦图文理解相关任务，在多语言图文理解能力上有显著提升。"}, "hunyuan-lite": {"description": "升级为 MOE 结构，上下文窗口为 256k ，在 NLP，代码，数学，行业等多项评测集上领先众多开源模型。"}, "hunyuan-lite-vision": {"description": "混元最新7B多模态模型，上下文窗口32K，支持中英文场景的多模态对话、图像物体识别、文档表格理解、多模态数学等，在多个维度上评测指标优于7B竞品模型。"}, "hunyuan-pro": {"description": "万亿级参数规模 MOE-32K 长文模型。在各种 benchmark 上达到绝对领先的水平，复杂指令和推理，具备复杂数学能力，支持 functioncall，在多语言翻译、金融法律医疗等领域应用重点优化。"}, "hunyuan-role": {"description": "混元最新版角色扮演模型，混元官方精调训练推出的角色扮演模型，基于混元模型结合角色扮演场景数据集进行增训，在角色扮演场景具有更好的基础效果。"}, "hunyuan-standard": {"description": "采用更优的路由策略，同时缓解了负载均衡和专家趋同的问题。长文方面，大海捞针指标达到99.9%。MOE-32K 性价比相对更高，在平衡效果、价格的同时，可对实现对长文本输入的处理。"}, "hunyuan-standard-256K": {"description": "采用更优的路由策略，同时缓解了负载均衡和专家趋同的问题。长文方面，大海捞针指标达到99.9%。MOE-256K 在长度和效果上进一步突破，极大的扩展了可输入长度。"}, "hunyuan-standard-vision": {"description": "混元最新多模态模型，支持多语种作答，中英文能力均衡。"}, "hunyuan-t1-20250321": {"description": "全面搭建模型文理科能力，长文本信息捕捉能力强。支持推理解答各种难度的数学/逻辑推理/科学/代码等科学问题。"}, "hunyuan-t1-20250403": {"description": "提升项目级别代码生成能力；提升文本生成写作质量；提升文本理解 topic 的多轮、tob 指令遵循和字词理解能力；优化繁简混杂和中英混杂输出问题。"}, "hunyuan-t1-20250529": {"description": "优化文本创作、作文写作，优化代码前端、数学、逻辑推理等理科能力，提升指令遵循能力。"}, "hunyuan-t1-20250711": {"description": "大幅提升高难度数学、逻辑和代码能力，优化模型输出稳定性，提升模型长文能力。"}, "hunyuan-t1-latest": {"description": "业内首个超大规模 Hybrid-Transformer-Mamba 推理模型，扩展推理能力，超强解码速度，进一步对齐人类偏好。"}, "hunyuan-t1-vision": {"description": "混元多模态理解深度思考模型，支持多模态原生长思维链，擅长处理各种图片推理场景，在理科难题上相比快思考模型全面提升。"}, "hunyuan-t1-vision-20250619": {"description": "混元最新版t1-vision多模态理解深度思考模型，支持多模态原生长思维链，相比上一代默认版本模型全面提升。"}, "hunyuan-turbo": {"description": "混元全新一代大语言模型的预览版，采用全新的混合专家模型（MoE）结构，相比hunyuan-pro推理效率更快，效果表现更强。"}, "hunyuan-turbo-20241223": {"description": "本版本优化：数据指令scaling，大幅提升模型通用泛化能力；大幅提升数学、代码、逻辑推理能力；优化文本理解字词理解相关能力；优化文本创作内容生成质量"}, "hunyuan-turbo-latest": {"description": "通用体验优化，包括NLP理解、文本创作、闲聊、知识问答、翻译、领域等；提升拟人性，优化模型情商；提升意图模糊时模型主动澄清能力；提升字词解析类问题的处理能力；提升创作的质量和可互动性；提升多轮体验。"}, "hunyuan-turbo-vision": {"description": "混元新一代视觉语言旗舰大模型，采用全新的混合专家模型（MoE）结构，在图文理解相关的基础识别、内容创作、知识问答、分析推理等能力上相比前一代模型全面提升。"}, "hunyuan-turbos-20250313": {"description": "统一数学解题步骤的风格，加强数学多轮问答。文本创作优化回答风格，去除AI味，增加文采。"}, "hunyuan-turbos-20250416": {"description": "预训练底座升级，增强底座的指令理解及遵循能力；对齐阶段增强数学、代码、逻辑、科学等理科能力；提升文创写作质量、文本理解、翻译准确率、知识问答等文科能力；增强各领域 Agent 能力，重点加强多轮对话理解能力等。"}, "hunyuan-turbos-20250604": {"description": "预训练底座升级，写作、阅读理解能力提升，较大幅度提升代码和理科能力，复杂指令遵循等持续提升。"}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS 混元旗舰大模型最新版本，具备更强的思考能力，更优的体验效果。"}, "hunyuan-turbos-longtext-128k-20250325": {"description": "擅长处理长文任务如文档摘要和文档问答等，同时也具备处理通用文本生成任务的能力。在长文本的分析和生成上表现优异，能有效应对复杂和详尽的长文内容处理需求。"}, "hunyuan-turbos-role-plus": {"description": "混元最新版角色扮演模型，混元官方精调训练推出的角色扮演模型，基于混元模型结合角色扮演场景数据集进行增训，在角色扮演场景具有更好的基础效果。"}, "hunyuan-turbos-vision": {"description": "此模型适用于图文理解场景，是基于混元最新 turbos 的新一代视觉语言旗舰大模型，聚焦图文理解相关任务，包括基于图片的实体识别、知识问答、文案创作、拍照解题等方面，相比前一代模型全面提升。"}, "hunyuan-turbos-vision-20250619": {"description": "混元最新版turbos-vision视觉语言旗舰大模型，在图文理解相关的任务上，包括基于图片的实体识别、知识问答、文案创作、拍照解题等上面相比上一代默认版本模型全面提升。"}, "hunyuan-vision": {"description": "混元最新多模态模型，支持图片+文本输入生成文本内容。"}, "image-01": {"description": "全新图像生成模型，画面表现细腻，支持文生图、图生图"}, "image-01-live": {"description": "图像生成模型，画面表现细腻，支持文生图并进行画风设置"}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 4th generation text-to-image model series"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 4th generation text-to-image model series Ultra version"}, "imagen4/preview": {"description": "Google 最高质量的图像生成模型"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5-7B-Chat 是一个开源的对话模型，基于 InternLM2 架构开发。该 7B 参数规模的模型专注于对话生成任务，支持中英双语交互。模型采用了最新的训练技术，旨在提供流畅、智能的对话体验。InternLM2.5-7B-Chat 适用于各种对话应用场景，包括但不限于智能客服、个人助手等领域"}, "internlm2.5-latest": {"description": "我们仍在维护的老版本模型，经过多轮迭代有着极其优异且稳定的性能，包含 7B、20B 多种模型参数量可选，支持 1M 的上下文长度以及更强的指令跟随和工具调用能力。默认指向我们最新发布的 InternLM2.5 系列模型，当前指向 internlm2.5-20b-chat。"}, "internlm3-latest": {"description": "我们最新的模型系列，有着卓越的推理性能，领跑同量级开源模型。默认指向我们最新发布的 InternLM3 系列模型，当前指向 internlm3-8b-instruct。"}, "internvl2.5-latest": {"description": "我们仍在维护的 InternVL2.5 版本，具备优异且稳定的性能。默认指向我们最新发布的 InternVL2.5 系列模型，当前指向 internvl2.5-78b。"}, "internvl3-latest": {"description": "我们最新发布多模态大模型，具备更强的图文理解能力、长时序图片理解能力，性能比肩顶尖闭源模型。默认指向我们最新发布的 InternVL 系列模型，当前指向 internvl3-78b。"}, "irag-1.0": {"description": "百度自研的iRAG（image based RAG），检索增强的文生图技术，将百度搜索的亿级图片资源跟强大的基础模型能力相结合，就可以生成各种超真实的图片，整体效果远远超过文生图原生系统，去掉了AI味儿，而且成本很低。iRAG具备无幻觉、超真实、立等可取等特点。"}, "jamba-large": {"description": "我们最强大、最先进的模型，专为处理企业级复杂任务而设计，具备卓越的性能。"}, "jamba-mini": {"description": "在同级别中最高效的模型，兼顾速度与质量，具备更小的体积。"}, "jina-deepsearch-v1": {"description": "深度搜索结合了网络搜索、阅读和推理，可进行全面调查。您可以将其视为一个代理，接受您的研究任务 - 它会进行广泛搜索并经过多次迭代，然后才能给出答案。这个过程涉及持续的研究、推理和从各个角度解决问题。这与直接从预训练数据生成答案的标准大模型以及依赖一次性表面搜索的传统 RAG 系统有着根本的不同。"}, "kimi-k2": {"description": "Kimi-K2 是一款Moonshot AI推出的具备超强代码和 Agent 能力的 MoE 架构基础模型，总参数 1T，激活参数 32B。在通用知识推理、编程、数学、Agent 等主要类别的基准性能测试中，K2 模型的性能超过其他主流开源模型。"}, "kimi-k2-0711-preview": {"description": "kimi-k2 是一款具备超强代码和 Agent 能力的 MoE 架构基础模型，总参数 1T，激活参数 32B。在通用知识推理、编程、数学、Agent 等主要类别的基准性能测试中，K2 模型的性能超过其他主流开源模型。"}, "kimi-latest": {"description": "Kimi 智能助手产品使用最新的 Kimi 大模型，可能包含尚未稳定的特性。支持图片理解，同时会自动根据请求的上下文长度选择 8k/32k/128k 模型作为计费模型"}, "kimi-thinking-preview": {"description": "kimi-thinking-preview 模型是月之暗面提供的具有多模态推理能力和通用推理能力的多模态思考模型，它擅长深度推理，帮助解决更多更难的事情"}, "learnlm-1.5-pro-experimental": {"description": "LearnLM 是一个实验性的、特定于任务的语言模型，经过训练以符合学习科学原则，可在教学和学习场景中遵循系统指令，充当专家导师等。"}, "learnlm-2.0-flash-experimental": {"description": "LearnLM 是一个实验性的、特定于任务的语言模型，经过训练以符合学习科学原则，可在教学和学习场景中遵循系统指令，充当专家导师等。"}, "lite": {"description": "Spark Lite 是一款轻量级大语言模型，具备极低的延迟与高效的处理能力，完全免费开放，支持实时在线搜索功能。其快速响应的特性使其在低算力设备上的推理应用和模型微调中表现出色，为用户带来出色的成本效益和智能体验，尤其在知识问答、内容生成及搜索场景下表现不俗。"}, "llama-2-7b-chat": {"description": "Llama2 是由 Meta 开发并开源的大型语言模型（LLM）系列，这是一组从 70 亿到 700 亿参数不同规模、经过预训练和微调的生成式文本模型。架构层面，LLama2 是一个使用优化型转换器架构的自动回归语言模型。调整后的版本使用有监督的微调（SFT）和带有人类反馈的强化学习（RLHF）以对齐人类对有用性和安全性的偏好。Llama2 较 Llama 系列在多种学术数据集上有着更加不俗的表现，为大量其他模型提供了设计和开发的思路。"}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B 提供更强大的AI推理能力，适合复杂应用，支持超多的计算处理并保证高效和准确率。"}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B 是一款高效能模型，提供了快速的文本生成能力，非常适合需要大规模效率和成本效益的应用场景。"}, "llama-3.1-instruct": {"description": "Llama 3.1 指令微调模型针对对话场景进行了优化，在常见的行业基准测试中，超越了许多现有的开源聊天模型。"}, "llama-3.2-11b-vision-instruct": {"description": "在高分辨率图像上表现出色的图像推理能力，适用于视觉理解应用。"}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "llama-3.2-90b-vision-instruct": {"description": "适用于视觉理解代理应用的高级图像推理能力。"}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "llama-3.2-vision-instruct": {"description": "Llama 3.2-Vision 指令微调模型针对视觉识别、图像推理、图像描述和回答与图像相关的常规问题进行了优化。"}, "llama-3.3-70b-instruct": {"description": "Meta 发布的 LLaMA 3.3 多语言大规模语言模型（LLMs）是一个经过预训练和指令微调的生成模型，提供 70B 规模（文本输入/文本输出）。该模型使用超过 15T 的数据进行训练，支持英语、德语、法语、意大利语、葡萄牙语、印地语、西班牙语和泰语，知识更新截止于 2023 年 12 月。"}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 多语言大语言模型 ( LLM ) 是 70B（文本输入/文本输出）中的预训练和指令调整生成模型。 Llama 3.3 指令调整的纯文本模型针对多语言对话用例进行了优化，并且在常见行业基准上优于许多可用的开源和封闭式聊天模型。"}, "llama-3.3-instruct": {"description": "Llama 3.3 指令微调模型针对对话场景进行了优化，在常见的行业基准测试中，超越了许多现有的开源聊天模型。"}, "llama3-70b-8192": {"description": "Meta Llama 3 70B 提供无与伦比的复杂性处理能力，为高要求项目量身定制。"}, "llama3-8b-8192": {"description": "Meta Llama 3 8B 带来优质的推理效能，适合多场景应用需求。"}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use 提供强大的工具调用能力，支持复杂任务的高效处理。"}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use 是针对高效工具使用优化的模型，支持快速并行计算。"}, "llama3.1": {"description": "Llama 3.1 是 Meta 推出的领先模型，支持高达 405B 参数，可应用于复杂对话、多语言翻译和数据分析领域。"}, "llama3.1:405b": {"description": "Llama 3.1 是 Meta 推出的领先模型，支持高达 405B 参数，可应用于复杂对话、多语言翻译和数据分析领域。"}, "llama3.1:70b": {"description": "Llama 3.1 是 Meta 推出的领先模型，支持高达 405B 参数，可应用于复杂对话、多语言翻译和数据分析领域。"}, "llava": {"description": "LLaVA 是结合视觉编码器和 Vicuna 的多模态模型，用于强大的视觉和语言理解。"}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B 提供视觉处理能力融合，通过视觉信息输入生成复杂输出。"}, "llava:13b": {"description": "LLaVA 是结合视觉编码器和 Vicuna 的多模态模型，用于强大的视觉和语言理解。"}, "llava:34b": {"description": "LLaVA 是结合视觉编码器和 Vicuna 的多模态模型，用于强大的视觉和语言理解。"}, "mathstral": {"description": "MathΣtral 专为科学研究和数学推理设计，提供有效的计算能力和结果解释。"}, "max-32k": {"description": "Spark Max 32K 配置了大上下文处理能力，更强的上下文理解和逻辑推理能力，支持32K tokens的文本输入，适用于长文档阅读、私有知识问答等场景"}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct 是由无问芯穹完全自主训练的大语言模型。Megrez-3B-Instruct 旨在通过软硬协同理念，打造一款极速推理、小巧精悍、极易上手的端侧智能解决方案。"}, "meta-llama-3-70b-instruct": {"description": "一个强大的700亿参数模型，在推理、编码和广泛的语言应用方面表现出色。"}, "meta-llama-3-8b-instruct": {"description": "一个多功能的80亿参数模型，针对对话和文本生成任务进行了优化。"}, "meta-llama-3.1-405b-instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "meta-llama-3.1-70b-instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "meta-llama-3.1-8b-instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) 提供优秀的语言处理能力和出色的交互体验。"}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 提供优秀的语言处理能力和出色的交互体验。"}, "meta-llama/Llama-3-70b-chat-hf": {"description": "Llama 3 70B Instruct Reference 是功能强大的聊天模型，支持复杂的对话需求。"}, "meta-llama/Llama-3-8b-chat-hf": {"description": "Llama 3 8B Instruct Reference 提供多语言支持，涵盖丰富的领域知识。"}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 多语言大语言模型 ( LLM ) 是 70B（文本输入/文本输出）中的预训练和指令调整生成模型。 Llama 3.3 指令调整的纯文本模型针对多语言对话用例进行了优化，并且在常见行业基准上优于许多可用的开源和封闭式聊天模型。"}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite 适合需要高效能和低延迟的环境。"}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo 提供卓越的语言理解和生成能力，适合最苛刻的计算任务。"}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite 适合资源受限的环境，提供出色的平衡性能。"}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo 是一款高效能的大语言模型，支持广泛的应用场景。"}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 是 Meta 推出的领先模型，支持高达 405B 参数，可应用于复杂对话、多语言翻译和数据分析领域。"}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "405B 的 Llama 3.1 Turbo 模型，为大数据处理提供超大容量的上下文支持，在超大规模的人工智能应用中表现突出。"}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 是 Meta 推出的领先模型，支持高达 405B 参数，可应用于复杂对话、多语言翻译和数据分析领域。"}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Llama 3.1 70B 模型经过精细调整，适用于高负载应用，量化至FP8提供更高效的计算能力和准确性，确保在复杂场景中的卓越表现。"}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Llama 3.1 8B 模型采用FP8量化，支持高达131,072个上下文标记，是开源模型中的佼佼者，适合复杂任务，表现优异于许多行业基准。"}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct 优化用于高质量对话场景，在各类人类评估中表现优异。"}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct 优化了高质量对话场景，性能优于许多闭源模型。"}, "meta-llama/llama-3.1-70b-instruct": {"description": "Meta最新一代的Llama 3.1模型系列，70B（700亿参数）的指令微调版本针对高质量对话场景进行了优化。在业界评估中，与领先的闭源模型相比，它展现出了强劲的性能。(仅针对企业实名认证通过主体开放）"}, "meta-llama/llama-3.1-8b-instruct": {"description": "Meta最新一代的Llama 3.1模型系列，8B（80亿参数）的指令微调版本特别快速高效。在业界评估中，表现出强劲的性能，超越了很多领先的闭源模型。(仅针对企业实名认证通过主体开放）"}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 提供多语言支持，是业界领先的生成模型之一。"}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 旨在处理结合视觉和文本数据的任务。它在图像描述和视觉问答等任务中表现出色，跨越了语言生成和视觉推理之间的鸿沟。"}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 是 Llama 系列最先进的多语言开源大型语言模型，以极低成本体验媲美 405B 模型的性能。基于 Transformer 结构，并通过监督微调（SFT）和人类反馈强化学习（RLHF）提升有用性和安全性。其指令调优版本专为多语言对话优化，在多项行业基准上表现优于众多开源和封闭聊天模型。知识截止日期为 2023 年 12 月"}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 是 Llama 系列最先进的多语言开源大型语言模型，以极低成本体验媲美 405B 模型的性能。基于 Transformer 结构，并通过监督微调（SFT）和人类反馈强化学习（RLHF）提升有用性和安全性。其指令调优版本专为多语言对话优化，在多项行业基准上表现优于众多开源和封闭聊天模型。知识截止日期为 2023 年 12 月"}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct 是 Llama 3.1 Instruct 模型中最大、最强大的模型，是一款高度先进的对话推理和合成数据生成模型，也可以用作在特定领域进行专业持续预训练或微调的基础。Llama 3.1 提供的多语言大型语言模型 (LLMs) 是一组预训练的、指令调整的生成模型，包括 8B、70B 和 405B 大小 (文本输入/输出)。Llama 3.1 指令调整的文本模型 (8B、70B、405B) 专为多语言对话用例进行了优化，并在常见的行业基准测试中超过了许多可用的开源聊天模型。Llama 3.1 旨在用于多种语言的商业和研究用途。指令调整的文本模型适用于类似助手的聊天，而预训练模型可以适应各种自然语言生成任务。Llama 3.1 模型还支持利用其模型的输出来改进其他模型，包括合成数据生成和精炼。Llama 3.1 是使用优化的变压器架构的自回归语言模型。调整版本使用监督微调 (SFT) 和带有人类反馈的强化学习 (RLHF) 来符合人类对帮助性和安全性的偏好。"}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Meta Llama 3.1 70B Instruct 的更新版，包括扩展的 128K 上下文长度、多语言性和改进的推理能力。Llama 3.1 提供的多语言大型语言模型 (LLMs) 是一组预训练的、指令调整的生成模型，包括 8B、70B 和 405B 大小 (文本输入/输出)。Llama 3.1 指令调整的文本模型 (8B、70B、405B) 专为多语言对话用例进行了优化，并在常见的行业基准测试中超过了许多可用的开源聊天模型。Llama 3.1 旨在用于多种语言的商业和研究用途。指令调整的文本模型适用于类似助手的聊天，而预训练模型可以适应各种自然语言生成任务。Llama 3.1 模型还支持利用其模型的输出来改进其他模型，包括合成数据生成和精炼。Llama 3.1 是使用优化的变压器架构的自回归语言模型。调整版本使用监督微调 (SFT) 和带有人类反馈的强化学习 (RLHF) 来符合人类对帮助性和安全性的偏好。"}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Meta Llama 3.1 8B Instruct 的更新版，包括扩展的 128K 上下文长度、多语言性和改进的推理能力。Llama 3.1 提供的多语言大型语言模型 (LLMs) 是一组预训练的、指令调整的生成模型，包括 8B、70B 和 405B 大小 (文本输入/输出)。Llama 3.1 指令调整的文本模型 (8B、70B、405B) 专为多语言对话用例进行了优化，并在常见的行业基准测试中超过了许多可用的开源聊天模型。Llama 3.1 旨在用于多种语言的商业和研究用途。指令调整的文本模型适用于类似助手的聊天，而预训练模型可以适应各种自然语言生成任务。Llama 3.1 模型还支持利用其模型的输出来改进其他模型，包括合成数据生成和精炼。Llama 3.1 是使用优化的变压器架构的自回归语言模型。调整版本使用监督微调 (SFT) 和带有人类反馈的强化学习 (RLHF) 来符合人类对帮助性和安全性的偏好。"}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 是一款面向开发者、研究人员和企业的开放大型语言模型 (LLM)，旨在帮助他们构建、实验并负责任地扩展他们的生成 AI 想法。作为全球社区创新的基础系统的一部分，它非常适合内容创建、对话 AI、语言理解、研发和企业应用。"}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 是一款面向开发者、研究人员和企业的开放大型语言模型 (LLM)，旨在帮助他们构建、实验并负责任地扩展他们的生成 AI 想法。作为全球社区创新的基础系统的一部分，它非常适合计算能力和资源有限、边缘设备和更快的训练时间。"}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "在高分辨率图像上表现出色的图像推理能力，适用于视觉理解应用。"}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "适用于视觉理解代理应用的高级图像推理能力。"}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 是 Llama 系列最先进的多语言开源大型语言模型，以极低成本体验媲美 405B 模型的性能。基于 Transformer 结构，并通过监督微调（SFT）和人类反馈强化学习（RLHF）提升有用性和安全性。其指令调优版本专为多语言对话优化，在多项行业基准上表现优于众多开源和封闭聊天模型。知识截止日期为 2023 年 12 月"}, "meta/Meta-Llama-3-70B-Instruct": {"description": "一个强大的700亿参数模型，在推理、编码和广泛的语言应用方面表现出色。"}, "meta/Meta-Llama-3-8B-Instruct": {"description": "一个多功能的80亿参数模型，针对对话和文本生成任务进行了优化。"}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1指令调优的文本模型，针对多语言对话用例进行了优化，在许多可用的开源和封闭聊天模型中，在常见行业基准上表现优异。"}, "meta/llama-3.1-405b-instruct": {"description": "高级 LLM，支持合成数据生成、知识蒸馏和推理，适用于聊天机器人、编程和特定领域任务。"}, "meta/llama-3.1-70b-instruct": {"description": "赋能复杂对话，具备卓越的上下文理解、推理能力和文本生成能力。"}, "meta/llama-3.1-8b-instruct": {"description": "先进的最尖端模型，具备语言理解、卓越的推理能力和文本生成能力。"}, "meta/llama-3.2-11b-vision-instruct": {"description": "尖端的视觉-语言模型，擅长从图像中进行高质量推理。"}, "meta/llama-3.2-1b-instruct": {"description": "先进的最尖端小型语言模型，具备语言理解、卓越的推理能力和文本生成能力。"}, "meta/llama-3.2-3b-instruct": {"description": "先进的最尖端小型语言模型，具备语言理解、卓越的推理能力和文本生成能力。"}, "meta/llama-3.2-90b-vision-instruct": {"description": "尖端的视觉-语言模型，擅长从图像中进行高质量推理。"}, "meta/llama-3.3-70b-instruct": {"description": "先进的 LLM，擅长推理、数学、常识和函数调用。"}, "microsoft/Phi-3-medium-128k-instruct": {"description": "相同的Phi-3-medium模型，但具有更大的上下文大小，适用于RAG或少量提示。"}, "microsoft/Phi-3-medium-4k-instruct": {"description": "一个140亿参数模型，质量优于Phi-3-mini，重点关注高质量、推理密集型数据。"}, "microsoft/Phi-3-mini-128k-instruct": {"description": "相同的Phi-3-mini模型，但具有更大的上下文大小，适用于RAG或少量提示。"}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Phi-3家族中最小的成员，针对质量和低延迟进行了优化。"}, "microsoft/Phi-3-small-128k-instruct": {"description": "相同的Phi-3-small模型，但具有更大的上下文大小，适用于RAG或少量提示。"}, "microsoft/Phi-3-small-8k-instruct": {"description": "一个70亿参数模型，质量优于Phi-3-mini，重点关注高质量、推理密集型数据。"}, "microsoft/Phi-3.5-mini-instruct": {"description": "Phi-3-mini模型的更新版。"}, "microsoft/Phi-3.5-vision-instruct": {"description": "Phi-3-vision模型的更新版。"}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 是微软AI提供的语言模型，在复杂对话、多语言、推理和智能助手领域表现尤为出色。"}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B 是微软AI最先进的Wizard模型，显示出极其竞争力的表现。"}, "minicpm-v": {"description": "MiniCPM-V 是 OpenBMB 推出的新一代多模态大模型，具备卓越的 OCR 识别和多模态理解能力，支持广泛的应用场景。"}, "ministral-3b-latest": {"description": "Ministral 3B 是Mistral的世界顶级边缘模型。"}, "ministral-8b-latest": {"description": "Ministral 8B 是Mistral的性价比极高的边缘模型。"}, "mistral": {"description": "Mistral 是 Mistral AI 发布的 7B 模型，适合多变的语言处理需求。"}, "mistral-ai/Mistral-Large-2411": {"description": "Mistral的旗舰模型，适合需要大规模推理能力或高度专业化的复杂任务（合成文本生成、代码生成、RAG或代理）。"}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo是一种尖端的语言模型（LLM），在其尺寸类别中拥有最先进的推理、世界知识和编码能力。"}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small可用于任何需要高效率和低延迟的基于语言的任务。"}, "mistral-large": {"description": "Mixtral Large 是 Mistral 的旗舰模型，结合代码生成、数学和推理的能力，支持 128k 上下文窗口。"}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 是一款先进的稠密大型语言模型（LLM），拥有 1230 亿参数，具备最先进的推理、知识和编码能力。"}, "mistral-large-latest": {"description": "Mistral Large是旗舰大模型，擅长多语言任务、复杂推理和代码生成，是高端应用的理想选择。"}, "mistral-medium-latest": {"description": "Mistral Medium 3 以 8 倍的成本提供最先进的性能，并从根本上简化了企业部署。"}, "mistral-nemo": {"description": "Mistral Nemo 由 Mistral AI 和 NVIDIA 合作推出，是高效性能的 12B 模型。"}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 大型语言模型（LLM）是 Mistral-Nemo-Base-2407 的指令微调版本。"}, "mistral-small": {"description": "Mistral Small可用于任何需要高效率和低延迟的基于语言的任务。"}, "mistral-small-latest": {"description": "Mistral Small是成本效益高、快速且可靠的选项，适用于翻译、摘要和情感分析等用例。"}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct 以高性能著称，适用于多种语言任务。"}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral (7B) Instruct v0.2 提供改进的指令处理能力和更精确的结果。"}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 提供高效的计算能力和自然语言理解，适合广泛的应用。"}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B是一款紧凑但高性能的模型，擅长批量处理和简单任务，如分类和文本生成，具有良好的推理能力。"}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) 是一款超级大语言模型，支持极高的处理需求。"}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral-8x7B Instruct (46.7B) 提供高容量的计算框架，适合大规模数据处理。"}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B是一个稀疏专家模型，利用多个参数提高推理速度，适合处理多语言和代码生成任务。"}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct 是一款兼有速度优化和长上下文支持的高性能行业标准模型。"}, "mistralai/mistral-nemo": {"description": "Mistral Nemo 是多语言支持和高性能编程的7.3B参数模型。"}, "mixtral": {"description": "Mixtral 是 Mistral AI 的专家模型，具有开源权重，并在代码生成和语言理解方面提供支持。"}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B 提供高容错的并行计算能力，适合复杂任务。"}, "mixtral:8x22b": {"description": "Mixtral 是 Mistral AI 的专家模型，具有开源权重，并在代码生成和语言理解方面提供支持。"}, "moonshot-v1-128k": {"description": "Moonshot V1 128K 是一款拥有超长上下文处理能力的模型，适用于生成超长文本，满足复杂的生成任务需求，能够处理多达128,000个tokens的内容，非常适合科研、学术和大型文档生成等应用场景。"}, "moonshot-v1-128k-vision-preview": {"description": "Kimi 视觉模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能够理解图片内容，包括图片文字、图片颜色和物体形状等内容。"}, "moonshot-v1-32k": {"description": "Moonshot V1 32K 提供中等长度的上下文处理能力，能够处理32,768个tokens，特别适合生成各种长文档和复杂对话，应用于内容创作、报告生成和对话系统等领域。"}, "moonshot-v1-32k-vision-preview": {"description": "Kimi 视觉模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能够理解图片内容，包括图片文字、图片颜色和物体形状等内容。"}, "moonshot-v1-8k": {"description": "Moonshot V1 8K 专为生成短文本任务设计，具有高效的处理性能，能够处理8,192个tokens，非常适合简短对话、速记和快速内容生成。"}, "moonshot-v1-8k-vision-preview": {"description": "Kimi 视觉模型（包括 moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview 等）能够理解图片内容，包括图片文字、图片颜色和物体形状等内容。"}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto 可以根据当前上下文占用的 Tokens 数量来选择合适的模型"}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B 是一款开源代码大模型，经过大规模强化学习优化，能输出稳健、可直接投产的补丁。该模型在 SWE-bench Verified 上取得 60.4 % 的新高分，刷新了开源模型在缺陷修复、代码评审等自动化软件工程任务上的纪录。"}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 是一款具备超强代码和 Agent 能力的 MoE 架构基础模型，总参数 1T，激活参数 32B。在通用知识推理、编程、数学、Agent 等主要类别的基准性能测试中，K2 模型的性能超过其他主流开源模型。"}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 是一款具备超强代码和 Agent 能力的 MoE 架构基础模型，总参数 1T，激活参数 32B。在通用知识推理、编程、数学、Agent 等主要类别的基准性能测试中，K2 模型的性能超过其他主流开源模型。"}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B 是 Nous Hermes 2的升级版本，包含最新的内部开发的数据集。"}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B 是由 NVIDIA 定制的大型语言模型，旨在提高 LLM 生成的响应对用户查询的帮助程度。该模型在 Arena Hard、AlpacaEval 2 LC 和 GPT-4-Turbo MT-Bench 等基准测试中表现出色，截至 2024 年 10 月 1 日，在所有三个自动对齐基准测试中排名第一。该模型使用 RLHF（特别是 REINFORCE）、Llama-3.1-Nemotron-70B-Reward 和 HelpSteer2-Preference 提示在 Llama-3.1-70B-Instruct 模型基础上进行训练"}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "独特的语言模型，提供无与伦比的准确性和效率表现。"}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct 是 NVIDIA 定制的大型语言模型，旨在提高 LLM 生成的响应的帮助性。"}, "o1": {"description": "o1是OpenAI新的推理模型，支持图文输入并输出文本，适用于需要广泛通用知识的复杂任务。该模型具有200K上下文和2023年10月的知识截止日期。"}, "o1-mini": {"description": "o1-mini是一款针对编程、数学和科学应用场景而设计的快速、经济高效的推理模型。该模型具有128K上下文和2023年10月的知识截止日期。"}, "o1-preview": {"description": "o1是OpenAI新的推理模型，适用于需要广泛通用知识的复杂任务。该模型具有128K上下文和2023年10月的知识截止日期。"}, "o1-pro": {"description": "o1 系列模型经过强化学习训练，能够在回答前进行思考，并执行复杂的推理任务。o1-pro 模型使用了更多计算资源，以进行更深入的思考，从而持续提供更优质的回答。"}, "o3": {"description": "o3 是一款全能强大的模型，在多个领域表现出色。它为数学、科学、编程和视觉推理任务树立了新标杆。它也擅长技术写作和指令遵循。用户可利用它分析文本、代码和图像，解决多步骤的复杂问题。"}, "o3-deep-research": {"description": "o3-deep-research 是我们最先进的深度研究模型，专为处理复杂的多步骤研究任务而设计。它可以从互联网上搜索和综合信息，也可以通过 MCP 连接器访问并利用你的自有数据。"}, "o3-mini": {"description": "o3-mini 是我们最新的小型推理模型，在与 o1-mini 相同的成本和延迟目标下提供高智能。"}, "o3-pro": {"description": "o3-pro 模型使用更多的计算来更深入地思考并始终提供更好的答案，仅支持 Responses API 下使用。"}, "o4-mini": {"description": "o4-mini 是我们最新的小型 o 系列模型。 它专为快速有效的推理而优化，在编码和视觉任务中表现出极高的效率和性能。"}, "o4-mini-deep-research": {"description": "o4-mini-deep-research 是我们更快速、更实惠的深度研究模型——非常适合处理复杂的多步骤研究任务。它可以从互联网上搜索和综合信息，也可以通过 MCP 连接器访问并利用你的自有数据。"}, "open-codestral-mamba": {"description": "Codestral Mamba是专注于代码生成的Mamba 2语言模型，为先进的代码和推理任务提供强力支持。"}, "open-mistral-7b": {"description": "Mistral 7B是一款紧凑但高性能的模型，擅长批量处理和简单任务，如分类和文本生成，具有良好的推理能力。"}, "open-mistral-nemo": {"description": "Mistral Nemo是一个与Nvidia合作开发的12B模型，提供出色的推理和编码性能，易于集成和替换。"}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B是一个更大的专家模型，专注于复杂任务，提供出色的推理能力和更高的吞吐量。"}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B是一个稀疏专家模型，利用多个参数提高推理速度，适合处理多语言和代码生成任务。"}, "openai/gpt-4.1": {"description": "GPT-4.1 是我们用于复杂任务的旗舰模型。它非常适合跨领域解决问题。"}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini 提供了智能、速度和成本之间的平衡，使其成为许多用例中有吸引力的模型。"}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano 是最快，最具成本效益的GPT-4.1模型。"}, "openai/gpt-4o": {"description": "ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。"}, "openai/gpt-4o-mini": {"description": "GPT-4o mini是OpenAI在GPT-4 Omni之后推出的最新模型，支持图文输入并输出文本。作为他们最先进的小型模型，它比其他近期的前沿模型便宜很多，并且比GPT-3.5 Turbo便宜超过60%。它保持了最先进的智能，同时具有显著的性价比。GPT-4o mini在MMLU测试中获得了 82% 的得分，目前在聊天偏好上排名高于 GPT-4。"}, "openai/o1": {"description": "o1是OpenAI新的推理模型，支持图文输入并输出文本，适用于需要广泛通用知识的复杂任务。该模型具有200K上下文和2023年10月的知识截止日期。"}, "openai/o1-mini": {"description": "o1-mini是一款针对编程、数学和科学应用场景而设计的快速、经济高效的推理模型。该模型具有128K上下文和2023年10月的知识截止日期。"}, "openai/o1-preview": {"description": "o1是OpenAI新的推理模型，适用于需要广泛通用知识的复杂任务。该模型具有128K上下文和2023年10月的知识截止日期。"}, "openai/o3": {"description": "o3 是一款全能强大的模型，在多个领域表现出色。它为数学、科学、编程和视觉推理任务树立了新标杆。它也擅长技术写作和指令遵循。用户可利用它分析文本、代码和图像，解决多步骤的复杂问题。"}, "openai/o3-mini": {"description": "o3-mini 在与 o1-mini 相同的成本和延迟目标下提供高智能。"}, "openai/o3-mini-high": {"description": "o3-mini 高推理等级版，在与 o1-mini 相同的成本和延迟目标下提供高智能。"}, "openai/o4-mini": {"description": "o4-mini 专为快速有效的推理而优化，在编码和视觉任务中表现出极高的效率和性能。"}, "openai/o4-mini-high": {"description": "o4-mini 高推理等级版，专为快速有效的推理而优化，在编码和视觉任务中表现出极高的效率和性能。"}, "openrouter/auto": {"description": "根据上下文长度、主题和复杂性，你的请求将发送到 Llama 3 70B Instruct、Claude 3.5 Sonnet（自我调节）或 GPT-4o。"}, "phi3": {"description": "Phi-3 是微软推出的轻量级开放模型，适用于高效集成和大规模知识推理。"}, "phi3:14b": {"description": "Phi-3 是微软推出的轻量级开放模型，适用于高效集成和大规模知识推理。"}, "pixtral-12b-2409": {"description": "Pixtral 模型在图表和图理解、文档问答、多模态推理和指令遵循等任务上表现出强大的能力，能够以自然分辨率和宽高比摄入图像，还能够在长达 128K 令牌的长上下文窗口中处理任意数量的图像。"}, "pixtral-large-latest": {"description": "Pixtral Large 是一款拥有 1240 亿参数的开源多模态模型，基于 Mistral Large 2 构建。这是我们多模态家族中的第二款模型，展现了前沿水平的图像理解能力。"}, "pro-128k": {"description": "Spark Pro 128K 配置了特大上下文处理能力，能够处理多达128K的上下文信息，特别适合需通篇分析和长期逻辑关联处理的长文内容，可在复杂文本沟通中提供流畅一致的逻辑与多样的引用支持。"}, "qvq-72b-preview": {"description": "QVQ-72B-Preview 是由 Qwen 团队开发的实验性研究模型，专注于提升视觉推理能力。"}, "qvq-max": {"description": "通义千问QVQ视觉推理模型，支持视觉输入及思维链输出，在数学、编程、视觉分析、创作以及通用任务上都表现了更强的能力。"}, "qvq-plus": {"description": "视觉推理模型。支持视觉输入及思维链输出，继qvq-max模型后推出的plus版本，相较于qvq-max模型，qvq-plus系列模型推理速度更快，效果和成本更均衡。"}, "qwen-coder-plus": {"description": "通义千问代码模型。"}, "qwen-coder-turbo": {"description": "通义千问代码模型。"}, "qwen-coder-turbo-latest": {"description": "通义千问代码模型。"}, "qwen-long": {"description": "通义千问超大规模语言模型，支持长文本上下文，以及基于长文档、多文档等多个场景的对话功能。"}, "qwen-math-plus": {"description": "通义千问数学模型是专门用于数学解题的语言模型。"}, "qwen-math-plus-latest": {"description": "通义千问数学模型是专门用于数学解题的语言模型。"}, "qwen-math-turbo": {"description": "通义千问数学模型是专门用于数学解题的语言模型。"}, "qwen-math-turbo-latest": {"description": "通义千问数学模型是专门用于数学解题的语言模型。"}, "qwen-max": {"description": "通义千问千亿级别超大规模语言模型，支持中文、英文等不同语言输入，当前通义千问2.5产品版本背后的API模型。"}, "qwen-omni-turbo": {"description": "Qwen-Omni 系列模型支持输入多种模态的数据，包括视频、音频、图片、文本，并输出音频与文本。"}, "qwen-plus": {"description": "通义千问超大规模语言模型增强版，支持中文、英文等不同语言输入。"}, "qwen-turbo": {"description": "通义千问超大规模语言模型，支持中文、英文等不同语言输入。"}, "qwen-vl-chat-v1": {"description": "通义千问VL支持灵活的交互方式，包括多图、多轮问答、创作等能力的模型。"}, "qwen-vl-max": {"description": "通义千问超大规模视觉语言模型。相比增强版，再次提升视觉推理能力和指令遵循能力，提供更高的视觉感知和认知水平。"}, "qwen-vl-max-latest": {"description": "通义千问超大规模视觉语言模型。相比增强版，再次提升视觉推理能力和指令遵循能力，提供更高的视觉感知和认知水平。"}, "qwen-vl-ocr": {"description": "通义千问OCR是文字提取专有模型，专注于文档、表格、试题、手写体文字等类型图像的文字提取能力。它能够识别多种文字，目前支持的语言有：汉语、英语、法语、日语、韩语、德语、俄语、意大利语、越南语、阿拉伯语。"}, "qwen-vl-plus": {"description": "通义千问大规模视觉语言模型增强版。大幅提升细节识别能力和文字识别能力，支持超百万像素分辨率和任意长宽比规格的图像。"}, "qwen-vl-plus-latest": {"description": "通义千问大规模视觉语言模型增强版。大幅提升细节识别能力和文字识别能力，支持超百万像素分辨率和任意长宽比规格的图像。"}, "qwen-vl-v1": {"description": "以 Qwen-7B 语言模型初始化，添加图像模型，图像输入分辨率为448的预训练模型。"}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2是全新的Qwen大型语言模型系列。Qwen2 7B是一个基于transformer的模型，在语言理解、多语言能力、编程、数学和推理方面表现出色。"}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 是全新的大型语言模型系列，具有更强的理解和生成能力。"}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL 是 Qwen-VL 模型的最新迭代版本，在视觉理解基准测试中达到了最先进的性能，包括 MathVista、DocVQA、RealWorldQA 和 MTVQA 等。Qwen2-VL 能够理解超过 20 分钟的视频，用于高质量的基于视频的问答、对话和内容创作。它还具备复杂推理和决策能力，可以与移动设备、机器人等集成，基于视觉环境和文本指令进行自动操作。除了英语和中文，Qwen2-VL 现在还支持理解图像中不同语言的文本，包括大多数欧洲语言、日语、韩语、阿拉伯语和越南语等"}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct 是阿里云发布的最新大语言模型系列之一。该 72B 模型在编码和数学等领域具有显著改进的能力。该模型还提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升。"}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct 是阿里云发布的最新大语言模型系列之一。该 32B 模型在编码和数学等领域具有显著改进的能力。该模型提供了多语言支持，覆盖超过 29 种语言，包括中文、英文等。模型在指令跟随、理解结构化数据以及生成结构化输出（尤其是 JSON）方面都有显著提升。"}, "qwen/qwen2.5-7b-instruct": {"description": "面向中文和英文的 LLM，针对语言、编程、数学、推理等领域。"}, "qwen/qwen2.5-coder-32b-instruct": {"description": "高级 LLM，支持代码生成、推理和修复，涵盖主流编程语言。"}, "qwen/qwen2.5-coder-7b-instruct": {"description": "强大的中型代码模型，支持 32K 上下文长度，擅长多语言编程。"}, "qwen/qwen3-14b": {"description": "Qwen3-14B 是 Qwen3 系列中一个密集的 148 亿参数因果语言模型，专为复杂推理和高效对话而设计。它支持在用于数学、编程和逻辑推理等任务的“思考”模式与用于通用对话的“非思考”模式之间无缝切换。该模型经过微调，可用于指令遵循、代理工具使用、创意写作以及跨 100 多种语言和方言的多语言任务。它原生处理 32K 令牌上下文，并可使用基于 YaRN 的扩展扩展到 131K 令牌。"}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B 是 Qwen3 系列中一个密集的 148 亿参数因果语言模型，专为复杂推理和高效对话而设计。它支持在用于数学、编程和逻辑推理等任务的“思考”模式与用于通用对话的“非思考”模式之间无缝切换。该模型经过微调，可用于指令遵循、代理工具使用、创意写作以及跨 100 多种语言和方言的多语言任务。它原生处理 32K 令牌上下文，并可使用基于 YaRN 的扩展扩展到 131K 令牌。"}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B 是由 Qwen 开发的 235B 参数专家混合 (MoE) 模型，每次前向传递激活 22B 参数。它支持在用于复杂推理、数学和代码任务的“思考”模式与用于一般对话效率的“非思考”模式之间无缝切换。该模型展示了强大的推理能力、多语言支持（100 多种语言和方言）、高级指令遵循和代理工具调用能力。它原生处理 32K 令牌上下文窗口，并使用基于 YaRN 的扩展扩展到 131K 令牌。"}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B 是由 Qwen 开发的 235B 参数专家混合 (MoE) 模型，每次前向传递激活 22B 参数。它支持在用于复杂推理、数学和代码任务的“思考”模式与用于一般对话效率的“非思考”模式之间无缝切换。该模型展示了强大的推理能力、多语言支持（100 多种语言和方言）、高级指令遵循和代理工具调用能力。它原生处理 32K 令牌上下文窗口，并使用基于 YaRN 的扩展扩展到 131K 令牌。"}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 是 Qwen 大型语言模型系列的最新一代，具有密集和专家混合 (MoE) 架构，在推理、多语言支持和高级代理任务方面表现出色。其在复杂推理的思考模式和高效对话的非思考模式之间无缝切换的独特能力确保了多功能、高质量的性能。\n\nQwen3 显著优于 QwQ 和 Qwen2.5 等先前模型，提供卓越的数学、编码、常识推理、创意写作和交互式对话能力。Qwen3-30B-A3B 变体包含 305 亿个参数（33 亿个激活参数）、48 层、128 个专家（每个任务激活 8 个），并支持高达 131K 令牌上下文（使用 YaRN），为开源模型树立了新标准。"}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 是 Qwen 大型语言模型系列的最新一代，具有密集和专家混合 (MoE) 架构，在推理、多语言支持和高级代理任务方面表现出色。其在复杂推理的思考模式和高效对话的非思考模式之间无缝切换的独特能力确保了多功能、高质量的性能。\n\nQwen3 显著优于 QwQ 和 Qwen2.5 等先前模型，提供卓越的数学、编码、常识推理、创意写作和交互式对话能力。Qwen3-30B-A3B 变体包含 305 亿个参数（33 亿个激活参数）、48 层、128 个专家（每个任务激活 8 个），并支持高达 131K 令牌上下文（使用 YaRN），为开源模型树立了新标准。"}, "qwen/qwen3-32b": {"description": "Qwen3-32B 是 Qwen3 系列中一个密集的 328 亿参数因果语言模型，针对复杂推理和高效对话进行了优化。它支持在用于数学、编码和逻辑推理等任务的“思考”模式与用于更快、通用对话的“非思考”模式之间无缝切换。该模型在指令遵循、代理工具使用、创意写作以及跨 100 多种语言和方言的多语言任务中表现出强大的性能。它原生处理 32K 令牌上下文，并可使用基于 YaRN 的扩展扩展到 131K 令牌。"}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B 是 Qwen3 系列中一个密集的 328 亿参数因果语言模型，针对复杂推理和高效对话进行了优化。它支持在用于数学、编码和逻辑推理等任务的“思考”模式与用于更快、通用对话的“非思考”模式之间无缝切换。该模型在指令遵循、代理工具使用、创意写作以及跨 100 多种语言和方言的多语言任务中表现出强大的性能。它原生处理 32K 令牌上下文，并可使用基于 YaRN 的扩展扩展到 131K 令牌。"}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B 是 Qwen3 系列中一个密集的 82 亿参数因果语言模型，专为推理密集型任务和高效对话而设计。它支持在用于数学、编码和逻辑推理的“思考”模式与用于一般对话的“非思考”模式之间无缝切换。该模型经过微调，可用于指令遵循、代理集成、创意写作以及跨 100 多种语言和方言的多语言使用。它原生支持 32K 令牌上下文窗口，并可通过 YaRN 扩展到 131K 令牌。"}, "qwen2": {"description": "Qwen2 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2-72b-instruct": {"description": "Qwen2 是 Qwen 团队推出的新一代大型语言模型系列。它基于 Transformer 架构，并采用 SwiGLU 激活函数、注意力 QKV 偏置(attention QKV bias)、群组查询注意力(group query attention)、滑动窗口注意力(mixture of sliding window attention)与全注意力的混合等技术。此外，Qwen 团队还改进了适应多种自然语言和代码的分词器。"}, "qwen2-7b-instruct": {"description": "Qwen2 是 Qwen 团队推出的新一代大型语言模型系列。它基于 Transformer 架构，并采用 SwiGLU 激活函数、注意力 QKV 偏置(attention QKV bias)、群组查询注意力(group query attention)、滑动窗口注意力(mixture of sliding window attention)与全注意力的混合等技术。此外，Qwen 团队还改进了适应多种自然语言和代码的分词器。"}, "qwen2.5": {"description": "Qwen2.5 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2.5-14b-instruct": {"description": "通义千问2.5对外开源的14B规模的模型。"}, "qwen2.5-14b-instruct-1m": {"description": "通义千问2.5对外开源的72B规模的模型。"}, "qwen2.5-32b-instruct": {"description": "通义千问2.5对外开源的32B规模的模型。"}, "qwen2.5-72b-instruct": {"description": "通义千问2.5对外开源的72B规模的模型。"}, "qwen2.5-7b-instruct": {"description": "通义千问2.5对外开源的7B规模的模型。"}, "qwen2.5-coder-1.5b-instruct": {"description": "通义千问代码模型开源版。"}, "qwen2.5-coder-14b-instruct": {"description": "通义千问代码模型开源版。"}, "qwen2.5-coder-32b-instruct": {"description": "通义千问代码模型开源版。"}, "qwen2.5-coder-7b-instruct": {"description": "通义千问代码模型开源版。"}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder 是 Qwen 系列中最新的代码专用大型语言模型（前身为 CodeQwen）。"}, "qwen2.5-instruct": {"description": "Qwen2.5 是 Qwen 大型语言模型的最新系列。对于 Qwen2.5，我们发布了多个基础语言模型和指令微调语言模型，参数范围从 5 亿到 72 亿不等。"}, "qwen2.5-math-1.5b-instruct": {"description": "<PERSON><PERSON>-Math 模型具有强大的数学解题能力。"}, "qwen2.5-math-72b-instruct": {"description": "<PERSON><PERSON>-Math 模型具有强大的数学解题能力。"}, "qwen2.5-math-7b-instruct": {"description": "<PERSON><PERSON>-Math 模型具有强大的数学解题能力。"}, "qwen2.5-omni-7b": {"description": "Qwen-Omni 系列模型支持输入多种模态的数据，包括视频、音频、图片、文本，并输出音频与文本。"}, "qwen2.5-vl-32b-instruct": {"description": "Qwen2.5VL系列模型，在math和学科问题解答达到了接近Qwen2.5VL-72B的水平，回复风格面向人类偏好进行大幅调整，尤其是数学、逻辑推理、知识问答等客观类query，模型回复详实程度和格式清晰度明显改善。此版本为32B版本。"}, "qwen2.5-vl-72b-instruct": {"description": "指令跟随、数学、解题、代码整体提升，万物识别能力提升，支持多样格式直接精准定位视觉元素，支持对长视频文件（最长10分钟）进行理解和秒级别的事件时刻定位，能理解时间先后和快慢，基于解析和定位能力支持操控OS或Mobile的Agent，关键信息抽取能力和Json格式输出能力强，此版本为72B版本，本系列能力最强的版本。"}, "qwen2.5-vl-7b-instruct": {"description": "指令跟随、数学、解题、代码整体提升，万物识别能力提升，支持多样格式直接精准定位视觉元素，支持对长视频文件（最长10分钟）进行理解和秒级别的事件时刻定位，能理解时间先后和快慢，基于解析和定位能力支持操控OS或Mobile的Agent，关键信息抽取能力和Json格式输出能力强，此版本为72B版本，本系列能力最强的版本。"}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL 是 Qwen 模型家族中视觉语言模型的最新版本。"}, "qwen2.5:0.5b": {"description": "Qwen2.5 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2.5:1.5b": {"description": "Qwen2.5 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2.5:72b": {"description": "Qwen2.5 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2:0.5b": {"description": "Qwen2 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2:1.5b": {"description": "Qwen2 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen2:72b": {"description": "Qwen2 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen3": {"description": "Qwen3 是阿里巴巴的新一代大规模语言模型，以优异的性能支持多元化的应用需求。"}, "qwen3-0.6b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-1.7b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-14b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-235b-a22b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-235b-a22b-instruct-2507": {"description": "基于Qwen3的非思考模式开源模型，相较上一版本（通义千问3-235B-A22B）主观创作能力与模型安全性均有小幅度提升。"}, "qwen3-235b-a22b-thinking-2507": {"description": "基于Qwen3的思考模式开源模型，相较上一版本（通义千问3-235B-A22B）逻辑能力、通用能力、知识增强及创作能力均有大幅提升，适用于高难度强推理场景。"}, "qwen3-30b-a3b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-30b-a3b-instruct-2507": {"description": "相较上一版本（Qwen3-30B-A3B）中英文和多语言整体通用能力有大幅提升。主观开放类任务专项优化，显著更加符合用户偏好，能够提供更有帮助性的回复。"}, "qwen3-30b-a3b-thinking-2507": {"description": "基于Qwen3的思考模式开源模型，相较上一版本（通义千问3-30B-A3B）逻辑能力、通用能力、知识增强及创作能力均有大幅提升，适用于高难度强推理场景。"}, "qwen3-32b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-4b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-8b": {"description": "Qwen3是一款能力大幅提升的新一代通义千问大模型，在推理、通用、Agent和多语言等多个核心能力上均达到业界领先水平，并支持思考模式切换。"}, "qwen3-coder-480b-a35b-instruct": {"description": "通义千问代码模型开源版。最新的 qwen3-coder-480b-a35b-instruct 是基于 Qwen3 的代码生成模型，具有强大的Coding Agent能力，擅长工具调用和环境交互，能够实现自主编程、代码能力卓越的同时兼具通用能力。"}, "qwen3-coder-plus": {"description": "通义千问代码模型。最新的 Qwen3-Coder-Plus 系列模型是基于 Qwen3 的代码生成模型，具有强大的Coding Agent能力，擅长工具调用和环境交互，能够实现自主编程，代码能力卓越的同时兼具通用能力。"}, "qwq": {"description": "QwQ 是 Qwen 系列的推理模型。与传统的指令调优模型相比，QwQ 具备思考和推理的能力，能够在下游任务中，尤其是困难问题上，显著提升性能。QwQ-32B 是中型推理模型，能够在与最先进的推理模型（如 DeepSeek-R1、o1-mini）竞争时取得可观的表现。"}, "qwq-32b": {"description": "QwQ 是 Qwen 系列的推理模型。与传统的指令微调模型相比，QwQ 具备思考和推理能力，在下游任务中，尤其是复杂问题上，能够实现显著增强的性能。QwQ-32B 是一款中型推理模型，其性能可与最先进的推理模型（如 DeepSeek-R1、o1-mini）相媲美。"}, "qwq-32b-preview": {"description": "QwQ模型是由 Qwen 团队开发的实验性研究模型，专注于增强 AI 推理能力。"}, "qwq-plus": {"description": "基于 Qwen2.5 模型训练的 QwQ 推理模型，通过强化学习大幅度提升了模型推理能力。模型数学代码等核心指标（AIME 24/25、LiveCodeBench）以及部分通用指标（IFEval、LiveBench等）达到DeepSeek-R1 满血版水平。"}, "qwq_32b": {"description": "Qwen 系列中等规模的推理模型。与传统的指令调优模型相比，具备思考和推理能力的 QwQ 在下游任务中，尤其是在解决难题时，能够显著提升性能。"}, "r1-1776": {"description": "R1-1776 是 DeepSeek R1 模型的一个版本，经过后训练，可提供未经审查、无偏见的事实信息。"}, "solar-mini": {"description": "Solar Mini 是一种紧凑型 LLM，性能优于 GPT-3.5，具备强大的多语言能力，支持英语和韩语，提供高效小巧的解决方案。"}, "solar-mini-ja": {"description": "Solar Mini (Ja) 扩展了 Solar Mini 的能力，专注于日语，同时在英语和韩语的使用中保持高效和卓越性能。"}, "solar-pro": {"description": "Solar Pro 是 Upstage 推出的一款高智能LLM，专注于单GPU的指令跟随能力，IFEval得分80以上。目前支持英语，正式版本计划于2024年11月推出，将扩展语言支持和上下文长度。"}, "sonar": {"description": "基于搜索上下文的轻量级搜索产品，比 Sonar Pro 更快、更便宜。"}, "sonar-deep-research": {"description": "Deep Research 进行全面的专家级研究，并将其综合成可访问、可作的报告。"}, "sonar-pro": {"description": "支持搜索上下文的高级搜索产品，支持高级查询和跟进。"}, "sonar-reasoning": {"description": "支持搜索上下文的高级搜索产品，支持高级查询和跟进。"}, "sonar-reasoning-pro": {"description": "支持搜索上下文的高级搜索产品，支持高级查询和跟进。"}, "stable-diffusion-3-medium": {"description": "由 Stability AI 推出的最新文生图大模型。这一版本在继承了前代的优点上，对图像质量、文本理解和风格多样性等方面进行了显著改进，能够更准确地解读复杂的自然语言提示，并生成更为精确和多样化的图像。"}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large 是一个具有8亿参数的多模态扩散变压器（MMDiT）文本到图像生成模型，具备卓越的图像质量和提示词匹配度，支持生成 100 万像素的高分辨率图像，且能够在普通消费级硬件上高效运行。"}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo 是在 stable-diffusion-3.5-large 的基础上采用对抗性扩散蒸馏（ADD）技术的模型，具备更快的速度。"}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 是以 stable-diffusion-v1.2 检查点的权重进行初始化，并在 \"laion-aesthetics v2 5+\" 上以 512x512 的分辨率进行了595k步的微调，减少了 10% 的文本条件化，以提高无分类器的引导采样。"}, "stable-diffusion-xl": {"description": "stable-diffusion-xl 相比于 v1.5 做了重大的改进，并且与当前开源的文生图 SOTA 模型 midjourney 效果相当。具体改进之处包括： 更大的 unet backbone，是之前的 3 倍； 增加了 refinement 模块用于改善生成图片的质量；更高效的训练技巧等。"}, "stable-diffusion-xl-base-1.0": {"description": "由 Stability AI 开发并开源的文生图大模型，其创意图像生成能力位居行业前列。具备出色的指令理解能力，能够支持反向 Prompt 定义来精确生成内容。"}, "step-1-128k": {"description": "平衡性能与成本，适合一般场景。"}, "step-1-256k": {"description": "具备超长上下文处理能力，尤其适合长文档分析。"}, "step-1-32k": {"description": "支持中等长度的对话，适用于多种应用场景。"}, "step-1-8k": {"description": "小型模型，适合轻量级任务。"}, "step-1-flash": {"description": "高速模型，适合实时对话。"}, "step-1.5v-mini": {"description": "该模型拥有强大的视频理解能力。"}, "step-1o-turbo-vision": {"description": "该模型拥有强大的图像理解能力，在数理、代码领域强于1o。模型比1o更小，输出速度更快。"}, "step-1o-vision-32k": {"description": "该模型拥有强大的图像理解能力。相比于 step-1v 系列模型，拥有更强的视觉性能。"}, "step-1v-32k": {"description": "支持视觉输入，增强多模态交互体验。"}, "step-1v-8k": {"description": "小型视觉模型，适合基本的图文任务。"}, "step-1x-edit": {"description": "该模型专注于图像编辑任务，能够根据用户提供的图片和文本描述，对图片进行修改和增强。支持多种输入格式，包括文本描述和示例图像。模型能够理解用户的意图，并生成符合要求的图像编辑结果。"}, "step-1x-medium": {"description": "该模型拥有强大的图像生成能力，支持文本描述作为输入方式。具备原生的中文支持，能够更好的理解和处理中文文本描述，并且能够更准确地捕捉文本描述中的语义信息，并将其转化为图像特征，从而实现更精准的图像生成。模型能够根据输入生成高分辨率、高质量的图像，并具备一定的风格迁移能力。"}, "step-2-16k": {"description": "支持大规模上下文交互，适合复杂对话场景。"}, "step-2-16k-exp": {"description": "step-2模型的实验版本，包含最新的特性，滚动更新中。不推荐在正式生产环境使用。"}, "step-2-mini": {"description": "基于新一代自研Attention架构MFA的极速大模型，用极低成本达到和step1类似的效果，同时保持了更高的吞吐和更快响应时延。能够处理通用任务，在代码能力上具备特长。"}, "step-2x-large": {"description": "阶跃星辰新一代生图模型,该模型专注于图像生成任务,能够根据用户提供的文本描述,生成高质量的图像。新模型生成图片质感更真实，中英文文字生成能力更强。"}, "step-r1-v-mini": {"description": "该模型是拥有强大的图像理解能力的推理大模型，能够处理图像和文字信息，经过深度思考后输出文本生成文本内容。该模型在视觉推理领域表现突出，同时拥有第一梯队的数学、代码、文本推理能力。上下文长度为100k。"}, "taichu_llm": {"description": "基于海量高质数据训练，具有更强的文本理解、内容创作、对话问答等能力"}, "taichu_o1": {"description": "taichu_o1是新一代推理大模型，通过多模态交互和强化学习实现类人思维链，支持复杂决策推演，在保持高精度输出的同时展现可模型推理的思维路径，适用于策略分析与深度思考等场景。"}, "taichu_vl": {"description": "融合了图像理解、知识迁移、逻辑归因等能力，在图文问答领域表现突出"}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct 参数量800 亿，激活 130 亿参数即可对标更大模型，支持“快思考/慢思考”混合推理；长文理解稳定；经 BFCL-v3 与 τ-Bench 验证，Agent 能力领先；结合 GQA 与多量化格式，实现高效推理。"}, "text-embedding-3-large": {"description": "最强大的向量化模型，适用于英文和非英文任务"}, "text-embedding-3-small": {"description": "高效且经济的新一代 Embedding 模型，适用于知识检索、RAG 应用等场景"}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 是一个 32B 双语（中英）开放权重语言模型，针对代码生成、函数调用和代理式任务进行了优化。它在 15T 高质量和重推理数据上进行了预训练，并使用人类偏好对齐、拒绝采样和强化学习进一步完善。该模型在复杂推理、工件生成和结构化输出任务方面表现出色，在多个基准测试中达到了与 GPT-4o 和 DeepSeek-V3-0324 相当的性能。"}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 是一个 32B 双语（中英）开放权重语言模型，针对代码生成、函数调用和代理式任务进行了优化。它在 15T 高质量和重推理数据上进行了预训练，并使用人类偏好对齐、拒绝采样和强化学习进一步完善。该模型在复杂推理、工件生成和结构化输出任务方面表现出色，在多个基准测试中达到了与 GPT-4o 和 DeepSeek-V3-0324 相当的性能。"}, "thudm/glm-4-9b-chat": {"description": "智谱AI发布的GLM-4系列最新一代预训练模型的开源版本。"}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 是 THUDM 开发的 GLM-4 系列中的 90 亿参数语言模型。GLM-4-9B-0414 使用与其较大的 32B 对应模型相同的强化学习和对齐策略进行训练，相对于其规模实现了高性能，使其适用于仍需要强大语言理解和生成能力的资源受限部署。"}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 是 GLM-4-32B 的增强推理变体，专为深度数学、逻辑和面向代码的问题解决而构建。它应用扩展强化学习（任务特定和基于通用成对偏好）来提高复杂多步骤任务的性能。与基础 GLM-4-32B 模型相比，Z1 显著提升了结构化推理和形式化领域的能力。\n\n该模型支持通过提示工程强制执行“思考”步骤，并为长格式输出提供改进的连贯性。它针对代理工作流进行了优化，并支持长上下文（通过 YaRN）、JSON 工具调用和用于稳定推理的细粒度采样配置。非常适合需要深思熟虑、多步骤推理或形式化推导的用例。"}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 是 GLM-4-32B 的增强推理变体，专为深度数学、逻辑和面向代码的问题解决而构建。它应用扩展强化学习（任务特定和基于通用成对偏好）来提高复杂多步骤任务的性能。与基础 GLM-4-32B 模型相比，Z1 显著提升了结构化推理和形式化领域的能力。\n\n该模型支持通过提示工程强制执行“思考”步骤，并为长格式输出提供改进的连贯性。它针对代理工作流进行了优化，并支持长上下文（通过 YaRN）、JSON 工具调用和用于稳定推理的细粒度采样配置。非常适合需要深思熟虑、多步骤推理或形式化推导的用例。"}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 是由 THUDM 开发的 GLM-4 系列中的 9B 参数语言模型。它采用了最初应用于更大 GLM-Z1 模型的技术，包括扩展强化学习、成对排名对齐以及对数学、代码和逻辑等推理密集型任务的训练。尽管其规模较小，但它在通用推理任务上表现出强大的性能，并在其权重级别中优于许多开源模型。"}, "thudm/glm-z1-rumination-32b": {"description": "GLM Z1 Rumination 32B 是 GLM-4-Z1 系列中的 32B 参数深度推理模型，针对需要长时间思考的复杂、开放式任务进行了优化。它建立在 glm-4-32b-0414 的基础上，增加了额外的强化学习阶段和多阶段对齐策略，引入了旨在模拟扩展认知处理的“反思”能力。这包括迭代推理、多跳分析和工具增强的工作流程，例如搜索、检索和引文感知合成。\n\n该模型在研究式写作、比较分析和复杂问答方面表现出色。它支持用于搜索和导航原语（`search`、`click`、`open`、`finish`）的函数调用，从而可以在代理式管道中使用。反思行为由具有基于规则的奖励塑造和延迟决策机制的多轮循环控制，并以 OpenAI 内部对齐堆栈等深度研究框架为基准。此变体适用于需要深度而非速度的场景。"}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera 通过合并 DeepSeek-R1 和 DeepSeek-V3 (0324) 创建，结合了 R1 的推理能力和 V3 的令牌效率改进。它基于 DeepSeek-MoE Transformer 架构，并针对通用文本生成任务进行了优化。\n\n该模型合并了两个源模型的预训练权重，以平衡推理、效率和指令遵循任务的性能。它根据 MIT 许可证发布，旨在用于研究和商业用途。"}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) 通过高效的策略和模型架构，提供增强的计算能力。"}, "tts-1": {"description": "最新的文本转语音模型，针对实时场景优化速度"}, "tts-1-hd": {"description": "最新的文本转语音模型，针对质量进行优化"}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) 适用于精细化指令任务，提供出色的语言处理能力。"}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet 提升了行业标准，性能超过竞争对手模型和 Claude 3 Opus，在广泛的评估中表现出色，同时具有我们中等层级模型的速度和成本。"}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet 是 Anthropic 最快的下一代模型。与 Claude 3 Haiku 相比，Claude 3.7 Sonnet 在各项技能上都有所提升，并在许多智力基准测试中超越了上一代最大的模型 Claude 3 Opus。"}, "v0-1.0-md": {"description": "v0-1.0-md 模型是通过 v0 API 提供服务的旧版模型"}, "v0-1.5-lg": {"description": "v0-1.5-lg 模型适用于高级思考或推理任务"}, "v0-1.5-md": {"description": "v0-1.5-md 模型适用于日常任务和用户界面（UI）生成"}, "wan2.2-t2i-flash": {"description": "万相2.2极速版，当前最新模型。在创意性、稳定性、写实质感上全面升级，生成速度快，性价比高。"}, "wan2.2-t2i-plus": {"description": "万相2.2专业版，当前最新模型。在创意性、稳定性、写实质感上全面升级，生成细节丰富。"}, "wanx-v1": {"description": "基础文生图模型。对应通义万相官网1.0通用模型。"}, "wanx2.0-t2i-turbo": {"description": "擅长质感人像，速度中等、成本较低。对应通义万相官网2.0极速模型。"}, "wanx2.1-t2i-plus": {"description": "全面升级版本。生成图像细节更丰富，速度稍慢。对应通义万相官网2.1专业模型。"}, "wanx2.1-t2i-turbo": {"description": "全面升级版本。生成速度快、效果全面、综合性价比高。对应通义万相官网2.1极速模型。"}, "whisper-1": {"description": "通用语音识别模型，支持多语言语音识别、语音翻译和语言识别。"}, "wizardlm2": {"description": "WizardLM 2 是微软AI提供的语言模型，在复杂对话、多语言、推理和智能助手领域表现尤为出色。"}, "wizardlm2:8x22b": {"description": "WizardLM 2 是微软AI提供的语言模型，在复杂对话、多语言、推理和智能助手领域表现尤为出色。"}, "x1": {"description": "Spark X1 模型将进一步升级，在原来数学任务国内领先基础上，推理、文本生成、语言理解等通用任务实现效果对标 OpenAI o1 和 DeepSeek R1。"}, "yi-1.5-34b-chat": {"description": "Yi-1.5 是 Yi 的升级版本。 它使用 500B Tokens 的高质量语料库在 Yi 上持续进行预训练，并在 3M 个多样化的微调样本上进行微调。"}, "yi-large": {"description": "全新千亿参数模型，提供超强问答及文本生成能力。"}, "yi-large-fc": {"description": "在 yi-large 模型的基础上支持并强化了工具调用的能力，适用于各种需要搭建 agent 或 workflow 的业务场景。"}, "yi-large-preview": {"description": "初期版本，推荐使用 yi-large（新版本）。"}, "yi-large-rag": {"description": "基于 yi-large 超强模型的高阶服务，结合检索与生成技术提供精准答案，实时全网检索信息服务。"}, "yi-large-turbo": {"description": "超高性价比、卓越性能。根据性能和推理速度、成本，进行平衡性高精度调优。"}, "yi-lightning": {"description": "最新高性能模型，保证高质量输出同时，推理速度大幅提升。"}, "yi-lightning-lite": {"description": "轻量化版本，推荐使用 yi-lightning。"}, "yi-medium": {"description": "中型尺寸模型升级微调，能力均衡，性价比高。深度优化指令遵循能力。"}, "yi-medium-200k": {"description": "200K 超长上下文窗口，提供长文本深度理解和生成能力。"}, "yi-spark": {"description": "小而精悍，轻量极速模型。提供强化数学运算和代码编写能力。"}, "yi-vision": {"description": "复杂视觉任务模型，提供高性能图片理解、分析能力。"}, "yi-vision-v2": {"description": "复杂视觉任务模型，提供基于多张图片的高性能理解、分析能力。"}, "zai-org/GLM-4.5": {"description": "GLM-4.5 是一款专为智能体应用打造的基础模型，使用了混合专家（Mixture-of-Experts）架构。在工具调用、网页浏览、软件工程、前端编程领域进行了深度优化，支持无缝接入 Claude Code、Roo Code 等代码智能体中使用。GLM-4.5 采用混合推理模式，可以适应复杂推理和日常使用等多种应用场景。"}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air 是一款专为智能体应用打造的基础模型，使用了混合专家（Mixture-of-Experts）架构。在工具调用、网页浏览、软件工程、前端编程领域进行了深度优化，支持无缝接入 Claude Code、Roo Code 等代码智能体中使用。GLM-4.5 采用混合推理模式，可以适应复杂推理和日常使用等多种应用场景。"}}