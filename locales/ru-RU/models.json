{"01-ai/yi-1.5-34b-chat": {"description": "零一万物 — это последняя версия открытой доработанной модели с 34 миллиардами параметров, которая поддерживает различные сценарии диалога, используя высококачественные обучающие данные, соответствующие человеческим предпочтениям."}, "01-ai/yi-1.5-9b-chat": {"description": "零一万物 — это последняя версия открытой доработанной модели с 9 миллиардами параметров, которая поддерживает различные сценарии диалога, используя высококачественные обучающие данные, соответствующие человеческим предпочтениям."}, "360/deepseek-r1": {"description": "【360 версия】DeepSeek-R1 использует технологии усиленного обучения на этапе постобучения в больших масштабах, значительно улучшая способности модели к выводу при наличии лишь небольшого количества размеченных данных. В задачах математики, кода и естественного языка его производительность сопоставима с официальной версией OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro, как важный член серии моделей AI от 360, удовлетворяет разнообразные приложения обработки текста с высокой эффективностью, поддерживает понимание длинных текстов и многораундные диалоги."}, "360gpt-pro-trans": {"description": "Модель, предназначенная для перевода, глубоко настроенная и оптимизированная, с выдающимися результатами перевода."}, "360gpt-turbo": {"description": "360GPT Turbo предлагает мощные вычислительные и диалоговые возможности, обладает выдающимся пониманием семантики и эффективностью генерации, что делает его идеальным решением для интеллектуальных помощников для предприятий и разработчиков."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K акцентирует внимание на семантической безопасности и ответственности, специально разработан для приложений с высокими требованиями к безопасности контента, обеспечивая точность и надежность пользовательского опыта."}, "360gpt2-o1": {"description": "360gpt2-o1 использует дерево поиска для построения цепочек размышлений и вводит механизм рефлексии, обучаясь с помощью усиленного обучения, модель обладает способностью к саморефлексии и исправлению ошибок."}, "360gpt2-pro": {"description": "360GPT2 Pro — это продвинутая модель обработки естественного языка, выпущенная компанией 360, обладающая выдающимися способностями к генерации и пониманию текста, особенно в области генерации и творчества, способная обрабатывать сложные языковые преобразования и ролевые задачи."}, "360zhinao2-o1": {"description": "Модель 360zhinao2-o1 использует дерево поиска для построения цепочки размышлений и включает механизм рефлексии, обучаясь с помощью усиленного обучения, что позволяет модели самостоятельно рефлексировать и исправлять ошибки."}, "4.0Ultra": {"description": "Spark4.0 Ultra — это самая мощная версия в серии больших моделей Xinghuo, которая, обновив сетевые поисковые связи, улучшает понимание и обобщение текстового контента. Это всестороннее решение для повышения производительности в офисе и точного реагирования на запросы, являющееся ведущим интеллектуальным продуктом в отрасли."}, "AnimeSharp": {"description": "AnimeSharp (также известный как \"4x‑AnimeSharp\") — это открытая модель сверхразрешения, разработанная Kim2091 на основе архитектуры ESRGAN, ориентированная на увеличение и улучшение изображений в аниме-стиле. В феврале 2022 года модель была переименована из \"4x-TextSharpV1\"; изначально она также применялась для текстовых изображений, но была значительно оптимизирована для аниме-контента."}, "Baichuan2-Turbo": {"description": "Использует технологии улучшенного поиска для полной связи между большой моделью и отраслевыми знаниями, а также знаниями из сети. Поддерживает загрузку различных документов, таких как PDF и Word, а также ввод URL, обеспечивая своевременное и полное получение информации с точными и профессиональными результатами."}, "Baichuan3-Turbo": {"description": "Оптимизирован для высокочастотных корпоративных сценариев, значительно улучшает результаты и предлагает высокую стоимость. По сравнению с моделью Baichuan2, создание контента увеличилось на 20%, ответы на вопросы на 17%, а способности ролевого взаимодействия на 40%. Общая эффективность лучше, чем у GPT3.5."}, "Baichuan3-Turbo-128k": {"description": "Обладает 128K сверхдлинным контекстным окном, оптимизированным для высокочастотных корпоративных сценариев, значительно улучшает результаты и предлагает высокую стоимость. По сравнению с моделью Baichuan2, создание контента увеличилось на 20%, ответы на вопросы на 17%, а способности ролевого взаимодействия на 40%. Общая эффективность лучше, чем у GPT3.5."}, "Baichuan4": {"description": "Модель обладает лучшими возможностями в стране, превосходя зарубежные модели в задачах на знание, длинные тексты и генерацию контента. Также обладает передовыми мультимодальными возможностями и показывает отличные результаты в нескольких авторитетных тестах."}, "Baichuan4-Air": {"description": "Модель обладает лучшими в стране возможностями, превосходя зарубежные модели в задачах на китайском языке, таких как энциклопедические знания, длинные тексты и генерация контента. Также обладает передовыми мультимодальными возможностями и демонстрирует отличные результаты в нескольких авторитетных оценочных тестах."}, "Baichuan4-Turbo": {"description": "Модель обладает лучшими в стране возможностями, превосходя зарубежные модели в задачах на китайском языке, таких как энциклопедические знания, длинные тексты и генерация контента. Также обладает передовыми мультимодальными возможностями и демонстрирует отличные результаты в нескольких авторитетных оценочных тестах."}, "DeepSeek-R1": {"description": "Современная эффективная LLM, специализирующаяся на логическом выводе, математике и программировании."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 — более крупная и умная модель в наборе DeepSeek, была дистиллирована в архитектуру Llama 70B. На основе бенчмарков и человеческой оценки эта модель более умная, чем оригинальная Llama 70B, особенно в задачах, требующих математической и фактической точности."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Модель DeepSeek-R1, основанная на Qwen2.5-Math-1.5B, оптимизирует производительность вывода с помощью усиленного обучения и данных холодного старта, обновляя стандарт многозадачности в открытых моделях."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Модель DeepSeek-R1, основанная на Qwen2.5-14B, оптимизирует производительность вывода с помощью усиленного обучения и данных холодного старта, обновляя стандарт многозадачности в открытых моделях."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "Серия DeepSeek-R1 оптимизирует производительность вывода с помощью усиленного обучения и данных холодного старта, обновляя стандарт многозадачности в открытых моделях, превосходя уровень OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Модель DeepSeek-R1, основанная на Qwen2.5-Math-7B, оптимизирует производительность вывода с помощью усиленного обучения и данных холодного старта, обновляя стандарт многозадачности в открытых моделях."}, "DeepSeek-V3": {"description": "DeepSeek-V3 — это модель MoE, разработанная компанией DeepSeek. Результаты DeepSeek-V3 в нескольких оценках превосходят другие открытые модели, такие как Qwen2.5-72B и Llama-3.1-405B, и по производительности не уступают мировым ведущим закрытым моделям GPT-4o и Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite обладает исключительной скоростью отклика и лучшим соотношением цена-качество, предоставляя клиентам более гибкие варианты для различных сценариев. Поддерживает вывод и дообучение с контекстным окном в 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite обладает исключительной скоростью отклика и лучшим соотношением цена-качество, предоставляя клиентам более гибкие варианты для различных сценариев. Поддерживает вывод и дообучение с контекстным окном в 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite обладает исключительной скоростью отклика и лучшим соотношением цена-качество, предоставляя клиентам более гибкие варианты для различных сценариев. Поддерживает вывод и дообучение с контекстным окном в 4k."}, "Doubao-pro-128k": {"description": "Основная модель с наилучшей производительностью, подходящая для решения сложных задач. Отлично справляется с вопросами-ответами, резюмированием, творческим написанием, классификацией текста, ролевыми играми и другими сценариями. Поддерживает вывод и дообучение с контекстным окном в 128k."}, "Doubao-pro-32k": {"description": "Основная модель с наилучшей производительностью, подходящая для решения сложных задач. Отлично справляется с вопросами-ответами, резюмированием, творческим написанием, классификацией текста, ролевыми играми и другими сценариями. Поддерживает вывод и дообучение с контекстным окном в 32k."}, "Doubao-pro-4k": {"description": "Основная модель с наилучшей производительностью, подходящая для решения сложных задач. Отлично справляется с вопросами-ответами, резюмированием, творческим написанием, классификацией текста, ролевыми играми и другими сценариями. Поддерживает вывод и дообучение с контекстным окном в 4k."}, "DreamO": {"description": "DreamO — это открытая модель генерации изображений, разработанная совместно ByteDance и Пекинским университетом, предназначенная для поддержки многозадачной генерации изображений в единой архитектуре. Она использует эффективный метод комбинированного моделирования, позволяющий создавать высоко согласованные и кастомизированные изображения на основе заданных пользователем условий, таких как идентичность, объект, стиль и фон."}, "ERNIE-3.5-128K": {"description": "Флагманская крупномасштабная языковая модель, разработанная Baidu, охватывающая огромные объемы китайских и английских текстов, обладающая мощными универсальными возможностями, способная удовлетворить большинство требований к диалоговым ответам, генерации контента и сценариям использования плагинов; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая актуальность информации в ответах."}, "ERNIE-3.5-8K": {"description": "Флагманская крупномасштабная языковая модель, разработанная Baidu, охватывающая огромные объемы китайских и английских текстов, обладающая мощными универсальными возможностями, способная удовлетворить большинство требований к диалоговым ответам, генерации контента и сценариям использования плагинов; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая актуальность информации в ответах."}, "ERNIE-3.5-8K-Preview": {"description": "Флагманская крупномасштабная языковая модель, разработанная Baidu, охватывающая огромные объемы китайских и английских текстов, обладающая мощными универсальными возможностями, способная удовлетворить большинство требований к диалоговым ответам, генерации контента и сценариям использования плагинов; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая актуальность информации в ответах."}, "ERNIE-4.0-8K-Latest": {"description": "Флагманская сверхкрупномасштабная языковая модель, разработанная Baidu, которая по сравнению с ERNIE 3.5 обеспечивает полное обновление возможностей модели и широко применяется в сложных задачах в различных областях; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая актуальность информации в ответах."}, "ERNIE-4.0-8K-Preview": {"description": "Флагманская сверхкрупномасштабная языковая модель, разработанная Baidu, которая по сравнению с ERNIE 3.5 обеспечивает полное обновление возможностей модели и широко применяется в сложных задачах в различных областях; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая актуальность информации в ответах."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Флагманская 超大型 языковая модель, разработанная Baidu, демонстрирует отличные результаты и хорошо подходит для сложных задач в различных областях; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая своевременность ответов. По сравнению с ERNIE 4.0 имеет лучшие показатели производительности."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Флагманская сверхкрупномасштабная языковая модель, разработанная Baidu, демонстрирующая отличные результаты в комплексной эффективности, широко применяемая в сложных задачах в различных областях; поддерживает автоматическую интеграцию с плагином поиска Baidu, обеспечивая актуальность информации в ответах. По сравнению с ERNIE 4.0, она демонстрирует лучшие показатели производительности."}, "ERNIE-Character-8K": {"description": "Специализированная языковая модель, разработанная Baidu для вертикальных сценариев, подходящая для применения в играх (NPC), диалогах службы поддержки, ролевых играх и других сценариях, обладающая ярко выраженным и согласованным стилем персонажей, высокой способностью следовать инструкциям и отличной производительностью вывода."}, "ERNIE-Lite-Pro-128K": {"description": "Легковесная языковая модель, разработанная Baidu, которая сочетает в себе отличные результаты модели и производительность вывода, превосходя ERNIE Lite, подходит для использования в системах с низкой вычислительной мощностью."}, "ERNIE-Speed-128K": {"description": "Новая высокопроизводительная языковая модель, разработанная Baidu в 2024 году, обладающая выдающимися универсальными возможностями, подходит для использования в качестве базовой модели для тонкой настройки, лучше справляясь с задачами в специфических сценариях, при этом обладая отличной производительностью вывода."}, "ERNIE-Speed-Pro-128K": {"description": "Новая высокопроизводительная языковая модель, разработанная Baidu в 2024 году, обладающая выдающимися универсальными возможностями, превосходящая ERNIE Speed, подходит для использования в качестве базовой модели для тонкой настройки, лучше справляясь с задачами в специфических сценариях, при этом обладая отличной производительностью вывода."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev — мультимодальная модель генерации и редактирования изображений, разработанная Black Forest Labs на основе архитектуры Rectified Flow Transformer с масштабом 12 миллиардов параметров. Модель специализируется на генерации, реконструкции, улучшении и редактировании изображений с учётом заданного контекста. Она сочетает преимущества контролируемой генерации диффузионных моделей и контекстного моделирования Transformer, обеспечивая высококачественный вывод и широкое применение в задачах восстановления, дополнения и реконструкции визуальных сцен."}, "FLUX.1-dev": {"description": "FLUX.1-dev — это открытая мультимодальная языковая модель (Multimodal Language Model, MLLM), разработанная Black Forest Labs и оптимизированная для задач, связанных с изображениями и текстом. Она объединяет возможности понимания и генерации изображений и текста, построена на основе передовой большой языковой модели (например, Mistral-7B) и использует тщательно разработанный визуальный кодировщик и многоступенчатую инструкционную донастройку для совместной обработки изображений и текста, а также сложного вывода."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) — это инновационная модель, подходящая для многообластных приложений и сложных задач."}, "HelloMeme": {"description": "HelloMeme — это AI-инструмент, который автоматически создаёт мемы, анимированные изображения или короткие видео на основе предоставленных вами картинок или действий. Для работы не требуется навыков рисования или программирования — достаточно подготовить референсное изображение, и инструмент поможет создать привлекательный, забавный и стилистически единый контент."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full — это открытая мультимодальная модель редактирования изображений, выпущенная HiDream.ai, основанная на передовой архитектуре Diffusion Transformer и обладающая мощными возможностями понимания языка (встроенный LLaMA 3.1-8B-Instruct). Модель поддерживает генерацию изображений, перенос стиля, локальное редактирование и перерисовку контента по естественным языковым инструкциям, демонстрируя выдающиеся способности в понимании и выполнении текстово-графических задач."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled — это облегчённая модель генерации изображений из текста, оптимизированная с помощью дистилляции для быстрой генерации высококачественных изображений, особенно подходящая для условий с ограниченными ресурсами и задач реального времени."}, "InstantCharacter": {"description": "InstantCharacter — персонализированная модель генерации персонажей без необходимости дообучения, выпущенная командой Tencent AI в 2025 году. Модель обеспечивает высокую точность и согласованность персонажей в различных сценах, позволяя создавать модели персонажей на основе одной референсной фотографии и гибко переносить их в разные стили, позы и фоны."}, "InternVL2-8B": {"description": "InternVL2-8B — это мощная визуально-языковая модель, поддерживающая многомодальную обработку изображений и текста, способная точно распознавать содержимое изображений и генерировать соответствующие описания или ответы."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B — это мощная визуально-языковая модель, поддерживающая многомодальную обработку изображений и текста, способная точно распознавать содержимое изображений и генерировать соответствующие описания или ответы."}, "Kolors": {"description": "Kolors — модель генерации изображений из текста, разработанная командой Kolors компании Kuaishou. Обученная на миллиардах параметров, она демонстрирует значительные преимущества в визуальном качестве, понимании китайской семантики и рендеринге текста."}, "Kwai-Kolors/Kolors": {"description": "Kolors — масштабная модель генерации изображений из текста на основе латентного диффузионного процесса, разработанная командой Kolors компании Kuaishou. Обученная на миллиардах пар текст-изображение, модель демонстрирует выдающиеся результаты в визуальном качестве, точности сложной семантики и рендеринге китайских и английских символов. Она поддерживает ввод на китайском и английском языках и особенно хорошо справляется с пониманием и генерацией специфического китайского контента."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Отличные способности к визуальному выводу на изображениях высокого разрешения, подходящие для приложений визуального понимания."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Передовые способности к визуальному выводу, подходящие для приложений визуального понимания."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Текстовая модель Llama 3.1 с оптимизацией под инструкции, разработанная для многоязычных диалоговых случаев, показывает отличные результаты по сравнению с многими доступными открытыми и закрытыми чат-моделями на общепринятых отраслевых бенчмарках."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Текстовая модель Llama 3.1 с оптимизацией под инструкции, разработанная для многоязычных диалоговых случаев, показывает отличные результаты по сравнению с многими доступными открытыми и закрытыми чат-моделями на общепринятых отраслевых бенчмарках."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Текстовая модель Llama 3.1 с оптимизацией под инструкции, разработанная для многоязычных диалоговых случаев, показывает отличные результаты по сравнению с многими доступными открытыми и закрытыми чат-моделями на общепринятых отраслевых бенчмарках."}, "Meta-Llama-3.2-1B-Instruct": {"description": "Современная передовая компактная языковая модель с выдающимися способностями к пониманию языка, логическому выводу и генерации текста."}, "Meta-Llama-3.2-3B-Instruct": {"description": "Современная передовая компактная языковая модель с выдающимися способностями к пониманию языка, логическому выводу и генерации текста."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 — это самая современная многоязычная открытая языковая модель из серии Llama, которая позволяет получить производительность, сопоставимую с 405B моделями, по крайне низкой цене. Основана на структуре Transformer и улучшена с помощью контролируемой донастройки (SFT) и обучения с подкреплением на основе человеческой обратной связи (RLHF) для повышения полезности и безопасности. Ее версия с оптимизацией под инструкции специально разработана для многоязычных диалогов и показывает лучшие результаты по сравнению с многими открытыми и закрытыми чат-моделями на нескольких отраслевых бенчмарках. Дата окончания знаний — декабрь 2023 года."}, "MiniMax-M1": {"description": "Совершенно новая собственная модель вывода. Мировой лидер: 80K цепочек мышления x 1M входов, эффективность сопоставима с ведущими зарубежными моделями."}, "MiniMax-Text-01": {"description": "В серии моделей MiniMax-01 мы сделали смелые инновации: впервые в крупномасштабном масштабе реализован линейный механизм внимания, традиционная архитектура Transformer больше не является единственным выбором. Объем параметров этой модели достигает 456 миллиардов, из которых 45,9 миллиарда активируются за один раз. Комплексная производительность модели сопоставима с ведущими зарубежными моделями, при этом она может эффективно обрабатывать контекст длиной до 4 миллионов токенов, что в 32 раза больше, чем у GPT-4o, и в 20 раз больше, чем у Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 — это масштабная модель вывода с гибридным вниманием и открытыми весами, содержащая 456 миллиардов параметров, при этом каждый токен активирует около 45,9 миллиарда параметров. Модель изначально поддерживает сверхдлинный контекст до 1 миллиона токенов и благодаря механизму молниеносного внимания экономит 75% вычислительных операций с плавающей точкой в задачах генерации на 100 тысяч токенов по сравнению с DeepSeek R1. Кроме того, MiniMax-M1 использует архитектуру MoE (смешанные эксперты), сочетая алгоритм CISPO и эффективное обучение с подкреплением с гибридным вниманием, достигая ведущих в отрасли показателей при выводе на длинных входах и в реальных сценариях программной инженерии."}, "Moonshot-Kimi-K2-Instruct": {"description": "Общая численность параметров — 1 триллион, активируемых параметров — 32 миллиарда. Среди немыслящих моделей достигает передовых результатов в области актуальных знаний, математики и программирования, особенно эффективна для универсальных агентских задач. Модель тщательно оптимизирована для агентских задач, способна не только отвечать на вопросы, но и предпринимать действия. Идеально подходит для импровизационного, универсального общения и агентских сценариев, являясь моделью рефлекторного уровня без необходимости длительного обдумывания."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) — это высокоточная модель команд, подходящая для сложных вычислений."}, "OmniConsistency": {"description": "OmniConsistency повышает согласованность стиля и обобщающую способность в задачах преобразования изображений (Image-to-Image) за счёт внедрения масштабных Diffusion Transformers (DiTs) и парных стилизованных данных, предотвращая деградацию стиля."}, "Phi-3-medium-128k-instruct": {"description": "Та же модель Phi-3-medium, но с большим размером контекста для RAG или нескольких подсказок."}, "Phi-3-medium-4k-instruct": {"description": "Модель с 14B параметрами, демонстрирующая лучшее качество, чем Phi-3-mini, с акцентом на высококачественные, насыщенные рассуждениями данные."}, "Phi-3-mini-128k-instruct": {"description": "Та же модель Phi-3-mini, но с большим размером контекста для RAG или нескольких подсказок."}, "Phi-3-mini-4k-instruct": {"description": "Самая маленькая модель в семействе Phi-3. Оптимизирована как для качества, так и для низкой задержки."}, "Phi-3-small-128k-instruct": {"description": "Та же модель Phi-3-small, но с большим размером контекста для RAG или нескольких подсказок."}, "Phi-3-small-8k-instruct": {"description": "Модель с 7B параметрами, демонстрирующая лучшее качество, чем Phi-3-mini, с акцентом на высококачественные, насыщенные рассуждениями данные."}, "Phi-3.5-mini-instruct": {"description": "Обновленная версия модели Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "Обновленная версия модели Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct — это языковая модель с дообучением на инструкциях в серии Qwen2, с параметрами 7B. Эта модель основана на архитектуре Transformer и использует такие технологии, как активационная функция SwiGLU, смещение внимания QKV и групповой запрос внимания. Она может обрабатывать большие объемы входных данных. Эта модель показывает отличные результаты в понимании языка, генерации, многоязычных способностях, кодировании, математике и выводах в различных бенчмарках, превосходя большинство открытых моделей и демонстрируя конкурентоспособность с проприетарными моделями в некоторых задачах. Qwen2-7B-Instruct показывает значительное улучшение производительности в нескольких оценках по сравнению с Qwen1.5-7B-Chat."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct — это одна из последних языковых моделей, выпущенных Alibaba Cloud. Эта 7B модель значительно улучшила способности в области кодирования и математики. Модель также поддерживает множество языков, охватывающих более 29 языков, включая китайский и английский. Она значительно улучшила выполнение инструкций, понимание структурированных данных и генерацию структурированных выходных данных (особенно JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct — это последняя версия серии языковых моделей, специфичных для кода, выпущенная Alibaba Cloud. Эта модель значительно улучшила способности генерации кода, вывода и исправления на основе Qwen2.5, обучаясь на 5.5 триллионах токенов. Она не только усилила кодирование, но и сохранила преимущества в математике и общих способностях. Модель предоставляет более полную основу для практических приложений, таких как интеллектуальные агенты кода."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL — это новый член семейства Qwen, обладающий мощными возможностями визуального понимания. Может анализировать текст, диаграммы и компоновку в изображениях, понимать длинные видео и фиксировать события. Способен к логическим рассуждениям, работе с инструментами, поддерживает локализацию объектов в различных форматах и генерацию структурированных выводов. Оптимизирован для понимания видео с динамическим разрешением и частотой кадров, а также улучшена эффективность визуального кодировщика."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking — это открытая визуально-языковая модель (VLM), совместно выпущенная Zhipu AI и лабораторией KEG Университета Цинхуа, специально разработанная для решения сложных мультимодальных когнитивных задач. Модель основана на базовой модели GLM-4-9B-0414 и значительно улучшает межмодальные способности рассуждения и стабильность за счёт внедрения механизма рассуждения «цепочка мышления» (Chain-of-Thought) и использования методов обучения с подкреплением."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat — это открытая версия предобученной модели из серии GLM-4, выпущенная Zhizhu AI. Эта модель показывает отличные результаты в семантике, математике, выводах, коде и знаниях. Кроме поддержки многократных диалогов, GLM-4-9B-Chat также обладает продвинутыми функциями, такими как веб-браузинг, выполнение кода, вызов пользовательских инструментов (Function Call) и вывод длинных текстов. Модель поддерживает 26 языков, включая китайский, английский, японский, корейский и немецкий. В нескольких бенчмарках GLM-4-9B-Chat демонстрирует отличные результаты, такие как AlignBench-v2, MT-Bench, MMLU и C-Eval. Эта модель поддерживает максимальную длину контекста 128K и подходит для академических исследований и коммерческих приложений."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 — это модель вывода, управляемая обучением с подкреплением (RL), которая решает проблемы повторяемости и читаемости в модели. Перед RL DeepSeek-R1 вводит данные холодного старта, что дополнительно оптимизирует производительность вывода. Она показывает сопоставимые результаты с OpenAI-o1 в математических, кодовых и задачах вывода и улучшает общую эффективность благодаря тщательно продуманным методам обучения."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B — это модель, полученная методом дистилляции знаний на основе Qwen2.5-Math-7B. Модель была доработана с использованием 800 тысяч отобранных образцов, сгенерированных DeepSeek-R1, и демонстрирует выдающиеся способности к логическому рассуждению. Показывает отличные результаты в различных тестах: точность 92,8% на MATH-500, проходной балл 55,5% на AIME 2024 и оценку 1189 на CodeForces, что подтверждает её высокие математические и программистские возможности для модели масштаба 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 — это языковая модель с 6710 миллиардами параметров, использующая архитектуру смешанных экспертов (MoE) и многофункциональное внимание (MLA), в сочетании с стратегией балансировки нагрузки без вспомогательных потерь, оптимизирующая эффективность вывода и обучения. После предобучения на 14.8 триллионах высококачественных токенов и последующей контролируемой донастройки и обучения с подкреплением, DeepSeek-V3 превосходит другие открытые модели и приближается к ведущим закрытым моделям."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 — базовая модель на архитектуре MoE с выдающимися возможностями в кодировании и агентских задачах, общим числом параметров 1 триллион и 32 миллиардами активируемых параметров. В тестах на универсальное знание, программирование, математику и агентские задачи производительность модели K2 превосходит другие ведущие открытые модели."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview — это инновационная модель обработки естественного языка, способная эффективно обрабатывать сложные задачи генерации диалогов и понимания контекста."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview — это исследовательская модель, разработанная командой Qwen, сосредоточенная на способностях визуального вывода, обладающая уникальными преимуществами в понимании сложных сцен и решении визуально связанных математических задач."}, "Qwen/QwQ-32B": {"description": "QwQ — это модель вывода из серии Qwen. В отличие от традиционных моделей, настроенных на инструкции, QwQ обладает способностями к мышлению и рассуждению, что позволяет значительно улучшить производительность в задачах нижнего уровня, особенно при решении сложных проблем. QwQ-32B — это средняя модель вывода, которая демонстрирует конкурентоспособные результаты в сравнении с самыми современными моделями вывода (такими как DeepSeek-R1, o1-mini). Эта модель использует технологии RoPE, SwiGLU, RMSNorm и Attention QKV bias, имеет 64-слойную архитектуру и 40 голов внимания Q (в архитектуре GQA KV составляет 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview — это последняя экспериментальная исследовательская модель Qwen, сосредоточенная на повышении возможностей вывода ИИ. Исследуя сложные механизмы, такие как смешение языков и рекурсивные выводы, основные преимущества включают мощные аналитические способности, математические и программные навыки. В то же время существуют проблемы с переключением языков, циклом вывода, соображениями безопасности и различиями в других способностях."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 — это передовая универсальная языковая модель, поддерживающая множество типов команд."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct — это языковая модель с дообучением на инструкциях в серии Qwen2, с параметрами 72B. Эта модель основана на архитектуре Transformer и использует такие технологии, как активационная функция SwiGLU, смещение внимания QKV и групповой запрос внимания. Она может обрабатывать большие объемы входных данных. Эта модель показывает отличные результаты в понимании языка, генерации, многоязычных способностях, кодировании, математике и выводах в различных бенчмарках, превосходя большинство открытых моделей и демонстрируя конкурентоспособность с проприетарными моделями в некоторых задачах."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL - это последняя версия модели Qwen-VL, которая достигла передовых результатов в тестировании визуального понимания."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 — это новая серия крупных языковых моделей, предназначенная для оптимизации обработки инструктивных задач."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 — это новая серия крупных языковых моделей, предназначенная для оптимизации обработки инструктивных задач."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Большая языковая модель, разработанная командой Alibaba Cloud Tongyi Qianwen."}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 - это новая серия крупных языковых моделей с улучшенными способностями понимания и генерации."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 - это новая серия крупных языковых моделей, нацеленная на оптимизацию обработки задач с инструкциями."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 — это новая серия крупных языковых моделей, предназначенная для оптимизации обработки инструктивных задач."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 - это новая серия крупных языковых моделей, нацеленная на оптимизацию обработки задач с инструкциями."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder сосредоточен на написании кода."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct — это последняя версия серии языковых моделей, специфичных для кода, выпущенная Alibaba Cloud. Эта модель значительно улучшила способности генерации кода, вывода и исправления на основе Qwen2.5, обучаясь на 5.5 триллионах токенов. Она не только усилила кодирование, но и сохранила преимущества в математике и общих способностях. Модель предоставляет более полную основу для практических приложений, таких как интеллектуальные агенты кода."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct — это мультимодальная языковая модель, разработанная командой Tongyi Qianwen, являющаяся частью серии Qwen2.5-VL. Модель не только превосходно распознаёт обычные объекты, но и анализирует текст, диаграммы, иконки, графики и композицию в изображениях. Она может функционировать как визуальный агент, способный к логическим рассуждениям и динамическому управлению инструментами, включая работу с компьютерами и мобильными устройствами. Кроме того, модель точно определяет местоположение объектов на изображениях и генерирует структурированные выводы для документов, таких как счета и таблицы. По сравнению с предыдущей версией Qwen2-VL, данная модель демонстрирует улучшенные математические способности и навыки решения задач благодаря обучению с подкреплением, а также более естественный стиль ответов, соответствующий человеческим предпочтениям."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL — это визуально-языковая модель из серии Qwen2.5. Модель демонстрирует значительные улучшения в различных аспектах: обладает более сильными способностями к визуальному пониманию, может распознавать обычные объекты, анализировать текст, диаграммы и макеты; как визуальный агент способна рассуждать и динамически направлять использование инструментов; поддерживает понимание длинных видео продолжительностью более 1 часа с возможностью выделения ключевых событий; может точно локализовать объекты на изображении, генерируя ограничивающие рамки или точки; поддерживает генерацию структурированного вывода, что особенно полезно для сканированных данных, таких как счета-фактуры и таблицы."}, "Qwen/Qwen3-14B": {"description": "Qwen3 — это новая генерация модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли по нескольким ключевым направлениям, включая рассуждение, общие задачи, агентские функции и многоязычность, а также поддерживающей переключение режимов размышления."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 — это новая генерация модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли по нескольким ключевым направлениям, включая рассуждение, общие задачи, агентские функции и многоязычность, а также поддерживающей переключение режимов размышления."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 — флагманская модель серии Qwen3 с архитектурой смешанных экспертов (MoE), разработанная командой Alibaba Cloud Tongyi Qianwen. Модель содержит 235 миллиардов параметров, из которых при каждом выводе активируется 22 миллиарда. Это обновлённая версия Qwen3-235B-A22B в неразмышляющем режиме, с улучшениями в следовании инструкциям, логическом выводе, понимании текста, математике, науке, программировании и использовании инструментов. Модель расширяет покрытие многоязычных знаний и лучше согласуется с пользовательскими предпочтениями в субъективных и открытых задачах, обеспечивая более полезный и качественный текст."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 — крупная языковая модель серии Qwen3, разработанная командой Alibaba Tongyi Qianwen, ориентированная на сложные задачи рассуждения. Модель построена на архитектуре смешанных экспертов (MoE) с общим числом параметров 235 миллиардов и активацией около 22 миллиардов параметров на токен, что обеспечивает высокую производительность при эффективном использовании ресурсов. Как специализированная \"мыслящая\" модель, она демонстрирует выдающиеся результаты в логическом выводе, математике, науке, программировании и академических тестах, достигая топовых показателей среди открытых моделей. Модель также улучшает универсальные способности, такие как следование инструкциям, использование инструментов и генерация текста, и нативно поддерживает контекст длиной до 256K токенов, что делает её идеальной для глубокого анализа и обработки длинных документов."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 — это новая генерация модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли по нескольким ключевым направлениям, включая рассуждение, общие задачи, агентские функции и многоязычность, а также поддерживающей переключение режимов размышления."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 — это обновленная версия модели Qwen3-30B-A3B в режиме без размышлений. Это модель с гибридными экспертами (MoE), имеющая в общей сложности 30,5 миллиарда параметров и 3,3 миллиарда активных параметров. Модель получила ключевые улучшения во многих аспектах, включая значительное повышение способности следовать инструкциям, логического мышления, понимания текста, математики, науки, программирования и использования инструментов. Кроме того, она достигла существенного прогресса в покрытии многоязычных редких знаний и лучше согласуется с предпочтениями пользователей в субъективных и открытых задачах, что позволяет генерировать более полезные ответы и тексты высокого качества. Также улучшена способность к пониманию длинных текстов — теперь до 256K. Эта модель поддерживает только режим без размышлений и не генерирует теги `<think></think>` в выводе."}, "Qwen/Qwen3-32B": {"description": "Qwen3 — это новая генерация модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли по нескольким ключевым направлениям, включая рассуждение, общие задачи, агентские функции и многоязычность, а также поддерживающей переключение режимов размышления."}, "Qwen/Qwen3-8B": {"description": "Qwen3 — это новая генерация модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли по нескольким ключевым направлениям, включая рассуждение, общие задачи, агентские функции и многоязычность, а также поддерживающей переключение режимов размышления."}, "Qwen2-72B-Instruct": {"description": "Qwen2 — это последняя серия моделей Qwen, поддерживающая контекст до 128k. По сравнению с текущими лучшими открытыми моделями, Qwen2-72B значительно превосходит ведущие модели по многим аспектам, включая понимание естественного языка, знания, код, математику и многоязычность."}, "Qwen2-7B-Instruct": {"description": "Qwen2 — это последняя серия моделей Qwen, способная превосходить лучшие открытые модели сопоставимого размера и даже более крупные модели. Qwen2 7B демонстрирует значительные преимущества в нескольких тестах, особенно в понимании кода и китайского языка."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B — это мощная модель визуального языка, поддерживающая многомодальную обработку изображений и текста, способная точно распознавать содержимое изображений и генерировать соответствующие описания или ответы."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct — это языковая модель с 14 миллиардами параметров, с отличными показателями производительности, оптимизированная для китайского и многоязычного контекста, поддерживает интеллектуальные ответы, генерацию контента и другие приложения."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct — это языковая модель с 32 миллиардами параметров, с сбалансированными показателями производительности, оптимизированная для китайского и многоязычного контекста, поддерживает интеллектуальные ответы, генерацию контента и другие приложения."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct поддерживает контекст до 16k, генерируя длинные тексты более 8K. Поддерживает вызовы функций и бесшовное взаимодействие с внешними системами, что значительно повышает гибкость и масштабируемость. Знания модели значительно увеличены, а способности в кодировании и математике значительно улучшены, поддерживает более 29 языков."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct — это языковая модель с 7 миллиардами параметров, поддерживающая вызовы функций и бесшовное взаимодействие с внешними системами, что значительно повышает гибкость и масштабируемость. Оптимизирована для китайского и многоязычного контекста, поддерживает интеллектуальные ответы, генерацию контента и другие приложения."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct — это модель программирования на основе масштабного предварительного обучения, обладающая мощными способностями к пониманию и генерации кода, способная эффективно решать различные задачи программирования, особенно подходит для интеллектуального написания кода, автоматизации скриптов и ответов на программные вопросы."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct — это крупная языковая модель, специально разработанная для генерации кода, понимания кода и эффективных сценариев разработки, с передовым масштабом параметров 32B, способная удовлетворить разнообразные потребности программирования."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B — модель MoE (гибридных экспертов), которая внедрила «гибридный режим рассуждений», позволяющий пользователям бесшовно переключаться между режимами «размышления» и «без размышлений». Поддерживает понимание и рассуждение на 119 языках и диалектах, обладает мощными возможностями вызова инструментов. По совокупности способностей, кода, математики, многоязычия, знаний и рассуждений модель конкурирует с ведущими современными крупными моделями на рынке, такими как DeepSeek R1, OpenAI o1, o3-mini, Grok 3 и Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B — плотная модель (Dense Model), внедрившая «гибридный режим рассуждений», позволяющий пользователям бесшовно переключаться между режимами «размышления» и «без размышлений». Благодаря улучшениям архитектуры модели, увеличению объема обучающих данных и более эффективным методам обучения, общая производительность сопоставима с Qwen2.5-72B."}, "SenseChat": {"description": "Базовая версия модели (V4), д<PERSON><PERSON><PERSON> контекста 4K, обладает мощными универсальными возможностями."}, "SenseChat-128K": {"description": "Базовая версия модели (V4), <PERSON><PERSON><PERSON><PERSON> контекста 128K, демонстрирует отличные результаты в задачах понимания и генерации длинных текстов."}, "SenseChat-32K": {"description": "Базовая версия модели (V4), <PERSON><PERSON><PERSON><PERSON> контекста 32K, гибко применяется в различных сценариях."}, "SenseChat-5": {"description": "Последняя версия модели (V5.5), <PERSON><PERSON><PERSON><PERSON> контекста 128K, значительно улучшенные способности в математическом рассуждении, английских диалогах, следовании инструкциям и понимании длинных текстов, сопоставимые с GPT-4o."}, "SenseChat-5-1202": {"description": "Основана на версии V5.5, с заметными улучшениями по нескольким направлениям: базовые навыки на китайском и английском, чат, знания в естественных и гуманитарных науках, письмо, математическая логика, контроль длины текста."}, "SenseChat-5-Cantonese": {"description": "<PERSON><PERSON><PERSON>на контекста 32K, превосходит GPT-4 в понимании диалогов на кантонском, сопоставим с GPT-4 Turbo в таких областях, как знания, рассуждение, математика и написание кода."}, "SenseChat-5-beta": {"description": "Частично превосходит производительность SenseCat-5-1202"}, "SenseChat-Character": {"description": "Стандартная версия модели, дли<PERSON> контекста 8K, высокая скорость отклика."}, "SenseChat-Character-Pro": {"description": "Расширенная версия модели, д<PERSON><PERSON><PERSON> контекста 32K, всеобъемлющие улучшения возможностей, поддерживает диалоги на китайском и английском языках."}, "SenseChat-Turbo": {"description": "Подходит для быстрого ответа на вопросы и сценариев тонкой настройки модели."}, "SenseChat-Turbo-1202": {"description": "Это последняя легковесная версия модели, которая достигает более 90% возможностей полной модели и значительно снижает затраты на вывод."}, "SenseChat-Vision": {"description": "Последняя версия модели (V5.5) поддерживает ввод нескольких изображений, полностью реализует оптимизацию базовых возможностей модели и значительно улучшила распознавание свойств объектов, пространственные отношения, распознавание событий, понимание сцен, распознавание эмоций, логическое рассуждение и понимание текста."}, "SenseNova-V6-5-Pro": {"description": "Благодаря всестороннему обновлению мультимодальных, языковых и рассуждательных данных, а также оптимизации стратегий обучения, новая модель значительно улучшила мультимодальные рассуждения и способность следовать универсальным инструкциям. Поддерживает контекстное окно до 128k и демонстрирует выдающиеся результаты в специализированных задачах, таких как OCR и распознавание туристических IP."}, "SenseNova-V6-5-Turbo": {"description": "Благодаря всестороннему обновлению мультимодальных, языковых и рассуждательных данных, а также оптимизации стратегий обучения, новая модель значительно улучшила мультимодальные рассуждения и способность следовать универсальным инструкциям. Поддерживает контекстное окно до 128k и демонстрирует выдающиеся результаты в специализированных задачах, таких как OCR и распознавание туристических IP."}, "SenseNova-V6-Pro": {"description": "Реализует родное единство возможностей изображений, текста и видео, преодолевая традиционные ограничения раздельных мультимодальных систем, завоевав двойное чемпионство в оценках OpenCompass и SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "Учитывает визуальное и языковое глубокое рассуждение, реализует медленное мышление и глубокое рассуждение, демонстрируя полный процесс мыслительной цепочки."}, "SenseNova-V6-Turbo": {"description": "Реализует родное единство возможностей изображений, текста и видео, преодолевая традиционные ограничения раздельных мультимодальных систем, значительно опережая в ключевых аспектах, таких как базовые мультимодальные и языковые способности, сочетая литературное и научное образование, многократно занимая позиции первой группы в различных оценках как в стране, так и за рубежом."}, "Skylark2-lite-8k": {"description": "Модель второго поколения Skylark (云雀), модель Skylark2-lite имеет высокую скорость отклика, подходит для сценариев с высокими требованиями к оперативности, чувствительных к стоимости и с не такими высокими требованиями к точности модели. Длина контекстного окна составляет 8k."}, "Skylark2-pro-32k": {"description": "Модель второго поколения Skylark (云雀), версия Skylark2-pro имеет высокую точность модели, подходит для более сложных сценариев генерации текста, таких как написание специализированной документации, создание романов, высококачественный перевод и т.д. Длина контекстного окна составляет 32k."}, "Skylark2-pro-4k": {"description": "Модель второго поколения Skylark (云雀), модель Skylark2-pro имеет высокую точность, подходит для более сложных сценариев генерации текста, таких как специализированная документация, создание романов, высококачественный перевод и т.д. Длина контекстного окна составляет 4k."}, "Skylark2-pro-character-4k": {"description": "Модель второго поколения Skylark (云雀), модель Skylark2-pro-character демонстрирует выдающиеся способности к ролевым взаимодействиям и чатам, умеет играть различные роли в зависимости от требований пользователя, что делает общение естественным и плавным. Подходит для разработки чат-ботов, виртуальных помощников и онлайн-сервисов с высокой скоростью отклика."}, "Skylark2-pro-turbo-8k": {"description": "Модель второго поколения Skylark (云雀), модель Skylark2-pro-turbo-8k обеспечивает более быструю обработку и сниженные затраты, длина контекстного окна составляет 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 — это новое поколение открытой модели серии GLM с 32 миллиардами параметров. Эта модель может соперничать с серией GPT от OpenAI и серией V3/R1 от DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 — это компактная модель серии GLM с 9 миллиардами параметров. Эта модель унаследовала технические характеристики серии GLM-4-32B, но предлагает более легкие варианты развертывания. Несмотря на меньший размер, GLM-4-9B-0414 все еще демонстрирует отличные способности в задачах генерации кода, веб-дизайна, генерации графики SVG и написания на основе поиска."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking — это открытая визуально-языковая модель (VLM), совместно выпущенная Zhipu AI и лабораторией KEG Университета Цинхуа, специально разработанная для решения сложных мультимодальных когнитивных задач. Модель основана на базовой модели GLM-4-9B-0414 и значительно улучшает межмодальные способности рассуждения и стабильность за счёт внедрения механизма рассуждения «цепочка мышления» (Chain-of-Thought) и использования методов обучения с подкреплением."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 — это модель вывода с глубокими размышлениями. Эта модель основана на GLM-4-32B-0414 и была разработана с помощью холодного старта и расширенного усиленного обучения, а также была дополнительно обучена в задачах математики, кода и логики. По сравнению с базовой моделью, GLM-Z1-32B-0414 значительно улучшила математические способности и способности к решению сложных задач."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 — это компактная модель серии GLM с 9 миллиардами параметров, но при этом демонстрирует удивительные способности, сохраняя традиции открытого исходного кода. Несмотря на меньший размер, эта модель все еще показывает отличные результаты в математическом выводе и общих задачах, ее общая производительность находится на ведущем уровне среди моделей открытого исходного кода аналогичного размера."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 — это модель глубокого вывода с размышлениями (сравнимая с Deep Research от OpenAI). В отличие от типичных моделей глубокого мышления, модель размышлений использует более длительное время глубокого мышления для решения более открытых и сложных задач."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B — это открытая версия, обеспечивающая оптимизированный диалоговый опыт для приложений."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B — первая крупномасштабная модель рассуждений с длинным контекстом (LRM), обученная с использованием обучения с подкреплением, оптимизированная для задач рассуждений с длинным текстом. Модель реализует стабильный переход от короткого к длинному контексту через прогрессивное расширение контекста в рамках обучения с подкреплением. В семи бенчмарках по вопросам с длинным контекстом QwenLong-L1-32B превзошла флагманские модели OpenAI-o3-mini и Qwen3-235B-A22B, демонстрируя производительность, сопоставимую с Claude-3.7-Sonnet-Thinking. Особенно хорошо справляется со сложными задачами математического, логического и многошагового рассуждения."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, сохраняя выдающиеся универсальные языковые способности оригинальной серии моделей, значительно улучшила математическую логику и способности к кодированию благодаря инкрементальному обучению на 500 миллиардов высококачественных токенов."}, "abab5.5-chat": {"description": "Ориентирован на производственные сценарии, поддерживает обработку сложных задач и эффективную генерацию текста, подходит для профессиональных приложений."}, "abab5.5s-chat": {"description": "Специально разработан для диалогов на китайском языке, обеспечивая высококачественную генерацию диалогов на китайском, подходит для различных приложений."}, "abab6.5g-chat": {"description": "Специально разработан для многоязычных диалогов, поддерживает высококачественную генерацию диалогов на английском и других языках."}, "abab6.5s-chat": {"description": "Подходит для широкого спектра задач обработки естественного языка, включая генерацию текста, диалоговые системы и т.д."}, "abab6.5t-chat": {"description": "Оптимизирован для диалогов на китайском языке, обеспечивая плавную генерацию диалогов, соответствующую китайским языковым привычкам."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 — это передовая большая языковая модель, оптимизированная с помощью обучения с подкреплением и холодных стартовых данных, обладающая выдающимися показателями вывода, математики и программирования."}, "accounts/fireworks/models/deepseek-v3": {"description": "Мощная языковая модель Mixture-of-Experts (MoE) от Deepseek с общим количеством параметров 671B, активирующая 37B параметров на каждый токен."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Модель Llama 3 70B для команд, специально оптимизированная для многоязычных диалогов и понимания естественного языка, превосходит большинство конкурентных моделей."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Модель Llama 3 8B для команд, оптимизированная для диалогов и многоязычных задач, демонстрирует выдающиеся и эффективные результаты."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Модель Llama 3 8B для команд (HF версия), результаты которой совпадают с официальной реализацией, обладает высокой согласованностью и совместимостью между платформами."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Модель Llama 3.1 405B для команд, обладающая огромным количеством параметров, подходит для сложных задач и сценариев с высокой нагрузкой."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Модель Llama 3.1 70B для команд, обеспечивающая выдающиеся возможности понимания и генерации естественного языка, является идеальным выбором для диалоговых и аналитических задач."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Модель Llama 3.1 8B для команд, оптимизированная для многоязычных диалогов, способная превосходить большинство открытых и закрытых моделей по общим отраслевым стандартам."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Модель Meta с 11B параметрами, оптимизированная для вывода изображений. Эта модель предназначена для визуального распознавания, вывода изображений, описания изображений и ответа на общие вопросы о изображениях. Эта модель способна понимать визуальные данные, такие как графики и диаграммы, и преодолевать разрыв между визуальным и языковым пониманием, генерируя текстовые описания деталей изображений."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Модель Llama 3.2 3B для инструкций - это компактная многоязычная модель, запущенная Meta. Эта модель предназначена для повышения эффективности и обеспечивает значительное улучшение в задержке и стоимости по сравнению с более крупными моделями. Примеры использования модели включают запросы, переоформление подсказок и помощь в написании."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Модель Meta с 90B параметрами, оптимизированная для вывода изображений. Эта модель предназначена для визуального распознавания, вывода изображений, описания изображений и ответа на общие вопросы о изображениях. Эта модель способна понимать визуальные данные, такие как графики и диаграммы, и преодолевать разрыв между визуальным и языковым пониманием, генерируя текстовые описания деталей изображений."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct — это обновленная версия Llama 3.1 70B от декабря. Эта модель улучшена на основе Llama 3.1 70B (выпущенной в июле 2024 года), с усиленной поддержкой вызовов инструментов, многоязычного текста, математических и программных возможностей. Модель достигла ведущих в отрасли показателей в области вывода, математики и соблюдения инструкций, обеспечивая производительность, сопоставимую с 3.1 405B, при этом обладая значительными преимуществами по скорости и стоимости."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Модель с 24B параметрами, обладающая передовыми возможностями, сопоставимыми с более крупными моделями."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Mixtral MoE 8x22B для команд, с большим количеством параметров и архитектурой с несколькими экспертами, всесторонне поддерживает эффективную обработку сложных задач."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Mixtral MoE 8x7B для команд, архитектура с несколькими экспертами обеспечивает эффективное выполнение и следование командам."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "Модель MythoMax L2 13B, использующая новые технологии объединения, хорошо подходит для повествования и ролевых игр."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Phi 3 Vision для команд, легковесная мультимодальная модель, способная обрабатывать сложную визуальную и текстовую информацию, обладая высокой способностью к выводу."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "Модель QwQ — это экспериментальная исследовательская модель, разработанная командой Qwen, сосредоточенная на улучшении возможностей вывода ИИ."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "72B версия модели Qwen-VL — это результат последней итерации <PERSON>, представляющий собой инновации почти за год."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 - это серия языковых моделей, содержащая только декодеры, разработанная командой Qwen от Alibaba Cloud. Эти модели предлагаются в различных размерах: 0.5B, 1.5B, 3B, 7B, 14B, 32B и 72B, с вариантами базовой и инструкционной версии."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct — это последняя версия серии языковых моделей, специфичных для кода, выпущенная Alibaba Cloud. Эта модель значительно улучшила способности генерации кода, вывода и исправления на основе Qwen2.5, обучаясь на 5.5 триллионах токенов. Она не только усилила кодирование, но и сохранила преимущества в математике и общих способностях. Модель предоставляет более полную основу для практических приложений, таких как интеллектуальные агенты кода."}, "accounts/yi-01-ai/models/yi-large": {"description": "Модель Yi-Large, обладающая выдающимися возможностями обработки нескольких языков, подходит для различных задач генерации и понимания языка."}, "ai21-jamba-1.5-large": {"description": "Многоязычная модель с 398B параметрами (94B активных), предлагающая контекстное окно длиной 256K, вызовы функций, структурированный вывод и основанное на фактах генерирование."}, "ai21-jamba-1.5-mini": {"description": "Многоязычная модель с 52B параметрами (12B активных), предлагающая контекстное окно длиной 256K, вызовы функций, структурированный вывод и основанное на фактах генерирование."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Многоязычная модель с 398 млрд параметров (94 млрд активных), предоставляющая окно контекста длиной 256K, вызовы функций, структурированный вывод и генерацию на основе фактов."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Многоязычная модель с 52 млрд параметров (12 млрд активных), предоставляющая окно контекста длиной 256K, вызовы функций, структурированный вывод и генерацию на основе фактов."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet устанавливает новые отраслевые стандарты, превосходя модели конкурентов и Claude 3 Opus, демонстрируя отличные результаты в широком спектре оценок, при этом обладая скоростью и стоимостью наших моделей среднего уровня."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet установил новые стандарты в отрасли, превзойдя модели конкурентов и Claude 3 Opus, продемонстрировав отличные результаты в широкомасштабных оценках, при этом обладая скоростью и стоимостью наших моделей среднего уровня."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku — это самая быстрая и компактная модель от Anthropic, обеспечивающая почти мгновенную скорость ответа. Она может быстро отвечать на простые запросы и запросы. Клиенты смогут создать бесшовный AI-опыт, имитирующий человеческое взаимодействие. Claude 3 Haiku может обрабатывать изображения и возвращать текстовый вывод, имея контекстное окно в 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus — это самый мощный AI-модель от Anthropic, обладающая передовыми характеристиками в области высоко сложных задач. Она может обрабатывать открытые подсказки и невидимые сценарии, демонстрируя отличную плавность и человеческое понимание. Claude 3 Opus демонстрирует передовые возможности генеративного AI. Claude 3 Opus может обрабатывать изображения и возвращать текстовый вывод, имея контекстное окно в 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet от Anthropic достигает идеального баланса между интеллектом и скоростью — особенно подходит для корпоративных рабочих нагрузок. Он предлагает максимальную полезность по цене ниже конкурентов и разработан как надежный, высокопрочный основной механизм для масштабируемых AI-развертываний. Claude 3 Sonnet может обрабатывать изображения и возвращать текстовый вывод, имея контекстное окно в 200K."}, "anthropic.claude-instant-v1": {"description": "Быстрая, экономичная и все еще очень мощная модель, способная обрабатывать широкий спектр задач, включая повседневные диалоги, текстовый анализ, резюме и вопросы к документам."}, "anthropic.claude-v2": {"description": "Модель Anthropic демонстрирует высокие способности в широком спектре задач, от сложных диалогов и генерации креативного контента до детального следования инструкциям."}, "anthropic.claude-v2:1": {"description": "Обновленная версия Claude 2, обладающая двойным контекстным окном и улучшениями в надежности, уровне галлюцинаций и точности на основе доказательств в длинных документах и контексте RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku — это самая быстрая и компактная модель от Anthropic, предназначенная для почти мгновенных ответов. Она обладает быстрой и точной направленной производительностью."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus — это самая мощная модель от Anthropic для обработки высококомплексных задач. Она демонстрирует выдающиеся результаты по производительности, интеллекту, плавности и пониманию."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku — это самая быстрая модель следующего поколения от Anthropic. По сравнению с Claude 3 Haiku, Claude 3.5 Haiku продемонстрировала улучшения во всех навыках и превзошла предыдущую крупнейшую модель Claude 3 Opus во многих интеллектуальных бенчмарках."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet предлагает возможности, превосходящие Opus, и скорость, превышающую Sonnet, при этом сохраняя ту же цену. Sonnet особенно хорошо справляется с программированием, наукой о данных, визуальной обработкой и агентскими задачами."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet — это самая умная модель от Anthropic на сегодняшний день и первая в мире смешанная модель вывода. Claude 3.7 Sonnet может генерировать почти мгновенные ответы или длительные пошаговые размышления, позволяя пользователям четко видеть эти процессы. Sonnet особенно хорошо справляется с программированием, научными данными, визуальной обработкой и агентскими задачами."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 — самый мощный модель Anthropic для решения высоко сложных задач. Она демонстрирует выдающиеся показатели в производительности, интеллекте, плавности и понимании."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 способен генерировать практически мгновенные ответы или длительные поэтапные размышления, которые пользователи могут ясно отслеживать. API-пользователи также могут точно контролировать время размышлений модели."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B — это разреженная большая языковая модель с 72 миллиардами параметров и 16 миллиардами активных параметров, основанная на архитектуре группового смешанного эксперта (MoGE). В фазе выбора экспертов эксперты группируются, и токен активирует равное количество экспертов в каждой группе, что обеспечивает баланс нагрузки между экспертами и значительно повышает эффективность развертывания модели на платформе Ascend."}, "aya": {"description": "Aya 23 — это многоязычная модель, выпущенная Cohere, поддерживающая 23 языка, обеспечивая удобство для многоязычных приложений."}, "aya:35b": {"description": "Aya 23 — это многоязычная модель, выпущенная Cohere, поддерживающая 23 языка, обеспечивая удобство для многоязычных приложений."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B — это открытая коммерческая крупная языковая модель с 13 миллиардами параметров, разработанная Baichuan Intelligence, которая показала лучшие результаты среди моделей того же размера на авторитетных бенчмарках на китайском и английском языках."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B — большая языковая модель, разработанная компанией Baidu на основе архитектуры смешанных экспертов (MoE). Общий объём параметров модели составляет 300 миллиардов, однако при выводе активируется только 47 миллиардов параметров на токен, что обеспечивает высокую производительность при оптимальной вычислительной эффективности. Как одна из ключевых моделей серии ERNIE 4.5, она демонстрирует выдающиеся способности в задачах понимания текста, генерации, рассуждения и программирования. Модель использует инновационный метод предварительного обучения с мультимодальным гетерогенным MoE, объединяющий текстовые и визуальные модальности, что значительно повышает её универсальные возможности, особенно в следовании инструкциям и запоминании знаний о мире."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse — это высокопроизводительная многоязычная модель 32B, созданная для того, чтобы бросить вызов производительности одноязычных моделей с помощью инноваций в области настройки по инструкциям, арбитража данных, обучения предпочтениям и объединения моделей. Она поддерживает 23 языка."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse — это высокопроизводительная многоязычная модель 8B, созданная для того, чтобы бросить вызов производительности одноязычных моделей с помощью инноваций в области настройки по инструкциям, арбитража данных, обучения предпочтениям и объединения моделей. Она поддерживает 23 языка."}, "c4ai-aya-vision-32b": {"description": "Aya Vision — это передовая мультимодальная модель, которая демонстрирует отличные результаты по нескольким ключевым бенчмаркам в области языковых, текстовых и визуальных возможностей. Эта версия с 32 миллиардами параметров сосредоточена на передовых многоязычных результатах."}, "c4ai-aya-vision-8b": {"description": "Aya Vision — это передовая мультимодальная модель, которая демонстрирует отличные результаты по нескольким ключевым бенчмаркам в области языковых, текстовых и визуальных возможностей. Эта версия с 8 миллиардами параметров сосредоточена на низкой задержке и оптимальной производительности."}, "charglm-3": {"description": "CharGLM-3 разработан для ролевых игр и эмоционального сопровождения, поддерживает сверхдлинную многократную память и персонализированные диалоги, имеет широкое применение."}, "charglm-4": {"description": "CharGLM-4 разработан для ролевых игр и эмоционального сопровождения, поддерживает сверхдолгую многократную память и персонализированные диалоги, имеет широкое применение."}, "chatglm3": {"description": "ChatGLM3 — это закрытая модель, разработанная AI-лабораторией Tsinghua KEG и Zhipu AI. Она прошла предварительное обучение на огромном количестве китайских и английских данных и обучение на основе предпочтений человека. По сравнению с первой версией модели, она показала улучшение на 16%, 36% и 280% в тестах MMLU, C-Eval и GSM8K соответственно, и заняла первое место в китайском рейтинге задач C-Eval. Эта модель подходит для сценариев, требующих высокого уровня знаний, способности к рассуждению и креативности, таких как создание рекламных текстов, написание романов, научной письменности и генерации кода."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base — это базовая модель с открытым исходным кодом последнего поколения серии ChatGLM, разработанная компанией Zhipu, с 6 миллиардами параметров."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o — это динамическая модель, которая обновляется в реальном времени, чтобы оставаться актуальной. Она сочетает в себе мощное понимание языка и генерацию, подходя для масштабных приложений, включая обслуживание клиентов, образование и техническую поддержку."}, "claude-2.0": {"description": "Claude 2 предлагает ключевые улучшения для бизнеса, включая ведущие в отрасли 200K токенов контекста, значительное снижение частоты галлюцинаций модели, системные подсказки и новую тестовую функцию: вызов инструментов."}, "claude-2.1": {"description": "Claude 2 предлагает ключевые улучшения для бизнеса, включая ведущие в отрасли 200K токенов контекста, значительное снижение частоты галлюцинаций модели, системные подсказки и новую тестовую функцию: вызов инструментов."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku — это самая быстрая следующая модель от Anthropic. По сравнению с Claude 3 Haiku, Claude 3.5 Haiku продемонстрировала улучшения во всех навыках и превзошла предыдущую крупнейшую модель Claude 3 Opus во многих интеллектуальных тестах."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet предлагает возможности, превосходящие Opus, и скорость, быстрее Sonnet, при этом сохраняя ту же цену. Sonnet особенно хорош в программировании, науке о данных, визуальной обработке и задачах агентов."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet предлагает возможности, превышающие Opus, и скорость, превышающую Sonnet, при этом сохраняя ту же цену. Sonnet особенно хорош в программировании, данных, визуальной обработке и代理задачах."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet — это самая мощная модель от Anthropic, обладающая передовыми характеристиками в области высоко сложных задач. Она может обрабатывать открытые подсказки и невидимые сценарии, демонстрируя отличную плавность и человеческое понимание. Claude 3.7 Sonnet демонстрирует передовые возможности генеративного AI. Claude 3.7 Sonnet может обрабатывать изображения и возвращать текстовый вывод, имея контекстное окно в 200K."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku — это самая быстрая и компактная модель от Anthropic, предназначенная для достижения почти мгновенных ответов. Она обладает быстрой и точной направленной производительностью."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus — это самая мощная модель от Anthropic для обработки высококомплексных задач. Она демонстрирует выдающиеся результаты по производительности, интеллекту, плавности и пониманию."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet обеспечивает идеальный баланс между интеллектом и скоростью для корпоративных рабочих нагрузок. Он предлагает максимальную полезность по более низкой цене, надежен и подходит для масштабного развертывания."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 — это самая мощная модель Anthropic для решения высококомплексных задач. Она превосходно проявляет себя в производительности, интеллекте, плавности и понимании."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet может генерировать почти мгновенные ответы или продуманные шаги, позволяя пользователям четко видеть эти процессы. Пользователи API также могут детально контролировать время размышлений модели."}, "codegeex-4": {"description": "CodeGeeX-4 — это мощный AI помощник по программированию, поддерживающий интеллектуальные ответы и автозаполнение кода на различных языках программирования, повышая эффективность разработки."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B — это многоязычная модель генерации кода, поддерживающая полный спектр функций, включая автозаполнение и генерацию кода, интерпретатор кода, веб-поиск, вызовы функций и вопросы по коду на уровне репозитория, охватывающая различные сценарии разработки программного обеспечения. Это одна из лучших моделей генерации кода с количеством параметров менее 10B."}, "codegemma": {"description": "CodeGemma — это легковесная языковая модель, специально разработанная для различных задач программирования, поддерживающая быструю итерацию и интеграцию."}, "codegemma:2b": {"description": "CodeGemma — это легковесная языковая модель, специально разработанная для различных задач программирования, поддерживающая быструю итерацию и интеграцию."}, "codellama": {"description": "Code Llama — это LLM, сосредоточенная на генерации и обсуждении кода, поддерживающая широкий спектр языков программирования, подходит для среды разработчиков."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama — это LLM, сосредоточенная на генерации и обсуждении кода, с поддержкой широкого спектра языков программирования, подходящая для среды разработчиков."}, "codellama:13b": {"description": "Code Llama — это LLM, сосредоточенная на генерации и обсуждении кода, поддерживающая широкий спектр языков программирования, подходит для среды разработчиков."}, "codellama:34b": {"description": "Code Llama — это LLM, сосредоточенная на генерации и обсуждении кода, поддерживающая широкий спектр языков программирования, подходит для среды разработчиков."}, "codellama:70b": {"description": "Code Llama — это LLM, сосредоточенная на генерации и обсуждении кода, поддерживающая широкий спектр языков программирования, подходит для среды разработчиков."}, "codeqwen": {"description": "CodeQwen1.5 — это крупномасштабная языковая модель, обученная на большом объёме кодовых данных, специально разработанная для решения сложных задач программирования."}, "codestral": {"description": "Codestral — это первая модель кода от Mistral AI, обеспечивающая отличную поддержку для задач генерации кода."}, "codestral-latest": {"description": "Codestral — это передовая генеративная модель, сосредоточенная на генерации кода, оптимизированная для промежуточного заполнения и задач дополнения кода."}, "codex-mini-latest": {"description": "codex-mini-latest — это доработанная версия o4-mini, специально предназначенная для Codex CLI. Для прямого использования через API мы рекомендуем начинать с gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B — это модель, разработанная для соблюдения инструкций, диалогов и программирования."}, "cogview-4": {"description": "CogView-4 — это первая в истории Zhipu открытая модель текст-в-изображение, поддерживающая генерацию китайских иероглифов. Она значительно улучшена в понимании семантики, качестве генерации изображений и способности создавать тексты на китайском и английском языках. Модель поддерживает двуязычный ввод любой длины и может генерировать изображения с любым разрешением в заданных пределах."}, "cohere-command-r": {"description": "Command R — это масштабируемая генеративная модель, нацеленная на RAG и использование инструментов для обеспечения AI на уровне производства для предприятий."}, "cohere-command-r-plus": {"description": "Command R+ — это модель, оптимизированная для RAG, предназначенная для решения задач корпоративного уровня."}, "cohere/Cohere-command-r": {"description": "Command R — масштабируемая генеративная модель, разработанная для использования с RAG и инструментами, позволяющая компаниям внедрять AI промышленного уровня."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ — передовая оптимизированная модель для RAG, предназначенная для корпоративных рабочих нагрузок."}, "command": {"description": "Диалоговая модель, следуя инструкциям, которая демонстрирует высокое качество и надежность в языковых задачах, а также имеет более длинную длину контекста по сравнению с нашей базовой генеративной моделью."}, "command-a-03-2025": {"description": "Команда A — это наша самая мощная модель на сегодняшний день, которая демонстрирует отличные результаты в использовании инструментов, агентировании, улучшенной генерации с помощью поиска (RAG) и многоязычных приложениях. Команда A имеет длину контекста 256K и может работать всего на двух GPU, а производительность увеличилась на 150% по сравнению с Command R+ 08-2024."}, "command-light": {"description": "Более компактная и быстрая версия Command, почти столь же мощная, но более быстрая."}, "command-light-nightly": {"description": "Чтобы сократить временные интервалы между основными выпусками, мы представили ночную версию модели Command. Для серии command-light эта версия называется command-light-nightly. Обратите внимание, что command-light-nightly — это самая последняя, экспериментальная и (возможно) нестабильная версия. Ночные версии регулярно обновляются без предварительного уведомления, поэтому их не рекомендуется использовать в производственной среде."}, "command-nightly": {"description": "Чтобы сократить временные интервалы между основными выпусками, мы представили ночную версию модели Command. Для серии Command эта версия называется command-cightly. Обратите внимание, что command-nightly — это самая последняя, экспериментальная и (возможно) нестабильная версия. Ночные версии регулярно обновляются без предварительного уведомления, поэтому их не рекомендуется использовать в производственной среде."}, "command-r": {"description": "Command R — это LLM, оптимизированная для диалогов и задач с длинным контекстом, особенно подходит для динамического взаимодействия и управления знаниями."}, "command-r-03-2024": {"description": "Команда R — это диалоговая модель, следуя инструкциям, которая демонстрирует более высокое качество и надежность в языковых задачах, а также имеет более длинную длину контекста по сравнению с предыдущими моделями. Она может использоваться для сложных рабочих процессов, таких как генерация кода, улучшенная генерация с помощью поиска (RAG), использование инструментов и агентирование."}, "command-r-08-2024": {"description": "command-r-08-2024 — это обновленная версия модели Command R, выпущенная в августе 2024 года."}, "command-r-plus": {"description": "Command R+ — это высокопроизводительная большая языковая модель, специально разработанная для реальных бизнес-сценариев и сложных приложений."}, "command-r-plus-04-2024": {"description": "Команда R+ — это диалоговая модель, следуя инструкциям, которая демонстрирует более высокое качество и надежность в языковых задачах, а также имеет более длинную длину контекста по сравнению с предыдущими моделями. Она лучше всего подходит для сложных рабочих процессов RAG и многократного использования инструментов."}, "command-r-plus-08-2024": {"description": "Command R+ — это диалоговая модель, следящая за инструкциями, которая демонстрирует более высокое качество и надежность в языковых задачах, а также имеет более длинную длину контекста по сравнению с предыдущими моделями. Она наиболее подходит для сложных рабочих процессов RAG и многошагового использования инструментов."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 — это компактная и эффективная обновленная версия, выпущенная в декабре 2024 года. Она демонстрирует отличные результаты в задачах, требующих сложного рассуждения и многократной обработки, таких как RAG, использование инструментов и агентирование."}, "compound-beta": {"description": "Compound-beta — это комплексная AI-система, поддерживаемая несколькими открытыми доступными моделями в GroqCloud, которая может интеллектуально и выборочно использовать инструменты для ответа на запросы пользователей."}, "compound-beta-mini": {"description": "Compound-beta-mini — это комплексная AI-система, поддерживаемая открытыми доступными моделями в GroqCloud, которая может интеллектуально и выборочно использовать инструменты для ответа на запросы пользователей."}, "computer-use-preview": {"description": "Модель computer-use-preview специально разработана для «инструментов использования компьютера» и обучена понимать и выполнять задачи, связанные с компьютером."}, "dall-e-2": {"description": "Вторая генерация модели DALL·E, поддерживающая более реалистичную и точную генерацию изображений с разрешением в 4 раза выше, чем у первой генерации."}, "dall-e-3": {"description": "Последняя модель DALL·E, выпущенная в ноябре 2023 года. Поддерживает более реалистичную и точную генерацию изображений с более сильной детализацией."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct предлагает высокую надежность в обработке команд, поддерживая приложения в различных отраслях."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 — это модель вывода, управляемая методом обучения с подкреплением (RL), которая решает проблемы повторяемости и читаемости модели. Перед применением RL DeepSeek-R1 вводит данные холодного старта, что дополнительно оптимизирует производительность вывода. Она показывает сопоставимые результаты с OpenAI-o1 в математических, кодовых и задачах вывода, а также улучшает общую эффективность благодаря тщательно разработанным методам обучения."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 значительно улучшил глубину рассуждений и выводов, используя увеличенные вычислительные ресурсы и алгоритмические оптимизации в процессе дообучения. Модель демонстрирует отличные результаты в различных бенчмарках, включая математику, программирование и общую логику. Общая производительность теперь близка к ведущим моделям, таким как O3 и Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B — модель, полученная путем дистилляции цепочек рассуждений из DeepSeek-R1-0528 в Qwen3 8B Base. Эта модель достигла передовых результатов (SOTA) среди открытых моделей, превзойдя Qwen3 8B на 10% в тесте AIME 2024 и достигнув уровня производительности Qwen3-235B-thinking. Модель демонстрирует отличные результаты в математическом рассуждении, программировании и общей логике, имеет архитектуру, аналогичную Qwen3-8B, но использует токенизатор DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "Модель DeepSeek-R1, дистиллированная с помощью усиленного обучения и данных холодного старта, оптимизирует производительность вывода, обновляя стандарт многозадачности в открытых моделях."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Модель DeepSeek-R1, дистиллированная с помощью усиленного обучения и данных холодного старта, оптимизирует производительность вывода, обновляя стандарт многозадачности в открытых моделях."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "Модель DeepSeek-R1, дистиллированная с помощью усиленного обучения и данных холодного старта, оптимизирует производительность вывода, обновляя стандарт многозадачности в открытых моделях."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B — это модель, полученная с помощью дистилляции на основе Qwen2.5-32B. Эта модель была дообучена на 800000 отобранных образцах, сгенерированных DeepSeek-R1, и демонстрирует выдающуюся производительность в таких областях, как математика, программирование и логика. Она показала отличные результаты в нескольких бенчмарках, включая AIME 2024, MATH-500 и GPQA Diamond, достигнув 94.3% точности на MATH-500, демонстрируя мощные способности математического вывода."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B — это модель, полученная с помощью дистилляции на основе Qwen2.5-Math-7B. Эта модель была дообучена на 800000 отобранных образцах, сгенерированных DeepSeek-R1, и демонстрирует отличные способности вывода. Она показала выдающиеся результаты в нескольких бенчмарках, включая 92.8% точности на MATH-500, 55.5% проходной уровень на AIME 2024 и 1189 баллов на CodeForces, демонстрируя сильные математические и программные способности для модели объемом 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 объединяет отличительные черты предыдущих версий, улучшая общие и кодировочные способности."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 — это языковая модель смешанных экспертов (MoE) с 6710 миллиардами параметров, использующая многоголовое потенциальное внимание (MLA) и архитектуру DeepSeekMoE, в сочетании с стратегией балансировки нагрузки без вспомогательных потерь, оптимизирующей эффективность вывода и обучения. После предобучения на 14,8 триллионах высококачественных токенов и последующей супервизионной донастройки и обучения с подкреплением, DeepSeek-V3 превосходит другие открытые модели и приближается к ведущим закрытым моделям."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B — это передовая модель, обученная для высококомплексных диалогов."}, "deepseek-ai/deepseek-r1": {"description": "Современная эффективная LLM, специализирующаяся на рассуждениях, математике и программировании."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 — это модель визуального языка, разработанная на основе DeepSeekMoE-27B, использующая архитектуру MoE с разреженной активацией, которая демонстрирует выдающуюся производительность при активации всего 4,5 миллиарда параметров. Эта модель показывает отличные результаты в таких задачах, как визуальные вопросы и ответы, оптическое распознавание символов, понимание документов/таблиц/графиков и визуальная локализация."}, "deepseek-chat": {"description": "Новая открытая модель, объединяющая общие и кодовые возможности, не только сохраняет общие диалоговые способности оригинальной модели Chat и мощные возможности обработки кода модели Coder, но и лучше согласуется с человеческими предпочтениями. Кроме того, DeepSeek-V2.5 значительно улучшила производительность в таких задачах, как написание текстов и следование инструкциям."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B — это модель языкового кодирования, обученная на 20 триллионах данных, из которых 87% составляют код, а 13% — китайский и английский языки. Модель использует размер окна 16K и задачи заполнения пропусков, предоставляя функции автозаполнения кода и заполнения фрагментов на уровне проектов."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 — это открытая смешанная экспертная модель кода, показывающая отличные результаты в задачах кода, сопоставимая с GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 — это открытая смешанная экспертная модель кода, показывающая отличные результаты в задачах кода, сопоставимая с GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 — это модель вывода, управляемая методом обучения с подкреплением (RL), которая решает проблемы повторяемости и читаемости модели. Перед применением RL DeepSeek-R1 вводит данные холодного старта, что дополнительно оптимизирует производительность вывода. Она показывает сопоставимые результаты с OpenAI-o1 в математических, кодовых и задачах вывода, а также улучшает общую эффективность благодаря тщательно разработанным методам обучения."}, "deepseek-r1-0528": {"description": "Модель полной мощности с 685 миллиардами параметров, выпущенная 28 мая 2025 года. DeepSeek-R1 широко использует методы обучения с подкреплением на этапе дообучения, что значительно улучшает способности модели к рассуждению при минимальном количестве размеченных данных. Высокая производительность и сильные возможности в задачах математики, программирования и естественно-языкового вывода."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B быстрая версия, поддерживающая онлайн-поиск в реальном времени, обеспечивающая более быструю скорость отклика при сохранении производительности модели."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B стандартная версия, поддерживающая онлайн-поиск в реальном времени, подходит для диалогов и текстовых задач, требующих актуальной информации."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama — это модель, полученная путём дистилляции из DeepSeek-R1 на основе Llama."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 — более крупная и умная модель в наборе DeepSeek, была дистиллирована в архитектуру Llama 70B. На основе бенчмарков и ручной оценки эта модель более умная, особенно в задачах, требующих математической и фактической точности."}, "deepseek-r1-distill-llama-8b": {"description": "Модели серии DeepSeek-R1-Distill были получены с помощью технологии дистилляции знаний, донастраивая образцы, сгенерированные DeepSeek-R1, на открытых моделях, таких как Qwen и Llama."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Выпущена 14 февраля 2025 года, дистиллированная модель, разработанная командой Qianfan на основе Llama3_70B (создана с использованием Meta Llama), в дистиллированные данные также были добавлены материалы Qianfan."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Выпущена 14 февраля 2025 года, дистиллированная модель, разработанная командой Qianfan на основе Llama3_8B (создана с использованием Meta Llama), в дистиллированные данные также были добавлены материалы Qianfan."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen — это модель, полученная методом дистилляции из DeepSeek-R1 на основе Qwen."}, "deepseek-r1-distill-qwen-1.5b": {"description": "Модели серии DeepSeek-R1-Distill были получены с помощью технологии дистилляции знаний, донастраивая образцы, сгенерированные DeepSeek-R1, на открытых моделях, таких как Qwen и Llama."}, "deepseek-r1-distill-qwen-14b": {"description": "Модели серии DeepSeek-R1-Distill были получены с помощью технологии дистилляции знаний, донастраивая образцы, сгенерированные DeepSeek-R1, на открытых моделях, таких как Qwen и Llama."}, "deepseek-r1-distill-qwen-32b": {"description": "Модели серии DeepSeek-R1-Distill были получены с помощью технологии дистилляции знаний, донастраивая образцы, сгенерированные DeepSeek-R1, на открытых моделях, таких как Qwen и Llama."}, "deepseek-r1-distill-qwen-7b": {"description": "Модели серии DeepSeek-R1-Distill были получены с помощью технологии дистилляции знаний, донастраивая образцы, сгенерированные DeepSeek-R1, на открытых моделях, таких как Qwen и Llama."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 полная быстрая версия, поддерживающая онлайн-поиск в реальном времени, объединяющая мощные возможности 671B параметров и более быструю скорость отклика."}, "deepseek-r1-online": {"description": "DeepSeek R1 полная версия, имеющая 671B параметров, поддерживающая онлайн-поиск в реальном времени, обладающая более мощными способностями понимания и генерации."}, "deepseek-reasoner": {"description": "Модель вывода, представленная DeepSeek. Перед тем как выдать окончательный ответ, модель сначала выводит цепочку размышлений, чтобы повысить точность окончательного ответа."}, "deepseek-v2": {"description": "DeepSeek V2 — это эффективная языковая модель Mixture-of-Experts, подходящая для экономически эффективных потребностей обработки."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B — это модель кода DeepSeek, обеспечивающая мощные возможности генерации кода."}, "deepseek-v3": {"description": "DeepSeek-V3 — это модель MoE, разработанная компанией Hangzhou DeepSeek AI Technology Research Co., Ltd., которая показывает выдающиеся результаты в нескольких тестах и занимает первое место среди открытых моделей в основных рейтингах. V3 по сравнению с моделью V2.5 увеличила скорость генерации в 3 раза, обеспечивая пользователям более быстрое и плавное использование."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 — это модель MoE с 671 миллиардами параметров, обладающая выдающимися способностями в программировании и технических навыках, понимании контекста и обработке длинных текстов."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 — это экспертная смешанная модель с 685B параметрами, являющаяся последней итерацией флагманской серии чат-моделей команды DeepSeek.\n\nОна унаследовала модель [DeepSeek V3](/deepseek/deepseek-chat-v3) и демонстрирует отличные результаты в различных задачах."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 — это экспертная смешанная модель с 685B параметрами, являющаяся последней итерацией флагманской серии чат-моделей команды DeepSeek.\n\nОна унаследовала модель [DeepSeek V3](/deepseek/deepseek-chat-v3) и демонстрирует отличные результаты в различных задачах."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 значительно улучшила способности модели к рассуждению при наличии лишь очень ограниченных размеченных данных. Перед тем как предоставить окончательный ответ, модель сначала выводит цепочку размышлений, чтобы повысить точность окончательного ответа."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 значительно улучшил способность модели к рассуждению при минимальном количестве размеченных данных. Перед выводом окончательного ответа модель сначала генерирует цепочку рассуждений для повышения точности ответа."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 значительно улучшил способность модели к рассуждению при минимальном количестве размеченных данных. Перед выводом окончательного ответа модель сначала генерирует цепочку рассуждений для повышения точности ответа."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B — это крупная языковая модель на основе Llama3.3 70B, которая использует доработку, полученную от DeepSeek R1, для достижения конкурентоспособной производительности, сопоставимой с крупными передовыми моделями."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B — это дистиллированная большая языковая модель на основе Llama-3.1-8B-Instruct, обученная с использованием выходных данных DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B — это дистиллированная большая языковая модель на основе Qwen 2.5 14B, обученная с использованием выходных данных DeepSeek R1. Эта модель превзошла o1-mini от OpenAI в нескольких бенчмарках, достигнув последних достижений в области плотных моделей (state-of-the-art). Вот некоторые результаты бенчмарков:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nРейтинг CodeForces: 1481\nЭта модель, доработанная на основе выходных данных DeepSeek R1, демонстрирует конкурентоспособную производительность, сопоставимую с более крупными передовыми моделями."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B — это дистиллированная большая языковая модель на основе Qwen 2.5 32B, обученная с использованием выходных данных DeepSeek R1. Эта модель превзошла o1-mini от OpenAI в нескольких бенчмарках, достигнув последних достижений в области плотных моделей (state-of-the-art). Вот некоторые результаты бенчмарков:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nРейтинг CodeForces: 1691\nЭта модель, доработанная на основе выходных данных DeepSeek R1, демонстрирует конкурентоспособную производительность, сопоставимую с более крупными передовыми моделями."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 — это последняя версия открытой модели, выпущенной командой DeepSeek, обладающая выдающимися возможностями вывода, особенно в математических, программных и логических задачах, достигая уровня, сопоставимого с моделью o1 от OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 значительно улучшила способности модели к рассуждению при наличии лишь очень ограниченных размеченных данных. Перед тем как предоставить окончательный ответ, модель сначала выводит цепочку размышлений, чтобы повысить точность окончательного ответа."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 достиг значительного прорыва в скорости вывода по сравнению с предыдущими моделями. Она занимает первое место среди открытых моделей и может соперничать с самыми современными закрытыми моделями в мире. DeepSeek-V3 использует архитектуры многоголового потенциального внимания (MLA) и DeepSeekMoE, которые были полностью проверены в DeepSeek-V2. Кроме того, DeepSeek-V3 внедрила вспомогательную безубыточную стратегию для балансировки нагрузки и установила цели обучения для многомаркерного прогнозирования для достижения более высокой производительности."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 достиг значительного прорыва в скорости вывода по сравнению с предыдущими моделями. Она занимает первое место среди открытых моделей и может соперничать с самыми современными закрытыми моделями в мире. DeepSeek-V3 использует архитектуры многоголового потенциального внимания (MLA) и DeepSeekMoE, которые были полностью проверены в DeepSeek-V2. Кроме того, DeepSeek-V3 внедрила вспомогательную безубыточную стратегию для балансировки нагрузки и установила цели обучения для многомаркерного прогнозирования для достижения более высокой производительности."}, "deepseek_r1": {"description": "DeepSeek-R1 — это модель вывода, управляемая усиленным обучением (RL), которая решает проблемы повторяемости и читаемости в модели. Перед RL DeepSeek-R1 вводит данные холодного старта, что дополнительно оптимизирует производительность вывода. Она демонстрирует сопоставимые результаты с OpenAI-o1 в задачах математики, кода и вывода, и благодаря тщательно разработанным методам обучения улучшает общие результаты."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B — это модель, полученная на основе Llama-3.3-70B-Instruct через дистилляцию обучения. Эта модель является частью серии DeepSeek-R1 и демонстрирует отличные результаты в математике, программировании и выводе, используя образцы, сгенерированные DeepSeek-R1, для дообучения."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B — это модель, полученная на основе Qwen2.5-14B через дистилляцию знаний. Эта модель была дообучена на 800000 отобранных образцах, сгенерированных DeepSeek-R1, демонстрируя отличные способности к выводу."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B — это модель, полученная на основе Qwen2.5-32B через дистилляцию знаний. Эта модель была дообучена на 800000 отобранных образцах, сгенерированных DeepSeek-R1, демонстрируя выдающиеся результаты в различных областях, включая математику, программирование и вывод."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite - совершенно новое поколение легкой модели, с максимальной скоростью отклика, результаты и задержка достигают мирового уровня."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k основан на полностью обновленной версии Doubao-1.5-Pro, с общим улучшением на 10%. Поддерживает вывод на 256k контекстных окнах, максимальная длина вывода составляет 12k токенов. Более высокая производительность, большее окно, отличное соотношение цена-качество, подходит для более широкого спектра приложений."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro - совершенно новое поколение основного модели, с полностью обновленной производительностью, выдающимися результатами в области знаний, кода, логики и других аспектов."}, "doubao-1.5-thinking-pro": {"description": "Doubao-1.5 — это новая модель глубокого мышления, которая демонстрирует выдающиеся результаты в таких профессиональных областях, как математика, программирование, научное мышление, а также в универсальных задачах креативного письма. Она достигает или приближается к уровню первой группы в отрасли на нескольких авторитетных бенчмарках, таких как AIME 2024, Codeforces, GPQA. Поддерживает контекстное окно 128k и вывод 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Новая глубокая модель мышления Doubao-1.5 (версия m оснащена нативной мультимодальной глубокой способностью вывода), демонстрирует выдающиеся результаты в профессиональных областях, таких как математика, программирование, научное рассуждение, а также в творческом письме и универсальных задачах. Достигла или приблизилась к первому уровню в отрасли по ряду авторитетных бенчмарков, включая AIME 2024, Codeforces, GPQA. Поддерживает контекстное окно 128k и вывод до 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Новая визуальная модель глубокого мышления с усиленными универсальными мультимодальными возможностями понимания и вывода, достигшая SOTA результатов в 37 из 59 открытых бенчмарков."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS — нативная модель агента, ориентированная на взаимодействие с графическим интерфейсом пользователя (GUI). Обеспечивает бесшовное взаимодействие с GUI через восприятие, рассуждение и действия, имитируя человеческие способности."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite — это новая усовершенствованная мультимодальная модель, поддерживающая распознавание изображений с любым разрешением и экстремальным соотношением сторон, улучшая способности к визуальному выводу, распознаванию документов, пониманию детальной информации и соблюдению инструкций. Поддерживает контекстное окно 128k, максимальная длина вывода составляет 16k токенов."}, "doubao-1.5-vision-pro": {"description": "Полностью обновленная мультимодальная крупная модель Doubao-1.5-vision-pro, поддерживающая распознавание изображений с любым разрешением и экстремальными соотношениями сторон, улучшенная визуальная логика, распознавание документов, понимание деталей и следование инструкциям."}, "doubao-1.5-vision-pro-32k": {"description": "Полностью обновленная мультимодальная крупная модель Doubao-1.5-vision-pro, поддерживающая распознавание изображений с любым разрешением и экстремальными соотношениями сторон, улучшенная визуальная логика, распознавание документов, понимание деталей и следование инструкциям."}, "doubao-lite-128k": {"description": "Обладает исключительной скоростью отклика и лучшим соотношением цена-качество, предоставляя клиентам более гибкие варианты для различных сценариев. Поддерживает вывод и дообучение с контекстным окном в 128k."}, "doubao-lite-32k": {"description": "Обладает исключительной скоростью отклика и лучшим соотношением цена-качество, предоставляя клиентам более гибкие варианты для различных сценариев. Поддерживает вывод и дообучение с контекстным окном в 32k."}, "doubao-lite-4k": {"description": "Обладает исключительной скоростью отклика и лучшим соотношением цена-качество, предоставляя клиентам более гибкие варианты для различных сценариев. Поддерживает вывод и дообучение с контекстным окном в 4k."}, "doubao-pro-256k": {"description": "Основная модель с наилучшей производительностью, подходящая для решения сложных задач. Отлично справляется с вопросами-ответами, резюмированием, творческим написанием, классификацией текста, ролевыми играми и другими сценариями. Поддерживает вывод и дообучение с контекстным окном в 256k."}, "doubao-pro-32k": {"description": "Основная модель с наилучшей производительностью, подходящая для решения сложных задач. Отлично справляется с вопросами-ответами, резюмированием, творческим написанием, классификацией текста, ролевыми играми и другими сценариями. Поддерживает вывод и дообучение с контекстным окном в 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 — новая мультимодальная модель глубокого мышления, поддерживающая три режима мышления: auto, thinking и non-thinking. В режиме non-thinking производительность модели значительно выше по сравнению с Doubao-1.5-pro/250115. Поддерживает контекстное окно размером 256k и максимальную длину вывода до 16k токенов."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash — мультимодальная модель глубокого мышления с экстремально высокой скоростью вывода, TPOT занимает всего 10 мс; поддерживает понимание текста и визуальных данных, текстовое понимание превосходит предыдущую lite-версию, визуальное понимание сопоставимо с pro-серией конкурентов. Поддерживает контекстное окно 256k и максимальную длину вывода до 16k токенов."}, "doubao-seed-1.6-thinking": {"description": "Мод<PERSON><PERSON>ь Doubao-Seed-1.6-thinking значительно улучшена в плане мышления, по сравнению с Doubao-1.5-thinking-pro дополнительно повышены базовые способности в программировании, математике и логическом рассуждении, поддерживается визуальное понимание. Поддерживает контекстное окно 256k и максимальную длину вывода до 16k токенов."}, "doubao-seedream-3-0-t2i-250415": {"description": "Модель генерации изображений Doubao разработана командой Seed компании ByteDance, поддерживает ввод текста и изображений, обеспечивая высококонтролируемый и качественный опыт генерации изображений на основе текстовых подсказок."}, "doubao-vision-lite-32k": {"description": "Модель Doubao-vision — мультимодальная крупная модель от Doubao, обладающая мощными возможностями понимания и вывода по изображениям, а также точным пониманием инструкций. Модель демонстрирует высокую производительность в задачах извлечения информации из изображений и текстов, а также в задачах вывода на основе изображений, что позволяет применять её в более сложных и широких визуальных вопросах."}, "doubao-vision-pro-32k": {"description": "Модель Doubao-vision — мультимодальная крупная модель от Doubao, обладающая мощными возможностями понимания и вывода по изображениям, а также точным пониманием инструкций. Модель демонстрирует высокую производительность в задачах извлечения информации из изображений и текстов, а также в задачах вывода на основе изображений, что позволяет применять её в более сложных и широких визуальных вопросах."}, "emohaa": {"description": "Emohaa — это психологическая модель, обладающая профессиональными консультационными способностями, помогающая пользователям понимать эмоциональные проблемы."}, "ernie-3.5-128k": {"description": "Флагманская большая языковая модель, разработанная Baidu, охватывающая огромные объемы китайских и английских текстов, обладающая мощными универсальными способностями, способная удовлетворить требования большинства сценариев диалогов, генерации контента и применения плагинов; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации."}, "ernie-3.5-8k": {"description": "Флагманская большая языковая модель, разработанная Baidu, охватывающая огромные объемы китайских и английских текстов, обладающая мощными универсальными способностями, способная удовлетворить требования большинства сценариев диалогов, генерации контента и применения плагинов; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации."}, "ernie-3.5-8k-preview": {"description": "Флагманская большая языковая модель, разработанная Baidu, охватывающая огромные объемы китайских и английских текстов, обладающая мощными универсальными способностями, способная удовлетворить требования большинства сценариев диалогов, генерации контента и применения плагинов; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации."}, "ernie-4.0-8k-latest": {"description": "Флагманская сверхбольшая языковая модель, разработанная Baidu, по сравнению с ERNIE 3.5 демонстрирует полное обновление возможностей модели, широко применима в сложных задачах различных областей; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации."}, "ernie-4.0-8k-preview": {"description": "Флагманская сверхбольшая языковая модель, разработанная Baidu, по сравнению с ERNIE 3.5 демонстрирует полное обновление возможностей модели, широко применима в сложных задачах различных областей; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации."}, "ernie-4.0-turbo-128k": {"description": "Флагманская сверхбольшая языковая модель, разработанная Baidu, демонстрирует отличные результаты в комплексных задачах, широко применима в различных областях; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации. По сравнению с ERNIE 4.0, она показывает лучшие результаты."}, "ernie-4.0-turbo-8k-latest": {"description": "Флагманская сверхбольшая языковая модель, разработанная Baidu, демонстрирует отличные результаты в комплексных задачах, широко применима в различных областях; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации. По сравнению с ERNIE 4.0, она показывает лучшие результаты."}, "ernie-4.0-turbo-8k-preview": {"description": "Флагманская сверхбольшая языковая модель, разработанная Baidu, демонстрирует отличные результаты в комплексных задачах, широко применима в различных областях; поддерживает автоматическое подключение к плагину поиска Baidu, обеспечивая актуальность информации. По сравнению с ERNIE 4.0, она показывает лучшие результаты."}, "ernie-4.5-8k-preview": {"description": "Модель Ernie 4.5 — это новое поколение оригинальной мультимодальной базовой модели, разработанной Baidu, которая достигает совместной оптимизации через совместное моделирование нескольких модальностей, обладая отличными способностями к мультимодальному пониманию; обладает более совершенными языковыми способностями, улучшенными способностями к пониманию, генерации, логике и памяти, а также значительно улучшенными способностями к устранению галлюцинаций, логическому выводу и кодированию."}, "ernie-4.5-turbo-128k": {"description": "Модель ERNIE 4.5 Turbo значительно улучшила свои способности в области устранения галлюцинаций, логического вывода и программирования. По сравнению с ERNIE 4.5, она быстрее и дешевле. Способности модели значительно улучшены, что лучше удовлетворяет потребности в обработке многократных длинных историй диалогов и задачах понимания длинных документов."}, "ernie-4.5-turbo-32k": {"description": "Модель ERNIE 4.5 Turbo также значительно улучшила свои способности в области устранения галлюцинаций, логического вывода и программирования. По сравнению с ERNIE 4.5, она быстрее и дешевле. Способности в текстовом творчестве и вопросах и ответах значительно возросли. Длина вывода и задержка целых предложений увеличились по сравнению с ERNIE 4.5."}, "ernie-4.5-turbo-vl-32k": {"description": "Новая версия большой модели ERNIE с заметным улучшением в понимании изображений, творчестве, переводе и программировании, впервые поддерживает длину контекста 32K, задержка первого токена значительно снижена."}, "ernie-char-8k": {"description": "Специализированная большая языковая модель, разработанная Baidu, подходящая для применения в игровых NPC, диалогах службы поддержки, ролевых играх и других сценариях, с более ярким и последовательным стилем персонажей, более высокой способностью следовать инструкциям и лучшей производительностью вывода."}, "ernie-char-fiction-8k": {"description": "Специализированная большая языковая модель, разработанная Baidu, подходящая для применения в игровых NPC, диалогах службы поддержки, ролевых играх и других сценариях, с более ярким и последовательным стилем персонажей, более высокой способностью следовать инструкциям и лучшей производительностью вывода."}, "ernie-irag-edit": {"description": "Собственная модель редактирования изображений ERNIE iRAG от Baidu поддерживает операции удаления объектов (erase), перерисовки (repaint) и вариаций (variation) на основе изображений."}, "ernie-lite-8k": {"description": "ERNIE Lite — это легковесная большая языковая модель, разработанная Baidu, которая сочетает в себе отличные результаты модели и производительность вывода, подходит для использования на AI-ускорителях с низкой вычислительной мощностью."}, "ernie-lite-pro-128k": {"description": "Легковесная большая языковая модель, разработанная Baidu, которая сочетает в себе отличные результаты модели и производительность вывода, превосходя ERNIE Lite, подходит для использования на AI-ускорителях с низкой вычислительной мощностью."}, "ernie-novel-8k": {"description": "Универсальная большая языковая модель, разработанная Baidu, обладающая явными преимуществами в способности продолжать написание романов, также может использоваться в сценариях коротких пьес и фильмов."}, "ernie-speed-128k": {"description": "Новая высокопроизводительная большая языковая модель, разработанная Baidu в 2024 году, обладающая выдающимися универсальными способностями, подходит для использования в качестве базовой модели для тонкой настройки, лучше справляясь с проблемами конкретных сценариев, при этом обладая отличной производительностью вывода."}, "ernie-speed-pro-128k": {"description": "Новая высокопроизводительная большая языковая модель, разработанная Baidu в 2024 году, обладающая выдающимися универсальными способностями, превосходя ERNIE Speed, подходит для использования в качестве базовой модели для тонкой настройки, лучше справляясь с проблемами конкретных сценариев, при этом обладая отличной производительностью вывода."}, "ernie-tiny-8k": {"description": "ERNIE Tiny — это сверхвысокопроизводительная большая языковая модель, стоимость развертывания и тонкой настройки которой является самой низкой среди моделей серии Wen<PERSON>."}, "ernie-x1-32k": {"description": "Обладает более сильными способностями к пониманию, планированию, рефлексии и эволюции. Как более универсальная модель глубокого мышления, ERNIE-X1 сочетает в себе точность, креативность и литературный стиль, особенно хорошо проявляя себя в китайских вопросах и ответах, литературном творчестве, написании текстов, повседневных диалогах, логическом выводе, сложных вычислениях и вызовах инструментов."}, "ernie-x1-32k-preview": {"description": "Модель Ernie X1 обладает более сильными способностями к пониманию, планированию, рефлексии и эволюции. Как более универсальная модель глубокого мышления, Ernie X1 сочетает в себе точность, креативность и литературный стиль, особенно выделяясь в области вопросов и ответов на китайском языке, литературного творчества, написания текстов, повседневных диалогов, логического вывода, сложных вычислений и вызова инструментов."}, "ernie-x1-turbo-32k": {"description": "Модель имеет лучшие результаты и производительность по сравнению с ERNIE-X1-32K."}, "flux-1-schnell": {"description": "Модель генерации изображений из текста с 12 миллиардами параметров, разработанная Black Forest Labs, использующая технологию латентного контрольно-диффузионного дистиллята. Способна генерировать высококачественные изображения за 1–4 шага. Производительность сопоставима с закрытыми аналогами. Распространяется под лицензией Apache-2.0, подходит для личного, научного и коммерческого использования."}, "flux-dev": {"description": "FLUX.1 [dev] — открытая модель с весами и оптимизациями для некоммерческого использования. Обеспечивает качество изображений и следование инструкциям, близкие к профессиональной версии FLUX, при более высокой эффективности работы и лучшем использовании ресурсов по сравнению с моделями того же размера."}, "flux-kontext/dev": {"description": "Модель редактирования изображений Frontier."}, "flux-merged": {"description": "Модель FLUX.1-merged объединяет глубокие особенности, исследованные в фазе разработки \"DEV\", и преимущества высокой скорости исполнения, представленные в \"Schnell\". Это позволяет расширить границы производительности модели и увеличить её применимость."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] способен обрабатывать текст и эталонные изображения в качестве входных данных, обеспечивая бесшовное целенаправленное локальное редактирование и сложные преобразования всей сцены."}, "flux-schnell": {"description": "FLUX.1 [schnell] — самая передовая открытая модель с малым числом шагов генерации, превосходящая конкурентов и даже такие мощные не дистиллированные модели, как Midjourney v6.0 и DALL·E 3 (HD). Модель специально донастроена для сохранения всего разнообразия выходных данных, достигнутого на этапе предобучения. По сравнению с современными топовыми моделями на рынке, FLUX.1 [schnell] значительно улучшает визуальное качество, следование инструкциям, изменение размеров и пропорций, обработку шрифтов и разнообразие выходных данных, обеспечивая пользователям более богатый и разнообразный творческий опыт генерации изображений."}, "flux.1-schnell": {"description": "Исправленный потоковый трансформер с 12 миллиардами параметров, способный генерировать изображения на основе текстовых описаний."}, "flux/schnell": {"description": "FLUX.1 [schnell] — это потоковая трансформерная модель с 12 миллиардами параметров, способная генерировать высококачественные изображения из текста за 1–4 шага, подходит для личного и коммерческого использования."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) предлагает стабильную и настраиваемую производительность, что делает её идеальным выбором для решения сложных задач."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Тю<PERSON><PERSON><PERSON><PERSON>) предлагает выдающуюся поддержку многомодальности, сосредотачиваясь на эффективном решении сложных задач."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro — это высокопроизводительная модель ИИ от Google, разработанная для масштабирования широкого спектра задач."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 — это эффективная многомодальная модель, поддерживающая масштабирование для широкого спектра приложений."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 — это эффективная мультимодальная модель, поддерживающая расширенные применения."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B — это высокоэффективная многомодальная модель, поддерживающая широкий спектр приложений."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 — это последняя экспериментальная модель, которая демонстрирует значительное улучшение производительности как в текстовых, так и в мультимодальных задачах."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B — это эффективная мультимодальная модель, поддерживающая широкий спектр приложений."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 предлагает оптимизированные многомодальные возможности обработки, подходящие для различных сложных задач."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash — это последняя многомодальная модель ИИ от Google, обладающая высокой скоростью обработки и поддерживающая текстовые, графические и видео входы, что делает её эффективной для масштабирования различных задач."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 — это масштабируемое решение для многомодального ИИ, поддерживающее широкий спектр сложных задач."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 — это последняя модель, готовая к производству, которая обеспечивает более высокое качество вывода, особенно в математических задачах, длинных контекстах и визуальных задачах."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 предлагает выдающиеся многомодальные возможности обработки, обеспечивая большую гибкость в разработке приложений."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 сочетает последние технологии оптимизации, обеспечивая более эффективную обработку многомодальных данных."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro поддерживает до 2 миллионов токенов и является идеальным выбором для средних многомодальных моделей, обеспечивая многостороннюю поддержку для сложных задач."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash предлагает функции следующего поколения и улучшения, включая выдающуюся скорость, использование встроенных инструментов, многомодальную генерацию и контекстное окно на 1M токенов."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash предлагает функции следующего поколения и улучшения, включая выдающуюся скорость, использование встроенных инструментов, многомодальную генерацию и контекстное окно на 1M токенов."}, "gemini-2.0-flash-exp": {"description": "Модельный вариант Gemini 2.0 Flash, оптимизированный для достижения таких целей, как экономическая эффективность и низкая задержка."}, "gemini-2.0-flash-exp-image-generation": {"description": "Экспериментальная модель Gemini 2.0 Flash, поддерживающая генерацию изображений"}, "gemini-2.0-flash-lite": {"description": "Модельный вариант Gemini 2.0 Flash, оптимизированный для достижения таких целей, как экономическая эффективность и низкая задержка."}, "gemini-2.0-flash-lite-001": {"description": "Модельный вариант Gemini 2.0 Flash, оптимизированный для достижения таких целей, как экономическая эффективность и низкая задержка."}, "gemini-2.0-flash-preview-image-generation": {"description": "Модель предварительного просмотра Gemini 2.0 Flash, поддерживающая генерацию изображений"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash — самая экономичная модель Google, предоставляющая полный набор функций."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite — это самая компактная и экономичная модель от Google, разработанная для масштабного использования."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview — самая компактная и экономичная модель Google, разработанная для масштабного использования."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview — это наиболее выгодная модель от Google, предлагающая широкий спектр возможностей."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview — самая экономичная модель Google с полным набором функций."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro — самая передовая модель мышления Google, способная рассуждать над сложными задачами в области кода, математики и STEM, а также анализировать большие наборы данных, кодовые базы и документы с использованием длинного контекста."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview — это самая современная модель мышления от Google, способная рассуждать о сложных задачах в области кода, математики и STEM, а также анализировать большие наборы данных, кодовые базы и документы с использованием длинного контекста."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview — это самая современная модель мышления от Google, способная рассуждать о сложных задачах в области кода, математики и STEM, а также анализировать большие наборы данных, кодовые базы и документы с помощью длинного контекста."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview — передовая модель мышления от Google, способная рассуждать над сложными задачами в области кода, математики и STEM, а также анализировать большие наборы данных, кодовые базы и документы с использованием длинного контекста."}, "gemma-7b-it": {"description": "Gemma 7B подходит для обработки задач среднего и малого масштаба, обеспечивая экономическую эффективность."}, "gemma2": {"description": "Gemma 2 — это высокоэффективная модель, выпущенная Google, охватывающая широкий спектр приложений от малых до сложных задач обработки данных."}, "gemma2-9b-it": {"description": "Gemma 2 9B — это модель, оптимизированная для конкретных задач и интеграции инструментов."}, "gemma2:27b": {"description": "Gemma 2 — это высокоэффективная модель, выпущенная Google, охватывающая широкий спектр приложений от малых до сложных задач обработки данных."}, "gemma2:2b": {"description": "Gemma 2 — это высокоэффективная модель, выпущенная Google, охватывающая широкий спектр приложений от малых до сложных задач обработки данных."}, "generalv3": {"description": "Spark Pro — это высокопроизводительная большая языковая модель, оптимизированная для профессиональных областей, таких как математика, программирование, медицина и образование, поддерживающая сетевой поиск и встроенные плагины для погоды, даты и т.д. Оптимизированная модель демонстрирует выдающиеся результаты и высокую эффективность в сложных задачах на знание, понимании языка и высокоуровневом создании текстов, что делает ее идеальным выбором для профессиональных приложений."}, "generalv3.5": {"description": "Spark3.5 Max — это самая полная версия, поддерживающая сетевой поиск и множество встроенных плагинов. Его полностью оптимизированные основные возможности, а также функции настройки системных ролей и вызовов функций делают его выдающимся и эффективным в различных сложных приложениях."}, "glm-4": {"description": "GLM-4 — это старая флагманская версия, выпущенная в январе 2024 года, которая была заменена более мощной GLM-4-0520."}, "glm-4-0520": {"description": "GLM-4-0520 — это последняя версия модели, специально разработанная для высоко сложных и разнообразных задач, демонстрирующая выдающиеся результаты."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat демонстрирует высокую производительность в семантике, математике, логическом мышлении, кодировании и знаниях. Также поддерживает веб-браузинг, выполнение кода, вызовы пользовательских инструментов и длинное текстовое рассуждение. Поддерживает 26 языков, включая японский, корейский и немецкий."}, "glm-4-air": {"description": "GLM-4-Air — это экономически эффективная версия, производительность которой близка к GLM-4, обеспечивая высокую скорость и доступную цену."}, "glm-4-air-250414": {"description": "GLM-4-Air — это версия с хорошим соотношением цены и качества, производительность близка к GLM-4, обеспечивая быструю скорость и доступную цену."}, "glm-4-airx": {"description": "GLM-4-AirX предлагает эффективную версию GLM-4-Air, скорость вывода может достигать 2.6 раз быстрее."}, "glm-4-alltools": {"description": "GLM-4-AllTools — это многофункциональная модель агента, оптимизированная для поддержки сложного планирования инструкций и вызовов инструментов, таких как веб-серфинг, интерпретация кода и генерация текста, подходящая для выполнения множества задач."}, "glm-4-flash": {"description": "GLM-4-Flash — это идеальный выбор для обработки простых задач, с самой высокой скоростью и самой низкой ценой."}, "glm-4-flash-250414": {"description": "GLM-4-Flash — идеальный выбор для обработки простых задач, обладает самой высокой скоростью и бесплатен."}, "glm-4-flashx": {"description": "GLM-4-FlashX — это улучшенная версия Flash с ультрабыстрой скоростью вывода."}, "glm-4-long": {"description": "GLM-4-Long поддерживает сверхдлинные текстовые вводы, подходит для задач, требующих памяти, и обработки больших документов."}, "glm-4-plus": {"description": "GLM-4-Plus, как флагман с высоким интеллектом, обладает мощными способностями обработки длинных текстов и сложных задач, с полным улучшением производительности."}, "glm-4.1v-thinking-flash": {"description": "Серия моделей GLM-4.1V-Thinking является самой производительной визуальной моделью уровня 10B VLM на сегодняшний день, объединяя передовые SOTA возможности в задачах визуально-языкового понимания, включая понимание видео, вопросы по изображениям, решение предметных задач, распознавание текста OCR, интерпретацию документов и графиков, GUI-агентов, фронтенд веб-кодинг, Grounding и другие. Во многих задачах её возможности превосходят Qwen2.5-VL-72B с параметрами в 8 раз больше. Благодаря передовым методам обучения с подкреплением модель овладела рассуждениями через цепочку мышления, что значительно повышает точность и полноту ответов, превосходя традиционные модели без thinking с точки зрения конечных результатов и интерпретируемости."}, "glm-4.1v-thinking-flashx": {"description": "Серия моделей GLM-4.1V-Thinking является самой производительной визуальной моделью уровня 10B VLM на сегодняшний день, объединяя передовые SOTA возможности в задачах визуально-языкового понимания, включая понимание видео, вопросы по изображениям, решение предметных задач, распознавание текста OCR, интерпретацию документов и графиков, GUI-агентов, фронтенд веб-кодинг, Grounding и другие. Во многих задачах её возможности превосходят Qwen2.5-VL-72B с параметрами в 8 раз больше. Благодаря передовым методам обучения с подкреплением модель овладела рассуждениями через цепочку мышления, что значительно повышает точность и полноту ответов, превосходя традиционные модели без thinking с точки зрения конечных результатов и интерпретируемости."}, "glm-4.5": {"description": "Последняя флагманская модель Zhizhu, поддерживающая режимы размышления, достигающая уровня SOTA среди открытых моделей по совокупным способностям, с длиной контекста до 128K токенов."}, "glm-4.5-air": {"description": "Лёгкая версия GLM-4.5, сочетающая производительность и экономичность, с возможностью гибкого переключения между смешанными режимами размышления."}, "glm-4.5-airx": {"description": "Ускоренная версия GLM-4.5-Air с более быстрой реакцией, созданная для масштабных задач с высокими требованиями к скорости."}, "glm-4.5-flash": {"description": "Бесплатная версия GLM-4.5, демонстрирующая отличные результаты в задачах вывода, программирования и работы с агентами."}, "glm-4.5-x": {"description": "Ускоренная версия GLM-4.5 с высокой производительностью и скоростью генерации до 100 токенов в секунду."}, "glm-4v": {"description": "GLM-4V предлагает мощные способности понимания и вывода изображений, поддерживает множество визуальных задач."}, "glm-4v-flash": {"description": "GLM-4V-Flash сосредоточен на эффективном понимании одного изображения, подходит для сценариев быстрого анализа изображений, таких как анализ изображений в реальном времени или пакетная обработка изображений."}, "glm-4v-plus": {"description": "GLM-4V-Plus обладает способностью понимать видео-контент и множество изображений, подходит для мультимодальных задач."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus обладает способностями к пониманию видео и множества изображений, подходит для мультимодальных задач."}, "glm-z1-air": {"description": "Модель вывода: обладает мощными способностями вывода, подходит для задач, требующих глубокого вывода."}, "glm-z1-airx": {"description": "Супербыстрый вывод: обладает сверхбыстрой скоростью вывода и мощными результатами вывода."}, "glm-z1-flash": {"description": "Серия GLM-Z1 обладает мощными возможностями сложного рассуждения, демонстрируя выдающиеся результаты в логике, математике и программировании."}, "glm-z1-flashx": {"description": "Высокая скорость и низкая цена: улучшенная версия Flash с сверхбыстрой скоростью вывода и повышенной поддержкой параллельных запросов."}, "glm-zero-preview": {"description": "GLM-Zero-Preview обладает мощными способностями к сложному выводу, демонстрируя отличные результаты в области логического вывода, математики и программирования."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash предлагает функции следующего поколения и улучшения, включая выдающуюся скорость, использование встроенных инструментов, многомодальную генерацию и контекстное окно на 1M токенов."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental — это последняя экспериментальная мультимодальная AI модель от Google, которая демонстрирует определенное улучшение качества по сравнению с историческими версиями, особенно в области мировых знаний, кода и длинного контекста."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash — это передовая основная модель Google, специально разработанная для сложных задач рассуждения, кодирования, математики и науки. Она включает встроенную функцию «мышления», которая позволяет предоставлять ответы с более высокой точностью и тщательной обработкой контекста.\n\nВнимание: у этой модели есть два варианта: с мышлением и без. Ценообразование вывода значительно отличается в зависимости от активации функции мышления. Если вы выбираете стандартный вариант (без суффикса \":thinking\"), модель явно избегает генерации токенов мышления.\n\nЧтобы использовать функцию мышления и получать токены мышления, необходимо выбрать вариант с суффиксом \":thinking\", что приведет к более высокой стоимости вывода с мышлением.\n\nКроме того, Gemini 2.5 Flash можно настроить с помощью параметра «максимальное количество токенов для рассуждения», как описано в документации (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash — это самая современная основная модель от Google, разработанная для сложного рассуждения, кодирования, математических и научных задач. Она включает встроенную способность \"думать\", что позволяет ей давать ответы с более высокой точностью и детализированной обработкой контекста.\n\nОбратите внимание: эта модель имеет два варианта: с \"думанием\" и без. Цены на вывод значительно различаются в зависимости от того, активирована ли способность думать. Если вы выберете стандартный вариант (без суффикса \":thinking\"), модель явно избегает генерации токенов для размышлений.\n\nЧтобы воспользоваться способностью думать и получать токены для размышлений, вы должны выбрать вариант \":thinking\", что приведет к более высокой цене на вывод размышлений.\n\nКроме того, Gemini 2.5 Flash можно настроить с помощью параметра \"максимальное количество токенов для рассуждения\", как указано в документации (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash — это самая современная основная модель от Google, разработанная для сложного рассуждения, кодирования, математических и научных задач. Она включает встроенную способность \"думать\", что позволяет ей давать ответы с более высокой точностью и детализированной обработкой контекста.\n\nОбратите внимание: эта модель имеет два варианта: с \"думанием\" и без. Цены на вывод значительно различаются в зависимости от того, активирована ли способность думать. Если вы выберете стандартный вариант (без суффикса \":thinking\"), модель явно избегает генерации токенов для размышлений.\n\nЧтобы воспользоваться способностью думать и получать токены для размышлений, вы должны выбрать вариант \":thinking\", что приведет к более высокой цене на вывод размышлений.\n\nКроме того, Gemini 2.5 Flash можно настроить с помощью параметра \"максимальное количество токенов для рассуждения\", как указано в документации (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro — это передовая модель мышления Google, способная рассуждать над сложными задачами в области кода, математики и STEM, а также анализировать большие наборы данных, кодовые базы и документы с использованием длинного контекста."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview — это самая передовая модель мышления от Google, способная рассуждать над сложными задачами в области кода, математики и STEM, а также анализировать большие наборы данных, кодовые базы и документы с использованием длинного контекста."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash предлагает оптимизированные возможности многомодальной обработки, подходящие для различных сложных задач."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro сочетает в себе новейшие технологии оптимизации, обеспечивая более эффективную обработку многомодальных данных."}, "google/gemma-2-27b": {"description": "Gemma 2 — это эффективная модель, представленная Google, охватывающая широкий спектр приложений от небольших до сложных задач обработки данных."}, "google/gemma-2-27b-it": {"description": "Gemma 2 продолжает концепцию легковесного и эффективного дизайна."}, "google/gemma-2-2b-it": {"description": "Легковесная модель настройки инструкций от Google."}, "google/gemma-2-9b": {"description": "Gemma 2 — это эффективная модель, представленная Google, охватывающая широкий спектр приложений от небольших до сложных задач обработки данных."}, "google/gemma-2-9b-it": {"description": "Gemma 2 — это легковесная серия текстовых моделей с открытым исходным кодом от Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 — это облегченная открытая текстовая модель от Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) предлагает базовые возможности обработки команд, подходящие для легковесных приложений."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B — это открытая языковая модель от Google, установившая новые стандарты в эффективности и производительности."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B — это открытая языковая модель от Google, которая установила новые стандарты в области эффективности и производительности."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo подходит для различных задач генерации и понимания текста, в настоящее время ссылается на gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo подходит для различных задач генерации и понимания текста, в настоящее время ссылается на gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo подходит для различных задач генерации и понимания текста, в настоящее время ссылается на gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo подходит для различных задач генерации и понимания текста, в настоящее время ссылается на gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo — это эффективная модель от OpenAI, предназначенная для задач чата и генерации текста, поддерживающая параллельные вызовы функций."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k — модель для генерации текста с высокой ёмкостью, подходящая для сложных задач."}, "gpt-4": {"description": "GPT-4 предлагает более широкий контекстный диапазон, способный обрабатывать более длинные текстовые вводы, подходя для сценариев, требующих обширной интеграции информации и анализа данных."}, "gpt-4-0125-preview": {"description": "Последняя модель GPT-4 Turbo обладает визуальными функциями. Теперь визуальные запросы могут использовать JSON-формат и вызовы функций. GPT-4 Turbo — это улучшенная версия, обеспечивающая экономически эффективную поддержку для мультимодальных задач. Она находит баланс между точностью и эффективностью, подходя для приложений, требующих взаимодействия в реальном времени."}, "gpt-4-0613": {"description": "GPT-4 предлагает более широкий контекстный диапазон, способный обрабатывать более длинные текстовые вводы, подходя для сценариев, требующих обширной интеграции информации и анализа данных."}, "gpt-4-1106-preview": {"description": "Последняя модель GPT-4 Turbo обладает визуальными функциями. Теперь визуальные запросы могут использовать JSON-формат и вызовы функций. GPT-4 Turbo — это улучшенная версия, обеспечивающая экономически эффективную поддержку для мультимодальных задач. Она находит баланс между точностью и эффективностью, подходя для приложений, требующих взаимодействия в реальном времени."}, "gpt-4-32k": {"description": "GPT-4 предлагает более широкий контекстный диапазон, способный обрабатывать более длинные текстовые вводы, подходя для сценариев, требующих обширной интеграции информации и анализа данных."}, "gpt-4-32k-0613": {"description": "GPT-4 предлагает более широкий контекстный диапазон, способный обрабатывать более длинные текстовые вводы, подходя для сценариев, требующих обширной интеграции информации и анализа данных."}, "gpt-4-turbo": {"description": "Последняя модель GPT-4 Turbo обладает визуальными функциями. Теперь визуальные запросы могут использовать JSON-формат и вызовы функций. GPT-4 Turbo — это улучшенная версия, обеспечивающая экономически эффективную поддержку для мультимодальных задач. Она находит баланс между точностью и эффективностью, подходя для приложений, требующих взаимодействия в реальном времени."}, "gpt-4-turbo-2024-04-09": {"description": "Последняя модель GPT-4 Turbo обладает визуальными функциями. Теперь визуальные запросы могут использовать JSON-формат и вызовы функций. GPT-4 Turbo — это улучшенная версия, обеспечивающая экономически эффективную поддержку для мультимодальных задач. Она находит баланс между точностью и эффективностью, подходя для приложений, требующих взаимодействия в реальном времени."}, "gpt-4-turbo-preview": {"description": "Последняя модель GPT-4 Turbo обладает визуальными функциями. Теперь визуальные запросы могут использовать JSON-формат и вызовы функций. GPT-4 Turbo — это улучшенная версия, обеспечивающая экономически эффективную поддержку для мультимодальных задач. Она находит баланс между точностью и эффективностью, подходя для приложений, требующих взаимодействия в реальном времени."}, "gpt-4-vision-preview": {"description": "Последняя модель GPT-4 Turbo обладает визуальными функциями. Теперь визуальные запросы могут использовать JSON-формат и вызовы функций. GPT-4 Turbo — это улучшенная версия, обеспечивающая экономически эффективную поддержку для мультимодальных задач. Она находит баланс между точностью и эффективностью, подходя для приложений, требующих взаимодействия в реальном времени."}, "gpt-4.1": {"description": "GPT-4.1 — это наша флагманская модель для сложных задач. Она отлично подходит для решения междисциплинарных проблем."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini предлагает баланс между интеллектом, скоростью и стоимостью, что делает его привлекательной моделью для многих случаев использования."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini предлагает баланс между интеллектом, скоростью и стоимостью, что делает его привлекательной моделью для многих случаев использования."}, "gpt-4.5-preview": {"description": "Предварительная версия исследования GPT-4.5, это наша самая большая и мощная модель GPT на сегодняшний день. Она обладает обширными знаниями о мире и лучше понимает намерения пользователей, что делает её выдающейся в творческих задачах и автономном планировании. GPT-4.5 принимает текстовые и графические входные данные и генерирует текстовый вывод (включая структурированный вывод). Поддерживает ключевые функции для разработчиков, такие как вызовы функций, пакетный API и потоковый вывод. В задачах, требующих креативного, открытого мышления и диалога (таких как написание, обучение или исследование новых идей), GPT-4.5 особенно эффективен. Дата окончания знаний - октябрь 2023 года."}, "gpt-4o": {"description": "ChatGPT-4o — это динамическая модель, которая обновляется в реальном времени, чтобы оставаться актуальной. Она сочетает в себе мощное понимание языка и генерацию, подходя для масштабных приложений, включая обслуживание клиентов, образование и техническую поддержку."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o — это динамическая модель, которая обновляется в реальном времени, чтобы оставаться актуальной. Она сочетает в себе мощное понимание языка и генерацию, подходя для масштабных приложений, включая обслуживание клиентов, образование и техническую поддержку."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o — это динамическая модель, которая обновляется в реальном времени, чтобы оставаться актуальной. Она сочетает в себе мощное понимание языка и генерацию, подходя для масштабных приложений, включая обслуживание клиентов, образование и техническую поддержку."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o — это динамическая модель, которая обновляется в реальном времени для поддержания актуальной версии. Она сочетает в себе мощное понимание языка и генерацию текста, подходя для широкого спектра приложений, включая обслуживание клиентов, образование и техническую поддержку."}, "gpt-4o-audio-preview": {"description": "Модель GPT-4o Audio, поддерживающая аудиовход и аудиовыход."}, "gpt-4o-mini": {"description": "GPT-4o mini — это последняя модель, выпущенная OpenAI после GPT-4 Omni, поддерживающая ввод изображений и текстов с выводом текста. Как их самый продвинутый компактный модель, она значительно дешевле других недавних передовых моделей и более чем на 60% дешевле GPT-3.5 Turbo. Она сохраняет передовой уровень интеллекта при значительном соотношении цена-качество. GPT-4o mini набрала 82% на тесте MMLU и в настоящее время занимает более высокое место в предпочтениях чата по сравнению с GPT-4."}, "gpt-4o-mini-audio-preview": {"description": "Модель GPT-4o mini Audio поддерживает ввод и вывод аудио."}, "gpt-4o-mini-realtime-preview": {"description": "Реальная версия GPT-4o-mini, поддерживающая аудио и текстовый ввод и вывод в реальном времени."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini — предварительная версия модели для поиска, специально обученная для понимания и выполнения запросов веб-поиска, использующая Chat Completions API. Помимо платы за токены, за каждый вызов инструмента веб-поиска взимается отдельная плата."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe — модель преобразования речи в текст, использующая GPT-4o для транскрибирования аудио. По сравнению с оригинальной моделью Whisper, она снижает количество ошибок в словах и повышает точность распознавания языка. Используйте её для более точной транскрипции."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS — это модель преобразования текста в речь, основанная на GPT-4o mini, обеспечивающая высокое качество синтеза речи при низкой стоимости."}, "gpt-4o-realtime-preview": {"description": "Реальная версия GPT-4o, поддерживающая аудио и текстовый ввод и вывод в реальном времени."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Реальная версия GPT-4o, поддерживающая аудио и текстовый ввод и вывод в реальном времени."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Реальное время GPT-4o, поддерживает одновременный ввод и вывод аудио и текста."}, "gpt-4o-search-preview": {"description": "GPT-4o — предварительная версия модели для поиска, специально обученная для понимания и выполнения запросов веб-поиска, использующая Chat Completions API. Помимо платы за токены, за каждый вызов инструмента веб-поиска взимается отдельная плата."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe — модель преобразования речи в текст, использующая GPT-4o для транскрибирования аудио. По сравнению с оригинальной моделью Whisper, она снижает количество ошибок в словах и повышает точность распознавания языка. Используйте её для более точной транскрипции."}, "gpt-image-1": {"description": "Родная мультимодальная модель генерации изображений ChatGPT."}, "grok-2-1212": {"description": "Модель улучшена в точности, соблюдении инструкций и многоязычных возможностях."}, "grok-2-image-1212": {"description": "Наша новейшая модель генерации изображений способна создавать живые и реалистичные изображения на основе текстовых подсказок. Она отлично подходит для маркетинга, социальных сетей и развлекательных приложений."}, "grok-2-vision-1212": {"description": "Модель улучшена в точности, соблюдении инструкций и многоязычных возможностях."}, "grok-3": {"description": "Флагманская модель, специализирующаяся на извлечении данных, программировании и резюмировании текста для корпоративных приложений, обладающая глубокими знаниями в финансах, медицине, юриспруденции и науке."}, "grok-3-fast": {"description": "Флагманская модель, специализирующаяся на извлечении данных, программировании и резюмировании текста для корпоративных приложений, обладающая глубокими знаниями в финансах, медицине, юриспруденции и науке."}, "grok-3-mini": {"description": "Легковесная модель, которая сначала обдумывает ответ перед разговором. Быстрая и умная, подходит для логических задач без глубоких отраслевых знаний и позволяет проследить исходные размышления."}, "grok-3-mini-fast": {"description": "Легковесная модель, которая сначала обдумывает ответ перед разговором. Быстрая и умная, подходит для логических задач без глубоких отраслевых знаний и позволяет проследить исходные размышления."}, "grok-4": {"description": "Наша новейшая и самая мощная флагманская модель, демонстрирующая выдающиеся результаты в обработке естественного языка, математических вычислениях и логическом рассуждении — идеальный универсальный инструмент."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B — это языковая модель, объединяющая креативность и интеллект, основанная на нескольких ведущих моделях."}, "hunyuan-a13b": {"description": "Hunyuan — первая гибридная модель рассуждения, обновлённая версия hunyuan-standard-256K с общим числом параметров 80B и 13B активных параметров. По умолчанию работает в режиме медленного мышления, поддерживает переключение между режимами быстрого и медленного мышления через параметры или команды, переключение осуществляется добавлением / no_think перед запросом. Общие возможности значительно улучшены по сравнению с предыдущим поколением, особенно в математике, науке, понимании длинных текстов и агентских функциях."}, "hunyuan-code": {"description": "Последняя модель генерации кода Hunyuan, обученная на базе 200B высококачественных данных кода, прошедшая полгода обучения на высококачественных данных SFT, с увеличенной длиной контекстного окна до 8K, занимает ведущие позиции по автоматическим оценочным показателям генерации кода на пяти языках; по десяти критериям оценки кода на пяти языках, производительность находится в первой группе."}, "hunyuan-functioncall": {"description": "Последняя модель Hunyuan с архитектурой MOE FunctionCall, обученная на высококачественных данных FunctionCall, с контекстным окном до 32K, занимает лидирующие позиции по множеству оценочных показателей."}, "hunyuan-large": {"description": "Модель Hunyuan-large имеет общее количество параметров около 389B, активных параметров около 52B, что делает её самой крупной и эффективной открытой моделью MoE с архитектурой Transformer в отрасли."}, "hunyuan-large-longcontext": {"description": "Специализируется на обработке длинных текстовых задач, таких как резюме документов и вопросы и ответы по документам, а также обладает способностью обрабатывать общие задачи генерации текста. Отлично справляется с анализом и генерацией длинных текстов, эффективно справляясь с требованиями к обработке сложного и детального длинного контента."}, "hunyuan-large-vision": {"description": "Эта модель предназначена для задач понимания изображений и текста, основана на смешанной модели Hunyuan Large, поддерживает ввод нескольких изображений с произвольным разрешением и текстом, генерирует текстовый контент, сосредоточена на задачах понимания изображений и текста, с заметным улучшением многоязычных возможностей."}, "hunyuan-lite": {"description": "Обновленная версия с MOE-структурой, контекстное окно составляет 256k, она опережает множество открытых моделей в оценках по NLP, коду, математике и другим областям."}, "hunyuan-lite-vision": {"description": "Последняя многомодальная модель Hunyuan с 7B параметрами, окно контекста 32K, поддерживает многомодальный диалог на китайском и английском языках, распознавание объектов на изображениях, понимание документов и таблиц, многомодальную математику и т. д., по многим измерениям превосходит модели конкурентов с 7B параметрами."}, "hunyuan-pro": {"description": "Модель длинного текста с параметрами уровня триллиона MOE-32K. Она достигает абсолютного лидерства на различных бенчмарках, обладает сложными инструкциями и выводом, имеет сложные математические способности и поддерживает вызовы функций, с акцентом на оптимизацию в области многоязычного перевода, финансов, права и медицины."}, "hunyuan-role": {"description": "Последняя версия модели ролевого взаимодействия Hunyuan, выпущенная с официальной тонкой настройкой, основанная на модели Hunyuan и дополненная данными сценариев ролевого взаимодействия, демонстрирует лучшие базовые результаты в ролевых сценариях."}, "hunyuan-standard": {"description": "Использует более оптимальную стратегию маршрутизации, одновременно смягчая проблемы с балансировкой нагрузки и сходимостью экспертов. В области длинных текстов показатель «найти иголку в стоге сена» достигает 99,9%. MOE-32K предлагает более высокую стоимость-эффективность, обеспечивая баланс между качеством и ценой, а также возможность обработки длинных текстовых вводов."}, "hunyuan-standard-256K": {"description": "Использует более оптимальную стратегию маршрутизации, одновременно смягчая проблемы с балансировкой нагрузки и сходимостью экспертов. В области длинных текстов показатель «найти иголку в стоге сена» достигает 99,9%. MOE-256K делает дальнейший прорыв в длине и качестве, значительно расширяя допустимую длину ввода."}, "hunyuan-standard-vision": {"description": "Последняя многомодальная модель Hunyuan, поддерживающая многоязычные ответы, с сбалансированными способностями на китайском и английском языках."}, "hunyuan-t1-20250321": {"description": "Полноценная модель, обладающая как гуманитарными, так и естественнонаучными способностями, с высокой способностью к захвату длинной текстовой информации. Поддерживает рассуждения и ответы на научные вопросы различной сложности, включая математику, логические задачи, науки и код."}, "hunyuan-t1-20250403": {"description": "Повышение возможностей генерации кода на уровне проекта; улучшение качества текстового творчества; улучшение многоходового понимания тем, соблюдения инструкций toB и понимания слов; оптимизация проблем с выводом смешанных упрощённых и традиционных иероглифов, а также смешанных китайско-английских текстов."}, "hunyuan-t1-20250529": {"description": "Оптимизация создания текстов и написания сочинений, улучшение навыков программирования, математики и логического мышления, повышение способности следовать инструкциям."}, "hunyuan-t1-20250711": {"description": "Значительное улучшение способностей в сложной математике, логике и программировании, оптимизация стабильности вывода модели и повышение возможностей работы с длинными текстами."}, "hunyuan-t1-latest": {"description": "Первый в отрасли сверхмасштабный гибридный трансформер-Mamba для вывода, расширяющий возможности вывода, обладающий высокой скоростью декодирования и лучше соответствующий человеческим предпочтениям."}, "hunyuan-t1-vision": {"description": "Глубокая мультимодальная модель понимания Hunyuan с нативной цепочкой размышлений для мультимодальных данных, отлично справляется с различными задачами рассуждения на изображениях, значительно превосходя модели быстрого мышления в решении научных задач."}, "hunyuan-t1-vision-20250619": {"description": "Последняя версия модели hunyuan t1-vision для мультимодального понимания с глубокой цепочкой мышления, поддерживающая нативные мультимодальные цепочки рассуждений, с существенным улучшением по сравнению с предыдущей версией по умолчанию."}, "hunyuan-turbo": {"description": "Предварительная версия нового поколения языковой модели Hunyuan, использующая совершенно новую структуру смешанной экспертной модели (MoE), которая обеспечивает более быструю эффективность вывода и более сильные результаты по сравнению с hunyuan-pro."}, "hunyuan-turbo-20241223": {"description": "Оптимизация этой версии: масштабирование данных и инструкций, значительное повышение общей обобщающей способности модели; значительное улучшение математических, кодовых и логических способностей; оптимизация понимания текста и связанных с ним способностей понимания слов; оптимизация качества генерации контента при создании текста."}, "hunyuan-turbo-latest": {"description": "Оптимизация общего опыта, включая понимание NLP, создание текста, общение, вопросы и ответы на знания, перевод, области и т. д.; повышение человечности, оптимизация эмоционального интеллекта модели; улучшение способности модели активно прояснять неясные намерения; повышение способности обработки вопросов, связанных с анализом слов; улучшение качества и интерактивности творчества; улучшение многократного взаимодействия."}, "hunyuan-turbo-vision": {"description": "Флагманская модель нового поколения Hunyuan в области визуального языка, использующая совершенно новую структуру смешанной экспертной модели (MoE), с полным улучшением способностей в области базового распознавания, создания контента, вопросов и ответов на знания, анализа и вывода по сравнению с предыдущей моделью."}, "hunyuan-turbos-20250313": {"description": "Унификация стиля решения математических задач, усиление многоходовых математических вопросов и ответов. Оптимизация стиля ответов в текстовом творчестве, устранение искусственного оттенка, добавление выразительности."}, "hunyuan-turbos-20250416": {"description": "Обновление предобученной базы, усиление способности базы к пониманию и соблюдению инструкций; улучшение математических, программных, логических и научных навыков на этапе согласования; повышение качества творческого письма, понимания текста, точности перевода и знаний в гуманитарных областях; усиление возможностей агентов в различных сферах, с особым акцентом на понимание многоходовых диалогов."}, "hunyuan-turbos-20250604": {"description": "Обновленная база предобучения, улучшенные навыки письма и понимания прочитанного, значительное повышение возможностей в программировании и точных науках, постоянное улучшение следования сложным инструкциям."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS — это последняя версия флагманской модели Hu<PERSON>, обладающая более сильными аналитическими способностями и улучшенным качеством работы."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Специализируется на обработке длинных текстов, таких как резюме документов и вопросы по документам, а также обладает способностью обрабатывать общие задачи генерации текста. Она демонстрирует выдающиеся результаты в анализе и генерации длинных текстов, эффективно справляясь с требованиями к сложной и детализированной обработке длинных текстов."}, "hunyuan-turbos-role-plus": {"description": "Последняя версия модели ролевых игр Hunyuan, официально дообученная модель, основанная на Hunyuan и дополненная данными для ролевых сценариев, обеспечивающая лучшие базовые результаты в ролевых играх."}, "hunyuan-turbos-vision": {"description": "Эта модель предназначена для задач понимания изображений и текста, основана на последней версии hunyuan turbos и является новым флагманским визуально-языковым большим моделью, сосредоточенной на задачах распознавания объектов на изображениях, ответах на вопросы, создании текстов и решении задач по фотографиям, с существенным улучшением по сравнению с предыдущим поколением."}, "hunyuan-turbos-vision-20250619": {"description": "Последняя версия флагманской визуально-языковой модели hunyuan turbos-vision, значительно улучшенная по сравнению с предыдущей версией по умолчанию в задачах понимания изображений и текста, включая распознавание объектов на изображениях, ответы на вопросы, создание текстов и решение задач по фотографиям."}, "hunyuan-vision": {"description": "Последняя многомодальная модель Hunyuan, поддерживающая ввод изображений и текста для генерации текстового контента."}, "image-01": {"description": "Новая модель генерации изображений с детальной прорисовкой, поддерживающая генерацию из текста и преобразование изображений."}, "image-01-live": {"description": "Модель генерации изображений с детальной прорисовкой, поддерживающая генерацию из текста и настройку стиля изображения."}, "imagen-4.0-generate-preview-06-06": {"description": "Серия моделей Imagen 4-го поколения для преобразования текста в изображение"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Ультра-версия серии моделей Imagen 4-го поколения для преобразования текста в изображение"}, "imagen4/preview": {"description": "Модель генерации изображений высочайшего качества от Google."}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 предлагает интеллектуальные решения для диалогов в различных сценариях."}, "internlm2.5-latest": {"description": "Наша последняя серия моделей с выдающимися показателями вывода, поддерживающая длину контекста до 1M и обладающая улучшенными возможностями следования инструкциям и вызова инструментов."}, "internlm3-latest": {"description": "Наша последняя серия моделей с выдающейся производительностью вывода, лидирующая среди моделей открытого кода того же уровня. По умолчанию указывает на нашу последнюю выпущенную серию моделей InternLM3."}, "internvl2.5-latest": {"description": "Мы продолжаем поддерживать версию InternVL2.5, обладающую отличной и стабильной производительностью. По умолчанию указывает на нашу последнюю выпущенную серию моделей InternVL2.5, в настоящее время указывает на internvl2.5-78b."}, "internvl3-latest": {"description": "Мы выпустили нашу последнюю многомодальную большую модель, обладающую более сильными способностями к пониманию текстов и изображений, а также к пониманию длинных последовательностей изображений, производительность которой сопоставима с ведущими закрытыми моделями. По умолчанию указывает на нашу последнюю выпущенную серию моделей InternVL, в настоящее время указывает на internvl3-78b."}, "irag-1.0": {"description": "Собственная технология Baidu iRAG (image based RAG) — это метод генерации изображений с усилением поиска, который объединяет миллиардные ресурсы изображений Baidu Search с мощными базовыми моделями, позволяя создавать сверхреалистичные изображения, значительно превосходящие традиционные системы генерации изображений. Модель отличается отсутствием артефактов AI, высокой реалистичностью и мгновенной доступностью при низких затратах."}, "jamba-large": {"description": "Наша самая мощная и передовая модель, разработанная для решения сложных задач корпоративного уровня, обладающая выдающейся производительностью."}, "jamba-mini": {"description": "Самая эффективная модель в своем классе, сочетающая скорость и качество, с меньшими размерами."}, "jina-deepsearch-v1": {"description": "Глубокий поиск сочетает в себе сетевой поиск, чтение и рассуждение, позволяя проводить всесторонние исследования. Вы можете рассматривать его как агента, который принимает ваши исследовательские задачи — он проводит обширный поиск и проходит через множество итераций, прежде чем предоставить ответ. Этот процесс включает в себя постоянные исследования, рассуждения и решение проблем с разных точек зрения. Это принципиально отличается от стандартных больших моделей, которые генерируют ответы непосредственно из предобученных данных, и от традиционных систем RAG, полагающихся на одноразовый поверхностный поиск."}, "kimi-k2": {"description": "Kimi-K2 — базовая модель на архитектуре MoE с выдающимися возможностями в кодировании и агентских задачах, выпущенная Moonshot AI, с общим числом параметров 1 триллион и 32 миллиардами активируемых параметров. В тестах на универсальное знание, программирование, математику и агентские задачи производительность модели K2 превосходит другие ведущие открытые модели."}, "kimi-k2-0711-preview": {"description": "kimi-k2 — базовая модель с архитектурой MoE, обладающая мощными возможностями кода и агента, с общим числом параметров 1 триллион и 32 миллиарда активных параметров. В тестах производительности по основным категориям, таким как универсальное знание, программирование, математика и агенты, модель K2 превосходит другие ведущие открытые модели."}, "kimi-latest": {"description": "Продукт Kim<PERSON> Smart Assistant использует последнюю модель Kimi, которая может содержать нестабильные функции. Поддерживает понимание изображений и автоматически выбирает модель 8k/32k/128k в качестве модели для выставления счетов в зависимости от длины контекста запроса."}, "kimi-thinking-preview": {"description": "Модель kimi-thinking-preview от Moon’s Dark Side — мультимодальная модель мышления с возможностями универсального и глубокого рассуждения, помогает решать более сложные задачи."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM — это экспериментальная языковая модель, ориентированная на конкретные задачи, обученная в соответствии с принципами науки о обучении, которая может следовать системным инструкциям в учебных и образовательных сценариях, выступая в роли эксперта-наставника и т.д."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM — это экспериментальная языковая модель, ориентированная на конкретные задачи, обученная в соответствии с принципами науки о обучении, способная следовать системным инструкциям в учебных и образовательных сценариях, выступая в роли эксперта-наставника и т.д."}, "lite": {"description": "Spark Lite — это легковесная большая языковая модель с крайне низкой задержкой и высокой эффективностью обработки, полностью бесплатная и открытая, поддерживающая функции онлайн-поиска в реальном времени. Ее быстрая реакция делает ее отличным выбором для применения в устройствах с низкой вычислительной мощностью и для тонкой настройки моделей, обеспечивая пользователям отличное соотношение цены и качества, особенно в сценариях вопросов и ответов, генерации контента и поиска."}, "llama-2-7b-chat": {"description": "Llama2 — это серия больших языковых моделей (LLM), разработанных и открытых для использования компанией Meta. Это набор предобученных и дообученных генеративных текстовых моделей, размер которых варьируется от 7 до 70 миллиардов параметров. С архитектурной точки зрения, Llama2 представляет собой автогрессивную языковую модель, использующую оптимизированную трансформерную архитектуру. Настроенные версии используют надзорное дообучение (SFT) и обучение с подкреплением на основе обратной связи от человека (RLHF) для согласования с предпочтениями человека в отношении полезности и безопасности. Llama2 показывает лучшие результаты на различных академических наборах данных по сравнению с предыдущими моделями серии Llama, что предоставляет ценные идеи для дизайна и разработки других моделей."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B предлагает более мощные возможности ИИ вывода, подходит для сложных приложений, поддерживает огромное количество вычислительных процессов и гарантирует эффективность и точность."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B — это высокоэффективная модель, обеспечивающая быструю генерацию текста, идеально подходящая для приложений, требующих масштабной эффективности и экономичности."}, "llama-3.1-instruct": {"description": "Модель Llama 3.1 с тонкой настройкой инструкций оптимизирована для диалоговых сценариев и превосходит многие существующие открытые чат-модели по стандартным отраслевым тестам."}, "llama-3.2-11b-vision-instruct": {"description": "Отличные способности к визуальному пониманию изображений на высоком разрешении, предназначенные для приложений визуального понимания."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 предназначена для обработки задач, сочетающих визуальные и текстовые данные. Она демонстрирует отличные результаты в задачах описания изображений и визуального вопросно-ответного взаимодействия, преодолевая разрыв между генерацией языка и визуальным выводом."}, "llama-3.2-90b-vision-instruct": {"description": "Совершенные возможности визуального понимания для приложения-агента."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 предназначена для обработки задач, сочетающих визуальные и текстовые данные. Она демонстрирует отличные результаты в задачах описания изображений и визуального вопросно-ответного взаимодействия, преодолевая разрыв между генерацией языка и визуальным выводом."}, "llama-3.2-vision-instruct": {"description": "Модель Llama 3.2-Vision с тонкой настройкой команд оптимизирована для визуального распознавания, анализа изображений, описания изображений и ответов на общие вопросы, связанные с изображениями."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 — это самая современная многоязычная открытая языковая модель из серии Llama, которая позволяет получить производительность, сопоставимую с 405B моделями, по очень низкой цене. Основана на структуре Transformer и улучшена с помощью контролируемой донастройки (SFT) и обучения с подкреплением на основе человеческой обратной связи (RLHF) для повышения полезности и безопасности. Ее версия с оптимизацией под инструкции специально разработана для многоязычных диалогов и показывает лучшие результаты по сравнению с множеством открытых и закрытых моделей чата на различных отраслевых бенчмарках. Дата окончания знаний — декабрь 2023 года."}, "llama-3.3-70b-versatile": {"description": "Многоязычная большая языковая модель Meta Llama 3.3 (LLM) — это предобученная и откорректированная модель генерации на 70B (текстовый ввод/текстовый вывод). Откорректированная на чистом тексте модель Llama 3.3 оптимизирована для многоязычных диалоговых задач и превосходит многие доступные открытые и закрытые модели чата по общим промышленным стандартам."}, "llama-3.3-instruct": {"description": "Модель Llama 3.3 с тонкой настройкой инструкций оптимизирована для диалоговых сценариев и превосходит многие существующие модели с открытым исходным кодом в стандартных отраслевых тестах."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B предлагает непревзойдённые возможности обработки сложности, специально разработанные для высоких требований проектов."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B обеспечивает высококачественную производительность вывода, подходящую для многообразных приложений."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use предлагает мощные возможности вызова инструментов, поддерживая эффективную обработку сложных задач."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use — это модель, оптимизированная для эффективного использования инструментов, поддерживающая быструю параллельную обработку."}, "llama3.1": {"description": "Llama 3.1 — это передовая модель, выпущенная Meta, поддерживающая до 405B параметров, применимая в сложных диалогах, многоязычном переводе и анализе данных."}, "llama3.1:405b": {"description": "Llama 3.1 — это передовая модель, выпущенная Meta, поддерживающая до 405B параметров, применимая в сложных диалогах, многоязычном переводе и анализе данных."}, "llama3.1:70b": {"description": "Llama 3.1 — это передовая модель, выпущенная Meta, поддерживающая до 405B параметров, применимая в сложных диалогах, многоязычном переводе и анализе данных."}, "llava": {"description": "LLaVA — это многомодальная модель, объединяющая визуальный кодировщик и Vicuna, предназначенная для мощного понимания визуальной и языковой информации."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B предлагает возможности визуальной обработки, генерируя сложные выходные данные на основе визуальной информации."}, "llava:13b": {"description": "LLaVA — это многомодальная модель, объединяющая визуальный кодировщик и Vicuna, предназначенная для мощного понимания визуальной и языковой информации."}, "llava:34b": {"description": "LLaVA — это многомодальная модель, объединяющая визуальный кодировщик и Vicuna, предназначенная для мощного понимания визуальной и языковой информации."}, "mathstral": {"description": "MathΣtral специально разработан для научных исследований и математического вывода, обеспечивая эффективные вычислительные возможности и интерпретацию результатов."}, "max-32k": {"description": "Spark Max 32K обладает большой способностью обработки контекста, улучшенным пониманием контекста и логическим выводом, поддерживает текстовый ввод до 32K токенов, подходит для чтения длинных документов, частных вопросов и ответов и других сценариев."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct — это крупномасштабная языковая модель, полностью обученная компанией Wuxin Xin Qiong. Megrez-3B-Instruct разработана с использованием концепции совместной оптимизации аппаратного и программного обеспечения, чтобы создать быстрое, компактное и легкое в использовании решение для интеллектуальных задач на стороне устройства."}, "meta-llama-3-70b-instruct": {"description": "Мощная модель с 70 миллиардами параметров, превосходящая в области рассуждений, кодирования и широких языковых приложений."}, "meta-llama-3-8b-instruct": {"description": "Универсальная модель с 8 миллиардами параметров, оптимизированная для диалоговых и текстовых задач."}, "meta-llama-3.1-405b-instruct": {"description": "Модели Llama 3.1, настроенные на инструкции, оптимизированы для многоязычных диалоговых случаев и превосходят многие доступные модели открытого и закрытого чата по общим отраслевым стандартам."}, "meta-llama-3.1-70b-instruct": {"description": "Модели Llama 3.1, настроенные на инструкции, оптимизированы для многоязычных диалоговых случаев и превосходят многие доступные модели открытого и закрытого чата по общим отраслевым стандартам."}, "meta-llama-3.1-8b-instruct": {"description": "Модели Llama 3.1, настроенные на инструкции, оптимизированы для многоязычных диалоговых случаев и превосходят многие доступные модели открытого и закрытого чата по общим отраслевым стандартам."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) предлагает отличные возможности обработки языка и выдающийся опыт взаимодействия."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 предлагает превосходные способности обработки языка и выдающийся пользовательский опыт."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) — мощная модель для чата, поддерживающая сложные диалоговые запросы."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) предлагает многоязычную поддержку и охватывает широкий спектр областей знаний."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 предназначена для выполнения задач, объединяющих визуальные и текстовые данные. Она отлично справляется с задачами по описанию изображений и визуальному вопросу-ответу, преодолевая разрыв между генерацией языка и визуальным пониманием."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 предназначена для выполнения задач, объединяющих визуальные и текстовые данные. Она отлично справляется с задачами по описанию изображений и визуальному вопросу-ответу, преодолевая разрыв между генерацией языка и визуальным пониманием."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 предназначена для выполнения задач, объединяющих визуальные и текстовые данные. Она отлично справляется с задачами по описанию изображений и визуальному вопросу-ответу, преодолевая разрыв между генерацией языка и визуальным пониманием."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Многоязычная большая языковая модель Meta Llama 3.3 (LLM) — это предобученная и настроенная на инструкции генеративная модель объемом 70B (входной/выходной текст). Модель Llama 3.3, настроенная на инструкции, оптимизирована для многоязычных диалоговых случаев и превосходит многие доступные открытые и закрытые модели чата по общим отраслевым бенчмаркам."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 предназначена для выполнения задач, объединяющих визуальные и текстовые данные. Она отлично справляется с задачами по описанию изображений и визуальному вопросу-ответу, преодолевая разрыв между генерацией языка и визуальным пониманием."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite подходит для сред, требующих высокой производительности и низкой задержки."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo обеспечивает выдающиеся возможности понимания и генерации языка, подходящие для самых требовательных вычислительных задач."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite подходит для ресурсов ограниченных сред, обеспечивая отличное соотношение производительности."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo — это высокоэффективная большая языковая модель, поддерживающая широкий спектр приложений."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B — это мощная модель, основанная на предобучении и настройке инструкций."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "Модель Llama 3.1 Turbo 405B предлагает огромную поддержку контекста для обработки больших данных и демонстрирует выдающиеся результаты в масштабных приложениях искусственного интеллекта."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 — это передовая модель, представленная Meta, поддерживающая до 405B параметров, применимая в сложных диалогах, многоязычном переводе и анализе данных."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Модель Llama 3.1 70B была тщательно настроена для высоконагруженных приложений, квантованная до FP8 для повышения вычислительной мощности и точности, обеспечивая выдающиеся результаты в сложных сценариях."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Модель Llama 3.1 8B использует FP8-квантование и поддерживает до 131,072 контекстных токенов, являясь выдающейся среди открытых моделей, подходящей для сложных задач и превосходящей многие отраслевые стандарты."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct оптимизирован для высококачественных диалоговых сцен и показывает отличные результаты в различных оценках."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct оптимизирован для высококачественных диалоговых сцен, его производительность превосходит многие закрытые модели."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct разработан для высококачественных диалогов и показывает выдающиеся результаты в оценках, особенно в высокоинтерактивных сценах."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct — это последняя версия от Meta, оптимизированная для высококачественных диалоговых сцен, превосходящая многие ведущие закрытые модели."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 предлагает поддержку нескольких языков и является одной из ведущих генеративных моделей в отрасли."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 предназначена для обработки задач, сочетающих визуальные и текстовые данные. Она демонстрирует отличные результаты в задачах описания изображений и визуального вопросно-ответного взаимодействия, преодолевая разрыв между генерацией языка и визуальным выводом."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 предназначена для обработки задач, сочетающих визуальные и текстовые данные. Она демонстрирует отличные результаты в задачах описания изображений и визуального вопросно-ответного взаимодействия, преодолевая разрыв между генерацией языка и визуальным выводом."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 — это самая современная многоязычная открытая языковая модель из серии Llama, которая позволяет получить производительность, сопоставимую с 405B моделями, по очень низкой цене. Основана на структуре Transformer и улучшена с помощью контролируемой донастройки (SFT) и обучения с подкреплением на основе человеческой обратной связи (RLHF) для повышения полезности и безопасности. Ее версия с оптимизацией под инструкции специально разработана для многоязычных диалогов и показывает лучшие результаты по сравнению с множеством открытых и закрытых моделей чата на различных отраслевых бенчмарках. Дата окончания знаний — декабрь 2023 года."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 — это самая современная многоязычная открытая языковая модель из серии Llama, которая позволяет получить производительность, сопоставимую с 405B моделями, по очень низкой цене. Основана на структуре Transformer и улучшена с помощью контролируемой донастройки (SFT) и обучения с подкреплением на основе человеческой обратной связи (RLHF) для повышения полезности и безопасности. Ее версия с оптимизацией под инструкции специально разработана для многоязычных диалогов и показывает лучшие результаты по сравнению с множеством открытых и закрытых моделей чата на различных отраслевых бенчмарках. Дата окончания знаний — декабрь 2023 года."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct — это самая большая и мощная модель в линейке Llama 3.1 Instruct, представляющая собой высокоразвёрнутую модель для диалогового вывода и генерации синтетических данных, также может использоваться в качестве основы для специализированного предобучения или дообучения в определённых областях. Многоязычные большие языковые модели (LLMs), предлагаемые Llama 3.1, представляют собой набор предобученных генеративных моделей с настройкой на инструкции, включая размеры 8B, 70B и 405B (вход/выход текста). Модели текста с настройкой на инструкции Llama 3.1 (8B, 70B, 405B) оптимизированы для многоязычных диалоговых случаев и превосходят многие доступные открытые модели чата в общепринятых отраслевых бенчмарках. Llama 3.1 предназначена для коммерческого и исследовательского использования на нескольких языках. Модели текста с настройкой на инструкции подходят для диалогов, похожих на помощников, в то время как предобученные модели могут адаптироваться к различным задачам генерации естественного языка. Модели Llama 3.1 также поддерживают использование их вывода для улучшения других моделей, включая генерацию синтетических данных и уточнение. Llama 3.1 является саморегрессионной языковой моделью, использующей оптимизированную архитектуру трансформеров. Настроенные версии используют контролируемое дообучение (SFT) и обучение с подкреплением с человеческой обратной связью (RLHF), чтобы соответствовать предпочтениям людей в отношении полезности и безопасности."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Обновленная версия Meta Llama 3.1 70B Instruct, включающая расширенную длину контекста до 128K, многоязычность и улучшенные способности вывода. Многоязычные большие языковые модели (LLMs), предлагаемые Llama 3.1, представляют собой набор предобученных, настроенных на инструкции генеративных моделей, включая размеры 8B, 70B и 405B (ввод/вывод текста). Настроенные на инструкции текстовые модели (8B, 70B, 405B) оптимизированы для многоязычных диалоговых случаев и превосходят многие доступные открытые модели чата в общих отраслевых бенчмарках. Llama 3.1 предназначена для коммерческого и исследовательского использования на нескольких языках. Настроенные на инструкции текстовые модели подходят для диалогов, похожих на помощника, в то время как предобученные модели могут адаптироваться к различным задачам генерации естественного языка. Модели Llama 3.1 также поддерживают использование вывода своих моделей для улучшения других моделей, включая генерацию синтетических данных и уточнение. Llama 3.1 — это саморегрессионная языковая модель, использующая оптимизированную архитектуру трансформеров. Настроенные версии используют контролируемую донастройку (SFT) и обучение с подкреплением с человеческой обратной связью (RLHF), чтобы соответствовать человеческим предпочтениям по полезности и безопасности."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Обновленная версия Meta Llama 3.1 8B Instruct, включающая расширенную длину контекста до 128K, многоязычность и улучшенные способности вывода. Многоязычные большие языковые модели (LLMs), предлагаемые Llama 3.1, представляют собой набор предобученных, настроенных на инструкции генеративных моделей, включая размеры 8B, 70B и 405B (ввод/вывод текста). Настроенные на инструкции текстовые модели (8B, 70B, 405B) оптимизированы для многоязычных диалоговых случаев и превосходят многие доступные открытые модели чата в общих отраслевых бенчмарках. Llama 3.1 предназначена для коммерческого и исследовательского использования на нескольких языках. Настроенные на инструкции текстовые модели подходят для диалогов, похожих на помощника, в то время как предобученные модели могут адаптироваться к различным задачам генерации естественного языка. Модели Llama 3.1 также поддерживают использование вывода своих моделей для улучшения других моделей, включая генерацию синтетических данных и уточнение. Llama 3.1 — это саморегрессионная языковая модель, использующая оптимизированную архитектуру трансформеров. Настроенные версии используют контролируемую донастройку (SFT) и обучение с подкреплением с человеческой обратной связью (RLHF), чтобы соответствовать человеческим предпочтениям по полезности и безопасности."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 — это открытая большая языковая модель (LLM), ориентированная на разработчиков, исследователей и предприятия, предназначенная для помощи в создании, экспериментировании и ответственном масштабировании их идей по генеративному ИИ. В качестве части базовой системы для инноваций глобального сообщества она идеально подходит для создания контента, диалогового ИИ, понимания языка, НИОКР и корпоративных приложений."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 — это открытая большая языковая модель (LLM), ориентированная на разработчиков, исследователей и предприятия, предназначенная для помощи в создании, экспериментировании и ответственном масштабировании их идей по генеративному ИИ. В качестве части базовой системы для инноваций глобального сообщества она идеально подходит для устройств с ограниченными вычислительными мощностями и ресурсами, а также для более быстрого времени обучения."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Отличные способности к рассуждению на основе изображений высокого разрешения, подходит для приложений визуального понимания."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Продвинутые возможности рассуждения на основе изображений для приложений визуального понимания и агентов."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 — самая передовая многоязычная открытая крупная языковая модель серии Llama, обеспечивающая производительность, сопоставимую с моделью на 405 млрд параметров, при очень низких затратах. Основана на архитектуре Transformer и улучшена с помощью контролируемой донастройки (SFT) и обучения с подкреплением на основе человеческой обратной связи (RLHF) для повышения полезности и безопасности. Версия с инструкциями оптимизирована для многоязычного диалога и превосходит многие открытые и закрытые чат-модели по ряду отраслевых бенчмарков. Дата отсечения знаний — декабрь 2023 года."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Мощная модель с 70 млрд параметров, демонстрирующая выдающиеся способности в рассуждениях, кодировании и широком спектре языковых приложений."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Универсальная модель с 8 млрд параметров, оптимизированная для задач диалога и генерации текста."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Текстовая модель Llama 3.1 с донастройкой по инструкциям, оптимизированная для многоязычных диалогов, демонстрирующая высокие результаты на популярных отраслевых бенчмарках среди доступных открытых и закрытых чат-моделей."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Текстовая модель Llama 3.1 с донастройкой по инструкциям, оптимизированная для многоязычных диалогов, демонстрирующая высокие результаты на популярных отраслевых бенчмарках среди доступных открытых и закрытых чат-моделей."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Текстовая модель Llama 3.1 с донастройкой по инструкциям, оптимизированная для многоязычных диалогов, демонстрирующая высокие результаты на популярных отраслевых бенчмарках среди доступных открытых и закрытых чат-моделей."}, "meta/llama-3.1-405b-instruct": {"description": "Современная LLM, поддерживающая генерацию синтетических данных, дистилляцию знаний и рассуждения, подходит для чат-ботов, программирования и специализированных задач."}, "meta/llama-3.1-70b-instruct": {"description": "Обеспечивает сложные диалоги, обладая выдающимся пониманием контекста, способностями к рассуждению и генерации текста."}, "meta/llama-3.1-8b-instruct": {"description": "Современная передовая модель, обладающая пониманием языка, выдающимися способностями к рассуждению и генерации текста."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Современная визуально-языковая модель, специализирующаяся на высококачественном рассуждении на основе изображений."}, "meta/llama-3.2-1b-instruct": {"description": "Современная передовая компактная языковая модель, обладающая пониманием языка, выдающимися способностями к рассуждению и генерации текста."}, "meta/llama-3.2-3b-instruct": {"description": "Современная передовая компактная языковая модель, обладающая пониманием языка, выдающимися способностями к рассуждению и генерации текста."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Современная визуально-языковая модель, специализирующаяся на высококачественном рассуждении на основе изображений."}, "meta/llama-3.3-70b-instruct": {"description": "Современная LLM, специализирующаяся на рассуждениях, математике, здравом смысле и вызовах функций."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "Та же модель Phi-3-medium, но с увеличенным размером контекста, подходящая для RAG или небольшого количества подсказок."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Модель с 14 млрд параметров, превосходящая Phi-3-mini по качеству, ориентированная на высококачественные, интенсивные по рассуждениям данные."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Та же модель Phi-3-mini, но с увеличенным размером контекста, подходящая для RAG или небольшого количества подсказок."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Самый маленький представитель семейства Phi-3, оптимизированный по качеству и низкой задержке."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Та же модель Phi-3-small, но с увеличенным размером контекста, подходящая для RAG или небольшого количества подсказок."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Модель с 7 млрд параметров, превосходящая Phi-3-mini по качеству, ориентированная на высококачественные, интенсивные по рассуждениям данные."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Обновленная версия модели Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Обновленная версия модели Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 — это языковая модель от Microsoft AI, которая особенно хорошо справляется с сложными диалогами, многоязычностью, выводами и интеллектуальными помощниками."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B — это передовая модель Wizard от Microsoft, демонстрирующая исключительно конкурентоспособные результаты."}, "minicpm-v": {"description": "MiniCPM-V — это новое поколение мультимодальной большой модели от OpenBMB, обладающее выдающимися возможностями OCR и мультимодального понимания, поддерживающее широкий спектр приложений."}, "ministral-3b-latest": {"description": "Ministral 3B - это выдающаяся модель от Mistral."}, "ministral-8b-latest": {"description": "Ministral 8B - это экономически эффективная модель от Mistral."}, "mistral": {"description": "Mistral — это 7B модель, выпущенная Mistral AI, подходящая для разнообразных языковых задач."}, "mistral-ai/Mistral-Large-2411": {"description": "Флагманская модель Mistral, предназначенная для задач, требующих масштабных возможностей рассуждения или высокой специализации (синтез текста, генерация кода, RAG или агенты)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo — передовая языковая модель (LLM), обладающая лучшими в своем классе способностями к рассуждению, мировым знаниям и кодированию."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small подходит для любых языковых задач, требующих высокой эффективности и низкой задержки."}, "mistral-large": {"description": "Mixtral Large — это флагманская модель от Mistral, объединяющая возможности генерации кода, математики и вывода, поддерживающая контекстное окно 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 — это передовая плотная большая языковая модель (LLM) с 123 миллиардами параметров, обладающая современными возможностями логического вывода, обработки знаний и программирования."}, "mistral-large-latest": {"description": "Mistral Large — это флагманская большая модель, хорошо подходящая для многоязычных задач, сложного вывода и генерации кода, идеальный выбор для высококлассных приложений."}, "mistral-medium-latest": {"description": "Mistral Medium 3 предлагает передовые характеристики с восьмикратными затратами и значительно упрощает развертывание в корпоративной среде."}, "mistral-nemo": {"description": "Mistral Nemo, разработанный в сотрудничестве между Mistral AI и NVIDIA, является высокоэффективной 12B моделью."}, "mistral-nemo-instruct": {"description": "Mistral-Nemo-Instruct-2407 — это крупная языковая модель (LLM), представляющая собой версию Mistral-Nemo-Base-2407 с тонкой настройкой для выполнения инструкций."}, "mistral-small": {"description": "Mistral Small может использоваться для любых языковых задач, требующих высокой эффективности и низкой задержки."}, "mistral-small-latest": {"description": "Mistral Small — это экономически эффективный, быстрый и надежный вариант для таких случаев, как перевод, резюме и анализ настроений."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct известен своей высокой производительностью и подходит для множества языковых задач."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B — это модель с настройкой по запросу, предлагающая оптимизированные ответы на задачи."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 обеспечивает эффективные вычислительные возможности и понимание естественного языка, подходящие для широкого спектра приложений."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B - это компактная, но высокопроизводительная модель, хорошо подходящая для пакетной обработки и простых задач, таких как классификация и генерация текста, с хорошими способностями к рассуждению."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) — это супер большая языковая модель, поддерживающая крайне высокие требования к обработке."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B — это предобученная модель разреженных смешанных экспертов, предназначенная для универсальных текстовых задач."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B - это разреженная модель эксперта, использующая множество параметров для повышения скорости вывода, подходит для обработки многоязычных и генеративных задач."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct — это высокопроизводительная модель стандартов отрасли, оптимизированная для скорости и поддержки длинного контекста."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo — это модель с 7.3B параметрами, поддерживающая несколько языков и высокопроизводительное программирование."}, "mixtral": {"description": "Mixtral — это экспертная модель от Mistral AI, обладающая открытыми весами и поддерживающая генерацию кода и понимание языка."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B предлагает высокую отказоустойчивость параллельной обработки, подходящей для сложных задач."}, "mixtral:8x22b": {"description": "Mixtral — это экспертная модель от Mistral AI, обладающая открытыми весами и поддерживающая генерацию кода и понимание языка."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K — это модель с возможностями обработки сверхдлинного контекста, подходящая для генерации очень длинных текстов, удовлетворяющая требованиям сложных задач генерации, способная обрабатывать до 128 000 токенов, идеально подходящая для научных исследований, академических и крупных документальных приложений."}, "moonshot-v1-128k-vision-preview": {"description": "Модель визуализаци<PERSON> (включая moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview и др.) может понимать содержимое изображений, включая текст на изображениях, цвета изображений и формы объектов."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K предлагает возможности обработки контекста средней длины, способная обрабатывать 32 768 токенов, особенно подходит для генерации различных длинных документов и сложных диалогов, применяется в создании контента, генерации отчетов и диалоговых систем."}, "moonshot-v1-32k-vision-preview": {"description": "Модель визуализаци<PERSON> (включая moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview и др.) может понимать содержимое изображений, включая текст на изображениях, цвета изображений и формы объектов."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K специально разработан для генерации коротких текстов, обладая высокой производительностью обработки, способный обрабатывать 8 192 токена, идеально подходит для кратких диалогов, стенографирования и быстрой генерации контента."}, "moonshot-v1-8k-vision-preview": {"description": "Модель визуализаци<PERSON> (включая moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview и др.) может понимать содержимое изображений, включая текст на изображениях, цвета изображений и формы объектов."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto может выбирать подходящую модель в зависимости от количества токенов, используемых в текущем контексте."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B — это крупная модель с открытым исходным кодом, оптимизированная с помощью масштабного обучения с подкреплением, способная выдавать надежные патчи, готовые к непосредственному внедрению. Эта модель достигла нового рекордного результата 60,4 % на SWE-bench Verified, обновив рекорды открытых моделей в автоматизированных задачах программной инженерии, таких как исправление ошибок и код-ревью."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 — базовая модель на архитектуре MoE с выдающимися возможностями в кодировании и агентских задачах, с общим числом параметров 1 триллион и 32 миллиардами активируемых параметров. В тестах на универсальное знание, программирование, математику и агентские задачи производительность модели K2 превосходит другие ведущие открытые модели."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 — базовая модель на архитектуре MoE с мощными возможностями кода и агента, общий объем параметров 1 триллион, активные параметры 32 миллиарда. В тестах производительности по основным категориям, таким как общие знания, программирование, математика и агенты, модель K2 превосходит другие популярные открытые модели."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B — это обновленная версия Nous Hermes 2, содержащая последние внутренние разработанные наборы данных."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B — это крупная языковая модель, созданная NVIDIA, предназначенная для повышения полезности ответов, генерируемых LLM, на запросы пользователей. Эта модель показала отличные результаты в таких бенчмарках, как Arena Hard, AlpacaEval 2 LC и GPT-4-Turbo MT-Bench, и на 1 октября 2024 года занимает первое место во всех трех автоматических тестах на согласование. Модель обучалась с использованием RLHF (в частности, REINFORCE), Llama-3.1-Nemotron-70B-Reward и HelpSteer2-Preference на основе модели Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Уникальная языковая модель, обеспечивающая непревзойденную точность и эффективность."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B — это крупная языковая модель, разработанная NVIDIA, предназначенная для повышения полезности ответов, генерируемых LLM."}, "o1": {"description": "Сосредоточена на высокоуровневом выводе и решении сложных задач, включая математические и научные задачи. Идеально подходит для приложений, требующих глубокого понимания контекста и управления рабочими процессами."}, "o1-mini": {"description": "o1-mini — это быстрое и экономичное модель вывода, разработанная для программирования, математики и научных приложений. Модель имеет контекст 128K и срок знания до октября 2023 года."}, "o1-preview": {"description": "o1 — это новая модель вывода от OpenAI, подходящая для сложных задач, требующих обширных общих знаний. Модель имеет контекст 128K и срок знания до октября 2023 года."}, "o1-pro": {"description": "Модели серии o1 обучены с использованием обучения с подкреплением, способны размышлять перед ответом и выполнять сложные задачи рассуждения. Модель o1-pro использует больше вычислительных ресурсов для более глубокого мышления, обеспечивая постоянно высокое качество ответов."}, "o3": {"description": "o3 — это универсальная мощная модель, которая демонстрирует отличные результаты в различных областях. Она устанавливает новые стандарты для задач математики, науки, программирования и визуального вывода. Она также хорошо справляется с техническим письмом и соблюдением инструкций. Пользователи могут использовать ее для анализа текста, кода и изображений, решая сложные многошаговые задачи."}, "o3-deep-research": {"description": "o3-deep-research — это наша самая передовая модель глубокого исследования, разработанная для обработки сложных многоэтапных исследовательских задач. Она может искать и обобщать информацию из интернета, а также получать доступ к вашим собственным данным и использовать их через MCP-коннектор."}, "o3-mini": {"description": "o3-mini — это наша последняя компактная модель вывода, обеспечивающая высокий уровень интеллекта при тех же затратах и задержках, что и o1-mini."}, "o3-pro": {"description": "Модель o3-pro использует больше вычислительных ресурсов для более глубокого мышления и всегда предоставляет лучшие ответы, поддерживается только в Responses API."}, "o4-mini": {"description": "o4-mini — это наша новейшая компактная модель серии o. Она оптимизирована для быстрого и эффективного вывода, демонстрируя высокую эффективность и производительность в задачах кодирования и визуализации."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research — это более быстрая и доступная модель глубокого исследования, идеально подходящая для обработки сложных многоэтапных исследовательских задач. Она может искать и обобщать информацию из интернета, а также получать доступ к вашим собственным данным и использовать их через MCP-коннектор."}, "open-codestral-mamba": {"description": "Codestral Mamba — это языковая модель Mamba 2, сосредоточенная на генерации кода, обеспечивающая мощную поддержку для сложных задач по коду и выводу."}, "open-mistral-7b": {"description": "Mistral 7B — это компактная, но высокопроизводительная модель, хорошо подходящая для пакетной обработки и простых задач, таких как классификация и генерация текста, обладающая хорошими возможностями вывода."}, "open-mistral-nemo": {"description": "Mistral Nemo — это 12B модель, разработанная в сотрудничестве с Nvidia, обеспечивающая выдающиеся возможности вывода и кодирования, легко интегрируемая и заменяемая."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B — это более крупная экспертная модель, сосредоточенная на сложных задачах, предлагающая выдающиеся возможности вывода и более высокую пропускную способность."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B — это разреженная экспертная модель, использующая несколько параметров для повышения скорости вывода, подходит для обработки многоязычных и кодовых задач."}, "openai/gpt-4.1": {"description": "GPT-4.1 — это наша флагманская модель для сложных задач. Она идеально подходит для решения междисциплинарных проблем."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini предлагает баланс между интеллектом, скоростью и стоимостью, что делает её привлекательной моделью для многих случаев использования."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano — это самая быстрая и экономически эффективная модель из GPT-4.1."}, "openai/gpt-4o": {"description": "ChatGPT-4o — это динамическая модель, которая обновляется в реальном времени, чтобы оставаться актуальной. Она сочетает в себе мощные способности понимания и генерации языка, подходит для масштабных приложений, включая обслуживание клиентов, образование и техническую поддержку."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini — это последняя модель от OpenAI, выпущенная после GPT-4 Omni, поддерживающая ввод изображений и текста с выводом текста. Как их самый продвинутый компактный модель, она значительно дешевле других недавних передовых моделей и более чем на 60% дешевле GPT-3.5 Turbo. Она сохраняет передовой уровень интеллекта при значительном соотношении цена-качество. GPT-4o mini набрала 82% в тесте MMLU и в настоящее время занимает более высокое место по предпочтениям в чате, чем GPT-4."}, "openai/o1": {"description": "o1 — новая модель рассуждений от OpenAI, поддерживающая ввод изображений и текста с выводом текста, предназначенная для сложных задач, требующих широких универсальных знаний. Модель обладает контекстом в 200K и датой отсечения знаний — октябрь 2023 года."}, "openai/o1-mini": {"description": "o1-mini — это быстрое и экономичное модель вывода, разработанная для программирования, математики и научных приложений. Модель имеет контекст 128K и срок знания до октября 2023 года."}, "openai/o1-preview": {"description": "o1 — это новая модель вывода от OpenAI, подходящая для сложных задач, требующих обширных общих знаний. Модель имеет контекст 128K и срок знания до октября 2023 года."}, "openai/o3": {"description": "o3 — это мощная универсальная модель, которая демонстрирует отличные результаты в различных областях. Она устанавливает новые стандарты для задач в математике, науке, программировании и визуальном мышлении. Она также хорошо справляется с техническим письмом и соблюдением инструкций. Пользователи могут использовать её для анализа текста, кода и изображений, а также для решения сложных многошаговых задач."}, "openai/o3-mini": {"description": "o3-mini обеспечивает высокий интеллект при тех же целях по стоимости и задержке, что и o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini high — версия с высоким уровнем вывода, которая обеспечивает высокий интеллект при тех же целях по стоимости и задержке, что и o1-mini."}, "openai/o4-mini": {"description": "o4-mini оптимизирована для быстрого и эффективного вывода, демонстрируя высокую эффективность и производительность в задачах кодирования и визуализации."}, "openai/o4-mini-high": {"description": "o4-mini версия с высоким уровнем вывода, оптимизированная для быстрого и эффективного вывода, демонстрирующая высокую эффективность и производительность в задачах кодирования и визуализации."}, "openrouter/auto": {"description": "В зависимости от длины контекста, темы и сложности ваш запрос будет отправлен в Llama 3 70B Instruct, Claude 3.5 Sonnet (саморегулирующийся) или GPT-4o."}, "phi3": {"description": "Phi-3 — это легковесная открытая модель, выпущенная Microsoft, подходящая для эффективной интеграции и масштабного вывода знаний."}, "phi3:14b": {"description": "Phi-3 — это легковесная открытая модель, выпущенная Microsoft, подходящая для эффективной интеграции и масштабного вывода знаний."}, "pixtral-12b-2409": {"description": "Модель Pixtral демонстрирует мощные способности в задачах графиков и понимания изображений, вопросов и ответов по документам, многомодального вывода и соблюдения инструкций, способная обрабатывать изображения в естественном разрешении и соотношении сторон, а также обрабатывать произвольное количество изображений в контекстном окне длиной до 128K токенов."}, "pixtral-large-latest": {"description": "Pixtral Large — это открытая многомодальная модель с 1240 миллиардами параметров, основанная на Mistral Large 2. Это вторая модель в нашей многомодальной семье, демонстрирующая передовые уровни понимания изображений."}, "pro-128k": {"description": "Spark Pro 128K оснащен огромной способностью обработки контекста, способной обрабатывать до 128K контекстной информации, что делает его особенно подходящим для анализа длинных текстов и обработки долгосрочных логических связей, обеспечивая плавную и последовательную логику и разнообразную поддержку ссылок в сложных текстовых коммуникациях."}, "qvq-72b-preview": {"description": "Модель QVQ, разработанная командой Qwen, является экспериментальной исследовательской моделью, сосредоточенной на повышении визуальных способностей рассуждения, особенно в области математического рассуждения."}, "qvq-max": {"description": "Модель визуального рассуждения Tongyi Qianwen QVQ, поддерживающая визуальный ввод и вывод цепочек рассуждений, демонстрирует усиленные возможности в математике, программировании, визуальном анализе, творчестве и общих задачах."}, "qvq-plus": {"description": "Модель визуального рассуждения. Поддерживает визуальный ввод и вывод цепочек рассуждений, версия plus, выпущенная после модели qvq-max. По сравнению с qvq-max, серия qvq-plus обеспечивает более высокую скорость рассуждений и более сбалансированное соотношение эффективности и затрат."}, "qwen-coder-plus": {"description": "Модель кода Tongyi Qianwen."}, "qwen-coder-turbo": {"description": "Модель кода Tongyi Qianwen."}, "qwen-coder-turbo-latest": {"description": "Модель кода Tongyi Qwen."}, "qwen-long": {"description": "Qwen — это сверхмасштабная языковая модель, поддерживающая длинный контекст текста и диалоговые функции на основе длинных документов и нескольких документов."}, "qwen-math-plus": {"description": "Модель Tongyi Qianwen, специально предназначенная для решения математических задач."}, "qwen-math-plus-latest": {"description": "Математическая модель Tongyi Qwen, специально разработанная для решения математических задач."}, "qwen-math-turbo": {"description": "Модель Tongyi Qianwen, специально предназначенная для решения математических задач."}, "qwen-math-turbo-latest": {"description": "Математическая модель Tongyi Qwen, специально разработанная для решения математических задач."}, "qwen-max": {"description": "Qwen-Max — это языковая модель масштаба триллиона, поддерживающая входные данные на различных языках, включая китайский и английский. В настоящее время это API, которое стоит за продуктовой версией Qwen 2.5."}, "qwen-omni-turbo": {"description": "Серия моделей Qwen-Omni поддерживает ввод данных различных модальностей, включая видео, аудио, изображения и текст, а также вывод аудио и текста."}, "qwen-plus": {"description": "Улучшенная версия Qwen-Turbo, поддерживающая входные данные на разных языках, включая китайский и английский."}, "qwen-turbo": {"description": "Qwen-Turbo — это крупная языковая модель, поддерживающая входные данные на разных языках, включая китайский и английский."}, "qwen-vl-chat-v1": {"description": "Qwen VL поддерживает гибкие способы взаимодействия, включая многократные изображения, многократные вопросы и ответы, а также творческие способности."}, "qwen-vl-max": {"description": "Сверхмасштабная визуально-языковая модель Tongyi Qianwen. По сравнению с усиленной версией дополнительно улучшены способности визуального рассуждения и соблюдения инструкций, обеспечивается более высокий уровень визуального восприятия и когнитивных функций."}, "qwen-vl-max-latest": {"description": "Супер масштабная визуально-языковая модель Tongyi Qianwen. По сравнению с улучшенной версией, еще больше повышает способности визуального вывода и соблюдения инструкций, обеспечивая более высокий уровень визуального восприятия и когнитивных способностей."}, "qwen-vl-ocr": {"description": "Специализированная модель OCR Tongyi Qianwen, ориентированная на извлечение текста из документов, таблиц, тестов, рукописного текста и других типов изображений. Поддерживает распознавание множества языков, включая китайский, английский, французский, японский, корейский, немецкий, русский, итальянский, вьетнамский и арабский."}, "qwen-vl-plus": {"description": "Усиленная версия масштабной визуально-языковой модели Tongyi Qianwen. Значительно улучшена способность распознавания деталей и текста, поддерживается разрешение свыше миллиона пикселей и изображения с произвольным соотношением сторон."}, "qwen-vl-plus-latest": {"description": "Улучшенная версия масштабной визуально-языковой модели Tongyi Qianwen. Значительно повышает способность распознавания деталей и текста, поддерживает разрешение более миллиона пикселей и изображения с произвольным соотношением сторон."}, "qwen-vl-v1": {"description": "Инициализированная языковой моделью Qwen-7B, добавлена модель изображения, предобученная модель с разрешением входного изображения 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 — это новая серия больших языковых моделей Qwen. Qwen2 7B — это модель на основе трансформера, которая демонстрирует отличные результаты в понимании языка, многоязычных способностях, программировании, математике и логическом рассуждении."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 — это новая серия крупных языковых моделей с более сильными возможностями понимания и генерации."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL — это последняя итерация модели Qwen-VL, достигшая передовых результатов в бенчмарках визуального понимания, включая MathVista, DocVQA, RealWorldQA и MTVQA. Qwen2-VL может понимать видео продолжительностью более 20 минут для высококачественного видеозапроса, диалога и создания контента. Она также обладает сложными способностями к рассуждению и принятию решений, может интегрироваться с мобильными устройствами, роботами и выполнять автоматические операции на основе визуальной среды и текстовых инструкций. Кроме английского и китайского, Qwen2-VL теперь также поддерживает понимание текста на разных языках в изображениях, включая большинство европейских языков, японский, корейский, арабский и вьетнамский."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct — это одна из последних серий больших языковых моделей, выпущенных Alibaba Cloud. Эта модель 72B демонстрирует значительные улучшения в области кодирования и математики. Модель также поддерживает множество языков, охватывающих более 29 языков, включая китайский и английский. Она значительно улучшила выполнение инструкций, понимание структурированных данных и генерацию структурированных выходных данных (особенно JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct — это одна из последних серий больших языковых моделей, выпущенных Alibaba Cloud. Эта модель 32B демонстрирует значительные улучшения в области кодирования и математики. Модель поддерживает множество языков, охватывающих более 29 языков, включая китайский и английский. Она значительно улучшила выполнение инструкций, понимание структурированных данных и генерацию структурированных выходных данных (особенно JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM, ориентированная на китайский и английский языки, охватывающая области языка, программирования, математики, рассуждений и др."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "Современная LLM, поддерживающая генерацию кода, рассуждения и исправления, охватывающая основные языки программирования."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Мощная средняя модель кода, поддерживающая контекст длиной 32K, специализирующаяся на многоязычном программировании."}, "qwen/qwen3-14b": {"description": "Qwen3-14B — это компактная языковая модель с 14 миллиардами параметров из серии Qwen3, специально разработанная для сложного вывода и эффективного диалога. Она поддерживает бесшовное переключение между режимом размышления для задач, таких как математика, программирование и логический вывод, и неразмышляющим режимом для общего диалога. Эта модель была дообучена для выполнения инструкций, использования инструментов агентов, креативного письма и многоязычных задач на более чем 100 языках и диалектах. Она изначально обрабатывает контекст в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B — это компактная языковая модель с 14 миллиардами параметров из серии Qwen3, специально разработанная для сложного вывода и эффективного диалога. Она поддерживает бесшовное переключение между режимом размышления для задач, таких как математика, программирование и логический вывод, и неразмышляющим режимом для общего диалога. Эта модель была дообучена для выполнения инструкций, использования инструментов агентов, креативного письма и многоязычных задач на более чем 100 языках и диалектах. Она изначально обрабатывает контекст в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B — это модель смешанной экспертизы (MoE) с 235 миллиардами параметров, разработан<PERSON>я Qwen, которая активирует 22 миллиарда параметров за один проход. Она поддерживает бесшовное переключение между режимом размышления для сложного вывода, математики и кодирования и неразмышляющим режимом для общей диалоговой эффективности. Эта модель демонстрирует мощные способности вывода, многоязычную поддержку (более 100 языков и диалектов), высокую точность выполнения инструкций и вызов инструментов агентов. Она изначально обрабатывает контекстное окно в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B — это модель смешанной экспертизы (MoE) с 235 миллиардами параметров, разработан<PERSON>я Qwen, которая активирует 22 миллиарда параметров за один проход. Она поддерживает бесшовное переключение между режимом размышления для сложного вывода, математики и кодирования и неразмышляющим режимом для общей диалоговой эффективности. Эта модель демонстрирует мощные способности вывода, многоязычную поддержку (более 100 языков и диалектов), высокую точность выполнения инструкций и вызов инструментов агентов. Она изначально обрабатывает контекстное окно в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 — это новое поколение серии крупных языковых моделей Qwen, обладающее архитектурой смешанной экспертизы (MoE), которое демонстрирует выдающиеся результаты в области вывода, многоязычной поддержки и сложных задач. Его уникальная способность бесшовно переключаться между режимами размышления для сложного вывода и неразмышляющим режимом для эффективного диалога обеспечивает многофункциональную и высококачественную производительность.\n\nQwen3 значительно превосходит предыдущие модели, такие как QwQ и Qwen2.5, предлагая выдающиеся способности в математике, программировании, логическом выводе, креативном письме и интерактивном диалоге. Вариант Qwen3-30B-A3B содержит 30,5 миллиарда параметров (3,3 миллиарда активируемых параметров), 48 слоев, 128 экспертов (по 8 активируемых для каждой задачи) и поддерживает контекст до 131K токенов (с использованием YaRN), устанавливая новый стандарт для открытых моделей."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 — это новое поколение серии крупных языковых моделей Qwen, обладающее архитектурой смешанной экспертизы (MoE), которое демонстрирует выдающиеся результаты в области вывода, многоязычной поддержки и сложных задач. Его уникальная способность бесшовно переключаться между режимами размышления для сложного вывода и неразмышляющим режимом для эффективного диалога обеспечивает многофункциональную и высококачественную производительность.\n\nQwen3 значительно превосходит предыдущие модели, такие как QwQ и Qwen2.5, предлагая выдающиеся способности в математике, программировании, логическом выводе, креативном письме и интерактивном диалоге. Вариант Qwen3-30B-A3B содержит 30,5 миллиарда параметров (3,3 миллиарда активируемых параметров), 48 слоев, 128 экспертов (по 8 активируемых для каждой задачи) и поддерживает контекст до 131K токенов (с использованием YaRN), устанавливая новый стандарт для открытых моделей."}, "qwen/qwen3-32b": {"description": "Qwen3-32B — это компактная языковая модель с 32 миллиардами параметров из серии Qwen3, оптимизированная для сложного вывода и эффективного диалога. Она поддерживает бесшовное переключение между режимом размышления для задач, таких как математика, программирование и логический вывод, и неразмышляющим режимом для более быстрого общего диалога. Эта модель демонстрирует высокую производительность в выполнении инструкций, использовании инструментов агентов, креативном письме и многоязычных задачах на более чем 100 языках и диалектах. Она изначально обрабатывает контекст в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B — это компактная языковая модель с 32 миллиардами параметров из серии Qwen3, оптимизированная для сложного вывода и эффективного диалога. Она поддерживает бесшовное переключение между режимом размышления для задач, таких как математика, программирование и логический вывод, и неразмышляющим режимом для более быстрого общего диалога. Эта модель демонстрирует высокую производительность в выполнении инструкций, использовании инструментов агентов, креативном письме и многоязычных задачах на более чем 100 языках и диалектах. Она изначально обрабатывает контекст в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B — это компактная языковая модель с 8 миллиардами параметров из серии Qwen3, специально разработанная для задач, требующих интенсивного вывода, и эффективного диалога. Она поддерживает бесшовное переключение между режимом размышления для математики, программирования и логического вывода и неразмышляющим режимом для общего диалога. Эта модель была дообучена для выполнения инструкций, интеграции агентов, креативного письма и многоязычного использования на более чем 100 языках и диалектах. Она изначально поддерживает контекстное окно в 32K токенов и может быть расширена до 131K токенов с помощью YaRN."}, "qwen2": {"description": "Qwen2 — это новое поколение крупномасштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных приложений."}, "qwen2-72b-instruct": {"description": "Qwen2 — это новая серия больших языковых моделей, разработанная командой Qwen. Она основана на архитектуре Transformer и использует такие технологии, как функция активации SwiGLU, смещение QKV внимания (attention QKV bias), групповой запрос внимания (group query attention), смесь скользящего окна внимания (mixture of sliding window attention) и полное внимание. Кроме того, команда Qwen улучшила токенизатор, адаптированный для обработки различных естественных языков и кода."}, "qwen2-7b-instruct": {"description": "Qwen2 — это новая серия больших языковых моделей, разработанная командой Qwen. Она основана на архитектуре Transformer и использует такие технологии, как функция активации SwiGLU, смещение QKV внимания (attention QKV bias), групповой запрос внимания (group query attention), смесь скользящего окна внимания (mixture of sliding window attention) и полное внимание. Кроме того, команда Qwen улучшила токенизатор, адаптированный для обработки различных естественных языков и кода."}, "qwen2.5": {"description": "Qwen2.5 — это новое поколение масштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных потребностей приложений."}, "qwen2.5-14b-instruct": {"description": "Модель Tongyi Qwen 2.5 с открытым исходным кодом объемом 14B."}, "qwen2.5-14b-instruct-1m": {"description": "Модель Qwen2.5 с открытым исходным кодом объемом 72B."}, "qwen2.5-32b-instruct": {"description": "Модель Tongyi Qwen 2.5 с открытым исходным кодом объемом 32B."}, "qwen2.5-72b-instruct": {"description": "Модель Tongyi Qwen 2.5 с открытым исходным кодом объемом 72B."}, "qwen2.5-7b-instruct": {"description": "Модель Tongyi Qwen 2.5 с открытым исходным кодом объемом 7B."}, "qwen2.5-coder-1.5b-instruct": {"description": "Открытая версия модели кода Qwen."}, "qwen2.5-coder-14b-instruct": {"description": "Открытая версия модели кода Tongyi Qianwen."}, "qwen2.5-coder-32b-instruct": {"description": "Открытая версия модели кода Tongyi Qianwen."}, "qwen2.5-coder-7b-instruct": {"description": "Открытая версия модели кода Tongyi Qwen."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder — это новейшая специализированная большая языковая модель для работы с кодом в серии Qwen (ранее известная как CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 — это новейшая серия больших языковых моделей Qwen. Для Qwen2.5 мы выпустили несколько базовых языковых моделей и моделей с тонкой настройкой инструкций, с диапазоном параметров от 500 миллионов до 7,2 миллиарда."}, "qwen2.5-math-1.5b-instruct": {"description": "Мод<PERSON><PERSON><PERSON> Qwen-Math обладает выдающимися способностями к решению математических задач."}, "qwen2.5-math-72b-instruct": {"description": "Мод<PERSON><PERSON>ь Qwen-Math с мощными способностями решения математических задач."}, "qwen2.5-math-7b-instruct": {"description": "Мод<PERSON><PERSON>ь Qwen-Math с мощными способностями решения математических задач."}, "qwen2.5-omni-7b": {"description": "Модели серии Qwen-Omni поддерживают ввод данных в различных модальностях, включая видео, аудио, изображения и текст, и выводят аудио и текст."}, "qwen2.5-vl-32b-instruct": {"description": "Модели серии Qwen2.5-VL демонстрируют повышенный уровень интеллекта, практичности и адаптивности, что обеспечивает их превосходную производительность в таких сценариях, как естественные диалоги, создание контента, предоставление экспертных знаний и разработка кода. Версия 32B оптимизирована с использованием технологий обучения с подкреплением, что по сравнению с другими моделями серии Qwen2.5 VL обеспечивает более соответствующий человеческим предпочтениям стиль вывода, способность к решению сложных математических задач, а также детальное понимание и анализ изображений."}, "qwen2.5-vl-72b-instruct": {"description": "Улучшение следования инструкциям, математики, решения задач и кода, улучшение способности распознавания объектов, поддержка точного позиционирования визуальных элементов в различных форматах, поддержка понимания длинных видеофайлов (максимум 10 минут) и локализация событий на уровне секунд, способность понимать последовательность времени и скорость, поддержка управления агентами ОС или мобильными устройствами на основе аналитических и позиционных возможностей, высокая способность извлечения ключевой информации и вывода в формате Json. Эта версия является 72B, самой мощной в серии."}, "qwen2.5-vl-7b-instruct": {"description": "Улучшение следования инструкциям, математики, решения задач и кода, улучшение способности распознавания объектов, поддержка точного позиционирования визуальных элементов в различных форматах, поддержка понимания длинных видеофайлов (максимум 10 минут) и локализация событий на уровне секунд, способность понимать последовательность времени и скорость, поддержка управления агентами ОС или мобильными устройствами на основе аналитических и позиционных возможностей, высокая способность извлечения ключевой информации и вывода в формате Json. Эта версия является 72B, самой мощной в серии."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL - это последняя версия визуально-языковой модели в семействе моделей Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 — это новое поколение масштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных потребностей приложений."}, "qwen2.5:1.5b": {"description": "Qwen2.5 — это новое поколение масштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных потребностей приложений."}, "qwen2.5:72b": {"description": "Qwen2.5 — это новое поколение масштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных потребностей приложений."}, "qwen2:0.5b": {"description": "Qwen2 — это новое поколение крупномасштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных приложений."}, "qwen2:1.5b": {"description": "Qwen2 — это новое поколение крупномасштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных приложений."}, "qwen2:72b": {"description": "Qwen2 — это новое поколение крупномасштабной языковой модели от Alibaba, обеспечивающее отличные результаты для разнообразных приложений."}, "qwen3": {"description": "Qwen3 — это новое поколение масштабной языковой модели от Alibaba, которая поддерживает разнообразные потребности приложений с выдающимися показателями."}, "qwen3-0.6b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-1.7b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-14b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-235b-a22b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-235b-a22b-instruct-2507": {"description": "Открытая модель на базе Qwen3 в неразмышляющем режиме, с небольшими улучшениями в творческих способностях и безопасности по сравнению с предыдущей версией (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Открытая модель на базе Qwen3 в режиме размышления, с существенными улучшениями в логических способностях, универсальности, расширении знаний и творчестве по сравнению с предыдущей версией (Tongyi Qianwen 3-235B-A22B), предназначенная для сложных задач с интенсивным рассуждением."}, "qwen3-30b-a3b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-30b-a3b-instruct-2507": {"description": "По сравнению с предыдущей версией (Qwen3-30B-A3B) значительно улучшены общие способности на английском, китайском и других языках. Специальная оптимизация для субъективных и открытых задач, что заметно лучше соответствует предпочтениям пользователей и позволяет предоставлять более полезные ответы."}, "qwen3-30b-a3b-thinking-2507": {"description": "Открытая модель в режиме размышлений на базе Qwen3, которая по сравнению с предыдущей версией (Tongyi Qianwen 3-30B-A3B) значительно улучшила логические способности, общие навыки, знания и творческие возможности. Подходит для сложных задач с интенсивным рассуждением."}, "qwen3-32b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-4b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-8b": {"description": "Qwen3 — это новое поколение модели Qwen с значительно улучшенными возможностями, достигнувшими ведущих позиций в отрасли в области вывода, универсальности, агентов и многоязычности, а также поддерживающей переключение режимов размышления."}, "qwen3-coder-480b-a35b-instruct": {"description": "Открытая версия модели кода Tongyi Qianwen. Последняя модель qwen3-coder-480b-a35b-instruct основана на Qwen3 и обладает мощными возможностями Coding Agent, хорошо справляется с вызовом инструментов и взаимодействием с окружением, обеспечивая автономное программирование с выдающимися кодовыми и универсальными способностями."}, "qwen3-coder-plus": {"description": "Модель кода Tongyi Qianwen. Последняя серия моделей Qwen3-Coder-Plus основана на Qwen3 и обладает мощными возможностями Coding Agent, хорошо справляется с вызовом инструментов и взаимодействием с окружением, обеспечивая автономное программирование с выдающимися кодовыми и универсальными способностями."}, "qwq": {"description": "QwQ — это экспериментальная исследовательская модель, сосредоточенная на повышении возможностей вывода ИИ."}, "qwq-32b": {"description": "Модель вывода QwQ, обученная на модели Qwen2.5-32B, значительно улучшила свои способности вывода благодаря обучению с подкреплением. Основные показатели модели, такие как математический код и другие ключевые метрики (AIME 24/25, LiveCodeBench), а также некоторые общие показатели (IFEval, LiveBench и др.) достигли уровня DeepSeek-R1 в полной мере, при этом все показатели значительно превышают аналогичные показатели DeepSeek-R1-Distill-Qwen-32B, также основанной на Qwen2.5-32B."}, "qwq-32b-preview": {"description": "Модель QwQ — это экспериментальная исследовательская модель, разработанная командой Qwen, сосредоточенная на улучшении возможностей вывода ИИ."}, "qwq-plus": {"description": "Модель рассуждений QwQ, обученная на базе Qwen2.5, значительно улучшила способности к рассуждению с помощью обучения с подкреплением. Ключевые показатели модели по математике и коду (AIME 24/25, LiveCodeBench), а также некоторые общие показатели (IFEval, LiveBench и др.) достигли уровня полной версии DeepSeek-R1."}, "qwq_32b": {"description": "Модель вывода среднего размера из серии Qwen. В отличие от традиционных моделей, оптимизированных для инструкций, QwQ, обладая способностями к размышлению и выводу, может значительно повысить производительность в задачах, особенно при решении сложных задач."}, "r1-1776": {"description": "R1-1776 — это версия модели DeepSeek R1, прошедшая дообучение, которая предоставляет непроверенную, беспристрастную фактическую информацию."}, "solar-mini": {"description": "Solar Mini — это компактная LLM, которая превосходит GPT-3.5, обладает мощными многоязычными возможностями, поддерживает английский и корейский языки, предлагая эффективное и компактное решение."}, "solar-mini-ja": {"description": "Solar Mini (Ja) расширяет возможности Solar Mini, сосредотачиваясь на японском языке, при этом поддерживая высокую эффективность и выдающиеся результаты в использовании английского и корейского языков."}, "solar-pro": {"description": "Solar Pro — это высокоинтеллектуальная LLM, выпущенная Upstage, сосредоточенная на способности следовать инструкциям на одном GPU, с оценкой IFEval выше 80. В настоящее время поддерживает английский язык, официальная версия запланирована на ноябрь 2024 года, с расширением языковой поддержки и длины контекста."}, "sonar": {"description": "Легковесный продукт поиска на основе контекста, быстрее и дешевле, чем Sonar Pro."}, "sonar-deep-research": {"description": "Глубокое исследование проводит всесторонние экспертные исследования и сводит их в доступные и практичные отчеты."}, "sonar-pro": {"description": "Расширенный продукт поиска, поддерживающий контекст поиска, сложные запросы и последующие действия."}, "sonar-reasoning": {"description": "Новый API продукт, поддерживаемый моделью вывода DeepSeek."}, "sonar-reasoning-pro": {"description": "Новый API продукт, поддерживаемый моделью вывода DeepSeek."}, "stable-diffusion-3-medium": {"description": "Последняя крупная модель генерации изображений из текста от Stability AI. Эта версия сохраняет преимущества предыдущих поколений и значительно улучшает качество изображений, понимание текста и разнообразие стилей, позволяя точнее интерпретировать сложные естественные языковые подсказки и создавать более точные и разнообразные изображения."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large — это модель генерации изображений из текста с 800 миллионами параметров на основе мультимодального диффузионного трансформера (MMDiT), обладающая выдающимся качеством изображений и соответствием подсказкам. Поддерживает генерацию изображений с разрешением до 1 миллиона пикселей и эффективно работает на обычном потребительском оборудовании."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo — модель, основанная на stable-diffusion-3.5-large с применением технологии адверсариального диффузионного дистиллята (ADD), обеспечивающая более высокую скорость генерации."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 инициализирована весами контрольной точки stable-diffusion-v1.2 и дообучена на \"laion-aesthetics v2 5+\" с разрешением 512x512 в течение 595 тысяч шагов, с уменьшением текстовой кондиционированности на 10% для улучшения безклассификаторного направленного сэмплинга."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl значительно улучшена по сравнению с версией v1.5 и сопоставима по качеству с текущими передовыми открытыми моделями генерации изображений, такими как midjourney. Основные улучшения включают: увеличенный в 3 раза unet-бэкбон, добавление модуля уточнения для улучшения качества изображений и более эффективные методы обучения."}, "stable-diffusion-xl-base-1.0": {"description": "Крупная модель генерации изображений из текста, разработанная и открытая Stability AI, обладающая передовыми возможностями творческой генерации изображений. Отличается превосходным пониманием инструкций и поддержкой обратных подсказок для точного создания контента."}, "step-1-128k": {"description": "Балансирует производительность и стоимость, подходит для общих сценариев."}, "step-1-256k": {"description": "Обладает сверхдлинной способностью обработки контекста, особенно подходит для анализа длинных документов."}, "step-1-32k": {"description": "Поддерживает диалоги средней длины, подходит для различных приложений."}, "step-1-8k": {"description": "Маленькая модель, подходящая для легковесных задач."}, "step-1-flash": {"description": "Высокоскоростная модель, подходящая для реального времени диалогов."}, "step-1.5v-mini": {"description": "Эта модель обладает мощными возможностями понимания видео."}, "step-1o-turbo-vision": {"description": "Эта модель обладает мощными способностями к пониманию изображений и превосходит 1o в области математики и кода. Модель меньше, чем 1o, и выводит результаты быстрее."}, "step-1o-vision-32k": {"description": "Эта модель обладает мощными способностями к пониманию изображений. По сравнению с серией моделей step-1v, она имеет более высокую визуальную производительность."}, "step-1v-32k": {"description": "Поддерживает визуальный ввод, улучшая мультимодальный опыт взаимодействия."}, "step-1v-8k": {"description": "Небольшая визуальная модель, подходящая для базовых задач с текстом и изображениями."}, "step-1x-edit": {"description": "Модель, ориентированная на задачи редактирования изображений, способная изменять и улучшать изображения на основе предоставленных пользователем изображений и текстовых описаний. Поддерживает различные форматы ввода, включая текстовые описания и примеры изображений. Модель понимает намерения пользователя и генерирует соответствующие результаты редактирования."}, "step-1x-medium": {"description": "Модель с мощными возможностями генерации изображений, поддерживающая ввод в виде текстовых описаний. Обладает нативной поддержкой китайского языка, что позволяет лучше понимать и обрабатывать китайские текстовые описания, точнее улавливать семантику и преобразовывать её в визуальные характеристики для более точной генерации изображений. Модель способна создавать изображения высокого разрешения и качества, а также обладает некоторыми возможностями переноса стиля."}, "step-2-16k": {"description": "Поддерживает масштабные взаимодействия контекста, подходит для сложных диалоговых сценариев."}, "step-2-16k-exp": {"description": "Экспериментальная версия модели step-2, содержащая последние функции, находящаяся в процессе обновления. Не рекомендуется для использования в производственной среде."}, "step-2-mini": {"description": "Супербыстрая большая модель на основе новой самодельной архитектуры внимания MFA, достигающая аналогичных результатов, как step1, при очень низких затратах, одновременно обеспечивая более высокую пропускную способность и более быстрое время отклика. Способна обрабатывать общие задачи и обладает особыми навыками в кодировании."}, "step-2x-large": {"description": "Новая модель Step Star следующего поколения, ориентированная на генерацию изображений. Модель способна создавать высококачественные изображения на основе текстовых описаний пользователя. Новая версия обеспечивает более реалистичную текстуру изображений и улучшенные возможности генерации текста на китайском и английском языках."}, "step-r1-v-mini": {"description": "Эта модель является мощной моделью вывода с сильными способностями к пониманию изображений, способной обрабатывать информацию из изображений и текста, выводя текст после глубокого размышления. Эта модель демонстрирует выдающиеся результаты в области визуального вывода, а также обладает первоклассными способностями в математике, коде и текстовом выводе. Длина контекста составляет 100k."}, "taichu_llm": {"description": "Модель языка TaiChu обладает выдающимися способностями к пониманию языка, а также к созданию текстов, ответам на вопросы, программированию, математическим вычислениям, логическому выводу, анализу эмоций и резюмированию текстов. Инновационно сочетает предобучение на больших данных с богатством многопоточных знаний, постоянно совершенствуя алгоритмические технологии и поглощая новые знания о словах, структуре, грамматике и семантике из огромных объемов текстовых данных, обеспечивая пользователям более удобную информацию и услуги, а также более интеллектуальный опыт."}, "taichu_o1": {"description": "taichu_o1 — это новая генерация модели вывода, реализующая цепочку мышления, подобную человеческой, через мультимодальное взаимодействие и усиленное обучение, поддерживающая сложные решения и демонстрирующая путь мышления, который можно моделировать, при этом обеспечивая высокую точность вывода, подходит для анализа стратегий и глубоких размышлений."}, "taichu_vl": {"description": "Объединяет способности к пониманию изображений, переносу знаний и логическому выводу, демонстрируя выдающиеся результаты в области вопросов и ответов на основе текста и изображений."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct имеет 80 миллиардов параметров, при активации 13 миллиардов параметров может конкурировать с более крупными моделями, поддерживает гибридное рассуждение «быстрое мышление/медленное мышление»; стабильное понимание длинных текстов; проверено BFCL-v3 и τ-Bench, способности агента на передовом уровне; сочетает GQA и множество форматов квантизации для эффективного вывода."}, "text-embedding-3-large": {"description": "Самая мощная модель векторизации, подходящая для английских и неанглийских задач."}, "text-embedding-3-small": {"description": "Эффективная и экономичная новая генерация модели Embedding, подходящая для поиска знаний, приложений RAG и других сценариев."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 — это двуязычная (китайский и английский) языковая модель с открытыми весами на 32B, оптимизированная для генерации кода, вызовов функций и агентских задач. Она была предварительно обучена на 15T высококачественных данных и данных повторного рассуждения, а также дополнительно улучшена с помощью согласования человеческих предпочтений, отказного отбора и обучения с подкреплением. Эта модель демонстрирует отличные результаты в сложном рассуждении, генерации артефактов и задачах структурированного вывода, достигая производительности, сопоставимой с GPT-4o и DeepSeek-V3-0324 в нескольких бенчмарках."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 — это двуязычная (китайский и английский) языковая модель с открытыми весами на 32B, оптимизированная для генерации кода, вызовов функций и агентских задач. Она была предварительно обучена на 15T высококачественных данных и данных повторного рассуждения, а также дополнительно улучшена с помощью согласования человеческих предпочтений, отказного отбора и обучения с подкреплением. Эта модель демонстрирует отличные результаты в сложном рассуждении, генерации артефактов и задачах структурированного вывода, достигая производительности, сопоставимой с GPT-4o и DeepSeek-V3-0324 в нескольких бенчмарках."}, "thudm/glm-4-9b-chat": {"description": "Открытая версия последнего поколения предобученной модели GLM-4, выпущенной Zhizhu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 — это языковая модель с 9B параметрами из серии GLM-4, разработанная THUDM. GLM-4-9B-0414 использует те же стратегии усиленного обучения и выравнивания, что и ее более крупная модель с 32B, обеспечивая высокую производительность относительно своего размера, что делает ее подходящей для развертываний с ограниченными ресурсами, которые все еще требуют мощных возможностей понимания и генерации языка."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 — это улучшенная версия GLM-4-32B, созданная для глубокого математического, логического и кодового решения задач. Она использует расширенное обучение с подкреплением (специфичное для задач и основанное на общих парных предпочтениях) для повышения производительности в сложных многошаговых задачах. По сравнению с базовой моделью GLM-4-32B, Z1 значительно улучшила способности в структурированном рассуждении и формальных областях.\n\nЭта модель поддерживает принудительное выполнение шагов \"думать\" через инженерное проектирование подсказок и обеспечивает улучшенную согласованность для длинных форматов вывода. Она оптимизирована для рабочих процессов агентов и поддерживает длинный контекст (через YaRN), вызовы инструментов JSON и конфигурацию тонкой выборки для стабильного рассуждения. Идеально подходит для случаев, требующих вдумчивого, многошагового рассуждения или формального вывода."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 — это улучшенная версия GLM-4-32B, созданная для глубокого математического, логического и кодового решения задач. Она использует расширенное обучение с подкреплением (специфичное для задач и основанное на общих парных предпочтениях) для повышения производительности в сложных многошаговых задачах. По сравнению с базовой моделью GLM-4-32B, Z1 значительно улучшила способности в структурированном рассуждении и формальных областях.\n\nЭта модель поддерживает принудительное выполнение шагов \"думать\" через инженерное проектирование подсказок и обеспечивает улучшенную согласованность для длинных форматов вывода. Она оптимизирована для рабочих процессов агентов и поддерживает длинный контекст (через YaRN), вызовы инструментов JSON и конфигурацию тонкой выборки для стабильного рассуждения. Идеально подходит для случаев, требующих вдумчивого, многошагового рассуждения или формального вывода."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 — это языковая модель с 9B параметрами из серии GLM-4, разработанная THUDM. Она использует технологии, первоначально примененные в более крупной модели GLM-Z1, включая расширенное усиленное обучение, выравнивание парных рангов и обучение для задач, требующих интенсивного вывода, таких как математика, кодирование и логика. Несмотря на меньший размер, она демонстрирует высокую производительность в общих задачах вывода и превосходит многие открытые модели по уровню своих весов."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B — это глубокая модель вывода с 32B параметрами из серии GLM-4-Z1, оптимизированная для сложных открытых задач, требующих длительного размышления. Она основана на glm-4-32b-0414 и включает дополнительные этапы усиленного обучения и многоступенчатую стратегию выравнивания, вводя способность \"размышления\", предназначенную для имитации расширенной когнитивной обработки. Это включает итеративный вывод, многошаговый анализ и рабочие процессы, улучшенные инструментами, такими как поиск, извлечение и синтез с учетом цитат.\n\nЭта модель демонстрирует отличные результаты в исследовательском письме, сравнительном анализе и сложных вопросах. Она поддерживает вызовы функций для поиска и навигации (\"search\", \"click\", \"open\", \"finish\"), что позволяет использовать ее в агентских потоках. Поведение размышления формируется с помощью многоуровневого контроля, основанного на правилах вознаграждения и механизмах отложенного принятия решений, и ориентируется на такие глубокие исследовательские рамки, как внутренний стек выравнивания OpenAI. Этот вариант подходит для сценариев, требующих глубины, а не скорости."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera создана путем объединения DeepSeek-R1 и DeepSeek-V3 (0324), сочетая способности вывода R1 и улучшения эффективности токенов V3. Она основана на архитектуре DeepSeek-MoE Transformer и оптимизирована для общих задач генерации текста.\n\nЭта модель объединяет предобученные веса двух исходных моделей, чтобы сбалансировать производительность в задачах вывода, эффективности и выполнения инструкций. <PERSON>на выпущена под лицензией MIT и предназначена для исследовательских и коммерческих целей."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) обеспечивает повышенные вычислительные возможности благодаря эффективным стратегиям и архитектуре модели."}, "tts-1": {"description": "Последняя модель преобразования текста в речь, оптимизированная для скорости в реальных сценариях."}, "tts-1-hd": {"description": "Последняя модель преобразования текста в речь, оптимизированная для качества."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) подходит для детализированных командных задач, обеспечивая отличные возможности обработки языка."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet устанавливает новые отраслевые стандарты, превосходя модели конкурентов и Claude 3 Opus, демонстрируя отличные результаты в широком спектре оценок, при этом обладая скоростью и стоимостью наших моделей среднего уровня."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet — это самая быстрая модель следующего поколения от Anthropic. По сравнению с Claude 3 Haiku, Claude 3.7 Sonnet продемонстрировала улучшения во всех навыках и превзошла предыдущую крупнейшую модель Claude 3 Opus по многим интеллектуальным бенчмаркам."}, "v0-1.0-md": {"description": "Модель v0-1.0-md — это устаревшая модель, предоставляемая через API v0"}, "v0-1.5-lg": {"description": "Модель v0-1.5-lg подходит для сложных мыслительных или логических задач"}, "v0-1.5-md": {"description": "Модель v0-1.5-md подходит для повседневных задач и генерации пользовательского интерфейса (UI)"}, "wan2.2-t2i-flash": {"description": "Экспресс-версия Wanxiang 2.2 — самая новая модель на данный момент. Полное обновление в креативности, стабильности и реалистичности, высокая скорость генерации и отличное соотношение цена-качество."}, "wan2.2-t2i-plus": {"description": "Профессиональная версия Wanxiang 2.2 — самая новая модель на данный момент. Полное обновление в креативности, стабильности и реалистичности, с более детальной проработкой изображений."}, "wanx-v1": {"description": "Базовая модель генерации изображений из текста. Соответствует универсальной модели версии 1.0 на официальном сайте Tongyi Wanxiang."}, "wanx2.0-t2i-turbo": {"description": "Специализирована на реалистичных портретах, средняя скорость и низкая стоимость. Соответствует экспресс-модели версии 2.0 на официальном сайте Tongyi Wanxiang."}, "wanx2.1-t2i-plus": {"description": "Полностью обновлённая версия с более детальной проработкой изображений, немного более медленная скорость. Соответствует профессиональной модели версии 2.1 на официальном сайте Tongyi Wanxiang."}, "wanx2.1-t2i-turbo": {"description": "Полностью обновлённая версия с высокой скоростью генерации, всесторонним качеством и отличным соотношением цена-качество. Соответствует экспресс-модели версии 2.1 на официальном сайте Tongyi Wanxiang."}, "whisper-1": {"description": "Универсальная модель распознавания речи, поддерживающая многоязычное распознавание речи, перевод речи и идентификацию языка."}, "wizardlm2": {"description": "WizardLM 2 — это языковая модель, предоставляемая Microsoft AI, которая особенно хорошо проявляет себя в сложных диалогах, многоязычных задачах, выводе и интеллектуальных помощниках."}, "wizardlm2:8x22b": {"description": "WizardLM 2 — это языковая модель, предоставляемая Microsoft AI, которая особенно хорошо проявляет себя в сложных диалогах, многоязычных задачах, выводе и интеллектуальных помощниках."}, "x1": {"description": "Модель Spark X1 будет дополнительно обновлена, и на основе уже существующих лидерских позиций в математических задачах, достигнет сопоставимых результатов в общих задачах, таких как рассуждение, генерация текста и понимание языка, с OpenAI o1 и DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 — это обновленная версия Yi. Она использует 500B токенов высококачественного корпуса данных для продолжения предварительной тренировки на основе Yi и微调在3M个多样化的微调样本上。"}, "yi-large": {"description": "Совершенно новая модель с триллионом параметров, обеспечивающая выдающиеся возможности для вопросов и ответов, а также генерации текста."}, "yi-large-fc": {"description": "На основе модели yi-large поддерживает и усиливает возможности вызова инструментов, подходит для различных бизнес-сценариев, требующих создания агентов или рабочих процессов."}, "yi-large-preview": {"description": "Начальная версия, рекомендуется использовать yi-large (новую версию)."}, "yi-large-rag": {"description": "Высококлассный сервис на основе модели yi-large, объединяющий технологии поиска и генерации для предоставления точных ответов и услуг по поиску информации в реальном времени."}, "yi-large-turbo": {"description": "Высокая стоимость и выдающаяся производительность. Балансировка высокой точности на основе производительности, скорости вывода и затрат."}, "yi-lightning": {"description": "Новая высокопроизводительная модель, обеспечивающая высокое качество вывода при значительно повышенной скорости вывода."}, "yi-lightning-lite": {"description": "Упрощенная версия, рекомендуется использовать yi-lightning."}, "yi-medium": {"description": "Модель среднего размера с улучшенной настройкой, сбалансированная по возможностям и стоимости. Глубокая оптимизация способности следовать указаниям."}, "yi-medium-200k": {"description": "200K сверхдлинное окно контекста, обеспечивающее глубокое понимание и генерацию длинных текстов."}, "yi-spark": {"description": "Маленькая и мощная, легковесная и быстрая модель. Обеспечивает улучшенные математические вычисления и возможности написания кода."}, "yi-vision": {"description": "Модель для сложных визуальных задач, обеспечивающая высокую производительность в понимании и анализе изображений."}, "yi-vision-v2": {"description": "Модель для сложных визуальных задач, обеспечивающая высокопроизводительное понимание и анализ на основе нескольких изображений."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 — базовая модель, специально созданная для приложений с агентами, использующая архитектуру смешанных экспертов (Mixture-of-Experts). Модель глубоко оптимизирована для вызова инструментов, веб-браузинга, программной инженерии и фронтенд-разработки, поддерживает бесшовную интеграцию с кодовыми агентами, такими как Claude Code и Roo Code. GLM-4.5 использует смешанный режим вывода, адаптируясь к сложным рассуждениям и повседневным задачам."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air — базовая модель, специально созданная для приложений с агентами, использующая архитектуру смешанных экспертов (Mixture-of-Experts). Модель глубоко оптимизирована для вызова инструментов, веб-браузинга, программной инженерии и фронтенд-разработки, поддерживает бесшовную интеграцию с кодовыми агентами, такими как Claude Code и Roo Code. GLM-4.5 использует смешанный режим вывода, адаптируясь к сложным рассуждениям и повседневным задачам."}}