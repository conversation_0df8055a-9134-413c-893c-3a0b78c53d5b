{"azure": {"azureApiVersion": {"desc": "Версия API Azure, следующая формату ГГГГ-ММ-ДД, см. [последнюю версию](https://learn.microsoft.com/ru-ru/azure/ai-services/openai/reference#chat-completions)", "fetch": "Получить список", "title": "Версия Azure API"}, "empty": "Введите идентификатор модели, чтобы добавить первую модель", "endpoint": {"desc": "Можно найти в разделе «Ключи и конечные точки» при проверке ресурса в портале Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Адрес Azure API"}, "modelListPlaceholder": "Выберите или добавьте модель OpenAI, которую вы развернули", "title": "Azure OpenAI", "token": {"desc": "Можно найти в разделе «Ключи и конечные точки» при проверке ресурса в портале Azure. Можно использовать KEY1 или KEY2", "placeholder": "Azure API Key", "title": "API Key"}}, "azureai": {"azureApiVersion": {"desc": "Версия API Azure, формат YYYY-MM-DD, смотрите [последнюю версию](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "Получить список", "title": "Версия API Azure"}, "endpoint": {"desc": "Найдите конечную точку вывода модели Azure AI в обзоре проекта Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Конечная точка Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Найдите API-ключ в обзоре проекта Azure AI", "placeholder": "К<PERSON>юч Azure", "title": "<PERSON><PERSON><PERSON><PERSON>"}}, "bedrock": {"accessKeyId": {"desc": "Введите ваш AWS Access Key ID", "placeholder": "AWS Access Key Id", "title": "AWS Access Key ID"}, "checker": {"desc": "Проверить правильность заполнения AccessKeyId/SecretAccessKey"}, "region": {"desc": "Введите ваш AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Введите ваш AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Если вы используете AWS SSO/STS, введите ваш AWS Session Token", "placeholder": "AWS Session Token", "title": "AWS Session Token (необязательно)"}, "title": "Bedrock", "unlock": {"customRegion": "Пользовательский регион обслуживания", "customSessionToken": "Пользовательский токен сессии", "description": "Введите свой ключ доступа AWS AccessKeyId / SecretAccessKey, чтобы начать сеанс. Приложение не будет сохранять вашу конфигурацию аутентификации", "imageGenerationDescription": "Введите ваш AWS AccessKeyId / SecretAccessKey, чтобы начать генерацию. Приложение не будет сохранять ваши данные аутентификации", "title": "Использовать пользовательскую информацию аутентификации Bedrock"}}, "cloudflare": {"apiKey": {"desc": "Пожалуйста, заполните Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Введите ID аккаунта Cloudflare или адрес API по умолчанию", "placeholder": "ID аккаунта Cloudflare / адрес API по умолчанию", "title": "ID аккаунта Cloudflare / адрес API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Пожалуйста, введите ваш API Key", "title": "API Key"}, "basicTitle": "Основная информация", "configTitle": "Конфигурационная информация", "confirm": "Создать", "createSuccess": "Создание успешно", "description": {"placeholder": "Описание провайдера (необязательно)", "title": "Описание провайдера"}, "id": {"desc": "Уникальный идентификатор для поставщика услуг, который нельзя изменить после создания", "format": "Может содержать только цифры, строчные буквы, дефисы (-) и подчеркивания (_) ", "placeholder": "Рекомендуется использовать строчные буквы, например, openai, после создания изменить нельзя", "required": "Пожалуйста, введите ID провайдера", "title": "ID провайдера"}, "logo": {"required": "Пожалуйста, загрузите правильный логотип провайдера", "title": "Логотип провайдера"}, "name": {"placeholder": "Пожалуйста, введите отображаемое имя провайдера", "required": "Пожалуйста, введите имя провайдера", "title": "Имя провайдера"}, "proxyUrl": {"required": "Пожалуйста, введите адрес прокси", "title": "Адрес прокси"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Пожалуйста, выберите тип SDK", "title": "Формат запроса"}, "title": "Создание пользовательского AI провайдера"}, "github": {"personalAccessToken": {"desc": "Введите ваш персональный токен доступа GitHub (PAT), нажмите [здесь](https://github.com/settings/tokens), чтобы создать его", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "Введите ваш токен <PERSON>, нажмите [здесь](https://huggingface.co/settings/tokens) для создания", "placeholder": "hf_xxxxxxxxx", "title": "Т<PERSON><PERSON><PERSON>н <PERSON>"}}, "list": {"title": {"disabled": "Поставщик не активирован", "enabled": "Поставщик активирован"}}, "menu": {"addCustomProvider": "Добавить пользовательского провайдера", "all": "Все", "list": {"disabled": "Не активирован", "enabled": "Ак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notFound": "Результаты поиска не найдены", "searchProviders": "Поиск провайдеров...", "sort": "Пользовательская сортировка"}, "ollama": {"checker": {"desc": "Проверить правильность адреса прокси", "title": "Проверка связности"}, "customModelName": {"desc": "Добавить кастомные модели, разделяя их через запятую (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Название кастомных моделей"}, "download": {"desc": "Ollama загружает эту модель, пожалуйста, старайтесь не закрывать эту страницу. При повторной загрузке процесс будет продолжен с места остановки", "failed": "Не удалось загрузить модель, пожалуйста, проверьте сеть или настройки Ollama и попробуйте снова", "remainingTime": "Оставшееся время", "speed": "Скорость загрузки", "title": "Загрузка модели {{model}} "}, "endpoint": {"desc": "Должен содержать http(s)://, если локально не указано иное, можно оставить пустым", "title": "Адрес прокси-интерфейса"}, "title": "Ollama", "unlock": {"cancel": "Cancel Download", "confirm": "Download", "description": "Enter your Ollama model tag to continue the session", "downloaded": "{{completed}} / {{total}}", "starting": "Starting download...", "title": "Download specified Ollama model"}}, "providerModels": {"config": {"aesGcm": "Ваши ключи и адрес прокси будут зашифрованы с использованием <1>AES-GCM</1>", "apiKey": {"desc": "Пожалуйста, введите ваш {{name}} API Key", "descWithUrl": "Пожалуйста, введите ваш {{name}} API ключ, <3>получить здесь</3>", "placeholder": "{{name}} API Key", "title": "API Key"}, "baseURL": {"desc": "Должен содержать http(s)://", "invalid": "Пожалуйста, введите действительный URL", "placeholder": "https://your-proxy-url.com/v1", "title": "API адрес прокси"}, "checker": {"button": "Проверить", "desc": "Проверьте, правильно ли заполнены API Key и адрес прокси", "pass": "Проверка пройдена", "title": "Проверка соединения"}, "fetchOnClient": {"desc": "Клиентский режим запросов будет инициировать сессии напрямую из браузера, что может ускорить время отклика", "title": "Использовать клиентский режим запросов"}, "helpDoc": "Документация по настройке", "responsesApi": {"desc": "Использует новый формат запросов OpenAI, открывая доступ к таким продвинутым функциям, как цепочки мышления", "title": "Использование спецификации Responses API"}, "waitingForMore": "Больше моделей находится в <1>планировании подключения</1>, ожидайте с нетерпением"}, "createNew": {"title": "Создание пользовательской AI модели"}, "item": {"config": "Настроить модель", "customModelCards": {"addNew": "Создать и добавить модель {{id}}", "confirmDelete": "Вы собираетесь удалить эту пользовательскую модель, после удаления восстановить ее будет невозможно, будьте осторожны."}, "delete": {"confirm": "Подтвердите удаление модели {{displayName}}?", "success": "Удаление успешно", "title": "Удалить модель"}, "modelConfig": {"azureDeployName": {"extra": "Поле, запрашиваемое в Azure OpenAI", "placeholder": "Пожалуйста, введите имя развертывания модели в Azure", "title": "Имя развертывания модели"}, "deployName": {"extra": "Это поле будет использоваться как идентификатор модели при отправке запроса", "placeholder": "Введите фактическое имя или id развертывания модели", "title": "Имя развертывания модели"}, "displayName": {"placeholder": "Пожалуйста, введите отображаемое имя модели, например, ChatGPT, GPT-4 и т.д.", "title": "Отображаемое имя модели"}, "files": {"extra": "Текущая реализация загрузки файлов является лишь хакерским решением, предназначенным только для самостоятельного тестирования. Полные возможности загрузки файлов ожидайте в будущем.", "title": "Поддержка загрузки файлов"}, "functionCall": {"extra": "Эта настройка позволит модели использовать инструменты, что даст возможность добавлять плагины инструментов. Однако возможность фактического использования инструментов полностью зависит от самой модели, пожалуйста, протестируйте их работоспособность самостоятельно", "title": "Поддержка использования инструментов"}, "id": {"extra": "После создания изменить нельзя, будет использоваться как идентификатор модели при вызове AI", "placeholder": "Введите идентификатор модели, например, gpt-4o или claude-3.5-sonnet", "title": "ID модели"}, "modalTitle": "Настройка пользовательской модели", "reasoning": {"extra": "Эта настройка активирует возможность глубокого мышления модели, конкретный эффект полностью зависит от самой модели, пожалуйста, протестируйте, обладает ли модель доступной способностью к глубокому мышлению", "title": "Поддержка глубокого мышления"}, "tokens": {"extra": "Установите максимальное количество токенов, поддерживаемое моделью", "title": "Максимальное окно контекста", "unlimited": "Без ограничений"}, "vision": {"extra": "Эта настройка только активирует возможность загрузки изображений в приложении, поддержка распознавания полностью зависит от самой модели, пожалуйста, протестируйте доступность визуального распознавания этой модели.", "title": "Поддержка визуального распознавания"}}, "pricing": {"image": "${{amount}}/изображение", "inputCharts": "${{amount}}/M символов", "inputMinutes": "${{amount}}/минуты", "inputTokens": "Ввод ${{amount}}/М", "outputTokens": "Вывод ${{amount}}/М"}, "releasedAt": "Выпущено {{releasedAt}}"}, "list": {"addNew": "Добавить модель", "disabled": "Не активирован", "disabledActions": {"showMore": "Показать все"}, "empty": {"desc": "Пожалуйста, создайте пользовательскую модель или загрузите модель, чтобы начать использовать.", "title": "Нет доступных моделей"}, "enabled": "Ак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enabledActions": {"disableAll": "Отключить все", "enableAll": "Включить все", "sort": "Сортировка моделей по индивидуальному порядку"}, "enabledEmpty": "Нет активированных моделей, пожалуйста, активируйте понравившиеся модели из списка ниже~", "fetcher": {"clear": "Очистить полученные модели", "fetch": "Получить список моделей", "fetching": "Получение списка моделей...", "latestTime": "Последнее обновление: {{time}}", "noLatestTime": "Список еще не получен"}, "resetAll": {"conform": "Вы уверены, что хотите сбросить все изменения текущей модели? После сброса список текущих моделей вернется к состоянию по умолчанию", "success": "Сброс выполнен успешно", "title": "Сбросить все изменения"}, "search": "Поиск моделей...", "searchResult": "Найдено {{count}} моделей", "title": "Список моделей", "total": "Всего доступно {{count}} моделей"}, "searchNotFound": "Результаты поиска не найдены"}, "sortModal": {"success": "Сортировка обновлена успешно", "title": "Пользовательская сортировка", "update": "Обновить"}, "updateAiProvider": {"confirmDelete": "Вы собираетесь удалить этого AI провайдера, после удаления его будет невозможно восстановить, подтвердите, хотите ли вы удалить?", "deleteSuccess": "Удаление успешно", "tooltip": "Обновить базовую конфигурацию провайдера", "updateSuccess": "Обновление успешно"}, "updateCustomAiProvider": {"title": "Обновить настройки поставщика пользовательского ИИ"}, "vertexai": {"apiKey": {"desc": "Введите ваши ключи Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Ключи Vertex AI"}}, "zeroone": {"title": "01.AI Цифровая Вселенная"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}