{"ai21": {"description": "AI21 Labs создает базовые модели и системы искусственного интеллекта для бизнеса, ускоряя внедрение генеративного ИИ в производстве."}, "ai360": {"description": "360 AI — это платформа AI-моделей и услуг, запущенная компанией 360, предлагающая множество передовых моделей обработки естественного языка, включая 360GPT2 Pro, 360GPT Pro, 360GPT Turbo и 360GPT Turbo Responsibility 8K. Эти модели сочетают в себе масштабные параметры и мультимодальные возможности, широко применяются в генерации текста, семантическом понимании, диалоговых системах и генерации кода. Благодаря гибкой ценовой политике 360 AI удовлетворяет разнообразные потребности пользователей, поддерживает интеграцию разработчиков и способствует инновациям и развитию интеллектуальных приложений."}, "aihubmix": {"description": "AiHubMix предоставляет доступ к различным AI-моделям через единый API-интерфейс."}, "anthropic": {"description": "Anthropic — это компания, сосредоточенная на исследованиях и разработке искусственного интеллекта, предлагающая ряд передовых языковых моделей, таких как Claude 3.5 Sonnet, Claude 3 Sonnet, Claude 3 Opus и Claude 3 Haiku. Эти модели достигают идеального баланса между интеллектом, скоростью и стоимостью, подходя для различных сценариев применения, от корпоративных рабочих нагрузок до быстрого реагирования. Claude 3.5 Sonnet, как их последняя модель, показала отличные результаты в нескольких оценках, сохраняя при этом высокую стоимость-эффективность."}, "azure": {"description": "Azure предлагает множество передовых AI-моделей, включая GPT-3.5 и новейшую серию GPT-4, поддерживающих различные типы данных и сложные задачи, с акцентом на безопасность, надежность и устойчивые AI-решения."}, "azureai": {"description": "Azure предлагает множество современных AI моделей, включая GPT-3.5 и последнюю серию GPT-4, поддерживающих различные типы данных и сложные задачи, нацеленных на безопасные, надежные и устойчивые AI решения."}, "baichuan": {"description": "Baichuan Intelligent — это компания, сосредоточенная на разработке больших моделей искусственного интеллекта, чьи модели показывают выдающиеся результаты в области китайских задач, таких как знаниевые энциклопедии, обработка длинных текстов и генерация контента, превосходя зарубежные модели. Baichuan Intelligent также обладает передовыми мультимодальными возможностями и показала отличные результаты в нескольких авторитетных оценках. Их модели включают Baichuan 4, Baichuan 3 Turbo и Baichuan 3 Turbo 128k, оптимизированные для различных сценариев применения, предлагая высокоэффективные решения."}, "bedrock": {"description": "Bedrock — это сервис, предоставляемый Amazon AWS, сосредоточенный на предоставлении предприятиям передовых AI-языковых и визуальных моделей. Его семейство моделей включает серию <PERSON> от Anthrop<PERSON>, серию Llama 3.1 от Meta и другие, охватывающие широкий спектр от легковесных до высокопроизводительных решений, поддерживающих текстовую генерацию, диалоги, обработку изображений и другие задачи, подходящие для предприятий различного масштаба и потребностей."}, "cloudflare": {"description": "Запуск моделей машинного обучения на базе серверов GPU в глобальной сети Cloudflare."}, "cohere": {"description": "Cohere предлагает вам самые современные многоязычные модели, продвинутые функции поиска и AI-рабочее пространство, специально разработанное для современных предприятий — всё это интегрировано в одной безопасной платформе."}, "deepseek": {"description": "DeepSeek — это компания, сосредоточенная на исследованиях и применении технологий искусственного интеллекта, ее последняя модель DeepSeek-V2.5 объединяет возможности общего диалога и обработки кода, достигнув значительных улучшений в области согласования с человеческими предпочтениями, написания текстов и выполнения инструкций."}, "fal": {"description": "Генеративная медиа-платформа для разработчиков"}, "fireworksai": {"description": "Fireworks AI — это ведущий поставщик высококлассных языковых моделей, сосредоточенный на вызовах функций и мультимодальной обработке. Их последняя модель Firefunction V2 основана на Llama-3 и оптимизирована для вызовов функций, диалогов и выполнения инструкций. Модель визуального языка FireLLaVA-13B поддерживает смешанный ввод изображений и текста. Другие заметные модели включают серию Llama и серию Mixtral, предлагая эффективную поддержку многоязычных инструкций и генерации."}, "giteeai": {"description": "API Serverless от Gitee AI предоставляет разработчикам AI сервисы API для рассуждений о больших моделях с открытым доступом."}, "github": {"description": "С помощью моделей GitHub разработчики могут стать инженерами ИИ и создавать с использованием ведущих моделей ИИ в отрасли."}, "google": {"description": "Серия Gemini от Google является самой передовой и универсальной AI-моделью, разработанной Google DeepMind, специально созданной для мультимодальной обработки, поддерживающей бесшовное понимание и обработку текста, кода, изображений, аудио и видео. Подходит для различных сред, от дата-центров до мобильных устройств, значительно повышая эффективность и универсальность AI-моделей."}, "groq": {"description": "Инженерный движок LPU от Groq показал выдающиеся результаты в последних независимых бенчмарках больших языковых моделей (LLM), переопределяя стандарты AI-решений благодаря своей удивительной скорости и эффективности. Groq представляет собой образец мгновенной скорости вывода, демонстрируя хорошие результаты в облачных развертываниях."}, "higress": {"description": "Higress — это облачный API шлюз, который был разработан внутри Alibaba для решения проблем, связанных с перезагрузкой Tengine, негативно влияющей на долгосрочные соединения, а также недостаточной способностью балансировки нагрузки для gRPC/Dubbo."}, "huggingface": {"description": "API для инференса HuggingFace предоставляет быстрый и бесплатный способ исследовать тысячи моделей для различных задач. Независимо от того, разрабатываете ли вы прототип для нового приложения или пробуете возможности машинного обучения, этот API обеспечивает мгновенный доступ к высокопроизводительным моделям в различных областях."}, "hunyuan": {"description": "Большая языковая модель, разработанная Tencent, обладающая мощными способностями к созданию текстов на китайском языке, логическим рассуждениям в сложных контекстах и надежным выполнением задач."}, "infiniai": {"description": "Предоставляет разработчикам приложений высокопроизводительные, удобные в использовании и надежные услуги больших моделей, охватывающие весь процесс от разработки больших моделей до их внедрения в качестве сервиса."}, "internlm": {"description": "Открытая организация, занимающаяся исследованием и разработкой инструментов для больших моделей. Предоставляет всем разработчикам ИИ эффективную и удобную открытую платформу, позволяя получить доступ к самым современным технологиям больших моделей и алгоритмов."}, "jina": {"description": "Jina AI была основана в 2020 году и является ведущей компанией в области поискового AI. Наша платформа поискового базиса включает векторные модели, реорганизаторы и небольшие языковые модели, которые помогают предприятиям создавать надежные и высококачественные генеративные AI и мультимодальные поисковые приложения."}, "lmstudio": {"description": "LM Studio — это настольное приложение для разработки и экспериментов с LLM на вашем компьютере."}, "minimax": {"description": "MiniMax — это компания по разработке универсального искусственного интеллекта, основанная в 2021 году, стремящаяся к совместному созданию интеллекта с пользователями. MiniMax самостоятельно разработала универсальные большие модели различных модальностей, включая текстовые модели с триллионом параметров, модели речи и модели изображений. Также были запущены приложения, такие как Conch AI."}, "mistral": {"description": "Mistral предлагает передовые универсальные, специализированные и исследовательские модели, широко применяемые в сложном выводе, многоязычных задачах, генерации кода и других областях. Через интерфейсы вызова функций пользователи могут интегрировать пользовательские функции для реализации конкретных приложений."}, "modelscope": {"description": "ModelScope — это платформа моделей как услуги, запущенная Alibaba Cloud, предоставляющая широкий спектр AI-моделей и сервисов вывода."}, "moonshot": {"description": "Moonshot — это открытая платформа, запущенная Beijing Dark Side Technology Co., Ltd., предлагающая различные модели обработки естественного языка, охватывающие широкий спектр областей применения, включая, но не ограничиваясь, создание контента, академические исследования, интеллектуальные рекомендации, медицинскую диагностику и т. д., поддерживающая обработку длинных текстов и сложные задачи генерации."}, "novita": {"description": "Novita AI — это платформа, предлагающая API-сервисы для различных больших языковых моделей и генерации изображений AI, гибкая, надежная и экономически эффективная. Она поддерживает новейшие открытые модели, такие как Llama3, Mistral и предоставляет комплексные, удобные для пользователя и автоматически масштабируемые API-решения для разработки генеративных AI-приложений, подходящие для быстрого роста AI-стартапов."}, "nvidia": {"description": "NVIDIA NIM™ предоставляет контейнеры для самообслуживания GPU-ускоренного вывода микросервисов, поддерживающих развертывание предобученных и пользовательских AI моделей в облаке, центрах обработки данных, на персональных компьютерах RTX™ AI и рабочих станциях."}, "ollama": {"description": "Модели, предлагаемые Ollama, охватывают широкий спектр областей, включая генерацию кода, математические вычисления, многоязыковую обработку и диалоговое взаимодействие, поддерживая разнообразные потребности в развертывании на уровне предприятий и локализации."}, "openai": {"description": "OpenAI является ведущим мировым исследовательским институтом в области искусственного интеллекта, чьи модели, такие как серия GPT, продвигают границы обработки естественного языка. OpenAI стремится изменить множество отраслей с помощью инновационных и эффективных AI-решений. Их продукты обладают выдающимися характеристиками и экономичностью, широко используются в исследованиях, бизнесе и инновационных приложениях."}, "openrouter": {"description": "OpenRouter — это сервисная платформа, предлагающая интерфейсы для различных передовых больших моделей, поддерживающая OpenAI, Anthropic, LLaMA и другие, подходящая для разнообразных потребностей в разработке и применении. Пользователи могут гибко выбирать оптимальные модели и цены в зависимости от своих потребностей, что способствует улучшению AI-опыта."}, "perplexity": {"description": "Perplexity — это ведущий поставщик моделей генерации диалогов, предлагающий множество передовых моделей Llama 3.1, поддерживающих онлайн и оффлайн приложения, особенно подходящих для сложных задач обработки естественного языка."}, "ppio": {"description": "PPIO Paiouyun предоставляет стабильные и высокоэффективные API-сервисы для открытых моделей, поддерживающие всю серию DeepSeek, <PERSON><PERSON><PERSON>, <PERSON>wen и другие ведущие модели в отрасли."}, "qiniu": {"description": "Qiniu — это ведущий поставщик облачных услуг, предлагающий API для больших моделей AI, включая DeepSeek, Llama и Qwen, с гибкими вариантами для создания и применения приложений AI."}, "qwen": {"description": "Qwen — это сверхбольшая языковая модель, разработанная Alibaba Cloud, обладающая мощными возможностями понимания и генерации естественного языка. Она может отвечать на различные вопросы, создавать текстовый контент, выражать мнения и писать код, играя важную роль в различных областях."}, "sambanova": {"description": "SambaNova Cloud позволяет разработчикам легко использовать лучшие открытые модели и наслаждаться самой быстрой скоростью вывода."}, "search1api": {"description": "Search1API предоставляет доступ к серии моделей DeepSeek, которые могут подключаться к сети по мере необходимости, включая стандартную и быструю версии, поддерживающие выбор моделей с различными параметрами."}, "sensenova": {"description": "SenseNova, опираясь на мощную инфраструктуру SenseTime, предлагает эффективные и удобные услуги полного стека больших моделей."}, "siliconcloud": {"description": "SiliconFlow стремится ускорить AGI, чтобы принести пользу человечеству, повышая эффективность масштабного AI с помощью простого и экономичного стека GenAI."}, "spark": {"description": "Стартап iFlytek Starfire предоставляет мощные AI-возможности в различных областях и языках, используя передовые технологии обработки естественного языка для создания инновационных приложений, подходящих для умных устройств, умного здравоохранения, умных финансов и других вертикальных сценариев."}, "stepfun": {"description": "StepFun — это большая модель, обладающая передовыми мультимодальными и сложными выводными возможностями, поддерживающая понимание сверхдлинных текстов и мощные функции автономного поиска."}, "taichu": {"description": "Новая генерация мультимодальных больших моделей, разработанная Институтом автоматизации Китайской академии наук и Институтом искусственного интеллекта Уханя, поддерживает многораундные вопросы и ответы, создание текстов, генерацию изображений, 3D-понимание, анализ сигналов и другие комплексные задачи, обладая более сильными когнитивными, понимательными и творческими способностями, предлагая новый опыт взаимодействия."}, "tencentcloud": {"description": "Атомные возможности движка знаний (LLM Knowledge Engine Atomic Power) основаны на разработке движка знаний и представляют собой полную цепочку возможностей для вопросов и ответов, ориентированную на предприятия и разработчиков. Вы можете создать собственный сервис модели, используя различные атомные возможности, комбинируя такие услуги, как анализ документов, разбиение, встраивание, многократное переписывание и другие, чтобы настроить уникальный AI-бизнес для вашей компании."}, "togetherai": {"description": "Together AI стремится достичь передовых результатов с помощью инновационных AI-моделей, предлагая широкий спектр возможностей для настройки, включая поддержку быстрого масштабирования и интуитивно понятные процессы развертывания, чтобы удовлетворить различные потребности бизнеса."}, "upstage": {"description": "Upstage сосредоточен на разработке AI-моделей для различных бизнес-потребностей, включая Solar LLM и документальный AI, с целью достижения искусственного общего интеллекта (AGI). Создавайте простые диалоговые агенты через Chat API и поддерживайте вызовы функций, переводы, встраивания и приложения в конкретных областях."}, "v0": {"description": "v0 — это помощник для парного программирования, который позволяет вам описывать идеи на естественном языке, а он генерирует код и пользовательский интерфейс (UI) для вашего проекта"}, "vertexai": {"description": "Серия Gemini от Google — это самые современные и универсальные AI-модели, разработанные Google DeepMind, специально созданные для мультимодальности, поддерживающие бесшовное понимание и обработку текста, кода, изображений, аудио и видео. Подходят для различных сред, от дата-центров до мобильных устройств, значительно повышая эффективность и универсальность применения AI-моделей."}, "vllm": {"description": "vLLM — это быстрая и простая в использовании библиотека для вывода и обслуживания LLM."}, "volcengine": {"description": "Платформа разработки сервисов больших моделей, запущенная ByteDance, предлагает функционально богатые, безопасные и конкурентоспособные по цене услуги вызова моделей, а также предоставляет полные функции от данных моделей, тонкой настройки, вывода до оценки, обеспечивая всестороннюю поддержку разработки ваших AI приложений."}, "wenxin": {"description": "Корпоративная платформа для разработки и обслуживания крупных моделей и нативных приложений ИИ, предлагающая самый полный и удобный инструментарий для разработки генеративных моделей искусственного интеллекта и полного процесса разработки приложений."}, "xai": {"description": "xAI — это компания, занимающаяся разработкой искусственного интеллекта для ускорения научных открытий человечества. Наша миссия — способствовать общему пониманию Вселенной."}, "xinference": {"description": "Xorbits Inference (Xinference) — это открытая платформа, предназначенная для упрощения запуска и интеграции различных моделей искусственного интеллекта. С помощью Xinference вы можете использовать любые открытые LLM, модели эмбеддингов и мультимодальные модели для выполнения логического вывода в облаке или локальной среде, а также создавать мощные приложения на основе ИИ."}, "zeroone": {"description": "01.AI сосредоточен на технологиях искусственного интеллекта 2.0, активно продвигая инновации и применение \"человек + искусственный интеллект\", используя мощные модели и передовые AI-технологии для повышения производительности человека и реализации технологического потенциала."}, "zhipu": {"description": "Zhipu AI предлагает открытую платформу для мультимодальных и языковых моделей, поддерживающую широкий спектр AI-приложений, включая обработку текста, понимание изображений и помощь в программировании."}}