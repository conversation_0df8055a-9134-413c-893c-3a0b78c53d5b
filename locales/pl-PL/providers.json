{"ai21": {"description": "AI21 Labs buduje podstawowe modele i systemy sztucznej inteligencji dla przedsiębiorstw, przyspieszając zastosowanie generatywnej sztucznej inteligencji w produkcji."}, "ai360": {"description": "360 AI to platforma modeli i usług AI wprowadzona przez firmę 360, oferująca różnorodne zaawansowane modele przetwarzania języka naturalnego, w tym 360GPT2 Pro, 360GPT Pro, 360GPT Turbo i 360GPT Turbo Responsibility 8K. Modele te łączą dużą liczbę parametrów z multimodalnymi zdolnościami, szeroko stosowanymi w generowaniu tekstu, rozumieniu semantycznym, systemach dialogowych i generowaniu kodu. Dzięki elastycznej strategii cenowej, 360 AI zaspokaja zróżnicowane potrzeby użytkowników, wspierając integrację przez deweloperów, co przyczynia się do innowacji i rozwoju aplikacji inteligentnych."}, "aihubmix": {"description": "AiHubMix zapewnia dostęp do różnych modeli AI za pośrednictwem zunifikowanego interfejsu API."}, "anthropic": {"description": "Anthropic to firma skoncentrowana na badaniach i rozwoju sztucznej inteligencji, oferująca szereg zaawansowanych modeli językowych, ta<PERSON><PERSON> jak <PERSON> 3.5 Sonnet, <PERSON> 3 Sonnet, Claude 3 Opus i Claude 3 Haiku. Modele te osiągają idealną równowagę między inteligencją, s<PERSON><PERSON><PERSON>ścią a kosztami, nadając się do różnych zastosowań, od obciążeń na poziomie przedsiębiorstw po szybkie odpowiedzi. Claude 3.5 Son<PERSON>, jak<PERSON><PERSON> model, wyróżnia się w wielu ocenach, jednocześnie zachowując wysoką opłacalność."}, "azure": {"description": "Azure oferuje różnorodne zaawansowane modele AI, w tym GPT-3.5 i najnowszą serię GPT-4, wspierające różne typy danych i złożone zadania, koncentrując się na bezpiecznych, niezawodnych i zrównoważonych rozwiązaniach AI."}, "azureai": {"description": "Azure oferuje wiele zaawansowanych modeli AI, w tym GPT-3.5 i najnowszą serię GPT-4, wspierając różne typy danych i złożone zadania, d<PERSON><PERSON><PERSON><PERSON> do bezpiecznych, niezawodnych i zrównoważonych rozwiązań AI."}, "baichuan": {"description": "Baichuan Intelligent to firma skoncentrowana na badaniach nad dużymi modelami sztucznej inteligencji, której modele osiągają doskonałe wyniki w krajowych zadaniach związanych z encyklopedią wiedzy, przetwarzaniem długich tekstów i generowaniem treści w języku chińskim, przewyższając zagraniczne modele mainstreamowe. Baichuan Intelligent dysponuje również wiodącymi w branży zdolnościami multimodalnymi, osiągając doskonałe wyniki w wielu autorytatywnych ocenach. Jej modele obejmują Baichuan 4, Baichuan 3 Turbo i Baichuan 3 Turbo 128k, zoptymalizowane pod kątem różnych scenariuszy zastosowań, oferując opłacalne rozwiązania."}, "bedrock": {"description": "Bedrock to usługa oferowana przez Amazon AWS, skoncentrowana na dostarczaniu zaawansowanych modeli językowych i wizualnych dla przedsiębiorstw. <PERSON><PERSON> rodzina modeli obejmuje serię <PERSON> o<PERSON>, se<PERSON><PERSON> Llama 3.1 od Meta i inne, oferując różnorodne opcje od lekkich do wysokowydajnych, wspierając generowanie tekstu, dialogi, przetwarzanie obrazów i inne zadania, odpowiednie dla różnych skal i potrzeb aplikacji biznesowych."}, "cloudflare": {"description": "Uruchamiaj modele uczenia maszynowego napędzane przez GPU w globalnej sieci Cloudflare."}, "cohere": {"description": "Cohere przynosi najnowocześniejsze modele wielojęzyczne, zaawansowane funkcje wyszukiwania oraz dostosowane do nowoczesnych przedsiębiorstw przestrzenie robocze AI — wszystko zintegrowane w jednej bezpiecznej platformie."}, "deepseek": {"description": "DeepSeek to firma skoncentrowana na badaniach i zastosowaniach technologii sztucznej inteligencji, której najnowszy model DeepSeek-V2.5 łączy zdolności do prowadzenia ogólnych rozmów i przetwarzania kodu, osiągając znaczące postępy w zakresie dostosowywania do preferencji ludzkich, zadań pisarskich i przestrzegania instrukcji."}, "fal": {"description": "Platforma generatywna mediów skierowana do deweloperów"}, "fireworksai": {"description": "Fireworks AI to wiodący dostawca zaawansowanych modeli językowych, skoncentrowany na wywołaniach funkcji i przetwarzaniu multimodalnym. <PERSON><PERSON> model Firefunction V2 oparty na Llama-3, zoptymalizowany do wywołań funkcji, dialogów i przestrzegania instrukcji. Model wizualny FireLLaVA-13B wspiera mieszane wejścia obrazów i tekstu. Inne znaczące modele to seria Llama i seria Mixtral, oferujące efektywne wsparcie dla wielojęzycznego przestrzegania instrukcji i generacji."}, "giteeai": {"description": "Serverless API Gitee AI zapewnia deweloperom sztucznej inteligencji gotową usługę interfejsu interfejsu interfejsu dużych modeli."}, "github": {"description": "Dzięki modelom GitHub, deweloperzy mogą stać się inżynierami AI i budować z wykorzystaniem wiodących modeli AI w branży."}, "google": {"description": "Seria Gemini od Google to najnowoc<PERSON>śniejsze, uniwersalne modele AI stworzone przez Google DeepMind, zaprojektowane z myślą o multimodalności, wspierające bezproblemowe rozumienie i przetwarzanie tekstu, kod<PERSON>, o<PERSON><PERSON><PERSON>, dźwięku i wideo. Nadają się do różnych środowisk, od centrów danych po urządzenia mobilne, znacznie zwiększając wydajność i wszechstronność modeli AI."}, "groq": {"description": "Silnik inferencyjny LPU firmy Groq wyróżnia się w najnowszych niezależnych testach benchmarkowych dużych modeli językowych (LLM), redefiniując standardy rozwiązań AI dzięki niesamowitej szybkości i wydajności. Groq jest reprezentantem natychmiastowej szybkości inferencji, wykazując dobrą wydajność w wdrożeniach opartych na chmurze."}, "higress": {"description": "Higress to ch<PERSON>rowa brama API, która powstała w firmie Alibaba, aby rozwiązać problemy związane z ponownym ładowaniem Tengine, które negatywnie wpływa na długoterminowe połączenia, oraz niewystarczającą zdolnością do równoważenia obciążenia gRPC/Dubbo."}, "huggingface": {"description": "HuggingFace Inference API oferuje szybki i bezpłatny sposób na eksplorację tysięcy modeli, które nadają się do różnych zadań. Niezależnie od tego, czy prototypujesz nową aplikację, czy próbujesz funkcji uczenia maszynowego, to API zapewnia natychmiastowy dostęp do wysokowydajnych modeli z wielu dziedzin."}, "hunyuan": {"description": "Model językowy opracowany przez <PERSON>, który posiada potężne zdolności twórcze w języku chińskim, umie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> logicznego wnioskowania w złożonych kontekstach oraz niezawodne zdolności wykonawcze w zadaniach."}, "infiniai": {"description": "Dostarczanie usług wysokiej wydaj<PERSON>ści, łatwych w użyciu i bezpiecznych modeli dużych, obejmujących cały proces od opracowywania modeli do wdrożenia usług opartych na modelach dużych."}, "internlm": {"description": "Organizacja open source poświęcona badaniom i rozwojowi narzędzi dla dużych modeli. Oferuje wszystkim deweloperom AI wydajną i łatwą w użyciu platformę open source, umożliwiającą dostęp do najnowocześniejszych technologii modeli i algorytmów."}, "jina": {"description": "Jina AI została założona w 2020 roku i jest wiodącą firmą zajmującą się AI w zakresie wyszukiwania. Nasza platforma bazowa do wyszukiwania zawiera modele wektorowe, przetasowujące i małe modele językowe, które pomagają firmom budować niezawodne i wysokiej jakości aplikacje wyszukiwania generatywnego i multimodalnego."}, "lmstudio": {"description": "LM Studio to aplikacja desktopowa do rozwijania i eksperymentowania z LLM-ami na Twoim komputerze."}, "minimax": {"description": "MiniMax to firma technologiczna zajmująca się ogólną sztuczną inteligencją, założona w 2021 roku, dążąca do współtworzenia inteligencji z użytkownikami. MiniMax opracowało różne modele dużych modeli o różnych modalnościach, w tym model tekstowy MoE z bilionem parametrów, model głosowy oraz model obrazowy. Wprowadziło również aplikacje takie jak Conch AI."}, "mistral": {"description": "<PERSON><PERSON><PERSON> of<PERSON><PERSON><PERSON> z<PERSON>wansowane modele ogólne, specjalistyczne i badawcze, szeroko stosowane w złożonym rozumowaniu, zadaniach wielojęzycznych, generowaniu kodu i innych dziedzinach. Dzięki interfejsowi wywołań funkcji użytkownicy mogą integrować dostosowane funkcje, realizując konkretne zastosowania."}, "modelscope": {"description": "ModelScope to platforma modelu jako usługi wprowadzona przez <PERSON>, oferująca bogaty wybór modeli AI i usług inferencyjnych."}, "moonshot": {"description": "Moonshot to otwarta platforma stworzona przez Beijing Dark Side Technology Co., Ltd., oferująca różnorodne modele przetwarzania języka naturalnego, szeroko stosowane w takich dziedzinach jak tworzenie tre<PERSON>, badania akademickie, inteligentne rekomendacje, diagnoza medyczna i inne, wspierająca przetwarzanie długich tekstów i złożone zadania generacyjne."}, "novita": {"description": "Novita AI to platforma oferująca API do różnych dużych modeli językowych i generacji obrazów AI, elastyczna, niezawodna i opłacalna. Wspiera najnowsze modele open-source, takie jak Llama3, Mi<PERSON>l, i oferuje kompleksowe, przyjazne dla użytkownika oraz automatycznie skalowalne rozwiązania API dla rozwoju aplikacji generatywnej AI, odpowiednie dla szybkiego rozwoju startupów AI."}, "nvidia": {"description": "NVIDIA NIM™ oferuje kontenery do samodzielnego hostowania przyspieszonych przez GPU mikroserwisów wnioskowania, wspierając wdrażanie w chmurze, centrach danych, komputerach osobistych RTX™ AI i stacjach roboczych wstępnie wytrenowanych i dostosowanych modeli AI."}, "ollama": {"description": "Modele oferowane przez Ollama obejmują szeroki zakres zastosowań, w tym generowanie kodu, obliczenia matematyczne, przetwarzanie wielojęzyczne i interakcje konwersacyjne, wspierając różnorodne potrzeby wdrożeń na poziomie przedsiębiorstw i lokalnych."}, "openai": {"description": "OpenAI jest wiodącą na świecie instytucją badawczą w dziedzinie sztucznej inteligencji, której modele, takie jak seria GPT, przesuwają granice przetwarzania języka naturalnego. OpenAI dąży do zmiany wielu branż poprzez innowacyjne i efektywne rozwiązania AI. Ich produkty charakteryzują się znaczną wydajnością i opłacalnością, znajdując szerokie zastosowanie w badaniach, biznesie i innowacyjnych aplikacjach."}, "openrouter": {"description": "OpenRouter to platforma usługowa oferująca różnorodne interfejsy do nowoczesnych dużych modeli, wspierająca OpenAI, Anthropic, LLaMA i inne, odpowiednia dla zróżnicowanych potrzeb rozwojowych i aplikacyjnych. Użytkownicy mogą elastycznie wybierać optymalne modele i ceny zgodnie z własnymi potrzebami, co przyczynia się do poprawy doświadczeń związanych z AI."}, "perplexity": {"description": "Perplexity to wiodący dostawca modeli generacji dialogów, oferujący różnorodne zaawansowane modele Llama 3.1, wspierające aplikacje online i offline, szczególnie odpowiednie do złożonych zadań przetwarzania języka naturalnego."}, "ppio": {"description": "PPIO Paiou Cloud oferuje stabilne i opłacalne usługi API modeli open source, wspierające pełną gamę DeepSeek, Llama, Qwen i inne wiodące modele w branży."}, "qiniu": {"description": "<PERSON><PERSON> to wiodący dostawca usług chmurowych, oferujący API do dużych modeli AI, w tym DeepSeek, Llama i Qwen, z elastycznymi opcjami do tworzenia i stosowania aplikacji AI."}, "qwen": {"description": "<PERSON><PERSON>wen to samodzielnie opracowany przez Alibaba Cloud model j<PERSON><PERSON><PERSON><PERSON> o dużej skali, chara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się silnymi zdolnościami rozumienia i generowania języka naturalnego. Może odpowiadać na różnorodne pytania, two<PERSON><PERSON><PERSON> treści pisemne, w<PERSON><PERSON><PERSON><PERSON> opinie, pisać kod i działać w wielu dziedzinach."}, "sambanova": {"description": "SambaNova Cloud umożliwia deweloperom łatwe korzystanie z najlepszych modeli open source oraz cieszenie się najszybszą prędkością wnioskowania."}, "search1api": {"description": "Search1API oferuje dostęp do serii modeli DeepSeek, które można łączyć z siecią w razie potrzeby, w tym wersji standardowej i szybkiej, wspierając wybór modeli o różnych rozmiarach parametrów."}, "sensenova": {"description": "SenseTime codziennie się rozwija, opierając się na potężnym wsparciu infrastrukturalnym SenseTime, oferując wydajne i łatwe w użyciu usługi dużych modeli w pełnym zakresie."}, "siliconcloud": {"description": "SiliconFlow dąży do przyspieszenia AGI, aby przyn<PERSON><PERSON> korzy<PERSON> ludz<PERSON>ści, poprawiając wydajność dużych modeli AI dzięki łatwemu w użyciu i niskokosztowemu stosowi GenAI."}, "spark": {"description": "Model Xinghuo od iFlytek oferuje potężne możliwości AI w wielu dziedzinach i językach, wykorzystując zaawansowaną technologię przetwarzania języka naturalnego do budowy innowacyjnych aplikacji odpowiednich dla inteligentnych urządzeń, inteligentnej medycyny, inteligentnych finansów i innych scenariuszy wertykalnych."}, "stepfun": {"description": "Model StepFun charakteryzuje się wiodącymi w branży zdolnościami multimodalnymi i złożonym rozumowaniem, wspierając zrozumienie bardzo długich tekstów oraz potężne funkcje samodzielnego wyszukiwania."}, "taichu": {"description": "Nowa generacja multimodalnych dużych modeli opracowana przez Instytut Automatyki Chińskiej Akademii Nauk i Wuhan Institute of Artificial Intelligence wspiera wielorundowe pytania i odpowiedzi, tworzenie tekstów, generowanie obrazów, zrozumienie 3D, analizę sygnałów i inne kompleksowe zadania pytaniowe, posiadając silniejsze zdolności poznawcze, rozumienia i tworzenia, oferując nową interaktywną doświadczenie."}, "tencentcloud": {"description": "Atomowe możliwości silnika wiedzy (LLM Knowledge Engine Atomic Power) oparte na silniku wiedzy, oferujące pełne możliwości zadawania pytań i odpowiedzi, skierowane do przedsiębiorstw i deweloperów, zapewniające elastyczność w budowaniu i rozwijaniu aplikacji modelowych. Możesz tworzyć własne usługi modelowe za pomocą różnych atomowych możliwości, korzystając z usług takich jak analiza dokumentów, pod<PERSON>ł, embedding, wielokrotne przeredagowywanie i inne, aby dostosować AI do specyficznych potrzeb Twojej firmy."}, "togetherai": {"description": "Together AI dąży do osiągnięcia wiodącej wydajności poprzez innowacyjne modele AI, oferując szerokie możliwości dostosowywania, w tym wsparcie dla szybkiej ekspansji i intuicyjnych procesów wdrożeniowych, aby zas<PERSON><PERSON>ić różnorodne potrzeby przedsiębiorstw."}, "upstage": {"description": "Upstage koncentruje się na opracowywaniu modeli AI dla różnych potrzeb biznesowych, w tym Solar LLM i dokumentów AI, mając na celu osiągnięcie sztucznej ogólnej inteligencji (AGI). Umożliwia tworzenie prostych agentów konwersacyjnych za pomocą Chat API oraz wspiera wywołania funkcji, tłumaczenia, osadzenia i zastosowania w określonych dziedzinach."}, "v0": {"description": "v0 to asystent programowania w parach, kt<PERSON>ry pozwala opisać pomysły w naturalnym języku, a następnie generuje kod i interfejs użytkownika (UI) dla Twojego projektu"}, "vertexai": {"description": "Seria Gemini od Google to najnowoc<PERSON>śniejsze, uniwersalne modele AI stworzone przez Google DeepMind, zaprojektowane z myślą o multimodalności, wspierające bezproblemowe rozumienie i przetwarzanie tekstu, kodu, o<PERSON><PERSON>ów, dźwięku i wideo. Odpowiednie do różnych środowisk, od centrów danych po urządzenia mobilne, znacznie zwiększa efektywność i wszechstronność modeli AI."}, "vllm": {"description": "vLLM to szybka i łatwa w użyciu biblioteka do wnioskowania i usług LLM."}, "volcengine": {"description": "Platforma deweloperska usług dużych modeli wprowadzona przez ByteDance, oferująca bogate w funkcje, bezpieczne i konkurencyjne cenowo usługi wywoływania modeli. Oferuje również dane modelowe, dostosowywanie, wnioskowanie, ocenę i inne funkcje end-to-end, zapewniając kompleksowe wsparcie dla rozwoju aplikacji AI."}, "wenxin": {"description": "Platforma do rozwoju i usług aplikacji AI oraz dużych modeli w skali przedsiębiorstwa, oferująca najbardziej kompleksowy i łatwy w użyciu zestaw narzędzi do rozwoju modeli sztucznej inteligencji generatywnej oraz całego procesu tworzenia aplikacji."}, "xai": {"description": "xAI to firma, która dąży do budowy sztucznej inteligencji w celu przyspieszenia ludzkich odkryć naukowych. Naszą misją jest wspieranie wspólnego zrozumienia wszechświata."}, "xinference": {"description": "Xorbits Inference (Xinference) to otwarty platforma, która ułatwia uruchamianie i integrację różnych modeli AI. Dzięki Xinference możesz wykonywać wnioskowanie za pomocą dowolnego otwartego modelu LLM, modelu osadzania i modelu wielomodalnego w środowisku chmurowym lub lokalnym, tworząc przy tym potężne aplikacje AI."}, "zeroone": {"description": "01.AI koncentruje się na technologiach sztucznej inteligencji w erze AI 2.0, intensywnie promując innowacje i zastosowania „człowiek + sztuczna inteligencja”, wykorzystując potężne modele i zaawansowane technologie AI w celu zwiększenia wydajności ludzkiej produkcji i realizacji technologicznego wsparcia."}, "zhipu": {"description": "Zhipu AI oferuje otwartą platformę modeli multimodalnych i językowych, wspierającą szeroki zakres zastosowań AI, w tym przetwarzanie tekstu, rozumienie obrazów i pomoc w programowaniu."}}