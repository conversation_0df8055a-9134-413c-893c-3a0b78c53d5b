{"01-ai/yi-1.5-34b-chat": {"description": "Zero One, na<PERSON><PERSON><PERSON> model open source z dostrojeniem, zawierający 34 miliardy parametrów, dostosowany do różnych scenariuszy <PERSON>ogowych, z wysokiej jakości danymi treningowymi, dostosowany do preferencji ludzkich."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One, na<PERSON><PERSON><PERSON> model open source z dostrojeniem, zawierający 9 miliardów parametrów, dostosowany do różnych scenariuszy dialogowych, z wysokiej jakości danymi treningowymi, dostosowany do preferencji ludzkich."}, "360/deepseek-r1": {"description": "[Wersja 360] DeepSeek-R1 wykorzystuje techniki uczenia przez wzmocnienie na dużą skalę w fazie po treningu, znacznie poprawiając zdolności wnioskowania modelu przy minimalnej ilości oznaczonych danych. W zadaniach matematycznych, kodowania i wnioskowania w języku naturalnym osiąga wyniki porównywalne z oficjalną wersją OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro, jako ważny członek serii modeli AI 360, zaspokaja różnorodne potrzeby aplikacji przetwarzania języka naturalnego dzięki wydajnym zdolnościom przetwarzania tekstu, obsługując zrozumienie długich tekstów i wielokrotne dialogi."}, "360gpt-pro-trans": {"description": "Model dedykowany do tłumaczeń, gł<PERSON>boko dostrojony i zoptymalizowany, oferujący wiodące efekty tłumaczeniowe."}, "360gpt-turbo": {"description": "360GPT Turbo oferuje potężne zdolności obliczeniowe i dialogowe, charakteryzując się doskonałym rozumieniem semantycznym i wydajnością generacyjną, stanowiąc idealne rozwiązanie dla firm i deweloperów jako inteligentny asystent."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K kładzie nacisk na bezpieczeństwo semantyczne i odpowiedzialność, zaprojektowany specjalnie dla aplikacji o wysokich wymaganiach dotyczących bezpieczeństwa treści, zapewniając dokładność i stabilność doświadczeń użytkowników."}, "360gpt2-o1": {"description": "360gpt2-o1 wykorzystuje wyszukiwanie drzew do budowy łańcucha myślenia i wprowadza mechanizm refleksji, wykorzystując uczenie przez wzmocnienie, model posiada zdolność do samorefleksji i korekty błędów."}, "360gpt2-pro": {"description": "360GPT2 Pro to zaawansowany model przetwarzania języka naturalnego wydany przez firmę 360, charakteryzuj<PERSON>cy się doskonałymi zdolnościami generowania i rozumienia tekstu, szczególnie w obszarze generowania i tworzenia treści, zdolny do obsługi skomplikowanych zadań związanych z konwersją językową i odgrywaniem ról."}, "360zhinao2-o1": {"description": "Model 360zhinao2-o1 wykorzystuje wyszukiwanie drzewne do budowy łańcucha myślowego i wprowadza mechanizm refleksji, wykorzystując uczenie przez wzmocnienie do treningu, co pozwala modelowi na samorefleksję i korekcję błędów."}, "4.0Ultra": {"description": "Spark4.0 Ultra to najsilniejsza wersja w serii modeli Spark, która, oprócz ulepszonego łącza wyszukiwania w sieci, zwiększa zdolność rozumienia i podsumowywania treści tekstowych. Jest to kompleksowe rozwiązanie mające na celu zwiększenie wydajności biurowej i dokładne odpowiadanie na potrzeby, stanowiące inteligentny produkt wiodący w branży."}, "AnimeSharp": {"description": "AnimeSharp (znany również jako „4x‑AnimeSharp”) to otwarty model superrozdzielczości opracowany przez Kim2091 na bazie architektury ESRGAN, skoncentrowany na powiększaniu i wyostrzaniu obrazów w stylu anime. W lutym 2022 roku zmieniono jego nazwę z „4x-TextSharpV1”. Początkowo model był również stosowany do obrazów tekstowych, ale jego wydajność została znacznie zoptymalizowana pod kątem treści anime."}, "Baichuan2-Turbo": {"description": "Wykorzystuje technologię wzmacniania wyszukiwania, aby p<PERSON><PERSON><PERSON><PERSON><PERSON> duży model z wiedzą branżową i wiedzą z całej sieci. Obsługuje przesyłanie różnych dokumentów, takich jak PDF, Word, oraz wprowadzanie adresów URL, zapewniając szybki i kompleksowy dostęp do informacji oraz dokładne i profesjonalne wyniki."}, "Baichuan3-Turbo": {"description": "Optymalizowany pod kątem częstych scenariuszy biznesowych, znacznie poprawiający efektywność i oferujący korzystny stosunek jakości do ceny. W porównaniu do modelu Baichuan2, generowanie treści wzrosło o 20%, pytania o wiedzę o 17%, a zdolności odgrywania ról o 40%. Ogólna wydajność jest lepsza niż GPT3.5."}, "Baichuan3-Turbo-128k": {"description": "Oferuje 128K ultra dług<PERSON> konte<PERSON>, zoptymalizowany pod kątem częstych scenariuszy biznesowych, znacznie poprawiający efektywność i oferujący korzystny stosunek jakości do ceny. W porównaniu do modelu Baichuan2, generowanie treści wzrosło o 20%, pytania o wiedzę o 17%, a zdolności odgrywania ról o 40%. Ogólna wydajność jest lepsza niż GPT3.5."}, "Baichuan4": {"description": "Model o najwy<PERSON><PERSON><PERSON> wydajności w kraju, prz<PERSON><PERSON>ż<PERSON>aj<PERSON><PERSON> zagraniczne modele w zadaniach związanych z encyklopedią, długimi tekstami i generowaniem treści w języku chińskim. Posiada również wiodące w branży zdolności multimodalne, osiągając doskonałe wyniki w wielu autorytatywnych testach."}, "Baichuan4-Air": {"description": "Model o najlepszych możliwościach w kraju, przew<PERSON>ższający zagraniczne modele w zadaniach związanych z wiedzą encyklopedyczną, długimi tekstami i twórczością w języku chińskim. Posiada również wiodące w branży możliwości multimodalne, osiągając doskonałe wyniki w wielu autorytatywnych testach."}, "Baichuan4-Turbo": {"description": "Model o najlepszych możliwościach w kraju, przew<PERSON>ższający zagraniczne modele w zadaniach związanych z wiedzą encyklopedyczną, długimi tekstami i twórczością w języku chińskim. Posiada również wiodące w branży możliwości multimodalne, osiągając doskonałe wyniki w wielu autorytatywnych testach."}, "DeepSeek-R1": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wyd<PERSON>ny LLM, specjalizujący się w wnioskowaniu, matematyce i programowaniu."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 — wi<PERSON><PERSON><PERSON> i inteligentniejszy model w zestawie DeepSeek — został skondensowany do architektury Llama 70B. Na podstawie testów porównawczych i ocen ludzkich, model ten jest bardziej inteligentny niż oryginalny Llama 70B, zwłaszcza w zadaniach wymagających precyzji matematycznej i faktograficznej."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Model destylacyjny DeepSeek-R1 oparty na Qwen2.5-Math-1.5B, optymalizujący wydajność wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustanawiają<PERSON> nowe standardy w wielu zadaniach."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Model destylacyjny DeepSeek-R1 oparty na Qwen2.5-14B, optymalizujący wydajność wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustanawiający nowe standardy w wielu zadaniach."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "Seria DeepSeek-R1 optymalizuje wydajność wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustanawiający nowe standardy w wielu zadaniach, przewyższający poziom OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Model destylacyjny DeepSeek-R1 oparty na Qwen2.5-Math-7B, optymalizuj<PERSON><PERSON> wydajność wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustanawiający nowe standardy w wielu zadaniach."}, "DeepSeek-V3": {"description": "DeepSeek-V3 to model MoE opracowany przez firmę DeepSeek. Wyniki DeepSeek-V3 w wielu testach przewyższają inne modele open source, takie jak Qwen2.5-72B i Llama-3.1-405B, a jego wydaj<PERSON> jest porównywalna z najlepszymi zamkniętymi modelami na świecie, takimi jak GPT-4o i Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite oferuje niezwykle szybkie reakcje i lepszy stosunek jakości do ceny, zapewniając klientom elastyczne opcje dla różnych scenariuszy. Obsługuje wnioskowanie i dostrajanie z kontekstem do 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite oferuje niezwykle szybkie reakcje i lepszy stosunek jakości do ceny, zapewniając klientom elastyczne opcje dla różnych scenariuszy. Obsługuje wnioskowanie i dostrajanie z kontekstem do 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite oferuje niezwykle szybkie reakcje i lepszy stosunek jakości do ceny, zapewniając klientom elastyczne opcje dla różnych scenariuszy. Obsługuje wnioskowanie i dostrajanie z kontekstem do 4k."}, "Doubao-pro-128k": {"description": "Na<PERSON><PERSON><PERSON><PERSON> model g<PERSON><PERSON><PERSON>, odpowiedni do złożonych zadań, osiągający doskonałe wyniki w scenariuszach takich jak pytania i odpowiedzi, streszcz<PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, klasyfikacja tekstu i odgrywanie ról. Obsługuje wnioskowanie i dostrajanie z kontekstem do 128k."}, "Doubao-pro-32k": {"description": "Na<PERSON><PERSON><PERSON><PERSON> model g<PERSON><PERSON><PERSON>, odpowiedni do złożonych zadań, osiągający doskonałe wyniki w scenariuszach takich jak pytania i odpowiedzi, streszcz<PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, klasyfikacja tekstu i odgrywanie ról. Obsługuje wnioskowanie i dostrajanie z kontekstem do 32k."}, "Doubao-pro-4k": {"description": "Na<PERSON><PERSON><PERSON><PERSON> model g<PERSON><PERSON><PERSON>, odpowiedni do złożonych zadań, osiągający doskonałe wyniki w scenariuszach takich jak pytania i odpowiedzi, streszcz<PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, klasyfikacja tekstu i odgrywanie ról. Obsługuje wnioskowanie i dostrajanie z kontekstem do 4k."}, "DreamO": {"description": "DreamO to otwarty model generowania obrazów opracowany wspólnie przez ByteDance i Uniwersytet Pekiński, maj<PERSON>cy na celu wsparcie wielozadaniowej generacji obrazów w ramach jednolitej architektury. Wykorzystuje efektywną metodę modelowania kombinacyjnego, umożliwiając generowanie spójnych i dostosowanych obrazów na podstawie wielu warunków, takich jak to<PERSON><PERSON>, temat, styl czy tło wskazane przez użytkownika."}, "ERNIE-3.5-128K": {"description": "Flagowy model dużego języka opracowany przez <PERSON>, obejmujący ogromne zbiory danych w języku chińskim i angielskim, charakteryzuj<PERSON>cy się silnymi zdolnościami ogólnymi, zdolny do spełnienia wymagań w większości scenariuszy związanych z pytaniami i odpowiedziami, generowaniem treści oraz aplikacjami wtyczek; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji w odpowiedziach."}, "ERNIE-3.5-8K": {"description": "Flagowy model dużego języka opracowany przez <PERSON>, obejmujący ogromne zbiory danych w języku chińskim i angielskim, charakteryzuj<PERSON>cy się silnymi zdolnościami ogólnymi, zdolny do spełnienia wymagań w większości scenariuszy związanych z pytaniami i odpowiedziami, generowaniem treści oraz aplikacjami wtyczek; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji w odpowiedziach."}, "ERNIE-3.5-8K-Preview": {"description": "Flagowy model dużego języka opracowany przez <PERSON>, obejmujący ogromne zbiory danych w języku chińskim i angielskim, charakteryzuj<PERSON>cy się silnymi zdolnościami ogólnymi, zdolny do spełnienia wymagań w większości scenariuszy związanych z pytaniami i odpowiedziami, generowaniem treści oraz aplikacjami wtyczek; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji w odpowiedziach."}, "ERNIE-4.0-8K-Latest": {"description": "Flagowy model ultra dużego języka opracowany przez <PERSON>, w porównaniu do ERNIE 3.5, oferujący kompleksową aktualizację możli<PERSON>ci modelu, szeroko stosowany w złożonych scenariuszach w róż<PERSON>ch dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ERNIE-4.0-8K-Preview": {"description": "Flagowy model ultra dużego języka opracowany przez <PERSON>, w porównaniu do ERNIE 3.5, oferujący kompleksową aktualizację możli<PERSON>ci modelu, szeroko stosowany w złożonych scenariuszach w róż<PERSON>ch dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Opracowany przez <PERSON> flagowy, ultra-duży model <PERSON><PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> wykazu<PERSON> doskonałe ogólne rezultaty i jest szeroko stosowany w złożonych zadaniach w różnych dziedzinach; obsługuje automatyczne łączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji odpowiadających na pytania. W porównaniu do ERNIE 4.0 wykazuje lepszą wydajność."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Flagowy model ultra dużego języka opracowany przez <PERSON>, charakt<PERSON><PERSON><PERSON><PERSON><PERSON> się doskonałymi wynikami ogó<PERSON>ymi, szeroko stosowany w złożonych scenariuszach w różnych dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji. W porównaniu do ERNIE 4.0, oferuje lepsze wyniki wydajności."}, "ERNIE-Character-8K": {"description": "Model dużego języka opracowany przez <PERSON>, skoncentrowany na specyficznych scenariuszach, odpowiedni do zastosowań takich jak NPC w grach, rozmowy z obsługą klienta, odgrywanie ról w dialogach, charakteryzuj<PERSON>cy się wyraźnym i spójnym stylem postaci, silniejszą zdolnością do przestrzegania poleceń oraz lepszą wydajnością wnioskowania."}, "ERNIE-Lite-Pro-128K": {"description": "Lekki model dużego języka opracowany przez <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe wyniki modelu z wydajnością wnioskowania, oferujący lepsze wyniki niż ERNIE Lite, odpowiedni do użycia w niskomocowych kartach przyspieszających AI."}, "ERNIE-Speed-128K": {"description": "Najnow<PERSON><PERSON>ś<PERSON>j<PERSON><PERSON> model dużego języka opracowany przez Baidu w 2024 roku, charaktery<PERSON><PERSON><PERSON><PERSON> się doskonałymi zdolnościami ogólnymi, odpowiedni jako model bazowy do dalszego dostosowywania, lepiej radzący sobie z problemami w specyficz<PERSON>ch scenariuszach, a także zapewniający doskonałą wydajność wnioskowania."}, "ERNIE-Speed-Pro-128K": {"description": "Najnow<PERSON><PERSON>ś<PERSON>j<PERSON><PERSON> model dużego języka opracowany przez Baidu w 2024 roku, charaktery<PERSON><PERSON><PERSON><PERSON> się doskonałymi zdolnościami ogólnymi, oferujący lepsze wyniki niż ERNIE Speed, odpowiedni jako model bazowy do dalszego dostosowywania, lepiej radzący sobie z problemami w specyficznych scenariuszach, a także zapewniający doskonałą wydajność wnioskowania."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev to multimodalny model generowania i edycji obrazów opracowany przez Black Forest Labs, oparty na architekturze Rectified Flow Transformer, posiadający 12 miliardów parametrów. Skupia się na generowaniu, rekonstruk<PERSON><PERSON>, wzmacnianiu i edycji obrazów w oparciu o podane warunki kontekstowe. Model łączy zalety kontrolowanej generacji modeli dyfuzyjnych z możliwościami modelowania kontekstu transformera, oferując wysoką jakość obrazów i szerokie zastosowanie w zadaniach takich jak naprawa, uzupełnianie i rekonstrukcja scen wizualnych."}, "FLUX.1-dev": {"description": "FLUX.1-dev to otwarty multimodalny model <PERSON><PERSON><PERSON><PERSON><PERSON> (MLLM) opracowany przez Black Forest Labs, zoptymalizowany pod kątem zadań tekstowo-obrazowych, łączący zdolności rozumienia i generowania obrazów oraz tekstu. Bazuje na zaawansowanych dużych modelach językowych (np. Mistral-7B) i dzięki starannie zaprojektowanemu enkoderowi wizualnemu oraz wieloetapowemu dostrajaniu instrukcji umożliwia współpracę tekstu i obrazu oraz złożone wnioskowanie."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) to innowacyjny model, idealny do zastosowań w wielu dziedzinach i złożonych zadań."}, "HelloMeme": {"description": "HelloMeme to narzędzie AI, które automatycznie generuje memy, animacje lub krótkie filmy na podstawie dostarczonych przez Ciebie obrazów lub ruchów. Nie wymaga żadnych umiejętności rysunkowych ani programistycznych — wystarczy przygotować obraz referencyjny, a narzędzie stworzy atrakcyjne, zabawne i spójne stylistycznie treści."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full to o<PERSON><PERSON><PERSON>, multimodalny model do edycji obrazów opracowany przez HiDream.ai, oparty na zaawansowanej architekturze Diffusion Transformer i wyposażony w potężne zdolności rozumienia języka (wbudowany LLaMA 3.1-8B-Instruct). Umożliwia generowanie obrazów, transfer stylu, lokalną edycję i przerysowywanie treści za pomocą naturalnych poleceń językowych, oferując doskonałe rozumienie i realizację zadań tekstowo-obrazowych."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled to lekki model generowania obrazów na podstawie tekstu, zoptymalizowany przez destyla<PERSON>ję, umożliwiający szybkie tworzenie wysokiej jakości obrazów, szczególnie odpowiedni do środowisk o ograniczonych zasobach i zadań generacji w czasie rzeczywistym."}, "InstantCharacter": {"description": "InstantCharacter to model generowania spersonalizowanych postaci bez potrzeby dostrajania, wydany przez zespół AI Tencent w 2025 roku. Model umożliwia wierne i spójne generowanie postaci w różnych scenariuszach na podstawie pojedynczego obrazu referencyjnego oraz elastyczne przenoszenie tej postaci do różnych stylów, ruchów i tła."}, "InternVL2-8B": {"description": "InternVL2-8B to potężny model j<PERSON><PERSON><PERSON>wy wizualny, wspierający przetwarzanie multimodalne obrazów i tekstu, zdolny do precyzyjnego rozpoznawania treści obrazów i generowania odpowiednich opisów lub odpowiedzi."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B to potężny model j<PERSON><PERSON><PERSON><PERSON> wizualny, wspierający przetwarzanie multimodalne obrazów i tekstu, zdolny do precyzyjnego rozpoznawania treści obrazów i generowania odpowiednich opisów lub odpowiedzi."}, "Kolors": {"description": "Kolors to model generowania obrazów na podstawie tekstu opracowany przez zespół Kolors z Kuaishou. Trenowany na miliardach parametrów, wyróżnia się wysoką jakością wizualną, doskonałym rozumieniem semantyki języka chińskiego oraz precyzyjnym renderowaniem tekstu."}, "Kwai-Kolors/Kolors": {"description": "Kolors to duży model generowania obrazów na podstawie tekstu oparty na latentnej dyfuzji, opracowany przez zespół Kolors z Kuaishou. Trenowany na miliardach par tekst-obraz, wykazuje znakomitą jako<PERSON> wizualn<PERSON>, precyzję w rozumieniu złożonych semantyk oraz doskonałe renderowanie znaków chińskich i angielskich. Obsługuje wejścia w języku chińskim i angielskim, a także wyróżnia się w generowaniu specyficznych treści w języku chińskim."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Wyróżniające się zdolnościami wnioskowania obrazów na wysokiej rozdzielczości, odpowiednie do zastosowań w rozumieniu wizualnym."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Zaawansowane zdolności wnioskowania obrazów, odpowiednie do zastosowań w agentach rozumienia wizualnego."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Model tekstowy Llama 3.1 dostosowany do instrukcji, zoptymalizowany do wielojęzycznych przypadków użycia dialogów, osiągający doskonałe wyniki w wielu dostępnych modelach czatu, zar<PERSON><PERSON> otwartych, jak i zamkniętych, w powszechnych benchmarkach branżowych."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Model tekstowy Llama 3.1 dostosowany do instrukcji, zoptymalizowany do wielojęzycznych przypadków użycia dialogów, osiągający doskonałe wyniki w wielu dostępnych modelach czatu, zar<PERSON><PERSON> otwartych, jak i zamkniętych, w powszechnych benchmarkach branżowych."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Model tekstowy Llama 3.1 dostosowany do instrukcji, zoptymalizowany do wielojęzycznych przypadków użycia dialogów, osiągający doskonałe wyniki w wielu dostępnych modelach czatu, zar<PERSON><PERSON> otwartych, jak i zamkniętych, w powszechnych benchmarkach branżowych."}, "Meta-Llama-3.2-1B-Instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, now<PERSON><PERSON><PERSON> mały model j<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zdolności rozumienia języka, doskonałe umiejętności wnioskowania oraz generowania tekstu."}, "Meta-Llama-3.2-3B-Instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, now<PERSON><PERSON><PERSON> mały model j<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zdolności rozumienia języka, doskonałe umiejętności wnioskowania oraz generowania tekstu."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 to najnowocześniejszy wielojęzyczny otwarty model języ<PERSON>wy z serii Llama, oferujący wydajność porównywalną z modelem 405B przy bardzo niskich kosztach. Oparty na strukturze Transformer, poprawiony dzięki nadzorowanemu dos<PERSON>iu (SFT) oraz uczeniu ze wzmocnieniem opartym na ludzkiej opinii (RLHF), co zwiększa jego użyteczność i bezpieczeństwo. Jego wersja dostosowana do instrukcji została zoptymalizowana do wielojęzycznych dialogów, osiągając lepsze wyniki niż wiele dostępnych modeli czatu, zarówno otwartych, jak i zamkniętych, w wielu branżowych benchmarkach. Data graniczna wiedzy to grudzień 2023."}, "MiniMax-M1": {"description": "<PERSON><PERSON>, samodzielnie opracowany model inferencyjny. Globalny lider: 80K łańcuchów myślowych x 1M we<PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> porównywalna z najlepszymi modelami zagranicznymi."}, "MiniMax-Text-01": {"description": "W serii modeli MiniMax-01 wprowadziliśmy odważne innowacje: po raz pierwszy na dużą skalę zrealizowano mechanizm liniowej uwagi, tradycyjna architektura Transformera nie jest już jedynym wyborem. Liczba parametrów tego modelu wynosi aż 456 miliardów, z aktywacją wynoszącą 45,9 miliarda. Ogólna wydajność modelu dorównuje najlepszym modelom zagranicznym, jednocześnie efektywnie przetwarzając kontekst o długości do 4 milionów tokenów, co stanowi 32 razy więcej niż GPT-4o i 20 razy więcej niż Claude-3.5-Sonnet."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 to otwartoźródłowy model inferencyjny o dużej skali z mieszanym mechanizmem uwagi, posiadający 456 miliard<PERSON> parametrów, z których około 45,9 miliarda jest aktywowanych na każdy token. Model natywnie obsługuje ultra-długi kontekst do 1 miliona tokenów i dzięki mechanizmowi błyskawicznej uwagi oszczędza 75% operacji zmiennoprzecinkowych w zadaniach generowania na 100 tysiącach tokenów w porównaniu do DeepSeek R1. Ponadto MiniMax-M1 wykorzystuje architekturę MoE (mieszani eksperci), łącząc algorytm CISPO z efektywnym treningiem wzmacniającym opartym na mieszanej uwadze, osiągając wiodącą w branży wydajność w inferencji długich wejść i rzeczywistych scenariuszach inżynierii oprogramowania."}, "Moonshot-Kimi-K2-Instruct": {"description": "Model o łącznej liczbie parametrów 1 biliona i aktywowanych 32 miliardach parametrów. Wśród modeli nie myślących osiąga czołowe wyniki w wiedzy specjalistycznej, matematyce i kodowaniu, lepiej radząc sobie z zadaniami ogólnymi agenta. Model jest starannie zoptymalizowany pod kątem zadań agenta, potrafi nie tylko odpowiadać na pytania, ale także podejmować działania. Idealny do improwizacji, ogólnej rozmowy i doświadczeń agenta, działający na poziomie refleksu bez potrzeby długiego przetwarzania."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) to model poleceń o wysokiej precyzji, idealny do złożonych obliczeń."}, "OmniConsistency": {"description": "OmniConsistency poprawia spójność stylu i zdolność generalizacji w zadaniach obraz-do-obrazu (Image-to-Image) poprzez wprowadzenie dużych modeli Diffusion Transformers (DiTs) oraz parowanych danych stylizowanych, zapobiegając degradacji stylu."}, "Phi-3-medium-128k-instruct": {"description": "Ten sam model Phi-3-medium, ale z większym rozmiarem kontekstu do RAG lub kilku strzałowego wywoływania."}, "Phi-3-medium-4k-instruct": {"description": "Model z 14 milia<PERSON><PERSON> paramet<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON> jako<PERSON> niż Phi-3-mini, z naciskiem na dane o wysokiej jakości i gęstości rozumowania."}, "Phi-3-mini-128k-instruct": {"description": "Ten sam model Phi-3-mini, ale z większym rozmiarem kontekstu do RAG lub kilku strzałowego wywoływania."}, "Phi-3-mini-4k-instruct": {"description": "Najmniejszy członek rodziny Phi-3. Zoptymalizowany zarówno pod kątem jakości, jak i niskiej latenc<PERSON>."}, "Phi-3-small-128k-instruct": {"description": "Ten sam model Phi-3-small, ale z większym rozmiarem kontekstu do RAG lub kilku strzałowego wywoływania."}, "Phi-3-small-8k-instruct": {"description": "Model z 7 milia<PERSON><PERSON> paramet<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>z<PERSON> jakoś<PERSON> niż Phi-3-mini, z naciskiem na dane o wysokiej jakości i gęstości rozumowania."}, "Phi-3.5-mini-instruct": {"description": "Zaktualizowana wersja modelu Phi-3-mini."}, "Phi-3.5-vision-instrust": {"description": "Zaktualizowana wersja modelu Phi-3-vision."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct to model dużego języka z serii Qwen2, dostosowany do instrukcji, o rozmiarze parametrów wynoszącym 7B. Model ten oparty jest na architekturze Transformer, wykorzystu<PERSON><PERSON><PERSON> funkcję aktywacji SwiGLU, przesunięcia QKV w uwadze oraz grupowe zapytania uwagi. Może obsługiwać duże wejścia. Model ten wykazuje doskonałe wyniki w wielu testach benchmarkowych dotyczących rozumienia języka, generowania, zdolności wielojęzycznych, kodowania, matematyki i wnioskowania, przewyższając większość modeli open-source i wykazując konkurencyjność z modelami własnościowymi w niektórych zadaniach. Qwen2-7B-Instruct wykazuje znaczną poprawę wydajności w wielu ocenach w porównaniu do Qwen1.5-7B-Chat."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct to jeden z najnowszych modeli dużych języków wydanych przez Alibaba Cloud. Model 7B ma znacząco poprawione zdolności w zakresie kodowania i matematyki. Oferuje również wsparcie dla wielu języków, obejmując ponad 29 języków, w tym chiński i angielski. Model ten wykazuje znaczną poprawę w zakresie przestrzegania instrukcji, rozumienia danych strukturalnych oraz generowania strukturalnych wyników (szczególnie JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct to najnowsza wersja serii dużych modeli językowych specyficznych dla kodu wydana przez <PERSON>. Model ten, oparty na Qwen2.5, zost<PERSON><PERSON> przeszkolony na 55 bilionach tokenów, znacznie poprawiając zdolności generowania kodu, wnioskowania i naprawy. Wzmacnia on nie tylko zdolności kodowania, ale także utrzymuje przewagę w zakresie matematyki i ogólnych umiejętności. Model ten stanowi bardziej kompleksową podstawę dla rzeczywistych zastosowań, takich jak inteligentne agenty kodowe."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL to nowa wersja seri<PERSON>, posiadająca zaawansowane zdolności zrozumienia wizualnego. Potrafi analiz<PERSON> tekst, wykresy i układ w obra<PERSON>h, a także zrozumieć długie filmy i wykrywać zdarzenia. Jest zdolny do przeprowadzania wnioskowania, operowania narzędziami, obsługuje lokalizację obiektów w różnych formatach i generowanie wyjścia strukturalnego. Optymalizuje trening rozdzielczości i klatki wideo, a także zwiększa efektywność kodera wizualnego."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking to otwarty model wizualno<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (VLM) opracowany wspólnie przez Zhipu AI i Laboratorium KEG Uniwersytetu Tsinghua, zaprojektowany do obsługi złożonych zadań poznawczych wielomodalnych. Model opiera się na bazowym modelu GLM-4-9B-0414 i znacząco poprawia zdolności wnioskowania międzymodalnego oraz stabilność dzięki wprowadzeniu mechanizmu rozumowania „łańcucha myślowego” (Chain-of-Thought) oraz zastosowaniu strategii uczenia ze wzmocnieniem."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat to otwarta wersja modelu pretrenowanego z serii GLM-4, wydana przez Zhipu AI. Model ten wykazuje doskonałe wyniki w zakresie semantyki, matemat<PERSON><PERSON>, wnioskowania, kodu i wiedzy. Oprócz wsparcia dla wieloetapowych rozmów, GLM-4-9B-Chat oferuje również zaawansowane funkcje, takie jak przeglądanie stron internetowych, wykonywanie kodu, wywoływanie niestandardowych narzędzi (Function Call) oraz wnioskowanie z długich tekstów. Model obsługuje 26 języków, w tym chi<PERSON>, an<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> i niemiecki. W wielu testach benchmarkowych, takich jak AlignBench-v2, MT-Bench, MMLU i C-Eval, GLM-4-9B-Chat wykazuje doskon<PERSON> wyd<PERSON>. Model obsługuje maksymalną długoś<PERSON> kontekstu 128K, co czyni go odpowiednim do badań akademickich i zastosowań komercyjnych."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 to model wnioskowania napędzany uczeniem ze wzmocnieniem (RL), kt<PERSON><PERSON> rozwiązuje problemy z powtarzalnością i czytelnością modeli. Przed RL, DeepSeek-R1 wprowad<PERSON>ł dane do zimnego startu, co dodatkowo zoptymalizowało wydajność wnioskowania. W zadaniach matematycznych, kodowych i wnioskowania, osiąga wyniki porównywalne z OpenAI-o1, a dzięki starannie zaprojektowanym metodom treningowym poprawia ogólne wyniki."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B to model stworzony na podstawie Qwen2.5-Math-7B poprzez proces wiedzy distylacji. Model ten został wytrenowany na 800 000 wybrukowanych próbkach wygenerowanych przez DeepSeek-R1, co pozwoliło mu wykazać się doskonałymi zdolnościami wnioskowania. W wielu testach referencyjnych osiągnął znakomite wyniki, w tym 92,8% dokładności na MATH-500, 55,5% sukcesów na AIME 2024 oraz 1189 punktów na CodeForces, co potwierdza jego silne umiejętności matematyczne i programistyczne jako modelu o rozmiarze 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 to model językowy z 6710 miliardami parametrów, oparty na architekturze mieszanych e<PERSON>ów (MoE), wykorzyst<PERSON><PERSON><PERSON><PERSON> wielogłowicową potencjalną uwa<PERSON> (MLA) oraz strategię równoważenia obciążenia bez dodatkowych strat, co optymalizuje wydajność wnioskowania i treningu. Dzięki wstępnemu treningowi na 14,8 bilionach wysokiej jakości tokenów oraz nadzorowanemu dostrajaniu i uczeniu ze wzmocnieniem, DeepSeek-V3 przewyższa inne modele open source, zbliżając się do wiodących modeli zamkniętych."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 to podstawowy model architektury MoE o potężnych zdolnościach kodowania i agenta, z łączną liczbą parametrów 1 biliona i 32 miliardami aktywowanych parametrów. W testach wydajności w zakresie ogólnej wiedzy, programowania, matematyki i zadań agenta model K2 przewyższa inne popularne otwarte modele."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview to innowacyjny model przetwarzania języka naturalnego, który efektywnie radzi sobie z złożonymi zadaniami generowania dialogów i rozumienia kontekstu."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview to model badawczy opracowany przez zespół Qwen, skoncentrowany na zdolnościach wnioskowania wizualnego, który ma unikalne zalety w zrozumieniu złożonych scenariuszy i rozwiązywaniu wizualnie związanych problemów matematycznych."}, "Qwen/QwQ-32B": {"description": "QwQ jest modelem inferencyjnym z serii Qwen. W porównaniu do tradycyjnych modeli dostosowanych do instrukcji, QwQ posiada zdolności myślenia i wnioskowania, co pozwala na znaczące zwiększenie wydajności w zadaniach końcowych, szczególnie w rozwiązywaniu trudnych problemów. QwQ-32B to średniej wielkości model inferencyjny, który osiąga konkurencyjną wydajność w porównaniu z najnowocześniejszymi modelami inferencyjnymi, takimi jak DeepSeek-R1 i o1-mini. Model ten wykorzystuje technologie takie jak RoPE, SwiGLU, RMSNorm oraz Attention QKV bias, posiada 64-warstwową strukturę sieci i 40 głowic uwagi Q (w architekturze GQA KV wynosi 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview to na<PERSON><PERSON><PERSON> eksperymentalny model <PERSON><PERSON><PERSON><PERSON>wen, skoncentrowany na zwiększeniu zdolności wnioskowania AI. Poprzez eksplorację złożonych mechanizmów, takich jak mieszanie języków i wnioskowanie rekurencyjne, główne zalety obejmują silne zdolności analizy wnioskowania, matematyki i programowania. Jednocześnie występują problemy z przełączaniem języków, cyklami wnioskowania, kwestiami bezpieczeństwa oraz różnicami w innych zdolnościach."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 to zaawansowany uniwersalny model <PERSON><PERSON><PERSON><PERSON><PERSON>, wspieraj<PERSON>cy różne typy poleceń."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct to model dużego języka z serii Qwen2, dostosowany do instrukcji, o rozmiarze parametrów wynoszącym 72B. Model ten oparty jest na architekturze Transformer, wykorzyst<PERSON><PERSON><PERSON><PERSON> funkcję aktywacji SwiGLU, przesunięcia QKV w uwadze oraz grupowe zapytania uwagi. Może obsługiwać duże wejścia. Model ten wykazuje doskonałe wyniki w wielu testach benchmarkowych dotyczących rozumienia języka, generowania, zdolności wielojęzycznych, kodowania, matematyki i wnioskowania, przewyższając większość modeli open-source i wykazując konkurencyjność z modelami własnościowymi w niektórych zadaniach."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL to najnowsza iteracja modelu Qwen-VL, osiągająca najnowocześniejsze wyniki w benchmarkach zrozumienia wizualnego."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 to nowa seria dużych modeli językowych, zaprojektowana w celu optymalizacji przetwarzania zadań instrukcyjnych."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 to nowa seria dużych modeli językowych, zaprojektowana w celu optymalizacji przetwarzania zadań instrukcyjnych."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Duży model j<PERSON><PERSON><PERSON>wy opracowany przez zespół Alibaba Cloud <PERSON>"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 to nowa seria dużych modeli językowych, charakteryzująca się mocniejszymi zdolnościami rozumienia i generowania."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 to nowa seria dużych modeli językowych, mająca na celu optymalizację przetwarzania zadań instruktażowych."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 to nowa seria dużych modeli językowych, zaprojektowana w celu optymalizacji przetwarzania zadań instrukcyjnych."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 to nowa seria dużych modeli językowych, mająca na celu optymalizację przetwarzania zadań instruktażowych."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder koncentruje się na pisaniu kodu."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct to najnowsza wersja serii dużych modeli językowych specyficznych dla kodu wydana przez <PERSON>. Model ten, oparty na Qwen2.5, zost<PERSON><PERSON> przeszkolony na 55 bilionach tokenów, znacznie poprawiając zdolności generowania kodu, wnioskowania i naprawy. Wzmacnia on nie tylko zdolności kodowania, ale także utrzymuje przewagę w zakresie matematyki i ogólnych umiejętności. Model ten stanowi bardziej kompleksową podstawę dla rzeczywistych zastosowań, takich jak inteligentne agenty kodowe."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct to wielomodalny model stwo<PERSON><PERSON> przez zespół Qwen2.5-V<PERSON>, kt<PERSON>ry jest częścią serii Qwen2.5-VL. Ten model nie tylko doskonale rozpoznaje obiekty, ale także analizuje tekst, w<PERSON><PERSON><PERSON>, i<PERSON><PERSON>, rysunki i układ w obrazach. Może działać jako inteligentny agent wizualny, który potrafi rozumować i dynamicznie sterować narzędziami, posiadając umiejętności korzystania z komputerów i telefonów. Ponadto, ten model może precyzyjnie lokalizować obiekty w obrazach i generować strukturalne wyjścia dla faktur, tabel i innych dokumentów. W porównaniu do poprzedniego modelu Qwen2-VL, ta wersja została dalej rozwinięta w zakresie umiejętności matematycznych i rozwiązywania problemów poprzez uczenie wzmacnianie, a styl odpowiedzi jest bardziej zgodny z preferencjami ludzkimi."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL to model językowo-wizualny z serii Qwen2.5. Ten model przyn<PERSON><PERSON> znaczące poprawy w wielu aspektach: posiada lepsze zdolności zrozumienia wizualnego, umożliwiaj<PERSON>c rozpoznawanie powszechnych obiektów, analizowanie tekstu, wykresów i układu; jako wizualny agent może wnioskować i dynamicznie kierować użyciem narzędzi; obsługuje zrozumienie filmów o długości przekraczającej 1 godzinę i łapanie kluczowych zdarzeń; może precyzyjnie lokalizować obiekty na obrazach poprzez generowanie ramki granicznej lub punktów; obsługuje generowanie danych strukturalnych, szczególnie przydatnych dla skanowanych danych, takich jak faktury i tabele."}, "Qwen/Qwen3-14B": {"description": "Qwen3 to nowa generacja modelu <PERSON>wen, która znacznie zwiększa zdolności w zakresie wnioskowania, og<PERSON><PERSON><PERSON>ń, agentów i wielojęzycz<PERSON>ści, osiągając wiodące w branży wyniki oraz wspierając przełączanie trybu myślenia."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 to nowa generacja modelu <PERSON>wen, która znacznie zwiększa zdolności w zakresie wnioskowania, og<PERSON><PERSON><PERSON>ń, agentów i wielojęzycz<PERSON>ści, osiągając wiodące w branży wyniki oraz wspierając przełączanie trybu myślenia."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 to flagowy model du<PERSON><PERSON> języka hybrydowe<PERSON> (MoE) z serii Qwen3, opracowany przez zespół Alibaba Cloud Tongyi Qianwen. Model posiada 235 miliardów parametrów ogółem, z 22 miliardami aktywowanymi podczas inferencji. Jest to zaktualizowana wersja trybu nie myślącego Qwen3-235B-A22B, skupiająca się na znaczącej poprawie w zakresie przestrzegania instrukcji, wnioskowania logicznego, rozumienia tekstu, matematyki, nauki, programowania i użycia narzędzi. Model rozszerza pokrycie wiedzy wielojęzycznej i lepiej dostosowuje się do preferencji użytkowników w zadaniach subiektywnych i otwartych, generując bardziej pomocne i wysokiej jakości teksty."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 to model z serii Qwen3 opracowany przez zesp<PERSON>ł Alibaba Tongyi Qianwen, skoncentrowany na złożonych zadaniach wymagających zaawansowanego wnioskowania. Model oparty na architekturze hybrydowych ekspertów (MoE) posiada 235 miliardów parametrów, z aktywacją około 22 miliardów parametrów na token, co pozwala na wysoką wydajność przy efektywności obliczeniowej. Jako model „myślący” osiąga czołowe wyniki w zadaniach wymagających wiedzy specjalistycznej, takich jak logika, matematyka, nauka, programowanie i testy akademickie. Ponadto wzmacnia zdolności ogólne, takie jak przestrzeganie instrukcji, użycie narzędzi i generowanie tekstu, oraz natywnie obsługuje kontekst o długości do 256K tokenów, co czyni go idealnym do głębokiego wnioskowania i pracy z długimi dokumentami."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 to nowa generacja modelu <PERSON>wen, która znacznie zwiększa zdolności w zakresie wnioskowania, og<PERSON><PERSON><PERSON>ń, agentów i wielojęzycz<PERSON>ści, osiągając wiodące w branży wyniki oraz wspierając przełączanie trybu myślenia."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 to zaktualizowana wersja modelu Qwen3-30B-A3B w trybie bez myślenia. Jest to model ekspertowy mieszany (MoE) z 30,5 miliardami parametrów ogółem i 3,3 miliardami parametrów aktywacyjnych. Model został znacząco ulepszony pod wieloma względami, w tym w zakresie przestrzegania instrukcji, rozumowania logicznego, rozumienia tekstu, matematyki, nauki, kodowania oraz korzystania z narzędzi. Ponadto osiągnął istotny postęp w pokryciu wiedzy wielojęzycznej oraz lepsze dopasowanie do preferencji użytkowników w zadaniach subiektywnych i otwartych, co pozwala generować bardziej pomocne odpowiedzi i teksty wyższej jakości. Dodatkowo zdolność rozumienia długich tekstów została zwiększona do 256K. Model ten obsługuje wyłącznie tryb bez myślenia i nie generuje tagów `<think></think>` w swoich odpowiedziach."}, "Qwen/Qwen3-32B": {"description": "Qwen3 to nowa generacja modelu <PERSON>wen, która znacznie zwiększa zdolności w zakresie wnioskowania, og<PERSON><PERSON><PERSON>ń, agentów i wielojęzycz<PERSON>ści, osiągając wiodące w branży wyniki oraz wspierając przełączanie trybu myślenia."}, "Qwen/Qwen3-8B": {"description": "Qwen3 to nowa generacja modelu <PERSON>wen, która znacznie zwiększa zdolności w zakresie wnioskowania, og<PERSON><PERSON><PERSON>ń, agentów i wielojęzycz<PERSON>ści, osiągając wiodące w branży wyniki oraz wspierając przełączanie trybu myślenia."}, "Qwen2-72B-Instruct": {"description": "Qwen2 to najnowsza seria modeli Qwen, obsługująca kontekst 128k. W porównaniu do obecnie najlepszych modeli open source, Qwen2-72B znacznie przewyższa w zakresie rozumienia języka naturalnego, wiedzy, kodowania, matematyki i wielu języków."}, "Qwen2-7B-Instruct": {"description": "Qwen2 to najnowsza seria modeli Qwen, która przewyższa najlepsze modele open source o podobnej skali, a nawet większe. Qwen2 7B osiągnęła znaczną przewagę w wielu testach, szczególnie w zakresie kodowania i rozumienia języka chińskiego."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B to potężny model językowo-wizualny, wspierający przetwarzanie multimodalne obrazów i tekstu, zdolny do precyzyjnego rozpoznawania treści obrazów i generowania odpowiednich opisów lub odpowiedzi."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct to model językowy z 14 miliardami parametrów, o doskonałej wyd<PERSON>ci, optymalizujący scenariusze w języku chińskim i wielojęzyczne, wspierający inteligentne odpowiedzi, generowanie treści i inne zastosowania."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct to model językowy z 32 miliardami parametrów, o zrównoważonej wydajności, optymalizujący scenariusze w języku chińskim i wielojęzyczne, wspierający inteligentne odpowiedzi, generowanie treści i inne zastosowania."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct obsługuje kontekst 16k, generując długie teksty przekraczające 8K. Wspiera wywołania funkcji i bezproblemową interakcję z systemami zewnętrznymi, znacznie zwiększając elastyczność i skalowalność. Wiedza modelu znacznie wzrosła, a jego zdolności w zakresie kodowania i matematyki uległy znacznemu poprawieniu, z obsługą ponad 29 języków."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct to model językowy z 7 miliardami parametrów, wspierający wywołania funkcji i bezproblemową interakcję z systemami zewnętrznymi, znacznie zwiększając elastyczność i skalowalność. Optymalizuje scenariusze w języku chińskim i wielojęzyczne, wspierając inteligentne odpowiedzi, generowanie treści i inne zastosowania."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct to model instrukcji programowania oparty na dużych wstępnych treningach, posiadający silne zdolności rozumienia i generowania kodu, zdolny do efektywnego przetwarzania różnych zadań programistycznych, szczególnie odpowiedni do inteligentnego pisania kodu, generowania skryptów automatycznych i rozwiązywania problemów programistycznych."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct to duży model j<PERSON>zy<PERSON>wy zaprojektowany specjalnie do generowania kodu, rozumienia kodu i efektywnych scenariuszy rozwoju, wykorzystujący wiodącą w branży skalę 32B parametrów, zdolny do zaspokojenia różnorodnych potrzeb programistycznych."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B to model MoE (eks<PERSON> mieszany), k<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON> „hybrydowy tryb rozumowania”, umożliwiający użytkownikom płynne przełączanie się między trybem myślenia a trybem bez myślenia. Obsługuje rozumienie i rozumowanie w 119 językach i dialektach oraz posiada zaawansowane możliwości wywoływania narzędzi. W testach porównawczych obejmujących zdolności ogólne, kodowanie, mate<PERSON><PERSON><PERSON>, wiel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wiedz<PERSON> i rozumowanie konkuruje z czołowymi modelami rynkowymi, takimi jak DeepSeek R1, OpenAI o1, o3-mini, Grok 3 oraz Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B to model <PERSON><PERSON><PERSON> (Dense Model), k<PERSON><PERSON><PERSON> w<PERSON> „hybrydowy tryb rozumowania”, umożliwiający użytkownikom płynne przełączanie się między trybem myślenia a trybem bez myślenia. Dzięki ulepszonej architekturze modelu, zwiększonej ilości danych treningowych oraz bardziej efektywnym metodom treningu, jego ogólna wydajność jest porównywalna z Qwen2.5-72B."}, "SenseChat": {"description": "Podstawowa wersja modelu (V4), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekstu 4K, silne zdolności ogólne."}, "SenseChat-128K": {"description": "Podstawowa wersja modelu (V4), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekstu 128K, doskonałe wyniki w zadaniach związanych z rozumieniem i generowaniem długich tekstów."}, "SenseChat-32K": {"description": "Podstawowa wersja modelu (V4), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekstu 32K, elastycznie stosowana w różnych scenariuszach."}, "SenseChat-5": {"description": "Najn<PERSON>za wersja modelu (V5.5), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekstu 128K, znacznie poprawione zdolności w zakresie rozumowania matematycznego, rozmów w języku angielskim, podążania za instrukcjami oraz rozumienia długich tekstów, dorównująca GPT-4o."}, "SenseChat-5-1202": {"description": "Oparty na najnowszej wersji V5.5, z wyraźnymi ulepszeniami w podstawowych zdolnościach w języku chińskim i angielskim, czacie, wied<PERSON> ścisłej i humanistycznej, p<PERSON><PERSON>, logice matematycznej oraz kontroli liczby słów."}, "SenseChat-5-Cantonese": {"description": "Długość kontekstu 32K, w rozumieniu rozmów w języku kantońskim przewyższa GPT-4, w wielu d<PERSON>, ta<PERSON><PERSON> jak w<PERSON>, r<PERSON><PERSON><PERSON><PERSON>, matematyka i programowanie, dorównuje GPT-4 Turbo."}, "SenseChat-5-beta": {"description": "Częściowo lepsza wydajność niż SenseCat-5-1202"}, "SenseChat-Character": {"description": "Standardowa wersja modelu, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekstu 8K, wysoka szybkość reakcji."}, "SenseChat-Character-Pro": {"description": "Zaawansowana wersja modelu, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekstu 32K, znacznie poprawione zdolności, obsługuje rozmowy w języku chińskim i angielskim."}, "SenseChat-Turbo": {"description": "Idealny do szybkich odpowiedzi i scenariuszy dostosowywania modelu."}, "SenseChat-Turbo-1202": {"description": "Jest to najnowsza wersja modelu o niskiej wadze, osiągająca ponad 90% możliwości pełnego modelu, znacznie obniżając koszty wnioskowania."}, "SenseChat-Vision": {"description": "Najnowsza wersja modelu (V5.5), o<PERSON>ług<PERSON>jąca wiele obrazów jako wej<PERSON>, w pełni optymalizuje podstawowe możliwości modelu, osiągając znaczną poprawę w rozpoznawaniu atrybutów obiektów, re<PERSON><PERSON><PERSON> przestrzennych, roz<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>ń, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> scen, roz<PERSON>znawaniu emocji, wnioskowaniu logicznym oraz generowaniu i rozumieniu tekstu."}, "SenseNova-V6-5-Pro": {"description": "Dzięki kompleksowej aktualizacji danych multimodalnych, językowych i rozumowania oraz optymalizacji strategii treningowej, nowy model osiągnął znaczące ulepszenia w zakresie rozumowania multimodalnego i uniwersalnego przestrzegania instrukcji. Obsługuje kontekst o długości do 128k i wykazuje doskonałe wyniki w specjalistycznych zadaniach, takich jak OCR oraz rozpoznawanie IP w turystyce i kulturze."}, "SenseNova-V6-5-Turbo": {"description": "Dzięki kompleksowej aktualizacji danych multimodalnych, językowych i rozumowania oraz optymalizacji strategii treningowej, nowy model osiągnął znaczące ulepszenia w zakresie rozumowania multimodalnego i uniwersalnego przestrzegania instrukcji. Obsługuje kontekst o długości do 128k i wykazuje doskonałe wyniki w specjalistycznych zadaniach, takich jak OCR oraz rozpoznawanie IP w turystyce i kulturze."}, "SenseNova-V6-Pro": {"description": "Osiąga natywną jedność zdolności do przetwarzania obrazów, tekstów i wideo, przełamując tradycyjne ograniczenia rozdzielnych modalności, zdobywając podwójne mistrzostwo w ocenach OpenCompass i SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "Łączy głębokie rozumienie wizualne i językowe, umożliwiając powolne myślenie i głęboką analizę, prezentując pełny proces myślowy."}, "SenseNova-V6-Turbo": {"description": "Osiąga natywną jedność zdolności do przetwarzania obrazów, tekstów i wideo, przełamując tradycyjne ograniczenia rozdzielnych modalności, prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w kluczowych wymiarach, takich jak podstawowe umiejętności multimodalne i językowe, oraz osiągając wysokie wyniki w wielu testach, wielokrotnie plasując się w czołówce krajowej i międzynarodowej."}, "Skylark2-lite-8k": {"description": "Model drugiej generacji Skylark (Skylark2) o wysokiej szybkości reakcji, odpowiedni do scenariuszy wymagających wysokiej reaktywności, wrażliwych na koszty, z mniejszymi wymaganiami co do precyzji modelu, z długością okna kontekstowego 8k."}, "Skylark2-pro-32k": {"description": "Model drugiej generacji Skylark (Skylark2) o wysokiej precyzji, odpowiedni do bardziej złożonych scenariuszy generowania tekstu, takich jak generowanie treści w profesjonalny<PERSON> dzied<PERSON>, tworzenie powieści oraz tłumaczenia wysokiej jak<PERSON>, z długością okna kontekstowego 32k."}, "Skylark2-pro-4k": {"description": "Model drugiej generacji Skylark (Skylark2) o wysokiej precyzji, odpowiedni do bardziej złożonych scenariuszy generowania tekstu, takich jak generowanie treści w profesjonalny<PERSON> dzied<PERSON>, tworzenie powieści oraz tłumaczenia wysokiej jak<PERSON>, z długością okna kontekstowego 4k."}, "Skylark2-pro-character-4k": {"description": "Model drugiej generacji <PERSON>rk (Skylark2) z doskonałymi umiejętnościami w odgrywaniu ról i czatowaniu. Doskonale reaguje na prompty użytkowników, odgrywają<PERSON> różne role w naturalny sposób, idealny do budowy chatbotów, wirtualnych asystentów i obsługi klienta online, cechujący się wysoką szybkością reakcji."}, "Skylark2-pro-turbo-8k": {"description": "Model drugiej <PERSON> (Skylark2) z szybszym wnioskowaniem i niższymi kosztami, z długością okna kontekstowego 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 to nowa generacja otwartego modelu z serii GLM, posiadająca 32 miliardy parametrów. Model ten osiąga wyniki porównywalne z serią GPT OpenAI i serią V3/R1 DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 to mały model z serii GLM, mający 9 miliardów parametrów. Model ten dziedziczy cechy technologiczne serii GLM-4-32B, ale oferuje lżejsze opcje wdrożeniowe. <PERSON><PERSON> r<PERSON>, GLM-4-9B-0414 nadal wykazuje doskonałe zdolności w generowaniu kodu, projektowaniu stron internetowych, generowaniu grafiki SVG i pisaniu opartym na wyszukiwaniu."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking to otwarty model wizualno<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (VLM) opracowany wspólnie przez Zhipu AI i Laboratorium KEG Uniwersytetu Tsinghua, zaprojektowany do obsługi złożonych zadań poznawczych wielomodalnych. Model opiera się na bazowym modelu GLM-4-9B-0414 i znacząco poprawia zdolności wnioskowania międzymodalnego oraz stabilność dzięki wprowadzeniu mechanizmu rozumowania „łańcucha myślowego” (Chain-of-Thought) oraz zastosowaniu strategii uczenia ze wzmocnieniem."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 to model wnioskowania z głęboką zdolnością myślenia. Model ten oparty jest na GLM-4-32B-0414, rozwinięty poprzez zimny start i rozszerzone uczenie przez wzmocnienie, a także przeszedł dalsze szkolenie w zadaniach matematycznych, kodowania i logiki. W porównaniu do modelu bazowego, GLM-Z1-32B-0414 znacznie poprawił zdolności matematyczne i umiejętność rozwiązywania złożonych zadań."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 to mały model z serii GLM, maj<PERSON>cy tylko 9 miliardów parametrów, ale zachowujący tradycję ot<PERSON>ła, jednocześnie wykazując zdumiewające zdolności. <PERSON><PERSON> m<PERSON> rozmiarów, model ten nadal osiąga doskonałe wyniki w wnioskowaniu matematycznym i ogólnych zadaniach, a jego ogólna wydajność jest na czołowej pozycji wśród modeli o podobnej wielkości."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 to model głębokiego wnioskowania z zdolnością do refleksji (konkurujący z Deep Research OpenAI). W przeciwieństwie do typowych modeli głębokiego myślenia, model refleksyjny stosuje dłuższy czas głębokiego myślenia do rozwiązywania bardziej otwartych i złożonych problemów."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B to o<PERSON><PERSON><PERSON>, oferująca zoptymalizowane doświadczenie dialogowe dla aplikacji konwersacyjnych."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B to pier<PERSON><PERSON> model wnioskowania z długim kontekstem (LRM) wytrenowany z użyciem uczenia ze wzmocnieniem, zoptymalizowany pod kątem zadań wnioskowania na długich tekstach. Model osiąga stabilne przejście od krótkiego do długiego kontekstu dzięki progresywnemu rozszerzaniu kontekstu w ramach uczenia ze wzmocnieniem. W siedmiu benchmarkach dotyczących pytań i odpowiedzi na długich dokumentach QwenLong-L1-32B przewyższa flagowe modele takie jak OpenAI-o3-mini i Qwen3-235B-A22B, osiągając wydajność porównywalną z Claude-3.7-Sonnet-Thinking. Model jest szczególnie silny w złożonych zadaniach matematycznego, logicznego i wieloetapowego wnioskowania."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, zachow<PERSON>j<PERSON>c doskonałe ogólne zdolności językowe oryginalnej serii modeli, znacznie poprawił zdolności logiczne i kodowania dzięki dodatkowym treningom na 500 miliardach wysokiej jakości tokenów."}, "abab5.5-chat": {"description": "Skierowany do scenariuszy produkcyjnych, wspierający przetwarzanie złożonych zadań i efektywne generowanie tekstu, odpowiedni do zastosowań w profesjonalnych dziedzinach."}, "abab5.5s-chat": {"description": "Zaprojektowany specjalnie do scenariuszy dialogowych w języku chińskim, oferujący wysokiej jakości generowanie dialogów w języku chińskim, odpowiedni do różnych zastosowań."}, "abab6.5g-chat": {"description": "Zaprojektowany specjalnie do dialogów z wielojęzycznymi postaciami, wspierający wysokiej jakości generowanie dialogów w języku angielskim i innych językach."}, "abab6.5s-chat": {"description": "Odpowiedni do szerokiego zakresu zadań przetwarzania języka naturalnego, w tym generowania tekstu, systemów dialogowych itp."}, "abab6.5t-chat": {"description": "Optymalizowany do scenariuszy dialogowych w języku chińskim, oferujący płynne i zgodne z chińskimi zwyczajami generowanie dialogów."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 to <PERSON><PERSON><PERSON><PERSON>wany model <PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> został zoptymalizowany dzięki uczeniu przez wzmocnienie i danym z zimnego startu, oferując doskonałe możliwości wnioskowania, matematyki i programowania."}, "accounts/fireworks/models/deepseek-v3": {"description": "Potężny model j<PERSON><PERSON><PERSON><PERSON> Mixture-of-Experts (MoE) oferowany przez Deepseek, z całkowitą liczbą parametrów wynoszącą 671 miliardów, aktywującym 37 miliardów parametrów na każdy token."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Model Llama 3 70B Instruct, zaprojektowany do wielojęzycznych dialogów i rozumienia języka naturalnego, przewyższa większość konkurencyjnych modeli."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Model Llama 3 8B Instruct, zoptymalizowany do dialogów i zadań wielojęzycznych, ofer<PERSON>je doskonałe i efektywne osiągi."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Model Llama 3 8B Instruct (wersja HF), zgodny z wynikami oficjalnej <PERSON>acji, zapewnia wysoką spójność i kompatybilność międzyplatformową."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Model Llama 3.1 405B Instruct, z ogromną liczbą parametrów, idealny do złożonych zadań i śledzenia poleceń w scenariuszach o dużym obciążeniu."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Model Llama 3.1 70B Instruct ofer<PERSON>je doskonałe możliwości rozumienia i generowania języka, idealny do zadań dialogowych i analitycznych."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Model Llama 3.1 8B Instruct, zoptymalizowany do wielojęzycznych dialogów, potrafi przewyższyć większość modeli open source i closed source w powszechnych standardach branżowych."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Model wnioskowania wizualnego z 11B parametrów od Meta. Model zoptymalizowany do rozpoznawania wizualnego, wnioskowania obrazów, opisywania obrazów oraz odpowiadania na ogólne pytania dotyczące obrazów. Model potrafi rozumi<PERSON> dane wizualne, takie jak wykresy i grafiki, a dzięki generowaniu tekstowych opisów szczegółów obrazów, łączy wizję z językiem."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Model instruktażowy Llama 3.2 3B to lekki model wielojęzyczny zaprezentowany przez Meta. Zaprojektowany, aby <PERSON><PERSON><PERSON> w<PERSON>, oferując znaczące usprawnienia w opóźnieniu i kosztach w porównaniu do większych modeli. Przykładowe przypadki użycia tego modelu obejmują zapytania i przepisanie sugestii oraz pomoc w pisaniu."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Model wnioskowania wizualnego z 90B parametrów od Meta. Model zoptymalizowany do rozpoznawania wizualnego, wnioskowania obrazów, opisywania obrazów oraz odpowiadania na ogólne pytania dotyczące obrazów. Model potrafi rozumi<PERSON> dane wizualne, takie jak wykresy i grafiki, a dzięki generowaniu tekstowych opisów szczegółów obrazów, łączy wizję z językiem."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct to zaktualizowana wersja Llama 3.1 70B z grudnia. Model ten został ulepszony w oparciu o Llama 3.1 70B (wydany w lipcu 2024), wzmacniając możliwości wywoływania narzędzi, wsparcie dla tekstów w wielu j<PERSON>zy<PERSON>, a także umiejętności matematyczne i programistyczne. Model osiągnął wiodący w branży poziom w zakresie wnioskowania, matematyki i przestrzegania instrukcji, oferując wydajność porównywalną z 3.1 405B, jednocześnie zapewniając znaczące korzyści w zakresie szybkości i kosztów."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Model z 24 miliardami parametrów, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zaawansowane możliwości porównywalne z większymi modelami."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Model Mixtral MoE 8x22B Instruct, z dużą liczbą parametrów i architekturą wielu ekspertów, kompleksowo wspierający efektywne przetwarzanie złożonych zadań."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Model Mixtral MoE 8x7B Instruct, architektura wielu ekspertów, oferująca efektywne śledzenie i wykonanie poleceń."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "Model MythoMax L2 13B, łączący nowatorskie techniki łączenia, doskonały w narracji i odgrywaniu ról."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Model Phi 3 Vision Instruct, lekki model multimodalny, zdolny do przetwarzania złożonych informacji wizualnych i tekstowych, z silnymi zdolnościami wnioskowania."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "Model QwQ to eksperymentalny model badawczy opracowany przez z<PERSON><PERSON><PERSON><PERSON>, skoncentrowany na zwiększeniu zdolności wnioskowania AI."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Wersja 72B modelu Qwen-VL to najnowszy owoc iteracji Ali<PERSON>ba, reprezentuj<PERSON><PERSON> innowacje z ostatniego roku."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 to seria modeli językowych opracowana przez zespół Qwen na chmurze Alibaba, która zawiera jedynie dekodery. Modele te występują w różnych rozmiarach, w tym 0.5B, 1.5B, 3B, 7B, 14B, 32B i 72B, i oferują dwie wersje: bazową (base) i instruktażową (instruct)."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct to najnowsza wersja serii dużych modeli językowych specyficznych dla kodu wydana przez Ali<PERSON> Cloud. Model ten, oparty na Qwen2.5, z<PERSON><PERSON><PERSON> przeszkolony na 55 bilionach tokenów, znacznie poprawiając zdolności generowania kodu, wnioskowania i naprawy. Wzmacnia on nie tylko zdolności kodowania, ale także utrzymuje przewagę w zakresie matematyki i ogólnych umiejętności. Model ten stanowi bardziej kompleksową podstawę dla rzeczywistych zastosowań, takich jak inteligentne agenty kodowe."}, "accounts/yi-01-ai/models/yi-large": {"description": "Model Yi-Large, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe możliwości przetwarzania wielojęzycznego, nadający się do różnych zadań generowania i rozumienia języka."}, "ai21-jamba-1.5-large": {"description": "Model wielojęzyczny z 398 miliardami parametrów (94 miliardy aktywnych), oferujący okno kontekstowe o długości 256K, wywoływanie funkcji, strukturalne wyjście i generację opartą na kontekście."}, "ai21-jamba-1.5-mini": {"description": "Model wielojęzyczny z 52 miliardami parametrów (12 miliardów aktywnych), oferujący okno kontekstowe o długości 256K, wywoływanie funkcji, strukturalne wyjście i generację opartą na kontekście."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Model wielojęzyczny o 398 miliardach parametrów (94 miliardy aktywnych), oferujący okno kontekstowe o długości 256K, wywoływanie funkcji, strukturalne wyjście oraz generowanie oparte na faktach."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Model wielojęzyczny o 52 miliardach parametrów (12 miliardów aktywnych), oferujący okno kontekstowe o długości 256K, wywoływanie funkcji, strukturalne wyjście oraz generowanie oparte na faktach."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Sonnet podnosi standardy branżowe, przew<PERSON>ż<PERSON><PERSON>ąc modele konkurencji oraz Claude 3 Opus, osiągając doskonałe wyniki w szerokim zakresie ocen, jednocześnie oferując szybkość i koszty na poziomie naszych modeli średniej klasy."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet podnosi standardy branżowe, przew<PERSON>ż<PERSON><PERSON>ąc modele konkurencji oraz Claude 3 Opus, wykaz<PERSON><PERSON><PERSON>c doskonałe wyniki w szerokich ocenach, jednocześnie oferując prędkość i koszty naszych modeli średniego poziomu."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku to najsz<PERSON>bszy i najbardziej kompaktowy model o<PERSON>, of<PERSON><PERSON><PERSON><PERSON><PERSON> niemal natychmiastową szybkość odpowiedzi. Może szybko odpowiadać na proste zapytania i prośby. Klienci będą mogli budować płynne doświadczenia AI, które naśladują interakcje międzyludzkie. Claude 3 Haiku może przetwarzać obrazy i zwracać wyjścia tekstowe, z oknem kontekstowym wynoszącym 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus to najpotężniejszy model <PERSON> o<PERSON>, z najnowocześniejszymi osiągami w wysoko złożonych zadaniach. Może obsługiwać otwarte podpowiedzi i nieznane scenar<PERSON>, ofer<PERSON><PERSON><PERSON><PERSON> doskonałą płynność i ludzkie zdolności rozumienia. Claude 3 Opus pokazuje granice możliwości generatywnej AI. Claude 3 Opus może przetwarzać obrazy i zwracać wyjścia tekstowe, z oknem kontekstowym wynoszącym 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet od Anthropic osiąga idealną równowagę między inteligencją a szybkością — szczególnie odpowiedni do obciążeń roboczych w przedsiębiorstwach. Oferuje maksymalną użyteczność po niższej cenie niż konkurencja i został zaprojektowany jako niezawodny, wytrzymały model główny, odpowiedni do skalowalnych wdrożeń AI. Claude 3 Sonnet może przetwarzać obrazy i zwracać wyjścia tekstowe, z oknem kontekstowym wynoszącym 200K."}, "anthropic.claude-instant-v1": {"description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> model, <PERSON><PERSON><PERSON><PERSON> w<PERSON>ż jest bard<PERSON>, może obsługiwać szereg zadań, w tym codzienne rozmowy, anal<PERSON><PERSON> tekstu, podsumowania i pytania dotyczące dokumentów."}, "anthropic.claude-v2": {"description": "Model Anthrop<PERSON> wykazuje wysokie zdolności w szerokim zakresie zadań, od złożonych rozmów i generowania treści kreatywnych po szczegółowe przestrzeganie instrukcji."}, "anthropic.claude-v2:1": {"description": "Zaktualizowana wersja Claude 2, z podwójnym oknem kontekstowym oraz poprawioną niezawodnością, wskaźnikiem halucynacji i dokładnością opartą na dowodach w kontekście długich dokumentów i RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku to najszybszy i najbardziej kompaktowy model <PERSON><PERSON><PERSON>, zaprojektowany do niemal natychmiastowych odpowiedzi. Oferuje szybkie i dokładne wyniki w ukierunkowanych zadaniach."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus to najpotężniejszy model <PERSON><PERSON><PERSON> do obsługi wysoce złożonych zadań. Wyróżnia się doskonałymi osiągami, inteligencją, płynnością i zdolnością rozumienia."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku to najsz<PERSON>bszy model nowej generacji od Anthropic. W porównaniu do Claude 3 Haiku, Claude 3.5 Haiku wykazuje poprawę w różnych umiejętnościach i przewyższa największy model poprz<PERSON><PERSON>j generacji, Claude 3 Opus, w wielu testach inteligencji."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet oferuje możliwości przewyższające Opus oraz szybsze tempo niż Sonnet, zachow<PERSON><PERSON><PERSON><PERSON> tę samą cenę. Sonnet szczególnie dobrze radzi sobie z programowaniem, nauk<PERSON> o danych, przetwarzaniem wizualnym i zadaniami agenta."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet to najinteligentniejszy model s<PERSON><PERSON><PERSON><PERSON> p<PERSON>, a także pierwszy na rynku model mi<PERSON><PERSON><PERSON>. Claude 3.7 Sonnet potrafi generować niemal natychmiastowe odpowiedzi lub w<PERSON><PERSON><PERSON><PERSON><PERSON>, krok po kroku <PERSON>, kt<PERSON>re użytkownicy mogą wyraźnie obserwować. Sonnet szczególnie dobrze radzi sobie z programowaniem, nauk<PERSON> o danych, przetwarzaniem wizualnym oraz zadaniami agenta."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 to naj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON> do obsługi wysoce złożonych zadań. Wyróżnia się doskonałą wydajnością, inteligencją, płynnością i zdolnością rozumienia."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 potrafi generow<PERSON>ć niemal natychmiastowe odpowiedzi lub w<PERSON><PERSON><PERSON><PERSON><PERSON>, stopniowe rozumowanie, które użytkownicy mogą wyraźnie obserwować. Użytkownicy API mają również precyzyjną kontrolę nad czasem rozmyślania modelu."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B to <PERSON><PERSON><PERSON>, du<PERSON>y model językowy o 72 miliardach parametrów i 16 miliardach aktywowanych parametrów, oparty na architekturze grupowanych ekspertów (MoGE). W fazie wyboru ekspertów model grupuje ekspertów i ogranicza aktywację tokenów do równej liczby ekspertów w każdej grupie, co zapewnia równomierne obciążenie ekspertów i znacznie poprawia efektywność wdrożenia modelu na platformie Ascend."}, "aya": {"description": "Aya 23 to model wielojęzyczny wydany przez <PERSON>here, wspierający 23 j<PERSON><PERSON><PERSON>, ułatwiaj<PERSON>cy różnorodne zastosowania językowe."}, "aya:35b": {"description": "Aya 23 to model wielojęzyczny wydany przez <PERSON>here, wspierający 23 j<PERSON><PERSON><PERSON>, ułatwiaj<PERSON>cy różnorodne zastosowania językowe."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B to otwarty model j<PERSON><PERSON><PERSON><PERSON> stworzony przez Baichuan Intelligence, zawierający 13 miliardów parametrów, który osiągnął najlepsze wyniki w swojej klasie w autorytatywnych benchmarkach w języku chińskim i angielskim."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B to duży model językowy opracowany przez firmę Baidu, oparty na hybrydowej architekturze ekspertów (MoE). Model ma 300 miliardów parametrów, ale podczas inferencji aktywuje tylko 47 miliardów parametrów na token, co zapewnia doskonałą wydajność przy efektywności obliczeniowej. Jako jeden z kluczowych modeli serii ERNIE 4.5, wykazuje znakomite zdolności w rozumieniu tekstu, generowaniu, wnioskowaniu i programowaniu. Model wykorzystuje innowacyjną metodę pretrenowania multimodalnego heterogenicznego MoE, łącząc trening tekstu i wizji, co skutecznie zwiększa jego zdolności, zwłaszcza w zakresie przestrzegania instrukcji i pamięci wiedzy o świecie."}, "c4ai-aya-expanse-32b": {"description": "<PERSON><PERSON> Expanse to model wielojęzyczny o wysokiej wydajności 32B, zaprojektowany w celu wyzwania wydajności modeli jednolanguage poprzez innowacje w zakresie dostosowywania instrukcji, arbitra<PERSON><PERSON> danych, treningu preferencji i łączenia modeli. Obsługuje 23 języki."}, "c4ai-aya-expanse-8b": {"description": "<PERSON><PERSON> Expanse to model wielojęzyczny o wysokiej wydajności 8B, zaprojektowany w celu wyzwania wydajności modeli jednolanguage poprzez innowacje w zakresie dostosowywania instrukcji, arbitra<PERSON><PERSON> danych, treningu preferencji i łączenia modeli. Obsługuje 23 języki."}, "c4ai-aya-vision-32b": {"description": "Aya Vision to zaawansowany model w<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> osi<PERSON>ga doskonałe wyniki w wielu kluczowych benchmarkach dotyczących zdolności językowych, tekstowych i obrazowych. Obsługuje 23 języki. Ta wersja z 32 miliardami parametrów koncentruje się na najnowocześniejszej wydajności wielojęzycznej."}, "c4ai-aya-vision-8b": {"description": "Aya Vision to zaawansowany model w<PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ry osiąga doskonałe wyniki w wielu kluczowych benchmarkach dotyczących zdolności językowych, tekstowych i obrazowych. Ta wersja z 8 miliardami parametrów koncentruje się na niskiej latencji i najlepszej wydajności."}, "charglm-3": {"description": "CharGLM-3 zaprojektowany z myślą o odgrywaniu ról i emocjonalnym towarzyszeniu, obsługujący ultra-długą pamięć wielokrotną i spersonalizowane dialogi, z szerokim zakresem zastosowań."}, "charglm-4": {"description": "CharGLM-4 zaprojektowany z myślą o odgrywaniu ról i emocjonalnym towarzyszeniu, wspierający długotrwałą pamięć i spersonalizowane rozmowy, z szerokim zakresem zastosowań."}, "chatglm3": {"description": "ChatGLM3 to zamknięty model opracowany przez AI ZhiPu i KEG Laboratorium z Politechniki Tsinghua, który przeszedł wstępne treningi na ogromnej liczbie identyfikatorów chińskich i angielskich oraz trening zgodności z preferencjami ludzkimi. W porównaniu do pierwszej generacji modelu, ChatGLM3 osiągnął poprawę o 16%, 36% i 280% w testach MMLU, C-Eval i GSM8K, oraz zajął pierwsze miejsce na liście chińskich zadań C-Eval. Jest odpowiedni do zastosowań, które wymagają wysokiej wiedzy, zdolności wnioskowania i kreatywności, takich jak tworzenie tekstów reklamowych, pisarstwo powieści, pisarstwo naukowe i generowanie kodu."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base to najnowszy model z serii ChatGLM opracowany przez ZhiPu, o skali 6 miliardów parametrów, dostępny jako oprogramowanie open source."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o to <PERSON>z<PERSON> model, który jest na bieżąco aktualizowany, aby utr<PERSON><PERSON><PERSON> najnowszą wersję. Łączy potężne zdolności rozumienia i generowania języka, co czyni go odpowiednim do zastosowań na dużą skalę, w tym obsługi klienta, edukacji i wsparcia technicznego."}, "claude-2.0": {"description": "Claude 2 oferuje postępy w kluczowych możliwościach dla przedsiębiorstw, w tym wiodącą w branży kontekst 200K tokenów, znacznie zmniejszającą cz<PERSON><PERSON><PERSON>ć występowania halucynacji modelu, systemowe podpowiedzi oraz nową funkcję testową: wywołania narzędzi."}, "claude-2.1": {"description": "Claude 2 oferuje postępy w kluczowych możliwościach dla przedsiębiorstw, w tym wiodącą w branży kontekst 200K tokenów, znacznie zmniejszającą cz<PERSON><PERSON><PERSON>ć występowania halucynacji modelu, systemowe podpowiedzi oraz nową funkcję testową: wywołania narzędzi."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku to najsz<PERSON>bszy model następnej generacji od Anthropic. W porównaniu do Claude 3 Haiku, Claude 3.5 Haiku wykazuje poprawę w różnych umiejętnościach i przewyższa największy model pop<PERSON><PERSON><PERSON><PERSON> generacji, Claude 3 Opus, w wielu testach inteligencji."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet oferuje możliwości przewyższające Opus oraz szybsze tempo niż Sonnet, przy zachowaniu tej samej ceny. Sonnet szczególnie dobrze radzi sobie z programowaniem, na<PERSON><PERSON>, przetwarzaniem wizualnym i zadaniami agenta."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet oferuje możliwości wykraczające poza Opus oraz szybsze działanie niż Sonnet, zachow<PERSON>j<PERSON>c jednocześnie tę samą cenę. Sonnet jest szczególnie uzdolniony w programowaniu, na<PERSON><PERSON>, przetwarzaniu wizualnym oraz zadaniach związanych z pośrednictwem."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet to na<PERSON><PERSON><PERSON> model <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>łe wyniki w szerokim zakresie zadań, w tym generowanie treś<PERSON>, rozumienie języka naturalnego i przestrzeganie instrukcji. Claude 3.7 Sonnet jest s<PERSON><PERSON>, niezawodny i ekonomiczny, co sprawia, że jest idealny do zastosowań produkcyjnych."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku to najszybszy i najbardziej kompaktowy model <PERSON><PERSON><PERSON>, zaprojektowany do osiągania niemal natychmiastowych odpowiedzi. Oferuje szybkie i dokładne wyniki w ukierunkowanych zadaniach."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus to najpotężniejszy model <PERSON><PERSON><PERSON> do przetwarzania wysoce złożonych zadań. Wykazuje doskonałe osiągi w zakresie wydajności, inteligencji, płynności i zrozumienia."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet zapewnia idealną równowagę między inteligencją a szybkością dla obciążeń roboczych w przedsiębiorstwach. Oferuje maksymalną użyteczność przy niższej cenie, jest niezawodny i odpowiedni do dużych wdrożeń."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 to naj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON>, zaprojektowany do obsługi wysoce złożonych zadań. Wyróżnia się doskonałymi osiągami, inteligencją, płynnością i zdolnością rozumienia."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet może generować niemal natychmiastowe odpowiedzi lub w<PERSON><PERSON><PERSON><PERSON>, stopniowe myślenie, co pozwala użytkownikom wyraźnie zobaczyć te procesy. Użytkownicy API mogą również szczegółowo kontrolować czas myślenia modelu."}, "codegeex-4": {"description": "CodeGeeX-4 to potężny asystent programowania AI, obsługujący inteligentne pytania i odpowiedzi oraz uzupełnianie kodu w różnych językach programowania, zwiększając wydajność programistów."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B to model generowania kodu w wiel<PERSON>, kt<PERSON><PERSON> obsługuje kompleksowe funkcje, w tym uzupeł<PERSON>ie i generowanie kodu, interpreter kodu, wyszukiwanie w sieci, wywołania funkcji oraz pytania i odpowiedzi na poziomie repozytoriów, obejmując różne scenariusze rozwoju oprogramowania. Jest to wiodący model generowania kodu z mniej niż 10B parametrów."}, "codegemma": {"description": "CodeGemma to lekki model <PERSON><PERSON><PERSON><PERSON><PERSON>, specjalizuj<PERSON><PERSON> się w różnych zadaniach programistycznych, wspierający szybkie iteracje i integrację."}, "codegemma:2b": {"description": "CodeGemma to lekki model <PERSON><PERSON><PERSON><PERSON><PERSON>, specjalizuj<PERSON><PERSON> się w różnych zadaniach programistycznych, wspierający szybkie iteracje i integrację."}, "codellama": {"description": "Code Llama to model LLM skoncentrowany na generowaniu i dyskusji kodu, łączący wsparcie dla szerokiego zakresu języków programowania, odpowiedni do środowisk deweloperskich."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama to LLM skoncentrowany na generowaniu i omawianiu kodu, z szerokim wsparciem dla różnych języków programowania, odpowiedni dla środowisk deweloperskich."}, "codellama:13b": {"description": "Code Llama to model LLM skoncentrowany na generowaniu i dyskusji kodu, łączący wsparcie dla szerokiego zakresu języków programowania, odpowiedni do środowisk deweloperskich."}, "codellama:34b": {"description": "Code Llama to model LLM skoncentrowany na generowaniu i dyskusji kodu, łączący wsparcie dla szerokiego zakresu języków programowania, odpowiedni do środowisk deweloperskich."}, "codellama:70b": {"description": "Code Llama to model LLM skoncentrowany na generowaniu i dyskusji kodu, łączący wsparcie dla szerokiego zakresu języków programowania, odpowiedni do środowisk deweloperskich."}, "codeqwen": {"description": "CodeQwen1.5 to du<PERSON>y model językowy wytrenowany na dużej ilości danych kodowych, zaprojektowany do rozwiązywania złożonych zadań programistycznych."}, "codestral": {"description": "Codestral to pier<PERSON>zy model kodowy Mistral AI, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>łe wsparcie dla zadań generowania kodu."}, "codestral-latest": {"description": "Codestral to nowoczesny model generacyjny skoncentrowany na generowaniu kodu, zoptymalizowany do zadań wypełniania i uzupełniania kodu."}, "codex-mini-latest": {"description": "codex-mini-latest to wersja dostrojona o4-mini, specjalnie zaprojektowana do Codex CLI. Do bezpośredniego użycia przez API zalecamy rozpoczęcie od gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B to model zaprojektowany do przestrzegania instrukcji, dialogów i programowania."}, "cogview-4": {"description": "CogView-4 to <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>źródłowy model generowania obrazów tekstowych firmy Zhipu, kt<PERSON>ry obsługuje generowanie znaków chińskich. Model oferuje kompleksowe ulepszenia w zakresie rozumienia semantycznego, jakości generowanych obrazów oraz zdolności generowania tekstu w języku chińskim i angielskim. Obsługuje dwujęzyczne wejście w dowolnej długości i potrafi generować obrazy o dowolnej rozdzielczości w określonym zakresie."}, "cohere-command-r": {"description": "Command R to skalowalny model generatywny, który koncentruje się na RAG i użyciu narzędzi, aby umożliwić AI na skalę produkcyjną dla przedsiębiorstw."}, "cohere-command-r-plus": {"description": "Command R+ to model zoptymalizowany pod kątem RAG, zaprojektowany do obsługi obciążeń roboczych na poziomie przedsiębiorstwa."}, "cohere/Cohere-command-r": {"description": "Command R to skalowalny model generatywny zaprojektowany do zastosowań RAG i narzędziowych, umożliwiający firmom wdrożenie AI na poziomie produkcyjnym."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ to zaawansowany model zoptymalizowany pod kątem RAG, stworzony do obsługi obciążeń na poziomie przedsiębiorstwa."}, "command": {"description": "Model konwer<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> prz<PERSON>rz<PERSON>a instrukcji, oferujący wysoką jakość i niezawodność w zadaniach językowych, a także dłuższą długość kontekstu w porównaniu do naszych podstawowych modeli generacyjnych."}, "command-a-03-2025": {"description": "Command A to nasz najsilniej<PERSON><PERSON> model, który do tej pory osiągnął najlepsze wyniki, doskonale sprawdzający się w zastosowaniach narzędziowych, agentowych, generacji wzbogaconej o wyszukiwanie (RAG) oraz w kontekście wielojęzycznym. Command A ma długość kontekstu 256K, działa na zaledwie dwóch GPU i w porównaniu do Command R+ 08-2024 zwiększa wydajność o 150%."}, "command-light": {"description": "Mniejsza i szybsza wersja Command, niemal równie potężna, ale szybsza."}, "command-light-nightly": {"description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> czas między wydaniami głównych wersji, wprowadziliśmy nocną wersję modelu Command. Dla serii command-light ta wersja nazywa się command-light-nightly. <PERSON><PERSON><PERSON>, że command-light-nightly to na<PERSON><PERSON><PERSON>, najbardziej eksperymentalna i (możliwie) niestabilna wersja. Wersje nocne są regularnie aktualizowane i nie są zapowiadane z wyprzedzeniem, dlatego nie zaleca się ich używania w środowisku produkcyjnym."}, "command-nightly": {"description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> czas między wydaniami głównych wersji, wprowadziliśmy nocną wersję modelu Command. Dla serii Command ta wersja nazywa się command-cightly. <PERSON><PERSON><PERSON>, że command-nightly to na<PERSON><PERSON><PERSON>, najbardziej eksperymentalna i (możliwie) niestabilna wersja. Wersje nocne są regularnie aktualizowane i nie są zapowiadane z wyprzedzeniem, dlatego nie zaleca się ich używania w środowisku produkcyjnym."}, "command-r": {"description": "Command R to LLM zoptymalizowany do dialogów i zadań z długim kontekstem, szczególnie odpowiedni do dynamicznej interakcji i zarządzania wiedzą."}, "command-r-03-2024": {"description": "Command R to model konwer<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> przestrz<PERSON>a instrukcji, oferujący wyższą jakość i niezawodność w zadaniach językowych, a także dłuższą długość kontekstu w porównaniu do wcześniejszych modeli. <PERSON>że być używany w złożonych przepływach pracy, takich jak generacja kodu, generacja wzbogacona o wyszukiwanie (RAG), korzystanie z narzędzi i agentów."}, "command-r-08-2024": {"description": "command-r-08-2024 to zaktualizowana wersja modelu Command R, wydana w sierpniu 2024 roku."}, "command-r-plus": {"description": "Command R+ to model językowy o wysokiej wydajności, zaprojektowany z myślą o rzeczywistych scenariuszach biznesowych i złożonych zastosowaniach."}, "command-r-plus-04-2024": {"description": "Command R+ to model k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> przestrz<PERSON>a instrukcji, oferujący wyższą jakoś<PERSON> i niezawodność w zadaniach językowych, a także dłuższą długość kontekstu w porównaniu do wcześniejszych modeli. Jest najlepiej dostosowany do złożonych przepływów pracy RAG i wieloetapowego korzystania z narzędzi."}, "command-r-plus-08-2024": {"description": "Command R+ to model konwersacyjny przestrzegający instrukcji, oferujący wyższą jakoś<PERSON> i niezawodność w zadaniach językowych, a także dłuższy kontekst w porównaniu do wcześniejszych modeli. Najlepiej sprawdza się w złożonych przepływach pracy RAG i wieloetapowym korzystaniu z narzędzi."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 to mała i wydajna zaktualizowana wersja, wydana w grudniu 2024 roku. Doskonale sprawdza się w zadaniach wymagających złożonego rozumowania i wieloetapowego przetwarzania, takich jak RAG, korzystanie z narzędzi i agenci."}, "compound-beta": {"description": "Compound-beta to złożony system AI wspierany przez wiele otwartych modeli dostępnych w GroqCloud, który inteligentnie i selektywnie wykorzystuje narzędzia do odpowiadania na zapytania użytkowników."}, "compound-beta-mini": {"description": "Compound-beta-mini to złożony system AI wspierany przez publicznie dostępne modele w GroqCloud, który inteligentnie i selektywnie wykorzystuje narzędzia do odpowiadania na zapytania użytkowników."}, "computer-use-preview": {"description": "Model computer-use-preview to dedykowany model zaprojektowany specjalnie do „narzędzi użycia komputera”, wytrenowany do rozumienia i wykonywania zadań związanych z komputerem."}, "dall-e-2": {"description": "Druga generacja modelu DALL·E, obsługująca bardziej realistyczne i dokładne generowanie obrazów, o rozdzielczości czterokrotnie większej niż pierwsza generacja."}, "dall-e-3": {"description": "Najnowocześniejszy model DALL·E, wydany w listopadzie 2023 roku. Obsługuje bardziej realistyczne i dokładne generowanie obrazów, z lepszą zdolnością do oddawania szczegółów."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct oferuje wysoką niezawodność w przetwarzaniu <PERSON>ń, wspierając różne branże."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 to model wnioskowania napędzany uczeniem przez wzmacnianie (RL), kt<PERSON><PERSON> rozwiązuje problemy z powtarzalnością i czytelnością modelu. Przed RL, DeepSeek-R1 wprowad<PERSON>ł dane z zimnego startu, co dodatkowo zoptymalizowało wydajność wnioskowania. W zadaniach matematycznych, kodowania i wnioskowania osiąga wyniki porównywalne z OpenAI-o1, a dzięki starannie zaprojektowanym metodom treningowym poprawia ogólne efekty."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 znacząco zwiększa głębokość zdolności wnioskowania i dedukcji dzięki zwiększonym zasobom obliczeniowym oraz wprowadzeniu mechanizmów optymalizacji algorytmów w trakcie dalszego treningu. Model osiąga doskonałe wyniki w róż<PERSON><PERSON> benchmarkach, w tym w matematyce, programowaniu i logice ogólnej. Jego ogólna wydajność jest obecnie zbliżona do czołowych modeli, takich jak O3 i Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B to model uzyskany przez destylację łańcuchów myślowych z modelu DeepSeek-R1-0528 do Qwen3 8B Base. Model osiąga najnow<PERSON> (SOTA) wyd<PERSON><PERSON><PERSON>ć wśród modeli open source, przewyższając Qwen3 8B o 10% w teście AIME 2024 i osiągając poziom wydajności Qwen3-235B-thinking. Wykaz<PERSON><PERSON> doskonałe wyniki w matematycznym wnioskowaniu, programowaniu i logice ogólnej, posiadając architekturę identyczną z Qwen3-8B, ale korzystając z tokenizera DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "Model destylacyjny DeepSeek-R1, optymalizuj<PERSON><PERSON> wydaj<PERSON>ść wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustana<PERSON><PERSON><PERSON><PERSON> nowe standardy w wielu zadaniach."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Model destylacyjny DeepSeek-R1, optymalizuj<PERSON><PERSON> wydaj<PERSON>ść wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustana<PERSON><PERSON><PERSON><PERSON> nowe standardy w wielu zadaniach."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "Model destylacyjny DeepSeek-R1, optymalizuj<PERSON><PERSON> wydaj<PERSON>ść wnioskowania dzięki uczeniu przez wzmocnienie i danym z zimnego startu, otwarty model ustana<PERSON><PERSON><PERSON><PERSON> nowe standardy w wielu zadaniach."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B to model uzyskany przez destylację Qwen2.5-32B. Model ten został dostosowany przy użyciu 800 000 starannie wybranych próbek wygenerowanych przez DeepSeek-R1, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe osiągi w wielu dziedzin<PERSON>, takich jak matematyka, programowanie i wnioskowanie. Osiągnął znakomite wyniki w wielu testach referencyjnych, w tym 94,3% dokładności w MATH-500, co pokazuje jego silne zdolności wnioskowania matematycznego."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B to model uzyskany przez destylację Qwen2.5-Math-7B. Model ten został dostosowany przy użyciu 800 000 starannie wybranych próbek wygenerowanych przez DeepSeek-R1, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe zdolności wnioskowania. Osiągnął znakomite wyniki w wielu testach referencyjnych, w tym 92,8% dokładności w MATH-500, 55,5% wskaźnika zdawalności w AIME 2024 oraz 1189 punktów w CodeForces, demonstrując silne zdolności matematyczne i programistyczne jako model o skali 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 łączy doskonałe cechy wcześniejszych wersji, wzmacniając zdolności ogólne i kodowania."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 to model językowy z 6710 miliardami parametrów, oparty na mieszanych ekspertach (MoE), wykorzyst<PERSON><PERSON><PERSON><PERSON> wielogłowicową potencja<PERSON>wag<PERSON> (MLA) oraz architekturę DeepSeekMoE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strategię równoważenia obciążenia bez dodatkowych strat, co optymalizuje wydajność wnioskowania i treningu. Dzięki wstępnemu treningowi na 14,8 bilionach wysokiej jakości tokenów oraz nadzorowanemu dostrajaniu i uczeniu przez wzmacnianie, DeepSeek-V3 przewyższa inne modele open source, zbliżając się do wiodących modeli zamkniętych."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B to zaawansowany model przeszkolony do złożonych dialogów."}, "deepseek-ai/deepseek-r1": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wyd<PERSON>ny LLM, specjalizujący się w wnioskowaniu, matematyce i programowaniu."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 to model wizualno-językowy oparty na DeepSeekMoE-27B, wykorzystujący architekturę MoE z rzadką aktywacją, osiągający doskonałe wyniki przy aktywacji jedynie 4,5 miliarda parametrów. Model ten wyróżnia się w wielu z<PERSON>, takich jak wizualne pytania i odpowiedzi, optyczne rozpoznawanie znaków, zrozumienie dokumentów/tabel/wykresów oraz lokalizacja wizualna."}, "deepseek-chat": {"description": "Nowy otwarty model ł<PERSON><PERSON><PERSON><PERSON> zdolności ogólne i kodowe, który nie tylko zachowuje ogólne zdolności dialogowe oryginalnego modelu czatu i potężne zdolności przetwarzania kodu modelu Coder, ale także lepiej dostosowuje się do ludzkich preferencji. Ponadto, DeepSeek-V2.5 osiągnął znaczne poprawy w zadaniach pisarskich, przestrzeganiu instrukcji i innych obszarach."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B to model j<PERSON><PERSON><PERSON> kodu, wytrenowany na 20 bilionach danych, z czego 87% to kod, a 13% to języki chiński i angielski. Model wprowadza okno o rozmiarze 16K oraz zadania uzupełniania, oferując funkcje uzupełniania kodu na poziomie projektu i wypełniania fragmentów."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 to otwarty model kodowy Mixture-of-Experts, który doskonale radzi sobie z zadaniami kodowymi, porównywalny z GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 to otwarty model kodowy Mixture-of-Experts, który doskonale radzi sobie z zadaniami kodowymi, porównywalny z GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 to model wnioskowania napędzany uczeniem przez wzmacnianie (RL), kt<PERSON><PERSON> rozwiązuje problemy z powtarzalnością i czytelnością modelu. Przed RL, DeepSeek-R1 wprowad<PERSON>ł dane z zimnego startu, co dodatkowo zoptymalizowało wydajność wnioskowania. W zadaniach matematycznych, kodowania i wnioskowania osiąga wyniki porównywalne z OpenAI-o1, a dzięki starannie zaprojektowanym metodom treningowym poprawia ogólne efekty."}, "deepseek-r1-0528": {"description": "Model w pełnej wersji 685B, wydany 28 maja 2025 roku. DeepSeek-R1 wykorzystuje techniki uczenia ze wzmocnieniem na dużą skalę w fazie post-treningowej, co znacznie poprawia zdolności wnioskowania modelu przy minimalnej ilości oznaczonych danych. Wysoka wydajność i zdolności w zadaniach matematycznych, kodowaniu oraz rozumowaniu języka naturalnego."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B s<PERSON><PERSON><PERSON> we<PERSON>, wspierająca wyszukiwanie w czasie rzeczywistym, oferująca szybszy czas reakcji przy zachowaniu wydajności modelu."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B standardowa wersja, wspierająca wyszukiwanie w czasie rzeczywistym, odpowiednia do zadań konwersacyjnych i przetwarzania tekstu wymagających najnowszych informacji."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama to model stworzony na podstawie Llamy, uzyskany przez destylację z DeepSeek-R1."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 — wi<PERSON><PERSON><PERSON> i inteligentniejszy model w zestawie DeepSeek — został destylowany do architektury Llama 70B. Na podstawie testów referencyjnych i ocen ręcznych, model ten jest bardziej inteligentny niż oryginalna Llama 70B, szczególnie w zadaniach wymagających precyzji matematycznej i faktograficznej."}, "deepseek-r1-distill-llama-8b": {"description": "Modele z serii DeepSeek-R1-Distill są dostosowywane do modeli open source, takich jak Qwen i Llama, poprzez technologię destylacji wiedzy, na podstawie próbek generowanych przez DeepSeek-R1."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Pierwsze wydanie 14 lutego 2025 roku, wyo<PERSON><PERSON><PERSON>nione przez zespół badawczy Qianfan z modelu bazowego Llama3_70B (zbudowanego z Meta Llama), w którym dodano również korpus Qianfan."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Pierwsze wydanie 14 lutego 2025 roku, wyo<PERSON><PERSON><PERSON>nione przez zespół badawczy Qianfan z modelu bazowego Llama3_8B (zbudowanego z Meta Llama), w którym dodano również korpus Qianfan."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen to model stworzony na podstawie Qwen poprzez destylację z DeepSeek-R1."}, "deepseek-r1-distill-qwen-1.5b": {"description": "Modele z serii DeepSeek-R1-Distill są dostosowywane do modeli open source, takich jak Qwen i Llama, poprzez technologię destylacji wiedzy, na podstawie próbek generowanych przez DeepSeek-R1."}, "deepseek-r1-distill-qwen-14b": {"description": "Modele z serii DeepSeek-R1-Distill są dostosowywane do modeli open source, takich jak Qwen i Llama, poprzez technologię destylacji wiedzy, na podstawie próbek generowanych przez DeepSeek-R1."}, "deepseek-r1-distill-qwen-32b": {"description": "Modele z serii DeepSeek-R1-Distill są dostosowywane do modeli open source, takich jak Qwen i Llama, poprzez technologię destylacji wiedzy, na podstawie próbek generowanych przez DeepSeek-R1."}, "deepseek-r1-distill-qwen-7b": {"description": "Modele z serii DeepSeek-R1-Distill są dostosowywane do modeli open source, takich jak Qwen i Llama, poprzez technologię destylacji wiedzy, na podstawie próbek generowanych przez DeepSeek-R1."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 pełna szybka wersja, wspierająca wyszukiwanie w czasie rzeczywistym, łącząca potężne możliwości 671 miliardów parametrów z szybszym czasem reakcji."}, "deepseek-r1-online": {"description": "DeepSeek R1 pełna wersja, z 671 miliardami parametrów, wspierająca wyszukiwanie w czasie rzeczywistym, z potężniejszymi zdolnościami rozumienia i generowania."}, "deepseek-reasoner": {"description": "Model inferency wprowadzony przez DeepSeek. <PERSON><PERSON>ed wygenerowaniem ostatecznej od<PERSON>, model naj<PERSON><PERSON><PERSON> przedstawia fragment łań<PERSON><PERSON>, aby z<PERSON><PERSON><PERSON><PERSON><PERSON> dokładno<PERSON> końcowej odpowiedzi."}, "deepseek-v2": {"description": "DeepSeek V2 to wyd<PERSON><PERSON> model <PERSON><PERSON><PERSON><PERSON><PERSON> Mixture-of-Experts, odpowiedni do ekonomicznych potrzeb przetwarzania."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B to model kodowy zaprojektowany przez DeepSeek, oferuj<PERSON>cy potężne możliwości generowania kodu."}, "deepseek-v3": {"description": "DeepSeek-V3 to model MoE opracowany przez Hangzhou DeepSeek AI Technology Research Co., Ltd., który osiągnął znakomite wyniki w wielu testach, zajmuj<PERSON>c pierwsze miejsce wśród modeli open-source na głównych listach. W porównaniu do modelu V2.5, pręd<PERSON><PERSON><PERSON> generowania wzrosła trzykrotnie, co zapewnia użytkownikom szybsze i płynniejsze doświadczenia."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 to model MoE z 671 miliardami parametrów, który wyróżnia się w zakresie programowania i umiejętności technicznych, rozumienia kontekstu oraz przetwarzania długich tekstów."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 to model mieszany z 685B parametrami, b<PERSON><PERSON><PERSON>cy najnowszą iteracją flagowej serii modeli czatu zespołu DeepSeek.\n\nDziedziczy po modelu [DeepSeek V3](/deepseek/deepseek-chat-v3) i wykazuje doskonałe wyniki w różnych zadaniach."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 to model mieszany z 685B parametrami, b<PERSON><PERSON><PERSON>cy najnowszą iteracją flagowej serii modeli czatu zespołu DeepSeek.\n\nDziedziczy po modelu [DeepSeek V3](/deepseek/deepseek-chat-v3) i wykazuje doskonałe wyniki w różnych zadaniach."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 znacznie poprawił zdolności wnioskowania modelu przy minimalnej ilości oznaczonych danych. Przed wygenerowaniem ostatecznej od<PERSON>wiedzi, model najpierw wygeneruje fragment myślenia, aby z<PERSON><PERSON><PERSON><PERSON><PERSON> dokładn<PERSON> końcowej odpowiedzi."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 znacząco poprawia zdolność wnioskowania modelu nawet przy minimalnej ilości oznaczonych danych. Przed wygenerowaniem ostatecznej odpowiedzi model najpierw generuje łańcuch myślowy, co zwiększa dokładność końcowej odpowiedzi."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 znacząco poprawia zdolność wnioskowania modelu nawet przy minimalnej ilości oznaczonych danych. Przed wygenerowaniem ostatecznej odpowiedzi model najpierw generuje łańcuch myślowy, co zwiększa dokładność końcowej odpowiedzi."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B to du<PERSON>y model językowy oparty na Llama3.3 70B, kt<PERSON>ry wykorzystuje dostrojenie na podstawie wyjścia DeepSeek R1, osiągając konkurencyjną wydajność porównywalną z dużymi modelami na czołowej pozycji."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B to destylowany duży model językowy oparty na Llama-3.1-8B-Instruct, wytrenowany przy użyciu wyjścia DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B to destylowany duży model językowy oparty na Qwen 2.5 14B, wytrenowany przy użyciu wyjścia DeepSeek R1. Model ten przewyższył OpenAI o1-mini w wielu testach benchmarkowych, osiągając najnowsze osiągnięcia technologiczne w dziedzinie modeli gęstych (dense models). Oto niektóre wyniki testów benchmarkowych:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nModel ten, dostrojony na podstawie wyjścia DeepSeek R1, wykazuje konkurencyjną wydajność porównywalną z większymi modelami na czołowej pozycji."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B to destylowany duży model językowy oparty na Qwen 2.5 32B, wytrenowany przy użyciu wyjścia DeepSeek R1. Model ten przewyższył OpenAI o1-mini w wielu testach benchmarkowych, osiągając najnowsze osiągnięcia technologiczne w dziedzinie modeli gęstych (dense models). Oto niektóre wyniki testów benchmarkowych:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nModel ten, dostrojony na podstawie wyjścia DeepSeek R1, wykazuje konkurencyjną wydajność porównywalną z większymi modelami na czołowej pozycji."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 to na<PERSON><PERSON><PERSON> model open source wydany przez zespół DeepSeek, który charakteryzuje się bardzo silnymi możliwościami wnioskowania, szczególnie w zadaniach matematycznych, programistycznych i logicznych, osiągając poziom porównywalny z modelem o1 OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 znacznie poprawił zdolności wnioskowania modelu przy minimalnej ilości oznaczonych danych. Przed wygenerowaniem ostatecznej od<PERSON>wiedzi, model najpierw wygeneruje fragment myślenia, aby z<PERSON><PERSON><PERSON><PERSON><PERSON> dokładn<PERSON> końcowej odpowiedzi."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 osiągnął znaczący przełom w szybkości wnioskowania w porównaniu do wcześniejszych modeli. Zajmuje pierwsze miejsce wśród modeli open source i może konkurować z najnowocześniejszymi modelami zamkniętymi na świecie. DeepSeek-V3 wykorzystuje architekturę wielogłowicowej uwagi (MLA) oraz DeepSeekMoE, które zostały w pełni zweryfikowane w DeepSeek-V2. Ponadto, DeepSeek-V3 wprowadza pomocniczą strategię bezstratną do równoważenia obciążenia oraz ustala cele treningowe dla wieloetykietowego przewidywania, aby uzyskać lepszą wydajność."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 osiągnął znaczący przełom w szybkości wnioskowania w porównaniu do wcześniejszych modeli. Zajmuje pierwsze miejsce wśród modeli open source i może konkurować z najnowocześniejszymi modelami zamkniętymi na świecie. DeepSeek-V3 wykorzystuje architekturę wielogłowicowej uwagi (MLA) oraz DeepSeekMoE, które zostały w pełni zweryfikowane w DeepSeek-V2. Ponadto, DeepSeek-V3 wprowadza pomocniczą strategię bezstratną do równoważenia obciążenia oraz ustala cele treningowe dla wieloetykietowego przewidywania, aby uzyskać lepszą wydajność."}, "deepseek_r1": {"description": "DeepSeek-R1 to model wnioskowania napędzany uczeniem przez wz<PERSON>nie (RL), kt<PERSON><PERSON> rozwiązuje problemy z powtarzalnością i czytelnością modelu. Przed RL, DeepSeek-R1 wprowad<PERSON>ł dane z zimnego startu, co dodatkowo zoptymalizowało wydajność wnioskowania. W zadaniach matematycznych, kodowania i wnioskowania osiąga wyniki porównywalne z OpenAI-o1, a dzięki starannie zaprojektowanym metodom szkoleniowym poprawia ogólne efekty."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B to model uzyskany poprzez destylację treningową z Llama-3.3-70B-Instruct. Model ten jest cz<PERSON>ś<PERSON>ą serii DeepSeek-R1, a dzięki użyciu pr<PERSON><PERSON> wygenerowanych przez DeepSeek-R1, wyk<PERSON><PERSON><PERSON> doskonałe wyniki w matematyce, programowaniu i wnioskowaniu."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-14B to model uzyskany poprzez destylację wiedzy z Qwen2.5-14B. Model ten został dostrojony przy użyciu 800 000 starannie wybranych próbek wygenerowanych przez DeepSeek-R1, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe zdolności wnioskowania."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Di<PERSON>ill-Qwen-32B to model uzyskany poprzez destylację wiedzy z Qwen2.5-32B. Model ten został dostrojony przy użyciu 800 000 starannie wybranych próbek wygenerowanych przez DeepSeek-R1, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe wyniki w wielu dzied<PERSON>, w tym matematyce, programowaniu i wnioskowaniu."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite to nowa generacja modelu o lekkiej konstrukcji, charakteryzująca się ekstremalną szybko<PERSON> reak<PERSON>, osiągając światowy poziom zarówno w zakresie wydajności, jak i opóźnienia."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k to kompleksowa wersja ulepszona na bazie Doubao-1.5-Pro, kt<PERSON>ra oferuje znaczny wzrost wydajności o 10%. Obsługuje wnioskowanie w kontekście 256k, a maksymalna długość wyjścia wynosi 12k tokenów. <PERSON><PERSON><PERSON><PERSON><PERSON>, w<PERSON><PERSON><PERSON><PERSON>, doskonały stosunek jakości do ceny, odpowiedni do szerszego zakresu zastosowań."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro to nowa generacja głównego modelu, k<PERSON><PERSON><PERSON> ofer<PERSON>je kompleksowe ulepszenia wydajności, wykazując doskonałe wyniki w zakresie wiedzy, kodowania, wnioskowania i innych obszarów."}, "doubao-1.5-thinking-pro": {"description": "Model głębokiego myślenia Doubao-1.5, nowa generacja, wyróżnia się w dziedzinach takich jak matematyka, programowanie, rozumowanie naukowe oraz w zadaniach ogólnych, takich jak twórcze pisanie. Osiąga lub zbliża się do poziomu czołowych graczy w branży w wielu uznawanych benchmarkach, takich jak AIME 2024, Codeforces, GPQA. Obsługuje okno kontekstowe o wielkości 128k oraz 16k wyjścia."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 to nowy model g<PERSON><PERSON><PERSON><PERSON><PERSON> (wersja m z natywną wielomodalną zdolnością głębokiego wnioskowania), wyróżniający się w dziedzinach takich jak matematyka, programowanie, rozumowanie naukowe oraz twórcze pisanie. Osiąga lub zbliża się do czołówki branży na wielu prestiżowych benchmarkach, takich jak AIME 2024, Codeforces, GPQA. Obsługuje kontekst do 128k i wyjście do 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Nowy model g<PERSON><PERSON><PERSON><PERSON><PERSON> myślen<PERSON> wiz<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zaawansowane zdolności uniwersalnego wielomodalnego rozumienia i wnioskowania, osiągając SOTA w 37 z 59 publicznych benchmarków."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS to natywny model agenta zaprojektowany do interakcji z graficznym interfejsem użytkownika (GUI). Dzięki zdolnościom percepcji, wnioskowania i działania na poziomie ludzkim umożliwia płynną interakcję z GUI."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite to nowo zaktualizowany model multimodalny, kt<PERSON>ry obsługuje rozpoznawanie obrazów o dowolnej rozdzielczości i ekstremalnych proporcjach, wzmacniając zdolności wnioskowania wizualnego, rozpoznawania dokumentów, rozumienia szczegółowych informacji i przestrzegania instrukcji. Obsługuje okno kontekstowe 128k, maks<PERSON><PERSON><PERSON> długość wyjścia to 16k tokenów."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro to now<PERSON> <PERSON><PERSON><PERSON><PERSON> wielomodalny model <PERSON><PERSON><PERSON>, obsługuj<PERSON><PERSON> rozpoznawanie obrazów o dowolnej rozdzielczości i ekstremalnych proporcjach, wzmacniający zdolności wizualnego wnioskowania, rozpoznawania dokumentów, rozumienia szczegółów i przestrzegania instrukcji."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro to now<PERSON> <PERSON><PERSON><PERSON><PERSON> wielomodalny model <PERSON><PERSON><PERSON>, obsługuj<PERSON><PERSON> rozpoznawanie obrazów o dowolnej rozdzielczości i ekstremalnych proporcjach, wzmacniający zdolności wizualnego wnioskowania, rozpoznawania dokumentów, rozumienia szczegółów i przestrzegania instrukcji."}, "doubao-lite-128k": {"description": "Oferuje niezwykle szybkie reakcje i lepszy stosunek jakości do ceny, zapewniając klientom elastyczne opcje dla różnych scenariuszy. Obsługuje wnioskowanie i dostrajanie z kontekstem do 128k."}, "doubao-lite-32k": {"description": "Oferuje niezwykle szybkie reakcje i lepszy stosunek jakości do ceny, zapewniając klientom elastyczne opcje dla różnych scenariuszy. Obsługuje wnioskowanie i dostrajanie z kontekstem do 32k."}, "doubao-lite-4k": {"description": "Oferuje niezwykle szybkie reakcje i lepszy stosunek jakości do ceny, zapewniając klientom elastyczne opcje dla różnych scenariuszy. Obsługuje wnioskowanie i dostrajanie z kontekstem do 4k."}, "doubao-pro-256k": {"description": "Na<PERSON><PERSON><PERSON><PERSON> model g<PERSON><PERSON><PERSON>, odpowiedni do złożonych zadań, osiągający doskonałe wyniki w scenariuszach takich jak pytania i odpowiedzi, streszcz<PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, klasyfikacja tekstu i odgrywanie ról. Obsługuje wnioskowanie i dostrajanie z kontekstem do 256k."}, "doubao-pro-32k": {"description": "Na<PERSON><PERSON><PERSON><PERSON> model g<PERSON><PERSON><PERSON>, odpowiedni do złożonych zadań, osiągający doskonałe wyniki w scenariuszach takich jak pytania i odpowiedzi, streszcz<PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, klasyfikacja tekstu i odgrywanie ról. Obsługuje wnioskowanie i dostrajanie z kontekstem do 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 to nowy, wielomodalny model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, obsługuj<PERSON><PERSON> trzy tryby myślenia: auto, thinking i non-thinking. W trybie non-thinking model o<PERSON><PERSON><PERSON> znacznie lepsze wyniki w porównaniu do Doubao-1.5-pro/250115. Obsługuje kontekst do 256k oraz maksymalną długość wyjścia do 16k tokenów."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash to ultraszybki model wielomodalnego głębokiego myślenia, z czasem TPOT zaledwie 10 ms; obsługuje zarówno rozumienie tekstu, jak i obrazu, z lepszymi zdolnościami tekstowymi niż poprzednia generacja lite oraz wizualnymi porównywalnymi do modeli pro konkurencji. Obsługuje kontekst do 256k oraz maksymalną długość wyjścia do 16k tokenów."}, "doubao-seed-1.6-thinking": {"description": "Model Doubao-Seed-1.6-thinking ma znacznie wzmocnione zdolności myślenia, w porównaniu do Doubao-1.5-thinking-pro osi<PERSON>ga dalsze ulepszenia w podstawowych umiejętnościach takich jak kodowanie, matematyka i rozumowanie logiczne, wspiera również rozumienie wizualne. Obsługuje kontekst do 256k oraz maksymalną długość wyjścia do 16k tokenów."}, "doubao-seedream-3-0-t2i-250415": {"description": "Model generowania obrazów Doubao opracowany przez zespół Seed ByteDance, obsługujący wejścia tekstowe i obrazowe, oferujący wysoką kontrolę i jakość generowanych obrazów. Generuje obrazy na podstawie tekstowych wskazówek."}, "doubao-vision-lite-32k": {"description": "Model <PERSON><PERSON><PERSON>-vision to wielomodalny model <PERSON><PERSON><PERSON> skali opracowany przez <PERSON>, ofer<PERSON><PERSON><PERSON><PERSON> potężne zdolności rozumienia i wnioskowania obrazów oraz precyzyjne rozumienie poleceń. Model wykazuje silne wyniki w ekstrakcji informacji z obrazów i tekstu oraz w zadaniach wnioskowania opartych na obrazach, umożliwiając zastosowanie w bardziej złożonych i szerokich zadaniach wizualnych pytań i odpowiedzi."}, "doubao-vision-pro-32k": {"description": "Model <PERSON><PERSON><PERSON>-vision to wielomodalny model <PERSON><PERSON><PERSON> skali opracowany przez <PERSON>, ofer<PERSON><PERSON><PERSON><PERSON> potężne zdolności rozumienia i wnioskowania obrazów oraz precyzyjne rozumienie poleceń. Model wykazuje silne wyniki w ekstrakcji informacji z obrazów i tekstu oraz w zadaniach wnioskowania opartych na obrazach, umożliwiając zastosowanie w bardziej złożonych i szerokich zadaniach wizualnych pytań i odpowiedzi."}, "emohaa": {"description": "Emohaa to model psychologiczny, posiadający profesjonalne umiejętności doradcze, pomagający użytkownikom zrozumieć problemy emocjonalne."}, "ernie-3.5-128k": {"description": "Flagowy model j<PERSON>zy<PERSON>wy opracowany przez <PERSON>, obejmuj<PERSON><PERSON> ogromne zbiory danych w języku chińskim i angielskim, charaktery<PERSON><PERSON><PERSON><PERSON> się silnymi zdolnościami ogólnymi, spełniającym wymagania większości zastosowań w dialogach, generowaniu treści i aplikacjach wtyczek; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ernie-3.5-8k": {"description": "Flagowy model j<PERSON>zy<PERSON>wy opracowany przez <PERSON>, obejmuj<PERSON><PERSON> ogromne zbiory danych w języku chińskim i angielskim, charaktery<PERSON><PERSON><PERSON><PERSON> się silnymi zdolnościami ogólnymi, spełniającym wymagania większości zastosowań w dialogach, generowaniu treści i aplikacjach wtyczek; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ernie-3.5-8k-preview": {"description": "Flagowy model j<PERSON>zy<PERSON>wy opracowany przez <PERSON>, obejmuj<PERSON><PERSON> ogromne zbiory danych w języku chińskim i angielskim, charaktery<PERSON><PERSON><PERSON><PERSON> się silnymi zdolnościami ogólnymi, spełniającym wymagania większości zastosowań w dialogach, generowaniu treści i aplikacjach wtyczek; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ernie-4.0-8k-latest": {"description": "Flagowy model j<PERSON><PERSON><PERSON><PERSON>du o ultra dużej skali, w porównaniu do ERNIE 3.5, oferujący kompleksową aktualizację zdo<PERSON> modelu, szeroko stosowany w złożonych zadaniach w różnych dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ernie-4.0-8k-preview": {"description": "Flagowy model j<PERSON><PERSON><PERSON><PERSON>du o ultra dużej skali, w porównaniu do ERNIE 3.5, oferujący kompleksową aktualizację zdo<PERSON> modelu, szeroko stosowany w złożonych zadaniach w różnych dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji."}, "ernie-4.0-turbo-128k": {"description": "Flagowy model j<PERSON><PERSON><PERSON><PERSON> o ultra dużej skali, chara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się doskonałymi wynikami ogólnymi, szeroko stosowany w złożonych zadaniach w różnych dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji. W porównaniu do ERNIE 4.0, oferuje lepsze wyniki wydajności."}, "ernie-4.0-turbo-8k-latest": {"description": "Flagowy model j<PERSON><PERSON><PERSON><PERSON> o ultra dużej skali, chara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się doskonałymi wynikami ogólnymi, szeroko stosowany w złożonych zadaniach w różnych dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji. W porównaniu do ERNIE 4.0, oferuje lepsze wyniki wydajności."}, "ernie-4.0-turbo-8k-preview": {"description": "Flagowy model j<PERSON><PERSON><PERSON><PERSON> o ultra dużej skali, chara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się doskonałymi wynikami ogólnymi, szeroko stosowany w złożonych zadaniach w różnych dziedzinach; wspiera automatyczne połączenie z wtyczką wyszukiwania Baidu, zapewniając aktualność informacji. W porównaniu do ERNIE 4.0, oferuje lepsze wyniki wydajności."}, "ernie-4.5-8k-preview": {"description": "Model ERNIE 4.5 to nowa generacja natywnego modelu wielomodalnego opracowanego przez <PERSON>, kt<PERSON><PERSON> osiąga doskonałe wyniki w zakresie zrozumienia wielomodalnego dzięki wspólnemu modelowaniu wielu modalności; posiada bardziej zaawansowane zdolności językowe, a także znacznie poprawione zdolności rozumienia, generowania, logicznego myślenia i pamięci, eliminując halucynacje oraz poprawiając zdolności wnioskowania logicznego i kodowania."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo ma wyraźnie poprawione zdolności w zakresie eliminacji halucynacji, rozumowania logicznego i programowania. W porównaniu do Wenxin 4.5, jest szy<PERSON>zy i tańszy. Zdolności modelu zostały znacznie zwiększone, lepiej spełniając wymagania dotyczące przetwarzania długich rozmów z wieloma rundami oraz zadań związanych z rozumieniem długich dokumentów."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo ma wyraźnie poprawione zdolności w zakresie eliminacji halucynacji, rozumowania logicznego i programowania. W porównaniu do Wenxin 4.5, jest szy<PERSON>zy i tańszy. Zdolności w zakresie twórczości tekstowej i pytań i odpowiedzi znacznie się poprawiły. Długość wyjścia i opóźnienie całych zdań w porównaniu do ERNIE 4.5 wzrosły."}, "ernie-4.5-turbo-vl-32k": {"description": "Nowa wersja dużego modelu <PERSON><PERSON>, z wyraźnie poprawionymi zdolnościami w zakresie rozumienia obrazów, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tłumaczenia i programowania, po raz pierwszy obsługująca długość kontekstu 32K, z wyraźnie zmniejszonym opóźnieniem pierwszego tokena."}, "ernie-char-8k": {"description": "Model językowy opracowany przez <PERSON>, skoncentrowany na specyficznych scenariuszach, odpowiedni do zastosowań w grach NPC, dialogach obsługi klienta, odgrywaniu ról w dialogach, charakteryzuj<PERSON>cy się wyraźnym i spójnym stylem postaci, silniejszą zdolnością do podążania za instrukcjami oraz lepszą wydajnością wnioskowania."}, "ernie-char-fiction-8k": {"description": "Model językowy opracowany przez <PERSON>, skoncentrowany na specyficznych scenariuszach, odpowiedni do zastosowań w grach NPC, dialogach obsługi klienta, odgrywaniu ról w dialogach, charakteryzuj<PERSON>cy się wyraźnym i spójnym stylem postaci, silniejszą zdolnością do podążania za instrukcjami oraz lepszą wydajnością wnioskowania."}, "ernie-irag-edit": {"description": "Model edycji obrazów ERNIE iRAG opracowany przez <PERSON>, wspierający operacje takie jak usuwanie obiektów (erase), prz<PERSON><PERSON><PERSON><PERSON><PERSON> (repaint) oraz generowanie wariantów (variation) na podstawie obrazów."}, "ernie-lite-8k": {"description": "ERNIE Lite to lekki model językowy opracowany przez <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe wyniki modelu z wydajnością wnioskowania, odpowiedni do użycia na kartach przyspieszających AI o niskiej mocy obliczeniowej."}, "ernie-lite-pro-128k": {"description": "Lekki model języ<PERSON>wy opracowany przez <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe wyniki modelu z wydajnością wnioskowania, oferujący lepsze wyniki niż ERNIE Lite, odpowiedni do użycia na kartach przyspieszających AI o niskiej mocy obliczeniowej."}, "ernie-novel-8k": {"description": "Ogólny model j<PERSON><PERSON><PERSON>wy opracowany przez <PERSON>, kt<PERSON>ry wykazuje wyraźne przewagi w zakresie kontynuacji powieści, może być również stosowany w scenariuszach krótkich dramatów i filmów."}, "ernie-speed-128k": {"description": "<PERSON><PERSON><PERSON><PERSON> model j<PERSON><PERSON><PERSON><PERSON> o wysokiej wydajności opracowany przez Baidu w 2024 roku, charakt<PERSON><PERSON><PERSON><PERSON><PERSON> się doskonałymi zdolnościami ogólnymi, odpowiedni jako model bazowy do dalszego dostosowania, lepiej radzący sobie z problemami w specyficz<PERSON><PERSON> scenarius<PERSON>h, a także oferujący doskonałą wydajność wnioskowania."}, "ernie-speed-pro-128k": {"description": "<PERSON><PERSON><PERSON><PERSON> model j<PERSON><PERSON><PERSON><PERSON> o wysokiej wydajności opracowany przez Baidu w 2024 roku, charakt<PERSON><PERSON><PERSON><PERSON><PERSON> się doskonałymi zdolnościami ogólnymi, oferujący lepsze wyniki niż ERNIE Speed, odpowiedni jako model bazowy do dalszego dostosowania, lepiej radzący sobie z problemami w specyficznych scenariuszach, a także oferujący doskonałą wydajność wnioskowania."}, "ernie-tiny-8k": {"description": "ERNIE Tiny to model j<PERSON><PERSON><PERSON><PERSON> o ultra wysokiej wydajności opracowany przez <PERSON>, charakteryzujący się najniższymi kosztami wdrożenia i dostosowania w serii modeli <PERSON>."}, "ernie-x1-32k": {"description": "Posiada silniejsze zdolności w zakresie rozumienia, planowania, refleksji i ewolucji. <PERSON>ako model głębokiego myślenia o wszechstronnych umiejętnościach, Wenxin X1 łączy w sobie do<PERSON>dn<PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i styl, doskonale sprawdzając się w chińskich pytaniach i odpowiedziach, tw<PERSON>rc<PERSON>ści literackiej, pisaniu dokumentów, codzie<PERSON>ch rozmowach, rozumowaniu logicznym, skomplikowanych obliczeniach oraz wywoływaniu narzędzi."}, "ernie-x1-32k-preview": {"description": "Model ERNIE X1 charakteryzuje się silniejszymi zdolnościami w zakresie rozumienia, planowania, refleksji i ewolucji. Jako model głębokiego myślenia o szerszych możli<PERSON>ś<PERSON>ch, ERNIE X1 łączy w sobie precyzję, kreatywno<PERSON><PERSON> i styl, osiągając szczególne wyniki w chińskich pytaniach i odpowiedziach, twórczości literackiej, pisaniu tekstów, codziennych rozmowach, wnioskowaniu logicznym, złożonych obliczeniach oraz wywoływaniu narzędzi."}, "ernie-x1-turbo-32k": {"description": "Model ma lepsze wyniki i wydajność w porównaniu do ERNIE-X1-32K."}, "flux-1-schnell": {"description": "Model generowania obrazów na podstawie tekstu o 12 miliardach parametrów opracowany przez Black Forest Labs, wykorzystujący technikę destylacji latentnej dyfuzji przeciwstawnej, zdolny do generowania wysokiej jakości obrazów w 1 do 4 kroków. Model osiąga wydajność porównywalną z zamkniętymi alternatywami i jest udostępniony na licencji Apache-2.0, odpowiedni do użytku osobistego, badawczego i komercyjnego."}, "flux-dev": {"description": "FLUX.1 [dev] to o<PERSON><PERSON><PERSON>, dopracowany model o otwartych wagach przeznaczony do zastosowań niekomercyjnych. Zachowuje jakość obrazu i zdolność do przestrzegania instrukcji zbliżoną do wersji profesjonalnej FLUX, oferując jednocześnie wyższą efektywność działania. W porównaniu do standardowych modeli o podobnej wielkości jest bardziej efektywny w wykorzystaniu zasobów."}, "flux-kontext/dev": {"description": "Model edy<PERSON>ji obrazów Frontier."}, "flux-merged": {"description": "Model FLUX.1-merged łączy głębokie cechy eksplorowane podczas fazy rozwojowej „DEV” z zaletami szybkiego wykonania reprezentowanymi przez „Schnell”. Dzięki temu FLUX.1-merged nie tylko przesuwa granice wydajności modelu, ale także rozszerza zakres jego zastosowań."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] potrafi przetwarzać tekst i obrazy referencyjne jako dane wej<PERSON>ciowe, umożliwiaj<PERSON><PERSON> płynną, celową edycję lokalną oraz złożone transformacje całych scen."}, "flux-schnell": {"description": "FLUX.1 [schnell] to obecnie najbard<PERSON>j zaawansowany otwarty model o małej liczbie kroków, prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> konkurencję, a nawet potężne modele nie destylowane, takie jak Midjourney v6.0 i DALL·E 3 (HD). Model z<PERSON><PERSON><PERSON> specjalnie dostrojony, aby zach<PERSON><PERSON> pełną różnorodność wyjść z fazy wstępnego treningu. W porównaniu z najlepszymi modelami na rynku FLUX.1 [schnell] znacząco poprawia jakość wizualną, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z instrukcjami, obsługę zmian rozmiaru/proporcji, przetwarzanie czcionek oraz różnorodność generowanych obrazów, oferując użytkownikom bogatsze i bardziej zróżnicowane doświadczenia twórcze."}, "flux.1-schnell": {"description": "Transformator przepływu skorygowanego o 12 miliardach parametrów, zdolny do generowania obrazów na podstawie opisów tekstowych."}, "flux/schnell": {"description": "FLUX.1 [schnell] to model transformera strumieniowego z 12 miliardami parametrów, zdolny generować wysokiej jakości obrazy z tekstu w 1 do 4 krokach, odpowiedni do użytku osobistego i komercyjnego."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning) oferuje stabilną i dostosowywalną wyd<PERSON>, co czyni go idealnym wyborem dla rozwiązań złożonych zadań."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning) oferuje doskonałe wsparcie multimodalne, koncentrując się na efektywnym rozwiązywaniu złożonych zadań."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro to model AI o wysokiej wydajności od Google, zaprojektowany do szerokiego rozszerzania zadań."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 to wydajny model multimodalny, wspierający szerokie zastosowania."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 to wydajny model multimodalny, który wspiera szeroką gamę zastosowań."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B to wydajny model multimodalny, który wspiera szeroki zakres zastosowań."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 to na<PERSON><PERSON><PERSON> eksperymentalny model, który wykazuje znaczące poprawy wydajności w zastosowaniach tekstowych i multimodalnych."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B to w<PERSON><PERSON><PERSON> model w<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> obsługuje szeroki zakres zastosowań."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 oferuje zoptymalizowane możliwości przetwarzania multimodalnego, odpowiednie dla wielu złoż<PERSON>ch scenar<PERSON>zy."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash to na<PERSON><PERSON><PERSON> model AI Google o wielu modalnościach, który charakteryzuje się szybkim przetwarzaniem i obsługuje wejścia tekstowe, obrazowe i wideo, co czyni go odpowiednim do efektywnego rozszerzania w różnych zadaniach."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 to skalowalne rozwiązanie AI multimodalnego, wspierające szeroki zakres złożonych zadań."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 to na<PERSON><PERSON><PERSON> model got<PERSON><PERSON> do produkcji, oferujący wyższą jako<PERSON> wyn<PERSON>, ze szczególnym uwzględnieniem zadań matematycznych, długich kontekstów i zadań wizualnych."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 of<PERSON><PERSON>je doskonałe możliwości przetwarzania multimodalnego, zapewniając większą elastyczność w rozwoju aplikacji."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 łączy najnowsze technologie optymalizacji, oferując bardziej efektywne możliwości przetwarzania danych multimodalnych."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro obsługuje do 2 milionów tokenów, co czyni go idealnym wyborem dla średniej wielkości modeli multimodalnych, odpowiednim do wszechstronnej obsługi złożonych zadań."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash oferuje funkcje i ulepszenia nowej generacji, w tym doskonałą pr<PERSON><PERSON><PERSON><PERSON>, natywne korzystanie z narzędzi, generowanie multimodalne oraz okno kontekstowe o długości 1M tokenów."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash oferuje funkcje i ulepszenia nowej generacji, w tym doskonałą pr<PERSON><PERSON><PERSON><PERSON>, natywne korzystanie z narzędzi, generowanie multimodalne oraz okno kontekstowe o długości 1M tokenów."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash to wariant modelu, zoptymalizowany pod kątem efektywności kosztowej i niskiego opóźnienia."}, "gemini-2.0-flash-exp-image-generation": {"description": "Model eksperymentalny Gemini 2.0 Flash, wspieraj<PERSON>cy generowanie obrazów"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash to wariant modelu, zoptymalizowany pod kątem efektywności kosztowej i niskiego opóźnienia."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash to wariant modelu, zoptymalizowany pod kątem efektywności kosztowej i niskiego opóźnienia."}, "gemini-2.0-flash-preview-image-generation": {"description": "Model Gemini 2.0 Flash do generowania obrazów, wspierający generację obrazów"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash to najbardziej opłacalny model Google, oferujący wszechstronne funkcje."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-<PERSON><PERSON> to najmniejszy i najbardziej opłacalny model Google, zaprojektowany z myślą o szerokim zastosowaniu."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview to najmniejszy i najbardziej opłacalny model Google, zaprojektowany z myślą o masowym zastosowaniu."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview to najbardziej opłacalny model Google, oferujący wszechstronne funkcje."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview to najbardziej opłacalny model Google, oferujący wszechstronne funkcje."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro to najnowocześniejszy model myślowy Google, zdolny do rozumowania nad złożonymi problemami w dziedzinach kodowania, matematyki i STEM oraz analizowania dużych zbiorów danych, repozytoriów kodu i dokumentacji przy użyciu długiego kontekstu."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview to najnowocześniejsz<PERSON> model myślenia Google, zdolny do wnioskowania w zakresie kodu, matematyki i złożonych problemów w dziedzinie STEM, a także do analizy dużych zbiorów danych, repozytoriów kodu i dokumentów przy użyciu długiego kontekstu."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview to najnowoc<PERSON>ś<PERSON><PERSON><PERSON><PERSON> model myślenia Google, zdolny do wnioskowania w złożonych problemach związanych z kodem, matematyką i dziedzinami STEM, a także do analizy dużych zbiorów danych, repozytoriów kodu i dokumentów przy użyciu długiego kontekstu."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview to najnowocześniejszy model myślowy Google, zdolny do rozumowania nad złożonymi problemami w dziedzinach kodowania, matematyki i STEM oraz do analizy dużych zbiorów danych, repozytoriów kodu i dokumentów z wykorzystaniem długich kontekstów."}, "gemma-7b-it": {"description": "Gemma 7B nadaje się do przetwarzania zadań średniej i małej skali, łącząc efektywność kosztową."}, "gemma2": {"description": "Gemma 2 to wydajny model w<PERSON><PERSON> przez <PERSON>, obejmuj<PERSON>cy różnorodne zastosowania, od małych aplikacji po złożone przetwarzanie danych."}, "gemma2-9b-it": {"description": "Gemma 2 9B to model zoptymalizowany do specyficznych zadań i integracji narzędzi."}, "gemma2:27b": {"description": "Gemma 2 to wydajny model w<PERSON><PERSON> przez <PERSON>, obejmuj<PERSON>cy różnorodne zastosowania, od małych aplikacji po złożone przetwarzanie danych."}, "gemma2:2b": {"description": "Gemma 2 to wydajny model w<PERSON><PERSON> przez <PERSON>, obejmuj<PERSON>cy różnorodne zastosowania, od małych aplikacji po złożone przetwarzanie danych."}, "generalv3": {"description": "Spark Pro to model du<PERSON><PERSON> języka o wysokiej wydajności, zoptymalizowany do profesjonalnych dziedzin, takich jak matematyka, programowanie, medycyna i edukacja, wspierający wyszukiwanie w sieci oraz wbudowane wtyczki, takie jak pogoda i daty. Jego zoptymalizowany model wykazuje doskonałe wyniki i wysoką wydajność w skomplikowanych pytaniach o wiedzę, rozumieniu języka oraz tworzeniu zaawansowanych tekstów, co czyni go idealnym wyborem do profesjonalnych zastosowań."}, "generalv3.5": {"description": "Spark3.5 Max to najbardziej wszechstronna wersja, wspierająca wyszukiwanie w sieci oraz wiele wbudowanych wtyczek. Jego kompleksowo zoptymalizowane zdolności rdzeniowe oraz funkcje ustawiania ról systemowych i wywoływania funkcji sprawiają, że wykazuje się wyjątkową wydajnością w różnych skomplikowanych zastosowaniach."}, "glm-4": {"description": "GLM-4 to stary flagowy model wydany w styczniu 2024 roku, obecnie zastąpiony przez silniejszy model GLM-4-0520."}, "glm-4-0520": {"description": "GLM-4-0520 to najnowsza wersja modelu, zaprojektowana do wysoko złożonych i zróżnicowanych zadań, z doskonałymi wynikami."}, "glm-4-9b-chat": {"description": "GLM-4-9B-Chat wykazuje wysoką wydaj<PERSON>ć w wielu aspekt<PERSON>, takich jak semanty<PERSON>, mate<PERSON><PERSON><PERSON>, wnioskowanie, kodowanie i wiedza. Posiada również funkcje przeglądania stron internetowych, wykonywania kodu, wywoływania niestandardowych narzędzi oraz wnioskowania z długich tekstów. Obsługuje 26 języków, w tym japo<PERSON>, koreański i niemiecki."}, "glm-4-air": {"description": "GLM-4-Air to opłacalna wersja, której wydajność jest zbliżona do GLM-4, oferująca szybkie działanie i przystępną cenę."}, "glm-4-air-250414": {"description": "GLM-4-Air to wersja o wysokim stosunku jakości do ceny, o wydajności zbliżonej do GLM-4, oferująca szybkie tempo i przystępną cenę."}, "glm-4-airx": {"description": "GLM-4-AirX oferuje wydajną wersję GLM-4-Air, z szybkością wnioskowania do 2,6 razy szybszą."}, "glm-4-alltools": {"description": "GLM-4-AllTools to model inteligentny o wielu funkcjach, zoptymalizowany do wsparcia złożonego planowania instrukcji i wywołań narzędzi, takich jak przeglądanie sieci, interpretacja kodu i generowanie tekstu, odpowiedni do wykonywania wielu zadań."}, "glm-4-flash": {"description": "GLM-4-Flash to idealny wybór do przetwarzania prostych zadań, najszybszy i najtańszy."}, "glm-4-flash-250414": {"description": "GLM-4-Flash to idealny wybór do prostych zadań, najszybszy i darmowy."}, "glm-4-flashx": {"description": "GLM-4-FlashX to ulepszona wersja Flash, charakteryzująca się niezwykle szybkim czasem wnioskowania."}, "glm-4-long": {"description": "GLM-4-Long obsługuje ultra-długie wejścia tekstowe, odpowiednie do zadań pamięciowych i przetwarzania dużych dokumentów."}, "glm-4-plus": {"description": "GLM-4-Plus jako flagowy model o wysokiej inteligencji, posiada potężne zdolności przetwarzania długich tekstów i złożonych zadań, z ogólnym wzrostem wydajności."}, "glm-4.1v-thinking-flash": {"description": "Seria modeli GLM-4.1V-Thinking to najsilniejsze znane modele wizualno-językowe (VLM) na poziomie 10 miliardów parametrów, integrujące najnowocześniejsze zadania wizualno-językowe na tym poziomie, w tym rozumienie wideo, pytania i odpowiedzi na obrazach, rozwiązywanie problemów naukowych, rozpoznawanie tekstu OCR, interpretację dokumentów i wykresów, agenta GUI, kodowanie front-endowe stron internetowych, grounding i inne. Wiele z tych zadań przewyższa możliwości modelu Qwen2.5-VL-72B, który ma ponad 8 razy więcej parametrów. Dzięki zaawansowanym technikom uczenia ze wzmocnieniem model opanował rozumowanie łańcuchowe, co znacząco poprawia dokładność i bogactwo odpowiedzi, przewyższając tradycyjne modele bez mechanizmu thinking pod względem końcowych rezultatów i interpretowalności."}, "glm-4.1v-thinking-flashx": {"description": "Seria modeli GLM-4.1V-Thinking to najsilniejsze znane modele wizualno-językowe (VLM) na poziomie 10 miliardów parametrów, integrujące najnowocześniejsze zadania wizualno-językowe na tym poziomie, w tym rozumienie wideo, pytania i odpowiedzi na obrazach, rozwiązywanie problemów naukowych, rozpoznawanie tekstu OCR, interpretację dokumentów i wykresów, agenta GUI, kodowanie front-endowe stron internetowych, grounding i inne. Wiele z tych zadań przewyższa możliwości modelu Qwen2.5-VL-72B, który ma ponad 8 razy więcej parametrów. Dzięki zaawansowanym technikom uczenia ze wzmocnieniem model opanował rozumowanie łańcuchowe, co znacząco poprawia dokładność i bogactwo odpowiedzi, przewyższając tradycyjne modele bez mechanizmu thinking pod względem końcowych rezultatów i interpretowalności."}, "glm-4.5": {"description": "<PERSON><PERSON><PERSON><PERSON> flagowy model <PERSON><PERSON><PERSON><PERSON>, wspiera<PERSON><PERSON><PERSON> tryb myślenia, osiągający poziom SOTA wśród otwartych modeli pod względem wszechstronnych zdolności, z długością kontekstu do 128K tokenów."}, "glm-4.5-air": {"description": "Lżejsza wersja GLM-4.5, <PERSON><PERSON><PERSON><PERSON><PERSON> wydajność i opłacalność, z możliwością elastycznego przełączania hybrydowego trybu myślenia."}, "glm-4.5-airx": {"description": "Ekspresowa wersja GLM-4.5-Air, oferująca szy<PERSON>zy czas reakcji, zaprojektowana do zastosowań wymagających dużej skali i wysokiej prędkości."}, "glm-4.5-flash": {"description": "Bezpłatna wersja GLM-4.5, <PERSON><PERSON><PERSON>żniająca się doskonałą wydajnością w zadaniach inferencyjnych, kodowania i agentów."}, "glm-4.5-x": {"description": "Ekspresowa wersja GLM-4.5, łącząca wysoką wydajność z prędkością generowania do 100 tokenów na sekundę."}, "glm-4v": {"description": "GLM-4V oferuje potężne zdolności rozumienia i wnioskowania obrazów, obsługując różne zadania wizualne."}, "glm-4v-flash": {"description": "GLM-4V-Flash koncentruje się na efektywnym zrozumieniu pojedynczego obrazu, idealny do scenariuszy szybkiej analizy obrazu, takich jak analiza obrazów w czasie rzeczywistym lub przetwarzanie partii obrazów."}, "glm-4v-plus": {"description": "GLM-4V-Plus ma zdolność rozumienia treści wideo oraz wielu obrazów, odpowiedni do zadań multimodalnych."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus posiada zdolność rozumienia treści wideo oraz wielu obrazów, odpowiedni do zadań multimodalnych."}, "glm-z1-air": {"description": "Model wnioskowania: posiadaj<PERSON><PERSON> silne zdolności wnioskowania, odpowiedni do zadań wymagających głębokiego wnioskowania."}, "glm-z1-airx": {"description": "Ekstremalne wnioskowanie: charakteryzujące się ultra szybkim tempem wnioskowania i silnymi efektami wnioskowania."}, "glm-z1-flash": {"description": "Seria GLM-Z1 charakteryzuje się silnymi zdolnościami do złożonego wnioskowania, osiągając doskonałe wyniki w logice, matematyce i programowaniu."}, "glm-z1-flashx": {"description": "Wysoka prędkość i niska cena: wersja wzbogacona Flash, ultra szybkie tempo inferencji i lepsza obsługa współbieżności."}, "glm-zero-preview": {"description": "GLM-Zero-Preview posiada silne zdolności do złożonego wnioskowania, wyróżniając się w dziedzinach takich jak wnioskowanie logiczne, matematyka i programowanie."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash oferuje funkcje i ulepszenia nowej generacji, w tym doskonałą pr<PERSON><PERSON><PERSON><PERSON>, natywne korzystanie z narzędzi, generowanie multimodalne oraz okno kontekstowe o długości 1M tokenów."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental to najn<PERSON>zy eksperymentalny model AI Google, który w porównaniu do wcześniejszych wersji wykazuje pewne poprawy jako<PERSON>ci, szczególnie w zakresie wiedzy o świecie, kodu i długiego kontekstu."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash to najnowocześniejszy model główny Google, zaprojektowany specjalnie do zaawansowanego rozumowania, kodowania, matematyki i zadań naukowych. Zawiera wbudowaną zdolność „myślenia”, co pozwala na generowanie odpowiedzi o wyższej dokładności i bardziej szczegółowej analizie kontekstu.\n\nUwaga: ten model ma dwie odmiany: z myśleniem i bez myślenia. Cena wyjścia różni się znacząco w zależności od tego, czy zdolność myślenia jest aktywna. Jeśli wybierzesz standardową odmianę (bez sufiksu „:thinking”), model wyraźnie unika generowania tokenów myślenia.\n\nAby skorzystać ze zdolności myślenia i otrzymywać tokeny myślenia, musisz wybrać odmian<PERSON> „:thinking”, co wiąże się z wyższą ceną za wyjście myślenia.\n\nPonadto Gemini 2.5 Flash można konfigurować za pomocą parametru „maksymalna liczba tokenów do rozumowania”, jak opisano w dokumentacji (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash to najnowocześniejszy model główny Google, zaprojektowany z myślą o zaawansowanym wnioskowaniu, k<PERSON><PERSON><PERSON><PERSON>, matemat<PERSON>ce i zadaniach naukowych. Zawiera wbudowaną zdolność 'myślenia', co pozwala mu na dostarczanie odpowiedzi z wyższą dokładnością i szczegółowym przetwarzaniem kontekstu.\n\nUwaga: ten model ma dwa warianty: myślenie i niemyslenie. Ceny wyjściowe różnią się znacznie w zależności od tego, czy zdolność myślenia jest aktywowana. Je<PERSON><PERSON> wybierzesz standardowy wariant (bez sufiksu ':thinking'), model wyraźnie unika generowania tokenów myślenia.\n\nAby skorzystać z zdolności myślenia i otrzymać tokeny myślenia, musisz wybrać wariant ':thinking', co spowoduje wyższe ceny wyjściowe za myślenie.\n\nPonadto Gemini 2.5 Flash można konfigurować za pomocą parametru 'maksymalna liczba tokenów do wnioskowania', jak opisano w dokumentacji (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash to najnowocześniejszy model główny Google, zaprojektowany z myślą o zaawansowanym wnioskowaniu, k<PERSON><PERSON><PERSON><PERSON>, matemat<PERSON>ce i zadaniach naukowych. Zawiera wbudowaną zdolność 'myślenia', co pozwala mu na dostarczanie odpowiedzi z wyższą dokładnością i szczegółowym przetwarzaniem kontekstu.\n\nUwaga: ten model ma dwa warianty: myślenie i niemyslenie. Ceny wyjściowe różnią się znacznie w zależności od tego, czy zdolność myślenia jest aktywowana. Je<PERSON><PERSON> wybierzesz standardowy wariant (bez sufiksu ':thinking'), model wyraźnie unika generowania tokenów myślenia.\n\nAby skorzystać z zdolności myślenia i otrzymać tokeny myślenia, musisz wybrać wariant ':thinking', co spowoduje wyższe ceny wyjściowe za myślenie.\n\nPonadto Gemini 2.5 Flash można konfigurować za pomocą parametru 'maksymalna liczba tokenów do wnioskowania', jak opisano w dokumentacji (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro to najnowocześniejszy model myślowy Google, zdolny do rozumowania nad złożonymi problemami w dziedzinie kodowania, matematyki i STEM oraz do analizy dużych zbiorów danych, repozytoriów kodu i dokumentów z wykorzystaniem długiego kontekstu."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview to najnowocześniejszy model myślowy Google, zdolny do rozumowania nad złożonymi problemami w dziedzinie kodowania, matematyki i STEM oraz do analizy dużych zbiorów danych, repozytoriów kodu i dokumentów przy użyciu długiego kontekstu."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash oferuje zoptymalizowane możliwości przetwarzania multimodalnego, odpowiednie do różnych złożonych scenariuszy zadań."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro łączy najnowsze technologie optymalizacji, oferując bardziej efektywne przetwarzanie danych multimodalnych."}, "google/gemma-2-27b": {"description": "Gemma 2 to wydajny model w<PERSON><PERSON> przez <PERSON>, obejmuj<PERSON><PERSON> różnorodne scenar<PERSON>ze zastosowań, od małych aplikacji po złożone przetwarzanie danych."}, "google/gemma-2-27b-it": {"description": "Gemma 2 kontynuuje ideę lekkiego i wydajnego projektowania."}, "google/gemma-2-2b-it": {"description": "Lekki model dostosowywania instrukcji od Google."}, "google/gemma-2-9b": {"description": "Gemma 2 to wydajny model w<PERSON><PERSON> przez <PERSON>, obejmuj<PERSON><PERSON> różnorodne scenar<PERSON>ze zastosowań, od małych aplikacji po złożone przetwarzanie danych."}, "google/gemma-2-9b-it": {"description": "Gemma 2 to lekka seria modeli tekstowych open source od Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 to odchudzona seria otwartych modeli tekstowych Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) oferuje podstawowe możliwości przetwarzania poleceń, idealne do lekkich aplikacji."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B to otwarty model językowy Google, ustanawiający nowe standardy w zakresie efektywności i wydajności."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B to otwarty model j<PERSON><PERSON><PERSON><PERSON> stworzony przez Google, kt<PERSON>ry ustanowił nowe standardy w zakresie wydajności i efektywności."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo, odpowiedni do różnych zadań generowania i rozumienia tekstu, obecnie wskazuje na gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo, odpowiedni do różnych zadań generowania i rozumienia tekstu, obecnie wskazuje na gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo, odpowiedni do różnych zadań generowania i rozumienia tekstu, obecnie wskazuje na gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo, odpowiedni do różnych zadań generowania i rozumienia tekstu, obecnie wskazuje na gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo to wydajny model dostarczany przez OpenAI, idealny do obsługi zadań związanych z czatowaniem i generowaniem tekstu, wspierający równoległe wywołania funkcji."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k, model do generowania tekstu o dużej pojem<PERSON>ci, odpowiedni do bardziej złożonych zadań."}, "gpt-4": {"description": "GPT-4 ofer<PERSON>je większe okno kontekstowe, zdolne do przetwarzania dłuższych wejść tekstowych, co czyni go odpowiednim do scenariuszy wymagających szerokiej integracji informacji i analizy danych."}, "gpt-4-0125-preview": {"description": "Najnowszy model GPT-4 Turbo posiada funkcje wizualne. Teraz zapytania wizualne mogą być obsługiwane za pomocą formatu JSON i wywołań funkcji. GPT-4 Turbo to ulepszona wersja, która oferuje opłacalne wsparcie dla zadań multimodalnych. Znajduje równowagę między dokładnością a wydajnością, co czyni go odpowiednim do aplikacji wymagających interakcji w czasie rzeczywistym."}, "gpt-4-0613": {"description": "GPT-4 ofer<PERSON>je większe okno kontekstowe, zdolne do przetwarzania dłuższych wejść tekstowych, co czyni go odpowiednim do scenariuszy wymagających szerokiej integracji informacji i analizy danych."}, "gpt-4-1106-preview": {"description": "Najnowszy model GPT-4 Turbo posiada funkcje wizualne. Teraz zapytania wizualne mogą być obsługiwane za pomocą formatu JSON i wywołań funkcji. GPT-4 Turbo to ulepszona wersja, która oferuje opłacalne wsparcie dla zadań multimodalnych. Znajduje równowagę między dokładnością a wydajnością, co czyni go odpowiednim do aplikacji wymagających interakcji w czasie rzeczywistym."}, "gpt-4-32k": {"description": "GPT-4 ofer<PERSON>je większe okno kontekstowe, zdolne do przetwarzania dłuższych wejść tekstowych, co czyni go odpowiednim do scenariuszy wymagających szerokiej integracji informacji i analizy danych."}, "gpt-4-32k-0613": {"description": "GPT-4 ofer<PERSON>je większe okno kontekstowe, zdolne do przetwarzania dłuższych wejść tekstowych, co czyni go odpowiednim do scenariuszy wymagających szerokiej integracji informacji i analizy danych."}, "gpt-4-turbo": {"description": "Najnowszy model GPT-4 Turbo posiada funkcje wizualne. Teraz zapytania wizualne mogą być obsługiwane za pomocą formatu JSON i wywołań funkcji. GPT-4 Turbo to ulepszona wersja, która oferuje opłacalne wsparcie dla zadań multimodalnych. Znajduje równowagę między dokładnością a wydajnością, co czyni go odpowiednim do aplikacji wymagających interakcji w czasie rzeczywistym."}, "gpt-4-turbo-2024-04-09": {"description": "Najnowszy model GPT-4 Turbo posiada funkcje wizualne. Teraz zapytania wizualne mogą być obsługiwane za pomocą formatu JSON i wywołań funkcji. GPT-4 Turbo to ulepszona wersja, która oferuje opłacalne wsparcie dla zadań multimodalnych. Znajduje równowagę między dokładnością a wydajnością, co czyni go odpowiednim do aplikacji wymagających interakcji w czasie rzeczywistym."}, "gpt-4-turbo-preview": {"description": "Najnowszy model GPT-4 Turbo posiada funkcje wizualne. Teraz zapytania wizualne mogą być obsługiwane za pomocą formatu JSON i wywołań funkcji. GPT-4 Turbo to ulepszona wersja, która oferuje opłacalne wsparcie dla zadań multimodalnych. Znajduje równowagę między dokładnością a wydajnością, co czyni go odpowiednim do aplikacji wymagających interakcji w czasie rzeczywistym."}, "gpt-4-vision-preview": {"description": "Najnowszy model GPT-4 Turbo posiada funkcje wizualne. Teraz zapytania wizualne mogą być obsługiwane za pomocą formatu JSON i wywołań funkcji. GPT-4 Turbo to ulepszona wersja, która oferuje opłacalne wsparcie dla zadań multimodalnych. Znajduje równowagę między dokładnością a wydajnością, co czyni go odpowiednim do aplikacji wymagających interakcji w czasie rzeczywistym."}, "gpt-4.1": {"description": "GPT-4.1 to nasz flagowy model do złożonych zadań. Doskonale nadaje się do rozwiązywania problemów w różnych dziedzinach."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini oferuje równowagę między inteligencją, s<PERSON><PERSON><PERSON>ścią a kosztami, co czyni go atrakcyjnym modelem w wielu zastosowaniach."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini oferuje równowagę między inteligencją, s<PERSON><PERSON><PERSON>ścią a kosztami, co czyni go atrakcyjnym modelem w wielu zastosowaniach."}, "gpt-4.5-preview": {"description": "Wersja badawcza GPT-4.5, kt<PERSON>ra jest naszym największym i najpotężniejszym modelem GPT do tej pory. Posiada szeroką wiedzę o świecie i lepiej rozumie intencje użytkowników, co sprawia, że doskonale radzi sobie w zadaniach kreatywnych i autonomicznym planowaniu. GPT-4.5 akceptuje tekstowe i graficzne wejścia oraz generuje wyjścia tekstowe (w tym wyjścia strukturalne). Wspiera kluczowe funkcje dla deweloperów, takie jak wywołania funkcji, API wsadowe i strumieniowe wyjścia. W zadaniach wymagających kreatywności, otwartego myślenia i dialogu (takich jak pisanie, nauka czy odkrywanie nowych pomysłów), GPT-4.5 sprawdza się szczególnie dobrze. Data graniczna wiedzy to październik 2023."}, "gpt-4o": {"description": "ChatGPT-4o to <PERSON>z<PERSON> model, który jest na bieżąco aktualizowany, aby utr<PERSON><PERSON><PERSON> najnowszą wersję. Łączy potężne zdolności rozumienia i generowania języka, co czyni go odpowiednim do zastosowań na dużą skalę, w tym obsługi klienta, edukacji i wsparcia technicznego."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o to <PERSON>z<PERSON> model, który jest na bieżąco aktualizowany, aby utr<PERSON><PERSON><PERSON> najnowszą wersję. Łączy potężne zdolności rozumienia i generowania języka, co czyni go odpowiednim do zastosowań na dużą skalę, w tym obsługi klienta, edukacji i wsparcia technicznego."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o to <PERSON>z<PERSON> model, który jest na bieżąco aktualizowany, aby utr<PERSON><PERSON><PERSON> najnowszą wersję. Łączy potężne zdolności rozumienia i generowania języka, co czyni go odpowiednim do zastosowań na dużą skalę, w tym obsługi klienta, edukacji i wsparcia technicznego."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o to dynamiczny model, aktualizowany w czasie rzeczywistym, aby być zawsze na bieżąco z najnowszą wersją. Łączy potężne zdolności rozumienia i generowania języka, idealny do zastosowań w dużej skali, w tym obsłudze klienta, edukacji i wsparciu technicznym."}, "gpt-4o-audio-preview": {"description": "Model audio GPT-4o, obsługujący wejście i wyjście audio."}, "gpt-4o-mini": {"description": "GPT-4o mini to na<PERSON><PERSON><PERSON> model OpenAI, wprowadzony po GPT-4 Omni, obsługujący wejścia tekstowe i wizualne oraz generujący tekst. Jako ich najnowocześniejszy model w małej skali, jest znacznie tańszy niż inne niedawno wprowadzone modele, a jego cena jest o ponad 60% niższa niż GPT-3.5 Turbo. Utrzymuje na<PERSON>ze<PERSON>nie<PERSON> inteligencję, jednocześnie oferując znaczną wartość za pieniądze. GPT-4o mini uzyskał wynik 82% w teście MMLU i obecnie zajmuje wyższą pozycję w preferencjach czatu niż GPT-4."}, "gpt-4o-mini-audio-preview": {"description": "Model GPT-4o mini Audio, obsługujący wejście i wyjście audio."}, "gpt-4o-mini-realtime-preview": {"description": "Wersja na żywo GPT-4o-mini, obsługująca wejście i wyjście audio oraz tekstowe w czasie rzeczywistym."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini wersja podglądowa do wyszukiwania to model specjalnie wytrenowany do rozumienia i realizacji zapytań wyszukiwania internetowego, korzystający z API Chat Completions. Poza opłatami za tokeny, zapytania wyszukiwania internetowego są dodatkowo obciążane opłatą za każde wywołanie narzędzia."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe to model konwersji mowy na tekst wykorzystujący GPT-4o do transkrypcji audio. W porównaniu z oryginalnym modelem Whisper poprawia wskaźnik błędów słów oraz rozpoznawanie i dokładność językową. Użyj go, aby uzyskać dokładniejsze transkrypcje."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS to model tekstu na mowę oparty na GPT-4o mini, oferujący wysokiej jakości generowanie mowy przy niższych kosztach."}, "gpt-4o-realtime-preview": {"description": "Wersja na żywo GPT-4o, obsługująca wejście i wyjście audio oraz tekstowe w czasie rzeczywistym."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Wersja na żywo GPT-4o, obsługująca wejście i wyjście audio oraz tekstowe w czasie rzeczywistym."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Wersja GPT-4o w czasie rzeczywistym, obsługująca jednoczesne wejście i wyjście audio oraz tekstu."}, "gpt-4o-search-preview": {"description": "GPT-4o wersja podglądowa do wyszukiwania to model specjalnie wytrenowany do rozumienia i realizacji zapytań wyszukiwania internetowego, korzystający z API Chat Completions. Poza opłatami za tokeny, zapytania wyszukiwania internetowego są dodatkowo obciążane opłatą za każde wywołanie narzędzia."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe to model konwersji mowy na tekst wykorzystujący GPT-4o do transkrypcji audio. W porównaniu z oryginalnym modelem Whisper poprawia wskaźnik błędów słów oraz rozpoznawanie i dokładność językową. Użyj go, aby uzyskać dokładniejsze transkrypcje."}, "gpt-image-1": {"description": "Natywny multimodalny model generowania obrazów ChatGPT."}, "grok-2-1212": {"description": "Model ten poprawił <PERSON>, przestrzeganie instrukcji oraz zdolności wielojęzyczne."}, "grok-2-image-1212": {"description": "<PERSON><PERSON>jn<PERSON>zy model generowania obrazów potrafi tworzyć żywe i realistyczne obrazy na podstawie tekstowych wskazówek. Sprawdza się doskonale w marketingu, mediach społecznościowych i rozrywce."}, "grok-2-vision-1212": {"description": "Model ten poprawił <PERSON>, przestrzeganie instrukcji oraz zdolności wielojęzyczne."}, "grok-3": {"description": "Flagowy model, specjalizujący się w ekstrakcji danych, programowaniu i streszczaniu tekstów na poziomie korporacyjnym, z głęboką wiedzą w dziedzinach finansów, medycyny, prawa i nauki."}, "grok-3-fast": {"description": "Flagowy model, specjalizujący się w ekstrakcji danych, programowaniu i streszczaniu tekstów na poziomie korporacyjnym, z głęboką wiedzą w dziedzinach finansów, medycyny, prawa i nauki."}, "grok-3-mini": {"description": "<PERSON><PERSON><PERSON> model, który najpierw analizuje przed rozmową. Działa szybko i inteligentnie, odpowiedni do zadań logicznych nie wymagających głębokiej wiedzy d<PERSON>d<PERSON>, z możliwością śledzenia pierwotnego toku myślenia."}, "grok-3-mini-fast": {"description": "<PERSON><PERSON><PERSON> model, który najpierw analizuje przed rozmową. Działa szybko i inteligentnie, odpowiedni do zadań logicznych nie wymagających głębokiej wiedzy d<PERSON>d<PERSON>, z możliwością śledzenia pierwotnego toku myślenia."}, "grok-4": {"description": "Nasz najnowszy i najpotężniejszy model flagowy, który wyróżnia się doskonałymi wynikami w przetwarzaniu języka naturalnego, obliczeniach matematycznych i rozumowaniu — to idealny wszechstronny zawodnik."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B to model językowy łączący kreatywność i inteligencję, zintegrowany z wieloma wiodącymi modelami."}, "hunyuan-a13b": {"description": "Hunyuan to pier<PERSON><PERSON> h<PERSON> model rozumowania, b<PERSON><PERSON><PERSON><PERSON> ulep<PERSON>oną wersją hunyuan-standard-256K, z 80 miliardami parametrów i 13 miliardami aktywowanych. Domyślnie działa w trybie wolnego myślenia, ale obsługuje przełączanie między trybami szybkiego i wolnego myślenia za pomocą parametrów lub instrukcji; przełączanie odbywa się przez dodanie / no_think przed zapytaniem. Ogólne zdolności modelu znacznie przewyższają poprzednią generację, zwłaszcza w matematyce, nauka<PERSON> ścisłych, rozumieniu długich tekstów i zdolnościach agenta."}, "hunyuan-code": {"description": "Najnowocześniejszy model generowania kodu Hunyuan, przeszkolony na bazie 200B wysokiej jakości danych kodu, z półrocznym treningiem na wysokiej jakości danych SFT, z wydłużonym oknem kontekstowym do 8K, zajmującym czołowe miejsca w automatycznych wskaźnikach oceny generowania kodu w pięciu językach; w ocenie jakościowej zadań kodowych w pięciu językach, osiąga wyniki w pierwszej lidze."}, "hunyuan-functioncall": {"description": "Najnowocześniejszy model FunctionCall w architekturze MOE Hunyuan, przeszkolony na wysokiej jakości danych FunctionCall, z oknem kontekstowym o długości 32K, osiągający wiodące wyniki w wielu wymiarach oceny."}, "hunyuan-large": {"description": "Model Hunyuan-large ma całkowitą liczbę parametrów wynoszącą około 389B, z aktywowanymi parametrami wynoszącymi około 52B, co czyni go obecnie największym i najlepiej działającym modelem MoE w architekturze Transformer w branży."}, "hunyuan-large-longcontext": {"description": "Specjalizuje się w zadaniach związanych z długimi tekstami, takich jak streszczenia dokumentów i pytania i odpowiedzi dotyczące dokumentów, a także ma zdolność do obsługi ogólnych zadań generowania tekstu. Wykazuje doskonałe wyniki w analizie i generowaniu długich tekstów, skutecznie radząc sobie z złożonymi i szczegółowymi wymaganiami dotyczącymi przetwarzania długich treści."}, "hunyuan-large-vision": {"description": "Model przeznaczony do scenariuszy rozumienia obrazów i tekstu, oparty na modelu Hunyuan Large, obsługujący dowolną rozdzielczość i wiele obrazów wraz z tekstem, generujący treści tekstowe, skupiający się na zadaniach związanych z rozumieniem obrazowo-tekstowym, z wyraźną poprawą zdolności wielojęzycznego rozumienia obrazów i tekstu."}, "hunyuan-lite": {"description": "Zaktualizowana do struktury MOE, z oknem kontekstowym o długości 256k, prowadzi w wielu zestawach testowych w NLP, kodowaniu, matematyce i innych dziedzinach w porównaniu do wielu modeli open source."}, "hunyuan-lite-vision": {"description": "Najnowocześniejszy model multimodalny 7B Hunyuan, z oknem kontekstowym 32K, wspierający multimodalne dialogi w języku chińskim i angielskim, rozpoznawanie obiektów w obrazach, zrozumienie dokumentów i tabel, multimodalną matematykę itp., z wynikami w wielu wymiarach lepszymi niż modele konkurencyjne 7B."}, "hunyuan-pro": {"description": "Model długiego tekstu MOE-32K o skali bilionów parametrów. Osiąga absolutnie wiodący poziom w różnych benchmarkach, obsługując złożone instrukcje i wnioskowanie, posiadając zaawansowane umiejętności matematyczne, wspierając wywołania funkcji, z optymalizacjami w obszarach takich jak tłumaczenia wielojęzyczne, prawo finansowe i medyczne."}, "hunyuan-role": {"description": "Najnowocześniejszy model odgrywania ról Hunyuan, stwo<PERSON><PERSON> przez oficjalne dostosowanie i trening Hunyuan, oparty na modelu Hunyuan i zestawie danych scenariuszy odgrywania ról, oferuj<PERSON><PERSON> lepsze podstawowe wyniki w scenariuszach odgrywania ról."}, "hunyuan-standard": {"description": "Zastosowano lepszą strategię <PERSON>u, jednocześnie łagodząc problemy z równoważeniem obciążenia i zbieżnością ekspertów. W przypadku długich tekstów wskaźnik 'znalezienia igły w stogu siana' osiąga 99,9%. MOE-32K oferuje lepszy stosunek jakości do ceny, równoważąc efektywność i cenę, umożliwiając przetwarzanie długich tekstów."}, "hunyuan-standard-256K": {"description": "Zastosowano lepszą strategię <PERSON>u, jednocześnie łagodząc problemy z równoważeniem obciążenia i zbieżnością ekspertów. W przypadku długich tekstów wskaźnik 'znalezienia igły w stogu siana' osiąga 99,9%. MOE-256K dokonuje dalszych przełomów w długości i efektywności, znacznie rozszerzając możliwą długość wejścia."}, "hunyuan-standard-vision": {"description": "Najnowocześniejszy model multimodalny Hunyuan, wspierający odpowiedzi w wielu j<PERSON>, z równoważnymi zdolnościami w języku chińskim i angielskim."}, "hunyuan-t1-20250321": {"description": "Kompleksowy model zdolności w naukach ścisłych i humanistycznych, z silną zdolnością do uchwycenia długich informacji tekstowych. Wspiera wnioskowanie w odpowiedzi na różnorodne trudności w matematyce, logice, naukach ścisłych i kodowaniu."}, "hunyuan-t1-20250403": {"description": "Zwiększenie zdolności generowania kodu na poziomie projektu; poprawa jakości pisania generowanego tekstu; ulepszenie wieloetapowego rozumienia tematów, przestrzegania instrukcji typu tob oraz rozumienia słów; optymalizacja problemów z mieszanym użyciem uproszczonych i tradycyjnych znaków oraz mieszanym językiem chińsko-angielskim."}, "hunyuan-t1-20250529": {"description": "Optymalizacja tworzenia tekstów, pisania <PERSON>ów, ulepszenie umiejętności w kodowaniu frontendowym, matematyce, rozumowaniu logicznym oraz zwiększenie zdolności do przestrzegania instrukcji."}, "hunyuan-t1-20250711": {"description": "Znacząca poprawa zdolności w zakresie zaawansowanej matematyki, logiki i kodowania, optymalizacja stabilności wyjścia modelu oraz zwiększenie zdolności do pracy z długimi tekstami."}, "hunyuan-t1-latest": {"description": "Pier<PERSON><PERSON> na świecie ultra-duży model wnioskowania Hybrid-Transformer-<PERSON><PERSON>, rozszerzaj<PERSON><PERSON> zdolności wnioskowania, z niezwykle szybkim <PERSON>, lepiej dostosowany do ludzkich preferencji."}, "hunyuan-t1-vision": {"description": "Model głębokiego myślenia multimodalnego Hunyuan, obsługuj<PERSON>cy natywne łańcuchy myślowe multimodalne, doskonały w różnych scenariuszach wnioskowania obrazowego, z wyraźną przewagą nad modelami szybkiego myślenia w rozwiązywaniu problemów ścisłych."}, "hunyuan-t1-vision-20250619": {"description": "<PERSON><PERSON><PERSON><PERSON> model wielomodalny t1-vision Hunyuan z głębokim rozumowaniem, obsługujący natywne łańcuchy myślowe wielomodalne, z kompleksową poprawą w stosunku do poprzedniej domyślnej wersji modelu."}, "hunyuan-turbo": {"description": "Hunyuan to nowa generacja dużego modelu językowego w wersji próbnej, wykorzystująca nową strukturę modelu mieszanych ekspertów (MoE), która w porównaniu do hunyuan-pro charakteryzuje się szybszą efektywnością wnioskowania i lepszymi wynikami."}, "hunyuan-turbo-20241223": {"description": "Optymalizacja tej wersji: skalowanie danych instrukcji, znaczne zwiększenie ogólnej zdolności generalizacji modelu; znaczne zwiększenie zdolności w zakresie matematyki, kodowania i rozumowania logicznego; optymalizacja zdolności związanych z rozumieniem tekstu i słów; optymalizacja jakości generowania treści w tworzeniu tekstów."}, "hunyuan-turbo-latest": {"description": "Ogólna optymalizacja doświadczeń, w tym zrozumienie NLP, tworzenie tekstów, rozmowy, pytania i odpowiedzi, tłumaczenia, obszary tematyczne itp.; zwiększenie humanizacji, optymalizacja inteligencji emocjonalnej modelu; poprawa zdolności modelu do aktywnego wyjaśniania w przypadku niejasnych intencji; poprawa zdolności do rozwiązywania problemów związanych z analizą słów; poprawa jakości i interaktywności twórczości; poprawa doświadczeń w wielokrotnych interakcjach."}, "hunyuan-turbo-vision": {"description": "Nowa generacja flagowego modelu językowo-wizualnego Hunyuan, wykorzystująca nową strukturę modelu mieszanych e<PERSON>pertów (MoE), z pełnym zwiększeniem zdolności w zakresie podstawowego rozpoznawania, tworzenia treści, pytań i odpowiedzi oraz analizy i rozumowania w porównaniu do poprzedniej generacji modeli."}, "hunyuan-turbos-20250313": {"description": "Ujednolicenie stylu kroków rozwiązywania zadań matematycznych, wzmocnienie wieloetapowego zadawania pytań matematycznych. Optymalizacja stylu odpowiedzi w tworzeniu tekstów, eliminacja sztuczności AI, wzbogacenie języka."}, "hunyuan-turbos-20250416": {"description": "Aktualizacja bazy pretrenowania, wzmacniająca zdolność rozumienia i przestrzegania instrukcji; w fazie dostrajania poprawa umiejętności matematycznych, programistycznych, logicznych i nauk ścisłych; podniesienie jakości twórczości literackiej, rozumienia tekstu, dokładności tłumaczeń oraz wiedzy ogólnej; wzmocnienie zdolności agentów w różnych dziedzinach, ze szczególnym naciskiem na rozumienie wieloetapowych dialogów."}, "hunyuan-turbos-20250604": {"description": "Ulepszona baza pretrenowania, poprawa umiejętności pisania i rozumienia tekstu, znaczne zwiększenie zdolności w kodowaniu i naukach ścisłych, ciągłe doskonalenie w realizacji złożonych poleceń."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS to najnowsza wersja flagowego modelu Hunyuan, oferująca silniejsze zdolności myślenia i lepsze efekty doświadczenia."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Specjalizuje się w zadaniach związanych z długimi tekstami, takimi jak streszczenia dokumentów i pytania do dokumentów, a także ma zdolność do generowania ogólnych tekstów. W analizie i generowaniu długich tekstów wykazuje doskonałe wyniki, skutecznie radząc sobie z złożonymi i szczegółowymi wymaganiami przetwarzania długich treści."}, "hunyuan-turbos-role-plus": {"description": "Najnowsza wersja modelu do odgrywania ról Hunyuan, oficjalnie dostrojona przez Hunyuan, oparta na modelu Hunyuan i wzbogacona o dane scenariuszy odgrywania ról, zapewniająca lepsze podstawowe efekty w tych scenariuszach."}, "hunyuan-turbos-vision": {"description": "Model przeznaczony do zadań rozumienia obrazów i tekstu, oparty na najnowszym modelu turbos Hunyuan, b<PERSON>dący nową generacją flagowego modelu wizualno-językowego. Skupia się na zadaniach związanych z rozpoznawaniem obiektów na obrazach, pytaniami i odpowiedziami opartymi na wiedzy, tworzeniem tekstów reklamowych, rozwiązywaniem problemów na podstawie zdjęć i innych, z kompleksową poprawą w stosunku do poprzedniej generacji."}, "hunyuan-turbos-vision-20250619": {"description": "Najnowszy flagowy model wizualno-językowy turbos-vision Hunyuan, z kompleksową poprawą w zadaniach związanych z rozumieniem obrazów i tekstu, w tym rozpoznawaniem obiektów na obrazach, pytaniami i odpowiedziami opartymi na wiedzy, tworzeniem tekstów reklamowych, rozwiązywaniem problemów na podstawie zdjęć, w porównaniu do poprzedniej domyślnej wersji modelu."}, "hunyuan-vision": {"description": "Najnow<PERSON><PERSON>śniejszy model multimodalny Hunyuan, wspierający generowanie treści tekstowych na podstawie obrazów i tekstu."}, "image-01": {"description": "Nowy model generowania obrazów o delikatnej jakości wizualnej, wspierający generację obrazów na podstawie tekstu oraz obrazów na podstawie obrazów."}, "image-01-live": {"description": "Model generowania obrazów o delikatnej jakości wizualnej, wspierający generację obrazów na podstawie tekstu z możliwością ustawienia stylu."}, "imagen-4.0-generate-preview-06-06": {"description": "Seria modeli tekst-na-obraz Imagen czwartej generacji"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Seria modeli tekst-na-obraz Imagen czwartej generacji, wersja Ultra"}, "imagen4/preview": {"description": "Najwy<PERSON><PERSON><PERSON> j<PERSON> model generowania obrazów Google."}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 oferuje inteligentne rozwiązania dialogowe w różnych scenariuszach."}, "internlm2.5-latest": {"description": "Nasza najnowsza seria modeli, charakteryzująca się doskonałymi osiągami wnioskowania, obsługująca długość kontekstu do 1M oraz lepsze możliwości śledzenia instrukcji i wywoływania narzędzi."}, "internlm3-latest": {"description": "Nasza najnowsza seria modeli, charakteryzująca się doskonałą wydajnością wnioskowania, prowadzi wśród modeli open-source o podobnej skali. Domyślnie wskazuje na naszą najnowszą wersję modelu InternLM3."}, "internvl2.5-latest": {"description": "Wersja InternVL2.5, kt<PERSON><PERSON><PERSON> nadal utr<PERSON><PERSON>my, charakteryzuje się doskonałą i stabilną wydajnością. Domyślnie wskazuje na nasz najnowszy model z serii InternVL2.5, obecnie wskazuje na internvl2.5-78b."}, "internvl3-latest": {"description": "<PERSON><PERSON> najn<PERSON>zy model multimodalny, kt<PERSON>ry ma silniejsze zdolności rozumienia tekstu i obrazów oraz długoterminowego rozumienia obrazów, osiągający wyniki porównywalne z najlepszymi modelami zamkniętymi. Domyślnie wskazuje na nasz najnowszy model z serii InternVL, obecnie wskazuje na internvl3-78b."}, "irag-1.0": {"description": "Opracowana przez Baidu technologia iRAG (image based RAG) to wzmacniana wyszukiwaniem generacja obrazów na podstawie tekstu, łącząca miliardowe zasoby obrazów Baidu z potężnymi możliwościami modelu bazowego. Pozwala generować niezwykle realistyczne obrazy, znacznie przewyższając natywne systemy generacji tekst-na-obraz, eliminując sztuczny efekt AI i przy niskich kosztach. iRAG cechuje się brakiem halucynacji, ultra-realistycznym wyglądem i natychmiastową dostępnością."}, "jamba-large": {"description": "Nasz najsilniejszy i najbardziej zaawansowany model, zaprojektowany do obsługi złożonych zadań na poziomie przedsiębiorstw, oferujący doskonałą wydajność."}, "jamba-mini": {"description": "Najbard<PERSON>j efektywny model w swojej klasie, <PERSON><PERSON><PERSON><PERSON><PERSON> szybkość z jakością, o mniejszych rozmiarach."}, "jina-deepsearch-v1": {"description": "Głębokie wyszukiwanie łączy wyszukiwanie w sieci, czytanie i wnioskowanie, umożliwiając kompleksowe badania. <PERSON><PERSON><PERSON><PERSON> to traktować jako agenta, który przyjmuje Twoje zadania badawcze - przeprowadza szerokie poszukiwania i wielokrotne iteracje, zanim poda odpowiedź. Proces ten obejmuje ciągłe badania, wnioskowanie i rozwiązywanie problemów z różnych perspektyw. To zasadniczo różni się od standardowych dużych modeli, które generują odpowiedzi bezpośrednio z wstępnie wytrenowanych danych oraz od tradycyjnych systemów RAG, które polegają na jednorazowym powierzchownym wyszukiwaniu."}, "kimi-k2": {"description": "Kimi-K2 to podstawowy model architektury MoE opracowany przez Moonshot AI, wyposażony w potężne zdolności kodowania i agenta, z łączną liczbą parametrów 1 biliona i 32 miliardami aktywowanych parametrów. W testach wydajności w zakresie ogólnej wiedzy, programowania, matematyki i zadań agenta model K2 przewyższa inne popularne otwarte modele."}, "kimi-k2-0711-preview": {"description": "kimi-k2 to podstawowy model architektury MoE o potężnych zdolnościach kodowania i agenta, z łączną liczbą parametrów 1T i 32B aktywowanych parametrów. W testach wydajności na benchmarkach obejmujących ogólne rozumowanie, programowanie, matematykę i agentów model K2 przewyższa inne popularne modele open source."}, "kimi-latest": {"description": "Produkt <PERSON>i Smart Assistant korzy<PERSON> z najnowszego modelu <PERSON>, kt<PERSON><PERSON> może zawierać cechy jeszcze niestabilne. Obsługuje zrozumienie obrazów i automatycznie wybiera model 8k/32k/128k jako model rozliczeniowy w zależności od długości kontekstu żądania."}, "kimi-thinking-preview": {"description": "Model kimi-thinking-preview dostar<PERSON><PERSON> przez <PERSON>’s Dark Side to multimodalny model <PERSON><PERSON><PERSON><PERSON> z umiejętnościami ogólnego i głębokiego rozumowania, kt<PERSON><PERSON> pomaga rozwiązywać bardziej złożone i trudniejsze problemy."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM to eksperymentalny model <PERSON><PERSON><PERSON><PERSON><PERSON>, specyficzny dla zadań, przeszkolony zgodnie z zasadami nauki o uczeniu się, który może przestrzegać systemowych instrukcji w scenariuszach nauczania i uczenia się, pełniąc rolę eksperta mentora."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM to eksperymentalny model <PERSON><PERSON><PERSON><PERSON><PERSON>, specyficzny dla zadań, przeszkolony zgodnie z zasadami nauki o uczeniu się, który może przestrzegać systemowych instrukcji w scenariuszach nauczania i uczenia się, pełniąc rolę eksperta mentora."}, "lite": {"description": "Spark Lite to lekki model j<PERSON><PERSON><PERSON><PERSON> o dużej skali, charaktery<PERSON><PERSON><PERSON><PERSON> się niezwykle niskim opóźnieniem i wysoką wydajnością przetwarzania, całkowicie darmowy i otwarty, wspierający funkcje wyszukiwania w czasie rzeczywistym. Jego cechy szybkiej reakcji sprawiają, że doskonale sprawdza się w zastosowaniach inferencyjnych na urządzeniach o niskiej mocy obliczeniowej oraz w dostosowywaniu modeli, oferując użytkownikom znakomity stosunek kosztów do korzyści oraz inteligentne doświadczenie, szczególnie w kontekście pytań i odpowiedzi, generowania treści oraz wyszukiwania."}, "llama-2-7b-chat": {"description": "Llama2 to seria modeli językowych (LLM) opracowanych i udostępnionych przez Meta, obejmująca modele generujące tekst o różnej skali, od 7 miliardów do 70 miliardów parametrów, które przeszły wstępną naukę i dostrajanie. Na poziomie architektury, Llama2 jest modelem językowym optymalizowanym za pomocą architektury transformerowej. Zdolność do dostosowywania modeli do preferencji ludzi pod względem użyteczności i bezpieczeństwa została osiągnięta poprzez nadzorowane dostrajanie (SFT) i uczenie wzmacnianie z uwzględnieniem opinii ludzi (RLHF). Llama2 osiąga lepsze wyniki niż poprzednia seria Llama na wielu zbiorach danych akademickich, co dostarcza inspiracji dla projektowania i tworzenia wielu innych modeli."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B oferuje potężne możliwości wnioskowania AI, odpowiednie do złożonych zastosowań, wspierające ogromne przetwarzanie obliczeniowe przy zachowaniu efektywności i dokładności."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B to model o wys<PERSON><PERSON>, oferujący szybkie możliwości generowania tekstu, idealny do zastosowań wymagających dużej efektywności i opłacalności."}, "llama-3.1-instruct": {"description": "Model Llama 3.1 zoptymalizowany do rozmów przewyższa wiele istniejących open-source modeli czatowych w standardowych testach branżowych."}, "llama-3.2-11b-vision-instruct": {"description": "Wyjątkowe zdolności wnioskowania wizualnego na obrazach o wysokiej rozdzielczości, idealne do zastosowań związanych ze zrozumieniem wizualnym."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 jest zaprojektowana do obsługi zadań łączących dane wizualne i tekstowe. Wykazuje doskonałe wyniki w zadaniach takich jak opisywanie obrazów i wizualne pytania i odpowiedzi, przekraczając przepaść między generowaniem języka a wnioskowaniem wizualnym."}, "llama-3.2-90b-vision-instruct": {"description": "Zaawansowane zdolności wnioskowania obrazów dla zastosowań w agentach zrozumienia wizualnego."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 jest zaprojektowana do obsługi zadań łączących dane wizualne i tekstowe. Wykazuje doskonałe wyniki w zadaniach takich jak opisywanie obrazów i wizualne pytania i odpowiedzi, przekraczając przepaść między generowaniem języka a wnioskowaniem wizualnym."}, "llama-3.2-vision-instruct": {"description": "Model Llama 3.2-Vision zoptymalizowany jest do rozpoznawania wizualnego, wnioskowania na podstawie obrazów, opisywania obrazów oraz odpowiadania na typowe pytania związane z obrazami."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 to najnowocześniejszy wielojęzyczny, otwarty model j<PERSON><PERSON><PERSON><PERSON> z serii Llama, k<PERSON><PERSON><PERSON> <PERSON>er<PERSON><PERSON> wydajno<PERSON>ć porównywalną z modelem 405B przy bardzo niskich kosztach. Opiera się na strukturze Transformer i poprawia użyteczność oraz bezpieczeństwo dzięki nadzorowanemu dostrajaniu (SFT) i uczeniu ze wzmocnieniem na podstawie ludzkich opinii (RLHF). Jego wersja dostosowana do instrukcji jest zoptymalizowana do wielojęzycznych rozmów i w wielu branżowych benchmarkach przewyższa wiele otwartych i zamkniętych modeli czatu. Data graniczna wiedzy to grudzień 2023."}, "llama-3.3-70b-versatile": {"description": "Meta Llama 3.3 to wielojęzyczny model j<PERSON><PERSON><PERSON><PERSON> (LLM) 70B, pretrenowany i dostosowany do poleceń. Model Llama 3.3, dostosowany do poleceń, jest zoptymalizowany do zastosowań w dialogach wielojęzycznych i przewyższa wiele dostępnych modeli czatu, zarówno open source, jak i zamkniętych, w popularnych branżowych benchmarkach."}, "llama-3.3-instruct": {"description": "Model Llama 3.3 zoptymalizowany do rozmów, który w standardowych testach branżowych przewyższa wiele istniejących modeli czatowych o otwartym kodzie."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B oferuje niezrównane możliwości przetwarzania złożoności, dostosowane do projektów o wysokich wymaganiach."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B zapewnia wysoką jakość wydajności wnioskowania, odpowiednią do różnych zastosowań."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use oferuje potężne możliwości wywoływania narzędzi, wspierając efektywne przetwarzanie złożonych zadań."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use to model zoptymalizowany do efektywnego korzystania z narzędzi, wspierający szybkie obliczenia równoległe."}, "llama3.1": {"description": "Llama 3.1 to wiodący model wydany przez Meta, obsługujący do 405B parametrów, mogący być stosowany w złożonych dialogach, tłumaczeniach wielojęzycznych i analizie danych."}, "llama3.1:405b": {"description": "Llama 3.1 to wiodący model wydany przez Meta, obsługujący do 405B parametrów, mogący być stosowany w złożonych dialogach, tłumaczeniach wielojęzycznych i analizie danych."}, "llama3.1:70b": {"description": "Llama 3.1 to wiodący model wydany przez Meta, obsługujący do 405B parametrów, mogący być stosowany w złożonych dialogach, tłumaczeniach wielojęzycznych i analizie danych."}, "llava": {"description": "LLaVA to multimodalny model łączący kodery wizualne i Vicunę, przeznaczony do silnego rozumienia wizualnego i językowego."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B oferuje zintegrowane możliwości przetwarzania wizualnego, generując złożone wyjścia na podstawie informacji wizualnych."}, "llava:13b": {"description": "LLaVA to multimodalny model łączący kodery wizualne i Vicunę, przeznaczony do silnego rozumienia wizualnego i językowego."}, "llava:34b": {"description": "LLaVA to multimodalny model łączący kodery wizualne i Vicunę, przeznaczony do silnego rozumienia wizualnego i językowego."}, "mathstral": {"description": "MathΣtral zaprojektowany do badań naukowych i wnioskowania matematycznego, oferujący efektywne możliwości obliczeniowe i interpretację wyników."}, "max-32k": {"description": "Spark Max 32K jest wyposażony w dużą zdolność przetwarzania kontekstu, oferując silniejsze zrozumienie kontekstu i zdolności logicznego wnioskowania, obsługując teksty o długości do 32K tokenów, co czyni go odpowiednim do czytania długich dokumentów, prywatnych pytań i odpowiedzi oraz innych scenariuszy."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct to model j<PERSON><PERSON><PERSON><PERSON> duży skali, w pełni samodzielnie wytrenowany przez Qwen. Megrez-3B-Instruct ma na celu stworzenie szybkiego, kompaktowego i łatwego w użyciu rozwiązania inteligentnego na urządzeniach klienckich, opartego na koncepcji integracji oprogramowania i sprzętu."}, "meta-llama-3-70b-instruct": {"description": "Potężny model z 70 miliardami parametrów, doskonały w rozumowaniu, kodowaniu i szerokich zastosowaniach językowych."}, "meta-llama-3-8b-instruct": {"description": "Wszechstronny model z 8 miliardami parametrów, zoptymalizowany do zadań dialogowych i generacji tekstu."}, "meta-llama-3.1-405b-instruct": {"description": "Modele tekstowe Llama 3.1 dostosowane do instrukcji, zoptymalizowane do wielojęzycznych przypadków użycia dialogowego, przewyższają wiele dostępnych modeli open source i zamkniętych w powszechnych benchmarkach branżowych."}, "meta-llama-3.1-70b-instruct": {"description": "Modele tekstowe Llama 3.1 dostosowane do instrukcji, zoptymalizowane do wielojęzycznych przypadków użycia dialogowego, przewyższają wiele dostępnych modeli open source i zamkniętych w powszechnych benchmarkach branżowych."}, "meta-llama-3.1-8b-instruct": {"description": "Modele tekstowe Llama 3.1 dostosowane do instrukcji, zoptymalizowane do wielojęzycznych przypadków użycia dialogowego, przewyższają wiele dostępnych modeli open source i zamkniętych w powszechnych benchmarkach branżowych."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) oferuje doskonałe możliwości przetwarzania języka i znakomite doświadczenie interakcji."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 oferuje doskonałe zdolności przetwarzania języka i znakomite doświadczenie interakcyjne."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) to <PERSON><PERSON>ż<PERSON> model c<PERSON><PERSON>, wspierający złożone potrzeby dialogowe."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) oferuje wsparcie dla wielu języków, obejmując bogatą wiedzę z różnych dziedzin."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 zaprojektowana do przetwarzania zadań łączących dane wizualne i tekstowe. Doskonała w zadaniach takich jak opisywanie obrazów i wizualne pytania odpowiedzi, przekracza granice między generowaniem języka a wnioskowaniem wizualnym."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 zaprojektowana do przetwarzania zadań łączących dane wizualne i tekstowe. Doskonała w zadaniach takich jak opisywanie obrazów i wizualne pytania odpowiedzi, przekracza granice między generowaniem języka a wnioskowaniem wizualnym."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 zaprojektowana do przetwarzania zadań łączących dane wizualne i tekstowe. Doskonała w zadaniach takich jak opisywanie obrazów i wizualne pytania odpowiedzi, przekracza granice między generowaniem języka a wnioskowaniem wizualnym."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Meta Llama 3.3 to wielojęzyczny model j<PERSON><PERSON><PERSON>wy (LLM) o skali 70B (wejście/wyjście tekstowe), będący modelem generacyjnym wstępnie wytrenowanym i dostosowanym do instrukcji. Model Llama 3.3 dostosowany do instrukcji jest zoptymalizowany pod kątem zastosowań w dialogach wielojęzycznych i przewyższa wiele dostępnych modeli open-source i zamkniętych w popularnych testach branżowych."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 zaprojektowana do przetwarzania zadań łączących dane wizualne i tekstowe. Doskonała w zadaniach takich jak opisywanie obrazów i wizualne pytania odpowiedzi, przekracza granice między generowaniem języka a wnioskowaniem wizualnym."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite jest idealny do środowisk wymagających wysokiej wydajności i niskiego opóźnienia."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo oferuje doskonałe możliwości rozumienia i generowania języka, idealny do najbardziej wymagających zadań obliczeniowych."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite jest dostosowany do środowisk z ograniczonymi zasobami, oferując doskonałą równowagę wydajności."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo to wydajny model j<PERSON><PERSON><PERSON><PERSON>, wspierający szeroki zakres zastosowań."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B to potężny model do wstępnego uczenia się i dostosowywania instrukcji."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "Model Llama 3.1 Turbo 405B oferuje ogromną pojem<PERSON><PERSON> kontekstową dla przetwarzania dużych danych, wyróżniając się w zastosowaniach sztucznej inteligencji o dużej skali."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 to wiodący model wydany przez Meta, wspierający do 405B parametrów, mogący być stosowany w złożonych rozmowach, tłumaczeniach wielojęzycznych i analizie danych."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Model Llama 3.1 70B został starannie dostosowany do aplikacji o dużym obciążeniu, kwantyzowany do FP8, co zapewnia wyższą wydajność obliczeniową i dokładność, gwarantując doskonałe osiągi w złożonych scenariuszach."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Model Llama 3.1 8B wykorzystuje kwantyzację FP8, obsługując do 131,072 kontekstowych tokenów, wyróżniając się wśród modeli open source, idealny do złożonych zadań, przewyższający wiele branżowych standardów."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct zoptymalizowano do wysokiej jakości dialogów, osiągając znakomite wyniki w różnych ocenach ludzkich."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct zoptymalizowano do wysokiej jakości scenarius<PERSON>, <PERSON>si<PERSON><PERSON>j<PERSON><PERSON> lepsze wyniki niż wiele modeli zamkniętych."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct zaprojektowano z myślą o wysokiej jakości dialogach, osiągając znakomite wyniki w ocenach ludzkich, szczególnie w scenariuszach o wysokiej interakcji."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct to najnowsza wersja wydana przez Meta, zoptymalizowana do wysokiej jakości scenarius<PERSON>, przewyższająca wiele wiodących modeli zamkniętych."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 oferuje wsparcie dla wielu języków i jest jednym z wiodących modeli generacyjnych w branży."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 jest zaprojektowana do przetwarzania zadań łączących dane wizualne i tekstowe. Wykazuje doskonałe wyniki w zadaniach takich jak opisywanie obrazów i wizualne pytania i odpowiedzi, przekraczając granice między generowaniem języka a wnioskowaniem wizualnym."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 jest zaprojektowana do przetwarzania zadań łączących dane wizualne i tekstowe. Wykazuje doskonałe wyniki w zadaniach takich jak opisywanie obrazów i wizualne pytania i odpowiedzi, przekraczając granice między generowaniem języka a wnioskowaniem wizualnym."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 to najnowocześniejszy wielojęzyczny, otwarty model j<PERSON><PERSON><PERSON><PERSON> z serii Llama, k<PERSON><PERSON><PERSON> <PERSON>er<PERSON><PERSON> wydajno<PERSON>ć porównywalną z modelem 405B przy bardzo niskich kosztach. Opiera się na strukturze Transformer i poprawia użyteczność oraz bezpieczeństwo dzięki nadzorowanemu dostrajaniu (SFT) i uczeniu ze wzmocnieniem na podstawie ludzkich opinii (RLHF). Jego wersja dostosowana do instrukcji jest zoptymalizowana do wielojęzycznych rozmów i w wielu branżowych benchmarkach przewyższa wiele otwartych i zamkniętych modeli czatu. Data graniczna wiedzy to grudzień 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 to najnowocześniejszy wielojęzyczny, otwarty model j<PERSON><PERSON><PERSON><PERSON> z serii Llama, k<PERSON><PERSON><PERSON> <PERSON>er<PERSON><PERSON> wydajno<PERSON>ć porównywalną z modelem 405B przy bardzo niskich kosztach. Opiera się na strukturze Transformer i poprawia użyteczność oraz bezpieczeństwo dzięki nadzorowanemu dostrajaniu (SFT) i uczeniu ze wzmocnieniem na podstawie ludzkich opinii (RLHF). Jego wersja dostosowana do instrukcji jest zoptymalizowana do wielojęzycznych rozmów i w wielu branżowych benchmarkach przewyższa wiele otwartych i zamkniętych modeli czatu. Data graniczna wiedzy to grudzień 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct to największy i najpotężniejszy model w rodzinie modeli Llama 3.1 Instruct. Jest to wysoko <PERSON>aawan<PERSON>wany model do dialogów, wnioskowania i generowania danych, który może być również używany jako podstawa do specjalistycznego, ciągłego wstępnego szkolenia lub dostosowywania w określonych dziedzinach. Llama 3.1 oferuje wielojęzyczne duże modele językowe (LLM), które są zestawem wstępnie wytrenowanych, dostosowanych do instrukcji modeli generacyjnych, obejmujących rozmiary 8B, 70B i 405B (wejście/wyjście tekstowe). Modele tekstowe Llama 3.1 dostosowane do instrukcji (8B, 70B, 405B) zostały zoptymalizowane do zastosowań w wielojęzycznych dialogach i przewyższają wiele dostępnych modeli czatu open source w powszechnych testach branżowych. Llama 3.1 jest zaprojektowana do użytku komercyjnego i badawczego w wielu językach. Modele tekstowe dostosowane do instrukcji nadają się do czatu w stylu asystenta, podczas gdy modele wstępnie wytrenowane mogą być dostosowane do różnych zadań generowania języka naturalnego. Modele Llama 3.1 wspierają również wykorzystanie ich wyjść do poprawy innych modeli, w tym generowania danych syntetycznych i udoskonalania. Llama 3.1 jest modelem językowym autoregresywnym opartym na zoptymalizowanej architekturze transformatora. Dostosowane wersje wykorzystują nadzorowane dostosowywanie (SFT) oraz uczenie się ze wzmocnieniem z ludzkim feedbackiem (RLHF), aby odpowiadać ludzkim preferencjom dotyczącym pomocności i bezpieczeństwa."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Zaktualizowana wersja Meta Llama 3.1 70B Instruct, obejmująca rozszerzone 128K długości kontekstu, wielojęzyczność i poprawione zdolności wnioskowania. Llama 3.1 oferuje wielojęzyczne modele językowe (LLMs) jako zestaw wstępnie wytrenowanych, dostosowanych do instrukcji modeli generacyjnych, w tym rozmiarów 8B, 70B i 405B (wejście/wyjście tekstowe). Modele tekstowe Llama 3.1 dostosowane do instrukcji (8B, 70B, 405B) są zoptymalizowane do zastosowań w dialogach wielojęzycznych i przewyższają wiele dostępnych modeli czatu w powszechnych testach branżowych. Llama 3.1 jest przeznaczona do zastosowań komercyjnych i badawczych w wielu językach. Modele tekstowe dostosowane do instrukcji są odpowiednie do czatu podobnego do asystenta, podczas gdy modele wstępnie wytrenowane mogą być dostosowane do różnych zadań generowania języka naturalnego. Modele Llama 3.1 wspierają również wykorzystanie wyników ich modeli do poprawy innych modeli, w tym generowania danych syntetycznych i rafinacji. Llama 3.1 jest modelem językowym autoregresywnym, wykorzystującym zoptymalizowaną architekturę transformatora. Wersje dostosowane wykorzystują nadzorowane dostrajanie (SFT) i uczenie się ze wzmocnieniem z ludzkim feedbackiem (RLHF), aby dostosować się do ludzkich preferencji dotyczących pomocności i bezpieczeństwa."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Zaktualizowana wersja Meta Llama 3.1 8B Instruct, obejmująca rozszerzone 128K długości kontekstu, wielojęzyczność i poprawione zdolności wnioskowania. Llama 3.1 oferuje wielojęzyczne modele językowe (LLMs) jako zestaw wstępnie wytrenowanych, dostosowanych do instrukcji modeli generacyjnych, w tym rozmiarów 8B, 70B i 405B (wejście/wyjście tekstowe). Modele tekstowe Llama 3.1 dostosowane do instrukcji (8B, 70B, 405B) są zoptymalizowane do zastosowań w dialogach wielojęzycznych i przewyższają wiele dostępnych modeli czatu w powszechnych testach branżowych. Llama 3.1 jest przeznaczona do zastosowań komercyjnych i badawczych w wielu językach. Modele tekstowe dostosowane do instrukcji są odpowiednie do czatu podobnego do asystenta, podczas gdy modele wstępnie wytrenowane mogą być dostosowane do różnych zadań generowania języka naturalnego. Modele Llama 3.1 wspierają również wykorzystanie wyników ich modeli do poprawy innych modeli, w tym generowania danych syntetycznych i rafinacji. Llama 3.1 jest modelem językowym autoregresywnym, wykorzystującym zoptymalizowaną architekturę transformatora. Wersje dostosowane wykorzystują nadzorowane dostrajanie (SFT) i uczenie się ze wzmocnieniem z ludzkim feedbackiem (RLHF), aby dostosować się do ludzkich preferencji dotyczących pomocności i bezpieczeństwa."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 to ot<PERSON><PERSON> duży model <PERSON><PERSON><PERSON><PERSON><PERSON> (LLM) skierowany do deweloperów, badaczy i przedsiębiorstw, maj<PERSON>cy na celu pomoc w budowaniu, eksperymentowaniu i odpowiedzialnym rozwijaniu ich pomysłów na generatywną sztuczną inteligencję. <PERSON><PERSON><PERSON> podstawowego systemu innowacji globalnej społeczności, jest idealny do tworzenia treści, AI do dialogów, rozumienia języka, badań i zastosowań biznesowych."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 to o<PERSON><PERSON><PERSON> duży model <PERSON><PERSON><PERSON><PERSON><PERSON> (LLM) skierowany do deweloperów, badaczy i przedsiębiorstw, mający na celu pomoc w budowaniu, eksperymentowaniu i odpowiedzialnym rozwijaniu ich pomysłów na generatywną sztuczną inteligencję. <PERSON><PERSON><PERSON> podstawowego systemu innowacji globalnej społeczności, jest idealny dla urządzeń o ograniczonej mocy obliczeniowej i zasobach, a także dla szybszego czasu szkolenia."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Wysokiej jakości zdolności wnioskowania obrazowego na obrazach o wysokiej rozdzielczości, idealne do zastosowań związanych z rozumieniem wizualnym."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Zaawansowane zdolności wnioskowania obrazowego przeznaczone do zastosowań agentów rozumienia wizualnego."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 to najnowocześniejszy wielojęzy<PERSON>ny, otwarty model j<PERSON><PERSON><PERSON><PERSON> z serii <PERSON>, oferujący wydajność porównywalną z modelem 405B przy bardzo niskich kosztach. Opiera się na architekturze Transformer i jest ulepszony przez nadzorowane dostrajanie (SFT) oraz uczenie ze wzmocnieniem na podstawie opinii ludzi (RLHF). Wersja dostrojona pod kątem instrukcji jest zoptymalizowana do wielojęzycznych dialogów i przewyższa wiele otwartych i zamkniętych modeli czatu w licznych branżowych benchmarkach. Data odcięcia wiedzy: grudzień 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Potężny model o 70 miliardach parametrów, wyróżniaj<PERSON><PERSON> się wnioskowaniem, kodowaniem i szerokim zastosowaniem językowym."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Wszechstronny model o 8 miliardach parametrów, zoptymalizowany do zadań dialogowych i generowania tekstu."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Model tekstowy Llama 3.1 dostrojony pod kątem instrukcji, zoptymalizowany do wielojęzycznych zastosowań dialogowych, osiągający doskonałe wyniki w wielu dostępnych otwartych i zamkniętych modelach czatu na popularnych branżowych benchmarkach."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Model tekstowy Llama 3.1 dostrojony pod kątem instrukcji, zoptymalizowany do wielojęzycznych zastosowań dialogowych, osiągający doskonałe wyniki w wielu dostępnych otwartych i zamkniętych modelach czatu na popularnych branżowych benchmarkach."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Model tekstowy Llama 3.1 dostrojony pod kątem instrukcji, zoptymalizowany do wielojęzycznych zastosowań dialogowych, osiągający doskonałe wyniki w wielu dostępnych otwartych i zamkniętych modelach czatu na popularnych branżowych benchmarkach."}, "meta/llama-3.1-405b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>y LLM, wspierający generowanie danych syntetycznych, destylację wiedzy i wnioskowanie, odpowiedni do chatbotów, programowania i zadań w określonych dziedzinach."}, "meta/llama-3.1-70b-instruct": {"description": "Umożliwia złożone rozmowy, posiadaj<PERSON>c doskonałe zrozumienie kontekstu, zdolności wnioskowania i generowania tekstu."}, "meta/llama-3.1-8b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> model, posiadaj<PERSON><PERSON> zrozumienie języka, doskonałe zdolności wnioskowania i generowania tekstu."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Nowoczesny model w<PERSON>ual<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, specjali<PERSON><PERSON><PERSON><PERSON> się w wysokiej jakości wnioskowaniu z obrazów."}, "meta/llama-3.2-1b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, now<PERSON>zesny mały model j<PERSON><PERSON><PERSON><PERSON>, posiadaj<PERSON><PERSON> zrozumienie języka, doskonałe zdolności wnioskowania i generowania tekstu."}, "meta/llama-3.2-3b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, now<PERSON>zesny mały model j<PERSON><PERSON><PERSON><PERSON>, posiadaj<PERSON><PERSON> zrozumienie języka, doskonałe zdolności wnioskowania i generowania tekstu."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Nowoczesny model w<PERSON>ual<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, specjali<PERSON><PERSON><PERSON><PERSON> się w wysokiej jakości wnioskowaniu z obrazów."}, "meta/llama-3.3-70b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>y LLM, specjali<PERSON>j<PERSON><PERSON> się w w<PERSON><PERSON>waniu, <PERSON><PERSON><PERSON><PERSON>, zdrowym rozsądku i wywoływaniu funkcji."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "Ten sam model Phi-3-medium, ale z większym rozmiarem kontekstu, odpowiedni do RAG lub nielicznych podpowiedzi."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Model o 14 milia<PERSON><PERSON> parametrów, le<PERSON><PERSON><PERSON> jakości niż Phi-3-mini, skoncentrowany na wysokiej jakości i danych wymagających intensywnego wnioskowania."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Ten sam model Phi-3-mini, ale z większym rozmiarem kontekstu, odpowiedni do RAG lub nielicznych podpowiedzi."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Najmniejszy członek rodziny Phi-3, zoptymalizowany pod kątem jakości i niskich opóźnień."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Ten sam model Phi-3-small, ale z większym rozmiarem kontekstu, odpowiedni do RAG lub nielicznych podpowiedzi."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Model o 7 milia<PERSON>ch parametrów, le<PERSON><PERSON><PERSON> jakości niż Phi-3-mini, skoncentrowany na wysokiej jakości i danych wymagających intensywnego wnioskowania."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Zaktualizowana wersja modelu Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Zaktualizowana wersja modelu Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 to model <PERSON><PERSON><PERSON><PERSON><PERSON> oferowany przez Microsoft AI, który wyróżnia się w złożonych rozmowach, wiel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wnioskowaniu i jako inteligentny asystent."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B to najnowocześniejszy model <PERSON> o<PERSON>, wykazuj<PERSON><PERSON> niezwykle konkurencyjne osiągi."}, "minicpm-v": {"description": "MiniCPM-V to nowa generacja multimodalnego dużego modelu wydanego przez OpenBMB, który posiada doskonałe zdolności rozpoznawania OCR oraz zrozumienia multimodalnego, wspierając szeroki zakres zastosowań."}, "ministral-3b-latest": {"description": "Ministral 3B to czołowy model brzegowy Mistrala."}, "ministral-8b-latest": {"description": "Ministral 8B to opłacalny model brzegowy Mistrala."}, "mistral": {"description": "Mi<PERSON>l to model 7B wydany przez Mistral AI, odpowiedni do zmiennych potrzeb przetwarzania języka."}, "mistral-ai/Mistral-Large-2411": {"description": "Flagowy model <PERSON><PERSON><PERSON>, odpowiedni do zadań wymagających dużej mocy obliczeniowej lub wys<PERSON> w<PERSON>zowanych, takich jak generowanie tekstu syntetycznego, generowanie kodu, RAG lub agentów."}, "mistral-ai/Mistral-Nemo": {"description": "<PERSON><PERSON><PERSON> to nowoczesny model <PERSON><PERSON><PERSON><PERSON><PERSON> (LLM) oferujący najlepsze w swojej klasie zdolności wnioskowania, wiedzy o świecie i kodowania."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small jest przeznaczony do wszelkich zadań językowych wymagających wysokiej wydajności i niskich opóźnień."}, "mistral-large": {"description": "Mixtral Large to flagowy model <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> zdolności generowania kodu, matematyki i wnioskowania, wspierający kontekst o długości 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 to zaawansowany gęsty model języko<PERSON> o dużym <PERSON> (LLM) z 123 miliardami parametrów, posiadający najnowocześniejsze zdolności wnioskowania, wiedzy i kodowania."}, "mistral-large-latest": {"description": "Mistral Large to flagowy model, doskonały w zadaniach wielojęzycznych, złożonym wnioskowaniu i generowaniu kodu, idealny do zaawansowanych zastosowań."}, "mistral-medium-latest": {"description": "Mistral Medium 3 oferuje najnowocześniejszą wydajność przy kosztach 8 razy niższych, a także zasadniczo upraszcza wdrożenia w przedsiębiorstwach."}, "mistral-nemo": {"description": "<PERSON><PERSON><PERSON> Nemo, opracowany przez Mistral AI i NVIDIA, to model 12B o wysokiej wydajności."}, "mistral-nemo-instruct": {"description": "Duży model j<PERSON><PERSON><PERSON>wy (LLM) Mistral-Nemo-Instruct-2407 to wersja dostosowana do poleceń modelu Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small może być używany w każdym zadaniu opartym na języku, które wymaga wysokiej wydajności i niskiej latencji."}, "mistral-small-latest": {"description": "Mistral Small to opcja o wysokiej efektywności kosztowej, szybka i niezawodna, odpowiednia do tłumaczeń, podsumowań i analizy sentymentu."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct jest znany z wysokiej wydajności, idealny do różnorodnych zadań językowych."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B to model dostosowany na żądanie, oferujący zoptymalizowane odpowiedzi na zadania."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 oferuje efektywne możliwości obliczeniowe i rozumienia języka naturalnego, idealne do szerokiego zakresu zastosowań."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B to kompaktowy, ale wysokowydajny model, dobrze radzący sobie z przetwarzaniem wsadowym i prostymi zadaniami, takimi jak klasyfikacja i generowanie tekstu, z dobrą zdolnością wnioskowania."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) to super duży model <PERSON><PERSON><PERSON><PERSON><PERSON>, wspierający ekstremalne wymagania przetwarzania."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B to wstępnie wytrenowany model rzadkiego mieszania ekspertów, przeznaczony do ogólnych zadań tekstowych."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B to model sparsity expert, kt<PERSON>ry korzysta z wielu parametrów, aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prę<PERSON><PERSON><PERSON> wnioskowania, idealny do przetwarzania zadań wielojęzycznych i generowania kodu."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct to model o wys<PERSON><PERSON>, kt<PERSON><PERSON> łączy optymalizację prędkości z obsługą długiego kontekstu."}, "mistralai/mistral-nemo": {"description": "<PERSON><PERSON><PERSON>emo to model z 7,3 mi<PERSON><PERSON><PERSON> parametrów, wspierający wiele języków i wysoką wydajność programowania."}, "mixtral": {"description": "Mixtral to model <PERSON><PERSON><PERSON><PERSON>, z <PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>, ofer<PERSON>j<PERSON>cy wsparcie w generowaniu kodu i rozumieniu języka."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B oferuje wysoką tolerancję na błędy w obliczeniach równoległych, odpowiednią do złożonych zadań."}, "mixtral:8x22b": {"description": "Mixtral to model <PERSON><PERSON><PERSON><PERSON>, z <PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>, ofer<PERSON>j<PERSON>cy wsparcie w generowaniu kodu i rozumieniu języka."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K to model o zdolności przetwarzania kontekstu o ultra-długiej długo<PERSON>ci, odpowiedni do generowania bardzo długich tekstów, spełniający wymagania złożonych zadań generacyjnych, zdolny do przetwarzania treści do 128 000 tokenów, idealny do zastosowań w badaniach, akademickich i generowaniu dużych dokumentów."}, "moonshot-v1-128k-vision-preview": {"description": "Model wizualny <PERSON> (w tym moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview itp.) potra<PERSON> rozumieć treść obrazów, w tym teksty na obrazach, kolory obrazów i kształty obiektów."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K oferuje zdolność przetwarzania kontekstu o średniej długości, zdolną do przetwarzania 32 768 tokenów, szczególnie odpowiednią do generowania różnych długich dokumentów i złożonych dialogów, stosowaną w tworzeniu treści, generowaniu raportów i systemach dialogowych."}, "moonshot-v1-32k-vision-preview": {"description": "Model wizualny <PERSON> (w tym moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview itp.) potra<PERSON> rozumieć treść obrazów, w tym teksty na obrazach, kolory obrazów i kształty obiektów."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K zaprojektowany do generowania krótkich tekstów, charakteryzuje się wydajnością przetwarzania, zdolny do przetwarzania 8 192 tokenów, idealny do krótkich dialogów, notatek i szybkiego generowania treści."}, "moonshot-v1-8k-vision-preview": {"description": "Model wizualny <PERSON> (w tym moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview itp.) potra<PERSON> rozumieć treść obrazów, w tym teksty na obrazach, kolory obrazów i kształty obiektów."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto może wybierać odpowiedni model w zależności od liczby tokenów zajmowanych przez bieżący kontekst."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B to otwarty model kod<PERSON> <PERSON><PERSON><PERSON><PERSON>, zoptymalizowany za pomocą zaawansowanego uczenia ze wzmocnieniem, zdolny do generowania stabilnych, gotowych do produkcji poprawek. Model o<PERSON><PERSON><PERSON><PERSON><PERSON> nowy rekord 60,4% na SWE-bench Verified, ustanawiając nowy standard w zadaniach automatyzacji inżynierii oprogramowania, takich jak naprawa błędów i przegląd kodu."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 to podstawowy model architektury MoE o potężnych zdolnościach kodowania i agenta, z łączną liczbą parametrów 1 biliona i 32 miliardami aktywowanych parametrów. W testach wydajności w zakresie ogólnej wiedzy, programowania, matematyki i zadań agenta model K2 przewyższa inne popularne otwarte modele."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 to podstawowy model architektury MoE o wyjątkowych zdolnościach w zakresie kodowania i agentów, z łączną liczbą parametrów 1T i 32B aktywnych parametrów. W testach wydajnościowych obejmujących ogólną wiedzę, program<PERSON><PERSON>, matematykę i agentów, model K2 przewyższa inne popularne modele open source."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B to ulepszona wersja Nous Hermes 2, zawieraj<PERSON><PERSON> najnowsze wewnętrznie opracowane zbiory danych."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B to dostosowany przez NVIDIA duży model j<PERSON>zy<PERSON>wy, mający na celu zwiększenie użyteczności odpowiedzi generowanych przez LLM w odpowiedzi na zapytania użytkowników. Model ten osi<PERSON>gn<PERSON>ł doskonałe wyniki w testach benchmarkowych, takich jak Arena Hard, AlpacaEval 2 LC i GPT-4-Turbo MT-Bench, zaj<PERSON>j<PERSON><PERSON> pierwsze miejsce we wszystkich trzech automatycznych testach do 1 października 2024 roku. Model został przeszkolony przy użyciu RLHF (szczególnie REINFORCE), Llama-3.1-Nemotron-70B-Reward i HelpSteer2-Preference na bazie modelu Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Unikalny model <PERSON><PERSON><PERSON><PERSON><PERSON>, oferuj<PERSON>cy niezrównaną dokładność i wydajność."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct to dostosowany przez NVIDIA duży model j<PERSON>zy<PERSON>wy, zaprojektowany w celu zwiększenia użyteczności odpowiedzi generowanych przez LLM."}, "o1": {"description": "Skupia się na zaawansowanym wnioskowaniu i rozwiązywaniu złożonych problemów, w tym zadań matematycznych i naukowych. Doskonale nadaje się do aplikacji wymagających głębokiego zrozumienia kontekstu i zarządzania procesami."}, "o1-mini": {"description": "o1-mini to s<PERSON>b<PERSON> i ekonomiczny model wnioskowania zaprojektowany z myślą o programowaniu, matematyce i zastosowaniach naukowych. Model ten ma kontekst 128K i datę graniczną wiedzy z października 2023 roku."}, "o1-preview": {"description": "o1 to nowy model wnioskowania OpenAI, odpowiedni do złożonych zadań wymagających szerokiej wiedzy ogólnej. Model ten ma kontekst 128K i datę graniczną wiedzy z października 2023 roku."}, "o1-pro": {"description": "Modele z serii o1 są trenowane z wykorzystaniem uczenia ze wzmocnieniem, potra<PERSON><PERSON> myśleć przed udzieleniem odpowiedzi i wykonywać złożone zadania rozumowania. Model o1-pro wykorzystuje więcej zasobów obliczeniowych, aby prowadzić głębsze rozważania i stale dostarczać lepsze odpowiedzi."}, "o3": {"description": "o3 to wszechstronny i potężny model, kt<PERSON>ry doskonale sprawdza się w wielu dziedzinach. Ustanawia nowe standardy w zadaniach matematycznych, naukowych, programistycznych i wizualnych. Jest również biegły w pisaniu technicznym i przestrzeganiu instrukcji. Użytkownicy mogą go wykorzystać do analizy tekstów, kodów i obrazów, rozwiązując złożone problemy wieloetapowe."}, "o3-deep-research": {"description": "o3-deep-research to nasz na<PERSON><PERSON><PERSON><PERSON>wany model g<PERSON><PERSON><PERSON><PERSON><PERSON> bad<PERSON>, zaprojektowany specjalnie do obsługi złożonych, wieloetapowych zadań badawczych. Potrafi wyszukiwać i integrować informacje z internetu, a także uzyskiwać dostęp do Twoich własnych danych i wykorzystywać je za pośrednictwem łącznika MCP."}, "o3-mini": {"description": "o3-mini to nasz <PERSON> mały model wnioskowania, kt<PERSON><PERSON> ofer<PERSON>je wysoką inteligencję przy tych samych kosztach i celach opóźnienia co o1-mini."}, "o3-pro": {"description": "Model o3-pro wykorzystuje większą moc obliczeniową do głębszego myślenia i zawsze dostarcza lepsze odpowiedzi, dostępny wyłącznie przez API Responses."}, "o4-mini": {"description": "o4-mini to na<PERSON> na<PERSON> mały model z serii o. Został zoptymalizowany do szybkiego i efektywnego wnioskowania, osiągając wysoką wydajność i efektywność w zadaniach kodowania i wizualnych."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research to nasz szybszy i bardziej przystępny cenowo model gł<PERSON><PERSON><PERSON><PERSON> badań — idealny do obsługi złożonych, wieloetapowych zadań badawczych. Potrafi wyszukiwać i integrować informacje z internetu, a także uzyskiwać dostęp do Twoich własnych danych i wykorzystywać je za pośrednictwem łącznika MCP."}, "open-codestral-mamba": {"description": "Codestral Mamba to model językowy Mamba 2 skoncentrowany na generowaniu kodu, oferujący silne wsparcie dla zaawansowanych zadań kodowania i wnioskowania."}, "open-mistral-7b": {"description": "Mistral 7B to kompaktowy, ale wydajny model, doskonały do przetwarzania wsadowego i prostych zadań, takich jak klasyfikacja i generowanie tekstu, z dobrą wydajnością wnioskowania."}, "open-mistral-nemo": {"description": "Mistral Nemo to model 12B opracowany we współpracy z Nvidia, oferujący doskonałe możliwości wnioskowania i kodowania, łatwy do integracji i zastąpienia."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B to w<PERSON><PERSON><PERSON><PERSON> model e<PERSON><PERSON><PERSON>, skoncentrowany na złożonych zadaniach, oferujący doskonałe możliwości wnioskowania i wyższą przepustowość."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B to model r<PERSON><PERSON><PERSON>, który wykorzystuje wiele parametrów do zwiększenia prędkości wnioskowania, odpowiedni do przetwarzania zadań wielojęzycznych i generowania kodu."}, "openai/gpt-4.1": {"description": "GPT-4.1 to nasz flagowy model do złożonych zadań. Idealnie nadaje się do rozwiązywania problemów w różnych dziedzinach."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini oferuje równowagę między inteligencją, s<PERSON><PERSON><PERSON>ścią a kosztami, co czyni go atrakcyjnym modelem w wielu zastosowaniach."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano to najszybszy i najbardziej opłacalny model GPT-4.1."}, "openai/gpt-4o": {"description": "ChatGPT-4o to <PERSON>zny model, który jest na bieżąco aktualizowany, aby utr<PERSON><PERSON><PERSON> najnowszą wersję. Łączy potężne zdolności rozumienia i generowania języka, idealny do zastosowań na dużą skalę, w tym obsługi klienta, edukacji i wsparcia technicznego."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini to na<PERSON><PERSON><PERSON> model OpenAI, wydany po GPT-4 Omni, obsługujący wejścia tekstowe i wizualne. Jako ich najnowocześniejszy mały model, jest znacznie tańszy od innych niedawnych modeli czołowych i kosztuje o ponad 60% mniej niż GPT-3.5 Turbo. Utrzymuje najnowocześniejszą inteligencję, oferując jednocześnie znaczną wartość za pieniądze. GPT-4o mini uzyskał wynik 82% w teście MMLU i obecnie zajmuje wyższą pozycję w preferencjach czatu niż GPT-4."}, "openai/o1": {"description": "o1 to nowy model wnioskowania OpenAI, obsługuj<PERSON>cy wejścia tekstowo-obrazowe i generujący tekst, odpowiedni do złożonych zadań wymagających szerokiej wiedzy ogólnej. Model posiada kontekst o długości 200K oraz datę odcięcia wiedzy na październik 2023."}, "openai/o1-mini": {"description": "o1-mini to s<PERSON>b<PERSON> i ekonomiczny model wnioskowania zaprojektowany z myślą o programowaniu, matematyce i zastosowaniach naukowych. Model ten ma kontekst 128K i datę graniczną wiedzy z października 2023 roku."}, "openai/o1-preview": {"description": "o1 to nowy model wnioskowania OpenAI, odpowiedni do złożonych zadań wymagających szerokiej wiedzy ogólnej. Model ten ma kontekst 128K i datę graniczną wiedzy z października 2023 roku."}, "openai/o3": {"description": "o3 to wszechstronny i potężny model, który osiąga doskonałe wyniki w wielu dziedzinach. Ustanawia nowe standardy w zadaniach związanych z matematyką, nauką, programowaniem i rozumowaniem wizualnym. Doskonale radzi sobie również z pisaniem technicznym i przestrzeganiem instrukcji. Użytkownicy mogą go wykorzystać do analizy tekstów, kodu i obrazów, rozwiązując złożone problemy wieloetapowe."}, "openai/o3-mini": {"description": "o3-mini oferuje wysoką inteligencję przy tych samych kosztach i celach opóźnienia co o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini w wersji o wysokim poziomie rozumowania, oferujący wysoką inteligencję przy tych samych kosztach i celach opóźnienia co o1-mini."}, "openai/o4-mini": {"description": "o4-mini zoptymalizowany do szybkiego i efektywnego wnioskowania, osiągający wysoką wydajność i efektywność w zadaniach kodowania i wizualnych."}, "openai/o4-mini-high": {"description": "o4-mini w wersji o wysokim poziomie wnioskowania, zoptymalizowany do szybkiego i efektywnego wnioskowania, osiągający wysoką wydajność i efektywność w zadaniach kodowania i wizualnych."}, "openrouter/auto": {"description": "W zależności od długoś<PERSON> kontekstu, tematu i zł<PERSON>ci, Twoje zapytanie zostanie wysłane do Llama 3 70B Instruct, Claude 3.5 Sonnet (samoregulacja) lub GPT-4o."}, "phi3": {"description": "Phi-3 to lekki model otwarty wydany przez Microsoft, odpowiedni do efektywnej integracji i dużej skali wnioskowania wiedzy."}, "phi3:14b": {"description": "Phi-3 to lekki model otwarty wydany przez Microsoft, odpowiedni do efektywnej integracji i dużej skali wnioskowania wiedzy."}, "pixtral-12b-2409": {"description": "Model Pixtral wykazuje silne zdolności w zadaniach związanych z analizą wykresów i zrozumieniem obrazów, pytaniami dokumentowymi, wielomodalnym rozumowaniem i przestrzeganiem instrukcji, zdolny do przyjmowania obrazów w naturalnej rozdzielczości i proporcjach, a także do przetwarzania dowolnej liczby obrazów w długim oknie kontekstowym o długości do 128K tokenów."}, "pixtral-large-latest": {"description": "Pixtral Large to otwarty model wielomodalny z 124 miliardami parametrów, zbudowany na bazie Mistral Large 2. To nasz drugi model w rodzinie wielomodalnej, kt<PERSON><PERSON> wykazuje zaawansowane zdolności rozumienia obrazów."}, "pro-128k": {"description": "Spark Pro 128K jest wyposażony w wyjątkową zdolność przetwarzania kontekstu, mogąc obsługiwać do 128K informacji kontekstowych, co czyni go idealnym do analizy całościowej i długoterminowego przetwarzania logicznych powiązań w długich treściach, zapewniając płynność i spójność logiczną oraz różnorodne wsparcie cytatów w złożonej komunikacji tekstowej."}, "qvq-72b-preview": {"description": "Model QVQ jest eksperymentalnym modelem badawczym opracowanym przez zesp<PERSON>ł <PERSON>wen, skoncentrowanym na zwiększeniu zdolności w zakresie rozumowania wizualnego, szczególnie w dziedzinie rozumowania matematycznego."}, "qvq-max": {"description": "Model wizualnego wnioskowania Tongyi Qianwen QV<PERSON>, obsługuj<PERSON><PERSON> wejścia wizualne i generujący łańcuchy myślowe, wykazuj<PERSON>cy silne zdolności w matematyce, programowaniu, analizie wizualnej, tw<PERSON><PERSON><PERSON><PERSON><PERSON> oraz zadaniach ogólnych."}, "qvq-plus": {"description": "Model wnioskowania wizualnego. Obsługuje wejścia wizualne oraz generowanie łańcuchów myślowych. Wersja plus po modelu qvq-max, charakteryzuje się szybszym wnioskowaniem oraz lepszą równowagą między efektywnością a kosztami w porównaniu do qvq-max."}, "qwen-coder-plus": {"description": "Model kod<PERSON><PERSON>."}, "qwen-coder-turbo": {"description": "Model kod<PERSON><PERSON>."}, "qwen-coder-turbo-latest": {"description": "Model k<PERSON><PERSON><PERSON>."}, "qwen-long": {"description": "<PERSON><PERSON> to ultra-duży model <PERSON><PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> obsługuje długie konteksty tekstowe oraz funkcje dialogowe oparte na długich dokumentach i wielu dokumentach."}, "qwen-math-plus": {"description": "Model matematyczny Tongyi <PERSON>wen, specjalnie zaprojektowany do rozwiązywania zadań matematycznych."}, "qwen-math-plus-latest": {"description": "Model mate<PERSON><PERSON><PERSON><PERSON>, stworzony specjalnie do rozwiązywania problemów matematycznych."}, "qwen-math-turbo": {"description": "Model matematyczny Tongyi <PERSON>wen, specjalnie zaprojektowany do rozwiązywania zadań matematycznych."}, "qwen-math-turbo-latest": {"description": "Model mate<PERSON><PERSON><PERSON><PERSON>, stworzony specjalnie do rozwiązywania problemów matematycznych."}, "qwen-max": {"description": "<PERSON><PERSON> to model j<PERSON><PERSON><PERSON><PERSON> o skali miliardowej, o<PERSON>ł<PERSON><PERSON><PERSON><PERSON><PERSON>, angielski i inne języki. Aktualna wersja API modelu na bazie Qwen 2.5."}, "qwen-omni-turbo": {"description": "Modele z serii Qwen-Omni obsługują dane wejściowe w różnych modalnościach, w tym wideo, audio, obrazy i tekst, oraz generują wyjścia w postaci audio i tekstu."}, "qwen-plus": {"description": "Qwen Plus to ulepszona wersja ogromnego modelu językowego, wspierająca różne języki, w tym chiński i angielski."}, "qwen-turbo": {"description": "<PERSON><PERSON> to ogromny model <PERSON><PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ry obsługuje różne języki, w tym chiński i angielski."}, "qwen-vl-chat-v1": {"description": "Qwen VL obsługuje elastyczne interakcje, w tym wiele obrazów, wielokrotne pytania i odpowiedzi oraz zdolności twórcze."}, "qwen-vl-max": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bar<PERSON><PERSON> model wizualno-językowy Tongyi Qianwen. W porównaniu z wersją wzmocnioną, ponownie poprawia zdolności wnioskowania wizualnego i przestrzegania instrukcji, oferując wyższy poziom percepcji i poznania wizualnego."}, "qwen-vl-max-latest": {"description": "Model wizualno-j<PERSON><PERSON><PERSON><PERSON> Qwen o ultra dużej skali. W porównaniu do wersji rozszerzonej, ponownie zwiększa zdolności wnioskowania wizualnego i przestrzegania instrukcji, oferując wyższy poziom percepcji wizualnej i poznawczej."}, "qwen-vl-ocr": {"description": "Tongyi Qianwen OCR to specjalistyczny model do ekstrakcji tekstu, skoncentrowany na rozpoznawaniu tekstu w dokumentach, tabel<PERSON>, zadaniach testowych i pismach odręcznych. Potrafi rozpoznawać wiele języków, w <PERSON>ym <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, w<PERSON><PERSON><PERSON>, wietnamski i arabski."}, "qwen-vl-plus": {"description": "Wzmocniona wersja dużego modelu wizualno-językowego Tongyi Qianwen. Znacząco poprawia zdolność rozpoznawania detali i tekstu, obsługuje obrazy o rozdzielczości przekraczającej milion pikseli oraz dowolnych proporcjach."}, "qwen-vl-plus-latest": {"description": "Wersja rozszerzona modelu wizualno-językowego Qwen. Znacząco poprawia zdolność rozpoznawania szczegółów i tekstu, obsługuje obrazy o rozdzielczości przekraczającej milion pikseli oraz dowolnych proporcjach."}, "qwen-vl-v1": {"description": "Model wst<PERSON><PERSON><PERSON> wytrenowany, zain<PERSON><PERSON><PERSON><PERSON> przez model j<PERSON><PERSON><PERSON><PERSON> Qwen-7B, <PERSON><PERSON><PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON><PERSON>, z rozdzielczością wejściową obrazu wynoszącą 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 to nowa seria dużych modeli językowych Qwen. Qwen2 7B to model oparty na transformatorze, który wykazuje doskonałe wyniki w zakresie rozumienia języka, zdolności wielojęzycznych, programowania, matematyki i wnioskowania."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 to nowa seria dużych modeli językowych, charakteryzująca się silniejszymi zdolnościami rozumienia i generowania."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL to najnowsza iteracja modelu Qwen-VL, która osiągnęła najnowocześniejsze wyniki w testach benchmarkowych dotyczących rozumienia wizualnego, w tym MathVista, DocVQA, RealWorldQA i MTVQA. Qwen2-VL potrafi rozumieć filmy trwające ponad 20 minut, umożliwiając wysokiej jakości pytania i odpowiedzi, dialogi oraz tworzenie treści oparte na wideo. Posiada również zdolności do złożonego wnioskowania i podejmowania decyzji, co pozwala na integrację z urządzeniami mobilnymi, robotami itp., aby automatycznie działać na podstawie środowiska wizualnego i instrukcji tekstowych. Oprócz angielskiego i chińskiego, Qwen2-VL teraz wspiera również rozumienie tekstu w różnych językach w obra<PERSON>h, w tym większości języków europejskich, j<PERSON><PERSON><PERSON><PERSON>, koreański<PERSON>, arabskiego i wietnamskiego."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct to jeden z najnowszych modeli dużych języków wydanych przez Alibaba Cloud. Model 72B wykazuje znaczną poprawę w obszarach kodowania i matematyki. Model ten oferuje wsparcie dla wielu języków, obejmując ponad 29 języków, w tym chiński i angielski. Model znacząco poprawił zdolność do podążania za instrukcjami, rozumienia danych strukturalnych oraz generowania strukturalnych wyników (szczególnie JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct to jeden z najnowszych modeli dużych języków wydanych przez Alibaba Cloud. Model 32B wykazuje znaczną poprawę w obszarach kodowania i matematyki. Model ten oferuje wsparcie dla wielu języków, obejmując ponad 29 języków, w tym chiński i angielski. Model znacząco poprawił zdolność do podążania za instrukcjami, rozumienia danych strukturalnych oraz generowania strukturalnych wyników (szczególnie JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM skierowany na język chiński i angielski, skoncentrowany na języku, programowaniu, matemat<PERSON>ce, wnioskowaniu i innych dziedzinach."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>y LLM, wspierający generowanie kodu, wnioskowanie i naprawę, obejmujący główne języki programowania."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Potężny średniej wielkości model kodu, wspierający długość kontekstu 32K, specjalizujący się w programowaniu wielojęzycznym."}, "qwen/qwen3-14b": {"description": "Qwen3-14B to gęsty model językowy o 14 miliardach parametrów w serii Qwen3, zaprojektowany z myślą o złożonym wnioskowaniu i efektywnych dialogach. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do matematyki, programowania i wnioskowania logicznego a trybem 'nie-myślenia' stosowanym w ogólnych rozmowach. Model został dostosowany do przestrzegania instrukcji, użycia narzędzi agenta, twórczego pisania oraz wielojęzycznych zadań w ponad 100 językach i dialektach. Obsługuje natywnie 32K tokenów kontekstu i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B to gęsty model językowy o 14 miliardach parametrów w serii Qwen3, zaprojektowany z myślą o złożonym wnioskowaniu i efektywnych dialogach. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do matematyki, programowania i wnioskowania logicznego a trybem 'nie-myślenia' stosowanym w ogólnych rozmowach. Model został dostosowany do przestrzegania instrukcji, użycia narzędzi agenta, twórczego pisania oraz wielojęzycznych zadań w ponad 100 językach i dialektach. Obsługuje natywnie 32K tokenów kontekstu i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B to model mies<PERSON><PERSON> <PERSON> (MoE) o 235 miliardach parametrów opracowany przez Qwen, aktywujący 22 miliardy parametrów przy każdym przejściu do przodu. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do złożonego wnioskowania, matematyki i zadań kodowania a trybem 'nie-myślenia' stosowanym w ogólnych rozmowach. Model wykazuje silne zdolności w zakresie wnioskowania, wsparcia wielojęzycznego (ponad 100 języków i dialektów), zaawansowanego przestrzegania instrukcji oraz wywoływania narzędzi agenta. Obsługuje natywnie okno kontekstu 32K tokenów i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B to model mies<PERSON><PERSON> <PERSON> (MoE) o 235 miliardach parametrów opracowany przez Qwen, aktywujący 22 miliardy parametrów przy każdym przejściu do przodu. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do złożonego wnioskowania, matematyki i zadań kodowania a trybem 'nie-myślenia' stosowanym w ogólnych rozmowach. Model wykazuje silne zdolności w zakresie wnioskowania, wsparcia wielojęzycznego (ponad 100 języków i dialektów), zaawansowanego przestrzegania instrukcji oraz wywoływania narzędzi agenta. Obsługuje natywnie okno kontekstu 32K tokenów i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 to najnowsza generacja serii dużych modeli językowych Qwen, charakteryzująca się architekturą gęstą i mieszanką ekspertów (MoE), która doskonale radzi sobie w zakresie wnioskowania, wsparcia wielojęzycznego i zaawansowanych zadań agenta. Jego unikalna zdolność do płynnego przełączania się między trybem myślenia w złożonym wnioskowaniu a trybem nie-myślenia w efektywnych dialogach zapewnia wszechstronność i wysoką jakość wydajności.\n\nQwen3 znacząco przewyższa wcześniejsze modele, takie jak QwQ i Qwen2.5, oferując doskonałe umiejętności w zakresie matematyki, kodowania, wnioskowania ogólnego, twórczego pisania i interaktywnych dialogów. Wariant Qwen3-30B-A3B zawiera 30,5 miliarda parametrów (3,3 miliarda aktywowanych parametrów), 48 warstw, 128 ekspertów (aktywowano 8 dla każdego zadania) i obsługuje kontekst do 131K tokenów (z użyciem YaRN), ustanawiając nowy standard dla modeli open source."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 to najnowsza generacja serii dużych modeli językowych Qwen, charakteryzująca się architekturą gęstą i mieszanką ekspertów (MoE), która doskonale radzi sobie w zakresie wnioskowania, wsparcia wielojęzycznego i zaawansowanych zadań agenta. Jego unikalna zdolność do płynnego przełączania się między trybem myślenia w złożonym wnioskowaniu a trybem nie-myślenia w efektywnych dialogach zapewnia wszechstronność i wysoką jakość wydajności.\n\nQwen3 znacząco przewyższa wcześniejsze modele, takie jak QwQ i Qwen2.5, oferując doskonałe umiejętności w zakresie matematyki, kodowania, wnioskowania ogólnego, twórczego pisania i interaktywnych dialogów. Wariant Qwen3-30B-A3B zawiera 30,5 miliarda parametrów (3,3 miliarda aktywowanych parametrów), 48 warstw, 128 ekspertów (aktywowano 8 dla każdego zadania) i obsługuje kontekst do 131K tokenów (z użyciem YaRN), ustanawiając nowy standard dla modeli open source."}, "qwen/qwen3-32b": {"description": "Qwen3-32B to gęsty model językowy o 32 miliardach parametrów w serii Qwen3, zoptymalizowany pod kątem złożonego wnioskowania i efektywnych dialogów. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do matematyki, kodowania i wnioskowania logicznego a trybem 'nie-myślenia' stosowanym w szy<PERSON>zych, ogólnych rozmowach. Model wykazuje silną wydajność w przestrzeganiu instrukcji, użyciu narzędzi agenta, twórczym pisaniu oraz wielojęzycznych zadań w ponad 100 językach i dialektach. Obsługuje natywnie 32K tokenów kontekstu i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B to gęsty model językowy o 32 miliardach parametrów w serii Qwen3, zoptymalizowany pod kątem złożonego wnioskowania i efektywnych dialogów. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do matematyki, kodowania i wnioskowania logicznego a trybem 'nie-myślenia' stosowanym w szy<PERSON>zych, ogólnych rozmowach. Model wykazuje silną wydajność w przestrzeganiu instrukcji, użyciu narzędzi agenta, twórczym pisaniu oraz wielojęzycznych zadań w ponad 100 językach i dialektach. Obsługuje natywnie 32K tokenów kontekstu i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B to gęsty model językowy o 8 miliardach parametrów w serii Qwen3, zaprojektowany z myślą o zadaniach wymagających intensywnego wnioskowania i efektywnych dialogach. Obsługuje płynne przełączanie między trybem 'myślenia' używanym do matematyki, kodowania i wnioskowania logicznego a trybem 'nie-myślenia' stosowanym w ogólnych rozmowach. Model został dostosowany do przestrzegania instrukcji, integracji agenta, twórczego pisania oraz wielojęzycznego użycia w ponad 100 językach i dialektach. Obsługuje natywnie okno kontekstu 32K tokenów i może być rozszerzany do 131K tokenów za pomocą YaRN."}, "qwen2": {"description": "Qwen2 to nowa generacja dużego modelu językowego <PERSON>, wspierająca różnorodne potrzeby aplikacyjne dzięki doskonałej wyd<PERSON>ci."}, "qwen2-72b-instruct": {"description": "Qwen2 to nowa generacja modeli językowych stworzona przez zespół Qwen. Opiera się na architekturze Transformer i wykorzystuje funkcję aktywacji SwiGLU, obciążenie QKV uwagi (attention QKV bias), grupowe zapytanie uwagi (group query attention), mieszankę uwagi z oknem przesuwnym (mixture of sliding window attention) i pełną uwagą. Ponadto, zespół Qwen wprowadził ulepszony tokenizator dostosowany do wielu języków naturalnych i kodu."}, "qwen2-7b-instruct": {"description": "Qwen2 to nowa seria modeli językowych stworzona przez zespół Qwen. Opiera się na architekturze Transformer i wykorzystuje funkcję aktywacji SwiGLU, obciążenie QKV uwagi (attention QKV bias), grupowe zapytanie uwagi (group query attention), mieszankę uwagi okna suwającego się (mixture of sliding window attention) i pełnej uwagi. Ponadto, zespół Qwen wprowadził ulepszone tokenizery dostosowane do wielu języków naturalnych i kodu."}, "qwen2.5": {"description": "Qwen2.5 to nowa generacja dużego modelu języ<PERSON>we<PERSON>, który wspiera różnorodne potrzeby aplikacyjne dzięki doskonałej w<PERSON>."}, "qwen2.5-14b-instruct": {"description": "Model Qwen 2.5 o skali 14B, udostępniony na zasadzie open source."}, "qwen2.5-14b-instruct-1m": {"description": "Model o skali 72B, udostępniony przez Tongyi Qianwen 2.5."}, "qwen2.5-32b-instruct": {"description": "Model Qwen 2.5 o skali 32B, udostępniony na zasadzie open source."}, "qwen2.5-72b-instruct": {"description": "Model Qwen 2.5 o skali 72B, udostępniony na zasadzie open source."}, "qwen2.5-7b-instruct": {"description": "Model Qwen 2.5 o skali 7B, udostępniony na zasadzie open source."}, "qwen2.5-coder-1.5b-instruct": {"description": "Otwarta wersja modelu kodowania Qwen."}, "qwen2.5-coder-14b-instruct": {"description": "Otwarta wersja modelu kodowania Tongyi Qianwen."}, "qwen2.5-coder-32b-instruct": {"description": "Otwarta wersja modelu kodowania Qwen."}, "qwen2.5-coder-7b-instruct": {"description": "Otwarta wersja modelu kodowania Qwen."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder to na<PERSON><PERSON><PERSON> model j<PERSON><PERSON><PERSON><PERSON> o dużym rozmiarze z serii <PERSON>wen, specjalnie przeznaczony do obsługi kodu (wcześniej znany jako CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 to najnowsza seria modeli językowych Qwen. W przypadku Qwen2.5 wydaliśmy wiele podstawowych modeli językowych oraz modeli językowych dostosowanych do instrukcji, z zakresem parametrów od 500 milionów do 7,2 miliarda."}, "qwen2.5-math-1.5b-instruct": {"description": "Model Qwen-Math ma silne umiejętności rozwiązywania problemów matematycznych."}, "qwen2.5-math-72b-instruct": {"description": "Model Qwen-Math, kt<PERSON>ry ma silne zdolności rozwiązywania problemów matematycznych."}, "qwen2.5-math-7b-instruct": {"description": "Model Qwen-Math, kt<PERSON>ry ma silne zdolności rozwiązywania problemów matematycznych."}, "qwen2.5-omni-7b": {"description": "Model seri<PERSON>-Omni obsługuje wprowadzanie danych w różnych modalnościach, w tym wideo, audio, obrazy i tekst, oraz generuje audio i tekst."}, "qwen2.5-vl-32b-instruct": {"description": "Seria modeli Qwen2.5-VL poprawia poziom inteligencji, praktyczności i zastosowania modelu, co pozwala mu lepiej radzić sobie w naturalnej konwersacji, tworzeni<PERSON> treści, usługach wiedzy specjalistycznej i programowaniu. Wersja 32B została zoptymalizowana za pomocą technologii uczenia wzmacniającego, co w porównaniu z innymi modelami serii Qwen2.5 VL, zapewnia bardziej zgodny z preferencjami ludzi styl wyjściowy, zdolność wnioskowania w złożonych problemach matematycznych oraz zdolność szczegółowej interpretacji i wnioskowania na podstawie obrazów."}, "qwen2.5-vl-72b-instruct": {"description": "Zwiększona zdolność do podążania za instrukcjami, matematyki, rozwiązywania problemów i kodowania, poprawiona zdolność do rozpoznawania obiektów, wsparcie dla różnych formatów do precyzyjnego lokalizowania elementów wizualnych, zdolność do rozumienia długich plików wideo (do 10 minut) oraz lokalizowania momentów zdarzeń w czasie rzeczywistym, zdolność do rozumienia kolejności czasowej i szybkości, wsparcie dla operacji na systemach OS lub Mobile, silna zdolność do ekstrakcji kluczowych informacji i generowania wyjścia w formacie JSON. Ta wersja to wersja 72B, najsilniejsza w tej serii."}, "qwen2.5-vl-7b-instruct": {"description": "Zwiększona zdolność do podążania za instrukcjami, matematyki, rozwiązywania problemów i kodowania, poprawiona zdolność do rozpoznawania obiektów, wsparcie dla różnych formatów do precyzyjnego lokalizowania elementów wizualnych, zdolność do rozumienia długich plików wideo (do 10 minut) oraz lokalizowania momentów zdarzeń w czasie rzeczywistym, zdolność do rozumienia kolejności czasowej i szybkości, wsparcie dla operacji na systemach OS lub Mobile, silna zdolność do ekstrakcji kluczowych informacji i generowania wyjścia w formacie JSON. Ta wersja to wersja 72B, najsilniejsza w tej serii."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL to najnowsza wersja modelu wizualno-lingwistycznego rodziny Qwen."}, "qwen2.5:0.5b": {"description": "Qwen2.5 to nowa generacja dużego modelu języ<PERSON>we<PERSON>, który wspiera różnorodne potrzeby aplikacyjne dzięki doskonałej w<PERSON>."}, "qwen2.5:1.5b": {"description": "Qwen2.5 to nowa generacja dużego modelu języ<PERSON>we<PERSON>, który wspiera różnorodne potrzeby aplikacyjne dzięki doskonałej w<PERSON>."}, "qwen2.5:72b": {"description": "Qwen2.5 to nowa generacja dużego modelu języ<PERSON>we<PERSON>, który wspiera różnorodne potrzeby aplikacyjne dzięki doskonałej w<PERSON>."}, "qwen2:0.5b": {"description": "Qwen2 to nowa generacja dużego modelu językowego <PERSON>, wspierająca różnorodne potrzeby aplikacyjne dzięki doskonałej wyd<PERSON>ci."}, "qwen2:1.5b": {"description": "Qwen2 to nowa generacja dużego modelu językowego <PERSON>, wspierająca różnorodne potrzeby aplikacyjne dzięki doskonałej wyd<PERSON>ci."}, "qwen2:72b": {"description": "Qwen2 to nowa generacja dużego modelu językowego <PERSON>, wspierająca różnorodne potrzeby aplikacyjne dzięki doskonałej wyd<PERSON>ci."}, "qwen3": {"description": "Qwen3 to nowa generacja dużego modelu językowego od Ali<PERSON>ba, który wspiera różnorodne potrzeby aplikacyjne dzięki doskonałej wydaj<PERSON>ści."}, "qwen3-0.6b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-1.7b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-14b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-235b-a22b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-235b-a22b-instruct-2507": {"description": "Otwartoźródłowy model trybu nie myślącego oparty na Qwen3, z niewielką poprawą w zakresie kreatywności subiektywnej i bezpieczeństwa modelu w porównaniu do poprzedniej wersji (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Otwartoźródłowy model trybu myślącego oparty na Qwen3, z dużymi ulepszeniami w zakresie zdolności logicznych, <PERSON><PERSON><PERSON><PERSON>, wzbogacenia wiedzy i kreatywności w porównaniu do poprzedniej wersji (Tongyi Qianwen 3-235B-A22B), odpowiedni do zadań wymagających zaawansowanego wnioskowania."}, "qwen3-30b-a3b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-30b-a3b-instruct-2507": {"description": "W porównaniu z poprzednią wersją (Qwen3-30B-A3B) ogólne zdolności w języku chińskim, angielskim i wielojęzyczne zostały znacznie poprawione. Specjalna optymalizacja dla zadań subiektywnych i otwartych sprawia, że model lepiej odpowiada preferencjom użytkowników i potrafi dostarczać bardziej pomocne odpowiedzi."}, "qwen3-30b-a3b-thinking-2507": {"description": "Model open source w trybie myślenia oparty na Qwen3, kt<PERSON>ry w porównaniu z poprzednią wersją (Tongyi Qianwen 3-30B-A3B) wykazuje znaczne ulepszenia w zakresie zdolności logicznych, <PERSON><PERSON><PERSON><PERSON>, wzbogacenia wiedzy oraz kreatywności. Nadaje się do trudnych scenariuszy wymagających zaawansowanego rozumowania."}, "qwen3-32b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-4b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-8b": {"description": "Qwen3 to nowa generacja modelu <PERSON>, który znacznie zwiększa możliwości w zakresie wnioskowania, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, agenta i wielojęzy<PERSON>ści, osiągając wiodące w branży wyniki w wielu kluczowych obszarach i wspierając przełączanie trybów myślenia."}, "qwen3-coder-480b-a35b-instruct": {"description": "Otwartoźródłowa wersja modelu kodowania Tongyi Qianwen. Najn<PERSON><PERSON> qwen3-coder-480b-a35b-instruct to model generowania kodu oparty na Qwen3, posiadający potężne zdolności agenta kodującego, specjalizujący się w wywoływaniu narzędzi i interakcji środowiskowej, umożliwiający autonomiczne programowanie z doskonałymi zdolnościami kodowania i ogólnymi."}, "qwen3-coder-plus": {"description": "Model kodowania Tongyi Qianwen. Najnowsza seria Qwen3-Coder-Plus to modele generowania kodu oparte na Qwen3, wyposażone w potężne zdolności agenta kodującego, specjalizujące się w wywoływaniu narzędzi i interakcji środowiskowej, umożliwiające autonomiczne programowanie z doskonałymi zdolnościami kodowania i ogólnymi."}, "qwq": {"description": "QwQ to eksperymentalny model <PERSON><PERSON><PERSON><PERSON>, skoncentrowany na zwiększeniu zdolności wnioskowania AI."}, "qwq-32b": {"description": "Model inferency QwQ, oparty na modelu Qwen2.5-32B, zost<PERSON>ł znacznie ulepszony dzięki uczeniu przez wzmo<PERSON>nienie, co zwiększa jego zdolności inferencyjne. Kluczowe wskaźniki modelu, takie jak matematyczny kod i inne (AIME 24/25, LiveCodeBench), oraz niektóre ogólne wskaźniki (IFEval, LiveBench itp.) osiągają poziom pełnej wersji DeepSeek-R1, a wszystkie wskaźniki znacznie przewyższają te, które są oparte na Qwen2.5-32B, w tym DeepSeek-R1-Distill-Qwen-32B."}, "qwq-32b-preview": {"description": "Model QwQ to eksperymentalny model badawczy opracowany przez z<PERSON><PERSON><PERSON><PERSON>, skoncentrowany na zwiększeniu zdolności wnioskowania AI."}, "qwq-plus": {"description": "Model wnioskowania QwQ oparty na modelu Qwen2.5, znacznie poprawiony dzięki uczeniu ze wzmocnieniem. Kluczowe wskaźniki modelu w matematyce i kodowaniu (AIME 24/25, LiveCodeBench) oraz niektóre wskaźniki ogólne (IFEval, LiveBench itp.) osiągają poziom pełnej wersji DeepSeek-R1."}, "qwq_32b": {"description": "Model wnioskowania średniej wielkości z serii Qwen. W porównaniu do tradycyjnych modeli dostosowanych do instrukcji, QwQ, posiadający zdolności myślenia i wnioskowania, może znacznie poprawić wydajność w zadaniach końcowych, zwłaszcza w rozwiązywaniu trudnych problemów."}, "r1-1776": {"description": "R1-1776 to wersja modelu DeepSeek R1, kt<PERSON>ra została poddana dalszemu tren<PERSON>i, a<PERSON> <PERSON><PERSON><PERSON>, bezstronne informacje faktograficzne."}, "solar-mini": {"description": "Solar Mini to kompaktowy LLM, który przewyższa GPT-3.5, posiadając potężne zdolności wielojęzyczne, wspierając angielski i koreański, oferując efektywne i zgrabne rozwiązania."}, "solar-mini-ja": {"description": "Solar Mini (Ja) rozszerza możliwości Solar Mini, koncentrując się na języku japońskim, jednocześnie zachowując wysoką efektywność i doskonałe osiągi w użyciu angielskiego i koreańskiego."}, "solar-pro": {"description": "Solar Pro to model LLM o wysokiej inteligencji wydany przez Upstage, koncentrujący się na zdolności do przestrzegania instrukcji na pojedynczym GPU, osiągając wynik IFEval powyżej 80. Obecnie wspiera język angielski, a wersja oficjalna planowana jest na listopad 2024, z rozszerzeniem wsparcia językowego i długości kontekstu."}, "sonar": {"description": "Lekki produkt wyszukiwania oparty na kontekście, szybszy i tańszy niż Sonar Pro."}, "sonar-deep-research": {"description": "Deep Research przeprowadza kompleksowe badania na poziomie eksperckim i łączy je w dostępne, praktyczne raporty."}, "sonar-pro": {"description": "Zaawansowany produkt wyszukiwania wspierający kontekst wyszukiwania, oferujący zaawansowane zapytania i śledzenie."}, "sonar-reasoning": {"description": "Nowy produkt API wspierany przez model wnioskowania DeepSeek."}, "sonar-reasoning-pro": {"description": "Nowy produkt API wspierany przez model wnioskowania DeepSeek."}, "stable-diffusion-3-medium": {"description": "<PERSON>jn<PERSON><PERSON> duży model generowania obrazów na podstawie tekstu wydany przez Stability AI. Ta wersja zachowuje zalety poprzednich generacji, jednocześnie znacząco poprawiając jako<PERSON> obrazu, rozumienie tekstu i różnorodność stylów. Potrafi dokładniej interpretować złożone naturalne polecenia i generować bardziej precyzyjne oraz zróżnicowane obrazy."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large to model multimodalnego dyfuzyjnego transformera (MMDiT) do generowania obrazów na podstawie tekstu, wyposażony w 800 milionów parametrów. Charakteryzuje się doskonałą jakością obrazu i zgodnością z poleceniami, wspiera generowanie obrazów o rozdzielczości do 1 miliona pikseli i działa efektywnie na standardowym sprzęcie konsumenckim."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo to model oparty na stable-diffusion-3.5-large, wykorzystujący technikę destylacji dyfuzji przeciwstawnej (ADD), oferujący wyższą szybkość działania."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 to model zainicjowany wagami ze stable-diffusion-v1.2 i dostrojony przez 595 tysięcy kroków na zbiorze \"laion-aesthetics v2 5+\" w rozdzielczości 512x512, z redukcją warunkowania tekstowego o 10% w celu poprawy próbkowania bez klasyfikatora."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl wprowadza znaczące ulepszenia w porównaniu do wersji v1.5 i osiąga efekty porównywalne z najlepszymi otwartymi modelami generacji obrazów, takimi jak midjourney. Kluczowe ulepszenia obejmują: trzykrotnie wię<PERSON><PERSON> unet backbone, dodanie modułu refinacji poprawiającego jakość generowanych obrazów oraz bardziej efektywne techniki treningowe."}, "stable-diffusion-xl-base-1.0": {"description": "Duży model generowania obrazów na podstawie tekstu opracowany i udostępniony przez Stability AI, wyróżniający się czołowymi zdolnościami twórczymi. Posiada doskonałe zdolności rozumienia instrukcji i wspiera definiowanie treści za pomocą odwrotnych promptów."}, "step-1-128k": {"description": "Równoważy wydajność i koszty, odpowiedni do ogólnych scenariuszy."}, "step-1-256k": {"description": "Posiada zdolność przetwarzania ultra długiego kontekstu, szczególnie odpowiedni do analizy długich dokumentów."}, "step-1-32k": {"description": "Obsługuje średniej długości dialogi, odpowiedni do różnych zastosowań."}, "step-1-8k": {"description": "<PERSON><PERSON><PERSON> model, odpowiedni do lekkich zadań."}, "step-1-flash": {"description": "Model o wysokiej p<PERSON>ę<PERSON>, odpowiedni do dialogów w czasie rzeczywistym."}, "step-1.5v-mini": {"description": "Ten model ma <PERSON>ężne zdolności rozumienia wideo."}, "step-1o-turbo-vision": {"description": "Model ten ma potężne zdolności rozumienia obrazów, w dziedzinie matematyki i kodowania przewyższa 1o. Model jest mniejszy niż 1o, a pręd<PERSON><PERSON><PERSON> wyjścia jest szybsza."}, "step-1o-vision-32k": {"description": "Ten model ma potężne zdolności rozumienia obrazów. W porównaniu do modeli z serii step-1v, of<PERSON><PERSON><PERSON> lepsze osiągi wizualne."}, "step-1v-32k": {"description": "Obsługuje wejścia wizualne, wzmacniając doświadczenie interakcji multimodalnych."}, "step-1v-8k": {"description": "<PERSON><PERSON><PERSON> model wiz<PERSON><PERSON>, odpowiedni do podstawowych zadań związanych z tekstem i obrazem."}, "step-1x-edit": {"description": "Model skoncentrowany na zadaniach edycji obrazów, potrafiący modyfikować i wzmacniać obrazy na podstawie dostarczonych przez użytkownika obrazów i opisów tekstowych. Obsługuje różne formaty wejściowe, w tym opisy tekstowe i obrazy przykładowe. Model rozumie intencje użytkownika i generuje zgodne z nimi wyniki edycji obrazów."}, "step-1x-medium": {"description": "Model o silnych zdolnościach generowania obrazów, obsługu<PERSON><PERSON><PERSON> wejścia w postaci opisów tekstowych. Posiada natywną obsługę języka chińskiego, co pozwala lepiej rozumieć i przetwarzać chińskie opisy tekstowe, dokładniej uchwycić ich znaczenie i przekształcić je w cechy obrazu, umożliwiając precyzyjne generowanie obrazów. Model generuje obrazy o wysokiej rozdzielczości i jakości oraz posiada pewne zdolności transferu stylu."}, "step-2-16k": {"description": "Obsługuje interakcje z dużą ilością kontekstu, idealny do złożonych scenariuszy dialogowy<PERSON>."}, "step-2-16k-exp": {"description": "Eksperymentalna wersja modelu step-2, zawierają<PERSON> najnowsze funkcje, aktualizacje w trybie ciągłym. Nie zaleca się używania w produkcji."}, "step-2-mini": {"description": "Model oparty na nowej generacji własnej architektury Attention MFA, osiągający podobne wyniki jak step1 przy bardzo niskich kosztach, jednocześnie zapewniając wyższą przepustowość i szybszy czas reakcji. Potrafi obsługiwać ogólne zadania, a w zakresie umiejętności kodowania ma szczególne zdolności."}, "step-2x-large": {"description": "Nowa generacja modelu Step Star, skoncentrowana na generowaniu obrazów na podstawie tekstu. Model tworzy obrazy o bardziej realistycznej fakturze i lepszych zdolnościach generowania tekstu w języku chińskim i angielskim."}, "step-r1-v-mini": {"description": "Model ten to potężny model wnioskowania z zdolnościami rozumienia obrazów, zdolny do przetwarzania informacji wizualnych i tekstowych, generując tekst po głę<PERSON><PERSON> przem<PERSON>. Model ten wyróżnia się w dziedzinie wnioskowania wizualnego, a także posiada pierwszorzędne zdolności wnioskowania matematycznego, kodowania i tekstu. Długość kontekstu wynosi 100k."}, "taichu_llm": {"description": "Model językowy TaiChu charakteryzuje się wyjątkową zdolnością rozumienia języka oraz umiejętnościami w zakresie tworzenia tekstów, odpowiadania na pytania, programowania, ob<PERSON><PERSON><PERSON> matematycznych, wnioskowania logicznego, analizy emocji i streszczenia tekstu. Innowacyjnie łączy wstępne uczenie się na dużych zbiorach danych z bogatą wiedzą z wielu źródeł, stale doskonaląc technologię algorytmiczną i nieustannie przyswajając nową wiedzę z zakresu słownictwa, struktury, gramatyki i semantyki z ogromnych zbiorów danych tekstowych, co prowadzi do ciągłej ewolucji modelu. Umożliwia użytkownikom łatwiejszy dostęp do informacji i usług oraz bardziej inteligentne doświadczenia."}, "taichu_o1": {"description": "taichu_o1 to nowa generacja modelu wnioskowania, która poprzez interakcje multimodalne i uczenie przez wzmocnienie realizuje łańcuchy myślenia przypominające ludzkie, wspierając złożone symulacje decyzji, jednocześnie prezentując ścieżki myślenia modelu przy zachowaniu wysokiej precyzji wyników, odpowiednia do analizy strategii i głębokiego myślenia."}, "taichu_vl": {"description": "Łączy zdolności rozumienia obrazów, transferu wiedzy i logicznego wnioskowania, wyróżniając się w dziedzinie pytań i odpowiedzi na podstawie tekstu i obrazów."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct ma 80 miliardów parametrów, a aktywacja 13 miliardów parametrów pozwala mu konkurować z większymi modelami. Wspiera hybrydowe wnioskowanie „szybkiego myślenia/powolnego myślenia”; stabilne rozumienie długich tekstów; potwierdzona przewaga zdolności agenta w testach BFCL-v3 i τ-Bench; dzięki połączeniu GQA i wielu formatów kwantyzacji zapewnia efektywne wnioskowanie."}, "text-embedding-3-large": {"description": "Najpotężniej<PERSON>y model <PERSON><PERSON><PERSON><PERSON><PERSON>, odpowiedni do zadań w języku angielskim i innych językach."}, "text-embedding-3-small": {"description": "Nowej generacji model Embedding, efektywny i ekonomiczny, odpowiedni do wyszukiwania wiedzy, aplikacji RAG i innych scenariuszy."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 to d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (chińsko-angielski) model j<PERSON><PERSON><PERSON><PERSON> o otwartych wagach 32B, zoptymalizowany do generowania kodu, wywołań funkcji i zadań agentowych. Został wstępnie wytrenowany na 15T wysokiej jakości danych i danych do ponownego wnioskowania, a następnie udoskonalony przy użyciu dostosowania do preferencji ludzkich, próbkowania odrzucającego i uczenia przez wzmocnienie. Model wykazuje doskonałe wyniki w złożonym wnioskowaniu, generowaniu artefaktów i zadaniach związanych z wyjściem strukturalnym, osiągając wyniki porównywalne z GPT-4o i DeepSeek-V3-0324 w wielu testach porównawczych."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 to d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (chińsko-angielski) model j<PERSON><PERSON><PERSON><PERSON> o otwartych wagach 32B, zoptymalizowany do generowania kodu, wywołań funkcji i zadań agentowych. Został wstępnie wytrenowany na 15T wysokiej jakości danych i danych do ponownego wnioskowania, a następnie udoskonalony przy użyciu dostosowania do preferencji ludzkich, próbkowania odrzucającego i uczenia przez wzmocnienie. Model wykazuje doskonałe wyniki w złożonym wnioskowaniu, generowaniu artefaktów i zadaniach związanych z wyjściem strukturalnym, osiągając wyniki porównywalne z GPT-4o i DeepSeek-V3-0324 w wielu testach porównawczych."}, "thudm/glm-4-9b-chat": {"description": "Otwarta wersja najnowszej generacji modelu pretrenowanego GLM-4 wydanego przez Zhipu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 to model językowy o 9 miliardach parametrów w serii GLM-4 opracowany przez THUDM. GLM-4-9B-0414 wykorzystuje te same strategie uczenia przez wzmocnienie i dostosowania, co jego wi<PERSON><PERSON><PERSON> model odpowiadający 32B, osiągając wysoką wydajność w stosunku do swojej skali, co czyni go odpowiednim do wdrożeń z ograniczonymi zasobami, które nadal wymagają silnych zdolności rozumienia i generowania języka."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 to wzmocniona wariant wnioskowania GLM-4-32B, zaprojektowana do rozwiązywania głębokich problemów matematycznych, logicznych i związanych z kodem. Wykorzystuje rozszerzone uczenie przez wzmocnienie (specyficzne dla zadań i oparte na ogólnych preferencjach par) w celu poprawy wydajności w złożonych zadaniach wieloetapowych. W porównaniu do podstawowego modelu GLM-4-32B, Z1 znacznie poprawia zdolności w zakresie wnioskowania strukturalnego i formalnego.\n\nModel wspiera wymuszanie kroków 'myślenia' poprzez inżynierię podpowiedzi i zapewnia poprawioną spójność dla długich formatów wyjściowych. Jest zoptymalizowany pod kątem przepływów pracy agentów i wspiera długi kontekst (przez YaRN), wywołania narzędzi JSON oraz konfiguracje drobnoziarnistego próbkowania dla stabilnego wnioskowania. Idealny do przypadków użycia wymagających przemyślanego, wieloetapowego wnioskowania lub formalnych dedukcji."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 to wzmocniona wariant wnioskowania GLM-4-32B, zaprojektowana do rozwiązywania głębokich problemów matematycznych, logicznych i związanych z kodem. Wykorzystuje rozszerzone uczenie przez wzmocnienie (specyficzne dla zadań i oparte na ogólnych preferencjach par) w celu poprawy wydajności w złożonych zadaniach wieloetapowych. W porównaniu do podstawowego modelu GLM-4-32B, Z1 znacznie poprawia zdolności w zakresie wnioskowania strukturalnego i formalnego.\n\nModel wspiera wymuszanie kroków 'myślenia' poprzez inżynierię podpowiedzi i zapewnia poprawioną spójność dla długich formatów wyjściowych. Jest zoptymalizowany pod kątem przepływów pracy agentów i wspiera długi kontekst (przez YaRN), wywołania narzędzi JSON oraz konfiguracje drobnoziarnistego próbkowania dla stabilnego wnioskowania. Idealny do przypadków użycia wymagających przemyślanego, wieloetapowego wnioskowania lub formalnych dedukcji."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 to model językowy o 9 miliardach parametrów w serii GLM-4 opracowany przez THUDM. Wykorzystuje techniki pierwotnie zastosowane w większym modelu GLM-Z1, w tym rozszerzone uczenie przez wzmocnienie, dostosowanie rankingowe w parach oraz trening do zadań intensywnie wymagających wnioskowania, takich jak matematyka, kodowanie i logika. Mi<PERSON> mniejszej skali, wykazuje silną wydajność w ogólnych zadaniach wnioskowania i przewyższa wiele modeli open source na poziomie swoich wag."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B to model głębokiego wnioskowania o 32 miliardach parametrów w serii GLM-4-Z1, zoptymalizowany do złożonych, otwart<PERSON> zadań wymagających długotrwałego myślenia. Opiera się na glm-4-32b-0414, dodaj<PERSON>c dodatkowe etapy uczenia przez wzmocnienie i strategie wieloetapowego dostosowania, wprow<PERSON><PERSON>ją<PERSON> zdolność 'refleksji' mającą na celu symulację rozszerzonego przetwarzania poznawczego. Obejmuje to iteracyjne wnioskowanie, analizy wielokrokowe i wzbogacone narzędziami przepływy pracy, takie jak wyszukiwanie, pobieranie i syntezę z uwzględnieniem cytatów.\n\nModel doskonale sprawdza się w pisaniu badawczym, analizie porównawczej i złożonych pytaniach i odpowiedziach. Obsługuje wywołania funkcji dla prymitywów wyszukiwania i nawigacji (`search`, `click`, `open`, `finish`), co umożliwia jego użycie w agentowych przepływach pracy. Zachowanie refleksyjne kształtowane jest przez wieloetapową kontrolę cykliczną z nagrodami opartymi na regułach i mechanizmem opóźnionych decyzji, a także na głębokich ramach badawczych, takich jak wewnętrzny stos dostosowujący OpenAI. Ten wariant jest odpowiedni dla scenariuszy wymagających głębokości, a nie szybkości."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera powstał poprzez połączenie DeepSeek-R1 i DeepSeek-V3 (0324), <PERSON><PERSON><PERSON><PERSON><PERSON> zdolności wnioskowania R1 z poprawą efektywności tokenów V3. Opiera się na architekturze DeepSeek-MoE Transformer i został zoptymalizowany do ogólnych zadań generowania tekstu.\n\nModel łączy w sobie wagi wstępnie wytrenowane z dwóch źródłowych modeli, aby zrównoważyć wydajność wnioskowania, efektywności i przestrzegania instrukcji. Został wydany na licencji MIT, z zamiarem użycia w badaniach i zastosowaniach komercyjnych."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) oferuje zwiększoną moc obliczeniową dzięki efektywnym strategiom i architekturze modelu."}, "tts-1": {"description": "Najnowocześniejszy model tekstu na mowę, zoptymalizowany pod kątem szybkości w scenariuszach w czasie rzeczywistym."}, "tts-1-hd": {"description": "Na<PERSON><PERSON>ocześniejszy model tekstu na mowę, zoptymalizowany pod kątem jakości."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) jest przeznaczony do precyzyjnych zadań poleceniowych, of<PERSON><PERSON><PERSON><PERSON><PERSON> doskonałe możliwości przetwarzania języka."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Sonnet podnosi standardy branżowe, przew<PERSON>ż<PERSON><PERSON>ąc modele konkurencji oraz Claude 3 Opus, osiągając doskonałe wyniki w szerokim zakresie ocen, przy zachowaniu prędkości i kosztów naszych modeli średniego poziomu."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonet to na<PERSON><PERSON><PERSON><PERSON><PERSON> model następnej generacji od Anthropic. W porównaniu do Claude 3 Haiku, Claude 3.7 Sonet wykazuje poprawę w różnych umiejętnościach i przewyższa najwię<PERSON>zy model pop<PERSON><PERSON><PERSON><PERSON> generacji, Claude 3 Opus, w wielu testach inteligencji."}, "v0-1.0-md": {"description": "Model v0-1.0-md to starsza wersja modelu udostępniana przez API v0"}, "v0-1.5-lg": {"description": "Model v0-1.5-lg jest przeznaczony do zaawansowanych zadań myślenia lub rozumowania"}, "v0-1.5-md": {"description": "Model v0-1.5-md jest odpowiedni do codziennych zadań i generowania interfejsu użytkownika (UI)"}, "wan2.2-t2i-flash": {"description": "Wersja ekspresowa Wanxiang 2.2, <PERSON><PERSON><PERSON><PERSON> model. Kompleksowo ulepszony pod względem kreatywności, stabilności i realizmu, generuje szybko i oferuje wysoką op<PERSON>."}, "wan2.2-t2i-plus": {"description": "Profesjonalna wersja Wanxiang 2.2, <PERSON><PERSON><PERSON><PERSON> model. Kompleksowo ulepszony pod względem kreatywności, stabilności i realizmu, generuje obrazy o bogatych detalach."}, "wanx-v1": {"description": "Podstawowy model generowania obrazów na podstawie tekstu. Odpowiada uniwersalnemu modelowi 1.0 na oficjalnej stronie Tongyi Wanxiang."}, "wanx2.0-t2i-turbo": {"description": "Specjalizuje się w realistycznych portretach, oferuje średnią prędkość i niskie koszty. Odpowiada ekspresowemu modelowi 2.0 na oficjalnej stronie Tongyi Wanxiang."}, "wanx2.1-t2i-plus": {"description": "Wersja z kompleksowymi ulepszeniami. Generuje obrazy o bogatszych detalach, z nieco wolniejszą prędkością. Odpowiada profesjonalnemu modelowi 2.1 na oficjalnej stronie Tongyi Wanxiang."}, "wanx2.1-t2i-turbo": {"description": "Wersja z kompleksowymi ulepszeniami. <PERSON><PERSON><PERSON>, oferuje wszechstronne efekty i wysoką opłacalność. Odpowiada ekspresowemu modelowi 2.1 na oficjalnej stronie Tongyi Wanxiang."}, "whisper-1": {"description": "Uniwersalny model r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mowy, obsług<PERSON><PERSON><PERSON><PERSON> wielojęzyczne rozpoznawanie mowy, tłumaczenie mowy oraz identyfikację języka."}, "wizardlm2": {"description": "WizardLM 2 to model językowy dostarczany przez Microsoft AI, który wyróżnia się w złożonych dialogach, wiel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wnioskowaniu i inteligentnych asystentach."}, "wizardlm2:8x22b": {"description": "WizardLM 2 to model językowy dostarczany przez Microsoft AI, który wyróżnia się w złożonych dialogach, wiel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wnioskowaniu i inteligentnych asystentach."}, "x1": {"description": "Model Spark X1 zostanie da<PERSON><PERSON>, osiągając wyniki w zadaniach ogólnych, takich jak rozumowanie, generowanie tekstu i rozumienie języka, które będą porównywalne z OpenAI o1 i DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 to ulepszona wersja Yi. Używa ona wysokiej jakości korpusu danych o rozmiarze 500B tokenów do dalszego wstępnego treningu Yi, a także do dopasowywania na 3M różnorodnych próbkach dopasowujących."}, "yi-large": {"description": "Nowy model z miliardami parametrów, oferujący niezwykłe możliwości w zakresie pytań i generowania tekstu."}, "yi-large-fc": {"description": "Model yi-large z wzmocnioną zdolnością do wywołań narzędzi, odpowiedni do różnych scenariuszy biznesowych wymagających budowy agentów lub workflow."}, "yi-large-preview": {"description": "Wersja wstępna, zaleca się korzystanie z yi-large (nowa wersja)."}, "yi-large-rag": {"description": "Zaawansowana usługa oparta na modelu yi-large, łącząca techniki wyszukiwania i generowania, oferująca precyzyjne odpowiedzi oraz usługi wyszukiwania informacji w czasie rzeczywistym."}, "yi-large-turbo": {"description": "Model o doskonałym stosunku jakości do ceny, z doskonałymi osiągami. Wysokiej precyzji optymalizacja w oparciu o wydaj<PERSON>ć, s<PERSON><PERSON><PERSON><PERSON><PERSON> wnioskowania i koszty."}, "yi-lightning": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>śniej<PERSON>y model o wysokiej wydaj<PERSON>ci, zapewniający wysoką jakość wyjściową przy znacznie zwiększonej prędkości wnioskowania."}, "yi-lightning-lite": {"description": "<PERSON><PERSON> we<PERSON>, zaleca się użycie yi-lightning."}, "yi-medium": {"description": "Model średnie<PERSON> w<PERSON>, zrównoważony pod względem możliwości i kosztów. Głęboko zoptymalizowana zdolność do przestrzegania poleceń."}, "yi-medium-200k": {"description": "Okno kontekstowe o długości 200K, oferujące głębokie zrozumienie i generowanie długich tekstów."}, "yi-spark": {"description": "<PERSON><PERSON><PERSON>, ale pot<PERSON>, lekki model o wysokiej prędkości. Oferuje wzmocnione możliwości obliczeń matematycznych i pisania kodu."}, "yi-vision": {"description": "Model do złożonych zadań wizualnych, oferujący wysoką wydajność w zakresie rozumienia i analizy obrazów."}, "yi-vision-v2": {"description": "Model do złożonych zadań wizualnych, oferujący wysokowydajną zdolność rozumienia i analizy na podstawie wielu obrazów."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 to podstawowy model zaprojektowany specjalnie do zastosowań agentowych, wykorzystujący architekturę mieszanych ekspertów (Mixture-of-Experts). Model jest g<PERSON><PERSON><PERSON><PERSON> z<PERSON> pod kątem wywoływania narzędzi, przeglądania stron internetowych, inżynierii oprogramowania i programowania frontendowego, wspierając bezproblemową integrację z inteligentnymi agentami kodu takimi jak Claude Code i Roo Code. GLM-4.5 stosuje hybrydowy tryb wnioskowania, dostosowując się do złożonych i codziennych scenariuszy użycia."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air to podstawowy model zaprojektowany specjalnie do zastosowań agentowych, wykorzystujący architekturę mieszanych ekspertów (Mixture-of-Experts). Model jest g<PERSON><PERSON><PERSON><PERSON> pod kątem wywoływania narzędzi, przeglądania stron internetowych, inżynierii oprogramowania i programowania frontendowego, wspierając bezproblemową integrację z inteligentnymi agentami kodu takimi jak Claude Code i Roo Code. GLM-4.5 stosuje hybrydowy tryb wnioskowania, dostosowując się do złożonych i codziennych scenariuszy użycia."}}