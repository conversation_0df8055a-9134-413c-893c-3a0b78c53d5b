{"azure": {"azureApiVersion": {"desc": "Wersja API Azure, stosuj format YYYY-MM-DD, zob<PERSON>z [najnowszą wersję](https://learn.microsoft.com/pl-pl/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON>", "title": "Wersja Azure API"}, "empty": "Wprowadź identyfikator modelu, a<PERSON> <PERSON><PERSON><PERSON> pier<PERSON> model", "endpoint": {"desc": "Wartość można znaleźć w sekcji 'Klucze i punkty końcowe' podczas sprawdzania zasobu w portalu Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Adres API Azure"}, "modelListPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lub dodaj model OpenAI, który wdrożyłeś", "title": "Azure OpenAI", "token": {"desc": "Wartość można znaleźć w sekcji 'Klucze i punkty końcowe' podczas sprawdzania zasobu w portalu Azure. Możesz użyć KEY1 lub KEY2", "placeholder": "Azure API Key", "title": "Klucz API"}}, "azureai": {"azureApiVersion": {"desc": "Wersja API Azure, w formacie YYYY-MM-DD, sprawd<PERSON> [najnowszą wersję](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON>", "title": "Wersja API Azure"}, "endpoint": {"desc": "Znajdź punkt końcowy wnioskowania modelu Azure AI w przeglądzie projektu Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Punkt końcowy Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "Znajdź klucz API w przeglądzie projektu Azure AI", "placeholder": "Klucz Azure", "title": "<PERSON><PERSON>cz"}}, "bedrock": {"accessKeyId": {"desc": "Wprowadź AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "Test czy AWS AccessKeyId / SecretAccessKey są poprawnie wypełnione"}, "region": {"desc": "Wprowadź AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Wprowadź AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "<PERSON><PERSON><PERSON> korzystasz z AWS SSO/STS, wprowadź swój token sesji AWS", "placeholder": "Token sesji AWS", "title": "Token <PERSON>ji <PERSON> (opcjonalnie)"}, "title": "Bedrock", "unlock": {"customRegion": "Niestandardowy region usługi", "customSessionToken": "Niestandardowy token sesji", "description": "Wprowadź swój AWS AccessKeyId / SecretAccessKey, aby r<PERSON> sesję. Aplikacja nie będzie przechowywać Twojej konfiguracji uwierzytelniania", "imageGenerationDescription": "Wprowadź swój AWS AccessKeyId / SecretAccessKey, aby r<PERSON> generowanie. Aplikacja nie będzie zapisywać Twoich danych uwierzytelniających", "title": "Użyj niestandardowych informacji uwierzytelniających Bedrock"}}, "cloudflare": {"apiKey": {"desc": "Wprowadź klucz Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Wprowadź ID konta Cloudflare lub adres API niestandardowy", "placeholder": "ID konta Cloudflare / adres API niestandardowy", "title": "ID konta Cloudflare / adres API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Proszę wpisać swój klucz API", "title": "Klucz API"}, "basicTitle": "Podstawowe informacje", "configTitle": "Informacje konfiguracyjne", "confirm": "Utwórz", "createSuccess": "Utworzenie zakończone sukcesem", "description": {"placeholder": "Opis dostawcy usług (opcjonalnie)", "title": "Opis dostawcy usług"}, "id": {"desc": "Unikalny identyfikator dostawcy usług, po utworzeniu nie można go zmienić", "format": "<PERSON><PERSON><PERSON> zaw<PERSON> t<PERSON> c<PERSON>, małe litery, my<PERSON><PERSON><PERSON> (-) i podkreślenia (_) ", "placeholder": "Zaleca się użycie małych liter, np. openai, po utworzeniu nie można edytować", "required": "<PERSON>sz<PERSON> wpisać identyfikator dostawcy", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dosta<PERSON>cy"}, "logo": {"required": "Proszę przesłać poprawne logo dostawcy", "title": "<PERSON><PERSON> dos<PERSON>"}, "name": {"placeholder": "Proszę wpisać nazwę wyświetlaną dostawcy", "required": "Proszę wpisać nazwę dostawcy", "title": "<PERSON><PERSON><PERSON>"}, "proxyUrl": {"required": "<PERSON><PERSON><PERSON> w<PERSON> adres proxy", "title": "Adres proxy"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Proszę wybrać typ SDK", "title": "Format żądania"}, "title": "Utwórz niestandardowego dostawcę AI"}, "github": {"personalAccessToken": {"desc": "Wprowadź swój osobisty token dostępu GitHub (PAT), klik<PERSON><PERSON> [tutaj](https://github.com/settings/tokens), aby go utworzyć", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "W<PERSON>rowadź swój token HuggingFace, klik<PERSON>j [tutaj](https://huggingface.co/settings/tokens), aby go ut<PERSON><PERSON><PERSON><PERSON>", "placeholder": "hf_xxxxxxxxx", "title": "Token <PERSON>"}}, "list": {"title": {"disabled": "Usługa nieaktywna", "enabled": "Usługa aktywna"}}, "menu": {"addCustomProvider": "<PERSON><PERSON><PERSON>rdo<PERSON>", "all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"disabled": "Nieaktywny", "enabled": "Aktywny"}, "notFound": "Nie znaleziono wyników wyszukiwania", "searchProviders": "<PERSON><PERSON>j dostawców...", "sort": "Niestandardowe sortowanie"}, "ollama": {"checker": {"desc": "Test czy adres proxy jest poprawnie wypełniony", "title": "Sprawdzanie łączności"}, "customModelName": {"desc": "<PERSON><PERSON><PERSON> w<PERSON> model, oddzielaj modele przecinkiem (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Nazwa własnego modelu"}, "download": {"desc": "Ollama pobiera ten model, proszę nie zamykać tej strony. Wznowienie pobierania nastąpi od miejsca przerwania", "failed": "Pobieranie modelu nie powiodło się, sprawdź połączenie sieciowe lub us<PERSON><PERSON>, a następnie spróbuj ponownie", "remainingTime": "Pozostały czas", "speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć pobierania", "title": "Pobieranie modelu {{model}}"}, "endpoint": {"desc": "<PERSON><PERSON> http(s)://, lo<PERSON><PERSON>, jeśli nie określon<PERSON> inaczej, mo<PERSON>na pozost<PERSON> puste", "title": "Adres proxy API"}, "title": "Ollama", "unlock": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "description": "Wprowadź etykietę swojego modelu Ollama, aby zakończyć i kontynuować rozmowę", "downloaded": "{{completed}} / {{total}}", "starting": "Rozpoczynam pobieranie...", "title": "<PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON>"}}, "providerModels": {"config": {"aesGcm": "Twój klucz oraz adres proxy będą szyfrowane za pomocą <1>AES-GCM</1>", "apiKey": {"desc": "<PERSON><PERSON><PERSON> wpisać swój {{name}} klucz API", "descWithUrl": "<PERSON><PERSON>ę wprowadzić swój klucz API {{name}}, <3>k<PERSON><PERSON><PERSON> tutaj, aby go u<PERSON><PERSON>ć</3>", "placeholder": "{{name}} klucz API", "title": "Klucz API"}, "baseURL": {"desc": "<PERSON><PERSON> http(s)://", "invalid": "Proszę wprow<PERSON><PERSON>ć prawidłowy URL", "placeholder": "https://your-proxy-url.com/v1", "title": "Adres proxy API"}, "checker": {"button": "Sprawdź", "desc": "<PERSON><PERSON><PERSON>, czy klucz API i adres proxy są poprawnie wpisane", "pass": "Sprawdzenie zakończone sukcesem", "title": "Sprawdzenie łączności"}, "fetchOnClient": {"desc": "Tryb żądania klienta rozpocznie sesję bezpośrednio z przeglądarki, co może przyspieszyć czas odpowiedzi", "title": "Użyj trybu żądania klienta"}, "helpDoc": "Dokumentacja konfiguracyjna", "responsesApi": {"desc": "Wykorzystuje nową generację formatu zapytań OpenAI, odblokowuj<PERSON><PERSON> zaawansowane funk<PERSON>je, takie jak łań<PERSON><PERSON> myślowe", "title": "Użyj specyfikacji Responses API"}, "waitingForMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> modeli jest w <1>planach integracji</1>, <PERSON><PERSON><PERSON>"}, "createNew": {"title": "Utwórz niestandardowy model AI"}, "item": {"config": "Konfiguracja modelu", "customModelCards": {"addNew": "Utwórz i dodaj model {{id}}", "confirmDelete": "Zaraz usuniesz ten niestandardowy model, po usunięciu nie będzie można go przy<PERSON><PERSON><PERSON><PERSON>ć, <PERSON><PERSON><PERSON> d<PERSON> ostroż<PERSON>."}, "delete": {"confirm": "<PERSON>zy na pewno chcesz usunąć model {{displayName}}?", "success": "Usunięcie zakończone sukcesem", "title": "Usuń model"}, "modelConfig": {"azureDeployName": {"extra": "<PERSON>, kt<PERSON>re jest rzeczywiście używane w Azure OpenAI", "placeholder": "Proszę wpisać nazwę wdrożenia modelu w Azure", "title": "Nazwa wdrożenia modelu"}, "deployName": {"extra": "To pole będzie używane jako identyfikator modelu podczas wysyłania żądania", "placeholder": "Wprowadź rzeczywistą nazwę lub identyfikator wdrożenia modelu", "title": "Nazwa wdrożenia modelu"}, "displayName": {"placeholder": "<PERSON><PERSON><PERSON> wpisać nazwę wyświetlaną modelu, np. ChatGPT, GPT-4 itp.", "title": "Nazwa wyświetlana modelu"}, "files": {"extra": "Obecna implementacja przesyłania plików jest jedynie rozwiązaniem hackowym, przeznaczonym do samodzielnego testowania. Pełna funkcjonalność przesyłania plików będzie dostępna w przyszłości.", "title": "Wsparcie dla przesyłania plików"}, "functionCall": {"extra": "Ta konfiguracja włączy jedynie możliwość korzystania z narzędzi przez model, co pozwoli na dodanie wtyczek narzędziowych. <PERSON><PERSON><PERSON><PERSON><PERSON>, czy model rzeczywiście obsługuje korzystanie z narzędzi, zależy całkowicie od samego modelu, proszę samodzielnie przetestować jego użyte<PERSON>ść", "title": "Wsparcie dla korzystania z narzędzi"}, "id": {"extra": "<PERSON><PERSON> można zmie<PERSON> po ut<PERSON><PERSON><PERSON>u, bę<PERSON><PERSON> używane jako identyfikator modelu podczas wywoływania AI", "placeholder": "W<PERSON>rowadź identyfikator modelu, na przykład gpt-4o lub claude-3.5-sonnet", "title": "ID modelu"}, "modalTitle": "Konfiguracja niestandardowego modelu", "reasoning": {"extra": "Ta konfiguracja włączy jedynie zdolność modelu do głębokiego myślenia, a konkretne efekty w pełni zależą od samego modelu. Proszę samodzielnie przetestować, czy model ma zdolność do głębokiego myślenia.", "title": "Wsparcie dla głębokiego myślenia"}, "tokens": {"extra": "Ustaw maksymalną liczbę tokenów wspieranych przez model", "title": "Maksymalne okno kontekstu", "unlimited": "Bez ograniczeń"}, "vision": {"extra": "Ta konfiguracja włączy tylko możliwość przesyłania obrazów w aplikacji, czy model obsługuje rozpoznawanie zależy od samego modelu, proszę samodzielnie przetestować dostępność rozpoznawania wizualnego tego modelu.", "title": "Wsparcie dla rozpoznawania wizualnego"}}, "pricing": {"image": "${{amount}}/obraz", "inputCharts": "${{amount}}/M znaków", "inputMinutes": "${{amount}}/minut", "inputTokens": "W<PERSON>rowadzenie ${{amount}}/M", "outputTokens": "Wyjście ${{amount}}/M"}, "releasedAt": "<PERSON><PERSON><PERSON> {{releasedAt}}"}, "list": {"addNew": "Dodaj model", "disabled": "Nieaktywne", "disabledActions": {"showMore": "Pokaż więcej"}, "empty": {"desc": "<PERSON><PERSON><PERSON> model niestandardowy lub pobrać model, a<PERSON> <PERSON><PERSON> kor<PERSON>.", "title": "Brak dostępnych modeli"}, "enabled": "Aktywne", "enabledActions": {"disableAll": "Dezaktywuj wszystkie", "enableAll": "Aktywuj wszystkie", "sort": "Sortowanie modeli niestandardowych"}, "enabledEmpty": "Brak aktywnych modeli, aktywuj ulubione modele z poniższej listy~", "fetcher": {"clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pobrane modele", "fetch": "<PERSON><PERSON><PERSON> modeli", "fetching": "Pobieranie listy modeli...", "latestTime": "Ostatnia aktualizacja: {{time}}", "noLatestTime": "Lista nie została jeszcze pobrana"}, "resetAll": {"conform": "Czy na pewno chcesz zresetować wszystkie zmiany w bieżącym modelu? Po zresetowaniu lista modeli wróci do stanu domyślnego", "success": "Resetowanie zakończone sukcesem", "title": "Zresetuj wszystkie zmiany"}, "search": "Szukaj modeli...", "searchResult": "Znaleziono {{count}} modeli", "title": "Lista modeli", "total": "Łą<PERSON><PERSON> dostępnych modeli: {{count}}"}, "searchNotFound": "Nie znaleziono wyników wyszukiwania"}, "sortModal": {"success": "Aktualizacja sortowania zakończona sukcesem", "title": "Niestandardowe sortowanie", "update": "Aktualizuj"}, "updateAiProvider": {"confirmDelete": "Zaraz usuniesz tego dostawcę AI, po usunięciu nie będzie można go przywrócić, czy na pewno chcesz usunąć?", "deleteSuccess": "Usunięcie zakończone sukcesem", "tooltip": "Aktualizuj podstawowe ustawienia dostawcy", "updateSuccess": "Aktualizacja zakończona sukcesem"}, "updateCustomAiProvider": {"title": "Aktualizuj konfigurację dostawcy AI"}, "vertexai": {"apiKey": {"desc": "Wprowadź swoje klucze Vertex AI", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Klucze Vertex AI"}}, "zeroone": {"title": "01.<PERSON> <PERSON>"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}