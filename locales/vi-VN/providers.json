{"ai21": {"description": "AI21 Labs xây dựng các mô hình cơ bản và hệ thống trí tuệ nhân tạo cho doanh nghiệ<PERSON>, tăng tốc ứng dụng trí tuệ nhân tạo sinh sinh trong sản xuất."}, "ai360": {"description": "360 AI là nền tảng mô hình và dịch vụ AI do công ty 360 phát hành, cung cấp nhiều mô hình xử lý ngôn ngữ tự nhiên tiên tiến, bao gồm 360GPT2 Pro, 360GPT Pro, 360GPT Turbo và 360GPT Turbo Responsibility 8K. Những mô hình này kết hợp giữa tham số quy mô lớn và khả năng đa phư<PERSON> thứ<PERSON>, đ<PERSON><PERSON><PERSON> <PERSON>ng dụng rộng rãi trong tạo văn bản, hi<PERSON><PERSON> ngữ nghĩa, hệ thống đối thoại và tạo mã. Thông qua chiến lư<PERSON><PERSON> gi<PERSON> l<PERSON>h ho<PERSON>, 360 AI đáp ứng nhu cầu đa dạng của người dùng, hỗ trợ nhà phát triển tí<PERSON> hợ<PERSON>, thúc đẩy sự đổi mới và phát triển ứng dụng thông minh."}, "aihubmix": {"description": "AiHubMix cung cấp truy cập đến nhiều mô hình AI thông qua một giao diện API thống nhất."}, "anthropic": {"description": "Anthropic là một công ty tập trung vào nghiên cứu và phát triển trí tuệ nhân tạo, cung cấp một loạt các mô hình ngôn ngữ tiên tiến nh<PERSON> 3.5 Son<PERSON>, <PERSON> 3 <PERSON><PERSON>, Claude 3 Opus và Claude 3 Haiku. Những mô hình này đạt được sự cân bằng lý tưởng giữa trí thông minh, tốc độ và chi phí, phù hợp cho nhiều ứng dụng từ khối lượng công việc doanh nghiệp đến phản hồ<PERSON> n<PERSON>h. <PERSON> 3.5 <PERSON><PERSON>, là mô hình mới nhất củ<PERSON> họ, thể hiện xuất sắc trong nhiều đánh giá, đồng thời duy trì tỷ lệ hiệu suất cao."}, "azure": {"description": "Azure cung cấp nhiều mô hình AI tiên tiến, bao gồm GPT-3.5 và dòng GPT-4 mớ<PERSON> nhất, hỗ trợ nhiều loại dữ liệu và nhiệm vụ phức tạp, cam kết cung cấp các giải pháp <PERSON> an toàn, đáng tin cậy và bền vững."}, "azureai": {"description": "Azure cung cấp nhiều mô hình AI tiên tiến, bao gồm GPT-3.5 và dòng GPT-4 mớ<PERSON> nhất, hỗ trợ nhiều loại dữ liệu và nhiệm vụ phức tạp, cam kết cung cấp các giải pháp <PERSON> an toàn, đáng tin cậy và bền vững."}, "baichuan": {"description": "Baichuan Intelligent là công ty tập trung vào nghiên cứu phát triển mô hình ngôn ngữ lớn AI, mô hình của họ thể hiện xuất sắc trong các nhiệm vụ tiếng Trung như bách khoa to<PERSON><PERSON> thư, xử lý văn bản dài và sáng tác, v<PERSON><PERSON><PERSON> tr<PERSON><PERSON> hơn so với các mô hình chính thống quốc tế. Baichuan Intelligent còn có khả năng đa phương thức hàng đầu trong ngành, thể hiện xuất sắc trong nhiều bài kiểm tra uy tín. Cá<PERSON> mô hình của họ bao gồm Baichuan 4, Baichuan 3 Turbo và Baichuan 3 Turbo 128k, đư<PERSON><PERSON> tối ưu hóa cho các tình huống ứng dụng khác nhau, cung cấp gi<PERSON>i pháp hiệu quả về chi phí."}, "bedrock": {"description": "Bedrock là dịch vụ do Amazon AWS cung cấp, tập trung vào việc cung cấp các mô hình ngôn ngữ AI và mô hình hình ảnh tiên tiến cho doanh nghiệp. Gia đình mô hình của nó bao gồm dòng <PERSON> c<PERSON>, dòng Llama 3.1 c<PERSON><PERSON>, v.v., bao quát nhiều lựa chọn từ nhẹ đến hiệu suất cao, hỗ trợ nhiều nhiệm vụ như tạ<PERSON> vă<PERSON> b<PERSON>, đ<PERSON><PERSON>o<PERSON>, x<PERSON> lý hình <PERSON>nh, phù hợp cho các ứng dụng doanh nghiệp với quy mô và nhu cầu khác nhau."}, "cloudflare": {"description": "Chạy các mô hình học máy được hỗ trợ bởi GPU không máy chủ trên mạng lưới toàn cầu của Cloudflare."}, "cohere": {"description": "Cohere mang đến cho bạn các mô hình đa ngôn ngữ tiên tiến nhất, t<PERSON>h năng tìm kiếm hiện đại và không gian làm việc AI được thiết kế riêng cho các doanh nghiệp hiện đại - tất cả đều được tích hợp trong một nền tảng an toàn."}, "deepseek": {"description": "DeepSeek là một công ty tập trung vào nghiên cứu và ứng dụng công nghệ trí tuệ nhân tạo, mô hình mới nhất của họ, DeepSeek-V2.5, kế<PERSON> hợ<PERSON> khả năng đối thoại chung và xử lý mã, đồng thời đạt được sự cải thiện đáng kể trong việc căn chỉnh sở thích của con người, nhiệm vụ viết và tuân theo chỉ dẫn."}, "fal": {"description": "<PERSON><PERSON><PERSON> tảng tru<PERSON>ền thông tạo sinh dành cho nhà phát triển"}, "fireworksai": {"description": "Fireworks AI là nhà cung cấp dịch vụ mô hình ngôn ngữ cao cấp hàng đầu, tập trung vào gọi chức năng và xử lý đa phương thức. <PERSON>ô hình mới nhất củ<PERSON> họ, Firefunction V2, dựa trên Llama-3, đ<PERSON><PERSON><PERSON> tối ưu hóa cho gọi chức năng, đối thoại và tuân theo chỉ dẫn. Mô hình ngôn ngữ hình ảnh FireLLaVA-13B hỗ trợ đầu vào hỗn hợp hình ảnh và văn bản. <PERSON><PERSON><PERSON> mô hình đáng chú ý khác bao gồm dòng Llama và dòng Mixtral, cung cấp hỗ trợ cho việc tuân theo và tạo ra chỉ dẫn đa ngôn ngữ hiệu quả."}, "giteeai": {"description": "Serverless API của Gitee AI cung cấp các dịch vụ API lý luận mô hình lớn cho các nhà phát triển AI."}, "github": {"description": "Với GitHub Models, các nhà phát triển có thể trở thành kỹ sư AI và xây dựng với các mô hình AI hàng đầu trong ngành."}, "google": {"description": "Dòng Gemini của Google là mô hình AI tiên tiến và đa năng nhất của họ, <PERSON><PERSON><PERSON><PERSON> phát triển bởi Google DeepMind, đ<PERSON><PERSON><PERSON> thiết kế cho đa phư<PERSON><PERSON> thức, hỗ trợ hiểu và xử lý liền mạch văn bản, mã, hình ảnh, âm thanh và video. <PERSON><PERSON> hợp cho nhiều môi trường từ trung tâm dữ liệu đến thiết bị di động, nâng cao đáng kể hiệu quả và tính ứng dụng của mô hình AI."}, "groq": {"description": "Bộ máy suy diễn LPU của Groq thể hiện xuất sắc trong các bài kiểm tra chuẩn mô hình ngôn ngữ lớn (LLM) độc lập mới nh<PERSON>, định nghĩa lại tiêu chuẩn cho các giải pháp AI với tốc độ và hiệu quả đáng kinh ngạc. Groq là đại diện cho tốc độ suy diễn tứ<PERSON> thì, thể hiện hiệu suất tốt trong triển khai dựa trên đám mây."}, "higress": {"description": "Higress là một cổng API gốc đá<PERSON>, <PERSON><PERSON><PERSON><PERSON> phát triển trong nội bộ của Alibaba để giải quyết vấn đề Tengine reload ảnh hưởng đến các dịch vụ kết nối dài h<PERSON>n, cũ<PERSON> như khả năng cân bằng tải gRPC/Dubbo chưa đủ."}, "huggingface": {"description": "HuggingFace Inference API cung cấp một cách nhanh chóng và miễn phí để bạn khám phá hàng ngàn mô hình cho nhiều nhiệm vụ khác nhau. Dù bạn đang thiết kế nguyên mẫu cho một ứng dụng mới hay đang thử nghiệm khả năng của học máy, API này cho phép bạn truy cập ngay lập tức vào các mô hình hiệu suất cao trong nhiều lĩnh vực."}, "hunyuan": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn đượ<PERSON> phát triển bởi Tencent, c<PERSON> khả năng sáng tạo tiếng Trung mạnh mẽ, kh<PERSON> năng suy luận logic trong các ngữ cảnh phức tạp, và khả năng thực hiện nhiệm vụ đáng tin cậy."}, "infiniai": {"description": "<PERSON><PERSON> cấp dịch vụ mô hình lớn hi<PERSON><PERSON> su<PERSON>t cao, dễ sử dụng và an toàn cho nhà phát triển ứng dụng, bao gồm toàn bộ quy trình từ phát triển mô hình lớn đến triển khai dịch vụ mô hình lớn."}, "internlm": {"description": "Tổ chức mã nguồn mở chuyên nghiên cứu và phát triển công cụ cho mô hình lớn. <PERSON><PERSON> cấp nền tảng mã nguồn mở hiệ<PERSON> quả, d<PERSON> sử dụng cho tất cả các nhà phát triển AI, gi<PERSON><PERSON> tiếp cận công nghệ mô hình lớn và thuật toán tiên tiến nhất."}, "jina": {"description": "Jina AI được thành lập vào năm 2020, là một công ty hàng đầu trong lĩnh vực AI tìm kiếm. Nền tảng tìm kiếm của chúng tôi bao gồm các mô hình vector, bộ tái sắp xếp và các mô hình ngôn ngữ nhỏ, gi<PERSON><PERSON> các doanh nghiệp xây dựng các ứng dụng tìm kiếm sinh tạo và đa phương tiện đáng tin cậy và chất lượng cao."}, "lmstudio": {"description": "LM Studio là một ứng dụng máy tính để phát triển và thử nghiệm các LLM trên máy tính của bạn."}, "minimax": {"description": "MiniMax là công ty công nghệ trí tuệ nhân tạo tổng quát đư<PERSON><PERSON> thành lập vào năm 2021, cam kết cùng người dùng sáng tạo trí thông minh. MiniMax đã tự phát triển nhiều mô hình lớn đa phương thức, bao gồm mô hình văn bản MoE với một triệu tham số, mô hình giọng nói và mô hình hình ảnh. Họ cũng đã phát hành các ứng dụng như AI Hải Lý."}, "mistral": {"description": "Mistral cung cấp các mô hình tiên tiến cho mục đích chung, chuy<PERSON><PERSON> nghiệp và nghiên cứ<PERSON>, đ<PERSON><PERSON><PERSON> <PERSON>ng dụng rộng rãi trong suy diễn phứ<PERSON> tạp, nhi<PERSON><PERSON> vụ đa ngôn ngữ, t<PERSON><PERSON> mã, v.v. <PERSON>h<PERSON><PERSON> qua giao diện gọ<PERSON> chức năng, người dùng có thể tích hợp các chức năng tùy chỉnh để thực hiện các ứng dụng cụ thể."}, "modelscope": {"description": "ModelScope là nền tảng mô hình như một dịch vụ do Alibaba Cloud phát triển, cung cấp nhiều mô hình AI và dịch vụ suy luận phong phú."}, "moonshot": {"description": "Moonshot là nền tảng mã nguồn mở do Công ty TNHH Công nghệ Mặt Trăng Bắc <PERSON> ph<PERSON> hành, cung cấp nhiều mô hình xử lý ngôn ngữ tự nhiên, <PERSON>ng dụng rộng rãi trong nhiều lĩnh vực, bao gồm nhưng không giới hạn ở sáng tác nội dung, nghi<PERSON><PERSON> c<PERSON><PERSON> họ<PERSON> thu<PERSON>, g<PERSON><PERSON> <PERSON> thông minh, chẩn đoán y tế, v.v., hỗ trợ xử lý văn bản dài và nhiệm vụ tạo phức tạp."}, "novita": {"description": "Novita AI là một nền tảng cung cấp dịch vụ API cho nhiều mô hình ngôn ngữ lớn và tạo hình ảnh AI, <PERSON><PERSON><PERSON> <PERSON>, đáng tin cậy và hiệu quả về chi phí. Nó hỗ trợ các mô hình mã nguồn mở mới nhất như Llama3, <PERSON><PERSON><PERSON>, và cung cấp giải pháp API toàn diện, thân thiện với người dùng và tự động mở rộng cho phát triển ứng dụng AI, phù hợp cho sự phát triển nhanh chóng của các công ty khởi nghiệp AI."}, "nvidia": {"description": "NVIDIA NIM™ cung cấp các container có thể được sử dụng để tự lưu trữ các dịch vụ vi mô suy diễn GPU tăng tốc, hỗ trợ triển khai các mô hình AI đã được huấn luyện trước và tùy chỉnh trên đám mây, trung tâm dữ liệu, má<PERSON> tính cá nhân RTX™ AI và trạm làm việc."}, "ollama": {"description": "<PERSON><PERSON> hình do Ollama cung cấp bao quát rộng rãi các lĩnh vực như tạo mã, t<PERSON>h to<PERSON> to<PERSON>, xử lý đa ngôn ngữ và tương tác đ<PERSON> tho<PERSON>, hỗ trợ nhu cầu đa dạng cho triển khai doanh nghiệp và địa phương."}, "openai": {"description": "OpenAI là tổ chức nghiên cứu trí tuệ nhân tạo hàng đầu thế giới, với các mô hình như dòng GPT đã thúc đẩy ranh giới của xử lý ngôn ngữ tự nhiên. OpenAI cam kết thay đổi nhiều ngành công nghiệp thông qua các giải pháp AI sáng tạo và hiệu quả. Sản phẩm của họ có hiệu suất và tính kinh tế nổi bật, đư<PERSON><PERSON> sử dụng rộng rãi trong nghiên cứu, thương mại và ứng dụng đổi mới."}, "openrouter": {"description": "OpenRouter là một nền tảng dịch vụ cung cấp nhiều giao diện mô hình lớn tiên tiến, hỗ trợ OpenAI, Anthropic, LLaMA và nhiều hơn n<PERSON>a, phù hợp cho nhu cầu phát triển và ứng dụng đa dạng. Người dùng có thể linh hoạt chọn mô hình và giá cả tối ưu theo nhu cầu của mình, gi<PERSON><PERSON> nâng cao trải nghiệm AI."}, "perplexity": {"description": "Perplexity là nhà cung cấp mô hình tạo đối thoại hàng đầu, cung cấp nhiều mô hình Llama 3.1 ti<PERSON><PERSON> tiến, hỗ trợ ứng dụng trực tuyến và ngoại tuyến, đặc biệt phù hợp cho các nhiệm vụ xử lý ngôn ngữ tự nhiên phức tạp."}, "ppio": {"description": "PPIO派欧云 cung cấp dịch vụ API mô hình mã nguồn mở ổn định, hiệ<PERSON> quả chi phí cao, hỗ trợ toàn bộ dòng sản phẩm DeepSeek, <PERSON><PERSON><PERSON>, <PERSON><PERSON> và các mô hình lớn hàng đầu trong ngành."}, "qiniu": {"description": "<PERSON><PERSON> là nhà cung cấp dịch vụ cloud hàng đầu, cung cấp API cho các mô hình AI lớn, b<PERSON> <PERSON><PERSON><PERSON> DeepSeek, <PERSON><PERSON><PERSON> <PERSON>, v<PERSON><PERSON> các tùy chọn linh hoạt để xây dựng và áp dụng các ứng dụng AI."}, "qwen": {"description": "<PERSON><PERSON> là mô hình ngôn ngữ quy mô lớn tự phát triển của Alibaba Cloud, có khả năng hiểu và tạo ngôn ngữ tự nhiên mạnh mẽ. <PERSON><PERSON> có thể trả lời nhiều câu hỏi, s<PERSON><PERSON> tác nội dung văn bản, b<PERSON><PERSON> tỏ quan điểm, viế<PERSON> mã, v.v., hoạt động trong nhiều lĩnh vực."}, "sambanova": {"description": "SambaNova Cloud cho phép các nhà phát triển dễ dàng sử dụng các mô hình mã nguồn mở tốt nhất và tận hưởng tốc độ suy diễn nhanh nhất."}, "search1api": {"description": "Search1API cung cấp quyền truy cập vào các mô hình DeepSeek có thể tự kết nối theo nhu cầu, bao gồm phiên bản tiêu chuẩn và phiên bả<PERSON>, hỗ trợ lựa chọn mô hình với nhiều quy mô tham số khác nhau."}, "sensenova": {"description": "SenseTime luôn đổi mới, dựa vào nền tảng mạnh mẽ của SenseTime để cung cấp dịch vụ mô hình lớn toà<PERSON>, hi<PERSON><PERSON> quả và dễ sử dụng."}, "siliconcloud": {"description": "SiliconFlow cam kết tăng tốc AGI để mang lại lợi ích cho nhân loại, nâng cao hiệu quả AI quy mô lớn thông qua một ngăn xếp GenAI dễ sử dụng và chi phí thấp."}, "spark": {"description": "<PERSON><PERSON> hình lớn Xi<PERSON> của iFlytek cung cấp khả năng AI mạnh mẽ cho nhiều lĩnh vực và ngôn ngữ, sử dụng công nghệ xử lý ngôn ngữ tự nhiên tiên tiến để xây dựng các ứng dụng đổi mới phù hợp cho các lĩnh vực như phần cứng thông minh, y tế thông minh, tà<PERSON> ch<PERSON>h thông minh, v.v."}, "stepfun": {"description": "<PERSON><PERSON> hình lớn Star Class có khả năng đa phương thức và suy diễn phức tạp hàng đầu trong ngành, hỗ trợ hiểu văn bản siêu dài và chức năng tìm kiếm tự động mạnh mẽ."}, "taichu": {"description": "Vi<PERSON><PERSON>hiê<PERSON> cứu Tự động hóa Trung Quốc và Viện Nghiên cứu Trí tuệ Nhân tạo Vũ Hán đã phát hành mô hình lớn đa phương thức thế hệ mới, hỗ trợ các nhiệm vụ hỏi đáp toàn diện như hỏi đáp nhiều vòng, sáng tác văn bản, tạ<PERSON> hì<PERSON>, hiể<PERSON>, phân tích tín hiệu, v.v., với khả năng nhận thức, hiểu biết và sáng tác mạnh mẽ hơn, mang đến trải nghiệm tương tác hoàn toàn mới."}, "tencentcloud": {"description": "Năng lực nguyên tử của động cơ tri thức (LLM Knowledge Engine Atomic Power) đư<PERSON><PERSON> phát triển dựa trên động cơ tri thức, cung cấp khả năng hỏi đáp toàn diện cho doanh nghiệp và nhà phát triển, cho phép xây dựng và phát triển ứng dụng mô hình một cách linh hoạt. Bạn có thể sử dụng nhiều năng lực nguyên tử để tạo ra dịch vụ mô hình riêng của mình, kết hợp các dịch vụ như phân tích tài liệu, t<PERSON><PERSON> rời, embedding, và viết lại nhiều vòng để tùy chỉnh các dịch vụ AI đặc thù cho doanh nghiệp."}, "togetherai": {"description": "Together AI cam kết đạt được hiệu suất hàng đầu thông qua các mô hình AI sán<PERSON> tạ<PERSON>, cung cấp khả năng tùy chỉnh rộng rãi, bao gồm hỗ trợ mở rộng nhanh chóng và quy trình triển khai trực quan, đ<PERSON><PERSON> <PERSON><PERSON> nhiều nhu cầu của doanh nghiệp."}, "upstage": {"description": "Upstage tập trung vào việc phát triển các mô hình AI cho nhiều nhu cầu thương mại kh<PERSON><PERSON>hau, bao <PERSON><PERSON>m Solar LLM và AI tài li<PERSON>, nhằm đạt được trí thông minh nhân tạo tổng quát (AGI) cho công việc. Tạo ra các đại lý đối thoại đơn giản thông qua Chat API, và hỗ trợ gọi chức năng, dị<PERSON> thu<PERSON><PERSON>, nhúng và ứng dụng trong các lĩnh vực cụ thể."}, "v0": {"description": "v0 là một trợ lý lập trình theo cặp, bạn chỉ cần mô tả ý tưởng bằng ngôn ngữ tự nhiên, nó sẽ tạo mã và giao diện người dùng (UI) cho dự án của bạn"}, "vertexai": {"description": "Dòng sản phẩm Gemini của Google là mô hình AI tiên tiến và đa năng nhất củ<PERSON> họ, đ<PERSON><PERSON><PERSON> phát triển bởi Google DeepMind, đư<PERSON><PERSON> thiết kế đặc biệt cho đa phư<PERSON><PERSON> thức, hỗ trợ hiểu và xử lý liền mạch văn bản, mã, hình ảnh, âm thanh và video. Phù hợp với nhiều môi trường từ trung tâm dữ liệu đến thiết bị di động, nâng cao đáng kể hiệu quả và tính ứng dụng của mô hình AI."}, "vllm": {"description": "vLLM là một thư viện n<PERSON>h chóng và dễ sử dụng cho suy diễn và dịch vụ LLM."}, "volcengine": {"description": "Nền tảng phát triển dịch vụ mô hình lớn do ByteDance phát triển, cung cấp dịch vụ gọi mô hình phong phú, an toàn và có giá cả cạnh tranh, đồng thời cung cấp dữ liệu mô hình, <PERSON><PERSON> chỉnh, <PERSON><PERSON> <PERSON>, đ<PERSON><PERSON> giá và các chức năng đầu cuố<PERSON>, đả<PERSON> bảo toàn diện cho việc phát triển ứng dụng AI của bạn."}, "wenxin": {"description": "Nền tảng phát triển và dịch vụ ứng dụng AI gốc với mô hình lớn một cửa dành cho doanh nghiệp, cung cấp chuỗi công cụ toàn diện và dễ sử dụng cho phát triển mô hình trí tuệ nhân tạo sinh sinh và phát triển ứng dụng."}, "xai": {"description": "xAI là một công ty cam kết xây dựng trí tuệ nhân tạo để tăng tốc khám phá khoa học của nhân loại. Sứ mệnh của chúng tôi là thúc đẩy sự hiểu biết chung của chúng ta về vũ trụ."}, "xinference": {"description": "Xorbits Inference (Xinference) là một nền tảng mã nguồn mở, đ<PERSON><PERSON><PERSON> thiết kế để đơn giản hóa việc chạy và tích hợp các mô hình AI khác nhau. Với <PERSON>n<PERSON>, bạn có thể chạy suy luận trên bất kỳ mô hình LLM mã nguồn mở, mô hình nhúng và mô hình đa phương thức nào trong môi trường đám mây hoặc cục bộ, và tạo ra các ứng dụng AI mạnh mẽ."}, "zeroone": {"description": "01.AI tập trung vào công nghệ trí tuệ nhân tạo trong kỷ nguyên AI 2.0, thúc đẩy mạnh mẽ sự đổi mới và ứng dụng của \"người + trí tuệ nhân tạo\", sử dụng các mô hình mạnh mẽ và công nghệ AI tiên tiến để nâng cao năng suất của con người và thực hiện sự trao quyền công nghệ."}, "zhipu": {"description": "Zhipu AI cung cấp nền tảng mở cho mô hình đa phương thức và ngôn ngữ, hỗ trợ nhiều tình huống ứng dụng AI, bao gồm xử lý văn bản, hi<PERSON><PERSON> hình ảnh và hỗ trợ lập trình."}}