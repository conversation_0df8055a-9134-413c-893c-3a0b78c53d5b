{"azure": {"azureApiVersion": {"desc": "Phiên bản API của Azure, tuân theo định dạng YYYY-MM-DD, tham khảo [phiên bản mới nhất](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> danh s<PERSON>ch", "title": "Phiên bản API Azure"}, "empty": "<PERSON><PERSON> lòng nhập ID mô hình để thêm mô hình đầu tiên", "endpoint": {"desc": "<PERSON>ểm tra tài nguyên từ cổng Azure, bạn có thể tìm thấy giá trị này trong phần 'Kh<PERSON>a và điểm cuối'", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Địa chỉ API Azure"}, "modelListPlaceholder": "Chọn hoặc thêm mô hình OpenAI bạn đã triển khai", "title": "Azure OpenAI", "token": {"desc": "<PERSON><PERSON>m tra tài nguyên từ cổng Azure, bạn có thể tìm thấy giá trị này trong phần 'Khóa và điểm cuối'. <PERSON><PERSON> thể sử dụng KEY1 hoặc KEY2", "placeholder": "Azure API Key", "title": "API Key"}}, "azureai": {"azureApiVersion": {"desc": "Phiên bản API của Azure, theo định dạng YYYY-MM-DD, tham khảo [phiên bản mới nhất](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> danh s<PERSON>ch", "title": "Phiên bản API Azure"}, "endpoint": {"desc": "Tìm điểm kết thúc suy diễn mô hình Azure AI từ tổng quan dự án Azure AI", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "<PERSON><PERSON><PERSON><PERSON> kết thúc Azure AI"}, "title": "Azure OpenAI", "token": {"desc": "T<PERSON>m kh<PERSON>a API từ tổng quan dự án Azure AI", "placeholder": "Khóa Azure", "title": "Khóa"}}, "bedrock": {"accessKeyId": {"desc": "Nhập AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "<PERSON><PERSON><PERSON> tra <PERSON>eyId / SecretAccessKey có đư<PERSON><PERSON> nhập ch<PERSON>h xác không"}, "region": {"desc": "Nhập AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Nhập AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "<PERSON><PERSON><PERSON> bạn đang sử dụng AWS SSO/STS, h<PERSON><PERSON> nh<PERSON>p AWS Session Token của bạn", "placeholder": "AWS Session Token", "title": "AWS Session Token (t<PERSON><PERSON>)"}, "title": "Bedrock", "unlock": {"customRegion": "<PERSON><PERSON><PERSON> v<PERSON> chỉnh", "customSessionToken": "<PERSON>ã thông báo phiên tùy chỉnh", "description": "Nhập AWS AccessKeyId / SecretAccessKey của bạn để bắt đầu phiên làm việc. Ứng dụng sẽ không lưu trữ cấu hình xác thực của bạn", "imageGenerationDescription": "Nhập AWS AccessKeyId / SecretAccessKey của bạn để bắt đầu tạo. Ứng dụng sẽ không lưu trữ cấu hình xác thực của bạn", "title": "Sử dụng Thông tin <PERSON>c thực <PERSON>rock tùy chỉnh"}}, "cloudflare": {"apiKey": {"desc": "<PERSON><PERSON> lòng nhập Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Nhập ID tài k<PERSON>n Cloudflare hoặc địa chỉ API tùy chỉnh", "placeholder": "ID tài k<PERSON>n Cloudflare / địa chỉ API tùy chỉnh", "title": "ID tài k<PERSON>n Cloudflare / địa chỉ API"}}, "createNewAiProvider": {"apiKey": {"placeholder": "<PERSON><PERSON> lòng nhập API Key của bạn", "title": "API Key"}, "basicTitle": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "configTitle": "<PERSON>h<PERSON>ng tin cấu hình", "confirm": "<PERSON><PERSON><PERSON> mới", "createSuccess": "<PERSON><PERSON><PERSON> mới thành công", "description": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> thiệu về nhà cung cấp (t<PERSON><PERSON> ch<PERSON>)", "title": "<PERSON><PERSON><PERSON><PERSON> thiệu về nhà cung cấp"}, "id": {"desc": "<PERSON><PERSON> định danh duy nhất của nhà cung cấp dị<PERSON> vụ, không thể sửa đổi sau khi tạo", "format": "Chỉ có thể chứa số, chữ c<PERSON><PERSON> thư<PERSON>, dấu gạch ngang (-) và dấu gạch dưới (_) ", "placeholder": "<PERSON><PERSON><PERSON> viết toàn bộ bằng chữ thường, ví dụ <PERSON><PERSON>, kh<PERSON><PERSON> thể sửa sau khi tạo", "required": "<PERSON><PERSON> lòng nhập ID nhà cung cấp", "title": "ID nhà cung cấp"}, "logo": {"required": "<PERSON><PERSON> lòng tải lên <PERSON> nhà cung cấp hợp lệ", "title": "Logo nhà cung cấp"}, "name": {"placeholder": "<PERSON><PERSON> lòng nhập tên hiển thị của nhà cung cấp", "required": "<PERSON><PERSON> lòng nhập tên nhà cung cấp", "title": "<PERSON><PERSON><PERSON> nhà cung cấp"}, "proxyUrl": {"required": "<PERSON><PERSON> lòng nh<PERSON>p địa chỉ proxy", "title": "Địa chỉ proxy"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "<PERSON><PERSON> lòng ch<PERSON>n lo<PERSON>i SDK", "title": "<PERSON><PERSON><PERSON> dạng yêu cầu"}, "title": "T<PERSON><PERSON> nhà cung cấp AI tùy chỉnh"}, "github": {"personalAccessToken": {"desc": "Nhập mã truy cập cá nhân G<PERSON>ub của bạn, nhấp vào [đây](https://github.com/settings/tokens) để tạo", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "<PERSON>h<PERSON><PERSON> mã thông báo <PERSON><PERSON> của bạn, nhấp vào [đây](https://huggingface.co/settings/tokens) để tạo", "placeholder": "hf_xxxxxxxxx", "title": "<PERSON><PERSON> thông b<PERSON>o <PERSON>"}}, "list": {"title": {"disabled": "<PERSON><PERSON><PERSON> cung cấp ch<PERSON>a đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>t", "enabled": "<PERSON><PERSON><PERSON> cung cấp đã đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>t"}}, "menu": {"addCustomProvider": "Thê<PERSON> nhà cung cấp tùy chỉnh", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "list": {"disabled": "<PERSON><PERSON><PERSON> k<PERSON>", "enabled": "Đ<PERSON> kích ho<PERSON>"}, "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả tìm kiếm", "searchProviders": "<PERSON><PERSON><PERSON> kiếm nhà cung cấp...", "sort": "<PERSON><PERSON><PERSON> xếp tùy chỉnh"}, "ollama": {"checker": {"desc": "<PERSON><PERSON><PERSON> tra địa chỉ proxy c<PERSON> đư<PERSON><PERSON> nhập ch<PERSON>h xác không", "title": "<PERSON><PERSON><PERSON> tra t<PERSON>h liên thông"}, "customModelName": {"desc": "Thêm mô hình tùy chỉnh, sử dụng dấu phẩy (,) để tách biệt nhiều mô hình", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Tên mô hình tùy chỉnh"}, "download": {"desc": "Ollama đang tải xuống mô hình này, vui lòng không đóng trang này. Qu<PERSON> trình tải xuống sẽ tiếp tục từ nơi đã bị gián đoạn khi tải lại", "failed": "<PERSON><PERSON><PERSON> mô hình không thành công, vui lòng kiểm tra kết nối mạng hoặc cài đặt Ollama và thử lại", "remainingTime": "<PERSON><PERSON><PERSON><PERSON> gian còn lại", "speed": "<PERSON><PERSON><PERSON> độ tải xuống", "title": "<PERSON><PERSON> tải mô hình {{model}}"}, "endpoint": {"desc": "<PERSON><PERSON>i bao gồm http(s)://, có thể để trống nếu không chỉ định thêm cho địa phương", "title": "Địa chỉ proxy API"}, "title": "Ollama", "unlock": {"cancel": "<PERSON><PERSON><PERSON> t<PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> nhãn mô hình <PERSON> của bạn để tiếp tục phiên làm việc", "downloaded": "{{completed}} / {{total}}", "starting": "<PERSON><PERSON><PERSON> đầu tải xuống...", "title": "<PERSON><PERSON><PERSON> xuống mô hình <PERSON>llama đã chỉ định"}}, "providerModels": {"config": {"aesGcm": "<PERSON><PERSON><PERSON><PERSON> của bạn và địa chỉ proxy sẽ được mã hóa bằng thuật toán <1>AES-GCM</1>", "apiKey": {"desc": "<PERSON><PERSON> lòng nhập {{name}} API Key của bạn", "descWithUrl": "<PERSON><PERSON> lòng nhập {{name}} API Key của bạn, <3>nhấn vào đây để lấy</3>", "placeholder": "{{name}} API Key", "title": "API Key"}, "baseURL": {"desc": "<PERSON><PERSON><PERSON> bao gồm http(s)://", "invalid": "<PERSON><PERSON> lòng nhậ<PERSON> một URL hợp lệ", "placeholder": "https://your-proxy-url.com/v1", "title": "Địa chỉ proxy API"}, "checker": {"button": "<PERSON><PERSON><PERSON> tra", "desc": "<PERSON><PERSON>m tra xem API Key và địa chỉ proxy có đư<PERSON><PERSON> nhập đúng không", "pass": "<PERSON><PERSON><PERSON> tra thành công", "title": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i"}, "fetchOnClient": {"desc": "Chế độ yêu cầu từ khách hàng sẽ phát động yêu cầu phiên trực tiếp từ trình du<PERSON>, có thể cải thiện tốc độ phản hồi", "title": "Sử dụng chế độ yêu cầu từ khách hàng"}, "helpDoc": "H<PERSON>ớng dẫn cấu hình", "responsesApi": {"desc": "Sử dụng định dạng yêu cầu thế hệ mới của OpenAI, mở khóa các tính năng nâng cao như chuỗi suy nghĩ", "title": "Sử dụng chuẩn Responses API"}, "waitingForMore": "<PERSON><PERSON><PERSON><PERSON> mô hình hơn đang <1><PERSON><PERSON><PERSON><PERSON> lên kế hoạch</1>, xin hãy chờ đợi"}, "createNew": {"title": "<PERSON><PERSON>o mô hình AI tùy chỉnh"}, "item": {"config": "<PERSON><PERSON><PERSON> hình mô hình", "customModelCards": {"addNew": "<PERSON><PERSON><PERSON> và thêm mô hình {{id}}", "confirmDelete": "Sắ<PERSON> xóa mô hình tùy chỉnh này, sau khi xóa sẽ không thể khôi phục, xin hãy cẩn thận."}, "delete": {"confirm": "<PERSON><PERSON><PERSON> nhận xóa mô hình {{displayName}}?", "success": "<PERSON><PERSON><PERSON> thành công", "title": "<PERSON><PERSON><PERSON> mô hình"}, "modelConfig": {"azureDeployName": {"extra": "Tr<PERSON><PERSON><PERSON> thực tế được yêu cầu trong Azure OpenAI", "placeholder": "<PERSON><PERSON> lòng nhập tên triển khai mô hình trong Azure", "title": "<PERSON>ên triển khai mô hình"}, "deployName": {"extra": "Trường này sẽ được sử dụng làm ID mô hình khi gửi yêu cầu", "placeholder": "<PERSON><PERSON> lòng nhập tên hoặc ID thực tế của mô hình đã triển khai", "title": "<PERSON>ên triển khai mô hình"}, "displayName": {"placeholder": "<PERSON><PERSON> lòng nhập tên hiển thị của mô hình, ví d<PERSON> ChatGPT, GPT-4, v.v.", "title": "<PERSON><PERSON><PERSON> hiển thị mô hình"}, "files": {"extra": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>, vi<PERSON><PERSON> tải lên tệp chỉ là một gi<PERSON>i ph<PERSON><PERSON>, chỉ dành cho thử nghiệm cá nhân. <PERSON>ui lòng chờ đợi khả năng tải lên tệp hoàn chỉnh trong các bản cập nhật sau.", "title": "Hỗ trợ tải lên tệp"}, "functionCall": {"extra": "C<PERSON>u hình này chỉ kích hoạt khả năng sử dụng công cụ của mô hình, từ đó có thể thêm các plugin loại công cụ cho mô hình. <PERSON><PERSON>, việc hỗ trợ sử dụng công cụ thực sự hoàn toàn phụ thuộc vào chính mô hình, vui lòng tự kiểm tra tính khả dụng", "title": "Hỗ trợ sử dụng công cụ"}, "id": {"extra": "<PERSON><PERSON><PERSON><PERSON> thể sửa đổi sau khi tạo, sẽ được sử dụng làm id mô hình khi gọi AI", "placeholder": "<PERSON><PERSON> lòng nhập id mô hình, ví dụ gpt-4o hoặc claude-3.5-sonnet", "title": "ID mô hình"}, "modalTitle": "<PERSON><PERSON>u hình mô hình tùy chỉnh", "reasoning": {"extra": "Cấu hình này sẽ chỉ kích hoạt khả năng suy nghĩ sâu của mô hình, hiệu quả cụ thể hoàn toàn phụ thuộc vào ch<PERSON>h mô hình, vui lòng tự kiểm tra xem mô hình này có khả năng suy nghĩ sâu có thể sử dụng hay không", "title": "Hỗ trợ suy nghĩ sâu"}, "tokens": {"extra": "Cài đặt số Token tối đa mà mô hình hỗ trợ", "title": "<PERSON><PERSON><PERSON> sổ ngữ cảnh tối đa", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn"}, "vision": {"extra": "Cấu hình này chỉ mở khả năng tải lên hình ảnh trong ứng dụng, việc hỗ trợ nhận diện hoàn toàn phụ thuộc vào mô hình, xin hãy tự kiểm tra khả năng nhận diện hình ảnh của mô hình này.", "title": "Hỗ trợ nhận diện hình ảnh"}}, "pricing": {"image": "${{amount}}/Hình <PERSON>nh", "inputCharts": "${{amount}}/Ký tự M", "inputMinutes": "${{amount}}/Phút", "inputTokens": "Nhập ${{amount}}/M", "outputTokens": "Xuất ${{amount}}/M"}, "releasedAt": "<PERSON><PERSON><PERSON> hành vào {{releasedAt}}"}, "list": {"addNew": "<PERSON><PERSON><PERSON><PERSON> mô hình", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "disabledActions": {"showMore": "<PERSON><PERSON><PERSON> thị tất cả"}, "empty": {"desc": "<PERSON><PERSON> lòng tạo mô hình tùy chỉnh hoặc kéo mô hình để bắt đầu sử dụng", "title": "Chưa có mô hình nào khả dụng"}, "enabled": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> k<PERSON>", "enabledActions": {"disableAll": "<PERSON><PERSON> hi<PERSON>u hóa tất cả", "enableAll": "<PERSON><PERSON><PERSON> ho<PERSON> tất cả", "sort": "<PERSON><PERSON><PERSON> xếp mô hình tùy chỉnh"}, "enabledEmpty": "Ch<PERSON>a có mô hình nào đ<PERSON><PERSON><PERSON> kích hoạt, hãy kích hoạt mô hình bạn yêu thích từ danh sách bên dư<PERSON>i nhé~", "fetcher": {"clear": "<PERSON><PERSON><PERSON> mô hình đã lấy", "fetch": "<PERSON><PERSON><PERSON> danh sách mô hình", "fetching": "<PERSON><PERSON> l<PERSON>y danh sách mô hình...", "latestTime": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON>t lần cuối: {{time}}", "noLatestTime": "<PERSON><PERSON><PERSON> l<PERSON> danh s<PERSON>ch"}, "resetAll": {"conform": "<PERSON><PERSON><PERSON> nhận việc đặt lại tất cả các thay đổi của mô hình hiện tại? <PERSON>u khi đặt lại, danh sách mô hình hiện tại sẽ trở về trạng thái mặc định", "success": "Đặt lại thành công", "title": "Đặt lại tất cả các thay đổi"}, "search": "<PERSON><PERSON>m kiếm mô hình...", "searchResult": "<PERSON><PERSON><PERSON> thấy {{count}} mô hình", "title": "<PERSON><PERSON> s<PERSON>ch mô hình", "total": "<PERSON><PERSON> tổng cộng {{count}} mô hình khả dụng"}, "searchNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả tìm kiếm"}, "sortModal": {"success": "<PERSON><PERSON><PERSON> nhật sắp xếp thành công", "title": "<PERSON><PERSON><PERSON> xếp tùy chỉnh", "update": "<PERSON><PERSON><PERSON>"}, "updateAiProvider": {"confirmDelete": "Sắp xóa nhà cung cấp <PERSON> nà<PERSON>, sau khi xóa sẽ không thể khôi phục, xác nhận có xóa không?", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "tooltip": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu hình cơ bản của nhà cung cấp", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công"}, "updateCustomAiProvider": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu hình nhà cung cấp AI tùy chỉnh"}, "vertexai": {"apiKey": {"desc": "Nhập khóa Vertex AI của bạn", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Khóa Vertex AI"}}, "zeroone": {"title": "01.AI Zero One"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}