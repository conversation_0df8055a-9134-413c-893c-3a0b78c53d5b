{"01-ai/yi-1.5-34b-chat": {"description": "Zero One Vạn <PERSON>, mô hình tinh chỉnh mã nguồn mở mới nhất với 34 tỷ tham số, hỗ trợ nhiều tình huống đối thoại, dữ liệu đào tạo chất l<PERSON><PERSON> cao, phù hợp với sở thích của con người."}, "01-ai/yi-1.5-9b-chat": {"description": "Zero One Vạn <PERSON>, mô hình tinh chỉnh mã nguồn mở mới nhất với 9 tỷ tham số, hỗ trợ nhiều tình huống đối thoại, dữ liệu đào tạo chất l<PERSON><PERSON> cao, phù hợp với sở thích của con người."}, "360/deepseek-r1": {"description": "【Phiên bản triển khai 360】DeepSeek-R1 đã sử dụng công nghệ học tăng cường quy mô lớn trong giai đoạn huấn luyện sau, nâng cao khả năng suy luận của mô hình một cách đáng kể với rất ít dữ liệu được gán nhãn. Hiệu suất trong các nhiệm vụ to<PERSON> họ<PERSON>, mã, suy luận ngôn ngữ tự nhiên tương đương với phiên bản chính thức OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro là thành viên quan trọng trong dòng mô hình AI của 360, đ<PERSON><PERSON> <PERSON><PERSON> nhu cầu đa dạng của các ứng dụng ngôn ngữ tự nhiên với khả năng xử lý văn bản hiệu quả, hỗ trợ hiểu văn bản dài và đối thoại nhiều vòng."}, "360gpt-pro-trans": {"description": "<PERSON><PERSON> hình chuyên dụng cho dịch thuật, đ<PERSON><PERSON><PERSON> tối ưu hóa bằng cách tinh chỉnh sâu, mang lại hiệu quả dịch thuật hàng đầu."}, "360gpt-turbo": {"description": "360GPT Turbo cung cấp khả năng tính toán và đối thoại mạnh mẽ, có khả năng hiểu ngữ nghĩa và hiệu suất tạo ra xuất sắc, là gi<PERSON>i pháp trợ lý thông minh lý tưởng cho doanh nghiệp và nhà phát triển."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K nhấn mạnh an toàn ngữ nghĩa và định hướng trách nhiệm, <PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt cho các tình huống ứng dụng có yêu cầu cao về an toàn nội dung, đ<PERSON><PERSON> bảo độ chính xác và độ ổn định trong trải nghiệm người dùng."}, "360gpt2-o1": {"description": "360gpt2-o1 sử dụng tìm kiếm cây để xây dựng chuỗi tư duy, và đưa vào cơ chế phản hồi, sử dụng học tăng cường để đào tạo, mô hình có khả năng tự phản hồi và sửa lỗi."}, "360gpt2-pro": {"description": "360GPT2 Pro là mô hình xử lý ngôn ngữ tự nhiên cao cấp do công ty 360 phát hành, có khả năng tạo và hiểu văn bản xuất sắc, đặc biệt trong lĩnh vực tạo ra và sáng tạo, có thể xử lý các nhiệm vụ chuyển đổi ngôn ngữ phức tạp và diễn xuất vai trò."}, "360zhinao2-o1": {"description": "360zhinao2-o1 sử dụng tìm kiếm cây để xây dựng chuỗi tư duy, và giới thiệu cơ chế phản hồi, sử dụng học tăng cường để đào tạo, mô hình có khả năng tự phản hồi và sửa lỗi."}, "4.0Ultra": {"description": "Spark4.0 Ultra là phiên bản mạnh mẽ nhất trong dòng mô hình lớn Xinghuo, nâng cao khả năng hiểu và tóm tắt nội dung văn bản trong khi nâng cấp liên kết tìm kiếm trực tuyến. Đ<PERSON>y là giải pháp toàn diện nhằm nâng cao năng suất văn phòng và đáp ứng chính xác nhu cầu, là sản phẩm thông minh dẫn đầu ngành."}, "AnimeSharp": {"description": "AnimeSharp (còn gọi là “4x‑AnimeSharp”) là mô hình siêu phân giải mã nguồn mở do Kim2091 phát triển dựa trên kiến trúc ESRGAN, tập trung vào phóng to và làm sắc nét hình ảnh phong cách anime. Nó được đổi tên từ “4x-TextSharpV1” vào tháng 2 năm 2022, ban đầu cũng phù hợp với hình ảnh văn bản nhưng đã được tối ưu đáng kể cho nội dung anime."}, "Baichuan2-Turbo": {"description": "Sử dụng công nghệ tăng cường tìm kiếm để kết nối toàn diện giữa mô hình lớn và kiến thức lĩnh vực, kiến thức toàn cầu. Hỗ trợ tải lên nhiều loại tài liệu như PDF, Word và nhập URL, thông tin được thu thập kịp thời và toàn diện, kết quả đầu ra chính xác và chuyên nghiệp."}, "Baichuan3-Turbo": {"description": "Tối ưu hóa cho các tình huống doanh nghiệp thư<PERSON><PERSON>, hiệu quả đượ<PERSON> cải thiện đáng kể, chi phí hiệu quả cao. So với mô hình Baichuan2, sáng tạo nội dung tăng 20%, tr<PERSON> lời câu hỏi kiến thức tăng 17%, khả năng đóng vai tăng 40%. Hiệu quả tổng thể tốt hơn GPT3.5."}, "Baichuan3-Turbo-128k": {"description": "<PERSON><PERSON> cửa sổ ngữ cảnh siêu dài 128K, tối ưu hóa cho các tình huống doanh nghiệp thư<PERSON><PERSON> xuyê<PERSON>, hiệu quả được cải thiện đáng kể, chi phí hiệu quả cao. So với mô hình Baichuan2, sáng tạo nội dung tăng 20%, tr<PERSON> lời câu hỏi kiến thức tăng 17%, khả năng đóng vai tăng 40%. Hiệu quả tổng thể tốt hơn GPT3.5."}, "Baichuan4": {"description": "<PERSON><PERSON> hình có khả năng hàng đầu trong nước, v<PERSON><PERSON><PERSON> tr<PERSON>i hơn các mô hình chính thống nước ngoài trong các nhiệm vụ tiếng Trung như bách khoa to<PERSON><PERSON> thư, vă<PERSON> b<PERSON><PERSON> dà<PERSON>, s<PERSON><PERSON> tạo nội dung. <PERSON><PERSON><PERSON> có khả năng đa phương tiện hàng đầu trong ngành, thể hiện xuất sắc trong nhiều tiêu chuẩn đánh giá uy tín."}, "Baichuan4-Air": {"description": "<PERSON><PERSON> hình có khả năng hàng đầu trong nước, v<PERSON><PERSON><PERSON> trội hơn các mô hình chính thống nước ngoài trong các nhiệm vụ tiếng Trung như bách khoa toàn thư, văn bản dài và sáng tạo nội dung. Cũng có khả năng đa phương tiện hàng đầu trong ngành, thể hiện xuất sắc trong nhiều tiêu chuẩn đánh giá uy tín."}, "Baichuan4-Turbo": {"description": "<PERSON><PERSON> hình có khả năng hàng đầu trong nước, v<PERSON><PERSON><PERSON> trội hơn các mô hình chính thống nước ngoài trong các nhiệm vụ tiếng Trung như bách khoa toàn thư, văn bản dài và sáng tạo nội dung. Cũng có khả năng đa phương tiện hàng đầu trong ngành, thể hiện xuất sắc trong nhiều tiêu chuẩn đánh giá uy tín."}, "DeepSeek-R1": {"description": "<PERSON><PERSON>ình LLM hiệu quả tiên tiến nhất, xu<PERSON><PERSON> s<PERSON><PERSON> trong suy luận, to<PERSON> học và lập trình."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 - mô hình lớn hơn và thông minh hơn trong bộ công cụ DeepSeek - đ<PERSON> đ<PERSON><PERSON><PERSON> chưng cất vào kiến trúc Llama 70B. Dựa trên các bài kiểm tra và đánh giá của con ng<PERSON><PERSON>i, mô hình này thông minh hơn so với Llama 70B gốc, đặc biệt thể hiện xuất sắc trong các nhiệm vụ yêu cầu độ chính xác về toán học và sự thật."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "<PERSON>ô hình chưng cất DeepSeek-R1 dựa trên Qwen2.5-Math-1.5B, tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "<PERSON>ô hình chưng cất DeepSeek-R1 dựa trên Qwen2.5-14B, tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "Dòng DeepSeek-R1 tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm, v<PERSON><PERSON><PERSON> qua mức OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "<PERSON>ô hình chưng cất DeepSeek-R1 dựa trên Qwen2.5-Math-7B, tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm."}, "DeepSeek-V3": {"description": "DeepSeek-V3 là một mô hình MoE do công ty DeepSeek tự phát triển. <PERSON><PERSON><PERSON>u kết quả đánh giá của DeepSeek-V3 đã vượt qua các mô hình mã nguồn mở khác như Qwen2.5-72B và Llama-3.1-405B, và về hiệu suất không thua kém các mô hình đóng nguồn hàng đầu thế giới như GPT-4o và Claude-3.5-Son<PERSON>."}, "Doubao-lite-128k": {"description": "Doubao-lite sở hữu tốc độ phản hồi tối ưu, hi<PERSON><PERSON> quả chi phí tốt hơn, cung cấp lựa chọn linh hoạt hơn cho các kịch bản khác nhau của khách hàng. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite sở hữu tốc độ phản hồi tối ưu, hi<PERSON><PERSON> quả chi phí tốt hơn, cung cấp lựa chọn linh hoạt hơn cho các kịch bản khác nhau của khách hàng. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite sở hữu tốc độ phản hồi tối ưu, hi<PERSON><PERSON> quả chi phí tốt hơn, cung cấp lựa chọn linh hoạt hơn cho các kịch bản khác nhau của khách hàng. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 4k."}, "Doubao-pro-128k": {"description": "<PERSON><PERSON> hình chủ lực với hiệu quả tốt nhất, phù hợp xử lý các nhiệm vụ phức tạp, có hiệu quả xuất sắc trong các kịch bản như hỏi đáp tham <PERSON>, t<PERSON><PERSON>, s<PERSON><PERSON>, phân lo<PERSON> vă<PERSON> bả<PERSON>, nh<PERSON><PERSON> vai. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 128k."}, "Doubao-pro-32k": {"description": "<PERSON><PERSON> hình chủ lực với hiệu quả tốt nhất, phù hợp xử lý các nhiệm vụ phức tạp, có hiệu quả xuất sắc trong các kịch bản như hỏi đáp tham <PERSON>, t<PERSON><PERSON>, s<PERSON><PERSON>, phân lo<PERSON> vă<PERSON> bả<PERSON>, nh<PERSON><PERSON> vai. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 32k."}, "Doubao-pro-4k": {"description": "<PERSON><PERSON> hình chủ lực với hiệu quả tốt nhất, phù hợp xử lý các nhiệm vụ phức tạp, có hiệu quả xuất sắc trong các kịch bản như hỏi đáp tham <PERSON>, t<PERSON><PERSON>, s<PERSON><PERSON>, phân lo<PERSON> vă<PERSON> bả<PERSON>, nh<PERSON><PERSON> vai. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 4k."}, "DreamO": {"description": "DreamO là mô hình tạo hình ảnh tùy chỉnh mã nguồn mở do ByteDance và Đại học Bắc Kinh hợp tác phát triển, nhằm hỗ trợ tạo hình ảnh đa nhiệm thông qua kiến trúc thống nhất. <PERSON><PERSON> sử dụng phương pháp mô hình hóa kết hợp hiệu quả, có thể tạo ra hình ảnh nhất quán và tùy chỉnh cao dựa trên các điều kiện như danh tính, chủ thể, phong cách, nền do người dùng chỉ định."}, "ERNIE-3.5-128K": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn hàng đầu do Baidu tự phát triển, bao phủ một lượng lớn tài liệu tiếng Trung và tiếng <PERSON>, c<PERSON> khả năng tổng quát mạnh mẽ, có thể đáp ứng hầu hết các yêu cầu về đối thoại, hỏi đáp, sáng tạo nội dung và các tình huống ứng dụng plugin; hỗ trợ tự động kết nối với plugin tìm kiếm c<PERSON><PERSON>, đảm bảo thông tin hỏi đáp luôn được cập nhật kịp thời."}, "ERNIE-3.5-8K": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn hàng đầu do Baidu tự phát triển, bao phủ một lượng lớn tài liệu tiếng Trung và tiếng <PERSON>, c<PERSON> khả năng tổng quát mạnh mẽ, có thể đáp ứng hầu hết các yêu cầu về đối thoại, hỏi đáp, sáng tạo nội dung và các tình huống ứng dụng plugin; hỗ trợ tự động kết nối với plugin tìm kiếm c<PERSON><PERSON>, đảm bảo thông tin hỏi đáp luôn được cập nhật kịp thời."}, "ERNIE-3.5-8K-Preview": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn hàng đầu do Baidu tự phát triển, bao phủ một lượng lớn tài liệu tiếng Trung và tiếng <PERSON>, c<PERSON> khả năng tổng quát mạnh mẽ, có thể đáp ứng hầu hết các yêu cầu về đối thoại, hỏi đáp, sáng tạo nội dung và các tình huống ứng dụng plugin; hỗ trợ tự động kết nối với plugin tìm kiếm c<PERSON><PERSON>, đảm bảo thông tin hỏi đáp luôn được cập nhật kịp thời."}, "ERNIE-4.0-8K-Latest": {"description": "Mô hình ngôn ngữ quy mô siêu lớn hàng đầu do Baidu tự phát triển, so với ERNIE 3.5 đã nâng cấp toàn diện khả năng của mô hình, phù hợp rộng rãi với các nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm <PERSON>, đảm bảo thông tin hỏi đáp luôn cập nhật."}, "ERNIE-4.0-8K-Preview": {"description": "Mô hình ngôn ngữ quy mô siêu lớn hàng đầu do Baidu tự phát triển, so với ERNIE 3.5 đã nâng cấp toàn diện khả năng của mô hình, phù hợp rộng rãi với các nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm <PERSON>, đảm bảo thông tin hỏi đáp luôn cập nhật."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "<PERSON>ô hình ngôn ngữ quy mô siêu lớn tự phát triển củ<PERSON>, có hiệu suất tổng thể xuất sắc, phù hợp rộng rãi cho các tình huống tác vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm của <PERSON>, đảm bảo tính kịp thời của thông tin câu hỏi đáp. So với ERNIE 4.0, nó có hiệu suất tốt hơn."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "<PERSON>ô hình ngôn ngữ quy mô siêu lớn hàng đầu do Baidu tự phát triển, có hiệu suất tổng thể xuất sắc, phù hợp rộng rãi với các nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm <PERSON>du, đảm bảo thông tin hỏi đáp luôn cập nhật. So với ERNIE 4.0, hiệu suất tốt hơn."}, "ERNIE-Character-8K": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn cho các tình huống chuyên biệt do Baidu tự phát triển, phù hợp cho các ứng dụng như NPC trong game, đối thoại dịch vụ kh<PERSON>ch hàng, và vai trò trong đối thoại, phong cách nhân vật rõ ràng và nhất qu<PERSON>, kh<PERSON> năng tuân thủ chỉ dẫn mạnh mẽ, hiệu suất suy diễn tốt hơn."}, "ERNIE-Lite-Pro-128K": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn nhẹ do Baidu tự phát triển, kết hợp hiệu suất mô hình xuất sắc với khả năng suy diễn, hi<PERSON><PERSON> quả tốt hơn ER<PERSON><PERSON> Li<PERSON>, phù hợp cho việc suy diễn trên thẻ tăng tốc AI có công suất thấp."}, "ERNIE-Speed-128K": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn hiệu suất cao do Baidu phát hành vào năm 2024, có khả năng tổng quát xu<PERSON><PERSON> sắ<PERSON>, phù hợp làm mô hình nền để tinh chỉnh, xử lý tốt hơn các vấn đề trong các tình huống cụ thể, đồng thời có khả năng suy diễn tuyệt vời."}, "ERNIE-Speed-Pro-128K": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn hiệu suất cao do Baidu phát hành vào năm 2024, c<PERSON> khả năng tổng quát xu<PERSON><PERSON> sắ<PERSON>, hiệ<PERSON> quả tốt hơn ERNI<PERSON> Speed, phù hợp làm mô hình nền để tinh chỉnh, xử lý tốt hơn các vấn đề trong các tình huống cụ thể, đồng thời có khả năng suy diễn tuyệt vời."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev là mô hình tạo và chỉnh sửa hình ảnh đa phương thức dựa trên kiến trúc Rectified Flow Transformer do Black Forest Labs phát triển, với quy mô 12 tỷ tham số, tập trung vào vi<PERSON><PERSON> tạo, t<PERSON><PERSON> c<PERSON><PERSON> trú<PERSON>, nâng cao hoặc chỉnh sửa hình ảnh dựa trên điều kiện ngữ cảnh cho trước. Mô hình kết hợp ưu điểm tạo có kiểm soát của mô hình khuếch tán và khả năng mô hình hóa ngữ cảnh của Transformer, hỗ trợ xuất hình ảnh chất lượng cao, ứng dụng rộng rãi trong sửa chữa hình ảnh, hoàn thiện hình ảnh, tái cấu trúc cảnh quan trực quan."}, "FLUX.1-dev": {"description": "FLUX.1-dev là mô hình ngôn ngữ đa phương thức mã nguồn mở do Black Forest Labs phát triển, tối ưu cho các tác vụ kết hợp hình ảnh và văn bản. <PERSON><PERSON> tích hợp khả năng hiểu và tạo hình ảnh cùng văn bản, xây dựng trên nền tảng các mô hình ngôn ngữ lớn tiên tiến như Mistral-7B, thông qua bộ mã hóa thị giác thiết kế tinh vi và điều chỉnh chỉ dẫn đa giai đo<PERSON>n, đạt được khả năng xử lý phối hợp hình ảnh-văn bản và suy luận tác vụ phức tạp."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) là một mô hình sáng tạo, phù hợp cho nhiều lĩnh vực ứng dụng và nhiệm vụ phức tạp."}, "HelloMeme": {"description": "HelloMeme là công cụ AI có thể tự động tạo meme, ảnh động hoặc video ngắn dựa trên hình ảnh hoặc hành động bạn cung cấp. Bạn không cần có kỹ năng vẽ hay lập trình, chỉ cần chuẩn bị hình ảnh tham khảo, nó sẽ giúp bạn tạo ra nội dung đẹp mắt, thú vị và đồng nhất về phong cách."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full là mô hình chỉnh sửa hình ảnh đa phương thức mã nguồn mở do HiDream.ai ph<PERSON>t triển, dựa trên kiến trúc Diffusion Transformer tiên tiến và kết hợp khả năng hiểu ngôn ngữ mạnh mẽ (tích hợp LLaMA 3.1-8B-Instruct). <PERSON><PERSON> hình hỗ trợ tạo hình ảnh, chuyển đổi phong cách, chỉnh sửa cục bộ và vẽ lại nội dung qua chỉ dẫn ngôn ngữ tự nhiên, c<PERSON> khả năng hiểu và thực thi tốt giữa hình ảnh và văn bản."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled là mô hình tạo hình ảnh từ văn bản nhẹ, đ<PERSON><PERSON><PERSON> tối ưu qua kỹ thuật chưng cất, c<PERSON> khả năng tạo hình ảnh chất lượng cao n<PERSON> chóng, đặc biệt phù hợp với môi trường tài nguyên thấp và các tác vụ tạo hình ảnh thời gian thực."}, "InstantCharacter": {"description": "InstantCharacter là mô hình tạo nhân vật cá nhân hóa không cần tinh chỉnh do đội AI Tencent phát hành năm 2025, nhằm đạt được tạo nhân vật nhất quán, độ trung thực cao và đa cảnh. Mô hình hỗ trợ xây dựng nhân vật chỉ dựa trên một hình ảnh tham khảo và có thể linh hoạt chuyển nhân vật đó sang nhiều phong cách, hành động và nền khác nhau."}, "InternVL2-8B": {"description": "InternVL2-8B là một mô hình ngôn ngữ hình ảnh mạnh mẽ, hỗ trợ xử lý đa phương tiện giữa hình ảnh và văn bản, có khả năng nhận diện chính xác nội dung hình ảnh và tạo ra mô tả hoặc câu trả lời liên quan."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B là một mô hình ngôn ngữ hình ảnh mạnh mẽ, hỗ trợ xử lý đa phương tiện giữa hình ảnh và văn bản, c<PERSON> khả năng nhận diện chính xác nội dung hình ảnh và tạo ra mô tả hoặc câu trả lời liên quan."}, "Kolors": {"description": "Kolors là mô hình tạo hình ảnh từ văn bản do nhóm Kolors của <PERSON>hou phát triển. <PERSON><PERSON><PERSON><PERSON> huấn luyện trên hàng tỷ tham số, nổi bật về chất lư<PERSON><PERSON> hình <PERSON>, hiểu ngữ nghĩa tiếng Trung và khả năng hiển thị văn bản."}, "Kwai-Kolors/Kolors": {"description": "Kolors là mô hình tạo hình ảnh từ văn bản quy mô lớn dựa trên khuếch tán tiềm ẩn do nhóm Kolors của Kuaishou phát triển. Mô hình được huấn luyện trên hàng tỷ cặp văn bản-hì<PERSON>nh, thể hiện ưu thế rõ rệt về chất lượng hình ảnh, độ chính xác ngữ nghĩa phức tạp và khả năng hiển thị ký tự tiếng Trung và tiếng Anh. Nó hỗ trợ đầu vào tiếng Trung và tiếng <PERSON>h, đồng thời thể hiện xuất sắc trong việc hiểu và tạo nội dung đặc thù tiếng Trung."}, "Llama-3.2-11B-Vision-Instruct": {"description": "<PERSON><PERSON><PERSON> năng suy luận hình ảnh xuất sắc trên hình ảnh độ phân gi<PERSON>i cao, phù hợp cho các ứng dụng hiểu biết thị giác."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "<PERSON><PERSON><PERSON> năng suy luận hình <PERSON>nh cao cấp cho các ứng dụng đại lý hiểu biết thị gi<PERSON>c."}, "Meta-Llama-3.1-405B-Instruct": {"description": "<PERSON>ô hình văn bản đượ<PERSON> tinh chỉnh theo chỉ dẫn Llama 3.1, đ<PERSON><PERSON><PERSON> tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ, thể hiện xuất sắc trong nhiều mô hình trò chuyện mã nguồn mở và đóng có sẵn trên nhiều tiêu chuẩn ngành."}, "Meta-Llama-3.1-70B-Instruct": {"description": "<PERSON>ô hình văn bản đượ<PERSON> tinh chỉnh theo chỉ dẫn Llama 3.1, đ<PERSON><PERSON><PERSON> tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ, thể hiện xuất sắc trong nhiều mô hình trò chuyện mã nguồn mở và đóng có sẵn trên nhiều tiêu chuẩn ngành."}, "Meta-Llama-3.1-8B-Instruct": {"description": "<PERSON>ô hình văn bản đượ<PERSON> tinh chỉnh theo chỉ dẫn Llama 3.1, đ<PERSON><PERSON><PERSON> tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ, thể hiện xuất sắc trong nhiều mô hình trò chuyện mã nguồn mở và đóng có sẵn trên nhiều tiêu chuẩn ngành."}, "Meta-Llama-3.2-1B-Instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ nhỏ tiên tiến nhất, có kh<PERSON> năng hiểu ngôn ngữ, kh<PERSON> năng suy luận xuất sắc và khả năng sinh văn bản."}, "Meta-Llama-3.2-3B-Instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ nhỏ tiên tiến nhất, có kh<PERSON> năng hiểu ngôn ngữ, kh<PERSON> năng suy luận xuất sắc và khả năng sinh văn bản."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 là mô hình ngôn ngữ lớn mã nguồn mở đa ngôn ngữ tiên tiến nhất trong dòng <PERSON>, mang đến trải nghiệm hiệu suất tương đương mô hình 405B với chi phí cực thấp. Dựa trên cấu trúc Transformer, và được cải thiện tính hữu ích và an toàn thông qua tinh chỉnh giám sát (SFT) và học tăng cường từ phản hồi của con người (RLHF). Phiên bản tinh chỉnh theo chỉ dẫn của nó được tối ưu hóa cho các cuộc đối thoại đa ngôn ngữ, thể hiện tốt hơn nhiều mô hình trò chuyện mã nguồn mở và đóng trong nhiều tiêu chuẩn ngành. <PERSON><PERSON><PERSON> cắt đứt kiến thức là tháng 12 năm 2023."}, "MiniMax-M1": {"description": "<PERSON>ô hình suy luận tự phát triển hoàn toàn mới. Dẫn đầu toàn cầu: 80K chuỗi tư duy x 1 triệu đầu vào, hiệu quả sánh ngang các mô hình hàng đầu quốc tế."}, "MiniMax-Text-01": {"description": "Trong dòng mô hình MiniMax-01, chúng tôi đã thực hiện những đổi mới táo bạo: lần đầu tiên hiện thực hóa quy mô lớn cơ chế chú ý tuyến tính, kiến trúc Transformer truyền thống không còn là lựa chọn duy nhất. Mô hình này có số lượng tham số lên tới 4560 tỷ, trong đó kích hoạt một lần là 45,9 tỷ. Hiệu suất tổng hợp của mô hình tương đương với các mô hình hàng đầu quốc tế, đồng thời có khả năng xử lý hiệu quả ngữ cảnh dài nhất toàn cầu lên tới 4 triệu token, gấp 32 lần GPT-4o và 20 lần Claude-3.5-Son<PERSON>."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 là mô hình suy luận chú ý hỗn hợp quy mô lớn với trọng số mã nguồn mở, sở hữu 456 tỷ 600 triệu tham số, mỗi Token có thể kích hoạt khoảng 45,9 tỷ tham số. Mô hình hỗ trợ ngữ cảnh siêu dài lên đến 1 triệu Token một cách nguyên bản, và thông qua cơ chế chú ý chớp nho<PERSON>, trong các tác vụ sinh 100.000 Token tiết kiệm 75% lượng phép tính dấu chấm động so với DeepSeek R1. <PERSON><PERSON><PERSON> thời, MiniMax-M1 áp dụng kiến trúc MoE (chuyên gia hỗn hợp), kết hợp thuật toán CISPO và thiết kế chú ý hỗn hợp trong huấn luyện tăng cường hiệu quả, đạt hiệu suất hàng đầu trong ngành khi suy luận đầu vào dài và các kịch bản kỹ thuật phần mềm thực tế."}, "Moonshot-Kimi-K2-Instruct": {"description": "Tổng tham số 1T, tham số kích hoạt 32B. Trong các mô hình không suy nghĩ, đạt trình độ hàng đầu về kiến thức tiên tiến, to<PERSON> học và lập trình, đặc biệt phù hợp với các tác vụ đại lý chung. Được tối ưu kỹ lưỡng cho tác vụ đại lý, không chỉ trả lời câu hỏi mà còn có thể thực hiện hành động. <PERSON><PERSON> hợp nhất cho trò chuyện ứng biến, trải nghiệm đại lý chung, là mô hình phản xạ không cần suy nghĩ lâu."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - <PERSON>tral 8x7B-DPO (46.7B) là mô hình chỉ dẫn ch<PERSON>h xác cao, phù hợp cho tính toán phức tạp."}, "OmniConsistency": {"description": "OmniConsistency nâng cao tính nhất quán phong cách và khả năng tổng quát hóa trong các tác vụ hình ảnh sang hình ảnh (Image-to-Image) bằng cách giới thiệu các Diffusion Transformers (DiTs) quy mô lớn và dữ liệu phong cách ghép đôi, tr<PERSON><PERSON> suy giảm phong cách."}, "Phi-3-medium-128k-instruct": {"description": "<PERSON><PERSON> hình Phi-3-medium gi<PERSON><PERSON> nhau, nhưng với kích thước ngữ cảnh lớn hơn cho RAG hoặc gợi ý ít."}, "Phi-3-medium-4k-instruct": {"description": "<PERSON><PERSON>ình 14B tham s<PERSON>, chứ<PERSON> <PERSON>h chất lượng tốt hơn Phi-3-mini, tập trung vào dữ liệu dày đặc lý luận chất lượng cao."}, "Phi-3-mini-128k-instruct": {"description": "<PERSON><PERSON> hình Phi-3-mini gi<PERSON><PERSON>hau, nhưng với kích thước ngữ cảnh lớn hơn cho RAG hoặc gợi ý ít."}, "Phi-3-mini-4k-instruct": {"description": "<PERSON>h<PERSON><PERSON> viên nhỏ nhất của gia đình Phi-3. <PERSON><PERSON><PERSON> ưu hóa cho cả chất lượng và độ trễ thấp."}, "Phi-3-small-128k-instruct": {"description": "<PERSON><PERSON> hình Phi-3-small gi<PERSON><PERSON> nhau, nhưng với kích thước ngữ cảnh lớn hơn cho RAG hoặc gợi ý ít."}, "Phi-3-small-8k-instruct": {"description": "<PERSON><PERSON>nh 7B tham số, chứ<PERSON> <PERSON>h chất lượng tốt hơn Phi-3-mini, tập trung vào dữ liệu dày đặc lý luận chất lượng cao."}, "Phi-3.5-mini-instruct": {"description": "Phi-3-mini là phiên bản cập nhật của mô hình."}, "Phi-3.5-vision-instrust": {"description": "Phi-3-<PERSON> l<PERSON> phiên bản cập nhật của mô hình."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct là mô hình ngôn ngữ lớn được tinh chỉnh theo chỉ dẫn trong loạt Qwen2, với quy mô tham số là 7B. Mô hình này dựa trên kiến trúc Transformer, sử dụng hàm kích hoạt SwiGLU, độ lệch QKV trong chú ý và chú ý theo nhóm. Nó có khả năng xử lý đầu vào quy mô lớn. Mô hình thể hiện xuất sắc trong nhiều bài kiểm tra chuẩn về hiểu ngôn ngữ, sinh ngôn ngữ, khả năng đa ngôn ngữ, mã hóa, toán học và suy luận, vư<PERSON>t qua hầu hết các mô hình mã nguồn mở và thể hiện sức cạnh tranh tương đương với các mô hình độc quyền trong một số nhiệm vụ. Qwen2-7B-Instruct đã thể hiện sự cải thiện đáng kể về hiệu suất trong nhiều bài kiểm tra so với Qwen1.5-7B-Chat."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct là một trong những mô hình ngôn ngữ lớn mới nhất do Alibaba Cloud phát hành. <PERSON><PERSON> hình 7B này có khả năng cải thiện đáng kể trong các lĩnh vực mã hóa và toán học. Mô hình cũng cung cấp hỗ trợ đa ngôn ngữ, bao gồm hơn 29 ngôn ngữ, bao gồ<PERSON> tiếng Trung, ti<PERSON><PERSON>, v.v. <PERSON>ô hình đã có sự cải thiện đáng kể trong việc tuân theo chỉ dẫn, hiểu dữ liệu có cấu trúc và tạo ra đầu ra có cấu trúc (đặc biệt là JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct là phiên bản mới nhất trong loạt mô hình ngôn ngữ lớn chuyên biệt cho mã do Alibaba Cloud phát hành. Mô hình này được cải thiện đáng kể khả năng tạo mã, suy luận và sửa chữa thông qua việc đào tạo trên 5.5 triệu tỷ tokens, không chỉ nâng cao khả năng lập trình mà còn duy trì lợi thế về khả năng toán học và tổng quát. Mô hình cung cấp nền tảng toàn diện hơn cho các ứng dụng thực tế như tác nhân mã."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-V<PERSON> là thành viên mới của series Qwen, sở hữu khả năng hiểu thị giác mạnh mẽ, có thể phân tích văn bản, biể<PERSON> đồ và bố cục trong hình ảnh, cũ<PERSON> nh<PERSON> hiểu video dài và bắt các sự kiện, có thể suy luận, thao tác công cụ, hỗ trợ định vị vật thể đa định dạng và tạo ra đầu ra có cấu trúc, tối ưu hóa việc huấn luyện độ phân giải và tốc độ khung hình động cho việc hiểu video, đồng thời cải thiện hiệu suất của bộ mã hóa thị giác."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking là một mô hình ngôn ngữ thị gi<PERSON><PERSON> (VLM) mã nguồn mở được phát hành chung bởi Zhipu AI và Phòng thí nghiệm KEG của Đại họ<PERSON>, <PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt để xử lý các nhiệm vụ nhận thức đa phương thức phức tạp. Mô hình này dựa trên mô hình cơ sở GLM-4-9B-0414, thông qua việc giới thiệu cơ chế suy luận “Chuỗi tư duy” (Chain-of-Thought) và áp dụng chiến lư<PERSON><PERSON> học tăng cường, đã nâng cao đáng kể khả năng suy luận đa phương thức và tính ổn định của nó."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Cha<PERSON> là phiên bản mã nguồn mở trong loạt mô hình tiền huấn luyện GLM-4 do Zhizhu AI phát hành. <PERSON>ô hình này thể hiện xuất sắc trong nhiều lĩnh vực như ngữ nghĩa, to<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, mã và kiến thức. Ngoài việc hỗ trợ đối thoại nhiều vòng, GLM-4-9B-Chat còn có các tính năng nâng cao như duyệt web, thực thi mã, gọi công cụ tùy chỉnh (Function Call) và suy luận văn bản dài. <PERSON>ô hình hỗ trợ 26 ngôn ngữ, bao gồm tiếng Trung, tiế<PERSON>, ti<PERSON><PERSON>, tiếng Hàn và tiếng Đức. Trong nhiều bài kiểm tra chuẩn, GLM-4-9B-Cha<PERSON> đã thể hiện hiệu su<PERSON><PERSON> xu<PERSON><PERSON> s<PERSON>, <PERSON><PERSON><PERSON>ch-v2, <PERSON><PERSON><PERSON><PERSON><PERSON>, MML<PERSON> và C-Eva<PERSON>. <PERSON><PERSON> hình hỗ trợ độ dài ngữ cảnh tối đa 128K, phù hợp cho nghiên cứu học thuật và ứng dụng thương mại."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 là một mô hình suy diễn đượ<PERSON> điều khiển bởi học tăng cườ<PERSON> (RL), gi<PERSON><PERSON> quyết các vấn đề về tính lặp lại và khả năng đọc trong mô hình. Trước khi áp dụng RL, DeepSeek-R1 đã giới thiệu dữ liệu khởi động lạnh, tối ưu hóa thêm hiệu suất suy diễn. Nó thể hiện hiệu suất tương đương với OpenAI-o1 trong các nhiệm vụ to<PERSON> h<PERSON>, mã và suy diễn, và thông qua phương pháp đào tạo được thiết kế cẩn thận, nâng cao hiệu quả tổng thể."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B là mô hình được tạo ra từ Qwen2.5-Math-7B thông qua quá trình chưng cất kiến thức. Mô hình này được tinh chỉnh bằng 800.000 mẫu được chọn lọc từ DeepSeek-R1, thể hiện khả năng suy luận xuất sắc. Nó đã đạt được hiệu suất tốt trong nhiều bài kiểm tra chuẩn, trong đó có độ chính xác 92,8% trên MATH-500, tỷ l<PERSON> vượt qua 55,5% trên AIME 2024, và điểm số 1189 trên CodeForces, thể hiện khả năng toán học và lập trình mạnh mẽ cho một mô hình có quy mô 7B."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 là một mô hình ngôn ngữ hỗn hợp chuyên <PERSON>ia (MoE) với 6710 tỷ tham số, sử dụng chú ý tiềm ẩn đa đầ<PERSON> (MLA) và kiến trúc DeepSeekMoE, kết hợ<PERSON> chiến lược cân bằng tải không có tổn thất phụ trợ, tối ưu hóa hiệu suất suy diễn và đào tạo. Thông qua việc được tiền huấn luyện trên 14.8 triệu tỷ token chất lượng cao, và thực hiện tinh chỉnh giám sát và học tăng cường, DeepSeek-V3 vượt trội hơn các mô hình mã nguồn mở khác, gần với các mô hình đóng kín hàng đầu."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 là mô hình nền tảng kiến trúc MoE với khả năng mã hóa và đại lý vượt trội, tổng tham số 1T, tham số kích hoạt 32B. Trong các bài kiểm tra chuẩn về suy luận kiến thứ<PERSON> chung, <PERSON><PERSON><PERSON>r<PERSON>, to<PERSON> học và đại lý, hiệu suất của mô hình K2 vượt trội so với các mô hình mã nguồn mở phổ biến khác."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview là một mô hình xử lý ngôn ngữ tự nhiên độc đáo, c<PERSON> khả năng xử lý hiệu quả các nhiệm vụ tạo đối thoại phức tạp và hiểu ngữ cảnh."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview là một mô hình nghiên cứu do đội ngũ Qwen phát triển, tập trung vào khả năng suy di<PERSON><PERSON> h<PERSON>, có lợi thế độc đáo trong việc hiểu các cảnh phức tạp và giải quyết các vấn đề toán học liên quan đến hình ảnh."}, "Qwen/QwQ-32B": {"description": "QwQ là mô hình suy diễn của dòng <PERSON>wen. So với các mô hình tinh chỉnh theo chỉ dẫn truyền thống, QwQ có khả năng tư duy và suy diễn, có thể đạt được hiệu suất được cải thiện đáng kể trong các nhiệm vụ hạ nguồn, đặc biệt là trong việc giải quyết các vấn đề khó khăn. QwQ-32B là mô hình suy diễn trung bình, có thể đạt được hiệu suất cạnh tranh khi so sánh với các mô hình suy diễn tiên tiến nhất (như DeepSeek-R1, o1-mini). Mô hình này sử dụng các công nghệ như RoPE, SwiGLU, RMSNorm và Attention QKV bias, có cấu trúc mạng 64 lớp và 40 đầu chú ý Q (trong kiến trúc GQA, KV là 8)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview là mô hình nghiên c<PERSON><PERSON> thử nghiệm mới nhất củ<PERSON>, tập trung vào việc nâng cao khả năng suy luận của AI. Thông qua việc khám phá các cơ chế phức tạp như trộn ngôn ngữ và suy luận đệ quy, những lợi thế chính bao gồm khả năng phân tích suy luận mạnh mẽ, khả năng toán học và lập trình. <PERSON><PERSON>, cũng có những vấn đề về chuyển đổi ngôn ngữ, vòng lặp suy luận, các vấn đề an toàn và sự khác biệt về các khả năng khác."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 là mô hình ngôn ngữ tổng quát tiên tiến, hỗ trợ nhiều loại chỉ dẫn."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct là mô hình ngôn ngữ lớn được tinh chỉnh theo chỉ dẫn trong loạt Qwen2, với quy mô tham số là 72B. Mô hình này dựa trên kiến trúc Transformer, sử dụng hàm kích hoạt SwiGLU, độ lệch QKV trong chú ý và chú ý theo nhóm. Nó có khả năng xử lý đầu vào quy mô lớn. Mô hình thể hiện xuất sắc trong nhiều bài kiểm tra chuẩn về hiểu ngôn ngữ, sinh ngôn ngữ, khả năng đa ngôn ngữ, mã hóa, toán học và suy luận, vư<PERSON>t qua hầu hết các mô hình mã nguồn mở và thể hiện sức cạnh tranh tương đương với các mô hình độc quyền trong một số nhiệm vụ."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-V<PERSON> là phiên bản mới nhất của mô hình <PERSON>wen-V<PERSON>, đạt đư<PERSON>c hiệu suất hàng đầu trong các thử nghiệm chuẩn hiểu biết hình <PERSON>nh."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, nhằm tối ưu hóa việc xử lý các nhiệm vụ theo hướng dẫn."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, nhằm tối ưu hóa việc xử lý các nhiệm vụ theo hướng dẫn."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn đượ<PERSON> phát triển bởi đội ngũ <PERSON> của <PERSON>"}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, sở hữu khả năng hiểu và tạo ra mạnh mẽ hơn."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, đ<PERSON><PERSON><PERSON> thiết kế để tối ưu hóa việc xử lý các tác vụ chỉ dẫn."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, nhằm tối ưu hóa việc xử lý các nhiệm vụ theo hướng dẫn."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, đ<PERSON><PERSON><PERSON> thiết kế để tối ưu hóa việc xử lý các tác vụ chỉ dẫn."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-<PERSON><PERSON> tập trung vào vi<PERSON><PERSON> viết mã."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct là phiên bản mới nhất trong loạt mô hình ngôn ngữ lớn chuyên biệt cho mã do Alibaba Cloud phát hành. Mô hình này được cải thiện đáng kể khả năng tạo mã, suy luận và sửa chữa thông qua việc đào tạo trên 5.5 triệu tỷ tokens, không chỉ nâng cao khả năng lập trình mà còn duy trì lợi thế về khả năng toán học và tổng quát. Mô hình cung cấp nền tảng toàn diện hơn cho các ứng dụng thực tế như tác nhân mã."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct là mô hình đa phương thức do đội ngũ Qwen2.5-VL ph<PERSON>t triển, là một phần của loạt Qwen2.5-VL. <PERSON>ô hình này không chỉ giỏi nhận diện các vật thể thông thường, mà còn có thể phân tích văn bản, biể<PERSON> đồ, biể<PERSON> tượ<PERSON>, hình vẽ và bố cục trong hình ảnh. <PERSON><PERSON> có thể hoạt động như một đại lý thị giác, c<PERSON> khả năng suy luận và điều khiển công cụ một cách động, bao gồm cả việc sử dụng máy tính và điện thoại. Ngo<PERSON>i ra, mô hình này có thể xác định chính xác vị trí của các đối tượng trong hình ảnh và tạo ra đầu ra có cấu trúc cho hóa đơn, b<PERSON><PERSON> biểu, v.v. So với mô hình tiền nhiệm Qwen2-VL, phiên bản này đã được cải thiện đáng kể về khả năng giải toán và giải quyết vấn đề thông qua học tăng cường, và phong cách phản hồi cũng phù hợp hơn với sở thích của con người."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-<PERSON><PERSON> là mô hình ngôn ngữ thị giác trong loạt Qwen2.5. <PERSON><PERSON> hình này có những cải tiến đáng kể: có khả năng hiểu thị gi<PERSON><PERSON> mạnh hơ<PERSON>, có thể nhận diện các vật thể thông thường, phân tích văn bản, biểu đồ và bố cục; hoạt động như một đại lý thị giác có thể suy luận và hướng dẫn sử dụng công cụ một cách động; hỗ trợ hiểu các video dài hơn 1 giờ và bắt các sự kiện quan trọng; có thể định vị chính xác các vật thể trong hình ảnh thông qua việc tạo khung giới hạn hoặc điểm; hỗ trợ tạo ra đầu ra có cấu trúc, đặc biệt phù hợp với dữ liệu quét như hóa đơn, bảng biểu."}, "Qwen/Qwen3-14B": {"description": "Qwen3 là một mô hình lớn thế hệ mới của Tongyi Qianwen với khả năng nâng cao đáng kể, đạt đư<PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy lu<PERSON>, t<PERSON><PERSON> qu<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 là một mô hình lớn thế hệ mới của Tongyi Qianwen với khả năng nâng cao đáng kể, đạt đư<PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy lu<PERSON>, t<PERSON><PERSON> qu<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 là mô hình ngôn ngữ lớn chuyên gia hỗn hợp (MoE) hàng đầu trong dòng Qwen3 do đội ngũ Aliyun Tongyi Qianwen phát triển. <PERSON><PERSON> hình có tổng 235 tỷ tham số, mỗi lần suy luận kích hoạt 22 tỷ tham số. Đ<PERSON>y là phiên bản cập nhật của Qwen3-235B-A22B không ở chế độ suy nghĩ, tập trung cải thiện đáng kể khả năng tuân thủ chỉ dẫn, suy luận logic, hiể<PERSON> vă<PERSON> bả<PERSON>, to<PERSON> h<PERSON>, kho<PERSON> họ<PERSON>, lập trình và sử dụng công cụ. Ng<PERSON><PERSON>i ra, mô hình tăng cường bao phủ kiến thức đa ngôn ngữ và điều chỉnh tốt hơn sở thích người dùng trong các tác vụ chủ quan và mở, tạo ra văn bản hữu ích và chất lượng cao hơn."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 là thành viên trong dòng mô hình ngôn ngữ lớn Qwen3 do đội ngũ Alibaba Tongyi Qianwen phát triển, tập trung vào các tác vụ suy luận phức tạp và khó khăn. Mô hình dựa trên kiến trúc chuyên gia hỗn hợp (MoE), tổng tham số 235 tỷ, mỗi token kích hoạt khoảng 22 tỷ tham số, giúp tăng hiệu quả tính toán trong khi duy trì hiệu suất mạnh mẽ. <PERSON><PERSON> mô hình “suy nghĩ” chuyên biệt, nó cải thiện đáng kể khả năng suy luận logic, to<PERSON> học, khoa học, lập trình và các bài kiểm tra học thuật, đạt trình độ hàng đầu trong các mô hình suy nghĩ mã nguồn mở. <PERSON><PERSON> hình cũng tăng cường khả năng chung như tuân thủ chỉ dẫn, sử dụng công cụ và tạo văn bản, hỗ trợ ngữ cảnh dài 256K token, rất phù hợp cho các kịch bản cần suy luận sâu và xử lý tài liệu dài."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 là một mô hình lớn thế hệ mới của Tongyi Qianwen với khả năng nâng cao đáng kể, đạt đư<PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy lu<PERSON>, t<PERSON><PERSON> qu<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 là phiên bản cập nhật của Qwen3-30B-A3B ở chế độ không suy nghĩ. Đ<PERSON>y là một mô hình chuyên gia hỗn hợp (MoE) với tổng cộng 30,5 tỷ tham số và 3,3 tỷ tham số kích hoạt. <PERSON>ô hình này đã được cải tiến quan trọng ở nhiều khía cạnh, bao gồm nâng cao đáng kể khả năng tuân thủ chỉ dẫn, suy luận logic, hiểu văn bản, to<PERSON> h<PERSON>, <PERSON><PERSON><PERSON> họ<PERSON>, lập trình và sử dụng công cụ. <PERSON><PERSON><PERSON> thời, nó đạt được tiến bộ thực chất trong việc bao phủ kiến thức đa ngôn ngữ và có khả năng điều chỉnh tốt hơn với sở thích của người dùng trong các nhiệm vụ chủ quan và mở, từ đó tạo ra các phản hồi hữu ích hơn và văn bản chất lượng cao hơn. Ngoài ra, khả năng hiểu văn bản dài của mô hình cũng được nâng lên đến 256K. Mô hình này chỉ hỗ trợ chế độ không suy nghĩ và không tạo ra thẻ `<think></think>` trong đầu ra."}, "Qwen/Qwen3-32B": {"description": "Qwen3 là một mô hình lớn thế hệ mới của Tongyi Qianwen với khả năng nâng cao đáng kể, đạt đư<PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy lu<PERSON>, t<PERSON><PERSON> qu<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "Qwen/Qwen3-8B": {"description": "Qwen3 là một mô hình lớn thế hệ mới của Tongyi Qianwen với khả năng nâng cao đáng kể, đạt đư<PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy lu<PERSON>, t<PERSON><PERSON> qu<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "Qwen2-72B-Instruct": {"description": "Qwen2 là dòng mô hình mới nhất củ<PERSON>, hỗ trợ ngữ cảnh 128k, so với các mô hình mã nguồn mở tốt nhất hiện tại, Qwen2-72B vượt trội hơn hẳn trong nhiều khả năng như hiểu ngôn ngữ tự nhiên, ki<PERSON><PERSON> thức, mã, toán học và đa ngôn ngữ."}, "Qwen2-7B-Instruct": {"description": "Qwen2 là dòng mô hình mới nhất c<PERSON><PERSON>, có khả năng vượt qua các mô hình mã nguồn mở cùng quy mô hoặc thậm chí lớn hơn, Qwen2 7B đạt được lợi thế đáng kể trong nhiều bài kiểm tra, đặc biệt là trong việc hiểu mã và tiếng Trung."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B là một mô hình ngôn ngữ hình ảnh mạnh mẽ, hỗ trợ xử lý đa phương thức giữa hình ảnh và văn bản, c<PERSON> khả năng nhận diện chính xác nội dung hình ảnh và sinh ra mô tả hoặc câu trả lời liên quan."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct là một mô hình ngôn ngữ lớn với 14 tỷ tham số, có hiệu suất xu<PERSON><PERSON> sắc, tối ưu cho các tình huống tiếng Trung và đa ngôn ngữ, hỗ trợ các ứng dụng như hỏi đáp thông minh, tạo nội dung."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct là một mô hình ngôn ngữ lớn với 32 tỷ tham số, có hiệu suất cân bằng, tối ưu cho các tình huống tiếng Trung và đa ngôn ngữ, hỗ trợ các ứng dụng như hỏi đáp thông minh, tạo nội dung."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct hỗ trợ ngữ cảnh 16k, tạo ra văn bản dài hơn 8K. Hỗ trợ gọi hàm và tương tác liền mạch với hệ thống bên ngoài, nâng cao đáng kể tính linh hoạt và khả năng mở rộng. Kiến thức của mô hình đã tăng lên rõ rệt và khả năng mã hóa cũng như toán học được cải thiện đáng kể, hỗ trợ hơn 29 ngôn ngữ."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct là một mô hình ngôn ngữ lớn với 7 tỷ tham số, hỗ trợ gọi hàm và tương tác liền mạch với các hệ thống bên ngo<PERSON>i, nâng cao tính linh hoạt và khả năng mở rộng. Tối ưu cho các tình huống tiếng Trung và đa ngôn ngữ, hỗ trợ các ứng dụng như hỏi đáp thông minh, tạo nội dung."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct là một mô hình hướng dẫn lập trình dựa trên đào tạo trước quy mô lớn, có khả năng hiểu và sinh mã mạnh mẽ, có thể xử lý hiệu quả các nhiệm vụ lập trình khác nhau, đặc biệt phù hợp cho việc viết mã thông minh, tạo kịch bản tự động và giải đáp các vấn đề lập trình."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct là một mô hình ngôn ngữ lớn được thiết kế đặc biệt cho việc tạo mã, hiểu mã và các tình huống phát triển hiệ<PERSON> quả, với quy mô 32B tham số hàng đầu trong ngành, có thể đáp ứng nhu cầu lập trình đa dạng."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B là mô hình Mo<PERSON> (mô hình chuyên gia hỗn hợp), gi<PERSON><PERSON> thiệu “chế độ suy luận hỗn hợp”, cho phép người dùng chuyển đổi liền mạch giữa “chế độ suy nghĩ” và “chế độ không suy nghĩ”. Mô hình hỗ trợ hiểu và suy luận bằng 119 ngôn ngữ và phương ngữ, đồng thời có khả năng gọi công cụ mạnh mẽ. Trong các bài kiểm tra chuẩn về năng lực tổng hợp, mã hóa và toán học, đa ngôn ngữ, kiến thức và suy luận, mô hình có thể cạnh tranh với các mô hình lớn hàng đầu trên thị trường hiện nay như DeepSeek R1, OpenAI o1, o3-mini, Grok 3 và Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B là mô hình đặc (Dense Model), gi<PERSON><PERSON> thiệu “chế độ suy luận hỗn hợp”, cho phép người dùng chuyển đổi liền mạch gi<PERSON>a “chế độ suy nghĩ” và “chế độ không suy nghĩ”. Nhờ cải tiến kiến trúc mô hình, tăng dữ liệu huấn luyện và phương pháp huấn luyện hiệu quả hơn, hiệu suất tổng thể tương đương với Qwen2.5-72B."}, "SenseChat": {"description": "<PERSON><PERSON> hình phiên bản c<PERSON> bản (V4), đ<PERSON> dà<PERSON> ngữ cảnh 4K, <PERSON><PERSON><PERSON> năng tổng quát mạnh mẽ."}, "SenseChat-128K": {"description": "<PERSON><PERSON> hình phiên bản c<PERSON> bản (V4), độ dài ngữ cảnh 128K, thể hiện xu<PERSON><PERSON> sắc trong các nhiệm vụ hiểu và sinh văn bản dài."}, "SenseChat-32K": {"description": "<PERSON><PERSON> hình phiên bản c<PERSON> bản (V4), đ<PERSON> dà<PERSON> ngữ cảnh 32K, <PERSON><PERSON><PERSON> ho<PERSON><PERSON> <PERSON><PERSON> dụng trong nhiều tình huống."}, "SenseChat-5": {"description": "<PERSON><PERSON><PERSON> bả<PERSON> mô hình mớ<PERSON> (V5.5), độ dài ngữ cảnh 128K, <PERSON><PERSON><PERSON> năng cải thiện đáng kể trong suy luận to<PERSON>, đ<PERSON><PERSON> tho<PERSON><PERSON> ti<PERSON>, theo dõi chỉ dẫn và hiểu biết văn bản dài, ngang tầm với GPT-4o."}, "SenseChat-5-1202": {"description": "<PERSON><PERSON><PERSON> bản mới nhất dựa trên V5.5, c<PERSON><PERSON> thiện đáng kể về năng lực cơ bản tiếng Trung và tiế<PERSON>, tr<PERSON>, ki<PERSON><PERSON> thứ<PERSON> khoa học tự nhi<PERSON>, <PERSON><PERSON><PERSON> <PERSON>ọ<PERSON> xã hội, v<PERSON><PERSON><PERSON>, logic to<PERSON> học và kiểm soát số lượng từ so với phiên bản trước."}, "SenseChat-5-Cantonese": {"description": "<PERSON><PERSON> dài ngữ cảnh 32K, <PERSON><PERSON><PERSON><PERSON> qua GPT-4 trong hiểu biết đối thoại tiếng Quảng <PERSON>, có thể so sánh với GPT-4 Turbo trong nhiều lĩnh vự<PERSON> nh<PERSON> thức, <PERSON><PERSON> l<PERSON>, to<PERSON> học và lập trình mã."}, "SenseChat-5-beta": {"description": "Một số hiệu su<PERSON><PERSON> v<PERSON><PERSON><PERSON> tr<PERSON><PERSON> hơn SenseCat-5-1202"}, "SenseChat-Character": {"description": "<PERSON><PERSON> hình phiên bản ti<PERSON><PERSON> ch<PERSON>, độ dài ngữ cảnh 8K, tốc độ ph<PERSON>n hồi cao."}, "SenseChat-Character-Pro": {"description": "<PERSON><PERSON> hình phiên bả<PERSON> cao cấ<PERSON>, độ dài ngữ cảnh 32K, kh<PERSON> năng đư<PERSON><PERSON> cải thiện to<PERSON><PERSON>, hỗ trợ đối thoại tiếng Trung/tiếng <PERSON>."}, "SenseChat-Turbo": {"description": "<PERSON><PERSON> hợp cho các tình huống hỏi đáp nhanh và tinh chỉnh mô hình."}, "SenseChat-Turbo-1202": {"description": "<PERSON><PERSON> phiên bản nhẹ mới nhất của mô hình, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> hơn 90% khả năng của mô hình đầy đủ, gi<PERSON><PERSON> đáng kể chi phí suy diễn."}, "SenseChat-Vision": {"description": "<PERSON><PERSON> hình phiên bản mớ<PERSON> (V5.5), hỗ trợ đầu vào nhiều hình <PERSON>, ho<PERSON>n thiện khả năng cơ bản củ<PERSON> mô hình, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> sự cải thiện lớn trong nhận diện thuộc tính đối tư<PERSON>, mố<PERSON> quan hệ không gian, nhận diện sự kiện hành động, hi<PERSON><PERSON> cảnh, nhận diện cảm xúc, suy luận kiến thức logic và hiểu sinh ra văn bản."}, "SenseNova-V6-5-Pro": {"description": "Thông qua việc cập nhật toàn diện dữ liệu đa phương thức, ngôn ngữ và suy luận cùng với tối ưu hóa chiến lư<PERSON><PERSON> huấn luyện, mô hình mới đạt được sự cải thiện đáng kể trong suy luận đa phương thức và khả năng tuân theo chỉ dẫn tổng quát, hỗ trợ cửa sổ ngữ cảnh lên đến 128k và thể hiện xuất sắc trong các nhiệm vụ chuyên biệt như nhận dạng OCR và nhận diện IP du lịch văn hóa."}, "SenseNova-V6-5-Turbo": {"description": "Thông qua việc cập nhật toàn diện dữ liệu đa phương thức, ngôn ngữ và suy luận cùng với tối ưu hóa chiến lư<PERSON><PERSON> huấn luyện, mô hình mới đạt được sự cải thiện đáng kể trong suy luận đa phương thức và khả năng tuân theo chỉ dẫn tổng quát, hỗ trợ cửa sổ ngữ cảnh lên đến 128k và thể hiện xuất sắc trong các nhiệm vụ chuyên biệt như nhận dạng OCR và nhận diện IP du lịch văn hóa."}, "SenseNova-V6-Pro": {"description": "<PERSON>h<PERSON><PERSON> hiện sự thống nhất nguyên bản gi<PERSON><PERSON> hình <PERSON>, văn bản và video, vư<PERSON><PERSON> qua giới hạn phân tách đa phương thức tru<PERSON> thống, gi<PERSON><PERSON> đư<PERSON><PERSON> hai giải vô địch trong các đánh giá OpenCompass và SuperCLUE."}, "SenseNova-V6-Reasoner": {"description": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> gi<PERSON>a lý luận sâu sắc về thị giác và ngôn ngữ, thực hiện tư duy chậm và lý luận sâu, trình bày quy trình chuỗi tư duy hoàn chỉnh."}, "SenseNova-V6-Turbo": {"description": "Thực hiện sự thống nhất nguyên bản gi<PERSON>a hình <PERSON>, văn bản và video, vư<PERSON><PERSON> qua giới hạn phân tách đa phương thức truyền thống, dẫn đầu toàn diện trong các khía cạnh cốt lõi như khả năng đa phương thức và khả năng ngôn ngữ, vừa văn vừa lý, nhiều lần đứng đầu trong các đánh giá trong và ngoài nước."}, "Skylark2-lite-8k": {"description": "<PERSON><PERSON> hình thế hệ thứ hai <PERSON>, mô hình Skylark2-lite có tốc độ phản hồi cao, phù hợp cho các tình huống yêu cầu tính thời gian thực cao, nh<PERSON><PERSON> cảm với chi <PERSON>hí, kh<PERSON><PERSON> yêu cầu độ chính xác mô hình cao, chi<PERSON><PERSON> dài cửa sổ ngữ cảnh là 8k."}, "Skylark2-pro-32k": {"description": "<PERSON><PERSON> hình thế hệ thứ hai <PERSON>, <PERSON><PERSON><PERSON><PERSON> bản <PERSON>2-pro c<PERSON> độ ch<PERSON>h x<PERSON><PERSON> cao h<PERSON>, ph<PERSON> hợp cho các tình huống tạo văn bả<PERSON> ph<PERSON><PERSON> tạ<PERSON>, <PERSON><PERSON><PERSON> tạo nội dung chuyê<PERSON>, s<PERSON><PERSON> tá<PERSON> ti<PERSON>, d<PERSON><PERSON> thu<PERSON><PERSON> chấ<PERSON> l<PERSON> cao, chi<PERSON><PERSON> dà<PERSON> cửa sổ ngữ cảnh là 32k."}, "Skylark2-pro-4k": {"description": "<PERSON><PERSON> hình thế hệ thứ hai <PERSON>, mô hình Skylark2-pro c<PERSON> độ chính xá<PERSON> cao h<PERSON>, ph<PERSON> hợp cho các tình huống tạo văn bản ph<PERSON><PERSON> tạ<PERSON>, <PERSON><PERSON><PERSON> tạo nội dung chuyê<PERSON> ng<PERSON>nh, s<PERSON><PERSON> tá<PERSON> ti<PERSON>uy<PERSON>, d<PERSON><PERSON> thu<PERSON><PERSON> chấ<PERSON> l<PERSON> cao, chi<PERSON><PERSON> dà<PERSON> c<PERSON>a sổ ngữ cảnh là 4k."}, "Skylark2-pro-character-4k": {"description": "<PERSON><PERSON> hình thế hệ thứ hai <PERSON>, mô hình Skylark2-pro-character có khả năng nhập vai và trò chuyện xuất sắc, giỏi nhập vai theo yêu cầu của ng<PERSON><PERSON><PERSON> dùng, tạ<PERSON> ra những cuộc trò chuyện tự nhiên, phù hợp để xây dựng chatbot, trợ lý ảo và dịch vụ khách hàng trự<PERSON> tuyế<PERSON>, có tốc độ phản hồi cao."}, "Skylark2-pro-turbo-8k": {"description": "<PERSON><PERSON> hình thế hệ thứ hai <PERSON>, mô hình Skylark2-pro-turbo-8k có tốc độ suy di<PERSON><PERSON>, chi <PERSON><PERSON><PERSON> thấ<PERSON>, chi<PERSON><PERSON> dài cửa sổ ngữ cảnh là 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 là mô hình mã nguồn mở thế hệ mới trong dòng GLM, với 32 tỷ tham số. Mô hình này có hiệu suất tương đương với các dòng GPT của OpenAI và các dòng V3/R1 của DeepSeek."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 là mô hình nhỏ trong dòng GLM, với 9 tỷ tham số. M<PERSON> hình này kế thừa các đặc điểm kỹ thuật của dòng GLM-4-32B, nhưng cung cấp lựa chọn triển khai nhẹ hơn. Mặc dù quy mô nhỏ, GLM-4-9B-0414 vẫn thể hiện khả năng xuất sắc trong các nhiệm vụ như tạo mã, thiết kế trang web, tạo đồ họa SVG và viết dựa trên tìm kiếm."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking là một mô hình ngôn ngữ thị gi<PERSON><PERSON> (VLM) mã nguồn mở được phát hành chung bởi Zhipu AI và Phòng thí nghiệm KEG của Đại họ<PERSON>, <PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt để xử lý các nhiệm vụ nhận thức đa phương thức phức tạp. Mô hình này dựa trên mô hình cơ sở GLM-4-9B-0414, thông qua việc giới thiệu cơ chế suy luận “Chuỗi tư duy” (Chain-of-Thought) và áp dụng chiến lư<PERSON><PERSON> học tăng cường, đã nâng cao đáng kể khả năng suy luận đa phương thức và tính ổn định của nó."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 là một mô hình suy luận có khả năng suy tư sâu. Mô hình này được phát triển dựa trên GLM-4-32B-0414 thông qua khởi động lạnh và tăng cường học tập, và đã được huấn luyện thêm trong các nhiệm vụ toán học, mã và logic. So với mô hình cơ sở, GLM-Z1-32B-0414 đã nâng cao đáng kể khả năng toán học và khả năng giải quyết các nhiệm vụ phức tạp."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 là mô hình nhỏ trong dòng GLM, chỉ có 9 tỷ tham số, nhưng vẫn thể hiện khả năng đáng kinh ngạc trong khi duy trì truyền thống mã nguồn mở. Mặc dù quy mô nhỏ, mô hình này vẫn thể hiện xuất sắc trong suy luận toán học và các nhiệm vụ chung, với hiệu suất tổng thể đứng đầu trong các mô hình mã nguồn mở cùng quy mô."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 là một mô hình suy luận sâu có khả năng suy tư (đối thủ của Deep Research của OpenAI). <PERSON><PERSON><PERSON><PERSON> với các mô hình suy tư sâu điển hình, mô hình suy tư này sử dụng thời gian suy tư sâu hơn để giải quyết các vấn đề mở và phức tạp hơn."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B <PERSON><PERSON> phiên bản mã nguồn mở, cung cấp trải nghiệm đối thoại tối ưu cho các ứng dụng hội thoại."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B là mô hình suy luận lớn có ngữ cảnh dài đầu tiên được huấn luyện bằng học tăng cường (LRM), tối ưu hóa cho các nhiệm vụ suy luận văn bản dài. <PERSON><PERSON> hình sử dụng khung học tăng cường mở rộng ngữ cảnh tiến dần, đạt được chuyển đổi ổn định từ ngữ cảnh ngắn sang dài. Trong bảy bài kiểm tra chuẩn hỏi đáp tài liệu ngữ cảnh dài, QwenLong-L1-32B v<PERSON><PERSON><PERSON> qua các mô hình hàng đầu như OpenAI-o3-mini và Qwen3-235B-A22B, hiệu suất tương đương Claude-3.7-Sonnet-Thinking. <PERSON><PERSON> hình đặc biệt mạnh về suy luận to<PERSON>, logic và suy luận đa bước."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B, trong khi vẫn giữ được khả năng ngôn ngữ chung xuất sắc của dòng mô hình gốc, đã tăng cường đào tạo với 500 tỷ token chất lư<PERSON><PERSON> cao, nâng cao đáng kể khả năng logic toán học và mã."}, "abab5.5-chat": {"description": "Hướng đến các tình huống sản xuất, hỗ trợ xử lý nhiệm vụ phức tạp và sinh văn bản hi<PERSON><PERSON> quả, phù hợp cho các ứng dụng trong lĩnh vực chuyên môn."}, "abab5.5s-chat": {"description": "<PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt cho các tình huống đối thoại bằng tiếng Trung, cung cấp khả năng sinh đối thoại chất lượng cao bằng tiếng Trung, phù hợp cho nhiều tình huống ứng dụng."}, "abab6.5g-chat": {"description": "<PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt cho các cuộc đối thoại đa ngôn ngữ, hỗ trợ sinh đối thoại chất lượng cao bằng tiếng Anh và nhiều ngôn ngữ khác."}, "abab6.5s-chat": {"description": "<PERSON><PERSON> hợp cho nhiều nhiệm vụ xử lý ngôn ngữ tự nhiên, bao gồ<PERSON> sinh vă<PERSON> bả<PERSON>, hệ thống đối tho<PERSON>, v.v."}, "abab6.5t-chat": {"description": "Tối ưu hóa cho các tình huống đối thoại bằng tiếng Trung, cung cấp khả năng sinh đối thoại mượt mà và phù hợp với thói quen diễn đạt tiếng Trung."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 là một mô hình ngôn ngữ lớn tiên tiến, đ<PERSON><PERSON><PERSON> tối ưu hóa thông qua học tăng cường và dữ liệu khởi động lạnh, có hiệu suất suy luận, to<PERSON> học và lập trình xuất sắc."}, "accounts/fireworks/models/deepseek-v3": {"description": "<PERSON><PERSON> hình ngôn ngữ Mixture-of-Experts (MoE) mạnh mẽ do Deepseek cung cấp, với tổng số tham số là 671B, mỗi ký hiệu k<PERSON>ch ho<PERSON>t 37B tham số."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Llama 3 70B, đ<PERSON><PERSON><PERSON> tối ưu hóa cho đối thoại đa ngôn ngữ và hiểu ngôn ngữ tự nhiên, hiệ<PERSON> suất vượt trội hơn nhiều mô hình cạnh tranh."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Llama 3 8B, đ<PERSON><PERSON><PERSON> tối ưu hóa cho đối thoại và các nhiệm vụ đa ngôn ngữ, thể hiện hiệu suất xuất sắc và hiệu quả."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "<PERSON><PERSON> hình chỉ dẫn Llama 3 8B (p<PERSON><PERSON>n bản HF), kế<PERSON> qu<PERSON> nhất quán với thực hiện ch<PERSON>h thức, c<PERSON> t<PERSON>h nhất quán cao và tương thích đa nền tảng."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Llama 3.1 405B, có số lượng tham số cực lớn, phù hợp cho các nhiệm vụ phức tạp và theo dõi chỉ dẫn trong các tình huống tải cao."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Llama 3.1 70B, cung cấp kh<PERSON> năng hiểu và sinh ngôn ngữ tự nhiên xuất sắc, là lựa chọn lý tưởng cho các nhiệm vụ đối thoại và phân tích."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Llama 3.1 8B, đ<PERSON><PERSON><PERSON> tối ưu hóa cho đối thoại đa ngôn ngữ, có thể vượt qua hầu hết các mô hình mã nguồn mở và đóng trong các tiêu chuẩn ngành phổ biến."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Mô hình suy luận hình ảnh chỉ dẫn với 11B tham số của Meta. Mô hình này được tối ưu hóa cho nhận diện hình ảnh, suy luận hình ảnh, mô tả hình ảnh và trả lời các câu hỏi chung liên quan đến hình ảnh. Mô hình có khả năng hiểu dữ liệu hình ảnh như biểu đồ và đồ thị, và thu hẹp khoảng cách giữa hình ảnh và ngôn ngữ thông qua việc tạo mô tả văn bản về chi tiết hình ảnh."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Mô hình chỉ dẫn Llama 3.2 3B là một mô hình đa ngôn ngữ nhẹ mà Meta phát hành. Mô hình này được thiết kế để tăng cường hiệu quả, mang lại cải tiến đáng kể về độ trễ và chi phí so với các mô hình lớn hơn. Các trường hợp sử dụng ví dụ của mô hình này bao gồm truy vấn, viết lại thông báo và hỗ trợ viết."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Mô hình suy luận hình ảnh chỉ dẫn với 90B tham số của Meta. Mô hình này được tối ưu hóa cho nhận diện hình ảnh, suy luận hình ảnh, mô tả hình ảnh và trả lời các câu hỏi chung liên quan đến hình ảnh. Mô hình có khả năng hiểu dữ liệu hình ảnh như biểu đồ và đồ thị, và thu hẹp khoảng cách giữa hình ảnh và ngôn ngữ thông qua việc tạo mô tả văn bản về chi tiết hình ảnh."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct là phiên bản cập nh<PERSON><PERSON> tháng 12 của Llama 3.1 70B. <PERSON>ô hình này được cải tiến dựa trên Llama 3.1 70B (ra mắt vào tháng 7 năm 2024), nâng cao khả năng gọi công cụ, hỗ trợ văn bản đa ngôn ngữ, toán học và lập trình. Mô hình này đạt được trình độ hàng đầu trong ngành về suy luận, toán học và tuân thủ hướng dẫn, đồng thời có thể cung cấp hiệu suất tương tự như 3.1 405B, với lợi thế đáng kể về tốc độ và chi phí."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "<PERSON><PERSON> hình 24B tham số, có khả năng tiên tiến tương đương với các mô hình lớn hơn."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Mixtral MoE 8x22B, với số lượng tham số lớn và kiến trúc nhiều chuyên gia, hỗ trợ toàn diện cho việc xử lý hiệu quả các nhiệm vụ phức tạp."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "<PERSON><PERSON> hình chỉ dẫn Mixtral MoE 8x7B, kiến trúc nhiều chuyên gia cung cấp khả năng theo dõi và thực hiện chỉ dẫn hiệu quả."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "<PERSON><PERSON> <PERSON>ình MythoMax L2 13B, kế<PERSON> hợp công nghệ hợp nhất mới, xu<PERSON><PERSON> sắ<PERSON> trong việc kể chuyện và đóng vai."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Mô hình chỉ dẫn Phi 3 Vision, mô hình đa mô hình nhẹ, có khả năng xử lý thông tin hình ảnh và văn bản phứ<PERSON> tạp, với khả năng suy luận mạnh mẽ."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "<PERSON>ô hình QwQ là một mô hình nghiên cứu thử nghiệm được phát triển bởi đội ngũ <PERSON>, tập trung vào việc nâng cao khả năng suy luận của AI."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "<PERSON><PERSON><PERSON> bản 72B củ<PERSON> mô hình <PERSON>wen-VL là thành quả mới nhất c<PERSON><PERSON>, đại diện cho gần một năm đổi mới."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 là một loạt mô hình ngôn ngữ chỉ chứa bộ giải mã do đội ngũ Qwen của Alibaba Cloud phát triển. Những mô hình này cung cấp các kích thước kh<PERSON>, bao gồm 0.5B, 1.5B, 3B, 7B, 14B, 32B và 72B, và có hai biến thể: phiên bản cơ sở (base) và phiên bản chỉ dẫn (instruct)."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct là phiên bản mới nhất trong loạt mô hình ngôn ngữ lớn chuyên biệt cho mã do Alibaba Cloud phát hành. Mô hình này được cải thiện đáng kể khả năng tạo mã, suy luận và sửa chữa thông qua việc đào tạo trên 5.5 triệu tỷ tokens, không chỉ nâng cao khả năng lập trình mà còn duy trì lợi thế về khả năng toán học và tổng quát. Mô hình cung cấp nền tảng toàn diện hơn cho các ứng dụng thực tế như tác nhân mã."}, "accounts/yi-01-ai/models/yi-large": {"description": "<PERSON><PERSON>-<PERSON>, có khả năng xử lý đa ngôn ngữ xuất sắc, có thể được sử dụng cho nhiều nhiệm vụ sinh và hiểu ngôn ngữ."}, "ai21-jamba-1.5-large": {"description": "<PERSON><PERSON> hình đa ngôn ngữ với 398B tham s<PERSON> (94B hoạt động), cung cấp cửa sổ ngữ cảnh dài 256K, g<PERSON><PERSON>, đ<PERSON><PERSON> ra có cấu trúc và tạo ra nội dung có căn cứ."}, "ai21-jamba-1.5-mini": {"description": "<PERSON><PERSON> hình đa ngôn ngữ với 52B tham số (12B hoạt động), cung cấp cửa sổ ngữ cảnh dài 256K, g<PERSON><PERSON>, đ<PERSON>u ra có cấu trúc và tạo ra nội dung có căn cứ."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Một mô hình đa ngôn ngữ với 398 tỷ tham số (94 tỷ tham số hoạt động), cung cấp cửa sổ ngữ cảnh dài 256K, g<PERSON><PERSON>, đầu ra có cấu trúc và sinh dựa trên sự thật."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "<PERSON><PERSON>t mô hình đa ngôn ngữ với 52 tỷ tham số (12 tỷ tham số hoạt động), cung cấp cửa sổ ngữ cảnh dài 256K, g<PERSON><PERSON>, đầu ra có cấu trúc và sinh dựa trên sự thật."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 <PERSON><PERSON> nâng cao tiêu chu<PERSON> ng<PERSON>nh, hi<PERSON><PERSON> su<PERSON><PERSON> v<PERSON><PERSON><PERSON> trội hơn các mô hình cạnh tranh và Claude 3 Opus, thể hiện xuất sắc trong nhiều đ<PERSON>, đồng thời có tốc độ và chi phí của mô hình tầm trung của chúng tôi."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 <PERSON><PERSON> nâng cao tiêu chu<PERSON> ng<PERSON>nh, hi<PERSON><PERSON> su<PERSON>t v<PERSON><PERSON><PERSON> trội so với các mô hình đối thủ và Claude 3 Opus, thể hiện xuất sắc trong các đánh giá rộng rãi, đồng thời có tốc độ và chi phí tương đương với các mô hình tầm trung của chúng tôi."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku là mô hình nhanh nhất và gọn nhẹ nhất củ<PERSON>, cung cấp tốc độ phản hồi gần như ngay lập tức. <PERSON><PERSON> có thể nhanh chóng trả lời các truy vấn và yêu cầu đơn giản. <PERSON>h<PERSON><PERSON> hàng sẽ có thể xây dựng trải nghiệm AI liền mạch mô phỏng tương tác của con người. Claude 3 Haiku có thể xử lý hình ảnh và trả về đầu ra văn bản, với cửa sổ ngữ cảnh 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus là mô hình AI mạnh nhất củ<PERSON>, có hiệu suất tiên tiến trong các nhiệm vụ phức tạp. Nó có thể xử lý các gợi ý mở và các tình huống chưa thấy, với độ trôi chảy và khả năng hiểu giống con người xuất sắc. Claude 3 Opus thể hiện những khả năng tiên tiến của AI sinh. Claude 3 Opus có thể xử lý hình ảnh và trả về đầu ra văn bản, với cửa sổ ngữ cảnh 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Claude 3 Sonnet của Anthropic đạt được sự cân bằng lý tưởng giữa trí thông minh và tốc độ - đặc biệt phù hợp cho khối lượng công việc doanh nghiệp. Nó cung cấp hiệu quả tối đa với giá thấp hơn đối thủ, đư<PERSON><PERSON> thiết kế để trở thành một máy chủ đáng tin cậy và bền bỉ, phù hợp cho triển khai AI quy mô lớn. Claude 3 Sonnet có thể xử lý hình ảnh và trả về đầu ra văn bản, với cửa sổ ngữ cảnh 200K."}, "anthropic.claude-instant-v1": {"description": "M<PERSON>t mô hình <PERSON>, kinh tế nhưng vẫn rất mạnh mẽ, có thể xử lý một loạt các nhiệm vụ bao gồm đối thoại hàng ngày, phân tích văn bản, tóm tắt và hỏi đáp tài liệu."}, "anthropic.claude-v2": {"description": "<PERSON><PERSON> hình của Anthropic thể hiện khả năng cao trong nhiều nhiệm vụ từ đối thoại phức tạp và sinh nội dung sáng tạo đến tuân thủ chỉ dẫn chi tiết."}, "anthropic.claude-v2:1": {"description": "<PERSON><PERSON><PERSON> bản cập nh<PERSON><PERSON> củ<PERSON> 2, c<PERSON> cửa sổ ngữ cảnh gấ<PERSON> đô<PERSON>, cùng với độ tin cậy, tỷ lệ ảo giác và độ chính xác dựa trên bằng chứng được cải thiện trong các tài liệu dài và ngữ cảnh RAG."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku là mô hình nhanh nhất và nhỏ gọn nhất c<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> thiết kế để đạt được phản hồi gần như ngay lập tức. <PERSON>ó có hiệu suất định hướng nhanh chóng và chính xác."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus là mô hình mạnh mẽ nhất c<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> sử dụng để xử lý các nhiệm vụ phức tạp cao. <PERSON><PERSON> thể hiện xuất sắc về hiệu suất, tr<PERSON> thông <PERSON>, sự trôi chảy và khả năng hiểu biết."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 <PERSON><PERSON> là mô hình thế hệ tiếp the<PERSON> <PERSON><PERSON> nh<PERSON>t của Anthropic. So v<PERSON><PERSON> 3 <PERSON><PERSON>, <PERSON> 3.5 <PERSON><PERSON> có sự cải thiện trong nhiều kỹ năng và vượt qua mô hình lớn nhất thế hệ trước Claude 3 Opus trong nhiều bài kiểm tra trí tuệ."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet cung cấp khả năng vượt trội hơn Opus và tốc độ nhanh hơn Sonnet, trong khi vẫn giữ giá tương tự. Sonnet đặc biệt xuất sắc trong lậ<PERSON> tr<PERSON>nh, <PERSON><PERSON><PERSON> họ<PERSON> dữ liệu, xử lý hình ảnh và các nhiệm vụ đại lý."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet là mô hình thông minh nhất của Anthropic cho đến nay, và cũng là mô hình suy luận hỗn hợp đầu tiên trên thị trường. Claude 3.7 Sonnet có khả năng tạo ra phản hồi gần như ngay lập tức hoặc suy nghĩ từng bước kéo dài, cho phép người dùng thấy rõ những quá trình này. Sonnet đặc biệt xuất sắc trong lậ<PERSON> trình, <PERSON><PERSON><PERSON> họ<PERSON> dữ liệu, xử lý hình ảnh và các nhiệm vụ đại diện."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 là mô hình mạnh mẽ nhất của Anthropic dùng để xử lý các nhiệm vụ phức tạp cao. <PERSON><PERSON> thể hiện xuất sắc về hiệu suất, tr<PERSON> tu<PERSON>, sự mượt mà và khả năng hiểu biết."}, "anthropic/claude-sonnet-4": {"description": "<PERSON> 4 có thể tạo ra phản hồi gần như tức thì hoặc suy nghĩ từng bướ<PERSON> kéo dài, người dùng có thể rõ ràng quan sát quá trình này. Người dùng API cũng có thể kiểm soát chi tiết thời gian suy nghĩ của mô hình."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B là một mô hình ngôn ngữ lớn thưa thớt với 72 tỷ tham số và 16 tỷ tham số kích hoạt, dựa trên kiến trúc chuyên gia hỗn hợp theo <PERSON> (MoGE). Nó phân nhóm các chuyên gia trong giai đoạn lựa chọn chuyên gia và giới hạn token kích hoạt số lượng chuyên gia bằng nhau trong mỗi nhóm, từ đó đạt được cân bằng tải chuyên gia và cải thiện đáng kể hiệu quả triển khai mô hình trên nền tảng Ascend."}, "aya": {"description": "Aya 23 là mô hình đa ngôn ngữ do Cohere phát hành, hỗ trợ 23 ngôn ngữ, tạo điều kiện thuận lợi cho các ứng dụng ngôn ngữ đa dạng."}, "aya:35b": {"description": "Aya 23 là mô hình đa ngôn ngữ do Cohere phát hành, hỗ trợ 23 ngôn ngữ, tạo điều kiện thuận lợi cho các ứng dụng ngôn ngữ đa dạng."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B là mô hình ngôn ngữ lớn mã nguồn mở có thể thương mại hóa với 130 tỷ tham số, đ<PERSON><PERSON><PERSON> phát triển bởi Baichuan Intelligence, đã đạt được hiệu suất tốt nhất trong cùng kích thước trên các benchmark tiếng Trung và tiếng Anh."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B là một mô hình ngôn ngữ lớn dựa trên kiến trúc chuyên gia hỗn hợp (MoE) do công ty Baidu phát triển. Mô hình có tổng số 300 tỷ tham số, nhưng trong quá trình suy luận mỗi token chỉ kích hoạt 47 tỷ tham số, đảm bảo hiệu suất mạnh mẽ đồng thời tối ưu hóa hiệu quả tính toán. Là một trong những mô hình cốt lõi của dòng ERNIE 4.5, nó thể hiện khả năng xuất sắc trong các nhiệm vụ hiểu, tạo vă<PERSON> bản, suy luận và lập trình. Mô hình áp dụng phương pháp tiền huấn luyện MoE dị thể đa phương thức sáng tạo, thông qua huấn luyện kết hợp văn bản và hình ảnh, nâng cao hiệu quả tổng thể, đặc biệt nổi bật trong việc tuân thủ chỉ dẫn và ghi nhớ kiến thức thế giới."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse là một mô hình đa ngôn ngữ hiệu suất cao 32B, đ<PERSON><PERSON><PERSON> thiết kế để thách thức hiệu suất của các mô hình đơn ngôn ngữ thông qua việc tinh chỉnh theo chỉ dẫn, khai thác dữ liệu, đào tạo theo sở thích và hợp nhất mô hình. Nó hỗ trợ 23 ngôn ngữ."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse là một mô hình đa ngôn ngữ hiệu suất cao 8B, đ<PERSON><PERSON><PERSON> thiết kế để thách thức hiệu suất của các mô hình đơn ngôn ngữ thông qua việc tinh chỉnh theo chỉ dẫn, khai thác dữ liệu, đào tạo theo sở thích và hợp nhất mô hình. Nó hỗ trợ 23 ngôn ngữ."}, "c4ai-aya-vision-32b": {"description": "Aya Vision là một mô hình đa phương tiện tiên tiến, thể hiện xuất sắc trên nhiều tiêu chuẩn chính về khả năng ngôn ngữ, văn bản và hình ảnh. Phiên bản 32 tỷ tham số này tập trung vào hiệu suất đa ngôn ngữ tiên tiến."}, "c4ai-aya-vision-8b": {"description": "Aya Vision là một mô hình đa phương tiện tiên tiến, thể hiện xuất sắc trên nhiều tiêu chuẩn chính về khả năng ngôn ngữ, văn bản và hình ảnh. Phiên bản 8 tỷ tham số này tập trung vào độ trễ thấp và hiệu suất tối ưu."}, "charglm-3": {"description": "CharGLM-3 <PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt cho vai trò và đồng hành cảm xúc, hỗ trợ trí nhớ nhiều vòng siêu dài và đối thoại cá nhân hóa, <PERSON>ng dụng rộng rãi."}, "charglm-4": {"description": "CharGLM-4 đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho vai trò và sự đồng hành cảm xúc, hỗ trợ trí nhớ đa vòng dài và đối thoại cá nhân hóa, <PERSON>ng dụng rộng rãi."}, "chatglm3": {"description": "ChatGLM3 là mô hình đóng nguồn do Trung tâm AI Zhizhu và Phòng thí nghiệm KEG của Đại học Thanh Hoa phát hành. Mô hình này đã được tiền huấn luyện với lượng lớn các bộ định danh tiếng Trung và tiếng Anh, cũng như được huấn luyện để phù hợp với sở thích của con người. So với mô hình thế hệ đầu tiên, ChatGLM3 đã cải thiện 16%, 36% và 280% trên các bảng xếp hạng MMLU, C-<PERSON>l và GSM8K, đồng thời đứng đầu bảng xếp hạng C-Eval cho các tác vụ tiếng Trung. Mô hình này phù hợp cho các trường hợp yêu cầu cao về lượng kiến thức, kh<PERSON> năng suy luận và sáng t<PERSON>, <PERSON><PERSON><PERSON> vi<PERSON> quả<PERSON> cáo, vi<PERSON><PERSON> ti<PERSON><PERSON>uy<PERSON>, viết nội dung kiến thức, và tạo mã nguồn."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base là mô hình cơ bản có quy mô 6 tỷ tham số, thu<PERSON><PERSON> thế hệ mới nhất của loạt ChatGLM do Zhipu phát triển."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o là một mô hình động, <PERSON><PERSON><PERSON><PERSON> cập nhật theo thời gian thực để giữ phiên bản mới nhất. <PERSON><PERSON> kết hợp khả năng hiểu và sinh ngôn ngữ mạnh mẽ, phù hợp cho các ứng dụng quy mô lớn, bao gồm dịch vụ khách hàng, gi<PERSON><PERSON> dục và hỗ trợ kỹ thuật."}, "claude-2.0": {"description": "Claude 2 cung cấp những tiến bộ quan trọng trong khả năng cho doanh nghiệp, bao gồm ngữ cảnh 200K token hàng đầu trong ngành, gi<PERSON><PERSON> đáng kể tỷ lệ ảo giác của mô hình, nh<PERSON><PERSON> nhở hệ thống và một tính năng kiểm tra mới: gọ<PERSON> công cụ."}, "claude-2.1": {"description": "Claude 2 cung cấp những tiến bộ quan trọng trong khả năng cho doanh nghiệp, bao gồm ngữ cảnh 200K token hàng đầu trong ngành, gi<PERSON><PERSON> đáng kể tỷ lệ ảo giác của mô hình, nh<PERSON><PERSON> nhở hệ thống và một tính năng kiểm tra mới: gọ<PERSON> công cụ."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 <PERSON><PERSON> là mô hình thế hệ tiếp the<PERSON> <PERSON><PERSON> nh<PERSON>t của Anthropic. So v<PERSON><PERSON> 3 <PERSON><PERSON>, <PERSON> 3.5 <PERSON><PERSON> đã cải thiện ở nhiều kỹ năng và vượt qua mô hình lớn nhất thế hệ trước là Claude 3 Opus trong nhiều bài kiểm tra trí tuệ."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet cung cấp khả năng vư<PERSON><PERSON> trội so với Opus và tốc độ nhanh hơn <PERSON>, đồng thời giữ nguyên mức giá như Sonnet. Sonnet đặc biệt xuất sắc trong lậ<PERSON> trình, <PERSON><PERSON><PERSON> <PERSON>ọ<PERSON> dữ liệu, xử lý hình ảnh và các nhiệm vụ đại lý."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet cung cấp khả năng vượt xa Opus và tốc độ nhanh hơn <PERSON>, đồng thời giữ mức giá giống như Sonnet. Sonnet đặc biệt xuất sắc trong lậ<PERSON> tr<PERSON>nh, <PERSON><PERSON><PERSON> <PERSON>ọ<PERSON> dữ liệu, xử lý hình ảnh và các nhiệm vụ đại diện."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 <PERSON><PERSON> là mô hình AI mạnh nhất c<PERSON><PERSON>, với hiệu suất vư<PERSON><PERSON> trội so với các mô hình đối thủ và Claude 3 Opus, thể hiện xuất sắc trong nhiều đánh gi<PERSON> rộng rãi, đồng thời có tốc độ và chi phí tương đương với các mô hình tầm trung của chúng tôi."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku là mô hình nhanh nhất và gọn nhẹ nhất c<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> thiết kế để đạt được phản hồi gần như ngay lập tức. <PERSON>ó có hiệu suất định hướng nhanh và chính xác."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus là mô hình mạnh mẽ nhất của Anthropic để xử lý các nhiệm vụ phức tạp. <PERSON><PERSON> thể hiện xuất sắc về hiệu suất, tr<PERSON> thông <PERSON>, sự trôi chảy và khả năng hiểu biết."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet cung cấp sự cân bằng lý tưởng giữa trí thông minh và tốc độ cho khối lượng công việc doanh nghiệp. <PERSON>ó cung cấp hiệu suất tối đa với mức gi<PERSON> thấp hơn, đ<PERSON>g tin cậy và phù hợp cho triển khai quy mô lớn."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 là mô hình mạnh mẽ nhất của Anthropic được sử dụng để xử lý các nhiệm vụ phức tạp cao. <PERSON><PERSON> thể hiện xuất sắc về hiệu suất, trí tuệ, sự trôi chảy và khả năng hiểu biết."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet có thể tạo ra phản hồi gần như ngay lập tức hoặc suy nghĩ dần dần ké<PERSON> dài, người dùng có thể thấy rõ những quá trình này. Người dùng API cũng có thể kiểm soát chi tiết thời gian suy nghĩ của mô hình."}, "codegeex-4": {"description": "CodeGeeX-4 là trợ lý lập trình AI mạnh mẽ, hỗ trợ nhiều ngôn ngữ lập trình với câu hỏi thông minh và hoàn thành mã, nâng cao hiệu suất phát triển."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B là một mô hình tạo mã đa ngôn ngữ, hỗ trợ đầy đủ các chức năng như hoàn thành và tạo mã, trình gi<PERSON><PERSON> thích mã, tì<PERSON> kiếm trê<PERSON> mạng, g<PERSON><PERSON> hà<PERSON>, và hỏi đáp mã cấp kho, bao phủ nhiều tình huống trong phát triển phần mềm. Đây là mô hình tạo mã hàng đầu với số tham số dưới 10B."}, "codegemma": {"description": "CodeGemma là mô hình ngôn ngữ nhẹ chuyên dụng cho các nhiệm vụ lập trình k<PERSON>, hỗ trợ lặp lại và tích hợp nhanh chóng."}, "codegemma:2b": {"description": "CodeGemma là mô hình ngôn ngữ nhẹ chuyên dụng cho các nhiệm vụ lập trình k<PERSON>, hỗ trợ lặp lại và tích hợp nhanh chóng."}, "codellama": {"description": "Code Llama là một LLM tập trung vào việc sinh và thảo luận mã, kết hợp hỗ trợ cho nhiều ngôn ngữ lập trình, phù hợp cho môi trường phát triển."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama là một LLM tập trung vào việc tạo mã và thảo luận, kết hợp hỗ trợ nhiều ngôn ngữ lập trình, phù hợp cho môi trường phát triển."}, "codellama:13b": {"description": "Code Llama là một LLM tập trung vào việc sinh và thảo luận mã, kết hợp hỗ trợ cho nhiều ngôn ngữ lập trình, phù hợp cho môi trường phát triển."}, "codellama:34b": {"description": "Code Llama là một LLM tập trung vào việc sinh và thảo luận mã, kết hợp hỗ trợ cho nhiều ngôn ngữ lập trình, phù hợp cho môi trường phát triển."}, "codellama:70b": {"description": "Code Llama là một LLM tập trung vào việc sinh và thảo luận mã, kết hợp hỗ trợ cho nhiều ngôn ngữ lập trình, phù hợp cho môi trường phát triển."}, "codeqwen": {"description": "CodeQwen1.5 là mô hình ngôn ngữ quy mô lớn được đào tạo trên một lượng lớn dữ liệu mã, chuyên gi<PERSON><PERSON> quyết các nhiệm vụ lập trình phức tạp."}, "codestral": {"description": "Codestral là mô hình mã đầu tiên của Mistral AI, cung cấp hỗ trợ xuất sắc cho các nhiệm vụ sinh mã."}, "codestral-latest": {"description": "Codestral là mô hình sinh mã tiên tiến tập trung vào việc sinh mã, tối ưu hóa cho các nhiệm vụ điền vào khoảng trống và hoàn thiện mã."}, "codex-mini-latest": {"description": "codex-mini-latest là phiên bản tinh chỉnh của o4-mini, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho Codex CLI. Đ<PERSON>i với việc sử dụng trực tiếp qua API, chúng tô<PERSON> khuyến nghị bắt đầu từ gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B là mô hình đư<PERSON>c thiết kế cho việc tuân thủ hướng dẫn, đối thoại và lập trình."}, "cogview-4": {"description": "CogView-4 là mô hình tạo hình ảnh văn bản mã nguồn mở đầu tiên của Zhipu hỗ trợ tạo ký tự Trung Hoa, với sự cải tiến toàn diện về hiểu ngữ nghĩa, chất lư<PERSON><PERSON> tạo hình ảnh, kh<PERSON> năng tạo ký tự tiếng Trung và tiếng Anh, hỗ trợ đầu vào song ngữ Trung-Anh với độ dài tùy ý, có thể tạo hình ảnh với độ phân giải bất kỳ trong phạm vi cho phép."}, "cohere-command-r": {"description": "Command R là một mô hình sinh tạo có thể mở rộng, nhắm đến RAG và Sử dụng Công cụ để cho phép AI quy mô sản xuất cho doanh nghiệp."}, "cohere-command-r-plus": {"description": "Command R+ là mô hình tối ưu hóa RAG hiện đ<PERSON>, <PERSON><PERSON><PERSON><PERSON> thiết kế để xử lý khối lượng công vi<PERSON><PERSON> cấp do<PERSON>h nghi<PERSON>."}, "cohere/Cohere-command-r": {"description": "Command R là một mô hình sinh có thể mở rộng, đ<PERSON><PERSON><PERSON> thiết kế cho việc sử dụng RAG và công cụ, gi<PERSON><PERSON> do<PERSON>h nghiệp triển khai <PERSON> cấp sản xuất."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ là mô hình tối ưu RAG tiên tiến nhất, đ<PERSON><PERSON><PERSON> thiết kế để xử lý khối lượng công việ<PERSON> cấp doanh nghiệ<PERSON>."}, "command": {"description": "Một mô hình đối thoại tuân theo chỉ dẫn, thể hiện chất lượng cao và đáng tin cậy trong các nhiệm vụ ngôn ngữ, đồng thời có độ dài ngữ cảnh dài hơn so với mô hình sinh cơ bản của chúng tôi."}, "command-a-03-2025": {"description": "Command A là mô hình mạnh nhất mà chúng tôi đã phát triển cho đến nay, thể hiện xuất sắc trong việc sử dụng công cụ, đ<PERSON><PERSON> lý, tạo ra thông tin tăng cường (RAG) và các ứng dụng đa ngôn ngữ. Command A có độ dài ngữ cảnh 256K, chỉ cần hai GPU để vận hành, và so với Command R+ 08-2024, hiệu suất tăng 150%."}, "command-light": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>n bản Command nhỏ hơn, <PERSON><PERSON><PERSON> hơn, gần nh<PERSON> mạnh mẽ tương đương nhưng có tốc độ nhanh hơn."}, "command-light-nightly": {"description": "<PERSON><PERSON> rút ngắn khoảng cách thời gian giữa các phiên bả<PERSON> ch<PERSON>, chúng tôi đã phát hành phiên bản hàng đêm của mô hình Command. Đ<PERSON>i với dòng command-light, phi<PERSON>n bản này đư<PERSON> gọ<PERSON> là command-light-nightly. <PERSON><PERSON> lưu ý rằng command-light-nightly là phiên bản mới nhất, mang tính thử nghiệm cao và (có thể) không ổn định. Phiên bản hàng đêm sẽ được cập nhật định kỳ mà không có thông bá<PERSON> trướ<PERSON>, vì vậy không nên sử dụng trong môi trường sản xuất."}, "command-nightly": {"description": "<PERSON><PERSON> rút ngắn khoảng cách thời gian giữa các phiên bả<PERSON> ch<PERSON>, chúng tôi đã phát hành phiên bản hàng đêm của mô hình Command. Đ<PERSON><PERSON> với dòng Command, phiên bản nà<PERSON> đư<PERSON><PERSON> gọ<PERSON> là command-cightly. <PERSON><PERSON> lưu ý rằng command-nightly là phiên bản mới nh<PERSON>t, mang tính thử nghiệm cao và (c<PERSON> thể) không ổn định. Phiên bản hàng đêm sẽ được cập nhật định kỳ mà không có thông báo trước, vì vậy không nên sử dụng trong môi trường sản xuất."}, "command-r": {"description": "Command R là LLM được tối ưu hóa cho các nhiệm vụ đối thoại và ngữ cảnh dài, đặc biệt phù hợp cho tương tác động và quản lý kiến thức."}, "command-r-03-2024": {"description": "Command R là một mô hình đối thoại tuân theo chỉ dẫn, thể hiện chất lượng cao hơn và đáng tin cậy hơn trong các nhiệm vụ ngôn ngữ, đồng thời có độ dài ngữ cảnh dài hơn so với các mô hình trước đây. Nó có thể được sử dụng cho các quy trình phức tạp như tạo mã, tạo ra thông tin tăng cường (RAG), sử dụng công cụ và đại lý."}, "command-r-08-2024": {"description": "command-r-08-2024 là p<PERSON><PERSON><PERSON> bản cập nhật của mô hình Command R, <PERSON><PERSON><PERSON><PERSON> phát hành vào tháng 8 năm 2024."}, "command-r-plus": {"description": "Command R+ là một mô hình ngôn ngữ lớn hiệu suất cao, <PERSON><PERSON><PERSON><PERSON> thiết kế cho các tình huống doanh nghiệp thực tế và ứng dụng phức tạp."}, "command-r-plus-04-2024": {"description": "Command R+ là một mô hình đối thoại tuân theo chỉ dẫn, thể hiện chất lượng cao hơn và đáng tin cậy hơn trong các nhiệm vụ ngôn ngữ, đồng thời có độ dài ngữ cảnh dài hơn so với các mô hình trước đây. Nó phù hợp nhất cho các quy trình RAG phức tạp và việc sử dụng công cụ nhiều bước."}, "command-r-plus-08-2024": {"description": "Command R+ là một mô hình đối thoại tuân theo hướng dẫn, thể hiện chất lượng cao hơn trong các nhiệm vụ ngôn ngữ, đáng tin cậy hơn và có độ dài ngữ cảnh dài hơn so với các mô hình trước đây. Nó phù hợp nhất cho các quy trình làm việc RAG phức tạp và việc sử dụng công cụ nhiều bước."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 là một phiên bản cập nhật nhỏ gọn và hiệu quả, <PERSON><PERSON><PERSON><PERSON> phát hành vào tháng 12 năm 2024. <PERSON><PERSON> thể hiện xuất sắc trong các nhiệm vụ cần suy luận phức tạp và xử lý nhiều bư<PERSON><PERSON> như RAG, sử dụng công cụ và đại lý."}, "compound-beta": {"description": "Compound-beta là một hệ thống AI ph<PERSON><PERSON> h<PERSON>, đ<PERSON><PERSON><PERSON> hỗ trợ bởi nhiều mô hình có sẵn công khai trong GroqCloud, có khả năng thông minh và chọn lọc sử dụng công cụ để trả lời các truy vấn của người dùng."}, "compound-beta-mini": {"description": "Compound-beta-mini là một hệ thống AI phứ<PERSON> h<PERSON>, đ<PERSON><PERSON><PERSON> hỗ trợ bởi các mô hình có sẵn công khai trong GroqCloud, có khả năng thông minh và chọn lọc sử dụng công cụ để trả lời các truy vấn của người dùng."}, "computer-use-preview": {"description": "<PERSON><PERSON> hình computer-use-preview đ<PERSON><PERSON><PERSON> thiết kế chuyên biệt cho “công cụ sử dụng máy t<PERSON>h”, đ<PERSON><PERSON><PERSON> huấn luyện để hiểu và thực hiện các nhiệm vụ liên quan đến máy tính."}, "dall-e-2": {"description": "<PERSON><PERSON> hình DALL·<PERSON> thế hệ thứ hai, hỗ trợ tạo hình ảnh chân thực và chính xác hơn, với độ phân giải gấp 4 lần thế hệ đầu tiên."}, "dall-e-3": {"description": "<PERSON>ô hình DALL·E mới nhất, ph<PERSON><PERSON> hành vào tháng 11 năm 2023. Hỗ trợ tạo hình ảnh chân thực và chính xá<PERSON> h<PERSON>, với khả năng thể hiện chi tiết mạnh mẽ hơn."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct cung cấp khả năng xử lý chỉ dẫn đáng tin cậy, hỗ trợ nhiều ứng dụng trong ngành."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 là một mô hình suy diễn được điều khiển bởi học tăng cườ<PERSON> (RL), gi<PERSON><PERSON> quyết các vấn đề về tính lặp lại và khả năng đọc hiểu trong mô hình. Trước khi áp dụng RL, DeepSeek-R1 đã giới thiệu dữ liệu khởi động lạnh, tối ưu hóa thêm hiệu suất suy diễn. Nó thể hiện hiệu suất tương đương với OpenAI-o1 trong các nhiệm vụ to<PERSON> h<PERSON>, mã và suy diễn, và thông qua phương pháp đào tạo được thiết kế cẩn thận, nâng cao hiệu quả tổng thể."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 đã nâng cao đáng kể chiều sâu khả năng suy luận và phán đoán nhờ tận dụng tài nguyên tính toán tăng thêm và cơ chế tối ưu thuật toán trong quá trình huấn luyện sau. <PERSON>ô hình thể hiện xuất sắc trong nhiều bài đánh gi<PERSON> ch<PERSON>, bao gồ<PERSON> toán h<PERSON>, lập trình và logic chung. Hiệu suất tổng thể hiện gần đạt các mô hình hàng đầu như O3 và Gemini 2.5 Pro."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B là mô hình được chưng cất chuỗi suy nghĩ từ DeepSeek-R1-0528 sang Qwen3 8B Base. Mô hình đạt hiệu suất tiên tiến nhất (SOTA) trong các mô hình mã nguồn mở, vư<PERSON><PERSON> Qwen3 8B 10% trong bài kiểm tra AIME 2024 và đạt mức hiệu suất của Qwen3-235B-thinking. Mô hình thể hiện xuất sắc trong suy luận to<PERSON> họ<PERSON>, lập trình và logic chung, c<PERSON> kiến trúc giống Qwen3-8B nhưng dùng chung cấu hình tokenizer của DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "<PERSON>ô hình chưng cất DeepSeek-R1, tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "<PERSON>ô hình chưng cất DeepSeek-R1, tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "<PERSON>ô hình chưng cất DeepSeek-R1, tối ưu hóa hiệu suất suy luận thông qua học tăng cường và dữ liệu khởi động lạnh, mô hình mã nguồn mở làm mới tiêu chuẩn đa nhiệm."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B là mô hình được tạo ra từ Qwen2.5-32B thông qua chưng cất kiến thức. Mô hình này sử dụng 800.000 mẫu được chọn lọc từ DeepSeek-R1 để tinh chỉnh, thể hiện hiệu suất xuất sắc trong nhiều lĩnh vực như toán họ<PERSON>, lập trình và suy luận. Trong nhiều bài kiểm tra chuẩn như AIME 2024, MATH-500, GPQA Diamond, nó đã đạt được kết quả xuất sắc, trong đó đạt 94.3% độ chính xác trên MATH-500, thể hiện khả năng suy luận toán học mạnh mẽ."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B là mô hình được tạo ra từ Qwen2.5-Math-7B thông qua chưng cất kiến thức. Mô hình này sử dụng 800.000 mẫu được chọn lọc từ DeepSeek-R1 để tinh chỉnh, thể hiện khả năng suy luận xuất sắc. Trong nhiều bài kiểm tra chuẩn, nó đã thể hiện xuất sắc, trong đó đạt 92.8% độ chính xác trên MATH-500, đạt 55.5% tỷ lệ vượt qua trên AIME 2024, và đạt điểm 1189 trên CodeForces, thể hiện khả năng toán học và lập trình mạnh mẽ cho mô hình quy mô 7B."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 kết hợp các đặc điểm xuất sắc của các phiên bản trước, tăng cường khả năng tổng quát và mã hóa."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 là một mô hình ngôn ngữ hỗn hợp chuyên <PERSON>ia (MoE) với 6710 tỷ tham số, sử dụng chú ý tiềm ẩn đa đầ<PERSON> (MLA) và kiến trúc DeepSeekMoE, kết hợp với chiến lược cân bằng tải không có tổn thất phụ trợ, tối ưu hóa hiệu suất suy diễn và đào tạo. Thông qua việc được tiền huấn luyện trên 14.8 triệu tỷ token chất lư<PERSON><PERSON> cao, và thực hiện tinh chỉnh giám sát và học tăng cường, DeepSeek-V3 vượt trội về hiệu suất so với các mô hình mã nguồn mở khác, gần gũi với các mô hình đóng nguồn hàng đầu."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B là mô hình tiên tiến đư<PERSON><PERSON> huấn luyện cho các cuộc đối thoại phức tạp."}, "deepseek-ai/deepseek-r1": {"description": "<PERSON><PERSON> hiệu quả tiên tiế<PERSON>, xu<PERSON><PERSON> s<PERSON><PERSON> trong suy luậ<PERSON>, to<PERSON> học và lập trình."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 là một mô hình ngôn ngữ hình ảnh hỗn hợp chuyên <PERSON> (MoE) được phát triển dựa trên DeepSeekMoE-27B, sử dụng kiến trúc MoE với kích hoạt thưa, đạt được hiệu suất xuất sắc chỉ với 4.5B tham số được kích hoạt. Mô hình này thể hiện xuất sắc trong nhiều nhiệm vụ như hỏi đáp hình ảnh, nhận diện ký tự quang học, hiểu tài liệu/bảng/biểu đồ và định vị hình ảnh."}, "deepseek-chat": {"description": "Mô hình mã nguồn mở mới kết hợp khả năng tổng quát và mã, không chỉ giữ lại khả năng đối thoại tổng quát của mô hình Chat ban đầu và khả năng xử lý mã mạnh mẽ của mô hình Coder, mà còn tốt hơn trong việc phù hợp với sở thích của con người. <PERSON><PERSON><PERSON> nữa, DeepSeek-V2.5 cũng đã đạt được sự cải thiện lớn trong nhiều khía cạnh như nhiệm vụ viết, theo dõi chỉ dẫn."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B là một mô hình ngôn ngữ mã, đ<PERSON><PERSON><PERSON> đào tạo trên 20 triệu tỷ dữ liệu, trong đó 87% là mã và 13% là ngôn ngữ Trung và Anh. Mô hình này giới thiệu kích thước cửa sổ 16K và nhiệm vụ điền chỗ trống, cung cấp chức năng hoàn thành mã và điền đoạn mã ở cấp độ dự án."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 là mô hình mã nguồn mở hỗn hợp chuyên gia, thể hiện xuất sắc trong các nhiệ<PERSON> vụ mã, tương đương với GPT4-Turbo."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 là mô hình mã nguồn mở hỗn hợp chuyên gia, thể hiện xuất sắc trong các nhiệ<PERSON> vụ mã, tương đương với GPT4-Turbo."}, "deepseek-r1": {"description": "DeepSeek-R1 là một mô hình suy diễn được điều khiển bởi học tăng cườ<PERSON> (RL), gi<PERSON><PERSON> quyết các vấn đề về tính lặp lại và khả năng đọc hiểu trong mô hình. Trước khi áp dụng RL, DeepSeek-R1 đã giới thiệu dữ liệu khởi động lạnh, tối ưu hóa thêm hiệu suất suy diễn. Nó thể hiện hiệu suất tương đương với OpenAI-o1 trong các nhiệm vụ to<PERSON> h<PERSON>, mã và suy diễn, và thông qua phương pháp đào tạo được thiết kế cẩn thận, nâng cao hiệu quả tổng thể."}, "deepseek-r1-0528": {"description": "<PERSON>ô hình phiên bản đầy đủ 685 tỷ tham số, ph<PERSON><PERSON> hành ngày 28 tháng 5 năm 2025. DeepSeek-R1 sử dụng rộng rãi kỹ thuật học tăng cường trong giai đoạn huấn luyện sau, nâng cao đáng kể khả năng suy luận của mô hình dù có rất ít dữ liệu gán nhãn. Hiệu suất cao và năng lực mạnh mẽ trong các nhiệm vụ to<PERSON> h<PERSON>, l<PERSON><PERSON> tr<PERSON><PERSON>, suy luận ngôn ngữ tự nhiên."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B p<PERSON><PERSON><PERSON> b<PERSON><PERSON>, hỗ trợ tìm kiếm trực tuyến theo thời gian thực, cung cấp tốc độ phản hồi nhanh hơn trong khi vẫn giữ hiệu suất của mô hình."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B phiên bản ti<PERSON><PERSON>, hỗ trợ tìm kiếm trực tuyến theo thời gian thực, phù hợp cho các nhiệm vụ đối thoại và xử lý văn bản cần thông tin mới nhất."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama là mô hình đ<PERSON><PERSON><PERSON> chưng cất từ DeepSeek-R1 dựa trên <PERSON>."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 - mô hình lớn hơn và thông minh hơn trong bộ công cụ DeepSeek - đ<PERSON> đư<PERSON><PERSON> chưng cất vào kiến trúc Llama 70B. Dựa trên các bài kiểm tra chuẩn và đánh giá của con ng<PERSON><PERSON>i, mô hình này thông minh hơn so với Llama 70B gốc, đặc biệt xuất sắc trong các nhiệm vụ yêu cầu độ chính xác về toán học và sự thật."}, "deepseek-r1-distill-llama-8b": {"description": "<PERSON><PERSON> hình DeepSeek-R1-<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tinh chỉnh từ các mẫu do DeepSeek-R1 tạo ra cho các mô hình mã nguồn mở <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> thông qua công nghệ chưng cất kiến thức."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "<PERSON><PERSON><PERSON><PERSON> phát hành lần đầu vào ngày 14 tháng 2 năm 2025, đ<PERSON><PERSON><PERSON> chắt lọc từ mô hình cơ sở Llama3_70B (Xây dựng với Meta Llama) bởi đội ngũ phát triển mô h<PERSON><PERSON>, dữ liệu chắt lọc cũng đồng thời bổ sung tài liệu củ<PERSON>."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "<PERSON><PERSON><PERSON><PERSON> phát hành lần đầu vào ngày 14 tháng 2 năm 2025, đ<PERSON><PERSON><PERSON> chắt lọc từ mô hình cơ sở Llama3_8B (Xây dựng với Meta Llama) bởi đội ngũ phát triển mô h<PERSON><PERSON>, dữ liệu chắt lọc cũng đồng thời bổ sung tài liệu củ<PERSON>."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen là mô hình đ<PERSON><PERSON><PERSON> chưng cất từ DeepSeek-R1 dựa trên <PERSON>."}, "deepseek-r1-distill-qwen-1.5b": {"description": "<PERSON><PERSON> hình DeepSeek-R1-<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tinh chỉnh từ các mẫu do DeepSeek-R1 tạo ra cho các mô hình mã nguồn mở <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> thông qua công nghệ chưng cất kiến thức."}, "deepseek-r1-distill-qwen-14b": {"description": "<PERSON><PERSON> hình DeepSeek-R1-<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tinh chỉnh từ các mẫu do DeepSeek-R1 tạo ra cho các mô hình mã nguồn mở <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> thông qua công nghệ chưng cất kiến thức."}, "deepseek-r1-distill-qwen-32b": {"description": "<PERSON><PERSON> hình DeepSeek-R1-<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tinh chỉnh từ các mẫu do DeepSeek-R1 tạo ra cho các mô hình mã nguồn mở <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> thông qua công nghệ chưng cất kiến thức."}, "deepseek-r1-distill-qwen-7b": {"description": "<PERSON><PERSON> hình DeepSeek-R1-<PERSON><PERSON><PERSON> đư<PERSON><PERSON> tinh chỉnh từ các mẫu do DeepSeek-R1 tạo ra cho các mô hình mã nguồn mở <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> thông qua công nghệ chưng cất kiến thức."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 phiên bản n<PERSON>h đầy đủ, hỗ trợ tìm kiếm trực tuyến theo thời gian thực, kế<PERSON> h<PERSON><PERSON> sức mạnh của 671B tham số với tốc độ phản hồi nhanh hơn."}, "deepseek-r1-online": {"description": "DeepSeek R1 phiên bản đ<PERSON>y đủ, có 671B tham số, hỗ trợ tìm kiếm trực tuyến theo thời gian thực, có kh<PERSON> năng hiểu và tạo ra mạnh mẽ hơn."}, "deepseek-reasoner": {"description": "Mô hình suy diễn do DeepSeek phát triển. Tr<PERSON><PERSON><PERSON> khi đưa ra câu trả lời cuối cùng, mô hình sẽ xuất ra một đoạn nội dung chuỗi suy nghĩ để nâng cao độ chính xác của câu trả lời cuối."}, "deepseek-v2": {"description": "DeepSeek V2 là mô hình ngôn ngữ Mixture-of-Experts <PERSON><PERSON><PERSON> qu<PERSON>, phù hợp cho các nhu cầu xử lý tiết kiệm."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B là mô hình mã thiết kế của DeepSeek, cung cấp khả năng sinh mã mạnh mẽ."}, "deepseek-v3": {"description": "DeepSeek-V3 là mô hình MoE tự phát triển của Công ty Nghiên cứu <PERSON>ông nghệ AI Độ Sâu <PERSON>, có nhiều thành tích xuất sắc trong các bài kiểm tra, đứng đầu bảng xếp hạng mô hình mã nguồn mở. V3 so với mô hình V2.5 đã cải thiện tốc độ tạo ra gấp 3 lần, mang đến trải nghiệm sử dụng nhanh chóng và mượt mà hơn cho người dùng."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 là mô hình MoE với 671B tham số, nổi bật trong khả năng lập trình và kỹ thuật, hiểu ngữ cảnh và xử lý văn bản dài."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 là một mô hình hỗn hợp chuyên gia với 685B tham số, là phiên bản mới nhất trong dòng mô hình trò chuyện flagship của đội ngũ DeepSeek.\n\n<PERSON><PERSON> kế thừa mô hình [DeepSeek V3](/deepseek/deepseek-chat-v3) và thể hiện xuất sắc trong nhiều nhiệm vụ."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 là một mô hình hỗn hợp chuyên gia với 685B tham số, là phiên bản mới nhất trong dòng mô hình trò chuyện flagship của đội ngũ DeepSeek.\n\n<PERSON><PERSON> kế thừa mô hình [DeepSeek V3](/deepseek/deepseek-chat-v3) và thể hiện xuất sắc trong nhiều nhiệm vụ."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 đã nâng cao khả năng suy luận của mô hình một cách đáng kể với rất ít dữ liệu được gán nhãn. Tr<PERSON>ớ<PERSON> khi đưa ra câu trả lời cuối cùng, mô hình sẽ xuất ra một chuỗi suy nghĩ để nâng cao độ chính xác của câu trả lời cuối cùng."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 đã cải thiện đáng kể khả năng suy luận của mô hình ngay cả khi có rất ít dữ liệu gán nhãn. Trước khi đưa ra câu trả lời cuối cùng, mô hình sẽ xuất ra một chuỗi suy nghĩ nhằm nâng cao độ chính xác của câu trả lời cuối."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 đã cải thiện đáng kể khả năng suy luận của mô hình ngay cả khi có rất ít dữ liệu gán nhãn. Trước khi đưa ra câu trả lời cuối cùng, mô hình sẽ xuất ra một chuỗi suy nghĩ nhằm nâng cao độ chính xác của câu trả lời cuối."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B là mô hình ngôn ngữ lớn dựa trên Llama3.3 70B, mô hình này sử dụng đầu ra tinh chỉnh từ DeepSeek R1 để đạt được hiệu suất cạnh tranh tương đương với các mô hình tiên tiến lớn."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B là một mô hình ngôn ngữ lớn đã đư<PERSON><PERSON> tinh chế dựa trên Llama-3.1-8B-Instruct, đ<PERSON><PERSON><PERSON> đào tạo bằng cách sử dụng đầu ra từ DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill <PERSON><PERSON> 14B là một mô hình ngôn ngữ lớn đã được tinh chế dựa trên <PERSON>wen 2.5 14B, đư<PERSON><PERSON> đào tạo bằng cách sử dụng đầu ra từ DeepSeek R1. <PERSON>ô hình này đã vượt qua o1-mini của OpenAI trong nhiều bài kiểm tra chuẩn, đạt được những thành tựu công nghệ tiên tiến nhất trong các mô hình dày đặc (dense models). Dưới đây là một số kết quả từ các bài kiểm tra chuẩn:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nMô hình này đã thể hiện hiệu suất cạnh tranh tương đương với các mô hình tiên tiến lớn hơn thông qua việc tinh chỉnh từ đầu ra của DeepSeek R1."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill <PERSON><PERSON> 32B là một mô hình ngôn ngữ lớn đã được tinh chế dựa trên <PERSON>wen 2.5 32B, đư<PERSON><PERSON> đào tạo bằng cách sử dụng đầu ra từ DeepSeek R1. <PERSON>ô hình này đã vượt qua o1-mini của OpenAI trong nhiều bài kiểm tra chuẩn, đạt được những thành tựu công nghệ tiên tiến nhất trong các mô hình dày đặc (dense models). Dưới đây là một số kết quả từ các bài kiểm tra chuẩn:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nMô hình này đã thể hiện hiệu suất cạnh tranh tương đương với các mô hình tiên tiến lớn hơn thông qua việc tinh chỉnh từ đầu ra của DeepSeek R1."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 là mô hình mã nguồn mở mới nhất được phát hành bởi đội ngũ DeepSeek, có hiệu suất suy diễn rất mạnh mẽ, đặc biệt trong các nhiệm vụ to<PERSON>, lậ<PERSON> trình và suy luận, đ<PERSON><PERSON> đư<PERSON><PERSON> mức độ tương đương với mô hình o1 của OpenAI."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 đã nâng cao khả năng suy luận của mô hình một cách đáng kể với rất ít dữ liệu được gán nhãn. Tr<PERSON>ớ<PERSON> khi đưa ra câu trả lời cuối cùng, mô hình sẽ xuất ra một chuỗi suy nghĩ để nâng cao độ chính xác của câu trả lời cuối cùng."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 đã đạt được bước đột phá lớn về tốc độ suy diễn so với các mô hình trước đó. Nó đứng đầu trong số các mô hình mã nguồn mở và có thể so sánh với các mô hình đóng nguồn tiên tiến nhất trên toàn cầu. DeepSeek-V3 sử dụng kiến trúc Attention đa đ<PERSON><PERSON> (MLA) và DeepSeekMoE, những kiến trúc này đã được xác thực toàn diện trong DeepSeek-V2. <PERSON><PERSON><PERSON> nữa, DeepSeek-V3 đã sáng tạo ra một chiến lược phụ trợ không mất mát cho cân bằng tải và thiết lập mục tiêu đào tạo dự đoán đa nhãn để đạt được hiệu suất mạnh mẽ hơn."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 đã đạt được bước đột phá lớn về tốc độ suy diễn so với các mô hình trước đó. Nó đứng đầu trong số các mô hình mã nguồn mở và có thể so sánh với các mô hình đóng nguồn tiên tiến nhất trên toàn cầu. DeepSeek-V3 sử dụng kiến trúc Attention đa đ<PERSON><PERSON> (MLA) và DeepSeekMoE, những kiến trúc này đã được xác thực toàn diện trong DeepSeek-V2. <PERSON><PERSON><PERSON> nữa, DeepSeek-V3 đã sáng tạo ra một chiến lược phụ trợ không mất mát cho cân bằng tải và thiết lập mục tiêu đào tạo dự đoán đa nhãn để đạt được hiệu suất mạnh mẽ hơn."}, "deepseek_r1": {"description": "DeepSeek-R1 là một mô hình suy luận được điều khiển bởi học tăng cườ<PERSON> (RL), gi<PERSON>i quyết các vấn đề về tính lặp lại và khả năng đọc hiểu trong mô hình. Trước khi áp dụng RL, DeepSeek-R1 đã giới thiệu dữ liệu khởi động lạnh, tối ưu hóa thêm hiệu suất suy luận. Nó thể hiện hiệu suất tương đương với OpenAI-o1 trong các nhiệm vụ to<PERSON> h<PERSON>, mã và suy luận, và đã nâng cao hiệu quả tổng thể thông qua phương pháp huấn luyện được thiết kế cẩn thận."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B là mô hình được phát triển từ Llama-3.3-70B-Instruct thông qua quá trình tinh chế. Mô hình này là một phần của dòng DeepSeek-R1, thể hiện hiệu suất xuất sắc trong nhiều lĩnh vực như to<PERSON> h<PERSON>, lập trình và suy luận thông qua việc tinh chỉnh bằng các mẫu được tạo ra từ DeepSeek-R1."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B là mô hình được phát triển từ Qwen2.5-14B thông qua quá trình tinh chế kiến thức. <PERSON>ô hình này được tinh chỉnh bằng 800.000 mẫu được chọn từ DeepSeek-R1, thể hiện khả năng suy luận xuất sắc."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B là mô hình được phát triển từ Qwen2.5-32B thông qua quá trình tinh chế kiến thức. <PERSON>ô hình này được tinh chỉnh bằng 800.000 mẫu được chọn từ DeepSeek-R1, thể hiện hiệu suất xuất sắc trong nhiều lĩnh vực nh<PERSON> to<PERSON> họ<PERSON>, lập trình và suy luận."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite là mô hình phiên bản nhẹ thế hệ mới, tốc độ phản hồi cự<PERSON>, hiệu quả và độ trễ đạt tiêu chuẩn hàng đầu thế giới."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k là phiên bản nâng cấp toàn diện dựa trên <PERSON>-1.5-<PERSON>, hi<PERSON><PERSON> quả tổng thể tăng 10%. Hỗ trợ suy luận với cửa sổ ngữ cảnh 256k, độ dài đầu ra tối đa lên đến 12k tokens. Hi<PERSON><PERSON> su<PERSON> cao hơ<PERSON>, c<PERSON><PERSON> sổ lớn hơn, gi<PERSON> trị vư<PERSON><PERSON> trộ<PERSON>, phù hợp với nhiều ứng dụng khác nhau."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro là mô hình chủ lực thế hệ mới, hi<PERSON><PERSON> suất đ<PERSON><PERSON><PERSON> nâng cấp to<PERSON><PERSON>, thể hiện xu<PERSON><PERSON> sắ<PERSON> trong các lĩnh vự<PERSON> k<PERSON><PERSON> thứ<PERSON>, mã ngu<PERSON>, <PERSON><PERSON> l<PERSON>, và nhiều hơn n<PERSON>a."}, "doubao-1.5-thinking-pro": {"description": "<PERSON><PERSON> hình tư duy sâu mới <PERSON>-1.5, nổi bật trong các lĩnh vực chuyên môn nh<PERSON> to<PERSON>, l<PERSON><PERSON> <PERSON>r<PERSON><PERSON>, suy luận khoa học và các nhiệm vụ viế<PERSON> sán<PERSON>, đạt hoặc gần đạt trình độ hàng đầu trong ngành trên nhiều tiêu chuẩn uy tín như AIME 2024, Codeforces, GPQA. Hỗ trợ cửa sổ ngữ cảnh 128k, đầu ra 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 là mô hình tư duy sâu hoàn toàn mới (phi<PERSON><PERSON> bản m có khả năng suy luận đa phương thức sâu nguyên bản), thể hiện xuất sắc trong các lĩnh vực chuyên môn nh<PERSON> to<PERSON>, <PERSON><PERSON><PERSON>, suy luận khoa học và các nhiệm vụ sáng tạo chung. Đạt hoặc gần đạt trình độ hàng đầu ngành trên nhiều chuẩn đánh giá uy tín như AIME 2024, Codeforces, GPQA. Hỗ trợ cửa sổ ngữ cảnh 128k, đầu ra 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "<PERSON><PERSON> hình tư duy sâu đa phương thức hoàn toàn mới, có khả năng hiểu và suy luận đa phương thức tổng quát mạnh mẽ, đạt hiệu suất SOTA trên 37 trong số 59 chuẩn đánh giá công khai."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS là mô hình Agent nguyên bản hướng tới tương tác giao diện đồ họa (GUI). Thông qua khả năng nhậ<PERSON> thứ<PERSON>, su<PERSON> luận và hành động giống con ngư<PERSON>i, tư<PERSON><PERSON> tác liền mạch với GUI."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite là mô hình đa phương tiện lớn đư<PERSON><PERSON> nâng cấp mới, hỗ trợ nhận diện hình ảnh với bất kỳ độ phân giải nào và tỷ lệ dài rộng cực đoan, tăng cường khả năng suy luận hình ảnh, nhận diện tài liệu, hiểu thông tin chi tiết và tuân thủ hướng dẫn. Hỗ trợ cửa sổ ngữ cảnh 128k, độ dài đầu ra tối đa 16k tokens."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro là mô hình đa phương thức lớn đư<PERSON><PERSON> nâng cấp hoàn toàn mới, hỗ trợ nhận dạng hình ảnh với độ phân giải tùy ý và tỷ lệ khung hình cực đoan, tăng cường khả năng suy luận thị giác, nhận dạng tài liệu, hiểu thông tin chi tiết và tuân thủ chỉ dẫn."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro là mô hình đa phương thức lớn đư<PERSON><PERSON> nâng cấp hoàn toàn mới, hỗ trợ nhận dạng hình ảnh với độ phân giải tùy ý và tỷ lệ khung hình cực đoan, tăng cường khả năng suy luận thị giác, nhận dạng tài liệu, hiểu thông tin chi tiết và tuân thủ chỉ dẫn."}, "doubao-lite-128k": {"description": "Sở hữu tốc độ phản hồi tối <PERSON>u, hi<PERSON><PERSON> quả chi phí tốt hơn, cung cấp lựa chọn linh hoạt hơn cho các kịch bản khác nhau của khách hàng. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 128k."}, "doubao-lite-32k": {"description": "Sở hữu tốc độ phản hồi tối <PERSON>u, hi<PERSON><PERSON> quả chi phí tốt hơn, cung cấp lựa chọn linh hoạt hơn cho các kịch bản khác nhau của khách hàng. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 32k."}, "doubao-lite-4k": {"description": "Sở hữu tốc độ phản hồi tối <PERSON>u, hi<PERSON><PERSON> quả chi phí tốt hơn, cung cấp lựa chọn linh hoạt hơn cho các kịch bản khác nhau của khách hàng. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 4k."}, "doubao-pro-256k": {"description": "<PERSON><PERSON> hình chủ lực với hiệu quả tốt nhất, phù hợp xử lý các nhiệm vụ phức tạp, có hiệu quả xuất sắc trong các kịch bản như hỏi đáp tham <PERSON>, t<PERSON><PERSON>, s<PERSON><PERSON>, phân lo<PERSON> vă<PERSON> bả<PERSON>, nh<PERSON><PERSON> vai. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 256k."}, "doubao-pro-32k": {"description": "<PERSON><PERSON> hình chủ lực với hiệu quả tốt nhất, phù hợp xử lý các nhiệm vụ phức tạp, có hiệu quả xuất sắc trong các kịch bản như hỏi đáp tham <PERSON>, t<PERSON><PERSON>, s<PERSON><PERSON>, phân lo<PERSON> vă<PERSON> bả<PERSON>, nh<PERSON><PERSON> vai. Hỗ trợ suy luận và tinh chỉnh với cửa sổ ngữ cảnh 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 là mô hình suy nghĩ sâu đa phương thức hoàn toàn mới, hỗ trợ ba chế độ suy nghĩ auto/thinking/non-thinking. Ở chế độ non-thinking, hiệu quả mô hình cải thiện đáng kể so với Doubao-1.5-pro/250115. Hỗ trợ cửa sổ ngữ cảnh 256k, độ dài đầu ra tối đa 16k tokens."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash là mô hình suy nghĩ sâu đa phương thức với tốc độ suy luận tối ưu, TPOT chỉ cần 10ms; đồng thời hỗ trợ hiểu văn bản và hình ảnh, khả năng hiểu văn bản vượt trội so với thế hệ lite trước, khả năng hiểu hình ảnh sánh ngang với các mô hình pro của đối thủ. Hỗ trợ cửa sổ ngữ cảnh 256k, độ dài đầu ra tối đa 16k tokens."}, "doubao-seed-1.6-thinking": {"description": "<PERSON><PERSON>-Seed-1.6-thinking có khả năng suy nghĩ được tăng cường đáng kể, so với <PERSON>-1.5-thinking-pro, nâng cao hơn nữa các năng lực cơ bản nh<PERSON> lậ<PERSON> tr<PERSON>, to<PERSON>, su<PERSON> luận logic, đồng thời hỗ trợ hiểu hình ảnh. Hỗ trợ cửa sổ ngữ cảnh 256k, độ dài đầu ra tối đa 16k tokens."}, "doubao-seedream-3-0-t2i-250415": {"description": "<PERSON>ô hình tạo hình ảnh Doubao do đội Seed của ByteDance phát triển, hỗ trợ đầu vào văn bản và hình ảnh, mang lại trải nghiệm tạo hình ảnh chất lượng cao và kiểm soát tốt. Tạo hình ảnh dựa trên từ khóa văn bản."}, "doubao-vision-lite-32k": {"description": "<PERSON><PERSON> hình Doubao-vision là mô hình đa phương thức lớn do Doubao phát triển, có khả năng hiểu và suy luận hình ảnh mạnh mẽ, cùng khả năng hiểu chỉ dẫn chính xác. Mô hình thể hiện hiệu suất vượt trội trong việc trích xuất thông tin văn bản từ hình ảnh và các nhiệm vụ suy luận dựa trên hình ảnh, có thể ứng dụng trong các nhiệm vụ hỏi đáp thị giác phức tạp và đa dạng hơn."}, "doubao-vision-pro-32k": {"description": "<PERSON><PERSON> hình Doubao-vision là mô hình đa phương thức lớn do Doubao phát triển, có khả năng hiểu và suy luận hình ảnh mạnh mẽ, cùng khả năng hiểu chỉ dẫn chính xác. Mô hình thể hiện hiệu suất vượt trội trong việc trích xuất thông tin văn bản từ hình ảnh và các nhiệm vụ suy luận dựa trên hình ảnh, có thể ứng dụng trong các nhiệm vụ hỏi đáp thị giác phức tạp và đa dạng hơn."}, "emohaa": {"description": "<PERSON><PERSON><PERSON> là mô hình tâm lý, c<PERSON> kh<PERSON> năng tư vấn chuyên nghi<PERSON>, gi<PERSON><PERSON> người dùng hiểu các vấn đề cảm xúc."}, "ernie-3.5-128k": {"description": "<PERSON>ô hình ngôn ngữ lớn quy mô lớn tự phát triển củ<PERSON>, bao phủ một lượng lớn tài liệu tiếng Trung và tiếng <PERSON>, c<PERSON> khả năng tổng quát mạnh mẽ, đ<PERSON><PERSON> <PERSON><PERSON> hầu hết các yêu cầu về đối thoại hỏi đáp, tạ<PERSON> nội dung, và ứng dụng plugin; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời."}, "ernie-3.5-8k": {"description": "<PERSON>ô hình ngôn ngữ lớn quy mô lớn tự phát triển củ<PERSON>, bao phủ một lượng lớn tài liệu tiếng Trung và tiếng <PERSON>, c<PERSON> khả năng tổng quát mạnh mẽ, đ<PERSON><PERSON> <PERSON><PERSON> hầu hết các yêu cầu về đối thoại hỏi đáp, tạ<PERSON> nội dung, và ứng dụng plugin; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời."}, "ernie-3.5-8k-preview": {"description": "<PERSON>ô hình ngôn ngữ lớn quy mô lớn tự phát triển củ<PERSON>, bao phủ một lượng lớn tài liệu tiếng Trung và tiếng <PERSON>, c<PERSON> khả năng tổng quát mạnh mẽ, đ<PERSON><PERSON> <PERSON><PERSON> hầu hết các yêu cầu về đối thoại hỏi đáp, tạ<PERSON> nội dung, và ứng dụng plugin; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời."}, "ernie-4.0-8k-latest": {"description": "<PERSON>ô hình ngôn ngữ lớn siêu quy mô tự phát triển của <PERSON>, so với ERNIE 3.5 đã thực hiện nâng cấp toàn diện về khả năng mô hình, phù hợp rộng rãi với các tình huống nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời."}, "ernie-4.0-8k-preview": {"description": "<PERSON>ô hình ngôn ngữ lớn siêu quy mô tự phát triển của <PERSON>, so với ERNIE 3.5 đã thực hiện nâng cấp toàn diện về khả năng mô hình, phù hợp rộng rãi với các tình huống nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời."}, "ernie-4.0-turbo-128k": {"description": "<PERSON>ô hình ngôn ngữ lớn siêu quy mô tự phát triển củ<PERSON>, có hiệu suất tổng thể xuất sắc, phù hợp rộng rãi với các tình huống nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời. So với ERNIE 4.0, hiệu suất tốt hơn."}, "ernie-4.0-turbo-8k-latest": {"description": "<PERSON>ô hình ngôn ngữ lớn siêu quy mô tự phát triển củ<PERSON>, có hiệu suất tổng thể xuất sắc, phù hợp rộng rãi với các tình huống nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời. So với ERNIE 4.0, hiệu suất tốt hơn."}, "ernie-4.0-turbo-8k-preview": {"description": "<PERSON>ô hình ngôn ngữ lớn siêu quy mô tự phát triển củ<PERSON>, có hiệu suất tổng thể xuất sắc, phù hợp rộng rãi với các tình huống nhiệm vụ phức tạp trong nhiều lĩnh vực; hỗ trợ tự động kết nối với plugin tìm kiếm củ<PERSON>, đảm bảo thông tin hỏi đáp kịp thời. So với ERNIE 4.0, hiệu suất tốt hơn."}, "ernie-4.5-8k-preview": {"description": "<PERSON>ô hình lớn văn tâm 4.5 là thế hệ mới của mô hình nền tảng đa phương tiện tự phát triển c<PERSON><PERSON>, đạt được tối ưu hóa hợp tác thông qua mô hình hóa đa phư<PERSON><PERSON> tiệ<PERSON>, c<PERSON> khả năng hiểu đa phương tiện xuất sắc; c<PERSON> khả năng ngôn ngữ tinh vi hơn, kh<PERSON> năng hiểu, t<PERSON><PERSON> ra, logic và ghi nhớ được cải thiện toà<PERSON> diện, g<PERSON><PERSON><PERSON> <PERSON><PERSON>, kh<PERSON> năng suy luận logic và mã được nâng cao rõ rệt."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo có sự cải thiện rõ rệt trong việc gi<PERSON>m ảo <PERSON>, suy luận logic và khả năng lập trình. So với Wenxin 4.5, tố<PERSON> độ nhanh hơn và giá cả thấp hơn. <PERSON><PERSON><PERSON> năng của mô hình được nâng cao toàn diện, đá<PERSON> <PERSON>ng tốt hơn cho việc xử lý đối thoại dài với nhiều vòng và nhiệm vụ hiểu biết hỏi đáp tài liệu dài."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo cũng có sự cải thiện rõ rệt trong việc gi<PERSON>m ảo <PERSON>, suy luận logic và khả năng lập trình. So với Wenxin 4.5, tốc độ nhanh hơn và giá cả thấp hơn. <PERSON><PERSON><PERSON> năng sáng tác văn bả<PERSON>, hỏi đáp kiến thức được nâng cao đáng kể. Độ dài đầu ra và độ trễ câu hoàn chỉnh so với ERNIE 4.5 có sự gia tăng."}, "ernie-4.5-turbo-vl-32k": {"description": "<PERSON><PERSON><PERSON> bản hoàn toàn mới của mô hình lớn <PERSON>, <PERSON><PERSON><PERSON> năng hiểu, s<PERSON><PERSON>, d<PERSON><PERSON> thu<PERSON>t và lập trình đư<PERSON><PERSON> cải thiện đáng kể, lần đầu tiên hỗ trợ độ dài ngữ cảnh 32K, độ trễ của token đầu tiên giảm đáng kể."}, "ernie-char-8k": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn theo ngữ cảnh tự phát triển c<PERSON><PERSON>, phù hợp cho các <PERSON>ng dụng như NPC trong tr<PERSON> ch<PERSON>, đ<PERSON><PERSON> tho<PERSON>i dịch vụ kh<PERSON>ch hà<PERSON>, và vai trò trong đối tho<PERSON>, có phong cách nhân vật rõ ràng và nhất quán, kh<PERSON> năng tuân theo lệnh mạnh mẽ, hiệu suất suy luận tốt hơn."}, "ernie-char-fiction-8k": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn theo ngữ cảnh tự phát triển c<PERSON><PERSON>, phù hợp cho các <PERSON>ng dụng như NPC trong tr<PERSON> ch<PERSON>, đ<PERSON><PERSON> tho<PERSON>i dịch vụ kh<PERSON>ch hà<PERSON>, và vai trò trong đối tho<PERSON>, có phong cách nhân vật rõ ràng và nhất quán, kh<PERSON> năng tuân theo lệnh mạnh mẽ, hiệu suất suy luận tốt hơn."}, "ernie-irag-edit": {"description": "<PERSON>ô hình chỉnh sửa hình ảnh ERNIE iRAG do Baidu tự phát triển hỗ trợ các thao tác như xóa (erase), tô lạ<PERSON> (repaint), tạo biến thể (variation) dựa trên hình ảnh."}, "ernie-lite-8k": {"description": "ERNIE Lite là mô hình ngôn ngữ lớn nhẹ tự phát triển củ<PERSON>, kết hợp hiệu suất mô hình xuất sắc với hiệu suất suy luận, phù hợp cho việc sử dụng trên thẻ tăng tốc AI với công suất thấp."}, "ernie-lite-pro-128k": {"description": "<PERSON>ô hình ngôn ngữ lớn nhẹ tự phát triển củ<PERSON>, kết hợp hiệu suất mô hình xuất sắc với hiệu suất suy luận, hiệ<PERSON> suất tốt hơn <PERSON><PERSON><PERSON>, phù hợp cho việc sử dụng trên thẻ tăng tốc AI với công suất thấp."}, "ernie-novel-8k": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn tổng quát tự phát triển c<PERSON><PERSON>, có lợi thế rõ rệt trong khả năng viết tiế<PERSON> ti<PERSON><PERSON> thuyế<PERSON>, cũng có thể được sử dụng trong các tình huống như kịch ngắn, phim <PERSON><PERSON>."}, "ernie-speed-128k": {"description": "<PERSON>ô hình ngôn ngữ lớn hiệu suất cao tự phát triển củ<PERSON>, <PERSON><PERSON><PERSON><PERSON> phát hành vào năm 2024, c<PERSON> kh<PERSON> năng tổng quát xu<PERSON><PERSON> sắ<PERSON>, phù hợp làm mô hình nền để tinh chỉnh, xử lý tốt hơn các vấn đề trong tình huống cụ thể, đồng thời có hiệu suất suy luận xuất sắc."}, "ernie-speed-pro-128k": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn hiệu suất cao tự phát triển c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ph<PERSON>t hành vào năm 2024, c<PERSON> kh<PERSON> năng tổng quát xu<PERSON><PERSON> sắ<PERSON>, hi<PERSON><PERSON> suất tốt hơn ERNI<PERSON>, phù hợp làm mô hình nền để tinh chỉnh, xử lý tốt hơn các vấn đề trong tình huống cụ thể, đồng thời có hiệu suất suy luận xuất sắc."}, "ernie-tiny-8k": {"description": "ERNIE Tiny là mô hình ngôn ngữ lớn hiệu suất siêu cao tự phát triển củ<PERSON>, có chi phí triển khai và tinh chỉnh thấp nhất trong dòng sản phẩm văn tâm."}, "ernie-x1-32k": {"description": "<PERSON><PERSON> năng hiểu, l<PERSON><PERSON> <PERSON>, ph<PERSON><PERSON> ánh và tiến hóa mạnh mẽ hơn. <PERSON><PERSON> một mô hình tư duy sâu sắc toàn diện hơn, Wenxin X1 kết hợp đ<PERSON> ch<PERSON>, sự sáng tạo và văn phong, thể hiện xuất sắc trong các lĩnh vực như hỏi đáp kiến thức tiếng <PERSON>rung, sán<PERSON> tá<PERSON> vă<PERSON> họ<PERSON>, viế<PERSON> tà<PERSON> li<PERSON>, đố<PERSON> tho<PERSON>i hàng ngày, suy luận logic, t<PERSON>h toán phức tạp và gọi công cụ."}, "ernie-x1-32k-preview": {"description": "<PERSON>ô hình lớn Wenxin X1 có khả năng hiểu, l<PERSON><PERSON> <PERSON><PERSON>, phản ánh và tiến hóa mạnh mẽ hơn. <PERSON><PERSON> một mô hình tư duy sâu sắc toàn di<PERSON> hơn, Wenxin X1 kết hợp độ ch<PERSON>h xác, sự sáng tạo và văn phong, đặc biệt xuất sắc trong các lĩnh vực như hỏi đáp kiến thức tiếng Trung, sáng tác văn học, viế<PERSON> tà<PERSON> li<PERSON>, đối thoại hàng ngày, suy luận logic, t<PERSON>h toán phức tạp và gọi công cụ."}, "ernie-x1-turbo-32k": {"description": "So với ERNIE-X1-32K, mô hình này có hiệu suất và hiệu quả tốt hơn."}, "flux-1-schnell": {"description": "<PERSON><PERSON> hình tạo hình ảnh từ văn bản 12 tỷ tham số do Black Forest Labs phát triển, sử dụng kỹ thuật chưng cất khuếch tán đối kháng tiềm <PERSON>, có thể tạo hình ảnh chất lượng cao trong 1 đến 4 bước. <PERSON><PERSON> hình có hiệu suất tương đương các sản phẩm đóng nguồn và được phát hành dưới giấy phép Apache-2.0, phù hợp cho cá nhân, nghi<PERSON><PERSON> cứ<PERSON> và thương mại."}, "flux-dev": {"description": "FLUX.1 [dev] là mô hình tinh luyện mã nguồn mở dành cho ứng dụng phi thương mại. FLUX.1 [dev] duy trì chất lượng hình ảnh và khả năng tuân thủ chỉ dẫn gần tương đương phiên bản chuyên nghiệp FLUX, đồng thời có hiệu suất vận hành cao hơn. So với mô hình chuẩn cùng kích thước, nó sử dụng tài nguyên hiệu qu<PERSON> hơn."}, "flux-kontext/dev": {"description": "<PERSON><PERSON> hình chỉnh sửa hình ảnh Frontier."}, "flux-merged": {"description": "<PERSON><PERSON> hình FLUX.1-merged kết hợp các đặc tính sâu sắc được khám phá trong giai đoạn phát triển của \"DEV\" và ưu thế thực thi nhanh của \"Schnell\". <PERSON><PERSON> đ<PERSON>, FLUX.1-merged không chỉ nâng cao giới hạn hiệu suất mà còn mở rộng phạm vi ứng dụng."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] c<PERSON> khả năng xử lý văn bản và hình ảnh tham khảo làm đầu vào, thực hiện chỉnh sửa cục bộ có mục tiêu và biến đổi cảnh tổng thể phức tạp một cách liền mạch."}, "flux-schnell": {"description": "FLUX.1 [schnell] là mô hình ít bước tiên tiến nhất mã nguồn mở hiện nay, vư<PERSON>t trội so với các đối thủ cùng loại và thậm chí hơn cả các mô hình không tinh luyện mạnh như Midjourney v6.0 và DALL·E 3 (HD). Mô hình được tinh chỉnh đặc biệt để giữ lại toàn bộ đa dạng đầu ra giai đoạn tiền huấn luyện, so với các mô hình tiên tiến trên thị trường, FLUX.1 [schnell] cải thiện đáng kể chất lượng hình ảnh, tuân thủ chỉ dẫn, thay đổi kích thước/tỷ lệ, xử lý phông chữ và đa dạng đầu ra, mang đến trải nghiệm tạo hình ảnh sáng tạo phong phú hơn cho người dùng."}, "flux.1-schnell": {"description": "Bộ biến đổi luồng hiệu chỉnh với 12 tỷ tham số, có khả năng tạo hình ảnh dựa trên mô tả văn bản."}, "flux/schnell": {"description": "FLUX.1 [schnell] là mô hình bộ chuyển đổi dòng với 12 tỷ tham số, có thể tạo ra hình ảnh chất lượng cao từ văn bản trong 1 đế<PERSON> 4 b<PERSON><PERSON><PERSON>, phù hợp cho mục đích cá nhân và thương mại."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning) cung cấp hiệu suất <PERSON>n định và có thể điều chỉnh, là lựa chọn lý tưởng cho các gi<PERSON>i pháp nhiệm vụ phức tạp."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning) cung cấp hỗ trợ đa phươ<PERSON> thức xuất sắc, tập trung vào việc gi<PERSON>i quyết hiệu quả các nhiệm vụ phức tạp."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro là mô hình AI hiệu suất cao của Google, <PERSON><PERSON><PERSON><PERSON> thiết kế để mở rộng cho nhiều nhiệm vụ."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 là một mô hình đa ph<PERSON><PERSON><PERSON> thức hiệu quả, hỗ trợ mở rộng cho nhiều ứng dụng."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 là một mô hình đa ph<PERSON><PERSON><PERSON> thức hiệu quả, hỗ trợ mở rộng cho nhiều ứng dụng."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B là một mô hình đa ph<PERSON><PERSON><PERSON> thức hiệu quả, hỗ trợ mở rộng cho nhiều ứng dụng."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 là mô hình thử nghiệm mớ<PERSON> nh<PERSON>, có sự cải thiện đáng kể về hiệu suất trong các trường hợp sử dụng văn bản và đa phương thức."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B là một mô hình đa chế độ hiệu quả, hỗ trợ mở rộng ứng dụng rộng rãi."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 cung cấp kh<PERSON> năng xử lý đa phương tiện tố<PERSON>, <PERSON><PERSON> dụng cho nhiều tình huống tác vụ phức tạp."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash là mô hình AI đa phương thức mới nhất của Google, c<PERSON> khả năng xử lý n<PERSON>h, hỗ trợ đầu vào văn bản, hình ảnh và video, phù hợp cho việc mở rộng hiệu quả cho nhiều nhiệm vụ."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 là gi<PERSON>i pháp AI đa phươ<PERSON> thức có thể mở rộng, hỗ trợ nhiều nhiệm vụ phức tạp."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 là mô hình sẵn sàng cho sản xuất mớ<PERSON> nhất, cung cấp đầu ra chất lư<PERSON><PERSON> cao hơn, đặc biệt là trong các nhiệm vụ to<PERSON>, ngữ cảnh dài và thị gi<PERSON><PERSON>."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 cung cấp kh<PERSON> năng xử lý đa phương tiện xu<PERSON>t sắ<PERSON>, mang lại t<PERSON>h linh hoạt cao hơn cho việc phát triển ứng dụng."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 kết hợp công nghệ tối ưu hóa mới nhất, mang lại khả năng xử lý dữ liệu đa phương tiện hiệu quả hơn."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro hỗ trợ lên đến 2 triệu tokens, là lựa chọn lý tưởng cho mô hình đa phương thức trung bình, phù hợp cho hỗ trợ đa diện cho các nhiệm vụ phức tạp."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash cung cấp các t<PERSON>h năng và cải tiến thế hệ tiếp theo, bao gồm tốc độ v<PERSON><PERSON><PERSON> tr<PERSON>, sử dụng công cụ bản địa, tạo đa phương tiện và cửa sổ ngữ cảnh 1M token."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash cung cấp các t<PERSON>h năng và cải tiến thế hệ tiếp theo, bao gồm tốc độ v<PERSON><PERSON><PERSON> tr<PERSON>, sử dụng công cụ bản địa, tạo đa phương tiện và cửa sổ ngữ cảnh 1M token."}, "gemini-2.0-flash-exp": {"description": "<PERSON><PERSON><PERSON><PERSON> thể mô hình Gemini 2.0 Flash, <PERSON><PERSON><PERSON><PERSON> tối ưu hóa cho hiệu quả chi phí và độ trễ thấp."}, "gemini-2.0-flash-exp-image-generation": {"description": "<PERSON><PERSON> hình thử nghiệm Gemini 2.0 Flash, hỗ trợ tạo hình <PERSON>nh"}, "gemini-2.0-flash-lite": {"description": "<PERSON><PERSON><PERSON><PERSON> thể mô hình Gemini 2.0 <PERSON> <PERSON><PERSON><PERSON><PERSON> tối ưu hóa cho hiệu quả chi phí và độ trễ thấp."}, "gemini-2.0-flash-lite-001": {"description": "<PERSON><PERSON><PERSON><PERSON> thể mô hình Gemini 2.0 <PERSON> <PERSON><PERSON><PERSON><PERSON> tối ưu hóa cho hiệu quả chi phí và độ trễ thấp."}, "gemini-2.0-flash-preview-image-generation": {"description": "<PERSON><PERSON> hình xem trước Gemini 2.0 Flash, hỗ trợ tạo hình ảnh"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash là mô hình có hiệu suất chi phí tốt nhất củ<PERSON>, cung cấp đầy đủ các chức năng."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-<PERSON><PERSON> là mô hình nhỏ nhất và có hiệu suất chi phí tốt nhất của <PERSON>, đ<PERSON><PERSON><PERSON> thiết kế dành cho việc sử dụng quy mô lớn."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview là mô hình nhỏ nhất và có hiệu suất chi phí tốt nhất của Google, <PERSON><PERSON><PERSON><PERSON> thiết kế dành cho sử dụng quy mô lớn."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview là mô hình có giá trị tốt nhất của <PERSON>, cung cấp đầy đủ các t<PERSON>h năng."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview là mô hình có hiệu suất chi phí tốt nhất của Google, cung cấp các t<PERSON>h năng to<PERSON>n <PERSON>."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro là mô hình tư duy tiên tiến nhất của Google, có khả năng suy luận các vấn đề phức tạp trong lĩnh vực mã nguồn, to<PERSON> và STEM, cũ<PERSON> nh<PERSON> phân tích các bộ dữ liệu lớn, kho mã và tài liệu bằng ngữ cảnh dài."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview là mô hình tư duy tiên tiến nhất của Google, c<PERSON> khả năng suy luận về mã, to<PERSON> học và các vấn đề phức tạp trong lĩnh vực STEM, cũ<PERSON> nh<PERSON> phân tích các tập dữ liệu lớn, kho mã và tài liệu bằng cách sử dụng ngữ cảnh dài."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview là mô hình tư duy tiên tiến nhất của Google, c<PERSON> khả năng suy luận về mã, to<PERSON> học và các vấn đề phức tạp trong lĩnh vực STEM, cũ<PERSON> nh<PERSON> phân tích các tập dữ liệu lớn, kho mã và tài liệu bằng cách sử dụng ngữ cảnh dài."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview là mô hình tư duy tiên tiến nhất của Google, có khả năng suy luận các vấn đề phức tạp trong lĩnh vực mã nguồn, to<PERSON> và STEM, c<PERSON><PERSON> nh<PERSON> phân tích dữ liệu lớn, kho mã và tài liệu với ngữ cảnh dài."}, "gemma-7b-it": {"description": "Gemma 7B phù hợp cho việc xử lý các nhiệm vụ quy mô vừa và nhỏ, đồng thời mang lại hiệu quả chi phí."}, "gemma2": {"description": "Gemma 2 là mô hình hiệu quả do Google ph<PERSON><PERSON> hành, bao gồm nhiều ứng dụng từ nhỏ đến xử lý dữ liệu phức tạp."}, "gemma2-9b-it": {"description": "Gemma 2 9B là một mô hình được tối ưu hóa cho các nhiệm vụ cụ thể và tích hợp công cụ."}, "gemma2:27b": {"description": "Gemma 2 là mô hình hiệu quả do Google ph<PERSON><PERSON> hành, bao gồm nhiều ứng dụng từ nhỏ đến xử lý dữ liệu phức tạp."}, "gemma2:2b": {"description": "Gemma 2 là mô hình hiệu quả do Google ph<PERSON><PERSON> hành, bao gồm nhiều ứng dụng từ nhỏ đến xử lý dữ liệu phức tạp."}, "generalv3": {"description": "Spark Pro là một mô hình ngôn ngữ lớn hiệu suất cao được tối ưu hóa cho các lĩnh vực chuyên môn, tập trung vào to<PERSON> họ<PERSON>, l<PERSON><PERSON> tr<PERSON><PERSON>, y tế, g<PERSON><PERSON><PERSON> dụ<PERSON> và nhiều lĩnh vự<PERSON>, đồng thời hỗ trợ tìm kiếm trực tuyến và các plugin tích hợp như thời tiết, ng<PERSON><PERSON> tháng. Mô hình đã được tối ưu hóa thể hiện xuất sắc và hiệu suất cao trong các nhiệm vụ hỏi đáp kiến thức phức tạp, hiểu ngôn ngữ và sáng tạo văn bản cấp cao, là lựa chọn lý tưởng cho các tình huống ứng dụng chuyên nghiệp."}, "generalv3.5": {"description": "Spark3.5 Max là phiên bản toàn diện nhất, hỗ trợ tìm kiếm trực tuyến và nhiều plugin tích hợp. <PERSON><PERSON><PERSON> năng cốt lõi đã được tối ưu hóa toàn diện cùng với thiết lập vai trò hệ thống và chức năng gọi hàm, gi<PERSON><PERSON> nó thể hiện xuất sắc và nổi bật trong nhiều tình huống ứng dụng phức tạp."}, "glm-4": {"description": "GLM-4 là phiên bản flagship c<PERSON> ph<PERSON><PERSON> hành vào tháng 1 năm 2024, hiện đã được GLM-4-0520 mạnh mẽ hơn thay thế."}, "glm-4-0520": {"description": "GLM-4-0520 là phiên bản mô hình mới nh<PERSON>t, <PERSON><PERSON><PERSON><PERSON> thiết kế cho các nhiệm vụ phức tạp và đa dạng, thể hiện xuất sắc."}, "glm-4-9b-chat": {"description": "GLM-4-9B-<PERSON><PERSON> thể hiện hiệu suất cao trong nhiều lĩnh vực như ngữ nghĩa, to<PERSON>, <PERSON><PERSON> l<PERSON><PERSON><PERSON>, mã và kiến thức. <PERSON><PERSON> còn có khả năng duyệt web, thự<PERSON> thi mã, gọ<PERSON> công cụ tùy chỉnh và suy luận văn bản dài. Hỗ trợ 26 ngôn ngữ, bao gồ<PERSON> t<PERSON>, tiế<PERSON> Hàn và tiếng Đức."}, "glm-4-air": {"description": "GLM-4-<PERSON> là phiên bản có giá trị sử dụng cao, hi<PERSON><PERSON> su<PERSON>t gần giống GLM-4, cung cấp tốc độ nhanh và giá cả phải chăng."}, "glm-4-air-250414": {"description": "GLM-4-<PERSON> là phiên bản có giá trị cao, hi<PERSON><PERSON> su<PERSON>t gần tương đương với GLM-4, cung cấp tốc độ nhanh và giá cả phải chăng."}, "glm-4-airx": {"description": "GLM-4-Air<PERSON> cung cấp phiên bản hiệu quả của GLM-4-Air, tốc độ suy luận có thể đạt 2.6 lần."}, "glm-4-alltools": {"description": "GLM-4-AllTools là một mô hình tác nhân đa chức năng, đ<PERSON><PERSON><PERSON> tối ưu hóa để hỗ trợ lập kế hoạch chỉ dẫn phức tạp và gọi công cụ, nh<PERSON> duyệt web, gi<PERSON><PERSON> thích mã và sinh văn bản, phù hợp cho thực hiện nhiều nhiệm vụ."}, "glm-4-flash": {"description": "GLM-4-<PERSON> là lựa chọn lý tưởng cho các nhiệm vụ đơn gi<PERSON>, tốc độ nhanh nhất và giá cả phải chăng nhất."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON> là lựa chọn lý tưởng cho các nhiệm vụ đơn gi<PERSON>, n<PERSON><PERSON> nh<PERSON>t và miễn phí."}, "glm-4-flashx": {"description": "GLM-4-<PERSON><PERSON> là phiên bản nâng cấp của <PERSON>, với tốc độ suy diễn si<PERSON>."}, "glm-4-long": {"description": "GLM-4-Long hỗ trợ đầu vào văn bản siêu dài, phù hợp cho các nhiệm vụ ghi nhớ và xử lý tài liệu quy mô lớn."}, "glm-4-plus": {"description": "GLM-4-Plus là mô hình flagship thô<PERSON> <PERSON>h cao, c<PERSON> <PERSON>h<PERSON> năng xử lý văn bản dài và nhiệm vụ phức tạp, hi<PERSON><PERSON> su<PERSON><PERSON> đ<PERSON><PERSON><PERSON> nâng cao toàn di<PERSON>n."}, "glm-4.1v-thinking-flash": {"description": "Dòng mô hình GLM-4.1V-Thinking là mô hình VLM cấp 10 tỷ tham số mạnh nhất hiện biết, tích hợp các nhiệm vụ ngôn ngữ thị giác SOTA cùng cấp, bao gồm hiểu video, hỏi đáp hình <PERSON>nh, gi<PERSON><PERSON> bài tập chuyên ngành, nhận dạng ký tự quang học (OCR), phân tích tài liệu và biểu đồ, tác nhân GUI, lập trình giao diện web frontend, định vị (Grounding) và nhiều nhiệ<PERSON> vụ <PERSON>, với khả năng vượt trội so với Qwen2.5-VL-72B có tham số gấp 8 lần. Thông qua công nghệ học tăng cường tiên tiến, mô hình nắm vững phương pháp suy luận chuỗi tư duy để nâng cao độ chính xác và sự phong phú của câu tr<PERSON> lời, v<PERSON><PERSON><PERSON> trội rõ rệt so với các mô hình truyền thống không có tính năng thinking về hiệu quả cuối cùng và khả năng giải thích."}, "glm-4.1v-thinking-flashx": {"description": "Dòng mô hình GLM-4.1V-Thinking là mô hình VLM cấp 10 tỷ tham số mạnh nhất hiện biết, tích hợp các nhiệm vụ ngôn ngữ thị giác SOTA cùng cấp, bao gồm hiểu video, hỏi đáp hình <PERSON>nh, gi<PERSON><PERSON> bài tập chuyên ngành, nhận dạng ký tự quang học (OCR), phân tích tài liệu và biểu đồ, tác nhân GUI, lập trình giao diện web frontend, định vị (Grounding) và nhiều nhiệ<PERSON> vụ <PERSON>, với khả năng vượt trội so với Qwen2.5-VL-72B có tham số gấp 8 lần. Thông qua công nghệ học tăng cường tiên tiến, mô hình nắm vững phương pháp suy luận chuỗi tư duy để nâng cao độ chính xác và sự phong phú của câu tr<PERSON> lời, v<PERSON><PERSON><PERSON> trội rõ rệt so với các mô hình truyền thống không có tính năng thinking về hiệu quả cuối cùng và khả năng giải thích."}, "glm-4.5": {"description": "<PERSON>ô hình hàng đầu mới nhất c<PERSON><PERSON>, hỗ trợ chuyển đổi chế độ suy nghĩ, đạt trình độ SOTA trong các mô hình mã nguồn mở, với độ dài ngữ cảnh lên đến 128K."}, "glm-4.5-air": {"description": "<PERSON><PERSON><PERSON> bản nhẹ của GLM-4.5, cân bằng giữa hiệu suất và chi phí, có thể linh hoạt chuyển đổi mô hình suy nghĩ hỗn hợp."}, "glm-4.5-airx": {"description": "<PERSON><PERSON><PERSON> bản tốc độ cao của GLM-4.5-<PERSON>, p<PERSON><PERSON><PERSON> h<PERSON><PERSON> h<PERSON>, thi<PERSON><PERSON> kế cho nhu cầu quy mô lớn và tốc độ cao."}, "glm-4.5-flash": {"description": "<PERSON><PERSON><PERSON> bản miễn phí của <PERSON>-4.5, thể hiện tốt trong các tác vụ su<PERSON> lu<PERSON>, lậ<PERSON> trình và tác nhân."}, "glm-4.5-x": {"description": "<PERSON><PERSON><PERSON> bản tốc độ cao của GLM-4.5, vừa mạnh mẽ về hiệu suất, vừa đạt tốc độ tạo 100 token/giây."}, "glm-4v": {"description": "GLM-4V cung cấp khả năng hiểu và suy luận hình ảnh mạnh mẽ, hỗ trợ nhiều nhiệm vụ hình ảnh."}, "glm-4v-flash": {"description": "GLM-4V-<PERSON> tập trung vào hiểu hình ảnh đơn lẻ một cách hiệu quả, phù hợp cho các tình huống phân tích hình ảnh nhanh chóng, chẳng hạn như phân tích hình ảnh theo thời gian thực hoặc xử lý hình ảnh hàng loạt."}, "glm-4v-plus": {"description": "GLM-4V-Plus c<PERSON> khả năng hiểu nội dung video và nhiều h<PERSON>nh <PERSON>, phù hợp cho các nhiệm vụ đa phương tiện."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus c<PERSON> khả năng hiểu nội dung video và nhiều h<PERSON>nh <PERSON>, phù hợp cho các nhiệm vụ đa phương tiện."}, "glm-z1-air": {"description": "<PERSON><PERSON> hình suy luận: có khả năng suy luận mạnh mẽ, phù hợp cho các nhiệm vụ cần suy luận sâu."}, "glm-z1-airx": {"description": "<PERSON>y luận siêu tốc: có tốc độ suy luận cực nhanh và hiệu quả suy luận mạnh mẽ."}, "glm-z1-flash": {"description": "Dòng GLM-Z1 có khả năng suy luận phức tạp mạnh mẽ, thể hiện xuất sắc trong các lĩnh vực suy luận logic, toán học và lập trình."}, "glm-z1-flashx": {"description": "Tốc độ cao, <PERSON><PERSON><PERSON> thấp: <PERSON><PERSON><PERSON> bản tăng cư<PERSON><PERSON>, tốc độ suy luậ<PERSON><PERSON><PERSON>, đ<PERSON><PERSON> b<PERSON><PERSON> đồng thời nhanh hơn."}, "glm-zero-preview": {"description": "GLM-Zero-Preview có khả năng suy luận phức tạp mạnh mẽ, thể hiện xuất sắc trong các lĩnh vực suy luận logic, to<PERSON> họ<PERSON>, lậ<PERSON> trình."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash cung cấp các t<PERSON>h năng và cải tiến thế hệ tiếp theo, bao gồm tốc độ v<PERSON><PERSON><PERSON> tr<PERSON>, sử dụng công cụ bản địa, tạo đa phương tiện và cửa sổ ngữ cảnh 1M token."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental là mô hình AI đa phương tiện thử nghiệm mới nhất của Google, có sự cải thiện về chất lượng so với các phiên bản trư<PERSON>, đặc biệt là đối với kiến thức thế giới, mã và ngữ cảnh dài."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash là mô hình chủ lực tiên tiến nhất của Google, đ<PERSON><PERSON><PERSON> thiết kế dành riêng cho các nhiệm vụ suy luận nâng cao, mã hóa, to<PERSON> học và khoa học. <PERSON><PERSON> bao gồm khả năng \"suy nghĩ\" tích hợp, cho phép cung cấp các phản hồi với độ chính xác cao hơn và xử lý ngữ cảnh tinh tế hơn.\n\nLưu ý: Mô hình này có hai biến thể: có suy nghĩ và không suy nghĩ. Giá đầu ra có sự khác biệt đáng kể tùy thuộc vào việc khả năng suy nghĩ có được kích hoạt hay không. Nếu bạn chọn biến thể tiêu chuẩ<PERSON> (kh<PERSON><PERSON> có hậu tố \":thinking\"), mô hình sẽ rõ ràng tránh tạo ra các token suy nghĩ.\n\n<PERSON>ể tận dụng khả năng suy nghĩ và nhận các token suy nghĩ, bạn phải chọn biến thể \":thinking\", điều này sẽ dẫn đến giá đầu ra suy nghĩ cao hơn.\n\nNgoài ra, Gemini 2.5 Flash có thể được cấu hình thông qua tham số \"Số token suy luận tối đa\", như được mô tả trong tài liệu (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash là mô hình chủ lực tiên tiến nhất của Google, đ<PERSON><PERSON><PERSON> thiết kế cho suy luận nâng cao, l<PERSON><PERSON> <PERSON>r<PERSON><PERSON>, to<PERSON> học và các nhiệm vụ khoa học. <PERSON><PERSON> bao gồm khả năng 'suy nghĩ' tích hợp, cho phép nó cung cấp phản hồi với độ chính xác cao hơn và xử lý ngữ cảnh chi tiết hơn.\n\nLưu ý: Mô hình này có hai biến thể: suy nghĩ và không suy nghĩ. Giá đầu ra có sự khác biệt đáng kể tùy thuộc vào việc khả năng suy nghĩ có được kích hoạt hay không. Nếu bạn chọn biến thể tiêu chuẩn (kh<PERSON><PERSON> có hậu tố ':thinking'), mô hình sẽ rõ ràng tránh việc tạo ra các token suy nghĩ.\n\n<PERSON><PERSON> tận dụng khả năng suy nghĩ và nhận các token suy nghĩ, bạn phải chọn biến thể ':thinking', điều này sẽ tạo ra giá đầu ra suy nghĩ cao hơn.\n\nNgoài ra, Gemini 2.5 Flash có thể được cấu hình thông qua tham số 'số token tối đa cho suy luận', như đã mô tả trong tài liệu (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash là mô hình chủ lực tiên tiến nhất của Google, đ<PERSON><PERSON><PERSON> thiết kế cho suy luận nâng cao, l<PERSON><PERSON> <PERSON>r<PERSON><PERSON>, to<PERSON> học và các nhiệm vụ khoa học. <PERSON><PERSON> bao gồm khả năng 'suy nghĩ' tích hợp, cho phép nó cung cấp phản hồi với độ chính xác cao hơn và xử lý ngữ cảnh chi tiết hơn.\n\nLưu ý: Mô hình này có hai biến thể: suy nghĩ và không suy nghĩ. Giá đầu ra có sự khác biệt đáng kể tùy thuộc vào việc khả năng suy nghĩ có được kích hoạt hay không. Nếu bạn chọn biến thể tiêu chuẩn (kh<PERSON><PERSON> có hậu tố ':thinking'), mô hình sẽ rõ ràng tránh việc tạo ra các token suy nghĩ.\n\n<PERSON><PERSON> tận dụng khả năng suy nghĩ và nhận các token suy nghĩ, bạn phải chọn biến thể ':thinking', điều này sẽ tạo ra giá đầu ra suy nghĩ cao hơn.\n\nNgoài ra, Gemini 2.5 Flash có thể được cấu hình thông qua tham số 'số token tối đa cho suy luận', như đã mô tả trong tài liệu (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro là mô hình tư duy tiên tiến nhất của Google, có khả năng suy luận các vấn đề phức tạp trong mã hóa, to<PERSON> học và lĩnh vực STEM, cũng nh<PERSON> sử dụng ngữ cảnh dài để phân tích các bộ dữ liệu lớn, kho mã và tài liệu."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview là mô hình tư duy tiên tiến nhất của Google, có khả năng suy luận các vấn đề phức tạp trong lĩnh vực mã hóa, to<PERSON> họ<PERSON> và STEM, cũ<PERSON> như phân tích các bộ dữ liệu lớn, kho mã và tài liệu bằng ngữ cảnh dài."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash cung cấp kh<PERSON> năng xử lý đa ph<PERSON><PERSON><PERSON> thức đư<PERSON><PERSON> tối <PERSON> hó<PERSON>, phù hợp cho nhiều tình huống nhiệm vụ phức tạp."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 <PERSON> kết hợp công nghệ tối ưu hóa mới nhất, mang lại khả năng xử lý dữ liệu đa phư<PERSON><PERSON> thức hiệu quả hơn."}, "google/gemma-2-27b": {"description": "Gemma 2 là mô hình hiệu quả do Google ph<PERSON><PERSON> hành, bao gồm nhiều ứng dụng từ ứng dụng nhỏ đến xử lý dữ liệu phức tạp."}, "google/gemma-2-27b-it": {"description": "Gemma 2 tiế<PERSON> tục triết lý thiết kế nhẹ và hiệu quả."}, "google/gemma-2-2b-it": {"description": "<PERSON><PERSON> hình tinh chỉnh hướng dẫn nhẹ của Google"}, "google/gemma-2-9b": {"description": "Gemma 2 là mô hình hiệu quả do Google ph<PERSON><PERSON> hành, bao gồm nhiều ứng dụng từ ứng dụng nhỏ đến xử lý dữ liệu phức tạp."}, "google/gemma-2-9b-it": {"description": "Gemma 2 là một loạt mô hình văn bản mã nguồn mở nhẹ của Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 là loạt mô hình văn bản mã nguồn mở nhẹ của Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) cung cấp khả năng xử lý chỉ dẫn c<PERSON> bản, phù hợp cho các ứng dụng nhẹ."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B là một mô hình ngôn ngữ mã nguồn mở của Google, thiết lập tiêu chuẩn mới về hiệu quả và hiệu suất."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B là một mô hình ngôn ngữ mã nguồn mở của Google, thiết lập tiêu chuẩn mới về hiệu suất và hiệu quả."}, "gpt-3.5-turbo": {"description": "GPT 3.5 <PERSON>, ph<PERSON> hợp cho nhiều nhiệm vụ sinh và hiểu văn bản, hi<PERSON><PERSON> tại trỏ đến gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 <PERSON>, ph<PERSON> hợp cho nhiều nhiệm vụ sinh và hiểu văn bản, hi<PERSON><PERSON> tại trỏ đến gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 <PERSON>, ph<PERSON> hợp cho nhiều nhiệm vụ sinh và hiểu văn bản, hi<PERSON><PERSON> tại trỏ đến gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 <PERSON>, ph<PERSON> hợp cho nhiều nhiệm vụ sinh và hiểu văn bản, hi<PERSON><PERSON> tại trỏ đến gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo, mô hình hiệu quả do OpenAI cung cấp, phù hợp cho các tác vụ trò chuyện và tạo văn bản, hỗ trợ gọi hàm song song."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k, mô hình tạo văn bản dung lư<PERSON> cao, phù hợp cho các nhiệm vụ phức tạp."}, "gpt-4": {"description": "GPT-4 cung cấp một cửa sổ ngữ cảnh lớn hơn, c<PERSON> khả năng xử lý các đầu vào văn bản dà<PERSON> h<PERSON>n, phù hợp cho các tình huống cần tích hợp thông tin rộng rãi và phân tích dữ liệu."}, "gpt-4-0125-preview": {"description": "Mô hình GPT-4 Turbo mới nhất có chức năng hình ảnh. <PERSON><PERSON><PERSON> tại, các yêu cầu hình ảnh có thể sử dụng chế độ JSON và gọi hàm. GPT-4 Turbo là một phiên bản nâng cao, cung cấp hỗ trợ chi phí hiệu quả cho các nhiệm vụ đa phương tiện. <PERSON><PERSON> tìm thấy sự cân bằng giữa độ chính xác và hiệu quả, phù hợp cho các ứng dụng cần tương tác theo thời gian thực."}, "gpt-4-0613": {"description": "GPT-4 cung cấp một cửa sổ ngữ cảnh lớn hơn, c<PERSON> khả năng xử lý các đầu vào văn bản dà<PERSON> h<PERSON>n, phù hợp cho các tình huống cần tích hợp thông tin rộng rãi và phân tích dữ liệu."}, "gpt-4-1106-preview": {"description": "Mô hình GPT-4 Turbo mới nhất có chức năng hình ảnh. <PERSON><PERSON><PERSON> tại, các yêu cầu hình ảnh có thể sử dụng chế độ JSON và gọi hàm. GPT-4 Turbo là một phiên bản nâng cao, cung cấp hỗ trợ chi phí hiệu quả cho các nhiệm vụ đa phương tiện. <PERSON><PERSON> tìm thấy sự cân bằng giữa độ chính xác và hiệu quả, phù hợp cho các ứng dụng cần tương tác theo thời gian thực."}, "gpt-4-32k": {"description": "GPT-4 cung cấp một cửa sổ ngữ cảnh lớn hơn, c<PERSON> khả năng xử lý các đầu vào văn bản dà<PERSON> h<PERSON>n, phù hợp cho các tình huống cần tích hợp thông tin rộng rãi và phân tích dữ liệu."}, "gpt-4-32k-0613": {"description": "GPT-4 cung cấp một cửa sổ ngữ cảnh lớn hơn, c<PERSON> khả năng xử lý các đầu vào văn bản dà<PERSON> h<PERSON>n, phù hợp cho các tình huống cần tích hợp thông tin rộng rãi và phân tích dữ liệu."}, "gpt-4-turbo": {"description": "Mô hình GPT-4 Turbo mới nhất có chức năng hình ảnh. <PERSON><PERSON><PERSON> tại, các yêu cầu hình ảnh có thể sử dụng chế độ JSON và gọi hàm. GPT-4 Turbo là một phiên bản nâng cao, cung cấp hỗ trợ chi phí hiệu quả cho các nhiệm vụ đa phương tiện. <PERSON><PERSON> tìm thấy sự cân bằng giữa độ chính xác và hiệu quả, phù hợp cho các ứng dụng cần tương tác theo thời gian thực."}, "gpt-4-turbo-2024-04-09": {"description": "Mô hình GPT-4 Turbo mới nhất có chức năng hình ảnh. <PERSON><PERSON><PERSON> tại, các yêu cầu hình ảnh có thể sử dụng chế độ JSON và gọi hàm. GPT-4 Turbo là một phiên bản nâng cao, cung cấp hỗ trợ chi phí hiệu quả cho các nhiệm vụ đa phương tiện. <PERSON><PERSON> tìm thấy sự cân bằng giữa độ chính xác và hiệu quả, phù hợp cho các ứng dụng cần tương tác theo thời gian thực."}, "gpt-4-turbo-preview": {"description": "Mô hình GPT-4 Turbo mới nhất có chức năng hình ảnh. <PERSON><PERSON><PERSON> tại, các yêu cầu hình ảnh có thể sử dụng chế độ JSON và gọi hàm. GPT-4 Turbo là một phiên bản nâng cao, cung cấp hỗ trợ chi phí hiệu quả cho các nhiệm vụ đa phương tiện. <PERSON><PERSON> tìm thấy sự cân bằng giữa độ chính xác và hiệu quả, phù hợp cho các ứng dụng cần tương tác theo thời gian thực."}, "gpt-4-vision-preview": {"description": "Mô hình GPT-4 Turbo mới nhất có chức năng hình ảnh. <PERSON><PERSON><PERSON> tại, các yêu cầu hình ảnh có thể sử dụng chế độ JSON và gọi hàm. GPT-4 Turbo là một phiên bản nâng cao, cung cấp hỗ trợ chi phí hiệu quả cho các nhiệm vụ đa phương tiện. <PERSON><PERSON> tìm thấy sự cân bằng giữa độ chính xác và hiệu quả, phù hợp cho các ứng dụng cần tương tác theo thời gian thực."}, "gpt-4.1": {"description": "GPT-4.1 là mô hình hàng đầu của chúng tôi cho các nhiệm vụ phức tạp. <PERSON><PERSON> rất phù hợp để giải quyết vấn đề đa lĩnh vực."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini cung cấp sự cân bằng giữa trí tuệ, tốc độ và chi phí, khi<PERSON><PERSON> nó trở thành mô hình hấp dẫn cho nhiều trường hợp sử dụng."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini cung cấp sự cân bằng giữa trí tuệ, tốc độ và chi phí, khi<PERSON><PERSON> nó trở thành mô hình hấp dẫn cho nhiều trường hợp sử dụng."}, "gpt-4.5-preview": {"description": "<PERSON><PERSON><PERSON> nghiên cứu preview của GPT-4.5, đ<PERSON><PERSON> là mô hình GPT lớn nhất và mạnh mẽ nhất mà chúng tôi từng phát triển. Nó sở hữu kiến thức rộng lớn về thế giới và có khả năng hiểu ý định của người dùng tốt hơn, gi<PERSON><PERSON> nó thể hiện xuất sắc trong các nhiệm vụ sáng tạo và lập kế hoạch tự động. GPT-4.5 có thể chấp nhận đầu vào văn bản và hình ảnh, và tạo ra đầu ra văn bản (bao gồm cả đầu ra có cấu trúc). Hỗ trợ các tính năng quan trọng cho nhà phát triển như gọi hàm, <PERSON> theo lô và đầu ra theo luồng. Trong các nhiệm vụ cần sự sáng t<PERSON>, tư duy mở và đối thoại (<PERSON><PERSON><PERSON> vi<PERSON> l<PERSON>, họ<PERSON> tập hoặc khám ph<PERSON> ý tưởng mới), GPT-4.5 thể hiện đặc biệt xuất sắc. Thời điểm cắt đứt kiến thức là tháng 10 năm 2023."}, "gpt-4o": {"description": "ChatGPT-4o là một mô hình động, <PERSON><PERSON><PERSON><PERSON> cập nhật theo thời gian thực để giữ phiên bản mới nhất. <PERSON><PERSON> kết hợp khả năng hiểu và sinh ngôn ngữ mạnh mẽ, phù hợp cho các ứng dụng quy mô lớn, bao gồm dịch vụ khách hàng, gi<PERSON><PERSON> dục và hỗ trợ kỹ thuật."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o là một mô hình động, <PERSON><PERSON><PERSON><PERSON> cập nhật theo thời gian thực để giữ phiên bản mới nhất. <PERSON><PERSON> kết hợp khả năng hiểu và sinh ngôn ngữ mạnh mẽ, phù hợp cho các ứng dụng quy mô lớn, bao gồm dịch vụ khách hàng, gi<PERSON><PERSON> dục và hỗ trợ kỹ thuật."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o là một mô hình động, <PERSON><PERSON><PERSON><PERSON> cập nhật theo thời gian thực để giữ phiên bản mới nhất. <PERSON><PERSON> kết hợp khả năng hiểu và sinh ngôn ngữ mạnh mẽ, phù hợp cho các ứng dụng quy mô lớn, bao gồm dịch vụ khách hàng, gi<PERSON><PERSON> dục và hỗ trợ kỹ thuật."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o là một mô hình động, <PERSON><PERSON><PERSON><PERSON> cập nhật liên tục để giữ phiên bản mới nhất. <PERSON><PERSON> kết hợp khả năng hiểu và tạo ngôn ngữ mạnh mẽ, phù hợp cho nhiều ứng dụng quy mô lớn, bao gồm dịch vụ khách hàng, gi<PERSON><PERSON> dụ<PERSON> và hỗ trợ kỹ thuật."}, "gpt-4o-audio-preview": {"description": "<PERSON>ô hình GPT-4o Audio, hỗ trợ đầu vào và đầu ra âm thanh."}, "gpt-4o-mini": {"description": "GPT-4o mini là mô hình mới nhất do OpenAI phát hành sau GPT-4 Omni, hỗ trợ đầu vào hình ảnh và đầu ra văn bản. Là mô hình nhỏ gọn tiên tiến nhất của họ, nó rẻ hơn nhiều so với các mô hình tiên tiến gần đây khác và rẻ hơn hơn 60% so với GPT-3.5 Turbo. Nó giữ lại trí thông minh tiên tiến nhất trong khi có giá trị sử dụng đáng kể. GPT-4o mini đạt 82% điểm trong bài kiểm tra MMLU và hiện đứng cao hơn GPT-4 về sở thích trò chuyện."}, "gpt-4o-mini-audio-preview": {"description": "Mô hình GPT-4o mini Audio, hỗ trợ đầu vào và đầu ra âm thanh."}, "gpt-4o-mini-realtime-preview": {"description": "<PERSON><PERSON><PERSON> bản thời gian thực của GPT-4o-mini, hỗ trợ đầu vào và đầu ra âm thanh và văn bản theo thời gian thực."}, "gpt-4o-mini-search-preview": {"description": "GPT-4o mini phiên bản xem trước tìm kiếm là mô hình được huấn luyện chuyên biệt để hiểu và thực thi các truy vấn tìm kiếm trên web, sử dụng API Chat Completions. Ngoài phí token, truy vấn tìm kiếm trên web còn tính phí theo mỗi lần gọi công cụ."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe là mô hình chuyển đổi giọng nói thành văn bản sử dụng GPT-4o để phiên âm âm thanh. So với mô hình Whisper gốc, nó cải thiện tỷ lệ lỗi từ và nâng cao khả năng nhận diện ngôn ngữ cũng như độ chính xác. Sử dụng nó để có bản phiên âm chính xác hơn."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS là mô hình chuyển văn bản thành giọng nói dựa trên GPT-4o mini, cung cấp sinh âm thanh cao cấp với chi phí thấp hơn."}, "gpt-4o-realtime-preview": {"description": "<PERSON><PERSON><PERSON> bản thời gian thực của GPT-4o, hỗ trợ đầu vào và đầu ra âm thanh và văn bản theo thời gian thực."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "<PERSON><PERSON><PERSON> bản thời gian thực của GPT-4o, hỗ trợ đầu vào và đầu ra âm thanh và văn bản theo thời gian thực."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "<PERSON><PERSON><PERSON> bản thời gian thực của GPT-4o, hỗ trợ nhập xuất âm thanh và văn bản theo thời gian thực."}, "gpt-4o-search-preview": {"description": "GPT-4o phiên bản xem trước tìm kiếm là mô hình được huấn luyện chuyên biệt để hiểu và thực thi các truy vấn tìm kiếm trên web, sử dụng API Chat Completions. Ngoài phí token, truy vấn tìm kiếm trên web còn tính phí theo mỗi lần gọi công cụ."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe là mô hình chuyển đổi giọng nói thành văn bản sử dụng GPT-4o để phiên âm âm thanh. So với mô hình Whisper gốc, nó cải thiện tỷ lệ lỗi từ và nâng cao khả năng nhận diện ngôn ngữ cũng như độ chính xác. Sử dụng nó để có bản phiên âm chính xác hơn."}, "gpt-image-1": {"description": "<PERSON><PERSON> hình tạo hình ảnh đa ph<PERSON><PERSON><PERSON> thức nguyên bản của ChatGPT"}, "grok-2-1212": {"description": "<PERSON><PERSON> hình này đã được cải thiện về độ chính xác, kh<PERSON> năng tuân thủ hướng dẫn và khả năng đa ngôn ngữ."}, "grok-2-image-1212": {"description": "<PERSON>ô hình tạo hình ảnh mới nhất của chúng tôi có thể tạo ra hình ảnh sống động và chân thực dựa trên gợi ý văn bản. <PERSON><PERSON> thể hiện xuất sắc trong các lĩnh vực marketing, mạng xã hội và giải trí."}, "grok-2-vision-1212": {"description": "<PERSON><PERSON> hình này đã được cải thiện về độ chính xác, kh<PERSON> năng tuân thủ hướng dẫn và khả năng đa ngôn ngữ."}, "grok-3": {"description": "<PERSON><PERSON> hình chủ lực, xu<PERSON><PERSON> sắc trong trích xuất dữ liệu, lậ<PERSON> trình và tóm tắt văn bản cho các ứng dụng doanh nghiệp, sở hữu kiến thức sâu rộng trong các lĩnh vực tài ch<PERSON>, y tế, pháp lý và khoa học."}, "grok-3-fast": {"description": "<PERSON><PERSON> hình chủ lực, xu<PERSON><PERSON> sắc trong trích xuất dữ liệu, lậ<PERSON> trình và tóm tắt văn bản cho các ứng dụng doanh nghiệp, sở hữu kiến thức sâu rộng trong các lĩnh vực tài ch<PERSON>, y tế, pháp lý và khoa học."}, "grok-3-mini": {"description": "<PERSON><PERSON> hình nhẹ, suy nghĩ trước khi trả lời. <PERSON><PERSON><PERSON>, th<PERSON><PERSON> minh, ph<PERSON> hợp cho các nhiệm vụ logic không đòi hỏi kiến thức chuyên sâu và có thể truy xuất được chuỗi suy nghĩ gốc."}, "grok-3-mini-fast": {"description": "<PERSON><PERSON> hình nhẹ, suy nghĩ trước khi trả lời. <PERSON><PERSON><PERSON>, th<PERSON><PERSON> minh, ph<PERSON> hợp cho các nhiệm vụ logic không đòi hỏi kiến thức chuyên sâu và có thể truy xuất được chuỗi suy nghĩ gốc."}, "grok-4": {"description": "<PERSON><PERSON> hình hàng đầu mới nhất và mạnh mẽ nhất của chúng tôi, thể hiện xuất sắc trong xử lý ngôn ngữ tự nhiên, t<PERSON>h toán toán học và suy luận — một lựa chọn toàn diện hoàn hảo."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B là mô hình ngôn ngữ kết hợp giữa sáng tạo và tr<PERSON> thông <PERSON>h, kết hợ<PERSON> nhiều mô hình hàng đầu."}, "hunyuan-a13b": {"description": "<PERSON><PERSON><PERSON> là mô hình suy luận hỗn hợp đầu tiên, phi<PERSON><PERSON> bả<PERSON> nâng cấp củ<PERSON> hunyuan-standard-256K, với tổng số tham số 80 tỷ và 13 tỷ tham số kích hoạt. Mặc định ở chế độ suy nghĩ chậm, hỗ trợ chuyển đổi giữa chế độ suy nghĩ nhanh và chậm qua tham số hoặc chỉ thị, cách chuyển đổi là thêm / no_think trước truy vấn; năng lực tổng thể được cải thiện toàn diện so với thế hệ trước, đặc biệt là về toán học, kho<PERSON> họ<PERSON>, hiểu văn bản dài và năng lực tác nhân."}, "hunyuan-code": {"description": "<PERSON><PERSON> hình sinh mã mới nhất c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> huấn luyện trên 200B dữ liệu mã chất lư<PERSON><PERSON> cao, trải qua nửa năm huấn luyện dữ liệu SFT chất lư<PERSON> cao, độ dài cửa sổ ngữ cảnh tăng lên 8K, đứng đầu trong các chỉ số đánh giá tự động sinh mã cho năm ngôn ngữ lớn; trong đánh giá chất lượng cao của 10 tiêu chí mã tổng hợp cho năm ngôn ngữ, hiệu suất nằm trong nhóm đầu."}, "hunyuan-functioncall": {"description": "<PERSON>ô hình FunctionCall với cấu trúc MOE mới nhất củ<PERSON>, đ<PERSON><PERSON><PERSON> huấn luyện trên dữ liệu FunctionCall chất lư<PERSON> cao, với cửa sổ ngữ cảnh đạt 32K, dẫn đầu trong nhiều chỉ số đánh giá."}, "hunyuan-large": {"description": "<PERSON><PERSON> hình <PERSON>-large có tổng số tham số khoảng 389B, số tham số kích hoạt khoảng 52B, là mô hình MoE mã nguồn mở có quy mô tham số lớn nhất và hiệu quả nhất trong ngành hiện nay."}, "hunyuan-large-longcontext": {"description": "Chuyên xử lý các nhiệm vụ văn bản dài như tóm tắt tài liệu và hỏi đáp tài liệu, đồng thời cũng có khả năng xử lý các nhiệm vụ tạo văn bản chung. Thể hiện xuất sắc trong phân tích và tạo nội dung văn bản dài, có thể đáp ứng hiệu quả các yêu cầu xử lý nội dung dài phức tạp và chi tiết."}, "hunyuan-large-vision": {"description": "<PERSON>ô hình này phù hợp với các kịch bản hiểu hình ảnh và văn bản, là mô hình ngôn ngữ thị giác lớn dựa trên Hunyuan Large, hỗ trợ đầu vào nhiều hình ảnh với độ phân giải tùy ý cùng văn bản, tạo ra nội dung văn bản, tập trung vào các nhiệm vụ liên quan đến hiểu hình ảnh và văn bản, có sự cải thiện đáng kể về khả năng hiểu đa ngôn ngữ hình ảnh và văn bản."}, "hunyuan-lite": {"description": "<PERSON><PERSON><PERSON> cấp lên cấ<PERSON> t<PERSON><PERSON><PERSON>, với cửa sổ ngữ cảnh <PERSON>k, dẫn đầu nhiều mô hình mã nguồn mở trong các bộ đánh giá NLP, mã, to<PERSON>, ngành nghề, v.v."}, "hunyuan-lite-vision": {"description": "<PERSON>ô hình đa phương thức mới nhất 7B c<PERSON><PERSON>, cửa sổ ngữ cảnh 32K, hỗ trợ đối thoại đa phương thức trong các tình huống tiếng Trung và tiếng <PERSON>, nhận diện đối tượ<PERSON> hình <PERSON>, hiểu biết tài liệu và bảng biểu, toán học đa phương thức, v.v., với các chỉ số đánh giá vượt trội hơn các mô hình cạnh tranh 7B ở nhiều khía cạnh."}, "hunyuan-pro": {"description": "<PERSON>ô hình văn bản dài MOE-32K với quy mô hàng triệu tham số. Đ<PERSON><PERSON> đư<PERSON><PERSON> mức độ dẫn đầu tuyệt đối trên nhiều benchmark, c<PERSON> khả năng xử lý các lệnh phức tạp và suy di<PERSON>, c<PERSON> kh<PERSON> năng toán học phức tạp, hỗ tr<PERSON>call, đư<PERSON><PERSON> tối ưu hóa cho các lĩnh vực dịch thuật đa ngôn ngữ, tà<PERSON> ch<PERSON>, pháp lý và y tế."}, "hunyuan-role": {"description": "<PERSON>ô hình đóng vai trò mới nhất củ<PERSON>, đ<PERSON><PERSON><PERSON> tin<PERSON> chỉnh và huấn luyện bởi <PERSON><PERSON><PERSON>, dựa trên mô hình Hu<PERSON>uan kết hợp với bộ dữ liệu tình huống đóng vai trò để tăng cường huấn luyện, có hiệu suất cơ bản tốt hơn trong các tình huống đóng vai trò."}, "hunyuan-standard": {"description": "Sử dụng chiến lược định tuyến tốt hơn, đồng thời giảm thiểu vấn đề cân bằng tải và đồng nhất chuyên gia. Về mặt văn bản dài, chỉ số tìm kiếm đạt 99.9%. MOE-32K có giá trị hiệu suất tương đối cao, cân bằng giữa hiệu quả và giá cả, có thể xử lý đầu vào văn bản dài."}, "hunyuan-standard-256K": {"description": "Sử dụng chiến lược định tuyến tốt hơn, đồng thời giảm thiểu vấn đề cân bằng tải và đồng nhất chuyên gia. Về mặt văn bản dài, chỉ số tìm kiếm đạt 99.9%. MOE-256K đã có bước đột phá về độ dài và hiệu quả, mở rộng đáng kể độ dài đầu vào có thể."}, "hunyuan-standard-vision": {"description": "<PERSON><PERSON> hình đa phươ<PERSON> thức mới nhất c<PERSON><PERSON>, hỗ trợ trả lời đa ngôn ngữ, kh<PERSON> năng tiếng Trung và tiếng Anh cân bằng."}, "hunyuan-t1-20250321": {"description": "<PERSON><PERSON>y dựng toàn diện khả năng mô hình cho cả khoa học tự nhiên và khoa học xã hội, kh<PERSON> năng nắm bắt thông tin văn bản dài mạnh mẽ. Hỗ trợ suy luận và giải đáp các vấn đề khoa học như toán học, logic, khoa học và mã với nhiều độ khó khác nhau."}, "hunyuan-t1-20250403": {"description": "Nâng cao khả năng tạo mã cấp dự án; cải thiện chất lượng viết văn bản; nâng cao khả năng hiểu chủ đề văn bản đa vòng, tuân thủ chỉ thị toB và hiểu từ ngữ; tối ưu hóa vấn đề đầu ra hỗn hợp phồn thể và giản thể, cũng như hỗn hợp tiếng Trung và tiếng Anh."}, "hunyuan-t1-20250529": {"description": "<PERSON><PERSON>i <PERSON>u hóa sáng tạ<PERSON> vă<PERSON> b<PERSON>, v<PERSON><PERSON><PERSON>, c<PERSON><PERSON> thi<PERSON><PERSON> khả năng lậ<PERSON> tr<PERSON><PERSON> frontend, <PERSON><PERSON> h<PERSON>, suy luận logic và các kỹ năng khoa học tự nhiên, nâng cao khả năng tuân thủ chỉ dẫn."}, "hunyuan-t1-20250711": {"description": "<PERSON><PERSON>g cao đáng kể khả năng to<PERSON>, logic và mã hóa khó, tối ưu độ ổn định đầu ra mô hình, cải thiện khả năng xử lý văn bản dài."}, "hunyuan-t1-latest": {"description": "<PERSON>ô hình suy luận Hybrid-Transformer-<PERSON><PERSON> quy mô siêu lớn đầu tiên trong ngành, mở rộng khả năng suy luận, tốc độ giải mã cự<PERSON>, và tiếp tục điều chỉnh theo sở thích của con người."}, "hunyuan-t1-vision": {"description": "<PERSON><PERSON> hình suy nghĩ sâu đa phương thứ<PERSON>, hỗ trợ chuỗi suy nghĩ dài nguyên bản đa ph<PERSON><PERSON><PERSON> thức, xu<PERSON>t sắc trong các tình huống suy luận hình ảnh đa dạng, cải thiện toàn diện so với mô hình suy nghĩ nhanh trong các bài toán khoa học tự nhiên."}, "hunyuan-t1-vision-20250619": {"description": "<PERSON><PERSON><PERSON> bản mới nhất của <PERSON>uan t1-vision là mô hình suy nghĩ sâu đa phư<PERSON><PERSON> thức, hỗ trợ chuỗi tư duy dài nguyên bản đa phư<PERSON><PERSON> thức, cải thiện toàn diện so với phiên bản mặc định thế hệ trước."}, "hunyuan-turbo": {"description": "<PERSON><PERSON><PERSON> bản xem trước của thế hệ mới mô hình ngôn ngữ lớn <PERSON>, sử dụng cấu trúc mô hình chuyên gia hỗn hợp (MoE) hoàn toàn mới, so v<PERSON><PERSON> hunyuan-pro, hiệu suất suy diễn nhanh hơn và hiệu quả mạnh mẽ hơn."}, "hunyuan-turbo-20241223": {"description": "Phiên bản này tối ưu hóa: quy mô chỉ thị dữ liệu, nâng cao đáng kể khả năng tổng quát của mô hình; nâng cao đáng kể khả năng to<PERSON>, <PERSON><PERSON><PERSON>, và suy luận logic; tối ưu hóa khả năng hiểu biết văn bản và từ ngữ; tối ưu hóa chất lượng tạo nội dung văn bản."}, "hunyuan-turbo-latest": {"description": "Tối ưu hóa trải nghiệ<PERSON> chung, bao gồm hiểu biết NLP, sáng tạo vă<PERSON> b<PERSON>, tr<PERSON>, hỏi đ<PERSON><PERSON> kiế<PERSON> thứ<PERSON>, d<PERSON><PERSON> thu<PERSON>, và các lĩnh vự<PERSON> kh<PERSON>; nâng cao tính nhân văn, tối ưu hóa trí tuệ cảm xúc của mô hình; cải thiện khả năng làm rõ khi ý định không rõ ràng; nâng cao khả năng xử lý các vấn đề phân tích từ ngữ; nâng cao chất lượng và khả năng tương tác trong sáng tạo; cải thiện trải nghiệm đa vòng."}, "hunyuan-turbo-vision": {"description": "<PERSON>ô hình ngôn ngữ hình ảnh thế hệ mới c<PERSON><PERSON>, sử dụng cấu trúc mô hình chuyên gia hỗn hợp (MoE) hoàn toàn mới, nâng cao toàn diện khả năng nhận diện c<PERSON> bản, sán<PERSON> tạo nộ<PERSON> dung, hỏi đáp kiến thức, và phân tích suy luận so với mô hình thế hệ trước."}, "hunyuan-turbos-20250313": {"description": "Thống nhất phong cách các bước giải toán, tăng cường hỏi đáp toán học đa vòng. Tối ưu hóa phong cách trả lời trong sáng tác văn bả<PERSON>, lo<PERSON>i bỏ cảm gi<PERSON>, tăng thêm tính văn chư<PERSON>."}, "hunyuan-turbos-20250416": {"description": "<PERSON><PERSON>g cấp nền tảng tiền huấn luyệ<PERSON>, tăng cường khả năng hiểu và tuân thủ chỉ thị của nền tảng; tăng cường năng lực các môn khoa học tự nhiên nh<PERSON> to<PERSON>, l<PERSON><PERSON>, logic, khoa học trong giai đoạn căn chỉnh; cải thiện chất lượng sáng tạo vă<PERSON> họ<PERSON>, hi<PERSON><PERSON> v<PERSON><PERSON> b<PERSON>, đ<PERSON> ch<PERSON>h xác dịch thuật, hỏi đáp kiến thức và các năng lực khoa học xã hội; tăng cường năng lực Agent trong các lĩnh vực, đặc biệt là khả năng hiểu đối thoại đa vòng."}, "hunyuan-turbos-20250604": {"description": "<PERSON><PERSON><PERSON> cấp nền tảng tiền hu<PERSON>n luyệ<PERSON>, c<PERSON><PERSON> thi<PERSON>n khả năng viết và đọc hiể<PERSON>, tăng cường đáng kể năng lực lập trình và khoa học tự nhiên, ti<PERSON><PERSON> tục nâng cao khả năng tuân thủ các chỉ dẫn phức tạp."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS là phiên bản mới nhất của mô hình lớn hỗn hợ<PERSON>, c<PERSON> kh<PERSON> năng tư duy mạnh mẽ hơn và trải nghiệm tốt hơn."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Chuyên xử lý các nhiệm vụ văn bản dài như tóm tắt tài liệu và hỏi đáp tài li<PERSON>, đồng thời cũng có khả năng xử lý các nhiệm vụ tạo văn bản chung. Nó thể hiện xuất sắc trong việc phân tích và tạo ra văn bản dài, có khả năng đáp ứng hiệu quả các yêu cầu xử lý nội dung dài phức tạp và chi tiết."}, "hunyuan-turbos-role-plus": {"description": "<PERSON><PERSON> hình nhập vai phiên bản mới nhất củ<PERSON>, đ<PERSON><PERSON><PERSON> tinh chỉnh ch<PERSON>h thức bởi <PERSON><PERSON><PERSON>, dựa trên mô hình Hu<PERSON>uan kết hợp với bộ dữ liệu kịch bản nhập vai để tăng cường huấn luyện, mang lại hiệu quả cơ bản tốt hơn trong các kịch bản nhập vai."}, "hunyuan-turbos-vision": {"description": "<PERSON><PERSON> hình này phù hợp với các kịch bản hiểu hình ảnh và văn bản, là mô hình ngôn ngữ thị giác hàng đầu thế hệ mới dựa trên Hunyuan turbos mới nh<PERSON>t, tập trung vào các nhiệm vụ liên quan đến hiểu hình ảnh và văn bản, bao gồm nhận dạng thực thể dựa trên hình ảnh, hỏi đáp kiến thức, sáng tạo nội dung, gi<PERSON>i bài tập qua ảnh chụp, với cải tiến toàn diện so với thế hệ trước."}, "hunyuan-turbos-vision-20250619": {"description": "<PERSON><PERSON><PERSON> bản mới nhất của Hu<PERSON>uan turbos-vision là mô hình ngôn ngữ thị giác hàng đầu, cải thiện toàn diện so với phiên bản mặc định thế hệ trước trong các nhiệm vụ liên quan đến hiểu hình ảnh và văn bản, bao gồm nhận dạng thực thể dựa trên hình ảnh, hỏi đá<PERSON> kiến thức, sáng tạo nội dung, gi<PERSON><PERSON> bài tập qua ảnh chụp."}, "hunyuan-vision": {"description": "<PERSON>ô hình đa phương thức mới nhất củ<PERSON>, hỗ trợ đầu vào hình ảnh + văn bản để tạo ra nội dung văn bản."}, "image-01": {"description": "<PERSON>ô hình tạo hình ảnh hoàn toàn mới, thể hiện hình ảnh tinh tế, hỗ trợ tạo hình ảnh từ văn bản và hình ảnh."}, "image-01-live": {"description": "<PERSON>ô hình tạo hình ảnh với chất lư<PERSON><PERSON> tinh tế, hỗ trợ tạo hình ảnh từ văn bản và thiết lập phong cách hình ảnh."}, "imagen-4.0-generate-preview-06-06": {"description": "Dòng mô hình chuyển đổi văn bản thành hình ảnh thế hệ thứ 4 của <PERSON>n"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "<PERSON><PERSON>n bản Ultra của dòng mô hình chuyển đổi văn bản thành hình ảnh thế hệ thứ 4 của Imagen"}, "imagen4/preview": {"description": "<PERSON><PERSON> hình tạo hình ảnh chất lượng cao nhất của Google"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 cung cấp gi<PERSON>i pháp đối tho<PERSON>i thông minh cho nhiều tình huống."}, "internlm2.5-latest": {"description": "Dòng mô hình mới nhất của chúng tôi, có hiệu suất suy luận xuất sắc, hỗ trợ độ dài ngữ cảnh 1M và khả năng theo dõi chỉ dẫn và gọi công cụ mạnh mẽ hơn."}, "internlm3-latest": {"description": "Dòng mô hình mới nhất của chúng tôi, có hiệu suất suy luận xuất sắc, dẫn đầu trong số các mô hình mã nguồn mở cùng cấp. Mặc định chỉ đến mô hình InternLM3 mới nhất mà chúng tôi đã phát hành."}, "internvl2.5-latest": {"description": "Phiên bản InternVL2.5 mà chúng tôi vẫn đang duy trì, có hiệu suất xuất sắc và ổn định. Mặc định chỉ đến mô hình InternVL2.5 mới nhất củ<PERSON> chúng tôi, hiện tại chỉ đến internvl2.5-78b."}, "internvl3-latest": {"description": "Chúng tôi vừa phát hành mô hình lớn đa phương thức mới nhất, c<PERSON> khả năng hiểu hình ảnh và văn bản mạnh mẽ hơn, kh<PERSON> năng hiểu hình ảnh theo chuỗi thời gian dài, hiệu suất tương đương với các mô hình đóng nguồn hàng đầu. Mặc định chỉ đến mô hình InternVL mới nhất của chúng tôi, hiện tại chỉ đến internvl3-78b."}, "irag-1.0": {"description": "iRAG (image based RAG) do Baidu tự phát triển, công nghệ tạo hình ảnh từ văn bản tăng cường truy xu<PERSON>, kế<PERSON> hợ<PERSON> kho ảnh hàng trăm triệu của Baidu Search với khả năng mô hình nền tảng mạnh mẽ, tạo ra các hình ảnh siêu thực đa dạng, vư<PERSON><PERSON> trội so với hệ thống tạo hình ảnh gốc, loại bỏ cảm giác AI và chi phí thấp. iRAG có đặc điểm không ảo giác, siê<PERSON> thực và có thể sử dụng ngay."}, "jamba-large": {"description": "<PERSON><PERSON> hình mạnh mẽ và tiên tiến nhất củ<PERSON> chúng tôi, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt để xử lý các nhiệm vụ phức tạp cấp do<PERSON>h nghi<PERSON>, với hiệu suất xuất sắc."}, "jamba-mini": {"description": "<PERSON><PERSON> hình hiệu quả nhất trong cùng phân khúc, cân bằng giữa tốc độ và chất lư<PERSON>, có kích thước nhỏ hơn."}, "jina-deepsearch-v1": {"description": "Tìm kiếm sâu kết hợp tìm kiếm trên mạng, đọc và suy luận, có thể thực hiện điều tra toàn diện. Bạn có thể coi nó như một đại lý, nhận nhiệm vụ nghiên cứu của bạn - nó sẽ thực hiện tìm kiếm rộng rãi và qua nhiều lần lặp lại trước khi đưa ra câu trả lời. Quá trình này liên quan đến nghiên cứu liên tục, suy luận và giải quyết vấn đề từ nhiều góc độ. Điều này khác biệt hoàn toàn với việc tạo ra câu trả lời trực tiếp từ dữ liệu đã được huấn luyện trước của các mô hình lớn tiêu chuẩn và các hệ thống RAG truyền thống dựa vào tìm kiếm bề mặt một lần."}, "kimi-k2": {"description": "Kimi-K2 là mô hình nền tảng kiến trúc MoE do Moonshot AI phát hành, c<PERSON> khả năng mã hóa và đại lý vượ<PERSON> trội, tổng tham số 1T, tham số kích hoạt 32B. Trong các bài kiểm tra chuẩn về suy luận kiến thứ<PERSON> chung, l<PERSON><PERSON>r<PERSON>, to<PERSON> học và đại lý, hiệu suất của mô hình K2 vượt trội so với các mô hình mã nguồn mở phổ biến khác."}, "kimi-k2-0711-preview": {"description": "kimi-k2 là mô hình cơ sở kiến trúc MoE với khả năng mã hóa và Agent cự<PERSON> mạnh, tổng số tham số 1T, tham số kích hoạt 32B. Trong các bài kiểm tra hiệu năng chuẩn về suy luận kiế<PERSON> th<PERSON><PERSON> chung, <PERSON><PERSON><PERSON>, to<PERSON>, Agent và các lĩnh vự<PERSON> ch<PERSON>h<PERSON>, mô hình K2 vượt trội hơn các mô hình mã nguồn mở phổ biến khác."}, "kimi-latest": {"description": "Sản phẩm trợ lý thông minh Kimi sử dụng mô hình lớn <PERSON><PERSON> mới nhất, có thể chứa các tính năng chưa ổn định. Hỗ trợ hiểu hình <PERSON>nh, đồng thời tự động chọn mô hình 8k/32k/128k làm mô hình tính phí dựa trên độ dài ngữ cảnh yêu cầu."}, "kimi-thinking-preview": {"description": "<PERSON><PERSON> hình kimi-thinking-preview do <PERSON>'s Dark Side cung cấp, c<PERSON> kh<PERSON> năng suy luận đa phương thức và suy luận tổng quát, nổi bật với khả năng suy luận sâu, g<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> quyết nhiều vấn đề khó khăn hơn."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM là một mô hình ngôn ngữ thử nghiệm, chuyê<PERSON> bi<PERSON>t cho các nhiệ<PERSON> vụ, đ<PERSON><PERSON><PERSON> đào tạo để tuân theo các nguyên tắc khoa học học tập, có thể tuân theo các chỉ dẫn hệ thống trong các tình huống giảng dạy và học tập, đóng vai trò như một người hướng dẫn chuyên gia."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM là một mô hình ngôn ngữ thử nghiệm, chuy<PERSON><PERSON> bi<PERSON>t cho nhiệm vụ, đ<PERSON><PERSON><PERSON> đào tạo để tuân theo các nguyên tắc khoa học học tập, có thể tuân theo hướng dẫn hệ thống trong các tình huống giảng dạy và học tập, đóng vai trò như một người hướng dẫn chuyên gia."}, "lite": {"description": "Spark Lite là một mô hình ngôn ngữ lớn nhẹ, có độ trễ cực thấp và khả năng xử lý hiệu quả, hoàn toàn miễn phí và mở, hỗ trợ chức năng tìm kiếm trực tuyến theo thời gian thực. Đặc điểm phản hồi nhanh của nó giúp nó nổi bật trong các ứng dụng suy diễn trên thiết bị có công suất thấp và tinh chỉnh mô hình, mang lại hiệu quả chi phí và trải nghiệm thông minh xuất sắc cho người dùng, đặc biệt trong các tình huống hỏi đáp kiến thức, tạo nội dung và tìm kiếm."}, "llama-2-7b-chat": {"description": "Llama2 là một loạt các mô hình ngôn ngữ lớn (LLM) do Meta phát triển và công khai, bao gồm các mô hình tạo văn bản đã được tiền huấn luyện và tinh chỉnh với quy mô từ 7 tỷ đến 700 tỷ tham số. Về mặt kiến trúc, Llama2 là một mô hình ngôn ngữ hồi quy tự động sử dụng kiến trúc biến đổi tối ưu. Các phiên bản đã điều chỉnh sử dụng tinh chỉnh có gi<PERSON> s<PERSON>t (SFT) và học củng cố với phản hồi từ con người (RLHF) để đồng bộ hóa với sở thích của con người về tính hữu ích và an toàn. Llama2 có hiệu suất vượt trội hơn so với loạt Llama trên nhiều bộ dữ liệu học thuật, cung cấp ý tưởng cho thiết kế và phát triển của nhiều mô hình khác."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B cung cấp khả năng suy luận AI mạnh mẽ hơn, phù hợp cho các ứng dụng phức tạp, hỗ trợ xử lý tính toán cực lớn và đảm bảo hiệu quả và độ chính xác cao."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B là một mô hình hiệu su<PERSON>t cao, cung cấp khả năng sinh văn bản n<PERSON> chóng, rất phù hợp cho các tình huống ứng dụng cần hiệu quả quy mô lớn và tiết kiệm chi phí."}, "llama-3.1-instruct": {"description": "<PERSON><PERSON> hình Llama 3.1 đ<PERSON><PERSON><PERSON> tối ưu hóa cho các tình huống đối thoại, v<PERSON><PERSON><PERSON> trội hơn nhiều mô hình trò chuyện nguồn mở hiện có trong các bài kiểm tra chuẩn ngành phổ biến."}, "llama-3.2-11b-vision-instruct": {"description": "<PERSON><PERSON><PERSON> năng suy luận hình <PERSON>nh xuất sắc trên hình ảnh độ phân gi<PERSON>i cao, ph<PERSON> hợp cho các ứng dụng hiểu biết hình <PERSON>nh."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các nhiệm vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> thể hiện xuất sắc trong các nhiệm vụ mô tả hình ảnh và hỏi đáp hình <PERSON>nh, vư<PERSON><PERSON> qua rào cản giữa tạo ngôn ngữ và suy luận hình ảnh."}, "llama-3.2-90b-vision-instruct": {"description": "<PERSON><PERSON><PERSON> năng suy luận hình ảnh tiên tiến dành cho các ứng dụng đại lý hiểu biết hình <PERSON>nh."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các nhiệm vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> thể hiện xuất sắc trong các nhiệm vụ mô tả hình ảnh và hỏi đáp hình <PERSON>nh, vư<PERSON><PERSON> qua rào cản giữa tạo ngôn ngữ và suy luận hình ảnh."}, "llama-3.2-vision-instruct": {"description": "<PERSON>ô hình Llama 3.2-<PERSON> đã được tối ưu hóa để nhận dạng hình ảnh, suy luậ<PERSON> hình ảnh, mô tả hình ảnh và trả lời các câu hỏi thông thường liên quan đến hình ảnh."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 là mô hình ngôn ngữ lớn mã nguồn mở đa ngôn ngữ tiên tiến nhất trong dòng <PERSON>lam<PERSON>, mang đến trải nghiệm hiệu suất tương đương với mô hình 405B với chi phí cực thấp. Dựa trên cấu trúc Transformer, và được cải thiện tính hữu ích và an toàn thông qua tinh chỉnh giám sát (SFT) và học tăng cường từ phản hồi của con người (RLHF). Phiên bản tinh chỉnh theo chỉ dẫn của nó được tối ưu hóa cho đối thoại đa ngôn ngữ, thể hiện tốt hơn nhiều mô hình trò chuyện mã nguồn mở và đóng kín trong nhiều tiêu chuẩn ngành. <PERSON><PERSON>y cắt đứt kiến thức là tháng 12 năm 2023."}, "llama-3.3-70b-versatile": {"description": "Mô hình ngôn ngữ lớn Meta Llama 3.3 (LLM) đa ngôn ngữ là mô hình tạo ra dựa trên 70B (đầu vào/đầu ra văn bản) đã được huấn luyện và điều chỉnh theo chỉ dẫn. Mô hình thuần văn bản Llama 3.3 được tối ưu hóa cho các trường hợp hội thoại đa ngôn ngữ và vượt trội hơn nhiều mô hình trò chuyện mã nguồn mở và đóng khác trên các tiêu chuẩn ngành thông thường."}, "llama-3.3-instruct": {"description": "<PERSON><PERSON> hình Llama 3.3 đư<PERSON>c tối ưu hóa cho các tình huống đối thoại, và đã vượt qua nhiều mô hình trò chuyện nguồn mở hiện có trong các bài kiểm tra chuẩn ngành phổ biến."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B cung cấp khả năng xử lý phức tạp vô song, đ<PERSON><PERSON><PERSON> thiết kế riêng cho các dự án yêu cầu cao."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B mang lại hiệu suất suy luận chất l<PERSON><PERSON><PERSON>o, ph<PERSON> hợ<PERSON> cho nhu cầu ứng dụng đa dạng."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use cung cấp khả năng gọi công cụ mạnh mẽ, hỗ trợ xử lý hiệu quả cho các nhiệm vụ phức tạp."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use là mô hình được tối ưu hóa cho việc sử dụng công cụ hiệu quả, hỗ trợ tính toán song song n<PERSON>h chóng."}, "llama3.1": {"description": "Llama 3.1 là mô hình tiên tiến do <PERSON>a phát hành, hỗ trợ lên đến 405B tham số, có thể áp dụng cho các cuộc đối tho<PERSON><PERSON> phức tạp, dịch đa ngôn ngữ và phân tích dữ liệu."}, "llama3.1:405b": {"description": "Llama 3.1 là mô hình tiên tiến do <PERSON>a phát hành, hỗ trợ lên đến 405B tham số, có thể áp dụng cho các cuộc đối tho<PERSON><PERSON> phức tạp, dịch đa ngôn ngữ và phân tích dữ liệu."}, "llama3.1:70b": {"description": "Llama 3.1 là mô hình tiên tiến do <PERSON>a phát hành, hỗ trợ lên đến 405B tham số, có thể áp dụng cho các cuộc đối tho<PERSON><PERSON> phức tạp, dịch đa ngôn ngữ và phân tích dữ liệu."}, "llava": {"description": "LLaVA là mô hình đa phương thức kết hợp bộ mã hóa hình ảnh và Vicuna, phục vụ cho việc hiểu biết mạnh mẽ về hình ảnh và ngôn ngữ."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B cung cấp kh<PERSON> năng xử lý hình <PERSON>nh tích hợp, tạo ra đầu ra phức tạp thông qua đầu vào thông tin hình ảnh."}, "llava:13b": {"description": "LLaVA là mô hình đa phương thức kết hợp bộ mã hóa hình ảnh và Vicuna, phục vụ cho việc hiểu biết mạnh mẽ về hình ảnh và ngôn ngữ."}, "llava:34b": {"description": "LLaVA là mô hình đa phương thức kết hợp bộ mã hóa hình ảnh và Vicuna, phục vụ cho việc hiểu biết mạnh mẽ về hình ảnh và ngôn ngữ."}, "mathstral": {"description": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> thiết kế cho nghiên cứu khoa học và suy luận to<PERSON> học, cung cấp kh<PERSON> năng t<PERSON>h toán hiệu quả và giải thích kết quả."}, "max-32k": {"description": "Spark Max 32K được cấu hình với khả năng xử lý ngữ cảnh lớn, c<PERSON> khả năng hiểu ngữ cảnh và suy luận logic mạnh mẽ hơn, hỗ trợ đầu vào văn bản 32K tokens, phù hợp cho việc đọc tài liệu dài, hỏi đáp kiến thức riêng tư và các tình huống khác."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct là mô hình ngôn ngữ lớn được huấn luyện hoàn toàn độc lập bởi Wúwèn Xīnqióng. Megrez-3B-Instruct nhằm tạo ra một giải pháp thông minh ở đầu cuối với khả năng suy luận nhanh chóng, kích thước nhỏ gọn và dễ sử dụng thông qua lý thuyết tích hợp phần mềm và phần cứng."}, "meta-llama-3-70b-instruct": {"description": "<PERSON><PERSON> 70 tỷ tham số mạnh mẽ, xu<PERSON><PERSON> sắc trong lý luận, lậ<PERSON> trình và các ứng dụng ngôn ngữ rộng lớn."}, "meta-llama-3-8b-instruct": {"description": "<PERSON><PERSON>nh 8 tỷ tham số đa năng, tối ưu hóa cho các tác vụ đối thoại và tạo văn bản."}, "meta-llama-3.1-405b-instruct": {"description": "<PERSON><PERSON><PERSON> mô hình văn bản chỉ được tinh chỉnh theo hướng dẫn Llama 3.1 được tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ và vượt trội hơn nhiều mô hình trò chuyện mã nguồn mở và đóng có sẵn trên các tiêu chuẩn ngành phổ biến."}, "meta-llama-3.1-70b-instruct": {"description": "<PERSON><PERSON><PERSON> mô hình văn bản chỉ được tinh chỉnh theo hướng dẫn Llama 3.1 được tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ và vượt trội hơn nhiều mô hình trò chuyện mã nguồn mở và đóng có sẵn trên các tiêu chuẩn ngành phổ biến."}, "meta-llama-3.1-8b-instruct": {"description": "<PERSON><PERSON><PERSON> mô hình văn bản chỉ được tinh chỉnh theo hướng dẫn Llama 3.1 được tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ và vượt trội hơn nhiều mô hình trò chuyện mã nguồn mở và đóng có sẵn trên các tiêu chuẩn ngành phổ biến."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) cung cấp kh<PERSON> năng xử lý ngôn ngữ xuất sắc và trải nghiệm tương tác tuyệt vời."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 cung cấp kh<PERSON> năng xử lý ngôn ngữ tuyệt vời và trải nghiệm tương tác xuất sắc."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) là mô hình trò chuyện mạnh mẽ, hỗ trợ các nhu cầu đối thoại phức tạp."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) cung cấp hỗ trợ đa ngôn ngữ, bao gồ<PERSON> nhiều lĩnh vực kiến thức phong phú."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các tác vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> có khả năng xuất sắc trong các tác vụ mô tả hình ảnh và trả lời câu hỏi hình ảnh, vư<PERSON><PERSON> qua khoảng cách giữa tạo ngôn ngữ và suy luận hình ảnh."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các tác vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> có khả năng xuất sắc trong các tác vụ mô tả hình ảnh và trả lời câu hỏi hình ảnh, vư<PERSON><PERSON> qua khoảng cách giữa tạo ngôn ngữ và suy luận hình ảnh."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các tác vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> có khả năng xuất sắc trong các tác vụ mô tả hình ảnh và trả lời câu hỏi hình ảnh, vư<PERSON><PERSON> qua khoảng cách giữa tạo ngôn ngữ và suy luận hình ảnh."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Mô hình ngôn ngữ lớn đa ngôn ngữ Meta Llama 3.3 (LLM) là mô hình sinh ra từ 70B (đầu vào văn bản/đầu ra văn bản) với việc điều chỉnh trước và điều chỉnh theo lệnh. Mô hình điều chỉnh theo lệnh Llama 3.3 được tối ưu hóa cho các trường hợp sử dụng đối thoại đa ngôn ngữ và vượt trội hơn nhiều mô hình trò chuyện mã nguồn mở và đóng khác trên các bài kiểm tra chuẩn ngành phổ biến."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các tác vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> có khả năng xuất sắc trong các tác vụ mô tả hình ảnh và trả lời câu hỏi hình ảnh, vư<PERSON><PERSON> qua khoảng cách giữa tạo ngôn ngữ và suy luận hình ảnh."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite phù hợp cho các môi trường cần hiệu suất cao và độ tr<PERSON> thấp."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo cung cấp khả năng hiểu và sinh ngôn ngữ xuất sắc, ph<PERSON> hợp cho các nhiệm vụ tính toán khắt khe nhất."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite phù hợp cho các môi trường hạn chế tài nguyên, cung cấp hiệu suất cân bằng xuất sắc."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo là một mô hình ngôn ngữ lớn hiệu suất cao, hỗ trợ nhiều tình huống ứng dụng."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B là mô hình mạnh mẽ cho việc đào tạo trước và điều chỉnh theo hướng dẫn."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "<PERSON>ô hình Llama 3.1 Turbo 405B cung cấp hỗ trợ ngữ cảnh dung lượng lớn cho xử lý dữ liệu lớn, thể hiện xuất sắc trong các ứng dụng trí tuệ nhân tạo quy mô lớn."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 là mô hình hàng đầu do <PERSON>a ph<PERSON> hành, hỗ trợ lên đến 405B tham số, có thể áp dụng cho cuộc đối thoại phứ<PERSON> tạp, dịch đa ngôn ngữ và phân tích dữ liệu."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "<PERSON><PERSON> hình Llama 3.1 70B đ<PERSON><PERSON><PERSON> tinh chỉnh để phù hợp với các ứng dụng tải cao, đ<PERSON><PERSON> lượng đến FP8 cung cấp khả năng tính toán và độ chính xác hiệu quả hơ<PERSON>, đ<PERSON><PERSON> bảo hiệu suất xuất sắc trong các tình huống phức tạp."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "<PERSON><PERSON> hình Llama 3.1 8B sử dụng định lượng FP8, hỗ trợ lên đến 131,072 mã ngữ cảnh, là một trong những mô hình mã nguồn mở hàng đầu, ph<PERSON> hợp cho các nhiệ<PERSON> vụ phức tạp, v<PERSON><PERSON><PERSON> tr<PERSON><PERSON> hơn nhiều tiêu chuẩn ngành."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct đ<PERSON><PERSON><PERSON> tối ưu hóa cho các tình huống đối thoại chất lư<PERSON> cao, thể hiện xuất sắc trong nhiều đánh giá của con người."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct tối ưu hóa cho các tình huống đối thoại chất lư<PERSON> cao, hi<PERSON><PERSON> su<PERSON><PERSON> v<PERSON><PERSON><PERSON> trội hơn nhiều mô hình đóng nguồn."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho các cuộc đối thoại chất l<PERSON><PERSON> cao, thể hiện xuất sắc trong các đánh giá của con người, đặc biệt phù hợp cho các tình huống tương tác cao."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct là phiên bản mới nhất do <PERSON>a ph<PERSON> hành, tối ưu hóa cho các tình huống đối thoại chất lư<PERSON> cao, v<PERSON><PERSON><PERSON> trội hơn nhiều mô hình đóng nguồn hàng đầu."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 cung cấp hỗ trợ đa ngôn ngữ, là một trong những mô hình sinh hàng đầu trong ngành."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các nhiệm vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> thể hiện xuất sắc trong các nhiệm vụ mô tả hình ảnh và hỏi đáp hình <PERSON>nh, vư<PERSON><PERSON> qua ranh giới giữa sinh ngôn ngữ và suy diễn hình ảnh."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 đ<PERSON><PERSON><PERSON> thiết kế để xử lý các nhiệm vụ kết hợp dữ liệu hình ảnh và văn bản. <PERSON><PERSON> thể hiện xuất sắc trong các nhiệm vụ mô tả hình ảnh và hỏi đáp hình <PERSON>nh, vư<PERSON><PERSON> qua ranh giới giữa sinh ngôn ngữ và suy diễn hình ảnh."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 là mô hình ngôn ngữ lớn mã nguồn mở đa ngôn ngữ tiên tiến nhất trong dòng <PERSON>lam<PERSON>, mang đến trải nghiệm hiệu suất tương đương với mô hình 405B với chi phí cực thấp. Dựa trên cấu trúc Transformer, và được cải thiện tính hữu ích và an toàn thông qua tinh chỉnh giám sát (SFT) và học tăng cường từ phản hồi của con người (RLHF). Phiên bản tinh chỉnh theo chỉ dẫn của nó được tối ưu hóa cho đối thoại đa ngôn ngữ, thể hiện tốt hơn nhiều mô hình trò chuyện mã nguồn mở và đóng kín trong nhiều tiêu chuẩn ngành. <PERSON><PERSON>y cắt đứt kiến thức là tháng 12 năm 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 là mô hình ngôn ngữ lớn mã nguồn mở đa ngôn ngữ tiên tiến nhất trong dòng <PERSON>lam<PERSON>, mang đến trải nghiệm hiệu suất tương đương với mô hình 405B với chi phí cực thấp. Dựa trên cấu trúc Transformer, và được cải thiện tính hữu ích và an toàn thông qua tinh chỉnh giám sát (SFT) và học tăng cường từ phản hồi của con người (RLHF). Phiên bản tinh chỉnh theo chỉ dẫn của nó được tối ưu hóa cho đối thoại đa ngôn ngữ, thể hiện tốt hơn nhiều mô hình trò chuyện mã nguồn mở và đóng kín trong nhiều tiêu chuẩn ngành. <PERSON><PERSON>y cắt đứt kiến thức là tháng 12 năm 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct là mô hình lớn nhất và mạnh mẽ nhất trong mô hình Llama 3.1 Instruct, là một mô hình sinh dữ liệu và suy luận đối thoại tiên tiến, cũng có thể được sử dụng làm nền tảng cho việc tiền huấn luyện hoặc tinh chỉnh chuyên sâu trong các lĩnh vực cụ thể. Các mô hình ngôn ngữ lớn đa ngôn ngữ (LLMs) mà Llama 3.1 cung cấp là một tập hợp các mô hình sinh đã được tiền huấn luyện và điều chỉnh theo chỉ dẫn, bao gồm kích thước 8B, 70B và 405B (đầu vào/đầu ra văn bản). <PERSON><PERSON><PERSON> mô hình văn bản điều chỉnh theo chỉ dẫn của Llama 3.1 (8B, 70B, 405B) được tối ưu hóa cho các trường hợp đối thoại đa ngôn ngữ và đã vượt qua nhiều mô hình trò chuyện mã nguồn mở có sẵn trong các bài kiểm tra chuẩn ngành phổ biến. Llama 3.1 được thiết kế để sử dụng cho nhiều mục đích thương mại và nghiên cứu bằng nhiều ngôn ngữ. Các mô hình văn bản điều chỉnh theo chỉ dẫn phù hợp cho các cuộc trò chuyện giống như trợ lý, trong khi các mô hình đã được tiền huấn luyện có thể thích ứng với nhiều nhiệm vụ sinh ngôn ngữ tự nhiên khác nhau. Mô hình Llama 3.1 cũng hỗ trợ việc cải thiện các mô hình khác bằng cách sử dụng đầu ra của nó, bao gồm sinh dữ liệu tổng hợp và tinh chỉnh. Llama 3.1 là một mô hình ngôn ngữ tự hồi quy sử dụng kiến trúc biến áp tối ưu. Phiên bản điều chỉnh sử dụng tinh chỉnh có giám sát (SFT) và học tăng cường có phản hồi từ con người (RLHF) để phù hợp với sở thích của con người về tính hữu ích và an toàn."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Phiên bản cập nhật của Meta Llama 3.1 70B Instruct, bao gồm độ dài ngữ cảnh mở rộng 128K, tính đa ngôn ngữ và khả năng suy luận cải tiến. <PERSON><PERSON><PERSON> mô hình ngôn ngữ lớn (LLMs) đa ngôn ngữ do Llama 3.1 cung cấp là một tập hợp các mô hình sinh đã được huấn luyện trước và điều chỉnh theo chỉ dẫn, bao gồm kích thước 8B, 70B và 405B (đầu vào/đầu ra văn bản). Các mô hình văn bản điều chỉnh theo chỉ dẫn của Llama 3.1 (8B, 70B, 405B) được tối ưu hóa cho các trường hợp đối thoại đa ngôn ngữ và đã vượt qua nhiều mô hình trò chuyện mã nguồn mở có sẵn trong các bài kiểm tra chuẩn ngành phổ biến. Llama 3.1 được thiết kế cho các mục đích thương mại và nghiên cứu đa ngôn ngữ. Các mô hình văn bản điều chỉnh theo chỉ dẫn phù hợp cho các cuộc trò chuyện giống như trợ lý, trong khi các mô hình đã được huấn luyện trước có thể thích ứng với nhiều nhiệm vụ sinh ngôn ngữ tự nhiên khác nhau. Mô hình Llama 3.1 cũng hỗ trợ việc sử dụng đầu ra của mô hình để cải thiện các mô hình khác, bao gồm tạo dữ liệu tổng hợp và tinh chỉnh. Llama 3.1 là mô hình ngôn ngữ tự hồi quy sử dụng kiến trúc biến áp được tối ưu hóa. Phiên bản điều chỉnh sử dụng tinh chỉnh giám sát (SFT) và học tăng cường có phản hồi của con người (RLHF) để phù hợp với sở thích của con người về tính hữu ích và an toàn."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Phiên bản cập nhật của Meta Llama 3.1 8B Instruct, bao gồm độ dài ngữ cảnh mở rộng 128K, tính đa ngôn ngữ và khả năng suy luận cải tiến. <PERSON><PERSON><PERSON> mô hình ngôn ngữ lớn (LLMs) đa ngôn ngữ do Llama 3.1 cung cấp là một tập hợp các mô hình sinh đã được huấn luyện trước và điều chỉnh theo chỉ dẫn, bao gồm kích thước 8B, 70B và 405B (đầu vào/đầu ra văn bản). Các mô hình văn bản điều chỉnh theo chỉ dẫn của Llama 3.1 (8B, 70B, 405B) được tối ưu hóa cho các trường hợp đối thoại đa ngôn ngữ và đã vượt qua nhiều mô hình trò chuyện mã nguồn mở có sẵn trong các bài kiểm tra chuẩn ngành phổ biến. Llama 3.1 được thiết kế cho các mục đích thương mại và nghiên cứu đa ngôn ngữ. Các mô hình văn bản điều chỉnh theo chỉ dẫn phù hợp cho các cuộc trò chuyện giống như trợ lý, trong khi các mô hình đã được huấn luyện trước có thể thích ứng với nhiều nhiệm vụ sinh ngôn ngữ tự nhiên khác nhau. Mô hình Llama 3.1 cũng hỗ trợ việc sử dụng đầu ra của mô hình để cải thiện các mô hình khác, bao gồm tạo dữ liệu tổng hợp và tinh chỉnh. Llama 3.1 là mô hình ngôn ngữ tự hồi quy sử dụng kiến trúc biến áp được tối ưu hóa. Phiên bản điều chỉnh sử dụng tinh chỉnh giám sát (SFT) và học tăng cường có phản hồi của con người (RLHF) để phù hợp với sở thích của con người về tính hữu ích và an toàn."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 là một mô hình ngôn ngữ lớn (LLM) mở dành cho các nhà phát triển, nhà nghiên cứu và do<PERSON>h nghiệ<PERSON>, nhằm gi<PERSON><PERSON> họ x<PERSON><PERSON> d<PERSON>, thử nghiệm và mở rộng ý tưởng AI sinh một cách có trách nhiệm. Là một phần của hệ thống cơ sở hạ tầng đổi mới toàn cầu, nó rất phù hợp cho việc tạo nội dung, <PERSON> đ<PERSON><PERSON> tho<PERSON>, hi<PERSON><PERSON> ngôn ngữ, nghiên cứu và ứng dụng doanh nghiệp."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 là một mô hình ngôn ngữ lớn (LLM) mở dành cho các nhà phát triển, nhà nghiên cứu và do<PERSON>h nghiệ<PERSON>, nhằm gi<PERSON><PERSON> họ x<PERSON><PERSON> d<PERSON>, thử nghiệm và mở rộng ý tưởng AI sinh một cách có trách nhiệm. Là một phần của hệ thống cơ sở hạ tầng đổi mới toàn cầu, nó rất phù hợp cho các thiết bị biên và thời gian huấn luyện nhanh hơn với khả năng tính toán và tài nguyên hạn chế."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "<PERSON><PERSON><PERSON> năng suy luận hình ảnh xuất sắc trên hình ảnh độ phân gi<PERSON>i cao, phù hợp cho các ứng dụng hiểu biết thị giác."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "<PERSON><PERSON><PERSON> năng suy luận hình ảnh nâng cao dành cho các ứng dụng đại lý hiểu biết thị giác."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 là mô hình ngôn ngữ lớn đa ngôn ngữ mã nguồn mở tiên tiến nhất trong dòng <PERSON>, mang lại hiệu suất tương đương mô hình 405 tỷ tham số với chi phí rất thấp. Dựa trên kiến trúc Transformer, đư<PERSON><PERSON> cải thiện qua huấn luyện giám sát (SFT) và học tăng cường từ phản hồi con người (RLHF) để nâng cao tính hữu ích và an toàn. Phiên bản tinh chỉnh chỉ dẫn được tối ưu cho đối thoại đa ngôn ngữ, vượt trội trên nhiều chuẩn mực ngành so với nhiều mô hình trò chuyện mã nguồn mở và đóng. <PERSON><PERSON><PERSON> thứ<PERSON> cập nhật đến tháng 12 năm 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Một mô hình mạnh mẽ với 70 tỷ tham số, thể hiện xuất sắc trong suy luận, mã hóa và các ứng dụng ngôn ngữ đa dạng."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "<PERSON><PERSON>t mô hình đa năng với 8 tỷ tham số, đ<PERSON><PERSON><PERSON> tối ưu cho các nhiệm vụ đối thoại và tạo văn bản."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "<PERSON>ô hình văn bản <PERSON>lama 3.1 đ<PERSON><PERSON><PERSON> tinh chỉnh chỉ dẫn, tối ưu cho các trường hợp sử dụng đối thoại đa ngôn ngữ, thể hiện xuất sắc trên nhiều chuẩn mực ngành so với nhiều mô hình trò chuyện mã nguồn mở và đóng hiện có."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "<PERSON>ô hình văn bản <PERSON>lama 3.1 đ<PERSON><PERSON><PERSON> tinh chỉnh chỉ dẫn, tối ưu cho các trường hợp sử dụng đối thoại đa ngôn ngữ, thể hiện xuất sắc trên nhiều chuẩn mực ngành so với nhiều mô hình trò chuyện mã nguồn mở và đóng hiện có."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "<PERSON>ô hình văn bản <PERSON>lama 3.1 đ<PERSON><PERSON><PERSON> tinh chỉnh chỉ dẫn, tối ưu cho các trường hợp sử dụng đối thoại đa ngôn ngữ, thể hiện xuất sắc trên nhiều chuẩn mực ngành so với nhiều mô hình trò chuyện mã nguồn mở và đóng hiện có."}, "meta/llama-3.1-405b-instruct": {"description": "<PERSON><PERSON> cao cấ<PERSON>, hỗ trợ tạo dữ liệu tổng hợp, chư<PERSON> cất kiến thức và su<PERSON> l<PERSON>, ph<PERSON> hợ<PERSON> cho chatbot, lậ<PERSON> trình và các nhiệm vụ chuyên biệt."}, "meta/llama-3.1-70b-instruct": {"description": "Tăng cường cuộc đối tho<PERSON>i ph<PERSON><PERSON> tạ<PERSON>, c<PERSON> <PERSON>h<PERSON> năng hiểu ngữ cảnh xu<PERSON> s<PERSON>, suy luận và sinh văn bản."}, "meta/llama-3.1-8b-instruct": {"description": "<PERSON><PERSON> hình tiên tiến hàng đầu, c<PERSON> kh<PERSON> năng hiểu ngôn ngữ, suy luận xu<PERSON>t sắc và khả năng sinh văn bản."}, "meta/llama-3.2-11b-vision-instruct": {"description": "<PERSON><PERSON> hình thị giác-ngôn ngữ tiên tiến, xu<PERSON><PERSON> sắ<PERSON> trong việc suy luận chất lượng cao từ hình <PERSON>nh."}, "meta/llama-3.2-1b-instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ nhỏ tiên tiến hàng đầu, c<PERSON> kh<PERSON> năng hiểu ngôn ngữ, suy luận xuất sắc và khả năng sinh văn bản."}, "meta/llama-3.2-3b-instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ nhỏ tiên tiến hàng đầu, c<PERSON> kh<PERSON> năng hiểu ngôn ngữ, suy luận xuất sắc và khả năng sinh văn bản."}, "meta/llama-3.2-90b-vision-instruct": {"description": "<PERSON><PERSON> hình thị giác-ngôn ngữ tiên tiến, xu<PERSON><PERSON> sắ<PERSON> trong việc suy luận chất lượng cao từ hình <PERSON>nh."}, "meta/llama-3.3-70b-instruct": {"description": "<PERSON><PERSON> hình <PERSON> ti<PERSON><PERSON> tiế<PERSON>, <PERSON><PERSON><PERSON><PERSON> sắ<PERSON> trong suy luậ<PERSON>, <PERSON><PERSON> h<PERSON>, ki<PERSON><PERSON> thức chung và gọi hàm."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "<PERSON><PERSON>ng mô hình Phi-3-medium nhưng với kích thước ngữ cảnh lớn hơn, phù hợp cho RAG hoặc ít gợi ý."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "<PERSON><PERSON> 14 tỷ tham số, chất lượ<PERSON> vư<PERSON><PERSON> trội so với Phi-3-mini, tập trung vào dữ liệu suy luận chất lượng cao."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Cùng mô hình Phi-3-mini nhưng với kích thước ngữ cảnh lớn hơn, phù hợp cho RAG hoặc ít gợi ý."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON> viên nhỏ nhất trong gia đình Phi-3, <PERSON><PERSON><PERSON><PERSON> tối ưu cho chất lượng và độ trễ thấp."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Cùng mô hình Phi-3-small nhưng với kích thước ngữ cảnh lớn hơn, phù hợp cho RAG hoặc ít gợi ý."}, "microsoft/Phi-3-small-8k-instruct": {"description": "<PERSON><PERSON> 7 tỷ tham số, chất lượ<PERSON> vư<PERSON><PERSON> trội so với Phi-3-mini, tập trung vào dữ liệu suy luận chất lượng cao."}, "microsoft/Phi-3.5-mini-instruct": {"description": "<PERSON><PERSON><PERSON> bản cập nhật của mô hình Phi-3-mini."}, "microsoft/Phi-3.5-vision-instruct": {"description": "<PERSON><PERSON><PERSON> bản cập nhật của mô hình Phi-3-vision."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 là mô hình ngôn ngữ do AI của Microsoft cung cấp, thể hiện xuất sắc trong các lĩnh vực đối tho<PERSON>i phứ<PERSON> tạp, đa ngôn ngữ, suy luận và trợ lý thông minh."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B là mô hình Wizard tiên tiến nhất của Microsoft AI, thể hiện hiệu suất cực kỳ cạnh tranh."}, "minicpm-v": {"description": "MiniCPM-V là mô hình đa phương thức thế hệ mới do OpenBMB phát triển, c<PERSON> khả năng nhận diện OCR xuất sắc và hiểu biết đa phư<PERSON>ng thức, hỗ trợ nhiều ứng dụng khác nhau."}, "ministral-3b-latest": {"description": "Ministral 3B là mô hình hàng đầu thế giới của Mistral về hiệu suất cạnh biên."}, "ministral-8b-latest": {"description": "Ministral 8B là mô hình cạnh biên cực kỳ tiết kiệm chi phí của Mistral."}, "mistral": {"description": "Mistral là mô hình 7B do Mistral AI phát hành, phù hợp cho các nhu cầu xử lý ngôn ngữ đa dạng."}, "mistral-ai/Mistral-Large-2411": {"description": "<PERSON><PERSON> hình chủ lực c<PERSON>, phù hợp cho các nhiệ<PERSON> vụ phức tạp cần khả năng suy luận quy mô lớn hoặc chuyên môn cao (tổng hợp văn bản, t<PERSON><PERSON> mã, RAG hoặc đại lý)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo là một mô hình ngôn ngữ tiên tiến (LLM), sở hữu khả năng suy luận, kiế<PERSON> thức thế giới và mã hóa hàng đầu trong phân khúc kích thước của nó."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small phù hợp cho bất kỳ nhiệm vụ dựa trên ngôn ngữ nào cần hiệu quả cao và độ tr<PERSON> thấp."}, "mistral-large": {"description": "Mixtral Large là mô hình hàng đầu c<PERSON><PERSON>, k<PERSON><PERSON> <PERSON><PERSON><PERSON> kh<PERSON> năng sinh mã, to<PERSON> h<PERSON> và suy luậ<PERSON>, hỗ trợ cửa sổ ngữ cảnh 128k."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 là một mô hình ngôn ngữ lớn (LLM) tiên tiến, có 123 tỷ tham số, với kh<PERSON> năng su<PERSON> luận, kiế<PERSON> thức và lập trình hàng đầu."}, "mistral-large-latest": {"description": "Mistral Large là mô hình lớn hàng đầu, chuy<PERSON><PERSON> về các nhiệm vụ đa ngôn ngữ, suy lu<PERSON>n phức tạp và sinh mã, là lựa chọn lý tưởng cho các ứng dụng cao cấp."}, "mistral-medium-latest": {"description": "Mistral Medium 3 cung cấp hiệu suất tiên tiến với chi phí gấp 8 lần và đơn giản hóa việc triển khai doanh nghiệp."}, "mistral-nemo": {"description": "Mistral N<PERSON>o đ<PERSON> phát triển hợp tác gi<PERSON>a Mistral AI và NVIDIA, là mô hình 12B hiệu suất cao."}, "mistral-nemo-instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn Mistral-Nemo-Instruct-2407 (LLM) là phiên bản điều chỉnh lệnh của Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small có thể được sử dụng cho bất kỳ nhiệm vụ nào dựa trên ngôn ngữ yêu cầu hiệu suất cao và độ trễ thấp."}, "mistral-small-latest": {"description": "Mistral Small là lựa chọn hiệu quả về chi phí, <PERSON><PERSON><PERSON> chóng và đáng tin cậy, phù hợp cho các tr<PERSON><PERSON><PERSON> hợ<PERSON> nh<PERSON> dịch thuật, tóm tắt và phân tích cảm xúc."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct nổi bật với hiệu su<PERSON> cao, phù hợp cho nhiều nhiệm vụ ngôn ngữ."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B là mô hình fine-tuning theo yêu cầu, cung cấp gi<PERSON>i pháp tối ưu cho các nhiệm vụ."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 cung cấp khả năng tính toán hiệu quả và hiểu ngôn ngữ tự nhiên, phù hợp cho nhiều ứng dụng."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B là một mô hình nhỏ gọn nhưng hiệu suất cao, chuyên về xử lý hàng loạt và các tác vụ đơn giản như phân loại và sinh văn bản, với khả năng suy luận tốt."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) là một mô hình ngôn ngữ lớn siêu cấp, hỗ trợ nhu cầu xử lý cực cao."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B là mô hình chuyên gia hỗn hợp thưa được tiền huấn luyện, dùng cho các nhiệm vụ văn bản tổng quát."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B là một mô hình chuyên gia thưa thớt, tận dụng nhiều tham số để tăng tốc độ suy luận, phù hợp để xử lý đa ngôn ngữ và tạo mã."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct là mô hình tiêu chuẩn ngành với tốc độ tối ưu hóa và hỗ trợ ngữ cảnh dài."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo là mô hình 7.3B tham số hỗ trợ đa ngôn ngữ và lập trình hiệu suất cao."}, "mixtral": {"description": "Mixtral là mô hình chuyên gia của Mistral AI, có trọng số mã nguồn mở và cung cấp hỗ trợ cho việc sinh mã và hiểu ngôn ngữ."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B cung cấp khả năng tính toán song song có độ dung sai cao, phù hợp cho các nhiệm vụ phức tạp."}, "mixtral:8x22b": {"description": "Mixtral là mô hình chuyên gia của Mistral AI, có trọng số mã nguồn mở và cung cấp hỗ trợ cho việc sinh mã và hiểu ngôn ngữ."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K là một mô hình có khả năng xử lý ngữ cảnh siêu dài, phù hợp cho việc sinh văn bản siê<PERSON> dà<PERSON>, đ<PERSON><PERSON> <PERSON><PERSON> nhu cầu nhiệm vụ sinh phứ<PERSON> tạp, c<PERSON> thể xử lý nội dung lên đến 128.000 tokens, rất phù hợp cho nghiên cứu, họ<PERSON> thuật và sinh tài liệu lớn."}, "moonshot-v1-128k-vision-preview": {"description": "<PERSON><PERSON> hình hình <PERSON> (bao gồm moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, v.v.) c<PERSON> <PERSON>h<PERSON> năng hiểu n<PERSON><PERSON> dung h<PERSON>nh <PERSON>, bao gồm văn bả<PERSON>, m<PERSON><PERSON> s<PERSON>c hình <PERSON>nh và hình dạng vật thể."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K cung cấp khả năng xử lý ngữ cảnh độ dài trung bình, có thể xử lý 32.768 tokens, đặc biệt phù hợp cho việc sinh các tài liệu dài và đối thoại phứ<PERSON> tạp, <PERSON><PERSON> dụng trong sáng tạo nội dung, sinh báo cáo và hệ thống đối thoại."}, "moonshot-v1-32k-vision-preview": {"description": "<PERSON><PERSON> hình hình <PERSON> (bao gồm moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, v.v.) c<PERSON> <PERSON>h<PERSON> năng hiểu n<PERSON><PERSON> dung h<PERSON>nh <PERSON>, bao gồm văn bả<PERSON>, m<PERSON><PERSON> s<PERSON>c hình <PERSON>nh và hình dạng vật thể."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K được thiết kế đặc biệt cho các nhiệm vụ sinh văn bản ngắn, c<PERSON> hiệu suất xử lý cao, c<PERSON> thể xử lý 8.192 tokens, rất phù hợp cho các cuộc đối thoại ngắn, ghi chú nhanh và sinh nội dung nhanh chóng."}, "moonshot-v1-8k-vision-preview": {"description": "<PERSON><PERSON> hình hình <PERSON> (bao gồm moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview, v.v.) c<PERSON> <PERSON>h<PERSON> năng hiểu n<PERSON><PERSON> dung h<PERSON>nh <PERSON>, bao gồm văn bả<PERSON>, m<PERSON><PERSON> s<PERSON>c hình <PERSON>nh và hình dạng vật thể."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto có thể chọn mô hình phù hợp dựa trên số lượng Tokens hiện tại đang chiếm dụng trong ngữ cảnh."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B là một mô hình mã nguồn mở lớn, đ<PERSON><PERSON><PERSON> tối ưu hóa qua học tăng cường quy mô lớn, có khả năng tạo ra các bản vá ổn định và có thể triển khai trực tiếp. Mô hình này đã đạt điểm cao kỷ lục 60,4% trên SWE-bench Verified, phá vỡ các kỷ lục của mô hình mã nguồn mở trong các nhiệm vụ kỹ thuật phần mềm tự động như sửa lỗi và đánh giá mã."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 là mô hình nền tảng kiến trúc MoE có khả năng mã hóa và đại lý vượt trội, tổng tham số 1T, tham số kích hoạt 32B. Trong các bài kiểm tra chuẩn về suy luận kiến thứ<PERSON> chung, <PERSON><PERSON><PERSON>, to<PERSON> học và đại lý, hiệu suất của mô hình K2 vượt trội so với các mô hình mã nguồn mở phổ biến khác."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 là mô hình cơ sở kiến trúc MoE với khả năng mã hóa và Agent cự<PERSON> mạnh, tổng số tham số 1T, tham số kích hoạt 32B. Trong các bài kiểm tra hiệu năng chuẩn về suy luận kiế<PERSON> th<PERSON><PERSON> chung, <PERSON><PERSON><PERSON>, to<PERSON>, Agent và các lo<PERSON>, mô hình K2 vượt trội hơn các mô hình mã nguồn mở phổ biến khác."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B là phi<PERSON>n bản nâng cấp của Nous Hermes 2, bao gồm bộ dữ liệu phát triển nội bộ mới nhất."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B là một mô hình ngôn ngữ quy mô lớn tùy chỉnh bởi NVIDIA, nhằm nâng cao mức độ hỗ trợ của phản hồi do LLM tạo ra đối với các truy vấn của người dùng. Mô hình này đã thể hiện xuất sắc trong các bài kiểm tra chuẩn như Arena Hard, AlpacaEval 2 LC và GPT-4-Turbo MT-Bench, đứng đầu trong cả ba bài kiểm tra tự động cho đến ngày 1 tháng 10 năm 2024. <PERSON>ô hình sử dụng RLHF (đặc biệt là REINFORCE), Llama-3.1-Nemotron-70B-Reward và HelpSteer2-Preference để đào tạo trên cơ sở mô hình Llama-3.1-70B-Instruct."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "<PERSON><PERSON> hình ngôn ngữ độc đáo, cung cấp độ ch<PERSON>h xác và hiệu suất không thể sánh kịp."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B là mô hình ngôn ngữ lớn tùy chỉnh của NVIDIA, nhằm nâng cao tính hữu ích của các phản hồi do LLM tạo ra."}, "o1": {"description": "Tập trung vào suy diễn nâng cao và giải quyết các vấn đề phức tạp, bao gồm các nhiệm vụ toán học và khoa học. Rất phù hợp cho các ứng dụng cần hiểu biết sâu sắc về ngữ cảnh và quy trình làm việc đại diện."}, "o1-mini": {"description": "o1-mini là một mô hình suy diễn nhanh chóng và tiết kiệm chi phí, <PERSON><PERSON><PERSON><PERSON> thiết kế cho các ứng dụng lập trình, to<PERSON> học và khoa học. <PERSON>ô hình này có ngữ cảnh 128K và thời điểm cắt kiến thức vào tháng 10 năm 2023."}, "o1-preview": {"description": "o1 là mô hình suy diễn mới của OpenAI, phù hợp cho các nhiệm vụ phức tạp cần kiến thức tổng quát rộng rãi. Mô hình này có ngữ cảnh 128K và thời điểm cắt kiến thức vào tháng 10 năm 2023."}, "o1-pro": {"description": "Dòng mô hình o1 được huấn luyện qua học tăng cườ<PERSON>, c<PERSON> khả năng suy nghĩ trước khi trả lời và thực hiện các nhiệm vụ suy luận phức tạp. <PERSON>ô hình o1-pro sử dụng nhiều tài nguyên tính toán hơn để suy nghĩ sâu hơn, từ đó liên tục cung cấp câu trả lời chất lượng cao hơn."}, "o3": {"description": "o3 là một mô hình toàn năng mạnh mẽ, thể hiện xuất sắc trong nhiều lĩnh vực. <PERSON><PERSON> thiết lập tiêu chuẩn mới cho các nhiệm vụ to<PERSON> h<PERSON>, <PERSON><PERSON><PERSON> h<PERSON>, lậ<PERSON> trình và suy luận hình ảnh. <PERSON>ó cũng giỏi trong việc viết kỹ thuật và tuân thủ hướng dẫn. Người dùng có thể sử dụng nó để phân tích văn bản, mã và hình ảnh, gi<PERSON>i quyết các vấn đề phức tạp nhiều bước."}, "o3-deep-research": {"description": "o3-deep-research là mô hình nghiên cứu sâu tiên tiến nhất của chúng tôi, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt để xử lý các nhiệm vụ nghiên cứu phức tạp nhiều bước. Nó có thể tìm kiếm và tổng hợp thông tin từ Internet, cũng như truy cập và tận dụng dữ liệu riêng của bạn thông qua kết nối MCP."}, "o3-mini": {"description": "o3-mini là mô hình suy diễn nhỏ gọn mới nhất của chúng tôi, cung cấp trí thông minh cao với chi phí và độ trễ tương tự như o1-mini."}, "o3-pro": {"description": "<PERSON><PERSON> hình o3-pro sử dụng nhiều tài nguyên tính toán hơn để suy nghĩ sâu sắc hơn và luôn cung cấp câu trả lời tốt hơn, chỉ hỗ trợ sử dụng dưới API Responses."}, "o4-mini": {"description": "o4-mini là mô hình nhỏ gọn mới nhất trong dòng o của chúng tôi. <PERSON><PERSON> được tối ưu hóa cho suy luận nhanh chóng và hiệu quả, thể hiện hiệu suất và hiệu quả cao trong các nhiệm vụ mã hóa và hình ảnh."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research là mô hình nghiên cứu sâu nhanh hơn và tiết kiệm hơn của chúng tôi — rất phù hợp để xử lý các nhiệm vụ nghiên cứu phức tạp nhiều bước. <PERSON><PERSON> có thể tìm kiếm và tổng hợp thông tin từ Internet, cũng như truy cập và tận dụng dữ liệu riêng của bạn thông qua kết nối MCP."}, "open-codestral-mamba": {"description": "Codestral Mamba là mô hình ngôn ngữ Mamba 2 tập trung vào sinh mã, cung cấp hỗ trợ mạnh mẽ cho các nhiệm vụ mã và suy luận tiên tiến."}, "open-mistral-7b": {"description": "Mistral 7B là một mô hình nhỏ gọn nhưng hiệu suất cao, chuy<PERSON>n về xử lý hàng loạt và các nhiệm vụ đơn giản như phân loại và sinh văn bản, c<PERSON> kh<PERSON> năng suy luận tốt."}, "open-mistral-nemo": {"description": "Mistral Nemo là một mô hình 12B đ<PERSON><PERSON><PERSON> phát triển hợp tác với Nvidia, cung cấp hiệu suất suy luận và mã hóa xu<PERSON><PERSON> sắc, dễ dàng tích hợp và thay thế."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B là một mô hình chuyên gia lớn hơn, tập trung vào các nhiệm vụ phức tạp, cung cấp kh<PERSON> năng suy luận xuất sắc và thông lượng cao hơn."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B là một mô hình chuyên gia thưa thớt, sử dụng nhiều tham số để tăng tốc độ suy luận, phù hợp cho việc xử lý đa ngôn ngữ và sinh mã."}, "openai/gpt-4.1": {"description": "GPT-4.1 là mô hình hàng đầu của chúng tôi cho các nhiệm vụ phức tạp. <PERSON><PERSON> rất phù hợp để giải quyết vấn đề xuyên lĩnh vực."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini cung cấp sự cân bằng giữa trí tuệ, tốc độ và chi phí, khi<PERSON><PERSON> nó trở thành một mô hình hấp dẫn cho nhiều trường hợp sử dụng."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano là mô hình GPT-4.1 nhanh nhất và tiết kiệm chi phí nhất."}, "openai/gpt-4o": {"description": "ChatGPT-4o là một mô hình động, cập nhật theo thời gian để giữ phiên bản mới nhất. <PERSON><PERSON> kết hợp khả năng hiểu và tạo ngôn ngữ mạnh mẽ, phù hợp với các tình huống ứng dụng quy mô lớn, bao gồm dịch vụ khách hàng, gi<PERSON><PERSON> dục và hỗ trợ kỹ thuật."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini là mô hình mới nhất của OpenAI, <PERSON><PERSON><PERSON><PERSON> phát hành sau GPT-4 <PERSON>mni, hỗ trợ đầu vào hình ảnh và văn bản, và đầu ra văn bản. Là mô hình nhỏ tiên tiến nhất của họ, nó rẻ hơn nhiều so với các mô hình tiên tiến gần đây khác và rẻ hơn hơn 60% so với GPT-3.5 Turbo. Nó giữ lại trí thông minh tiên tiến nhất trong khi có giá trị sử dụng đáng kể. GPT-4o mini đạt 82% điểm trong bài kiểm tra MMLU và hiện đứng đầu về sở thích trò chuyện so với GPT-4."}, "openai/o1": {"description": "o1 là mô hình suy luận mới của OpenAI, hỗ trợ đầu vào hình ảnh và văn bản, đồng thời xuất ra văn bản, phù hợp cho các nhiệm vụ phức tạp đòi hỏi kiến thức phổ quát rộng rãi. Mô hình này có ngữ cảnh 200K và kiến thức cập nhật đến tháng 10 năm 2023."}, "openai/o1-mini": {"description": "o1-mini là một mô hình suy diễn nhanh chóng và tiết kiệm chi phí, <PERSON><PERSON><PERSON><PERSON> thiết kế cho các ứng dụng lập trình, to<PERSON> học và khoa học. <PERSON>ô hình này có ngữ cảnh 128K và thời điểm cắt kiến thức vào tháng 10 năm 2023."}, "openai/o1-preview": {"description": "o1 là mô hình suy diễn mới của OpenAI, phù hợp cho các nhiệm vụ phức tạp cần kiến thức tổng quát rộng rãi. Mô hình này có ngữ cảnh 128K và thời điểm cắt kiến thức vào tháng 10 năm 2023."}, "openai/o3": {"description": "o3 là một mô hình mạnh mẽ toàn di<PERSON>, thể hiện xuất sắc trong nhiều lĩnh vực. <PERSON><PERSON> thiết lập tiêu chuẩn mới cho các nhiệm vụ to<PERSON> h<PERSON>, <PERSON><PERSON><PERSON> h<PERSON>, lậ<PERSON> trình và suy luận hình ảnh. <PERSON>ó cũng giỏi trong việc viết kỹ thuật và tuân thủ hướng dẫn. Người dùng có thể sử dụng nó để phân tích văn bản, mã và hình ảnh, gi<PERSON>i quyết các vấn đề phức tạp nhiều bước."}, "openai/o3-mini": {"description": "o3-mini cung cấp trí tuệ cao với cùng chi phí và mục tiêu độ trễ như o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini phiên bản cao cấp về suy luận, cung cấp trí tuệ cao với cùng chi phí và mục tiêu độ trễ như o1-mini."}, "openai/o4-mini": {"description": "o4-mini đư<PERSON><PERSON> tối ưu hóa cho suy luận nhanh chóng và hiệu quả, thể hiện hiệu suất và hiệu quả cao trong các nhiệm vụ mã hóa và hình ảnh."}, "openai/o4-mini-high": {"description": "o4-mini phiên bả<PERSON> cao c<PERSON>, đ<PERSON><PERSON><PERSON> tối ưu hóa cho suy luận nhanh chóng và hiệu quả, thể hiện hiệu suất và hiệu quả cao trong các nhiệm vụ mã hóa và hình ảnh."}, "openrouter/auto": {"description": "Dựa trên độ dài ngữ cảnh, chủ đề và độ phứ<PERSON> tạp, yê<PERSON> cầu của bạn sẽ được gửi đến Llama 3 70B <PERSON>st<PERSON><PERSON>, Claude 3.5 Son<PERSON> (tự điều chỉnh) hoặc GPT-4o."}, "phi3": {"description": "Phi-3 là mô hình mở nhẹ do Microsoft phát hành, phù hợp cho việc tích hợp hiệu quả và suy luận kiến thức quy mô lớn."}, "phi3:14b": {"description": "Phi-3 là mô hình mở nhẹ do Microsoft phát hành, phù hợp cho việc tích hợp hiệu quả và suy luận kiến thức quy mô lớn."}, "pixtral-12b-2409": {"description": "<PERSON><PERSON> hình Pixtral thể hiện khả năng mạnh mẽ trong các nhiệm vụ như hiểu biểu đồ và hình ảnh, hỏi đáp tài liệu, suy luận đa phương tiện và tuân thủ hướng dẫn, có khả năng tiếp nhận hình ảnh với độ phân giải và tỷ lệ khung hình tự nhiên, cũng như xử lý bất kỳ số lượng hình ảnh nào trong cửa sổ ngữ cảnh dài lên đến 128K token."}, "pixtral-large-latest": {"description": "Pixtral Large là một mô hình đa phương thức mã nguồn mở với 1240 tỷ tham số, đ<PERSON><PERSON><PERSON> xây dựng dựa trên Mistral Large 2. Đ<PERSON>y là mô hình thứ hai trong gia đình đa phương thức của chúng tôi, thể hiện khả năng hiểu hình ảnh ở mức tiên tiến."}, "pro-128k": {"description": "Spark Pro 128K được cấu hình với khả năng xử lý ngữ cảnh cực lớn, có thể xử lý tới 128K thông tin ngữ cảnh, đặc biệt phù hợp cho việc phân tích toàn bộ và xử lý mối liên hệ logic lâu dài trong nội dung văn bản dài, có thể cung cấp logic mạch lạc và hỗ trợ trích dẫn đa dạng trong giao tiếp văn bản phức tạp."}, "qvq-72b-preview": {"description": "<PERSON>ô hình QVQ là mô hình nghiên cứu thử nghiệm do đội ngũ Qwen phát triển, tập trung vào việc nâng cao khả năng suy luận hình ảnh, đặc biệt trong lĩnh vực suy luận toán học."}, "qvq-max": {"description": "<PERSON><PERSON> hình suy luận thị giác QVQ củ<PERSON>, hỗ trợ đầu vào thị giác và xuất ra chuỗi suy nghĩ, thể hiện năng lực mạnh mẽ trong to<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> tích thị gi<PERSON>, s<PERSON><PERSON> tạo và các nhiệm vụ chung."}, "qvq-plus": {"description": "<PERSON><PERSON> hình suy luận thị giác. Hỗ trợ đầu vào hình ảnh và đầu ra chuỗi suy nghĩ, phi<PERSON><PERSON> bản plus ra mắt sau mô hình qvq-max, với tốc độ suy luận n<PERSON> hơ<PERSON>, hiệu quả và chi phí cân bằng hơn so với qvq-max."}, "qwen-coder-plus": {"description": "<PERSON><PERSON> hình mã hóa <PERSON>."}, "qwen-coder-turbo": {"description": "<PERSON><PERSON> hình mã hóa <PERSON>."}, "qwen-coder-turbo-latest": {"description": "<PERSON><PERSON> h<PERSON>nh mã <PERSON>."}, "qwen-long": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn <PERSON>, hỗ trợ ngữ cảnh văn bản dài và chức năng đối thoại dựa trên tài liệu dài, nhiều tài liệu."}, "qwen-math-plus": {"description": "<PERSON><PERSON> hình toán học <PERSON>wen được thiết kế chuyên biệt cho việc gi<PERSON>i toán."}, "qwen-math-plus-latest": {"description": "<PERSON><PERSON> hình toán học Qwen được thiết kế đặc biệt để giải quyết các bài toán toán học."}, "qwen-math-turbo": {"description": "<PERSON><PERSON> hình toán học <PERSON>wen được thiết kế chuyên biệt cho việc gi<PERSON>i toán."}, "qwen-math-turbo-latest": {"description": "<PERSON><PERSON> hình toán học Qwen được thiết kế đặc biệt để giải quyết các bài toán toán học."}, "qwen-max": {"description": "<PERSON>ô hình ngôn ngữ quy mô lớn <PERSON>wen cấp tỷ, hỗ trợ đầu vào bằng tiếng Trung, tiế<PERSON> <PERSON>h và nhiều ngôn ngữ khác, là mô hình API đằng sau phiên bản sản phẩm Qwen 2.5 hiện tại."}, "qwen-omni-turbo": {"description": "Dòng mô hình <PERSON>-Omni hỗ trợ đầu vào đa dạng các loại dữ liệu đa phương thức, bao gồm video, <PERSON><PERSON> thanh, <PERSON><PERSON><PERSON>, vă<PERSON> bản, và xuất ra âm thanh cùng văn bản."}, "qwen-plus": {"description": "<PERSON><PERSON> hình ngôn ngữ quy mô lớn Qwen phiên bản nâng cao, hỗ trợ đầu vào bằng tiếng Trung, tiếng <PERSON>h và nhiều ngôn ngữ khác."}, "qwen-turbo": {"description": "<PERSON><PERSON> hình ngôn ngữ quy mô lớn Qwen hỗ trợ đầu vào bằng tiếng Trung, tiếng <PERSON>h và nhiều ngôn ngữ khác."}, "qwen-vl-chat-v1": {"description": "<PERSON><PERSON> hình <PERSON> VL hỗ trợ các phương thức tương tác linh ho<PERSON>, bao gồ<PERSON> nhiều h<PERSON>nh <PERSON>, nhi<PERSON><PERSON> vòng hỏi đáp, s<PERSON><PERSON> t<PERSON>, v.v."}, "qwen-vl-max": {"description": "<PERSON><PERSON> hình ngôn ngữ thị giác quy mô siêu lớn <PERSON>. So với phiên bản nâng cao, tiếp tục cải thiện khả năng suy luận thị giác và tuân thủ chỉ thị, cung cấp mức độ nhận thức và cảm nhận thị giác cao hơn."}, "qwen-vl-max-latest": {"description": "Mô hình ngôn ngữ hình ảnh quy mô siêu lớn của Tong<PERSON>anwen. So với phiên bản nâng cao, nó lại nâng cao khả năng suy luận hình ảnh và khả năng tuân thủ chỉ dẫn, cung cấp mức độ nhận thức và cảm nhận hình ảnh cao hơn."}, "qwen-vl-ocr": {"description": "<PERSON>yi Qianwen OCR là mô hình chuyên biệt cho trích xuất văn bản, tập trung vào khả năng trích xuất chữ viết từ các loại hình ảnh như tài li<PERSON>, b<PERSON><PERSON> bi<PERSON><PERSON>, đ<PERSON> thi, chữ viết tay. <PERSON>ô hình có thể nhận diện nhiều ngôn ngữ, hiện hỗ trợ: tiế<PERSON>rung, tiế<PERSON>, tiế<PERSON>, tiế<PERSON>, tiế<PERSON>, tiế<PERSON> Đức, tiếng <PERSON>, tiếng Ý, tiế<PERSON>, tiếng Ả Rập."}, "qwen-vl-plus": {"description": "Phiên bản nâng cao của mô hình ngôn ngữ thị giác quy mô lớn Tongyi Qianwen. Nâng cao đáng kể khả năng nhận diện chi tiết và nhận dạng văn bản, hỗ trợ hình ảnh có độ phân giải trên một triệu điểm ảnh và tỷ lệ khung hình tùy ý."}, "qwen-vl-plus-latest": {"description": "Mô hình ngôn ngữ hình ảnh quy mô lớn phiên bản nâng cao của Tong<PERSON>anwen. Nâng cao khả năng nhận diện chi tiết và nhận diện văn bản, hỗ trợ độ phân giải trên một triệu pixel và các tỷ lệ chiều dài và chiều rộng tùy ý."}, "qwen-vl-v1": {"description": "Mô hình được khởi tạo bằng mô hình ngôn ngữ Qwen-7B, thêm mô hình hình ảnh, mô hình được huấn luyện trước với độ phân giải đầu vào hình ảnh là 448."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 là dòng mô hình ngôn ngữ lớn hoàn toàn mới. Qwen2 7B là một mô hình dựa trên transformer, thể hiện xuất sắc trong việc hiểu ngôn ngữ, <PERSON><PERSON><PERSON> năng đa ngôn ngữ, l<PERSON><PERSON>, to<PERSON> học và suy luận."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 là một loạt mô hình ngôn ngữ lớn hoàn toàn mới, có kh<PERSON> năng hiểu và sinh mạnh mẽ hơn."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL là phiên bản cải tiến mới nhất của mô hình Qwen-VL, đã đạt được hiệu suất tiên tiến trong các bài kiểm tra hiểu biết thị giá<PERSON>, bao gồm MathVista, DocVQA, RealWorldQA và MTVQA. Qwen2-VL có khả năng hiểu video dài hơn 20 phút, phục vụ cho các câu hỏi, đối thoại và sáng tạo nội dung dựa trên video chất lượng cao. <PERSON><PERSON> cũng có khả năng suy luận và ra quyết định phức tạp, có thể tích hợp với các thiết bị di động, robot, v.v., để thực hiện các thao tác tự động dựa trên môi trường thị giác và hướng dẫn văn bản. Ngoài tiếng Anh và tiếng Trung, Qwen2-VL hiện cũng hỗ trợ hiểu văn bản trong hình ảnh bằng nhiều ngôn ngữ khác nhau, bao gồm hầu hết các ngôn ngữ châu Âu, tiếng Nhật, tiếng Hàn, tiếng Ả Rập và tiếng Việt."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct là một trong những mô hình ngôn ngữ lớn mới nhất được phát hành bởi Alibaba Cloud. Mô hình 72B này có khả năng cải thiện đáng kể trong các lĩnh vực như mã hóa và toán học. Mô hình cũng cung cấp hỗ trợ đa ngôn ngữ, bao gồm hơn 29 ngôn ngữ, bao gồ<PERSON> tiếng Trung, tiế<PERSON>, v.v. <PERSON>ô hình đã có sự cải thiện đáng kể trong việc theo dõi hướng dẫn, hiểu dữ liệu có cấu trúc và tạo ra đầu ra có cấu trúc (đặc biệt là JSON)."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct là một trong những mô hình ngôn ngữ lớn mới nhất được phát hành bởi Alibaba Cloud. Mô hình 32B này có khả năng cải thiện đáng kể trong các lĩnh vực như mã hóa và toán học. Mô hình cung cấp hỗ trợ đa ngôn ngữ, bao gồm hơn 29 ngôn ngữ, bao gồ<PERSON> tiếng Trung, ti<PERSON><PERSON>, v.v. Mô hình đã có sự cải thiện đáng kể trong việc theo dõi hướng dẫn, hiểu dữ liệu có cấu trúc và tạo ra đầu ra có cấu trúc (đặc biệt là JSON)."}, "qwen/qwen2.5-7b-instruct": {"description": "<PERSON><PERSON> hướng đến tiếng Trung và tiế<PERSON>, tập trung vào ngôn ngữ, l<PERSON><PERSON>r<PERSON>, <PERSON><PERSON>, suy lu<PERSON><PERSON> và các lĩnh vực <PERSON>."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "<PERSON><PERSON>o cấ<PERSON>, hỗ trợ tạo mã, suy luận và sửa chữa, bao gồ<PERSON> các ngôn ngữ lập trình phổ biến."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "<PERSON><PERSON> hình mã mạnh mẽ cỡ trung, hỗ trợ độ dài ngữ cảnh 32K, xu<PERSON>t sắc trong lập trình đa ngôn ngữ."}, "qwen/qwen3-14b": {"description": "Qwen3-14B là một mô hình ngôn ngữ nguyên nhân dày đặc với 14,8 tỷ tham số trong dòng Qwen3, đ<PERSON><PERSON><PERSON> thiết kế cho suy luận phức tạp và đối thoại hiệu quả. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho các nhiệm vụ như toán họ<PERSON>, lập trình và suy luận logic với chế độ \"không suy nghĩ\" cho đối thoại thông thường. Mô hình này đã được tinh chỉnh để sử dụng cho việc tuân theo chỉ dẫn, sử dụng công cụ đại lý, viết sáng tạo và các nhiệm vụ đa ngôn ngữ trên hơn 100 ngôn ngữ và phương ngữ. N<PERSON> xử lý ngữ cảnh 32K token một cách tự nhiên và có thể mở rộng lên 131K token bằng cách sử dụng mở rộng dựa trên YaRN."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B là một mô hình ngôn ngữ nguyên nhân dày đặc với 14,8 tỷ tham số trong dòng Qwen3, đ<PERSON><PERSON><PERSON> thiết kế cho suy luận phức tạp và đối thoại hiệu quả. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho các nhiệm vụ như toán họ<PERSON>, lập trình và suy luận logic với chế độ \"không suy nghĩ\" cho đối thoại thông thường. Mô hình này đã được tinh chỉnh để sử dụng cho việc tuân theo chỉ dẫn, sử dụng công cụ đại lý, viết sáng tạo và các nhiệm vụ đa ngôn ngữ trên hơn 100 ngôn ngữ và phương ngữ. N<PERSON> xử lý ngữ cảnh 32K token một cách tự nhiên và có thể mở rộng lên 131K token bằng cách sử dụng mở rộng dựa trên YaRN."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B là mô hình hỗn hợp chuyên gia (MoE) với 235B tham số được phát triển bởi Qwen, kích hoạt 22B tham số mỗi lần truyền tiến. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho suy luận phức tạp, toán học và các nhiệm vụ mã với chế độ \"không suy nghĩ\" cho hiệu suất đối thoại thông thường. Mô hình này thể hiện khả năng suy luận mạnh mẽ, hỗ trợ đa ngôn ngữ (hơn 100 ngôn ngữ và phương ngữ), tuân theo chỉ dẫn nâng cao và khả năng gọi công cụ đại lý. Nó xử lý cửa sổ ngữ cảnh 32K token một cách tự nhiên và có thể mở rộng lên 131K token bằng cách sử dụng mở rộng dựa trên YaRN."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B là mô hình hỗn hợp chuyên gia (MoE) với 235B tham số được phát triển bởi Qwen, kích hoạt 22B tham số mỗi lần truyền tiến. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho suy luận phức tạp, toán học và các nhiệm vụ mã với chế độ \"không suy nghĩ\" cho hiệu suất đối thoại thông thường. Mô hình này thể hiện khả năng suy luận mạnh mẽ, hỗ trợ đa ngôn ngữ (hơn 100 ngôn ngữ và phương ngữ), tuân theo chỉ dẫn nâng cao và khả năng gọi công cụ đại lý. Nó xử lý cửa sổ ngữ cảnh 32K token một cách tự nhiên và có thể mở rộng lên 131K token bằng cách sử dụng mở rộng dựa trên YaRN."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 là thế hệ mới nhất trong dòng mô hình ngôn ngữ lớn Qwen, với kiến trúc hỗn hợp chuyên gia (MoE) dày đặc, thể hiện xuất sắc trong suy luận, hỗ trợ đa ngôn ngữ và các nhiệm vụ đại lý nâng cao. Kh<PERSON> năng chuyển đổi liền mạch giữa chế độ suy nghĩ cho suy luận phức tạp và chế độ không suy nghĩ cho đối thoại hiệu quả đảm bảo hiệu suất đa chức năng và chất lượng cao.\n\nQwen3 vượt trội hơn hẳn các mô hình trước như QwQ và Qwen2.5, cung cấp kh<PERSON> năng to<PERSON> họ<PERSON>, l<PERSON><PERSON> tr<PERSON><PERSON>, suy luận thông thườ<PERSON>, viết sáng tạo và đối thoại tương tác xuất sắc. <PERSON><PERSON><PERSON><PERSON> thể Qwen3-30B-A3B chứa 30,5 tỷ tham số (3,3 tỷ tham số kích hoạt), 48 lớp, 128 chuyên gia (mỗi nhiệm vụ kích hoạt 8), và hỗ trợ ngữ cảnh lên đến 131K token (sử dụng YaRN), thiết lập tiêu chuẩn mới cho các mô hình mã nguồn mở."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 là thế hệ mới nhất trong dòng mô hình ngôn ngữ lớn Qwen, với kiến trúc hỗn hợp chuyên gia (MoE) dày đặc, thể hiện xuất sắc trong suy luận, hỗ trợ đa ngôn ngữ và các nhiệm vụ đại lý nâng cao. Kh<PERSON> năng chuyển đổi liền mạch giữa chế độ suy nghĩ cho suy luận phức tạp và chế độ không suy nghĩ cho đối thoại hiệu quả đảm bảo hiệu suất đa chức năng và chất lượng cao.\n\nQwen3 vượt trội hơn hẳn các mô hình trước như QwQ và Qwen2.5, cung cấp kh<PERSON> năng to<PERSON> họ<PERSON>, l<PERSON><PERSON> tr<PERSON><PERSON>, suy luận thông thườ<PERSON>, viết sáng tạo và đối thoại tương tác xuất sắc. <PERSON><PERSON><PERSON><PERSON> thể Qwen3-30B-A3B chứa 30,5 tỷ tham số (3,3 tỷ tham số kích hoạt), 48 lớp, 128 chuyên gia (mỗi nhiệm vụ kích hoạt 8), và hỗ trợ ngữ cảnh lên đến 131K token (sử dụng YaRN), thiết lập tiêu chuẩn mới cho các mô hình mã nguồn mở."}, "qwen/qwen3-32b": {"description": "Qwen3-32B là một mô hình ngôn ngữ nguyên nhân dày đặc với 32,8 tỷ tham số trong dòng Qwen3, đ<PERSON><PERSON><PERSON> tối ưu hóa cho suy luận phức tạp và đối thoại hiệu quả. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho các nhiệm vụ như toán họ<PERSON>, lập trình và suy luận logic với chế độ \"không suy nghĩ\" cho đối thoại nhanh hơn và thông thường. Mô hình này thể hiện hiệu suất mạnh mẽ trong việc tuân theo chỉ dẫn, sử dụng công cụ đại lý, viết sáng tạo và các nhiệm vụ đa ngôn ngữ trên hơn 100 ngôn ngữ và phương ngữ. Nó xử lý ngữ cảnh 32K token một cách tự nhiên và có thể mở rộng lên 131K token bằng cách sử dụng mở rộng dựa trên YaRN."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B là một mô hình ngôn ngữ nguyên nhân dày đặc với 32,8 tỷ tham số trong dòng Qwen3, đ<PERSON><PERSON><PERSON> tối ưu hóa cho suy luận phức tạp và đối thoại hiệu quả. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho các nhiệm vụ như toán họ<PERSON>, lập trình và suy luận logic với chế độ \"không suy nghĩ\" cho đối thoại nhanh hơn và thông thường. Mô hình này thể hiện hiệu suất mạnh mẽ trong việc tuân theo chỉ dẫn, sử dụng công cụ đại lý, viết sáng tạo và các nhiệm vụ đa ngôn ngữ trên hơn 100 ngôn ngữ và phương ngữ. Nó xử lý ngữ cảnh 32K token một cách tự nhiên và có thể mở rộng lên 131K token bằng cách sử dụng mở rộng dựa trên YaRN."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B là một mô hình ngôn ngữ nguyên nhân dày đặc với 8,2 tỷ tham số trong dòng Qwen3, đ<PERSON><PERSON><PERSON> thiết kế cho các nhiệm vụ yêu cầu suy luận dày đặc và đối thoại hiệu quả. Nó hỗ trợ chuyển đổi liền mạch giữa chế độ \"suy nghĩ\" cho to<PERSON> họ<PERSON>, lập trình và suy luận logic với chế độ \"không suy nghĩ\" cho đối thoại thông thường. Mô hình này đã được tinh chỉnh để sử dụng cho việc tuân theo chỉ dẫn, tích hợp đại lý, viết sáng tạo và sử dụng đa ngôn ngữ trên hơn 100 ngôn ngữ và phương ngữ. Nó hỗ trợ cửa sổ ngữ cảnh 32K token và có thể mở rộng lên 131K token thông qua YaRN."}, "qwen2": {"description": "Qwen2 là mô hình ngôn ngữ quy mô lớn thế hệ mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2-72b-instruct": {"description": "Qwen2 là thế hệ mô hình ngôn ngữ lớn mới do đội Qwen phát triển. Nó dựa trên kiến trúc Transformer và sử dụng hàm kích hoạt SwiGLU, chệch QKV chú ý (attention QKV bias), chú ý truy vấn nhóm (group query attention), hỗn hợp chú ý cửa sổ trượt (mixture of sliding window attention) và chú ý đầy đủ. <PERSON><PERSON><PERSON><PERSON> ra, đội Qwen còn cải tiến bộ tách từ để thích ứng với nhiều ngôn ngữ tự nhiên và mã nguồn."}, "qwen2-7b-instruct": {"description": "Qwen2 là một loạt mô hình ngôn ngữ lớn mới do đội Qwen phát triển. Nó dựa trên kiến trúc Transformer và sử dụng hàm kích hoạt SwiGLU, chệch QKV chú ý (attention QKV bias), chú ý truy vấn nhóm (group query attention), hỗn hợp chú ý cửa sổ trượt (mixture of sliding window attention) và chú ý đầy đủ. <PERSON><PERSON><PERSON><PERSON> ra, đội Qwen còn cải tiến bộ tách từ để thích ứng với nhiều ngôn ngữ tự nhiên và mã nguồn."}, "qwen2.5": {"description": "Qwen2.5 là thế hệ mô hình ngôn ngữ quy mô lớn mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2.5-14b-instruct": {"description": "<PERSON><PERSON> hình 14B quy mô mở nguồn củ<PERSON> 2.5."}, "qwen2.5-14b-instruct-1m": {"description": "<PERSON><PERSON> hình quy mô 72B <PERSON><PERSON><PERSON><PERSON> mở nguồn từ <PERSON> 2.5."}, "qwen2.5-32b-instruct": {"description": "<PERSON><PERSON> <PERSON><PERSON>nh 32B quy mô mở nguồn củ<PERSON> 2.5."}, "qwen2.5-72b-instruct": {"description": "<PERSON><PERSON> <PERSON><PERSON>nh 72B quy mô mở nguồn củ<PERSON> 2.5."}, "qwen2.5-7b-instruct": {"description": "<PERSON><PERSON> hình 7B quy mô mở nguồn củ<PERSON> 2.5."}, "qwen2.5-coder-1.5b-instruct": {"description": "<PERSON><PERSON><PERSON> bản mã nguồn mở của mô hình mã Qwen."}, "qwen2.5-coder-14b-instruct": {"description": "<PERSON><PERSON><PERSON> bản mã nguồn mở của mô hình mã hóa <PERSON>."}, "qwen2.5-coder-32b-instruct": {"description": "<PERSON><PERSON><PERSON> bản mã nguồn mở của mô hình mã Qwen."}, "qwen2.5-coder-7b-instruct": {"description": "<PERSON><PERSON><PERSON> bản mã nguồn mở của mô hình mã Qwen."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder là mô hình ngôn ngữ lớn mới nhất trong series <PERSON><PERSON>, chuyên dụng cho lập trình (trước đây được gọi là CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 là phiên bản mới nhất của mô hình ngôn ngữ lớn Qwen. Đối với Qwen2.5, chúng tôi đã phát hành nhiều mô hình ngôn ngữ cơ sở và mô hình ngôn ngữ điều chỉnh theo lệnh, với phạm vi tham số từ 500 triệu đến 7,2 tỷ."}, "qwen2.5-math-1.5b-instruct": {"description": "<PERSON><PERSON>-<PERSON> có khả năng giải toán mạnh mẽ."}, "qwen2.5-math-72b-instruct": {"description": "<PERSON><PERSON>-<PERSON> có khả năng giải quyết bài toán toán học mạnh mẽ."}, "qwen2.5-math-7b-instruct": {"description": "<PERSON><PERSON>-<PERSON> có khả năng giải quyết bài toán toán học mạnh mẽ."}, "qwen2.5-omni-7b": {"description": "<PERSON><PERSON> hình <PERSON>-Omni hỗ trợ đầu vào từ nhiều loại dữ liệu khác nhau, bao gồm video, âm thanh, hình ảnh và văn bản, và xuất ra âm thanh và văn bản."}, "qwen2.5-vl-32b-instruct": {"description": "Dòng mô hình Qwen2.5-V<PERSON> đã nâng cao mức độ thông minh, t<PERSON><PERSON> thực tế và khả năng áp dụng, gi<PERSON><PERSON> mô hình hoạt động hiệu quả hơn trong các tình huống như đối thoại tự nhiên, sán<PERSON> tạo nội dung, cung cấp dịch vụ kiến thức chuyên môn và phát triển mã. Phiên bản 32B đã sử dụng công nghệ học tăng cường để tối ưu hóa mô hình, so với các mô hình khác trong dòng Qwen2.5 VL, cung cấp phong cách đầu ra phù hợp hơn với sở thích của con người, kh<PERSON> năng suy luận các vấn đề toán học phứ<PERSON> tạ<PERSON>, cũng như khả năng hiểu và suy luận chi tiết về hình ảnh."}, "qwen2.5-vl-72b-instruct": {"description": "Nâng cao khả năng theo dõi lệnh, to<PERSON>, g<PERSON><PERSON><PERSON> quyết vấn đề, mã hó<PERSON>, nâng cao khả năng nhận diện mọi thứ, hỗ trợ định vị chính xác các yếu tố thị giác từ nhiều định dạng khác nhau, hỗ trợ hiểu và định vị thời gian sự kiện trong các tệp video dài (tối đa 10 phút), c<PERSON> khả năng hiểu thứ tự thời gian và tốc độ, hỗ trợ điều khiển Agent trên OS hoặc Mobile dựa trên khả năng phân tích và định vị, khả năng trích xuất thông tin quan trọng và xuất định dạng Json mạnh mẽ, phiên bản này là phiên bản 72B, phiên bản mạnh nhất trong dòng sản phẩm này."}, "qwen2.5-vl-7b-instruct": {"description": "Nâng cao khả năng theo dõi lệnh, to<PERSON>, g<PERSON><PERSON><PERSON> quyết vấn đề, mã hó<PERSON>, nâng cao khả năng nhận diện mọi thứ, hỗ trợ định vị chính xác các yếu tố thị giác từ nhiều định dạng khác nhau, hỗ trợ hiểu và định vị thời gian sự kiện trong các tệp video dài (tối đa 10 phút), c<PERSON> khả năng hiểu thứ tự thời gian và tốc độ, hỗ trợ điều khiển Agent trên OS hoặc Mobile dựa trên khả năng phân tích và định vị, khả năng trích xuất thông tin quan trọng và xuất định dạng Json mạnh mẽ, phiên bản này là phiên bản 72B, phiên bản mạnh nhất trong dòng sản phẩm này."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL là phiên bản mới nhất của mô hình ngôn ngữ và hình ảnh trong gia đình mô hình <PERSON>."}, "qwen2.5:0.5b": {"description": "Qwen2.5 là thế hệ mô hình ngôn ngữ quy mô lớn mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2.5:1.5b": {"description": "Qwen2.5 là thế hệ mô hình ngôn ngữ quy mô lớn mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2.5:72b": {"description": "Qwen2.5 là thế hệ mô hình ngôn ngữ quy mô lớn mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2:0.5b": {"description": "Qwen2 là mô hình ngôn ngữ quy mô lớn thế hệ mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2:1.5b": {"description": "Qwen2 là mô hình ngôn ngữ quy mô lớn thế hệ mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen2:72b": {"description": "Qwen2 là mô hình ngôn ngữ quy mô lớn thế hệ mới củ<PERSON>, hỗ trợ các nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen3": {"description": "Qwen3 là mô hình ngôn ngữ quy mô lớn thế hệ mới củ<PERSON>, hỗ trợ nhu cầu ứng dụng đa dạng với hiệu suất xuất sắc."}, "qwen3-0.6b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-1.7b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-14b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-235b-a22b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-235b-a22b-instruct-2507": {"description": "<PERSON><PERSON> hình mã nguồn mở không ở chế độ suy nghĩ dựa trên Qwen3, so với phiên bản trước (Tongyi Qianwen 3-235B-A22B) có cải thiện nhẹ về khả năng sáng tạo chủ quan và an toàn mô hình."}, "qwen3-235b-a22b-thinking-2507": {"description": "<PERSON><PERSON> hình mã nguồn mở ở chế độ suy nghĩ dựa trên Qwen3, so với phiên bản trước (Tongyi Qianwen 3-235B-A22B) có cải thiện lớn về khả năng logic, năng lự<PERSON> chung, tăng cường kiến thức và khả năng sáng tạo, phù hợp cho các kịch bản suy luận phức tạp và khó."}, "qwen3-30b-a3b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-30b-a3b-instruct-2507": {"description": "So với phiên bản tr<PERSON><PERSON><PERSON> (Qwen3-30B-A3B), kh<PERSON> năng tổng quát của mô hình trong tiếng Trung, tiếng Anh và đa ngôn ngữ đã được cải thiện đáng kể. Mô hình được tối ưu hóa đặc biệt cho các nhiệm vụ mở và chủ quan, phù hợp hơn với sở thích người dùng và có thể cung cấp các phản hồi hữu ích hơn."}, "qwen3-30b-a3b-thinking-2507": {"description": "Dựa trên mô hình mã nguồn mở chế độ suy nghĩ của Qwen3, so với phiên bản trước (通义千问3-30B-A3B), kh<PERSON> năng logic, năng lực tổ<PERSON> qu<PERSON>t, kiến thức được tăng cường và khả năng sáng tạo đều được cải thiện đáng kể, phù hợp cho các kịch bản suy luận phức tạp và khó khăn."}, "qwen3-32b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-4b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-8b": {"description": "Qwen3 là một mô hình lớn thế hệ mới với khả năng vư<PERSON><PERSON> trội, đ<PERSON><PERSON> đ<PERSON><PERSON><PERSON> trình độ hàng đầu trong nhiều khả năng cốt lõi như suy luậ<PERSON>, t<PERSON><PERSON>u<PERSON>, đại lý và đa ngôn ngữ, đồng thời hỗ trợ chuyển đổi chế độ suy nghĩ."}, "qwen3-coder-480b-a35b-instruct": {"description": "<PERSON>ên bản mã nguồn mở của mô hình mã hóa Tong<PERSON>wen. M<PERSON> hình qwen3-coder-480b-a35b-instruct mới nhất dựa trên Qwen3, c<PERSON> kh<PERSON> năng Coding Agent mạnh mẽ, thành thạo gọi công cụ và tương tác môi trường, có thể lập trình tự chủ, vừa xuất sắc về mã hóa vừa có năng lực chung."}, "qwen3-coder-plus": {"description": "<PERSON>ô hình mã hóa Tong<PERSON>anwen. Dòng mô hình Qwen3-Coder-Plus mới nhất dựa trên Qwen3, có khả năng Coding Agent mạnh mẽ, thành thạo gọi công cụ và tương tác môi trường, có thể lập trình tự chủ, vừa xuất sắc về mã hóa vừa có năng lực chung."}, "qwq": {"description": "QwQ là một mô hình nghiên cứu thử nghiệm, tập trung vào việc nâng cao khả năng suy luận của AI."}, "qwq-32b": {"description": "Mô hình suy diễn QwQ được đào tạo dựa trên mô hình Qwen2.5-32B, đã đư<PERSON>c cải thiện đáng kể khả năng suy diễn của mô hình thông qua học tăng cường. Các chỉ số cốt lõi của mô hình như mã toán (AIME 24/25, LiveCodeBench) và một số chỉ số chung (IFEval, LiveBench, v.v.) đạt đến mức độ của phiên bản đầy đủ DeepSeek-R1, tất cả các chỉ số đều vượt trội so với DeepSeek-R1-Distill-Qwen-32B cũng dựa trên Qwen2.5-32B."}, "qwq-32b-preview": {"description": "<PERSON>ô hình QwQ là một mô hình nghiên cứu thử nghiệm được phát triển bởi đội ngũ <PERSON>, tập trung vào việc nâng cao khả năng suy luận của AI."}, "qwq-plus": {"description": "Mô hình suy luận QwQ dựa trên mô hình Qwen2.5, đã nâng cao đáng kể khả năng suy luận thông qua học tăng cường. Các chỉ số cốt lõi về to<PERSON> h<PERSON>, mã h<PERSON><PERSON> (AIME 24/25, LiveCodeBench) và một số chỉ số chung (IFEval, LiveBench, v.v.) đạt mức tương đương phiên bản đầy đủ của DeepSeek-R1."}, "qwq_32b": {"description": "Mô hình suy luận có quy mô trung bình trong dòng Qwen. So với các mô hình tinh chỉnh hướng dẫn truyền thống, QwQ có khả năng suy nghĩ và suy luận, có thể nâng cao hiệu suất đáng kể trong các nhiệm vụ hạ nguồn, đặc biệt là trong việc giải quyết các vấn đề khó khăn."}, "r1-1776": {"description": "R1-1776 là một phiên bản của mô hình DeepSeek R1, đ<PERSON> đ<PERSON><PERSON><PERSON> huấn luyệ<PERSON> lạ<PERSON>, cung cấp thông tin sự thật chưa đư<PERSON><PERSON> kiểm duyệt và không thiên lệch."}, "solar-mini": {"description": "Solar Mini là một LLM dạng nhỏ gọn, hi<PERSON><PERSON> suất vượt trội hơn GPT-3.5, c<PERSON> khả năng đa ngôn ngữ mạnh mẽ, hỗ trợ tiếng Anh và tiếng <PERSON>, cung cấp giải pháp hiệu quả và nhỏ gọn."}, "solar-mini-ja": {"description": "Solar Mini (Ja) mở rộng khả năng của Solar Mini, tập trung vào tiế<PERSON>, đồng thời duy trì hiệu quả và hiệu suất xuất sắc trong việc sử dụng tiếng Anh và tiếng Hàn."}, "solar-pro": {"description": "Solar Pro là một LLM thông minh cao do Upstage phá<PERSON> hành, tập trung vào khả năng tuân theo hướng dẫn trên một GPU, đạt điểm IFEval trên 80. Hiện tại hỗ trợ tiế<PERSON>, phi<PERSON><PERSON> bả<PERSON> ch<PERSON>h thức dự kiến ra mắt vào tháng 11 năm 2024, sẽ mở rộng hỗ trợ ngôn ngữ và độ dài ngữ cảnh."}, "sonar": {"description": "<PERSON><PERSON><PERSON> phẩm tìm kiếm nhẹ dựa trên ngữ cảnh tìm kiếm, n<PERSON>h hơn và rẻ hơn so với Sonar Pro."}, "sonar-deep-research": {"description": "<PERSON><PERSON><PERSON><PERSON> cứu sâu tiến hành nghiên cứu chuyên gia toàn diện và tổng hợp thành các báo cáo có thể truy cập và có thể hành động."}, "sonar-pro": {"description": "<PERSON><PERSON>n phẩm tìm kiếm nâng cao hỗ trợ ngữ cảnh tìm kiếm, cho phép truy vấn và theo dõi nâng cao."}, "sonar-reasoning": {"description": "<PERSON>ản phẩm API mới được hỗ trợ bởi mô hình suy luận của DeepSeek."}, "sonar-reasoning-pro": {"description": "<PERSON>ản phẩm API mới được hỗ trợ bởi mô hình suy diễn DeepSeek."}, "stable-diffusion-3-medium": {"description": "<PERSON><PERSON> hình tạo hình ảnh từ văn bản mới nhất do Stability AI phát hành. Phiên bản này kế thừa ưu điểm của thế hệ trước, cải tiến đáng kể về chất lư<PERSON>, hiểu văn bản và đa dạng phong cách, có thể giải thích chính xác các gợi ý ngôn ngữ tự nhiên phức tạp và tạo ra hình ảnh chính xác, đa dạng hơn."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large là mô hình tạo hình ảnh từ văn bản đa phương thức khuếch tán biến áp (MMDiT) với 800 triệu tham số, c<PERSON> chất lượng hình ảnh xuất sắc và độ khớp gợi ý cao, hỗ trợ tạo hình ảnh độ phân giải cao 1 triệu pixel, đồng thời vận hành hiệu quả trên phần cứng tiêu dùng phổ thông."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo là mô hình dựa trên stable-diffusion-3.5-large, sử dụng kỹ thuật chưng cất khu<PERSON>ch tán đ<PERSON> (ADD), c<PERSON> tốc độ n<PERSON>h hơn."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 đư<PERSON><PERSON> khởi tạo từ trọng số checkpoint stable-diffusion-v1.2, đ<PERSON><PERSON><PERSON> tinh chỉnh 595k bước ở độ phân giải 512x512 trên \"laion-aesthetics v2 5+\", gi<PERSON><PERSON> 10% điều kiện hóa văn bản để cải thiện lấy mẫu hướng dẫn không bộ phân loại."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl có cải tiến lớn so với v1.5 và đạt hiệu quả tương đương mô hình SOTA mã nguồn mở hiện tại như midjourney. Cải tiến cụ thể bao gồm: unet backbone lớn hơn gấp 3 lần; thêm module tinh chỉnh để cải thiện chất lượng hình ảnh tạo ra; kỹ thuật huấn luyện hiệu quả hơn."}, "stable-diffusion-xl-base-1.0": {"description": "<PERSON>ô hình tạo hình ảnh từ văn bản quy mô lớn do Stability AI phát triển và mã nguồn mở, có khả năng tạo hình ảnh sáng tạo đứng đầu ngành. <PERSON><PERSON> khả năng hiểu chỉ dẫn xuất sắc, hỗ trợ định nghĩa prompt ngược để tạo nội dung chính xác."}, "step-1-128k": {"description": "<PERSON><PERSON> bằng hiệu suất và chi phí, phù hợp cho các tình huống chung."}, "step-1-256k": {"description": "<PERSON><PERSON>h<PERSON> năng xử lý ngữ cảnh siêu dài, đặc biệt phù hợp cho phân tích tài liệu dài."}, "step-1-32k": {"description": "Hỗ trợ đối thoại có độ dài trung bình, phù hợp cho nhiều tình huống ứng dụng."}, "step-1-8k": {"description": "<PERSON><PERSON> hình nhỏ, phù hợp cho các nhiệm vụ nhẹ."}, "step-1-flash": {"description": "<PERSON><PERSON> hình tốc độ cao, phù hợp cho đối tho<PERSON>i thời gian thực."}, "step-1.5v-mini": {"description": "<PERSON><PERSON> hình này có khả năng hiểu video mạnh mẽ."}, "step-1o-turbo-vision": {"description": "<PERSON>ô hình này có khả năng hiểu hình ảnh mạnh mẽ, vư<PERSON><PERSON> trội hơn 1o trong lĩnh vực toán học và mã. Mô hình nhỏ hơn 1o và có tốc độ xuất ra nhanh hơn."}, "step-1o-vision-32k": {"description": "<PERSON>ô hình này có khả năng hiểu hình ảnh mạnh mẽ. So với các mô hình trong series step-1v, nó có hiệu suất thị gi<PERSON>c vượt trội hơn."}, "step-1v-32k": {"description": "Hỗ trợ đầu và<PERSON> hình <PERSON>, tăng cường trải nghiệm tương tác đa mô hình."}, "step-1v-8k": {"description": "<PERSON><PERSON> hình thị giác nhỏ, phù hợp cho các nhiệm vụ cơ bản về văn bản và hình ảnh."}, "step-1x-edit": {"description": "Mô hình tập trung vào tác vụ chỉnh sửa hình ảnh, có thể sửa đổi và nâng cao hình ảnh dựa trên hình ảnh và mô tả văn bản do người dùng cung cấp. Hỗ trợ nhiều định dạng đầu vào, bao gồm mô tả văn bản và hình ảnh mẫu. Mô hình hiểu ý định người dùng và tạo ra kết quả chỉnh sửa hình ảnh phù hợp."}, "step-1x-medium": {"description": "<PERSON>ô hình có khả năng tạo hình ảnh mạnh mẽ, hỗ trợ đầu vào mô tả văn bản. Hỗ trợ tiếng Trung bản địa, có thể hiểu và xử lý mô tả văn bản tiếng Trung tốt hơn, nắm bắt chính xác thông tin ngữ nghĩa trong mô tả và chuyển đổi thành đặc trưng hình ảnh, từ đó tạo hình ảnh chính xác hơn. Mô hình có thể tạo hình ảnh độ phân giải cao, chất lượng tốt và có khả năng chuyển đổi phong cách nhất định."}, "step-2-16k": {"description": "Hỗ trợ tương tác ngữ cảnh quy mô lớn, phù hợp cho các tình huống đối thoại phức tạp."}, "step-2-16k-exp": {"description": "<PERSON><PERSON><PERSON> bản thử nghiệm của mô hình step-2, bao gồm các tính năng mới nhất, đang đ<PERSON><PERSON><PERSON> cập nhật liên tục. <PERSON><PERSON><PERSON><PERSON> khuyến nghị sử dụng trong môi trường sản xuất ch<PERSON>h thức."}, "step-2-mini": {"description": "<PERSON><PERSON> hình lớn siêu tốc dựa trên kiến trúc Attention tự nghiên cứu thế hệ mới MFA, đạt đư<PERSON><PERSON> hiệu quả tương tự như step1 với chi phí rất thấp, đồ<PERSON> thời duy trì thông lượng cao hơn và độ trễ phản hồi nhanh hơn. <PERSON><PERSON> khả năng xử lý các nhiệm vụ chung, đặc biệt có năng lực trong lập trình."}, "step-2x-large": {"description": "<PERSON>ô hình tạo hình ảnh thế hệ mới của Step <PERSON>, tập trung vào tác vụ tạo hình <PERSON>, có thể tạo ra hình ảnh chất lượng cao dựa trên mô tả văn bản do người dùng cung cấp. Mô hình mới tạo ra hình ảnh có cảm gi<PERSON><PERSON> thự<PERSON>ơn, kh<PERSON> năng tạo chữ tiếng Trung và tiếng Anh mạnh hơn."}, "step-r1-v-mini": {"description": "<PERSON>ô hình này là một mô hình suy luận lớn với khả năng hiểu hình ảnh mạnh mẽ, có thể xử lý thông tin hình ảnh và văn bản, và xuất ra nội dung văn bản sau khi suy nghĩ sâu. Mô hình này thể hiện xuất sắc trong lĩnh vực suy luận hình ảnh, đồng thời có khả năng to<PERSON> học, mã và suy luận văn bản hàng đầu. Độ dài ngữ cảnh là 100k."}, "taichu_llm": {"description": "<PERSON><PERSON> hình ngôn ngữ lớn Taichu có khả năng hiểu ngôn ngữ mạnh mẽ và các khả năng như sáng tạo văn bản, tr<PERSON> lời câu hỏi ki<PERSON><PERSON> thứ<PERSON>, l<PERSON><PERSON> tr<PERSON><PERSON> mã, t<PERSON><PERSON> to<PERSON> to<PERSON> h<PERSON>, suy luận logic, phân tích cảm xúc, tóm tắt văn bản. Đổi mới kết hợp giữa đào tạo trước với dữ liệu phong phú từ nhiều nguồn, thông qua việc liên tục cải tiến công nghệ thuật toán và hấp thụ kiến thức mới từ dữ liệu văn bản khổng lồ, giúp mô hình ngày càng hoàn thiện. Cung cấp thông tin và dịch vụ tiện lợi hơn cho người dùng cùng trải nghiệm thông minh hơn."}, "taichu_o1": {"description": "taichu_o1 là mô hình suy luận lớn thế hệ mới, đạt được chuỗi suy nghĩ giống con người thông qua tương tác đa phương tiện và học tăng cường, hỗ trợ suy diễn quyết định phức tạp, đồng thời thể hiện con đường suy luận có thể mô hình hóa trong khi duy trì đầu ra chính xác cao, phù hợp cho phân tích chiến lược và suy nghĩ sâu."}, "taichu_vl": {"description": "<PERSON><PERSON><PERSON> hợ<PERSON> khả năng hiể<PERSON> hình <PERSON>, chuy<PERSON><PERSON> giao ki<PERSON><PERSON> thức, suy luận logic, thể hiện xuất sắc trong lĩnh vực hỏi đáp hình ảnh và văn bản."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct có 80 tỷ tham số, kích hoạt 13 tỷ tham số để đạt hiệu năng tương đương các mô hình lớn hơn, hỗ trợ suy luận kết hợp “tư duy n<PERSON>/tư duy chậm”; khả năng hiểu văn bản dài ổn định; đư<PERSON><PERSON> xác nhận qua BFCL-v3 và τ-Ben<PERSON>, năng lực Agent dẫn đầu; kết hợp GQA và nhiều định dạng lượng tử hóa, đạt hiệu quả suy luận cao."}, "text-embedding-3-large": {"description": "<PERSON><PERSON> hình vector hóa mạnh mẽ nhất, phù hợp cho các nhiệm vụ tiếng Anh và không phải tiếng <PERSON>h."}, "text-embedding-3-small": {"description": "<PERSON><PERSON> hình Embedding thế hệ mới hiệu quả và tiết ki<PERSON>, phù hợp cho tìm kiếm ki<PERSON>n thức, <PERSON><PERSON> dụng RAG và các tình huống khác."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 là một mô hình ngôn ngữ mở với trọng số 32B song ngữ (Trung-Anh), đ<PERSON><PERSON><PERSON> tối ưu hóa cho việc tạo mã, gọi hàm và các nhiệm vụ theo kiểu đại lý. Nó đã được huấn luyện trước trên 15T dữ liệu chất lượng cao và dữ liệu suy luận lại, và được hoàn thiện thêm bằng cách sử dụng sự phù hợp với sở thích của con người, lấy mẫu từ chối và học tăng cường. Mô hình này thể hiện xuất sắc trong suy luận phức tạp, tạo ra sản phẩm và các nhiệm vụ đầu ra có cấu trúc, đ<PERSON><PERSON> được hiệu suất tương đương với GPT-4o và DeepSeek-V3-0324 trong nhiều bài kiểm tra chuẩn."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 là một mô hình ngôn ngữ mở với trọng số 32B song ngữ (Trung-Anh), đ<PERSON><PERSON><PERSON> tối ưu hóa cho việc tạo mã, gọi hàm và các nhiệm vụ theo kiểu đại lý. Nó đã được huấn luyện trước trên 15T dữ liệu chất lượng cao và dữ liệu suy luận lại, và được hoàn thiện thêm bằng cách sử dụng sự phù hợp với sở thích của con người, lấy mẫu từ chối và học tăng cường. Mô hình này thể hiện xuất sắc trong suy luận phức tạp, tạo ra sản phẩm và các nhiệm vụ đầu ra có cấu trúc, đ<PERSON><PERSON> được hiệu suất tương đương với GPT-4o và DeepSeek-V3-0324 trong nhiều bài kiểm tra chuẩn."}, "thudm/glm-4-9b-chat": {"description": "<PERSON><PERSON>n bản mã nguồn mở của thế hệ mô hình tiền huấn luyện GLM-4 mới nhất được phát hành bởi Zhiyu AI."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 là mô hình ngôn ngữ 9 tỷ tham số trong dòng GLM-4 đ<PERSON><PERSON><PERSON> phát triển bởi THUDM. GLM-4-9B-0414 sử dụng cùng một chiến lược học tăng cường và căn chỉnh như mô hình tương ứng lớn hơn 32B, đạt được hiệu suất cao so với quy mô của nó, khiến nó phù hợp cho các triển khai hạn chế tài nguyên nhưng vẫn cần khả năng hiểu và tạo ngôn ngữ mạnh mẽ."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 là biến thể suy luận nâng cao của GLM-4-32B, đ<PERSON><PERSON><PERSON> xây dựng cho việc gi<PERSON>i quyết các vấn đề sâu về toán học, logic và lập trình. <PERSON>ó áp dụng học tăng cường mở rộng (cụ thể cho nhiệm vụ và dựa trên sở thích cặp chung) để cải thiện hiệu suất cho các nhiệm vụ phức tạp nhiều bước. So với mô hình GLM-4-32B c<PERSON> bản, Z1 đã nâng cao đáng kể khả năng suy luận có cấu trúc và trong các lĩnh vực chính thức.\n\nMô hình này hỗ trợ thực hiện các bước 'suy nghĩ' thông qua kỹ thuật nhắc nhở và cung cấp tính liên kết cải thiện cho đầu ra định dạng dài. <PERSON><PERSON> được tối ưu hóa cho quy trình làm việc của đại lý và hỗ trợ ngữ cảnh dài (thông qua YaRN), gọi công cụ JSON và cấu hình lấy mẫu chi tiết cho suy luận ổn định. Rất phù hợp cho các trường hợp cần suy nghĩ sâu sắc, suy luận nhiều bước hoặc suy diễn chính thức."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 là biến thể suy luận nâng cao của GLM-4-32B, đ<PERSON><PERSON><PERSON> xây dựng cho việc gi<PERSON>i quyết các vấn đề sâu về toán học, logic và lập trình. <PERSON>ó áp dụng học tăng cường mở rộng (cụ thể cho nhiệm vụ và dựa trên sở thích cặp chung) để cải thiện hiệu suất cho các nhiệm vụ phức tạp nhiều bước. So với mô hình GLM-4-32B c<PERSON> bản, Z1 đã nâng cao đáng kể khả năng suy luận có cấu trúc và trong các lĩnh vực chính thức.\n\nMô hình này hỗ trợ thực hiện các bước 'suy nghĩ' thông qua kỹ thuật nhắc nhở và cung cấp tính liên kết cải thiện cho đầu ra định dạng dài. <PERSON><PERSON> được tối ưu hóa cho quy trình làm việc của đại lý và hỗ trợ ngữ cảnh dài (thông qua YaRN), gọi công cụ JSON và cấu hình lấy mẫu chi tiết cho suy luận ổn định. Rất phù hợp cho các trường hợp cần suy nghĩ sâu sắc, suy luận nhiều bước hoặc suy diễn chính thức."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 là mô hình ngôn ngữ 9B trong dòng GLM-4 đư<PERSON><PERSON> phát triển bởi THUDM. Nó áp dụng các kỹ thuật ban đầu được sử dụng cho mô hình GLM-Z1 lớ<PERSON> hơ<PERSON>, bao gồm học tăng cường mở rộng, căn chỉnh xếp hạng cặp và đào tạo cho các nhiệm vụ yêu cầu suy luận dày đặc như toán họ<PERSON>, mã và logic. Mặc dù quy mô nhỏ hơn, nhưng nó thể hiện hiệu suất mạnh mẽ trong các nhiệm vụ suy luận tổng quát và vượt trội hơn nhiều mô hình mã nguồn mở ở cấp độ trọng số của nó."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B là mô hình suy luận sâu với 32B tham số trong dòng GLM-4-Z1, đ<PERSON><PERSON><PERSON> tối ưu hóa cho các nhiệm vụ phức tạp, mở cần suy nghĩ lâu dài. N<PERSON> được xây dựng trên nền tảng glm-4-32b-0414, tăng cường thêm giai đoạn học tăng cường và chiến lược căn chỉnh đa giai đoạn, giới thiệu khả năng \"phản tư\" nhằm mô phỏng quá trình xử lý nhận thức mở rộng. Điều này bao gồm suy luận lặp đi lặp lại, phân tích đa bước và quy trình làm việc tăng cường công cụ như tìm kiếm, truy xuất và tổng hợp nhận thức trích dẫn.\n\n<PERSON><PERSON> hình này thể hiện xuất sắc trong viết nghiên cứu, phân tích so sánh và câu hỏi phức tạp. Nó hỗ trợ gọi hàm cho các nguyên ngữ tìm kiếm và điều hướng (`search`, `click`, `open`, `finish`), cho phép sử dụng trong quy trình đại lý. Hành vi phản tư được hình thành bởi các phần thưởng dựa trên quy tắc và cơ chế quyết định trì hoãn trong kiểm soát vòng lặp đa vòng, và được chuẩn hóa theo các khung nghiên cứu sâu như ngăn xếp căn chỉnh nội bộ của OpenAI. Biến thể này phù hợp cho các tình huống cần độ sâu hơn là tốc độ."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-<PERSON><PERSON><PERSON> đượ<PERSON> tạo ra bằng cách kết hợp DeepSeek-R1 và DeepSeek-V3 (0324), kết hợ<PERSON> khả năng suy luận của R1 và cải tiến hiệu quả token của V3. Nó dựa trên kiến trúc DeepSeek-MoE Transformer và được tối ưu hóa cho các nhiệm vụ tạo văn bản tổng quát.\n\nMô hình này kết hợp trọng số tiền huấn luyện của hai mô hình nguồn để cân bằng hiệu suất trong suy luận, hiệu quả và các nhiệm vụ tuân theo chỉ dẫn. <PERSON><PERSON> được phát hành theo giấy phép MIT, nhằm mục đích sử dụng cho nghiên cứu và thương mại."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) cung cấp khả năng tính toán nâng cao thông qua chiến lược và kiến trúc mô hình hiệu quả."}, "tts-1": {"description": "<PERSON><PERSON> hình chuyển văn bản thành giọng nói mới nhất, tối ưu hóa tốc độ cho các tình huống thời gian thực."}, "tts-1-hd": {"description": "<PERSON><PERSON> hình chuyển văn bản thành giọng nói mới nhất, tối ưu hóa cho chất lư<PERSON>."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) phù hợp cho các nhiệm vụ chỉ dẫn tinh vi, cung cấp khả năng xử lý ngôn ngữ xuất sắc."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 <PERSON><PERSON> nâng cao tiêu chu<PERSON> ng<PERSON>nh, hi<PERSON><PERSON> su<PERSON>t vư<PERSON><PERSON> trội so với các mô hình cạnh tranh và Claude 3 Opus, thể hiện xuất sắc trong nhiều đ<PERSON>, đồng thời có tốc độ và chi phí tương đương với các mô hình tầm trung của chúng tôi."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 sonnet là mô hình thế hệ tiếp the<PERSON> <PERSON><PERSON> nh<PERSON>t của Anthropic. So v<PERSON><PERSON> 3 <PERSON><PERSON>, <PERSON> 3.7 Sonnet đã cải thiện ở nhiều kỹ năng và vượt qua mô hình lớn nhất thế hệ trước là Claude 3 Opus trong nhiều bài kiểm tra trí tuệ."}, "v0-1.0-md": {"description": "<PERSON><PERSON> hình v0-1.0-md là phiên bản cũ được cung cấp dịch vụ qua API v0"}, "v0-1.5-lg": {"description": "<PERSON><PERSON> hình v0-1.5-lg phù hợp cho các nhiệ<PERSON> vụ suy nghĩ hoặc lý luận nâng cao"}, "v0-1.5-md": {"description": "<PERSON><PERSON> hình v0-1.5-md phù hợp cho các nhiệm vụ hàng ngày và tạo giao diện người dùng (UI)"}, "wan2.2-t2i-flash": {"description": "<PERSON><PERSON><PERSON> bản tốc độ cao <PERSON> 2.2, là mô hình mới nhất hiện nay. <PERSON><PERSON><PERSON> cấp to<PERSON>n diện về sáng t<PERSON>o, <PERSON><PERSON> định và cảm g<PERSON><PERSON><PERSON>, tốc độ tạ<PERSON>, hi<PERSON><PERSON> quả chi phí cao."}, "wan2.2-t2i-plus": {"description": "<PERSON><PERSON><PERSON> bản chuyên nghiệ<PERSON> 2.2, là mô hình mới nhất hiện nay. <PERSON><PERSON><PERSON> cấp toàn diện về sáng t<PERSON>o, <PERSON><PERSON> định và cảm g<PERSON><PERSON><PERSON>ự<PERSON>, tạo chi tiết phong phú."}, "wanx-v1": {"description": "<PERSON><PERSON> hình tạo hình ảnh từ văn bản c<PERSON> bản, tư<PERSON><PERSON>ng với mô hình chung 1.0 trên trang ch<PERSON>h thức <PERSON>."}, "wanx2.0-t2i-turbo": {"description": "<PERSON><PERSON><PERSON><PERSON> về chân dung có cảm g<PERSON><PERSON><PERSON>ực, tốc độ trung bình, chi phí thấp. Tương ứng với mô hình tốc độ cao 2.0 trên trang ch<PERSON>h thức <PERSON>."}, "wanx2.1-t2i-plus": {"description": "<PERSON><PERSON><PERSON> bả<PERSON> nâng cấ<PERSON><PERSON><PERSON>, t<PERSON><PERSON> hình <PERSON>nh chi tiết phong phú hơn, tốc đ<PERSON> hơi chậm. Tương ứng với mô hình chuyên nghiệp 2.1 trên trang chính thức <PERSON>."}, "wanx2.1-t2i-turbo": {"description": "<PERSON><PERSON><PERSON> bả<PERSON> nâng cấ<PERSON> to<PERSON><PERSON>, tốc độ tạ<PERSON>, hi<PERSON><PERSON> qu<PERSON> toà<PERSON>, chi phí tổng hợp cao. Tương ứng với mô hình tốc độ cao 2.1 trên trang ch<PERSON>h thức <PERSON>."}, "whisper-1": {"description": "<PERSON><PERSON> hình nhận dạng giọng nói đa năng, hỗ trợ nhận dạng giọng nói đa ngôn ngữ, dị<PERSON> giọng nói và nhận diện ngôn ngữ."}, "wizardlm2": {"description": "WizardLM 2 là mô hình ngôn ngữ do Microsoft AI cung cấp, đặc biệt xuất sắc trong các lĩnh vực đối thoại phức tạp, đa ngôn ngữ, suy luận và trợ lý thông minh."}, "wizardlm2:8x22b": {"description": "WizardLM 2 là mô hình ngôn ngữ do Microsoft AI cung cấp, đặc biệt xuất sắc trong các lĩnh vực đối thoại phức tạp, đa ngôn ngữ, suy luận và trợ lý thông minh."}, "x1": {"description": "<PERSON><PERSON> hình Spark X1 sẽ đượ<PERSON> nâng cấp thêm, trên nền tảng dẫn đầu trong các nhiệm vụ toán học trong nước, đạt được hiệu quả trong các nhiệm vụ chung như su<PERSON> lu<PERSON>, t<PERSON><PERSON> vă<PERSON> bả<PERSON>, hi<PERSON><PERSON> ngôn ngữ tương đương với OpenAI o1 và DeepSeek R1."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 là phiên bản nâng cấp của Yi. <PERSON><PERSON> sử dụng 500B token từ cơ sở dữ liệu chất lượng cao để tiếp tục tiền huấn luyện trên <PERSON>, và đượ<PERSON> tinh chỉnh trên 3M mẫu đa dạng."}, "yi-large": {"description": "<PERSON><PERSON> hình với hàng trăm tỷ tham số mới, cung cấp khả năng hỏi đáp và sinh văn bản mạnh mẽ."}, "yi-large-fc": {"description": "Hỗ trợ và tăng cường khả năng gọi công cụ trên cơ sở mô hình yi-large, phù hợp cho nhiều tình huống kinh doanh cần xây dựng agent hoặc workflow."}, "yi-large-preview": {"description": "<PERSON><PERSON><PERSON> bản ban đầu, khuyến nghị sử dụng yi-large (phiên bản mới)."}, "yi-large-rag": {"description": "<PERSON><PERSON><PERSON> vụ cao cấp dựa trên mô hình yi-large mạnh mẽ, kế<PERSON> hợp công nghệ tìm kiếm và sinh để cung cấp câu trả lời ch<PERSON> x<PERSON>c, dịch vụ tìm kiếm thông tin toàn mạng theo thời gian thực."}, "yi-large-turbo": {"description": "Hiệu suất vư<PERSON><PERSON> trội với chi phí hợp lý. Tối ưu hóa độ chính xác cao dựa trên hiệ<PERSON> suất, tốc độ suy luận và chi phí."}, "yi-lightning": {"description": "<PERSON><PERSON> hình hiệu suất cao mới nh<PERSON>t, đ<PERSON><PERSON> bảo đầu ra chất lượng cao trong khi tốc độ suy luận đư<PERSON><PERSON> cải thiện đáng kể."}, "yi-lightning-lite": {"description": "<PERSON><PERSON><PERSON> bản nhẹ, <PERSON><PERSON><PERSON><PERSON> khu<PERSON>ến nghị sử dụng yi-lightning."}, "yi-medium": {"description": "<PERSON><PERSON> hình kích thước trung bình được nâng cấp và tinh chỉnh, kh<PERSON> năng cân bằng, chi phí hiệu quả cao. Tối ưu hóa sâu khả năng tuân theo chỉ dẫn."}, "yi-medium-200k": {"description": "<PERSON><PERSON>a sổ ngữ cảnh siêu dài 200K, cung cấp khả năng hiểu và sinh văn bản sâu cho các văn bản dài."}, "yi-spark": {"description": "<PERSON><PERSON> hình nhỏ gọn và nhanh chóng. <PERSON><PERSON> cấp khả năng tính toán toán học và viết mã được tăng cường."}, "yi-vision": {"description": "<PERSON><PERSON> hình cho các nhiệm vụ hình <PERSON>nh phức tạp, cung cấp kh<PERSON> năng hiểu và phân tích hình ảnh hiệu suất cao."}, "yi-vision-v2": {"description": "<PERSON><PERSON> hình nhiệm vụ thị gi<PERSON>c phứ<PERSON> tạp, cung cấp kh<PERSON> năng hiểu và phân tích hiệu suất cao dựa trên nhiều hình <PERSON>nh."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 là mô hình nền tảng dành cho ứng dụng tác nhân thông minh, sử dụng kiến trúc chuyên gia hỗn hợp (Mixture-of-Experts). Đư<PERSON><PERSON> tối ưu sâu trong các lĩnh vực gọi công cụ, duy<PERSON><PERSON> web, kỹ thuật phần mềm và lập trình front-end, hỗ trợ tích hợp liền mạch vào các tác nhân mã nh<PERSON>, Roo Code. GLM-4.5 sử dụng chế độ suy luận hỗn hợp, thích <PERSON>ng với nhiều kịch bản ứng dụng như suy luận phức tạp và sử dụng hàng ngày."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air là mô hình nền tảng dành cho ứng dụng tác nhân thông minh, sử dụng kiến trúc chuyên gia hỗn hợp (Mixture-of-Experts). Đ<PERSON><PERSON><PERSON> tối ưu sâu trong các lĩnh vực gọi công cụ, duy<PERSON>t web, kỹ thuật phần mềm và lập trình front-end, hỗ trợ tích hợp liền mạch vào các tác nhân mã như <PERSON>, Roo Code. GLM-4.5 sử dụng chế độ suy luận hỗn hợp, thích <PERSON>ng với nhiều kịch bản ứng dụng như suy luận phức tạp và sử dụng hàng ngày."}}