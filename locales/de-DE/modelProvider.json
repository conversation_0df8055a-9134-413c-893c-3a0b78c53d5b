{"azure": {"azureApiVersion": {"desc": "Die API-Version von Azure im Format JJJJ-MM-TT, siehe [neueste Version](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "Liste abrufen", "title": "Azure-API-Version"}, "empty": "Geben Sie eine Modell-ID ein, um das erste Modell hinzuzufügen", "endpoint": {"desc": "<PERSON>sen Wert finden Sie im Abschnitt 'Schlüssel und Endpunkte', wenn Sie in Azure Portal Ihre Ressource überprüfen", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure-API-Adresse"}, "modelListPlaceholder": "Wählen Sie ein bereitgestelltes OpenAI-Modell aus oder fügen Sie eines hinzu", "title": "Azure OpenAI", "token": {"desc": "<PERSON>sen Wert finden Sie im Abschnitt 'Schlüssel und Endpunkte', wenn Sie in Azure Portal Ihre Ressource überprüfen. Sie können KEY1 oder KEY2 verwenden", "placeholder": "Azure API Key", "title": "API Key"}}, "azureai": {"azureApiVersion": {"desc": "API-Version von Azure, im Format YYYY-MM-DD, siehe [aktuelle Version](https://learn.microsoft.com/de-de/azure/ai-services/openai/reference#chat-completions)", "fetch": "Liste abrufen", "title": "Azure API-Version"}, "endpoint": {"desc": "Finden Sie den Endpunkt für die Modellinferenz von Azure AI im Überblick über das Azure AI-Projekt", "placeholder": "https://ai-userxxxxxxxxxx.services.ai.azure.com/models", "title": "Azure AI-Endpunkt"}, "title": "Azure OpenAI", "token": {"desc": "Finden Sie den API-Schlüssel im Überblick über das Azure AI-Projekt", "placeholder": "Azure-Schl<PERSON>ssel", "title": "Schlüssel"}}, "bedrock": {"accessKeyId": {"desc": "Geben Sie Ihre AWS Access Key Id ein", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "<PERSON><PERSON>, ob AccessKeyId / SecretAccessKey korrekt eingegeben wurden"}, "region": {"desc": "Geben Sie Ihre AWS Region ein", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "<PERSON><PERSON><PERSON>e Ihren AWS Secret Access Key ein", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Wenn Sie AWS SSO/STS verwenden, geben Sie Ihr AWS Session Token ein", "placeholder": "AWS Session Token", "title": "AWS Session Token (optional)"}, "title": "Bedrock", "unlock": {"customRegion": "Benutzerdefinierter Regionsservice", "customSessionToken": "Benutzerdefiniertes Sitzungstoken", "description": "Geben Sie Ihren AWS AccessKeyId / SecretAccessKey ein, um das Gespräch zu beginnen. Die App speichert Ihre Authentifizierungsinformationen nicht.", "imageGenerationDescription": "Geben Sie Ihre AWS AccessKeyId / SecretAccessKey ein, um mit der Generierung zu beginnen. Die Anwendung speichert Ihre Authentifizierungsdaten nicht.", "title": "Verwenden Sie benutzerdefinierte Bedrock-Authentifizierungsinformationen"}}, "cloudflare": {"apiKey": {"desc": "Bitte füllen Sie die Cloudflare API Key", "placeholder": "Cloudflare API Key", "title": "Cloudflare API Key"}, "baseURLOrAccountID": {"desc": "Eingeben Sie die Cloudflare-Kundenkennung oder die benutzerdefinierte API-Adresse", "placeholder": "Cloudflare-Kundenkennung / benutzerdefinierte API-Adresse", "title": "Cloudflare-Kundenkennung / API-Adresse"}}, "createNewAiProvider": {"apiKey": {"placeholder": "Bitte geben Sie Ihren API-Schlüssel ein", "title": "API-Schlüssel"}, "basicTitle": "Grundinformationen", "configTitle": "Konfigurationsinformationen", "confirm": "<PERSON><PERSON>", "createSuccess": "Erstellung erfolgreich", "description": {"placeholder": "Beschreibung des Anbieters (optional)", "title": "Beschreibung des Anbieters"}, "id": {"desc": "Eindeutige Kennung des Anbieters, die nach der Erstellung nicht mehr geändert werden kann", "format": "<PERSON><PERSON> nur <PERSON><PERSON> Z<PERSON>en, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (-) und <PERSON>terstrichen (_) bestehen", "placeholder": "Empfohlen in Kleinbuchstaben, z.B. openai, nach der Erstellung nicht mehr änderbar", "required": "<PERSON>te geben Sie die Anbieter-ID ein", "title": "Anbieter-ID"}, "logo": {"required": "Bitte laden Sie das korrekte Anbieter-Logo hoch", "title": "Anbieter-Logo"}, "name": {"placeholder": "Bitte geben Sie den angezeigten Namen des Anbieters ein", "required": "<PERSON>te geben Si<PERSON> den Namen des Anbieters ein", "title": "Name des Anbieters"}, "proxyUrl": {"required": "Bitte geben Sie die Proxy-Adresse ein", "title": "Proxy-<PERSON><PERSON><PERSON>"}, "sdkType": {"placeholder": "openai/anthropic/azureai/ollama/...", "required": "Bitte wählen Sie den SDK-Typ aus", "title": "Anforderungsformat"}, "title": "Erstellen Sie einen benutzerdefinierten AI-Anbieter"}, "github": {"personalAccessToken": {"desc": "Geben Sie Ihr GitHub-PAT ein und klicken Sie [hier](https://github.com/settings/tokens), um eines zu erstellen.", "placeholder": "ghp_xxxxxx", "title": "GitHub PAT"}}, "huggingface": {"accessToken": {"desc": "<PERSON><PERSON><PERSON> Sie Ihr HuggingFace-Token ein, klicken <PERSON> [hier](https://huggingface.co/settings/tokens), um eines zu erstellen", "placeholder": "hf_xxxxxxxxx", "title": "HuggingFace-Token"}}, "list": {"title": {"disabled": "Dienstanbieter nicht aktiviert", "enabled": "Dienstanbieter aktiviert"}}, "menu": {"addCustomProvider": "Benutzerdefinierten Anbieter hinzufügen", "all": "Alle", "list": {"disabled": "Nicht aktiviert", "enabled": "Aktiviert"}, "notFound": "<PERSON><PERSON> Suchergebnisse gefunden", "searchProviders": "An<PERSON>ter suchen...", "sort": "Benutzerdefinierte Sortierung"}, "ollama": {"checker": {"desc": "<PERSON><PERSON>, ob die Proxy-Adresse korrekt eingetragen wurde", "title": "Konnektivitätsprüfung"}, "customModelName": {"desc": "Fügen Sie benutzerdefinierte Modelle hinzu, trennen Sie mehrere Modelle mit Kommas (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Benutzerdefinierte Modellnamen"}, "download": {"desc": "<PERSON><PERSON><PERSON> lädt dieses Modell herunter. <PERSON>te schließen Si<PERSON> diese Seite nicht. Ein erneuter Download wird an der unterbrochenen Stelle fortgesetzt.", "failed": "Der Download des Modells ist fehlgeschlagen. Bitte überprüfen Sie Ihre Netzwerkverbindung oder die Ollama-Einstellungen und versuchen Sie es erneut.", "remainingTime": "Verbleibende Zeit", "speed": "Downloadgeschwindigkeit", "title": "<PERSON>de Modell {{model}} herunter"}, "endpoint": {"desc": "Muss http(s):// enthalten, kann leer gelassen werden, wenn lokal nicht zusätzlich angegeben.", "title": "Schnittstellen-Proxy-Adresse"}, "title": "Ollama", "unlock": {"cancel": "Cancel Download", "confirm": "Download", "description": "Enter your Ollama model tag to continue the session", "downloaded": "{{completed}} / {{total}}", "starting": "Starting download...", "title": "Download specified Ollama model"}}, "providerModels": {"config": {"aesGcm": "<PERSON><PERSON> Schlüssel und die Proxy-Adresse werden mit dem <1>AES-GCM</1>-Verschlüsselungsalgorithmus verschlüsselt", "apiKey": {"desc": "<PERSON>te geben Si<PERSON> {{name}} API-Schlüssel ein", "descWithUrl": "Bitte gib deinen {{name}} API-<PERSON><PERSON><PERSON><PERSON> ein, <3>hier klicken zum Abrufen</3>", "placeholder": "{{name}} API-Schlüssel", "title": "API-Schlüssel"}, "baseURL": {"desc": "Muss http(s):// enthalten", "invalid": "<PERSON>te geben Si<PERSON> eine gültige URL ein", "placeholder": "https://your-proxy-url.com/v1", "title": "API-Proxy-Adresse"}, "checker": {"button": "Überprüfen", "desc": "<PERSON><PERSON> Si<PERSON>, ob der API-Schlüssel und die Proxy-Adresse korrekt eingegeben wurden", "pass": "Überprüfung bestanden", "title": "Verbindungsprüfung"}, "fetchOnClient": {"desc": "Der Client-Anforderungsmodus initiiert die Sitzung direkt aus dem Browser, was die Reaktionsgeschwindigkeit erhöhen kann", "title": "Client-Anforderungsmodus verwenden"}, "helpDoc": "Konfigurationsanleitung", "responsesApi": {"desc": "Verwendet das neue Anforderungsformat von OpenAI, um fortgeschrittene Funktionen wie Chain-of-Thought freizuschalten", "title": "Verwendung des Responses API-Standards"}, "waitingForMore": "<PERSON><PERSON><PERSON> Modelle werden <1>gep<PERSON></1>, bitte warten <PERSON>e"}, "createNew": {"title": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein benutzerdefiniertes AI-Modell"}, "item": {"config": "<PERSON><PERSON>", "customModelCards": {"addNew": "Erstellen und hinzufügen {{id}} Modell", "confirmDelete": "Das benutzerdefinierte Modell wird gel<PERSON>, nach dem Löschen kann es nicht wiederhergestellt werden. Bitte vorsichtig vorgehen."}, "delete": {"confirm": "Bestätigen Sie das Löschen des Modells {{displayName}}?", "success": "Löschung erfolgreich", "title": "<PERSON><PERSON> l<PERSON>"}, "modelConfig": {"azureDeployName": {"extra": "<PERSON><PERSON>, das in Azure OpenAI tatsächlich angefordert wird", "placeholder": "Bitte geben Sie den Modellbereitstellungsnamen in Azure ein", "title": "Modellbereitstellungsname"}, "deployName": {"extra": "<PERSON><PERSON> wird als Modell-ID gesendet, wenn die Anfrage gesendet wird", "placeholder": "Bitte geben Sie den tatsächlichen Namen oder die ID des bereitgestellten Modells ein", "title": "Modellbereitstellungsname"}, "displayName": {"placeholder": "<PERSON>te geben Sie den angezeigten Namen des Modells ein, z.B. ChatGPT, GPT-4 usw.", "title": "Anzeigename des Modells"}, "files": {"extra": "Der aktuelle Datei-Upload ist nur eine Hack-Lösung und nur für eigene Versuche gedacht. Warten Sie auf die vollständige Datei-Upload-Funktionalität.", "title": "Datei-Upload unterstützen"}, "functionCall": {"extra": "Diese Konfiguration aktiviert nur die Fähigkeit des Modells, Werkzeuge zu verwenden, und ermöglicht es, pluginartige Werkzeuge hinzuzufügen. Ob das Modell tatsächlich in der Lage ist, Werkzeuge zu verwenden, hängt jedoch vollständig vom Modell selbst ab. Bitte testen Sie die Verwendbarkeit selbst.", "title": "Unterstützung der Werkzeugnutzung"}, "id": {"extra": "Nach der Erstellung nicht mehr änderbar, wird als Modell-<PERSON> verwen<PERSON>, wenn AI aufgerufen wird", "placeholder": "<PERSON><PERSON> Modell-<PERSON>, z. B. gpt-4o oder claude-3.5-sonnet", "title": "Modell-ID"}, "modalTitle": "Benutzerdefinierte Modellkonfiguration", "reasoning": {"extra": "Diese Konfiguration aktiviert nur die Fähigkeit des Modells zu tiefem Denken. Die tatsächlichen Ergebnisse hängen vollständig vom Modell selbst ab. Bitte testen Si<PERSON> selbst, ob das Modell über die Fähigkeit zum tiefen Denken verfügt.", "title": "Unterstützung für tiefes Denken"}, "tokens": {"extra": "Maximale Token-Anzahl für das Modell festlegen", "title": "Maximales Kontextfenster", "unlimited": "Unbegrenzt"}, "vision": {"extra": "Diese Konfiguration aktiviert nur die Bild-Upload-Funktionalität in der Anwendung. Ob die Erkennung unterstützt wird, hängt vollständig vom Modell selbst ab. Bitte testen Sie die Verwendbarkeit der visuellen Erkennungsfähigkeit des Modells selbst.", "title": "Visuelle Erkennung unterstützen"}}, "pricing": {"image": "${{amount}}/Bild", "inputCharts": "${{amount}}/<PERSON>", "inputMinutes": "${{amount}}/Minuten", "inputTokens": "Eingabe ${{amount}}/M", "outputTokens": "Ausgabe ${{amount}}/M"}, "releasedAt": "Veröffentlicht am {{releasedAt}}"}, "list": {"addNew": "<PERSON><PERSON>", "disabled": "Nicht aktiviert", "disabledActions": {"showMore": "Alle anzeigen"}, "empty": {"desc": "Bitte erstellen Sie ein benutzerdefiniertes Modell oder ziehen Si<PERSON> ein Modell, um zu beginnen.", "title": "<PERSON><PERSON> verfügbaren Modelle"}, "enabled": "Aktiviert", "enabledActions": {"disableAll": "Alle deaktivieren", "enableAll": "Alle aktivieren", "sort": "Benutzerdefinierte Modellreihenfolge"}, "enabledEmpty": "<PERSON>ine aktivierten Modelle vorhanden, bitte aktivieren Sie Ihre bevorzugten Modelle aus der Liste unten~", "fetcher": {"clear": "Abgerufene Modelle löschen", "fetch": "Model<PERSON>te a<PERSON>", "fetching": "<PERSON><PERSON><PERSON> wird abgerufen...", "latestTime": "Letzte Aktualisierung: {{time}}", "noLatestTime": "Liste wurde noch nicht abgerufen"}, "resetAll": {"conform": "Möchten Sie alle Änderungen am aktuellen Modell wirklich zurücksetzen? Nach dem Zurücksetzen wird die aktuelle Modellliste auf den Standardzustand zurückgesetzt.", "success": "Zurücksetzen erfolgreich", "title": "Alle Änderungen zurücksetzen"}, "search": "Modelle suchen...", "searchResult": "{{count}} <PERSON>le gefunden", "title": "<PERSON><PERSON><PERSON>", "total": "Insgesamt {{count}} verfügbare Modelle"}, "searchNotFound": "<PERSON><PERSON> Suchergebnisse gefunden"}, "sortModal": {"success": "Sortierung erfolgreich aktualisiert", "title": "Benutzerdefinierte Sortierung", "update": "Aktualisieren"}, "updateAiProvider": {"confirmDelete": "Der AI-Anbie<PERSON> wird <PERSON>, nach dem Löschen kann er nicht wiederhergestellt werden. Bestätigen Sie, ob Sie löschen möchten?", "deleteSuccess": "Löschung erfolgreich", "tooltip": "Aktualisieren Sie die grundlegenden Anbieterinformationen", "updateSuccess": "Aktualisierung erfolgreich"}, "updateCustomAiProvider": {"title": "Konfiguration des benutzerdefinierten KI-Anbieters aktualisieren"}, "vertexai": {"apiKey": {"desc": "Geben Sie Ihre Vertex AI-Schlü<PERSON> ein", "placeholder": "{ \"type\": \"service_account\", \"project_id\": \"xxx\", \"private_key_id\": ... }", "title": "Vertex AI-Schlüssel"}}, "zeroone": {"title": "01.<PERSON> Alles und Nichts"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}