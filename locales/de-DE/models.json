{"01-ai/yi-1.5-34b-chat": {"description": "Yi 1.5, das neueste Open-Source-Fine-Tuning-Modell mit 34 Milliarden Parametern, unterstützt verschiedene Dialogszenarien mit hochwertigen Trainingsdaten, die auf menschliche Präferenzen abgestimmt sind."}, "01-ai/yi-1.5-9b-chat": {"description": "Yi 1.5, das neueste Open-Source-Fine-Tuning-Modell mit 9 Milliarden Parametern, unterstützt verschiedene Dialogszenarien mit hochwertigen Trainingsdaten, die auf menschliche Präferenzen abgestimmt sind."}, "360/deepseek-r1": {"description": "【360 Deployment Version】DeepSeek-R1 nutzt in der Nachtrainingsphase umfangreiche Techniken des verstärkenden Lernens, um die Modellinferenzfähigkeit erheblich zu verbessern, selbst bei minimalen gekennzeichneten Daten. In Aufgaben wie Mathematik, Programmierung und natürlicher Sprachverarbeitung erreicht es Leistungen auf Augenhöhe mit der offiziellen Version von OpenAI o1."}, "360gpt-pro": {"description": "360GPT Pro ist ein wichtiger Bestandteil der 360 AI-Modellreihe und erfüllt mit seiner effizienten Textverarbeitungsfähigkeit vielfältige Anwendungen der natürlichen Sprache, unterstützt das Verständnis langer Texte und Mehrfachdialoge."}, "360gpt-pro-trans": {"description": "Ein auf Übersetzungen spezialisiertes Modell, das durch tiefes Feintuning optimiert wurde und führende Übersetzungsergebnisse liefert."}, "360gpt-turbo": {"description": "360GPT Turbo bietet leistungsstarke Berechnungs- und Dialogfähigkeiten, mit hervorragendem semantischen Verständnis und Generierungseffizienz, und ist die ideale intelligente Assistentenlösung für Unternehmen und Entwickler."}, "360gpt-turbo-responsibility-8k": {"description": "360GPT Turbo Responsibility 8K betont semantische Sicherheit und verantwortungsbewusste Ausrichtung, speziell für Anwendungen mit hohen Anforderungen an die Inhaltssicherheit konzipiert, um die Genauigkeit und Robustheit der Benutzererfahrung zu gewährleisten."}, "360gpt2-o1": {"description": "360gpt2-o1 verwendet Baumsuche zur Konstruktion von Denkketten und führt einen Reflexionsmechanismus ein, der durch verstärkendes Lernen trainiert wird. Das Modell verfügt über die Fähigkeit zur Selbstreflexion und Fehlerkorrektur."}, "360gpt2-pro": {"description": "360GPT2 Pro ist ein fortschrittliches Modell zur Verarbeitung natürlicher Sprache, das von der 360 Company entwickelt wurde und über außergewöhnliche Textgenerierungs- und Verständnisfähigkeiten verfügt, insbesondere im Bereich der Generierung und Kreativität, und in der Lage ist, komplexe Sprachumwandlungs- und Rollendarstellungsaufgaben zu bewältigen."}, "360zhinao2-o1": {"description": "360zhinao2-o1 verwendet Baumsuche zur Konstruktion von Denkketten und führt einen Reflexionsmechanismus ein, der durch verstärkendes Lernen trainiert wird. Das Modell verfügt über die Fähigkeit zur Selbstreflexion und Fehlerkorrektur."}, "4.0Ultra": {"description": "Spark4.0 Ultra ist die leistungsstärkste Version der Spark-Großmodellreihe, die die Online-Suchverbindung aktualisiert und die Fähigkeit zur Textverständnis und -zusammenfassung verbessert. Es ist eine umfassende Lösung zur Steigerung der Büroproduktivität und zur genauen Reaktion auf Anforderungen und ein führendes intelligentes Produkt in der Branche."}, "AnimeSharp": {"description": "AnimeSharp (auch bekannt als „4x‑AnimeSharp“) ist ein von Kim2091 auf Basis der ESRGAN-Architektur entwickeltes Open-Source-Superauflösungsmodell, das sich auf die Vergrößerung und Schärfung von Anime-Stil-Bildern spezialisiert hat. Es wurde im Februar 2022 von „4x-TextSharpV1“ umbenannt und war ursprünglich auch für Textbilder geeignet, wurde jedoch für Anime-Inhalte erheblich optimiert."}, "Baichuan2-Turbo": {"description": "Verwendet Suchverbesserungstechnologie, um eine umfassende Verknüpfung zwischen großen Modellen und Fachwissen sowie Wissen aus dem gesamten Internet zu ermöglichen. Unterstützt das Hochladen von Dokumenten wie PDF, Word und die Eingabe von URLs, um Informationen zeitnah und umfassend zu erhalten, mit genauen und professionellen Ergebnissen."}, "Baichuan3-Turbo": {"description": "<PERSON><PERSON>r häufige Unternehmensszenarien optimiert, mit erheblichen Leistungssteigerungen und einem hohen Preis-Leistungs-Verhältnis. Im Vergleich zum Baichuan2-Modell wurde die Inhaltserstellung um 20 %, die Wissensabfrage um 17 % und die Rollenspiel-Fähigkeit um 40 % verbessert. Die Gesamtleistung übertrifft die von GPT-3.5."}, "Baichuan3-Turbo-128k": {"description": "Verfügt über ein 128K Ultra-Langkontextfenster, optimiert für häufige Unternehmensszenarien, mit erheblichen Leistungssteigerungen und einem hohen Preis-Leistungs-Verhältnis. Im Vergleich zum Baichuan2-Modell wurde die Inhaltserstellung um 20 %, die Wissensabfrage um 17 % und die Rollenspiel-Fähigkeit um 40 % verbessert. Die Gesamtleistung übertrifft die von GPT-3.5."}, "Baichuan4": {"description": "Das Modell hat die höchste Fähigkeit im Inland und übertrifft ausländische Mainstream-Modelle in Aufgaben wie Wissensdatenbanken, langen Texten und kreativer Generierung. Es verfügt auch über branchenführende multimodale Fähigkeiten und zeigt in mehreren autoritativen Bewertungsbenchmarks hervorragende Leistungen."}, "Baichuan4-Air": {"description": "Das Modell hat die höchste Leistungsfähigkeit im Inland und übertrifft ausländische Mainstream-Modelle in Aufgaben wie Wissensdatenbanken, langen Texten und kreativen Generierungen auf Chinesisch. Es verfügt auch über branchenführende multimodale Fähigkeiten und zeigt in mehreren anerkannten Bewertungsbenchmarks hervorragende Leistungen."}, "Baichuan4-Turbo": {"description": "Das Modell hat die höchste Leistungsfähigkeit im Inland und übertrifft ausländische Mainstream-Modelle in Aufgaben wie Wissensdatenbanken, langen Texten und kreativen Generierungen auf Chinesisch. Es verfügt auch über branchenführende multimodale Fähigkeiten und zeigt in mehreren anerkannten Bewertungsbenchmarks hervorragende Leistungen."}, "DeepSeek-R1": {"description": "Ein hochmodernes, effizientes LLM, das sich auf Schlussfolgerungen, Mathematik und Programmierung spezialisiert hat."}, "DeepSeek-R1-Distill-Llama-70B": {"description": "DeepSeek R1 – das größere und intelligentere Modell im DeepSeek-Paket – wurde in die Llama 70B-Architektur destilliert. Basierend auf Benchmark-Tests und menschlicher Bewertung ist dieses Modell intelligenter als das ursprüngliche Llama 70B, insbesondere bei Aufgaben, die mathematische und faktische Genauigkeit erfordern."}, "DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Das DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON><PERSON> basiert auf Qwen2.5-Math-1.5B und optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten. Das Open-Source-Modell setzt neue Maßstäbe für Multitasking."}, "DeepSeek-R1-Distill-Qwen-14B": {"description": "Das DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON>l basiert auf Qwen2.5-14B und optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten. Das Open-Source-Modell setzt neue Maßstäbe für Multitasking."}, "DeepSeek-R1-Distill-Qwen-32B": {"description": "Die DeepSeek-R1-Serie optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten, das Open-Source-Modell setzt neue Maßstäbe für Multitasking und übertrifft das Niveau von OpenAI-o1-mini."}, "DeepSeek-R1-Distill-Qwen-7B": {"description": "Das DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON><PERSON> basiert auf Qwen2.5-Math-7B und optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten. Das Open-Source-Modell setzt neue Maßstäbe für Multitasking."}, "DeepSeek-V3": {"description": "DeepSeek-V3 ist ein von der DeepSeek Company entwickeltes MoE-Modell. Die Ergebnisse von DeepSeek-V3 übertreffen die anderer Open-Source-Modelle wie Qwen2.5-72B und Llama-3.1-405B und stehen in der Leistung auf Augenhöhe mit den weltweit führenden Closed-Source-Modellen GPT-4o und Claude-3.5-Sonnet."}, "Doubao-lite-128k": {"description": "Doubao-lite bietet extrem schnelle Reaktionszeiten und ein hervorragendes Preis-Leistungs-Verhältnis, um Kunden in verschiedenen Szenarien flexiblere Optionen zu bieten. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 128k."}, "Doubao-lite-32k": {"description": "Doubao-lite bietet extrem schnelle Reaktionszeiten und ein hervorragendes Preis-Leistungs-Verhältnis, um Kunden in verschiedenen Szenarien flexiblere Optionen zu bieten. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 32k."}, "Doubao-lite-4k": {"description": "Doubao-lite bietet extrem schnelle Reaktionszeiten und ein hervorragendes Preis-Leistungs-Verhältnis, um Kunden in verschiedenen Szenarien flexiblere Optionen zu bieten. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 4k."}, "Doubao-pro-128k": {"description": "Das leistungsstärkste Hauptmodell, geeignet für komplexe Aufgaben. Es erzielt hervorragende Ergebnisse in Szenarien wie Referenzfragen, Zusammenfassungen, kreatives Schreiben, Textklassifikation und Rollenspielen. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 128k."}, "Doubao-pro-32k": {"description": "Das leistungsstärkste Hauptmodell, geeignet für komplexe Aufgaben. Es erzielt hervorragende Ergebnisse in Szenarien wie Referenzfragen, Zusammenfassungen, kreatives Schreiben, Textklassifikation und Rollenspielen. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 32k."}, "Doubao-pro-4k": {"description": "Das leistungsstärkste Hauptmodell, geeignet für komplexe Aufgaben. Es erzielt hervorragende Ergebnisse in Szenarien wie Referenzfragen, Zusammenfassungen, kreatives Schreiben, Textklassifikation und Rollenspielen. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 4k."}, "DreamO": {"description": "DreamO ist ein von ByteDance und der Peking-Universität gemeinsam entwickeltes Open-Source-Bildgenerierungsmodell zur individuellen Anpassung, das durch eine einheitliche Architektur Multitasking-Bildgenerierung unterstützt. Es verwendet eine effiziente kombinierte Modellierungsmethode, um basierend auf vom Nutzer angegebenen Identität, Motiv, Stil, Hintergrund und weiteren Bedingungen hochgradig konsistente und maßgeschneiderte Bilder zu erzeugen."}, "ERNIE-3.5-128K": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für großangelegte Sprachverarbeitung, das eine riesige Menge an chinesischen und englischen Texten abdeckt. Es verfügt über starke allgemeine Fähigkeiten und kann die meisten Anforderungen an Dialogfragen, kreative Generierung und Anwendungsfälle von Plugins erfüllen. Es unterstützt die automatische Anbindung an das Baidu-Such-Plugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ERNIE-3.5-8K": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für großangelegte Sprachverarbeitung, das eine riesige Menge an chinesischen und englischen Texten abdeckt. Es verfügt über starke allgemeine Fähigkeiten und kann die meisten Anforderungen an Dialogfragen, kreative Generierung und Anwendungsfälle von Plugins erfüllen. Es unterstützt die automatische Anbindung an das Baidu-Such-Plugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ERNIE-3.5-8K-Preview": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für großangelegte Sprachverarbeitung, das eine riesige Menge an chinesischen und englischen Texten abdeckt. Es verfügt über starke allgemeine Fähigkeiten und kann die meisten Anforderungen an Dialogfragen, kreative Generierung und Anwendungsfälle von Plugins erfüllen. Es unterstützt die automatische Anbindung an das Baidu-Such-Plugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ERNIE-4.0-8K-Latest": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für ultra-große Sprachverarbeitung, das im Vergleich zu ERNIE 3.5 eine umfassende Verbesserung der Modellfähigkeiten erreicht hat und sich breit für komplexe Aufgaben in verschiedenen Bereichen eignet; unterstützt die automatische Anbindung an das Baidu-Such-Plugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ERNIE-4.0-8K-Preview": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für ultra-große Sprachverarbeitung, das im Vergleich zu ERNIE 3.5 eine umfassende Verbesserung der Modellfähigkeiten erreicht hat und sich breit für komplexe Aufgaben in verschiedenen Bereichen eignet; unterstützt die automatische Anbindung an das Baidu-Such-Plugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ERNIE-4.0-Turbo-8K-Latest": {"description": "Baidus selbstentwickeltes Flaggschiff-Modell für großflächige Sprachverarbeitung, das in vielen komplexen Aufgaben hervorragende Ergebnisse zeigt und umfassend in verschiedenen Bereichen eingesetzt werden kann; unterstützt die automatische Anbindung an Baidu-Suchplugins, um die Aktualität von Antwortinformationen zu gewährleisten. Im Vergleich zu ERNIE 4.0 hat es eine bessere Leistung."}, "ERNIE-4.0-Turbo-8K-Preview": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für ultra-große Sprachverarbeitung, das in der Gesamtleistung herausragend ist und sich breit für komplexe Aufgaben in verschiedenen Bereichen eignet; unterstützt die automatische Anbindung an das Baidu-Such-Plugin, um die Aktualität der Antwortinformationen zu gewährleisten. Im Vergleich zu ERNIE 4.0 bietet es eine bessere Leistungsfähigkeit."}, "ERNIE-Character-8K": {"description": "<PERSON> von Baidu entwickelte Sprachmodell für vertikale Szenarien, das sich für Anwendungen wie Spiel-NPCs, Kundenservice-Dialoge und Rollenspiele eignet. Es hat einen klareren und konsistenteren Charakterstil, eine stärkere Befolgung von Anweisungen und eine bessere Inferenzleistung."}, "ERNIE-Lite-Pro-128K": {"description": "Das von Baidu entwickelte leichte Sprachmodell, das hervorragende Modellleistung und Inferenzleistung kombiniert. Es bietet bessere Ergebnisse als ERNIE Lite und eignet sich für die Inferenznutzung auf AI-Beschleunigungskarten mit geringer Rechenleistung."}, "ERNIE-Speed-128K": {"description": "Das neueste von Baidu im Jahr 2024 veröffentlichte hochleistungsfähige Sprachmodell, das überragende allgemeine Fähigkeiten bietet und sich als Basis-Modell für Feinabstimmungen eignet, um spezifische Szenarien besser zu bearbeiten, und bietet gleichzeitig hervorragende Inferenzleistung."}, "ERNIE-Speed-Pro-128K": {"description": "Das neueste von Baidu im Jahr 2024 veröffentlichte hochleistungsfähige Sprachmodell, das überragende allgemeine Fähigkeiten bietet und bessere Ergebnisse als ERNIE Speed erzielt. Es eignet sich als Basis-Modell für Feinabstimmungen, um spezifische Szenarien besser zu bearbeiten, und bietet gleichzeitig hervorragende Inferenzleistung."}, "FLUX.1-Kontext-dev": {"description": "FLUX.1-Kontext-dev ist ein von Black Forest Labs entwickeltes multimodales Bildgenerierungs- und Bearbeitungsmodell auf Basis der Rectified Flow Transformer-Architektur mit 12 Milliarden Parametern. Es konzentriert sich auf die Generierung, Rekonstruktion, Verbesserung oder Bearbeitung von Bildern unter gegebenen Kontextbedingungen. Das Modell kombiniert die kontrollierbare Generierung von Diffusionsmodellen mit der Kontextmodellierung von Transformern, unterstützt hochwertige Bildausgaben und ist vielseitig einsetzbar für Bildrestaurierung, Bildvervollständigung und visuelle Szenenrekonstruktion."}, "FLUX.1-dev": {"description": "FLUX.1-dev ist ein von Black Forest Labs entwickeltes Open-Source-multimodale<PERSON>dell (Multimodal Language Model, MLLM), das für Bild-Text-Aufgaben optimiert ist und Verständnis sowie Generierung von Bildern und Texten vereint. Es basiert auf fortschrittlichen großen Sprachmodellen wie Mistral-7B und erreicht durch sorgfältig gestaltete visuelle Encoder und mehrstufige Instruktions-Feinabstimmung eine kooperative Verarbeitung von Bild und Text sowie komplexe Aufgabenlogik."}, "Gryphe/MythoMax-L2-13b": {"description": "MythoMax-L2 (13B) ist ein innovatives Modell, das sich für Anwendungen in mehreren Bereichen und komplexe Aufgaben eignet."}, "HelloMeme": {"description": "HelloMeme ist ein KI-Tool, das automatisch Memes, animierte GIFs oder Kurzvideos basierend auf von dir bereitgestellten Bildern oder Aktionen erstellt. Es erfordert keine Zeichen- oder Programmierkenntnisse – du brauchst nur Referenzbilder, und es hilft dir, ansprechende, unterhaltsame und stilistisch einheitliche Inhalte zu erstellen."}, "HiDream-I1-Full": {"description": "HiDream-E1-Full ist ein von HiDream.ai entwickeltes Open-Source-multimodales Bildbearbeitungsmodell, das auf der fortschrittlichen Diffusion Transformer-Architektur basiert und mit leistungsstarker Sprachverständnisfähigkeit (integriert LLaMA 3.1-8B-Instruct) ausgestattet ist. Es unterstützt die Bildgenerierung, Stilübertragung, lokale Bearbeitung und Neugestaltung durch natürliche Sprachbefehle und bietet exzellentes Verständnis und Ausführung von Bild-Text-Anweisungen."}, "HunyuanDiT-v1.2-Diffusers-Distilled": {"description": "hunyuandit-v1.2-distilled ist ein leichtgewichtiges Text-zu-Bild-Modell, das durch Destillation optimiert wurde, um schnell hochwertige Bilder zu erzeugen. Es eignet sich besonders für ressourcenarme Umgebungen und Echtzeit-Generierungsaufgaben."}, "InstantCharacter": {"description": "InstantCharacter ist ein 2025 vom Tencent AI-Team veröffentlichtes tuning-freies personalisiertes Charaktergenerierungsmodell, das eine hochpräzise und konsistente Charaktererstellung über verschiedene Szenarien hinweg ermöglicht. Das Modell kann einen Charakter allein anhand eines Referenzbildes modellieren und diesen flexibel in verschiedene Stile, Bewegungen und Hintergründe übertragen."}, "InternVL2-8B": {"description": "InternVL2-8B ist ein leistungsstarkes visuelles Sprachmodell, das multimodale Verarbeitung von Bildern und Text unterstützt und in der Lage ist, Bildinhalte präzise zu erkennen und relevante Beschreibungen oder Antworten zu generieren."}, "InternVL2.5-26B": {"description": "InternVL2.5-26B ist ein leistungsstarkes visuelles Sprachmodell, das multimodale Verarbeitung von Bildern und Text unterstützt und in der Lage ist, Bildinhalte präzise zu erkennen und relevante Beschreibungen oder Antworten zu generieren."}, "Kolors": {"description": "Kolors ist ein von <PERSON>hou Kolors Team entwickeltes Text-zu-Bild-Modell, das mit Milliarden von Parametern trainiert wurde und in visueller Qualität, chinesischem semantischem Verständnis sowie Textdarstellung herausragende Vorteile bietet."}, "Kwai-Kolors/Kolors": {"description": "Kolors ist ein von <PERSON>hou Kolors Team entwickeltes groß angelegtes latentes Diffusionsmodell zur Text-zu-Bild-Generierung. Es wurde mit Milliarden von Text-Bild-Paaren trainiert und zeigt herausragende Leistungen in visueller Qualität, komplexer semantischer Genauigkeit sowie der Darstellung chinesischer und englischer Schriftzeichen. Es unterstützt sowohl chinesische als auch englische Eingaben und ist besonders leistungsfähig bei der Verarbeitung und Erzeugung chinesischsprachiger Inhalte."}, "Llama-3.2-11B-Vision-Instruct": {"description": "Hervorragende Bildschlussfolgerungsfähigkeiten auf hochauflösenden Bildern, geeignet für Anwendungen im Bereich der visuellen Verständigung."}, "Llama-3.2-90B-Vision-Instruct\t": {"description": "Fortgeschrittene Bildschlussfolgerungsfähigkeiten für Anwendungen im Bereich der visuellen Verständigung."}, "Meta-Llama-3.1-405B-Instruct": {"description": "Das auf Anweisungen optimierte Textmodell Llama 3.1 wurde für mehrsprachige Dialoganwendungen optimiert und zeigt in vielen verfügbaren Open-Source- und geschlossenen Chat-Modellen in gängigen Branchenbenchmarks hervorragende Leistungen."}, "Meta-Llama-3.1-70B-Instruct": {"description": "Das auf Anweisungen optimierte Textmodell Llama 3.1 wurde für mehrsprachige Dialoganwendungen optimiert und zeigt in vielen verfügbaren Open-Source- und geschlossenen Chat-Modellen in gängigen Branchenbenchmarks hervorragende Leistungen."}, "Meta-Llama-3.1-8B-Instruct": {"description": "Das auf Anweisungen optimierte Textmodell Llama 3.1 wurde für mehrsprachige Dialoganwendungen optimiert und zeigt in vielen verfügbaren Open-Source- und geschlossenen Chat-Modellen in gängigen Branchenbenchmarks hervorragende Leistungen."}, "Meta-Llama-3.2-1B-Instruct": {"description": "Ein fortschrittliches, hochmodernes kleines Sprachmodell mit Sprachverständnis, hervorragenden Schlussfolgerungsfähigkeiten und Textgenerierungsfähigkeiten."}, "Meta-Llama-3.2-3B-Instruct": {"description": "Ein fortschrittliches, hochmodernes kleines Sprachmodell mit Sprachverständnis, hervorragenden Schlussfolgerungsfähigkeiten und Textgenerierungsfähigkeiten."}, "Meta-Llama-3.3-70B-Instruct": {"description": "Llama 3.3 ist das fortschrittlichste mehrsprachige Open-Source-Sprachmodell der Llama-Serie, das eine Leistung bietet, die mit einem 405B-Modell vergleichbar ist, und das zu extrem niedrigen Kosten. Es basiert auf der Transformer-Architektur und wurde durch überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF) in Bezug auf Nützlichkeit und Sicherheit verbessert. Die auf Anweisungen optimierte Version ist speziell für mehrsprachige Dialoge optimiert und übertrifft in mehreren Branchenbenchmarks viele verfügbare Open-Source- und geschlossene Chat-Modelle. Das Wissensdatum endet im Dezember 2023."}, "MiniMax-M1": {"description": "Ein völlig neu entwickeltes Inferenzmodell. Weltweit führend: 80K Denkketten x 1M Eingaben, Leistung auf Augenhöhe mit den besten Modellen im Ausland."}, "MiniMax-Text-01": {"description": "In der MiniMax-01-Serie haben wir mutige Innovationen vorgenommen: Erstmals wurde die lineare Aufmerksamkeitsmechanismus in großem Maßstab implementiert, sodass die traditionelle Transformer-Architektur nicht mehr die einzige Wahl ist. Dieses Modell hat eine Parameteranzahl von bis zu 456 Milliarden, wobei eine Aktivierung 45,9 Milliarden beträgt. Die Gesamtleistung des Modells kann mit den besten Modellen im Ausland mithalten und kann gleichzeitig effizient den weltweit längsten Kontext von 4 Millionen Tokens verarbeiten, was 32-mal so viel wie GPT-4o und 20-mal so viel wie Claude-3.5-Sonnet ist."}, "MiniMaxAI/MiniMax-M1-80k": {"description": "MiniMax-M1 ist ein groß angelegtes hybrides Aufmerksamkeits-Inferenzmodell mit offenen Gewichten, das 456 Milliarden Parameter umfasst und etwa 45,9 Milliarden Parameter pro Token aktiviert. Das Modell unterstützt nativ einen ultralangen Kontext von 1 Million Tokens und spart durch den Blitz-Attention-Mechanismus bei Aufgaben mit 100.000 Tokens im Vergleich zu DeepSeek R1 75 % der Fließkommaoperationen ein. Gleichzeitig verwendet MiniMax-M1 eine MoE-Architektur (Mixture of Experts) und kombiniert den CISPO-Algorithmus mit einem hybriden Aufmerksamkeitsdesign für effizientes verstärkendes Lernen, was in der Langzeiteingabe-Inferenz und realen Software-Engineering-Szenarien branchenführende Leistung erzielt."}, "Moonshot-Kimi-K2-Instruct": {"description": "Mit insgesamt 1 Billion Parametern und 32 Milliarden aktivierten Parametern erreicht dieses nicht-denkende Modell Spitzenleistungen in den Bereichen aktuelles Wissen, Mathematik und Programmierung und ist besonders für allgemeine Agentenaufgaben optimiert. Es wurde speziell für Agentenaufgaben verfeinert, kann nicht nur Fragen beantworten, sondern auch Aktionen ausführen. Ideal für spontane, allgemeine Gespräche und Agentenerfahrungen, ist es ein reflexartiges Modell ohne lange Denkzeiten."}, "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO": {"description": "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B) ist ein hochpräzises Anweisungsmodell, das für komplexe Berechnungen geeignet ist."}, "OmniConsistency": {"description": "OmniConsistency verbessert durch den Einsatz großskaliger Diffusion Transformers (DiTs) und gepaarter stilisierter Daten die Stil-Konsistenz und Generalisierungsfähigkeit bei Bild-zu-Bild-Aufgaben und verhindert Stilverschlechterung."}, "Phi-3-medium-128k-instruct": {"description": "Das gleiche Phi-3-medium-Modell, jedoch mit einer größeren Kontextgröße für RAG oder Few-Shot-Prompting."}, "Phi-3-medium-4k-instruct": {"description": "Ein Modell mit 14 Milliarden Parametern, das eine bessere Qualität als Phi-3-mini bietet und sich auf qualitativ hochwertige, reasoning-dense Daten konzentriert."}, "Phi-3-mini-128k-instruct": {"description": "Das gleiche Phi-3-mini-Modell, jedoch mit einer größeren Kontextgröße für RAG oder Few-Shot-Prompting."}, "Phi-3-mini-4k-instruct": {"description": "Das kleinste Mitglied der Phi-3-Familie. Optimiert für Qualität und geringe Latenz."}, "Phi-3-small-128k-instruct": {"description": "Das gleiche Phi-3-small-Modell, jedoch mit einer größeren Kontextgröße für RAG oder Few-Shot-Prompting."}, "Phi-3-small-8k-instruct": {"description": "Ein Modell mit 7 Milliarden Parametern, das eine bessere Qualität als Phi-3-mini bietet und sich auf qualitativ hochwertige, reasoning-dense Daten konzentriert."}, "Phi-3.5-mini-instruct": {"description": "Aktualisierte Version des Phi-3-mini-Modells."}, "Phi-3.5-vision-instrust": {"description": "Aktualisierte Version des Phi-3-vision-Modells."}, "Pro/Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-7B-Instruct ist das anweisungsfeinabgestimmte große Sprachmodell der Qwen2-Serie mit einer Parametergröße von 7B. Dieses Modell basiert auf der Transformer-Architektur und verwendet Technologien wie die SwiGLU-Aktivierungsfunktion, QKV-Offsets und gruppierte Abfrageaufmerksamkeit. Es kann große Eingaben verarbeiten. Das Modell zeigt hervorragende Leistungen in der Sprachverständnis, -generierung, Mehrsprachigkeit, Codierung, Mathematik und Inferenz in mehreren Benchmark-Tests und übertrifft die meisten Open-Source-Modelle und zeigt in bestimmten Aufgaben eine vergleichbare Wettbewerbsfähigkeit mit proprietären Modellen. Qwen2-7B-Instruct übertrifft Qwen1.5-7B-Chat in mehreren Bewertungen und zeigt signifikante Leistungsverbesserungen."}, "Pro/Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct ist eines der neuesten großen Sprachmodelle, die von Alibaba Cloud veröffentlicht wurden. Dieses 7B-Modell hat signifikante Verbesserungen in den Bereichen Codierung und Mathematik. Das Modell bietet auch mehrsprachige Unterstützung und deckt über 29 Sprachen ab, einschließlich Chinesisch und Englisch. Es zeigt signifikante Verbesserungen in der Befolgung von Anweisungen, im Verständnis strukturierter Daten und in der Generierung strukturierter Ausgaben (insbesondere JSON)."}, "Pro/Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct ist die neueste Version der von Alibaba Cloud veröffentlichten Reihe von code-spezifischen großen Sprachmodellen. Dieses Modell basiert auf Qwen2.5 und wurde mit 55 Billionen Tokens trainiert, um die Fähigkeiten zur Codegenerierung, Inferenz und Fehlerbehebung erheblich zu verbessern. Es verbessert nicht nur die Codierungsfähigkeiten, sondern bewahrt auch die Vorteile in Mathematik und allgemeinen Fähigkeiten. Das Modell bietet eine umfassendere Grundlage für praktische Anwendungen wie Code-Agenten."}, "Pro/Qwen/Qwen2.5-VL-7B-Instruct": {"description": "Qwen2.5-VL ist ein neues Mitglied der Qwen-Serie und verfügt über leistungsstarke visuelle Wahrnehmungsfähigkeiten. Es kann Text, Diagramme und Layouts in Bildern analysieren, längere Videos verstehen und Ereignisse erfassen. Zudem kann es Schlussfolgerungen ziehen, Werkzeuge bedienen, mehrere Formate für Objektlokalisation unterstützen und strukturierte Ausgaben generieren. Die Videoverarbeitung wurde durch dynamische Auflösungs- und Frameratetraining optimiert, und die Effizienz des visuellen Encoders wurde verbessert."}, "Pro/THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking ist ein von Zhipu AI und dem KEG-Labor der Tsinghua-Universität gemeinsam veröffentlichtes Open-Source-Visuell-Sprachmodell (VLM), das speziell für die Bewältigung komplexer multimodaler kognitiver Aufgaben entwickelt wurde. Das Modell basiert auf dem GLM-4-9B-0414-Grundmodell und verbessert durch die Einführung des „Chain-of-Thought“-Schlussmechanismus und den Einsatz von Verstärkungslernstrategien seine multimodale Schlussfolgerungsfähigkeit und Stabilität erheblich."}, "Pro/THUDM/glm-4-9b-chat": {"description": "GLM-4-9B-Chat ist die Open-Source-Version des GLM-4-Modells, das von Zhizhu AI eingeführt wurde. Dieses Modell zeigt hervorragende Leistungen in den Bereichen Semantik, Mathematik, Inferenz, Code und Wissen. Neben der Unterstützung für mehrstufige Dialoge bietet GLM-4-9B-Chat auch fortgeschrittene Funktionen wie Web-Browsing, Code-Ausführung, benutzerdefinierte Tool-Aufrufe (Function Call) und langes Textverständnis. Das Modell unterstützt 26 Sprachen, da<PERSON><PERSON> Chin<PERSON>ch, Englisch, Japanisch, Koreanisch und Deutsch. In mehreren Benchmark-Tests zeigt GLM-4-9B-Chat hervorragende Leistungen, wie AlignBench-v2, MT-Bench, MMLU und C-Eval. Das Modell unterstützt eine maximale Kontextlänge von 128K und ist für akademische Forschung und kommerzielle Anwendungen geeignet."}, "Pro/deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 ist ein durch verstärkendes Lernen (RL) gesteuertes Inferenzmodell, das Probleme mit Wiederholungen und Lesbarkeit im Modell löst. Vor dem RL führte DeepSeek-R1 Kaltstartdaten ein, um die Inferenzleistung weiter zu optimieren. E<PERSON> zeigt in mathematischen, programmierbezogenen und Inferenzaufgaben eine vergleichbare Leistung zu OpenAI-o1 und verbessert die Gesamtleistung durch sorgfältig gestaltete Trainingsmethoden."}, "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B ist ein <PERSON>l, das durch Wissensdistillierung auf Basis von Qwen2.5-Math-7B erstellt wurde. Dieses Modell wurde mit 800.000 sorgfältig ausgewählten Beispielen, die von DeepSeek-R1 generiert wurden, feinjustiert und zeigt ausgezeichnete Inferenzfähigkeiten. Es erzielte in mehreren Benchmarks hervorragende Ergebnisse, darunter eine Genauigkeit von 92,8 % im MATH-500, einen Durchgangsrate von 55,5 % im AIME 2024 und eine Bewertung von 1189 auf CodeForces, was seine starken mathematischen und programmierischen Fähigkeiten als Modell mit 7B Parametern unterstreicht."}, "Pro/deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 ist ein hybrides Experten (MoE) Sprachmodell mit 6710 Milliarden Parametern, das eine Multi-Head-Latente-Attention (MLA) und DeepSeekMoE-Architektur verwendet, kombiniert mit einer Lastenausgleichsstrategie ohne Hilfskosten, um die Inferenz- und Trainingseffizienz zu optimieren. Durch das Pre-Training auf 14,8 Billionen hochwertigen Tokens und anschließende überwachte Feinabstimmung und verstärktes Lernen übertrifft DeepSeek-V3 in der Leistung andere Open-Source-Modelle und nähert sich führenden geschlossenen Modellen."}, "Pro/moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 ist ein MoE-Architektur-Basis-Modell mit herausragenden Code- und Agentenfähigkeiten, insgesamt 1 Billion Parameter und 32 Milliarden aktivierten Parametern. In Benchmark-Tests zu allgemeinem Wissen, Programmierung, Mathematik und Agentenaufgaben übertrifft das K2-Modell andere führende Open-Source-Modelle."}, "QwQ-32B-Preview": {"description": "QwQ-32B-Preview ist ein innovatives Modell für die Verarbeitung natürlicher Sprache, das komplexe Aufgaben der Dialoggenerierung und des Kontextverständnisses effizient bewältigen kann."}, "Qwen/QVQ-72B-Preview": {"description": "QVQ-72B-Preview ist ein forschungsorientiertes Modell, das vom Qwen-Team entwickelt wurde und sich auf visuelle Inferenzfähigkeiten konzentriert. Es hat einzigartige Vorteile beim Verständnis komplexer Szenen und der Lösung visuell verwandter mathematischer Probleme."}, "Qwen/QwQ-32B": {"description": "QwQ ist das Inferenzmodell der Qwen-Serie. Im Vergleich zu traditionellen, anweisungsoptimierten Modellen verfügt QwQ über Denk- und Schlussfolgerungsfähigkeiten, die eine signifikante Leistungssteigerung bei nachgelagerten Aufgaben ermöglichen, insbesondere bei der Lösung schwieriger Probleme. QwQ-32B ist ein mittelgroßes Inferenzmodell, das im Vergleich zu den fortschrittlichsten Inferenzmodellen (wie DeepSeek-R1, o1-mini) wettbewerbsfähige Leistungen erzielt. Dieses Modell verwendet Technologien wie RoPE, SwiGLU, RMSNorm und Attention QKV Bias und hat eine Netzwerkstruktur mit 64 Schichten und 40 Q-Attention-Köpfen (im GQA-Architektur sind es 8 KV)."}, "Qwen/QwQ-32B-Preview": {"description": "QwQ-32B-Preview ist das neueste experimentelle Forschungsmodell von <PERSON>, das sich auf die Verbesserung der KI-Inferenzfähigkeiten konzentriert. Durch die Erforschung komplexer Mechanismen wie Sprachmischung und rekursive Inferenz bietet es Hauptvorteile wie starke Analysefähigkeiten, mathematische und Programmierfähigkeiten. Gleichzeitig gibt es Herausforderungen wie Sprachwechsel, Inferenzzyklen, Sicherheitsüberlegungen und Unterschiede in anderen Fähigkeiten."}, "Qwen/Qwen2-72B-Instruct": {"description": "Qwen2 ist ein fortschrittliches allgemeines Sprachmodell, das eine Vielzahl von Anweisungsarten unterstützt."}, "Qwen/Qwen2-7B-Instruct": {"description": "Qwen2-72B-Instruct ist das anweisungsfeinabgestimmte große Sprachmodell der Qwen2-Serie mit einer Parametergröße von 72B. Dieses Modell basiert auf der Transformer-Architektur und verwendet Technologien wie die SwiGLU-Aktivierungsfunktion, QKV-Offsets und gruppierte Abfrageaufmerksamkeit. Es kann große Eingaben verarbeiten. Das Modell zeigt hervorragende Leistungen in der Sprachverständnis, -generierung, Mehrsprachigkeit, Codierung, Mathematik und Inferenz in mehreren Benchmark-Tests und übertrifft die meisten Open-Source-Modelle und zeigt in bestimmten Aufgaben eine vergleichbare Wettbewerbsfähigkeit mit proprietären Modellen."}, "Qwen/Qwen2-VL-72B-Instruct": {"description": "Qwen2-VL ist die neueste Iteration des Qwen-VL-Modells, das in visuellen Verständnis-Benchmarks erstklassige Leistungen erzielt."}, "Qwen/Qwen2.5-14B-Instruct": {"description": "Qwen2.5 ist eine brandneue Serie von großen Sprachmodellen, die darauf abzielt, die Verarbeitung von Anweisungsaufgaben zu optimieren."}, "Qwen/Qwen2.5-32B-Instruct": {"description": "Qwen2.5 ist eine brandneue Serie von großen Sprachmodellen, die darauf abzielt, die Verarbeitung von Anweisungsaufgaben zu optimieren."}, "Qwen/Qwen2.5-72B-Instruct": {"description": "Ein großes Sprachmodell, das vom Alibaba Cloud <PERSON>-Team entwickelt wurde."}, "Qwen/Qwen2.5-72B-Instruct-128K": {"description": "Qwen2.5 ist eine neue Serie großer Sprachmodelle mit stärkeren Verständnis- und Generierungsfähigkeiten."}, "Qwen/Qwen2.5-72B-Instruct-Turbo": {"description": "Qwen2.5 ist eine neue Serie großer Sprachmodelle, die darauf abzielt, die Verarbeitung von instructiven Aufgaben zu optimieren."}, "Qwen/Qwen2.5-7B-Instruct": {"description": "Qwen2.5 ist eine brandneue Serie von großen Sprachmodellen, die darauf abzielt, die Verarbeitung von Anweisungsaufgaben zu optimieren."}, "Qwen/Qwen2.5-7B-Instruct-Turbo": {"description": "Qwen2.5 ist eine neue Serie großer Sprachmodelle, die darauf abzielt, die Verarbeitung von instructiven Aufgaben zu optimieren."}, "Qwen/Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-<PERSON><PERSON> konzen<PERSON> sich auf das Programmieren."}, "Qwen/Qwen2.5-Coder-7B-Instruct": {"description": "Qwen2.5-Coder-7B-Instruct ist die neueste Version der von Alibaba Cloud veröffentlichten Reihe von code-spezifischen großen Sprachmodellen. Dieses Modell basiert auf Qwen2.5 und wurde mit 55 Billionen Tokens trainiert, um die Fähigkeiten zur Codegenerierung, Inferenz und Fehlerbehebung erheblich zu verbessern. Es verbessert nicht nur die Codierungsfähigkeiten, sondern bewahrt auch die Vorteile in Mathematik und allgemeinen Fähigkeiten. Das Modell bietet eine umfassendere Grundlage für praktische Anwendungen wie Code-Agenten."}, "Qwen/Qwen2.5-VL-32B-Instruct": {"description": "Qwen2.5-VL-32B-Instruct ist ein multimodales G<PERSON>dell, das vom Qwen-Team entwickelt wurde und Teil der Qwen2.5-VL-<PERSON><PERSON>e ist. Dieses Modell ist nicht nur in der Lage, übliche Objekte zu erkennen, sondern kann auch Text, Diagramme, Symbole, Grafiken und Layouts in Bildern analysieren. Es kann als visueller Agent dienen, der in der Lage ist, zu schließen und Werkzeuge dynamisch zu steuern, wobei es Fähigkeiten im Umgang mit Computern und Smartphones besitzt. Darüber hinaus kann dieses Modell Objekte in Bildern präzise lokalisieren und strukturierte Ausgaben für Rechnungen, Tabellen usw. generieren. Im Vergleich zum Vorgängermodell Qwen2-VL wurde diese Version durch verstärktes Lernen in Mathematik und Problemlösungsfähigkeiten weiter verbessert, und ihr Antwortstil entspricht stärker den menschlichen Vorlieben."}, "Qwen/Qwen2.5-VL-72B-Instruct": {"description": "Qwen2.5-VL ist ein visueller Sprachmodell der Qwen2.5-Serie. Dieses Modell zeichnet sich durch erhebliche Verbesserungen aus: Es verfügt über eine stärkere visuelle Wahrnehmungsfähigkeit, kann übliche Objekte erkennen, Texte, Diagramme und Layouts analysieren; als visueller Agent kann es Schlussfolgerungen ziehen und die dynamische Nutzung von Werkzeugen leiten; es unterstützt das Verstehen von Videos mit einer Länge von über einer Stunde und kann wichtige Ereignisse erfassen; es kann durch die Generierung von Begrenzungsrahmen oder Punkten Objekte in Bildern präzise lokalisieren; es unterstützt die Erstellung strukturierter Ausgaben, insbesondere für gescannte Daten wie Rechnungen und Tabellen."}, "Qwen/Qwen3-14B": {"description": "Qwen3 ist ein neues, leistungsstark verbessertes Modell von <PERSON>, das in den Bereichen Denken, Allgemeinwissen, Agenten und Mehrsprachigkeit in mehreren Kernfähigkeiten branchenführende Standards erreicht und den Wechsel zwischen Denkmodi unterstützt."}, "Qwen/Qwen3-235B-A22B": {"description": "Qwen3 ist ein neues, leistungsstark verbessertes Modell von <PERSON>, das in den Bereichen Denken, Allgemeinwissen, Agenten und Mehrsprachigkeit in mehreren Kernfähigkeiten branchenführende Standards erreicht und den Wechsel zwischen Denkmodi unterstützt."}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"description": "Qwen3-235B-A22B-Instruct-2507 ist ein Flaggschiff-Misch-Experten-(MoE)-Großsprachmodell aus der Qwen3-Serie, entwickelt vom Alibaba Cloud Tongyi Qianwen Team. Es verfügt über 235 Milliarden Gesamtparameter und aktiviert bei jeder Inferenz 22 Milliarden Parameter. Als aktualisierte Version des nicht-denkenden Qwen3-235B-A22B fokussiert es sich auf signifikante Verbesserungen in Instruktionsbefolgung, logischem Denken, Textverständnis, Mathematik, Wissenschaft, Programmierung und Werkzeugnutzung. Zudem wurde die Abdeckung mehrsprachigen Langschwanzwissens erweitert und die Ausrichtung auf Nutzerpräferenzen bei subjektiven und offenen Aufgaben verbessert, um hilfreichere und qualitativ hochwertigere Texte zu generieren."}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"description": "Qwen3-235B-A22B-Thinking-2507 ist ein Mitglied der Qwen3-Serie großer Sprachmodelle von Alibaba Tongyi Qianwen, spezialisiert auf komplexe anspruchsvolle Schlussfolgerungsaufgaben. Das Modell basiert auf der Misch-Experten-(MoE)-Architektur mit 235 Milliarden Gesamtparametern, aktiviert jedoch nur etwa 22 Milliarden Parameter pro Token, was eine hohe Rechenleistung bei Effizienz ermöglicht. Als dediziertes „Denk“-Modell zeigt es herausragende Leistungen in logischem Denken, Mathematik, Wissenschaft, Programmierung und akademischen Benchmarks und erreicht Spitzenwerte unter Open-Source-Denkmodellen. Zusätzlich verbessert es allgemeine Fähigkeiten wie Instruktionsbefolgung, Werkzeugnutzung und Textgenerierung und unterstützt nativ eine Kontextlänge von 256K, ideal für tiefgehende Schlussfolgerungen und lange Dokumente."}, "Qwen/Qwen3-30B-A3B": {"description": "Qwen3 ist ein neues, leistungsstark verbessertes Modell von <PERSON>, das in den Bereichen Denken, Allgemeinwissen, Agenten und Mehrsprachigkeit in mehreren Kernfähigkeiten branchenführende Standards erreicht und den Wechsel zwischen Denkmodi unterstützt."}, "Qwen/Qwen3-30B-A3B-Instruct-2507": {"description": "Qwen3-30B-A3B-Instruct-2507 ist eine aktualisierte Version des Qwen3-30B-A3B im Nicht-Denkmodus. Es handelt sich um ein Mixture-of-Experts (MoE)-Modell mit insgesamt 30,5 Milliarden Parametern und 3,3 Milliarden Aktivierungsparametern. Das Modell wurde in mehreren Bereichen entscheidend verbessert, darunter eine signifikante Steigerung der Befolgung von Anweisungen, logisches Denken, Textverständnis, Mathematik, Wissenschaft, Programmierung und Werkzeugnutzung. Gleichzeitig wurden substanzielle Fortschritte bei der Abdeckung von Langschwanzwissen in mehreren Sprachen erzielt, und es kann besser auf die Präferenzen der Nutzer bei subjektiven und offenen Aufgaben abgestimmt werden, um hilfreichere Antworten und qualitativ hochwertigere Texte zu generieren. Darüber hinaus wurde die Fähigkeit zum Verständnis langer Texte auf 256K erweitert. Dieses Modell unterstützt ausschließlich den Nicht-Denkmodus und generiert keine `<think></think>`-Tags in der Ausgabe."}, "Qwen/Qwen3-32B": {"description": "Qwen3 ist ein neues, leistungsstark verbessertes Modell von <PERSON>, das in den Bereichen Denken, Allgemeinwissen, Agenten und Mehrsprachigkeit in mehreren Kernfähigkeiten branchenführende Standards erreicht und den Wechsel zwischen Denkmodi unterstützt."}, "Qwen/Qwen3-8B": {"description": "Qwen3 ist ein neues, leistungsstark verbessertes Modell von <PERSON>, das in den Bereichen Denken, Allgemeinwissen, Agenten und Mehrsprachigkeit in mehreren Kernfähigkeiten branchenführende Standards erreicht und den Wechsel zwischen Denkmodi unterstützt."}, "Qwen2-72B-Instruct": {"description": "Qwen2 ist die neueste Reihe des Qwen-Modells, das 128k Kontext unterstützt. Im Vergleich zu den derzeit besten Open-Source-Modellen übertrifft Qwen2-72B in den Bereichen natürliche Sprachverständnis, Wissen, Code, Mathematik und Mehrsprachigkeit deutlich die führenden Modelle."}, "Qwen2-7B-Instruct": {"description": "Qwen2 ist die neueste Reihe des Qwen-Modells, das in der Lage ist, die besten Open-Source-Modelle ähnlicher Größe oder sogar größerer Modelle zu übertreffen. Qwen2 7B hat in mehreren Bewertungen signifikante Vorteile erzielt, insbesondere im Bereich Code und Verständnis der chinesischen Sprache."}, "Qwen2-VL-72B": {"description": "Qwen2-VL-72B ist ein leistungsstarkes visuelles Sprachmodell, das multimodale Verarbeitung von Bildern und Text unterstützt und in der Lage ist, Bildinhalte präzise zu erkennen und relevante Beschreibungen oder Antworten zu generieren."}, "Qwen2.5-14B-Instruct": {"description": "Qwen2.5-14B-Instruct ist ein großes Sprachmodell mit 14 Milliarden Parametern, das hervorragende Leistungen bietet, für chinesische und mehrsprachige Szenarien optimiert ist und Anwendungen wie intelligente Fragen und Antworten sowie Inhaltserstellung unterstützt."}, "Qwen2.5-32B-Instruct": {"description": "Qwen2.5-32B-Instruct ist ein großes Sprachmodell mit 32 Milliarden Parametern, das eine ausgewogene Leistung bietet, für chinesische und mehrsprachige Szenarien optimiert ist und Anwendungen wie intelligente Fragen und Antworten sowie Inhaltserstellung unterstützt."}, "Qwen2.5-72B-Instruct": {"description": "Qwen2.5-72B-Instruct unterstützt 16k Kontext und generiert lange Texte über 8K. Es unterstützt Funktionsaufrufe und nahtlose Interaktionen mit externen Systemen, was die Flexibilität und Skalierbarkeit erheblich verbessert. Das Wissen des Modells hat deutlich zugenommen, und die Codierungs- und mathematischen Fähigkeiten wurden erheblich verbessert, mit Unterstützung für über 29 Sprachen."}, "Qwen2.5-7B-Instruct": {"description": "Qwen2.5-7B-Instruct ist ein großes Sprachmodell mit 7 Milliarden Parametern, das Funktionsaufrufe unterstützt und nahtlos mit externen Systemen interagiert, was die Flexibilität und Skalierbarkeit erheblich erhöht. Es ist für chinesische und mehrsprachige Szenarien optimiert und unterstützt Anwendungen wie intelligente Fragen und Antworten sowie Inhaltserstellung."}, "Qwen2.5-Coder-14B-Instruct": {"description": "Qwen2.5-Coder-14B-Instruct ist ein auf großflächigem Pre-Training basierendes Programmiermodell, das über starke Fähigkeiten zur Codeverstehung und -generierung verfügt und effizient verschiedene Programmieraufgaben bearbeiten kann. Es eignet sich besonders gut für intelligente Codeerstellung, automatisierte Skripterstellung und die Beantwortung von Programmierfragen."}, "Qwen2.5-Coder-32B-Instruct": {"description": "Qwen2.5-Coder-32B-Instruct ist ein große<PERSON> Sprach<PERSON>dell, das speziell für die Codegenerierung, das Verständnis von Code und effiziente Entwicklungsszenarien entwickelt wurde. Es verwendet eine branchenführende Parametergröße von 32B und kann vielfältige Programmieranforderungen erfüllen."}, "Qwen3-235B": {"description": "Qwen3-235B-A22B ist ein MoE (Mixture-of-Experts)-<PERSON>l, das den „Hybrid-Reasoning-Modus“ einführt und Nutzern nahtloses Umschalten zwischen „Denkmodus“ und „Nicht-Denkmodus“ ermöglicht. Es unterstützt das Verständnis und die Argumentation in 119 Sprachen und Dialekten und verfügt über leistungsstarke Werkzeugaufruffähigkeiten. In umfassenden Benchmark-Tests zu allgemeinen Fähigkeiten, Programmierung und Mathematik, Mehrsprachigkeit, Wissen und Argumentation konkurriert es mit führenden aktuellen Großmodellen auf dem Markt wie DeepSeek R1, OpenAI o1, o3-mini, Grok 3 und Google Gemini 2.5 Pro."}, "Qwen3-32B": {"description": "Qwen3-32B ist ein dichtes Modell (Dense Model), das den „Hybrid-Reasoning-Modus“ einführt und Nutzern nahtloses Umschalten zwischen „Denkmodus“ und „Nicht-Denkmodus“ ermöglicht. Aufgrund von Verbesserungen in der Modellarchitektur, einer Erweiterung der Trainingsdaten und effizienteren Trainingsmethoden entspricht die Gesamtleistung der von Qwen2.5-72B."}, "SenseChat": {"description": "Basisversion des Modells (V4) mit 4K Kontextlänge, die über starke allgemeine Fähigkeiten verfügt."}, "SenseChat-128K": {"description": "Basisversion des Modells (V4) mit 128K Kontextlänge, das in Aufgaben des Verständnisses und der Generierung langer Texte hervorragende Leistungen zeigt."}, "SenseChat-32K": {"description": "Basisversion des Modells (V4) mit 32K Kontextlänge, flexibel einsetzbar in verschiedenen Szenarien."}, "SenseChat-5": {"description": "Die neueste Modellversion (V5.5) mit 128K Kontextlänge hat signifikante Verbesserungen in den Bereichen mathematische Schlussfolgerungen, englische Konversation, Befolgen von Anweisungen und Verständnis langer Texte, vergleichbar mit GPT-4o."}, "SenseChat-5-1202": {"description": "Basierend auf der neuesten Version V5.5 zeigt es im Vergleich zur Vorgängerversion deutliche Verbesserungen in den Bereichen Grundfähigkeiten in Chinesisch und Englisch, Chat, naturwissenschaftliches Wissen, geisteswissenschaftliches Wissen, Schreiben, mathematische Logik und Wortzahlkontrolle."}, "SenseChat-5-Cantonese": {"description": "Mit 32K Kontextlänge übertrifft es GPT-4 im Verständnis von Konversationen auf Kantonesisch und kann in mehreren Bereichen wie Wissen, Schlussfolgerungen, Mathematik und Programmierung mit GPT-4 Turbo konkurrieren."}, "SenseChat-5-beta": {"description": "<PERSON>il<PERSON>se bessere Leistung als SenseCat-5-1202"}, "SenseChat-Character": {"description": "Standardmodell mit 8K Kontextlänge und hoher Reaktionsgeschwindigkeit."}, "SenseChat-Character-Pro": {"description": "Premium-Modell mit 32K Kontextlänge, das umfassende Verbesserungen in den Fähigkeiten bietet und sowohl chinesische als auch englische Konversationen unterstützt."}, "SenseChat-Turbo": {"description": "Geeignet für schnelle Fragen und Antworten sowie Szenarien zur Feinabstimmung des Modells."}, "SenseChat-Turbo-1202": {"description": "Dies ist das neueste leichte Modell, das über 90 % der Fähigkeiten des Vollmodells erreicht und die Kosten für die Inferenz erheblich senkt."}, "SenseChat-Vision": {"description": "Das neueste Modell (V5.5) unterstützt die Eingabe mehrerer Bilder und optimiert umfassend die grundlegenden Fähigkeiten des Modells. Es hat signifikante Verbesserungen in der Erkennung von Objektattributen, räumlichen Beziehungen, Aktionsereignissen, Szenenverständnis, Emotionserkennung, logischem Wissen und Textverständnis und -generierung erreicht."}, "SenseNova-V6-5-Pro": {"description": "Durch umfassende Aktualisierungen multimodaler, sprachlicher und argumentativer Daten sowie Optimierungen der Trainingsstrategie erzielt das neue Modell erhebliche Verbesserungen bei multimodalem Schließen und generalisierter Befolgung von Anweisungen. Es unterstützt Kontextfenster von bis zu 128k und zeigt herausragende Leistungen bei spezialisierten Aufgaben wie OCR und der Erkennung von Tourismus-IP."}, "SenseNova-V6-5-Turbo": {"description": "Durch umfassende Aktualisierungen multimodaler, sprachlicher und argumentativer Daten sowie Optimierungen der Trainingsstrategie erzielt das neue Modell erhebliche Verbesserungen bei multimodalem Schließen und generalisierter Befolgung von Anweisungen. Es unterstützt Kontextfenster von bis zu 128k und zeigt herausragende Leistungen bei spezialisierten Aufgaben wie OCR und der Erkennung von Tourismus-IP."}, "SenseNova-V6-Pro": {"description": "Erreicht eine native Einhe<PERSON> von Bild-, Text- und Video-Fähigkeiten, überwindet die traditionellen Grenzen der multimodalen Trennung und hat in den Bewertungen von OpenCompass und SuperCLUE zwei Meistertitel gewonnen."}, "SenseNova-V6-Reasoner": {"description": "Vereint visuelle und sprachliche Tiefenlogik, ermöglicht langsames Denken und tiefgreifende Schlussfolgerungen und präsentiert den vollständigen Denkprozess."}, "SenseNova-V6-Turbo": {"description": "Erreicht eine native Einhe<PERSON> von Bild-, Text- und Video-Fähigkeiten, überwindet die traditionellen Grenzen der multimodalen Trennung und führt in den Kernbereichen wie multimodalen Grundfähigkeiten und sprachlichen Grundfähigkeiten umfassend. Es kombiniert literarische und wissenschaftliche Bildung und hat in mehreren Bewertungen mehrfach die Spitzenposition im In- und Ausland erreicht."}, "Skylark2-lite-8k": {"description": "Das zweite Modell der Skylark-Reihe, das Skylark2-lite-Modell bietet eine hohe Reaktionsgeschwindigkeit und eignet sich für Szenarien mit hohen Echtzeitanforderungen, kostensensitiven Anforderungen und geringeren Genauigkeitsanforderungen, mit einer Kontextfensterlänge von 8k."}, "Skylark2-pro-32k": {"description": "Das zweite Modell der Skylark-Reihe, die Skylark2-pro-Version hat eine hohe Modellgenauigkeit und eignet sich für komplexere Textgenerierungsszenarien, wie z. B. professionelle Texterstellung, Romankreation und hochwertige Übersetzungen, mit einer Kontextfensterlänge von 32k."}, "Skylark2-pro-4k": {"description": "Das zweite Modell der Skylark-Reihe, die Skylark2-pro-Version hat eine hohe Modellgenauigkeit und eignet sich für komplexere Textgenerierungsszenarien, wie z. B. professionelle Texterstellung, Romankreation und hochwertige Übersetzungen, mit einer Kontextfensterlänge von 4k."}, "Skylark2-pro-character-4k": {"description": "Das zweite Modell der Skylark-Reihe, das Skylark2-pro-character-<PERSON>l hat hervorragende Fähigkeiten im Rollenspiel und Chat, kann sich entsprechend den Anforderungen des Benutzers verkleiden und bietet natürliche und flüssige Dialoginhalte. Es eignet sich für den Aufbau von Cha<PERSON>s, virtuellen Assistenten und Online-Kundensupport und bietet eine hohe Reaktionsgeschwindigkeit."}, "Skylark2-pro-turbo-8k": {"description": "Das zweite Modell der Skylark-Reihe, das Skylark2-pro-turbo-8k bietet schnellere Schlussfolgerungen und niedrigere Kosten, mit einer Kontextfensterlänge von 8k."}, "THUDM/GLM-4-32B-0414": {"description": "GLM-4-32B-0414 ist das neue Open-Source-Modell der GLM-Serie mit 32 Milliarden Parametern. Die Leistung dieses Modells kann mit der GPT-Serie von OpenAI und der V3/R1-Serie von DeepSeek verglichen werden."}, "THUDM/GLM-4-9B-0414": {"description": "GLM-4-9B-0414 ist ein kleines Modell der GLM-Serie mit 9 Milliarden Parametern. Dieses Modell übernimmt die technischen Merkmale der GLM-4-32B-Serie, bietet jedoch eine leichtere Bereitstellungsoption. Trotz seiner kleineren Größe zeigt GLM-4-9B-0414 hervorragende Fähigkeiten in Aufgaben wie Codegenerierung, Webdesign, SVG-Grafikgenerierung und suchbasiertem Schreiben."}, "THUDM/GLM-4.1V-9B-Thinking": {"description": "GLM-4.1V-9B-Thinking ist ein von Zhipu AI und dem KEG-Labor der Tsinghua-Universität gemeinsam veröffentlichtes Open-Source-Visuell-Sprachmodell (VLM), das speziell für die Bewältigung komplexer multimodaler kognitiver Aufgaben entwickelt wurde. Das Modell basiert auf dem GLM-4-9B-0414-Grundmodell und verbessert durch die Einführung des „Chain-of-Thought“-Schlussmechanismus und den Einsatz von Verstärkungslernstrategien seine multimodale Schlussfolgerungsfähigkeit und Stabilität erheblich."}, "THUDM/GLM-Z1-32B-0414": {"description": "GLM-Z1-32B-0414 ist ein Schlussfolgerungsmodell mit tiefen Denkfähigkeiten. Dieses Modell wurde auf der Grundlage von GLM-4-32B-0414 durch Kaltstart und verstärktes Lernen entwickelt und wurde weiter in Mathematik, Programmierung und logischen Aufgaben trainiert. Im Vergleich zum Basismodell hat GLM-Z1-32B-0414 die mathematischen Fähigkeiten und die Fähigkeit zur Lösung komplexer Aufgaben erheblich verbessert."}, "THUDM/GLM-Z1-9B-0414": {"description": "GLM-Z1-9B-0414 ist ein kleines Modell der GLM-Serie mit nur 9 Milliarden Parametern, das jedoch erstaunliche Fähigkeiten zeigt, während es die Open-Source-Tradition beibehält. Trotz seiner kleineren Größe zeigt dieses Modell hervorragende Leistungen in mathematischen Schlussfolgerungen und allgemeinen Aufgaben und hat in seiner Größenklasse eine führende Gesamtleistung unter Open-Source-Modellen."}, "THUDM/GLM-Z1-Rumination-32B-0414": {"description": "GLM-Z1-Rumination-32B-0414 ist ein tiefes Schlussfolgerungsmodell mit nachdenklichen Fähigkeiten (vergleichbar mit OpenAI's Deep Research). Im Gegensatz zu typischen tiefen Denkmodellen verwendet das nachdenkliche Modell längere Zeiträume des tiefen Denkens, um offenere und komplexere Probleme zu lösen."}, "THUDM/glm-4-9b-chat": {"description": "GLM-4 9B ist die Open-Source-Version, die ein optimiertes Dialogerlebnis für Konversationsanwendungen bietet."}, "Tongyi-Zhiwen/QwenLong-L1-32B": {"description": "QwenLong-L1-32B ist das erste große Langkontext-Inferenzmodell (LRM), das mit verstärkendem Lernen trainiert wurde und speziell für Langtext-Inferenzaufgaben optimiert ist. Das Modell erreicht durch ein progressives Kontext-Erweiterungs-Framework eine stabile Übertragung von kurzen zu langen Kontexten. In sieben Langkontext-Dokumenten-Q&A-Benchmarks übertrifft QwenLong-L1-32B Flaggschiffmodelle wie OpenAI-o3-mini und Qwen3-235B-A22B und erreicht eine Leistung vergleichbar mit Claude-3.7-Sonnet-Thinking. Es ist besonders stark in komplexen Aufgaben wie mathematischer, logischer und mehrstufiger Inferenz."}, "Yi-34B-Chat": {"description": "Yi-1.5-34B hat die hervorragenden allgemeinen Sprachfähigkeiten des ursprünglichen Modells beibehalten und durch inkrementelles Training von 500 Milliarden hochwertigen Tokens die mathematische Logik und Codierungsfähigkeiten erheblich verbessert."}, "abab5.5-chat": {"description": "<PERSON>ür produktivitätsorientierte Szenarien konzipiert, unterstützt es die Verarbeitung komplexer Aufgaben und die effiziente Textgenerierung, geeignet für professionelle Anwendungen."}, "abab5.5s-chat": {"description": "Speziell für chinesische Charakterdialoge konzipiert, bietet es hochwertige chinesische Dialoggenerierung und ist für verschiedene Anwendungsszenarien geeignet."}, "abab6.5g-chat": {"description": "Speziell für mehrsprachige Charakterdialoge konzipiert, unterstützt die hochwertige Dialoggenerierung in Englisch und anderen Sprachen."}, "abab6.5s-chat": {"description": "Geeignet für eine Vielzahl von Aufgaben der natürlichen Sprachverarbeitung, einschließlich Textgenerierung und Dialogsystemen."}, "abab6.5t-chat": {"description": "Für chinesische Charakterdialoge optimiert, bietet es flüssige und den chinesischen Ausdrucksgewohnheiten entsprechende Dialoggenerierung."}, "accounts/fireworks/models/deepseek-r1": {"description": "DeepSeek-R1 ist ein hochmodernes großes Sprachmodell, das durch verstärktes Lernen und Optimierung mit Kaltstartdaten hervorragende Leistungen in Inferenz, Mathematik und Programmierung bietet."}, "accounts/fireworks/models/deepseek-v3": {"description": "Ein leistungsstarkes Mixture-of-Experts (MoE) Sprachmodell von Deepseek mit insgesamt 671B Parametern, wobei 37B Parameter pro Token aktiviert werden."}, "accounts/fireworks/models/llama-v3-70b-instruct": {"description": "Das Llama 3 70B Instruct-Modell ist speziell für mehrsprachige Dialoge und natürliche Sprachverständnis optimiert und übertrifft die meisten Wettbewerbsmodelle."}, "accounts/fireworks/models/llama-v3-8b-instruct": {"description": "Das Llama 3 8B Instruct-Modell ist für Dialoge und mehrsprachige Aufgaben optimiert und bietet hervorragende und effiziente Leistungen."}, "accounts/fireworks/models/llama-v3-8b-instruct-hf": {"description": "Das Llama 3 8B Instruct-Modell (HF-Version) stimmt mit den offiziellen Ergebnissen überein und bietet hohe Konsistenz und plattformübergreifende Kompatibilität."}, "accounts/fireworks/models/llama-v3p1-405b-instruct": {"description": "Das Llama 3.1 405B Instruct-Modell verfügt über eine extrem große Anzahl von Parametern und eignet sich für komplexe Aufgaben und Anweisungsverfolgung in hochbelasteten Szenarien."}, "accounts/fireworks/models/llama-v3p1-70b-instruct": {"description": "Das Llama 3.1 70B Instruct-Modell bietet hervorragende natürliche Sprachverständnis- und Generierungsfähigkeiten und ist die ideale Wahl für Dialog- und Analyseaufgaben."}, "accounts/fireworks/models/llama-v3p1-8b-instruct": {"description": "Das Llama 3.1 8B Instruct-Modell ist speziell für mehrsprachige Dialoge optimiert und kann die meisten Open-Source- und Closed-Source-Modelle in gängigen Branchenbenchmarks übertreffen."}, "accounts/fireworks/models/llama-v3p2-11b-vision-instruct": {"description": "Meta's 11B Parameter instruct-Modell für Bildverarbeitung. Dieses Modell ist optimiert für visuelle Erkennung, Bildverarbeitung, Bildbeschreibung und die Beantwortung allgemeiner Fragen zu Bildern. Es kann visuelle Daten wie Diagramme und Grafiken verstehen und schließt die Lücke zwischen visuellen und sprachlichen Informationen, indem es textuelle Beschreibungen der Bilddetails generiert."}, "accounts/fireworks/models/llama-v3p2-3b-instruct": {"description": "Llama 3.2 3B instruct-Modell ist ein leichtgewichtiges mehrsprachiges Modell, das von Meta veröffentlicht wurde. Dieses Modell zielt darauf ab, die Effizienz zu steigern und bietet im Vergleich zu größeren Modellen signifikante Verbesserungen in Bezug auf Latenz und Kosten. Anwendungsbeispiele für dieses Modell sind Abfragen und Aufforderungsneuschreibungen sowie Schreibassistenz."}, "accounts/fireworks/models/llama-v3p2-90b-vision-instruct": {"description": "Meta's 90B Parameter instruct-Modell für Bildverarbeitung. Dieses Modell ist optimiert für visuelle Erkennung, Bildverarbeitung, Bildbeschreibung und die Beantwortung allgemeiner Fragen zu Bildern. Es kann visuelle Daten wie Diagramme und Grafiken verstehen und schließt die Lücke zwischen visuellen und sprachlichen Informationen, indem es textuelle Beschreibungen der Bilddetails generiert."}, "accounts/fireworks/models/llama-v3p3-70b-instruct": {"description": "Llama 3.3 70B Instruct ist die aktualisierte Version von Llama 3.1 70B aus dem Dezember. Dieses Modell wurde auf der Grundlage von Llama 3.1 70B (veröffentlicht im Juli 2024) verbessert und bietet erweiterte Funktionen für Toolaufrufe, mehrsprachige Textunterstützung sowie mathematische und Programmierfähigkeiten. Das Modell erreicht branchenführende Leistungen in den Bereichen Inferenz, Mathematik und Befehlsbefolgung und bietet eine ähnliche Leistung wie 3.1 405B, während es gleichzeitig signifikante Vorteile in Bezug auf Geschwindigkeit und Kosten bietet."}, "accounts/fireworks/models/mistral-small-24b-instruct-2501": {"description": "Ein 24B-Parameter-Modell mit fortschrittlichen Fähigkeiten, die mit größeren Modellen vergleichbar sind."}, "accounts/fireworks/models/mixtral-8x22b-instruct": {"description": "Das Mixtral MoE 8x22B Instruct-Modell unterstützt durch seine große Anzahl an Parametern und Multi-Expert-Architektur die effiziente Verarbeitung komplexer Aufgaben."}, "accounts/fireworks/models/mixtral-8x7b-instruct": {"description": "Das Mixtral MoE 8x7B Instruct-Modell bietet durch seine Multi-Expert-Architektur effiziente Anweisungsverfolgung und -ausführung."}, "accounts/fireworks/models/mythomax-l2-13b": {"description": "Das MythoMax L2 13B-Modell kombiniert neuartige Kombinations-Technologien und ist besonders gut in Erzählungen und Rollenspielen."}, "accounts/fireworks/models/phi-3-vision-128k-instruct": {"description": "Das Phi 3 Vision Instruct-Modell ist ein leichtgewichtiges multimodales Modell, das komplexe visuelle und textuelle Informationen verarbeiten kann und über starke Schlussfolgerungsfähigkeiten verfügt."}, "accounts/fireworks/models/qwen-qwq-32b-preview": {"description": "Das QwQ-Modell ist ein experimentelles Forschungsmodell, das vom Qwen-Team entwickelt wurde und sich auf die Verbesserung der KI-Inferenzfähigkeiten konzentriert."}, "accounts/fireworks/models/qwen2-vl-72b-instruct": {"description": "Die 72B-Version des Qwen-VL-Modells ist das neueste Ergebnis von Alibabas Iteration und repräsentiert fast ein Jahr an Innovation."}, "accounts/fireworks/models/qwen2p5-72b-instruct": {"description": "Qwen2.5 ist eine Reihe von Sprachmodellen mit ausschließlich Decodern, die vom Alibaba Cloud Qwen-Team entwickelt wurde. Diese Modelle sind in verschiedenen Größen erh<PERSON>ltlich, da<PERSON><PERSON> 0.5B, 1.5B, 3B, 7B, 14B, 32B und 72B, mit Basis- und instruct-Varianten."}, "accounts/fireworks/models/qwen2p5-coder-32b-instruct": {"description": "Qwen2.5 Coder 32B Instruct ist die neueste Version der von Alibaba Cloud veröffentlichten Reihe von code-spezifischen großen Sprachmodellen. Dieses Modell basiert auf Qwen2.5 und wurde mit 55 Billionen Tokens trainiert, um die Fähigkeiten zur Codegenerierung, Inferenz und Fehlerbehebung erheblich zu verbessern. Es verbessert nicht nur die Codierungsfähigkeiten, sondern bewahrt auch die Vorteile in Mathematik und allgemeinen Fähigkeiten. Das Modell bietet eine umfassendere Grundlage für praktische Anwendungen wie Code-Agenten."}, "accounts/yi-01-ai/models/yi-large": {"description": "Das Yi-Large-Modell bietet hervorragende mehrsprachige Verarbeitungsfähigkeiten und kann für verschiedene Sprachgenerierungs- und Verständnisaufgaben eingesetzt werden."}, "ai21-jamba-1.5-large": {"description": "Ein mehrsprachiges Modell mit 398 Milliarden Parametern (94 Milliarden aktiv), das ein 256K langes Kontextfenster, Funktionsaufrufe, strukturierte Ausgaben und fundierte Generierung bietet."}, "ai21-jamba-1.5-mini": {"description": "Ein mehrsprachiges Modell mit 52 Milliarden Parametern (12 Milliarden aktiv), das ein 256K langes Kontextfenster, Funktionsaufrufe, strukturierte Ausgaben und fundierte Generierung bietet."}, "ai21-labs/AI21-Jamba-1.5-Large": {"description": "Ein mehrsprachiges Modell mit 398 Milliarden Parametern (davon 94 Milliarden aktiv), das ein 256K langes Kontextfenster, Funktionsaufrufe, strukturierte Ausgaben und faktengestützte Generierung bietet."}, "ai21-labs/AI21-Jamba-1.5-Mini": {"description": "Ein mehrsprachiges Modell mit 52 Milliarden Parametern (davon 12 Milliarden aktiv), das ein 256K langes Kontextfenster, Funktionsaufrufe, strukturierte Ausgaben und faktengestützte Generierung bietet."}, "anthropic.claude-3-5-sonnet-20240620-v1:0": {"description": "Claude 3.5 Son<PERSON> hebt den Branchenstandard an, übertrifft die Konkurrenzmodelle und Claude 3 Opus und zeigt in umfassenden Bewertungen hervorragende Leistungen, während es die Geschwindigkeit und Kosten unserer mittleren Modelle beibehält."}, "anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 <PERSON><PERSON> setzt neue Maßstäbe in der Branche, übertrifft die Modelle der Konkurrenz und Claude 3 Opus, und zeigt in umfassenden Bewertungen hervorragende Leistungen, während es die Geschwindigkeit und Kosten unserer mittelgroßen Modelle beibehält."}, "anthropic.claude-3-haiku-20240307-v1:0": {"description": "Claude 3 Haiku ist das schnellste und kompakteste Modell von Anthropic und bietet nahezu sofortige Reaktionsgeschwindigkeiten. Es kann schnell einfache Anfragen und Anforderungen beantworten. Kunden werden in der Lage sein, nahtlose AI-Erleb<PERSON><PERSON> zu schaffen, die menschliche Interaktionen nachahmen. Claude 3 Haiku kann Bilder verarbeiten und Textausgaben zurückgeben, mit einem Kontextfenster von 200K."}, "anthropic.claude-3-opus-20240229-v1:0": {"description": "Claude 3 Opus ist das leistungsstärkste AI-Modell von Anthropic mit fortschrittlicher Leistung bei hochkomplexen Aufgaben. Es kann offene Eingaben und unbekannte Szenarien verarbeiten und zeigt hervorragende Flüssigkeit und menschenähnliches Verständnis. Claude 3 Opus demonstriert die Grenzen der Möglichkeiten generativer AI. Claude 3 Opus kann Bilder verarbeiten und Textausgaben zurückgeben, mit einem Kontextfenster von 200K."}, "anthropic.claude-3-sonnet-20240229-v1:0": {"description": "Anthropic's Claude 3 Sonnet erreicht ein ideales Gleichgewicht zwischen Intelligenz und Geschwindigkeit – besonders geeignet für Unternehmensarbeitslasten. Es bietet maximalen Nutzen zu einem Preis, der unter dem der Konkurrenz liegt, und wurde als zuverlässiges, langlebiges Hauptmodell für skalierbare AI-Implementierungen konzipiert. Claude 3 Sonnet kann Bilder verarbeiten und Textausgaben zurückgeben, mit einem Kontextfenster von 200K."}, "anthropic.claude-instant-v1": {"description": "Ein schnelles, kostengünstiges und dennoch sehr leistungsfähiges Modell, das eine Reihe von Aufgaben bewältigen kann, darunter alltägliche Gespräche, Textanalysen, Zusammenfassungen und Dokumentenfragen."}, "anthropic.claude-v2": {"description": "Anthropic zeigt in e<PERSON> Vielzahl von <PERSON>, von komplexen Dialogen und kreativer Inhaltserstellung bis hin zu detaillierten Anweisungen, ein hohes Ma<PERSON> an Fähigkeiten."}, "anthropic.claude-v2:1": {"description": "Die aktualisierte Version von Claude 2 bietet ein doppelt so großes Kontextfenster sowie Verbesserungen in der Zuverlässigkeit, der Halluzinationsrate und der evidenzbasierten Genauigkeit in langen Dokumenten und RAG-Kontexten."}, "anthropic/claude-3-haiku": {"description": "Claude 3 Haiku ist das schnellste und kompakteste Modell von Anthropic, das darauf ausgelegt ist, nahezu sofortige Antworten zu liefern. Es bietet schnelle und präzise zielgerichtete Leistungen."}, "anthropic/claude-3-opus": {"description": "Claude 3 Opus ist das leistungsstärkste Modell von Anthropic zur Bearbeitung hochkomplexer Aufgaben. E<PERSON> zeichnet sich durch hervorragende Leistung, Intelligenz, Flüssigkeit und Verständnis aus."}, "anthropic/claude-3.5-haiku": {"description": "Claude 3.5 Haiku ist das schnellste nächste Generation Modell von Anthropic. Im Vergleich zu Claude 3 Haiku hat Claude 3.5 Hai<PERSON> in allen Fähigkeiten Fortschritte gemacht und übertrifft in vielen intellektuellen Benchmark-Tests das größte Modell der vorherigen Generation, Claude 3 Opus."}, "anthropic/claude-3.5-sonnet": {"description": "Claude 3.5 Sonnet biete<PERSON> Fähigkeiten, die über Opus hinausgehen, und eine schnellere Geschwindigkeit als Sonnet, während es den gleichen Preis wie Sonnet beibehält. Sonnet ist besonders gut in Programmierung, Datenwissenschaft, visueller Verarbeitung und Agentenaufgaben."}, "anthropic/claude-3.7-sonnet": {"description": "Claude 3.7 Sonnet ist das intelligenteste Modell von Anthropic bis heute und das erste hybride Inferenzmodell auf dem Markt. Claude 3.7 Sonnet kann nahezu sofortige Antworten oder verlängerte, schritt<PERSON>se Überlegungen erzeugen, wobei die Benutzer diesen Prozess klar nachvollziehen können. Sonnet ist besonders gut in den Bereichen Programmierung, Datenwissenschaft, visuelle Verarbeitung und Agentenaufgaben."}, "anthropic/claude-opus-4": {"description": "Claude Opus 4 ist das leistungsstärkste Modell von Anthropic zur Bewältigung hochkomplexer Aufgaben. Es zeichnet sich durch herausragende Leistung, Intelligenz, Flüssigkeit und Verständnis aus."}, "anthropic/claude-sonnet-4": {"description": "Claude Sonnet 4 kann nahezu sofortige Antworten oder verlängerte schrittweise Überlegungen erzeugen, die für den Nutzer klar nachvollziehbar sind. API-Nutzer können zudem die Denkzeit des Modells präzise steuern."}, "ascend-tribe/pangu-pro-moe": {"description": "Pangu-Pro-MoE 72B-A16B ist ein spärlich besetztes großes Sprachmodell mit 72 Milliarden Parametern und 16 Milliarden aktivierten Parametern. Es basiert auf der gruppierten Mixture-of-Experts-Architektur (MoGE), bei der Experten in Gruppen eingeteilt werden und Tokens innerhalb jeder Gruppe eine gleiche Anzahl von Experten aktivieren, um eine ausgewogene Expertenauslastung zu gewährleisten. Dies verbessert die Effizienz der Modellausführung auf der Ascend-Plattform erheblich."}, "aya": {"description": "Aya 23 ist ein mehrsprachiges Modell von Cohere, das 23 Sprachen unterstützt und die Anwendung in einer Vielzahl von Sprachen erleichtert."}, "aya:35b": {"description": "Aya 23 ist ein mehrsprachiges Modell von Cohere, das 23 Sprachen unterstützt und die Anwendung in einer Vielzahl von Sprachen erleichtert."}, "baichuan/baichuan2-13b-chat": {"description": "Baichuan-13B ist ein Open-Source-Sprachmodell mit 13 Milliarden Parametern, das von Baichuan Intelligence entwickelt wurde und in autorisierten chinesischen und englischen Benchmarks die besten Ergebnisse in seiner Größenordnung erzielt hat."}, "baidu/ERNIE-4.5-300B-A47B": {"description": "ERNIE-4.5-300B-A47B ist ein von Baidu entwickeltes großes Sprachmodell, das auf einer Mixture-of-Experts (MoE)-Architektur basiert. Das Modell verfügt über insgesamt 300 Milliarden Parameter, aktiviert jedoch bei der Inferenz nur 47 Milliarden Parameter pro Token, was eine starke Leistung bei gleichzeitig hoher Rechen-effizienz gewährleistet. Als eines der Kernmodelle der ERNIE 4.5-Serie zeigt es herausragende Fähigkeiten in Textverständnis, -generierung, Schlussfolgerung und Programmierung. Das Modell verwendet eine innovative multimodale heterogene MoE-Vortrainingsmethode, die durch gemeinsames Training von Text- und visuellen Modalitäten die Gesamtleistung verbessert, insbesondere bei der Befolgung von Anweisungen und dem Erinnern von Weltwissen."}, "c4ai-aya-expanse-32b": {"description": "Aya Expanse ist ein leistungsstarkes 32B mehrsprachiges Modell, das darauf abzielt, die Leistung von einsprachigen Modellen durch innovative Ansätze wie Anweisungsoptimierung, Datenarbitrage, Präferenztraining und Modellfusion herauszufordern. Es unterstützt 23 Sprachen."}, "c4ai-aya-expanse-8b": {"description": "Aya Expanse ist ein leistungsstarkes 8B mehrsprachiges Modell, das darauf abzielt, die Leistung von einsprachigen Modellen durch innovative Ansätze wie Anweisungsoptimierung, Datenarbitrage, Präferenztraining und Modellfusion herauszufordern. Es unterstützt 23 Sprachen."}, "c4ai-aya-vision-32b": {"description": "Aya Vision ist ein hochmodernes multimodales Modell, das in mehreren wichtigen Benchmarks für Sprache, Text und Bild hervorragende Leistungen zeigt. Diese 32B-Version konzentriert sich auf die fortschrittlichste mehrsprachige Leistung und unterstützt 23 Sprachen."}, "c4ai-aya-vision-8b": {"description": "Aya Vision ist ein hochmodernes multimodales Modell, das in mehreren wichtigen Benchmarks für Sprache, Text und Bild hervorragende Leistungen zeigt. Diese 8B-Version konzentriert sich auf niedrige Latenz und optimale Leistung."}, "charglm-3": {"description": "CharGLM-3 ist für Rollenspiele und emotionale Begleitung konzipiert und unterstützt extrem lange Mehrfachgedächtnisse und personalisierte Dialoge, mit breiter Anwendung."}, "charglm-4": {"description": "CharGLM-4 wurde speziell für Rollenspiele und emotionale Begleitung entwickelt, unterstützt extrem lange Mehrfachgedächtnisse und personalisierte Dialoge und findet breite Anwendung."}, "chatglm3": {"description": "ChatGLM3 ist ein proprietäres Modell, das von der KI-Forschungsgruppe Zhipu AI und dem KEG-Labor der Tsinghua-Universität veröffentlicht wurde. Es wurde durch umfangreiche Vortrainings mit chinesischen und englischen Bezeichnern sowie durch die Anpassung an menschliche Präferenzen entwickelt. Im Vergleich zum ersten Modell erzielte es Verbesserungen von 16 %, 36 % und 280 % in den Benchmarks MMLU, C-Eval und GSM8K und steht an der Spitze der chinesischen Aufgabenliste C-Eval. Es eignet sich für Szenarien, die hohe Anforderungen an das Wissensvolumen, die Inferenzfähigkeit und die Kreativität stellen, wie z. B. die Erstellung von Werbetexten, das Schreiben von Romanen, wissensbasiertes Schreiben und die Generierung von Code."}, "chatglm3-6b-base": {"description": "ChatGLM3-6b-base ist das neueste Modell der ChatGLM-Serie mit 6 Milliarden Parametern, ent<PERSON><PERSON><PERSON> von <PERSON>."}, "chatgpt-4o-latest": {"description": "ChatGPT-4o ist ein dynamisches Modell, das in Echtzeit aktualisiert wird, um die neueste Version zu gewährleisten. Es kombiniert starke Sprachverständnis- und Generierungsfähigkeiten und eignet sich für großangelegte Anwendungsszenarien, einschließlich Kundenservice, Bildung und technische Unterstützung."}, "claude-2.0": {"description": "Claude 2 bietet Unternehmen Fortschritte in kritischen Fähigkeiten, e<PERSON><PERSON><PERSON>ßlich branchenführenden 200K Token Kontext, erheblich reduzierter Häufigkeit von Modellillusionen, Systemaufforderungen und einer neuen Testfunktion: Werkzeugaufrufe."}, "claude-2.1": {"description": "Claude 2 bietet Unternehmen Fortschritte in kritischen Fähigkeiten, e<PERSON><PERSON><PERSON>ßlich branchenführenden 200K Token Kontext, erheblich reduzierter Häufigkeit von Modellillusionen, Systemaufforderungen und einer neuen Testfunktion: Werkzeugaufrufe."}, "claude-3-5-haiku-20241022": {"description": "Claude 3.5 Haiku ist das schnellste nächste Modell von Anthropic. Im Vergleich zu Claude 3 Hai<PERSON> hat <PERSON> 3.5 Hai<PERSON> in allen Fähigkeiten Verbesserungen erzielt und übertrifft das vorherige größte Modell, Claude 3 Opus, in vielen intellektuellen Benchmark-Tests."}, "claude-3-5-sonnet-20240620": {"description": "Claude 3.5 Sonnet bietet Fähigkeiten, die über Opus hinausgehen, und ist schneller als Sonnet, während es den gleichen Preis wie Sonnet beibehält. Sonnet ist besonders gut in Programmierung, Datenwissenschaft, visueller Verarbeitung und Agenturaufgaben."}, "claude-3-5-sonnet-20241022": {"description": "Claude 3.5 Sonnet bietet überlegene Fähigkeiten im Vergleich zu Opus und schnellere Geschwindigkeiten als Sonnet, während es den gleichen Preis wie Sonnet beibehält. Sonnet ist besonders gut in den Bereichen Programmierung, Datenwissenschaft, visuelle Verarbeitung und Aufgabenübertragung."}, "claude-3-7-sonnet-20250219": {"description": "Claude 3.7 Sonnet hebt den Branchenstandard an, übertrifft die Modelle der Konkurrenz und Claude 3 Opus, und zeigt in umfassenden Bewertungen hervorragende Leistungen, während es die Geschwindigkeit und Kosten unserer mittelgroßen Modelle beibehält."}, "claude-3-haiku-20240307": {"description": "Claude 3 Haiku ist das schnellste und kompakteste Modell von Anthropic, das darauf abzielt, nahezu sofortige Antworten zu liefern. Es bietet schnelle und präzise zielgerichtete Leistungen."}, "claude-3-opus-20240229": {"description": "Claude 3 Opus ist das leistungsstärkste Modell von Anthropic für die Verarbeitung hochkomplexer Aufgaben. Es bietet herausragende Leistungen in Bezug auf Leistung, Intelligenz, Flüssigkeit und Verständnis."}, "claude-3-sonnet-20240229": {"description": "Claude 3 Sonnet bietet eine ideale Balance zwischen Intelligenz und Geschwindigkeit für Unternehmensarbeitslasten. Es bietet maximalen Nutzen zu einem niedrigeren Preis, ist zuverlässig und für großflächige Bereitstellungen geeignet."}, "claude-opus-4-20250514": {"description": "Claude Opus 4 ist das leistungsstärkste Modell von Anthropic zur Bewältigung hochkomplexer Aufgaben. Es zeichnet sich durch hervorragende Leistung, Intelligenz, Flüssigkeit und Verständnis aus."}, "claude-sonnet-4-20250514": {"description": "Claude 4 Sonnet kann nahezu sofortige Antworten oder verlängerte, schrittweise Überlegungen erzeugen, wobei die Benutzer diesen Prozess klar nachvollziehen können. API-Nutzer haben zudem die Möglichkeit, die Denkzeit des Modells detailliert zu steuern."}, "codegeex-4": {"description": "CodeGeeX-4 ist ein leistungsstarker AI-Programmierassistent, der intelligente Fragen und Codevervollständigung in verschiedenen Programmiersprachen unterstützt und die Entwicklungseffizienz steigert."}, "codegeex4-all-9b": {"description": "CodeGeeX4-ALL-9B ist ein mehrsprachiges Code-Generierungsmodell, das umfassende Funktionen unterstützt, darunter Code-Vervollständigung und -Generierung, Code-Interpreter, Websuche, Funktionsaufrufe und repository-weite Codefragen und -antworten, und deckt verschiedene Szenarien der Softwareentwicklung ab. Es ist das führende Code-Generierungsmodell mit weniger als 10B Parametern."}, "codegemma": {"description": "CodeGemma ist ein leichtgewichtiges Sprachmodell, das speziell für verschiedene Programmieraufgaben entwickelt wurde und schnelle Iterationen und Integrationen unterstützt."}, "codegemma:2b": {"description": "CodeGemma ist ein leichtgewichtiges Sprachmodell, das speziell für verschiedene Programmieraufgaben entwickelt wurde und schnelle Iterationen und Integrationen unterstützt."}, "codellama": {"description": "Code Llama ist ein LLM, das sich auf die Codegenerierung und -diskussion konzentriert und eine breite Unterstützung für Programmiersprachen bietet, die sich für Entwicklerumgebungen eignet."}, "codellama/CodeLlama-34b-Instruct-hf": {"description": "Code Llama ist ein LLM, das sich auf die Codegenerierung und -diskussion konzentriert und eine breite Unterstützung für Programmiersprachen bietet, die für Entwicklerumgebungen geeignet ist."}, "codellama:13b": {"description": "Code Llama ist ein LLM, das sich auf die Codegenerierung und -diskussion konzentriert und eine breite Unterstützung für Programmiersprachen bietet, die sich für Entwicklerumgebungen eignet."}, "codellama:34b": {"description": "Code Llama ist ein LLM, das sich auf die Codegenerierung und -diskussion konzentriert und eine breite Unterstützung für Programmiersprachen bietet, die sich für Entwicklerumgebungen eignet."}, "codellama:70b": {"description": "Code Llama ist ein LLM, das sich auf die Codegenerierung und -diskussion konzentriert und eine breite Unterstützung für Programmiersprachen bietet, die sich für Entwicklerumgebungen eignet."}, "codeqwen": {"description": "CodeQwen1.5 ist ein großes Sprachmodell, das auf einer umfangreichen Code-Datenbasis trainiert wurde und speziell für die Lösung komplexer Programmieraufgaben entwickelt wurde."}, "codestral": {"description": "Codestral ist das erste Code-Modell von Mistral AI und bietet hervorragende Unterstützung für Aufgaben der Codegenerierung."}, "codestral-latest": {"description": "Codestral ist ein hochmodernes Generierungsmodell, das sich auf die Codegenerierung konzentriert und für Aufgaben wie das Ausfüllen von Zwischenräumen und die Codevervollständigung optimiert wurde."}, "codex-mini-latest": {"description": "codex-mini-latest ist eine feinabgestimmte Version von o4-mini, speziell für Codex CLI entwickelt. Für die direkte Nutzung über die API empfehlen wir den Start mit gpt-4.1."}, "cognitivecomputations/dolphin-mixtral-8x22b": {"description": "Dolphin Mixtral 8x22B ist ein Modell, das für die Befolgung von Anweisungen, Dialoge und Programmierung entwickelt wurde."}, "cogview-4": {"description": "CogView-4 ist das erste von Zhipu entwickelte Open-Source-Text-zu-Bild-Modell, das die Generierung chinesischer Schriftzeichen unterstützt. Es bietet umfassende Verbesserungen in den Bereichen semantisches Verständnis, Bildgenerierungsqualität und die Fähigkeit, chinesische und englische Schriftzeichen zu erzeugen. Es unterstützt mehrsprachige Eingaben beliebiger Länge in Chinesisch und Englisch und kann Bilder in beliebiger Auflösung innerhalb eines vorgegebenen Bereichs erzeugen."}, "cohere-command-r": {"description": "Command R ist ein skalierbares generatives Modell, das auf RAG und Tool-Nutzung abzielt, um KI in Produktionsgröße für Unternehmen zu ermöglichen."}, "cohere-command-r-plus": {"description": "Command R+ ist ein hochmodernes, RAG-optimiertes Modell, das für unternehmensgerechte Arbeitslasten konzipiert ist."}, "cohere/Cohere-command-r": {"description": "Command R ist ein skalierbares Generierungsmodell, das für RAG und Tool-Nutzung entwickelt wurde, um Unternehmen produktionsreife KI zu ermöglichen."}, "cohere/Cohere-command-r-plus": {"description": "Command R+ ist ein hochmodernes, für RAG optimiertes Modell, das für unternehmensweite Arbeitslasten ausgelegt ist."}, "command": {"description": "Ein dialogbasiertes Modell, das Anweisungen folgt und in sprachlichen Aufgaben hohe Qualität und Zuverlässigkeit bietet. Im Vergleich zu unserem grundlegenden Generierungsmodell hat es eine längere Kontextlänge."}, "command-a-03-2025": {"description": "Command A ist unser bisher leistungsstärkstes Modell, das in der Nutzung von Werkzeugen, Agenten, Retrieval-Enhanced Generation (RAG) und mehrsprachigen Anwendungsszenarien hervorragende Leistungen zeigt. Command A hat eine Kontextlänge von 256K, benötigt nur zwei GPUs zum Betrieb und bietet im Vergleich zu Command R+ 08-2024 eine Steigerung der Durchsatzrate um 150 %."}, "command-light": {"description": "<PERSON><PERSON> kleinere, schnellere Version von Command, die fast ebenso leistungsstark ist, aber schneller arbeitet."}, "command-light-nightly": {"description": "Um die Zeitspanne zwischen den Hauptversionsveröffentlichungen zu verkürzen, haben wir eine nächtliche Version des Command Modells eingeführt. Für die command-light-Serie wird diese Version als command-light-nightly bezeichnet. Bitte beachten Si<PERSON>, dass command-light-nightly die neueste, experimentellste und (möglicherweise) instabilste Version ist. Die nächtlichen Versionen werden regelmäßig aktualisiert, ohne vorherige Ankündigung, daher wird die Verwendung in Produktionsumgebungen nicht empfohlen."}, "command-nightly": {"description": "Um die Zeitspanne zwischen den Hauptversionsveröffentlichungen zu verkürzen, haben wir eine nächtliche Version des Command Modells eingeführt. Für die Command-Serie wird diese Version als command-cightly bezeichnet. Bitte beachten Sie, dass command-nightly die neueste, experimentellste und (möglicherweise) instabilste Version ist. Die nächtlichen Versionen werden regelmäßig aktualisiert, ohne vorherige Ankündigung, daher wird die Verwendung in Produktionsumgebungen nicht empfohlen."}, "command-r": {"description": "Command R ist ein LLM, das für Dialoge und Aufgaben mit langen Kontexten optimiert ist und sich besonders gut für dynamische Interaktionen und Wissensmanagement eignet."}, "command-r-03-2024": {"description": "Command R ist ein dialogbasiertes Modell, das Anweisungen folgt und in sprachlichen Aufgaben eine höhere Qualität und Zuverlässigkeit bietet. Im Vergleich zu früheren Modellen hat es eine längere Kontextlänge. Es kann für komplexe Workflows wie Codegenerierung, Retrieval-Enhanced Generation (RAG), Werkzeugnutzung und Agenten verwendet werden."}, "command-r-08-2024": {"description": "command-r-08-2024 ist die aktualisierte Version des Command R Modells, das im August 2024 veröffentlicht wurde."}, "command-r-plus": {"description": "Command R+ ist ein leistungsstarkes großes Sprachmodell, das speziell für reale Unternehmensszenarien und komplexe Anwendungen entwickelt wurde."}, "command-r-plus-04-2024": {"description": "Command R+ ist ein dialogbasiertes Modell, das Anweisungen folgt und in sprachlichen Aufgaben eine höhere Qualität und Zuverlässigkeit bietet. Im Vergleich zu früheren Modellen hat es eine längere Kontextlänge. Es eignet sich am besten für komplexe RAG-Workflows und mehrstufige Werkzeugnutzung."}, "command-r-plus-08-2024": {"description": "Command R+ ist ein dialogbasiertes Modell, das Anweisungen befolgt und in sprachlichen Aufgaben eine höhere Qualität und Zuverlässigkeit bietet, mit einer längeren Kontextlänge im Vergleich zu früheren Modellen. Es eignet sich am besten für komplexe RAG-Workflows und die Nutzung mehrerer Werkzeuge."}, "command-r7b-12-2024": {"description": "command-r7b-12-2024 ist eine kompakte und effiziente aktualisierte Version, die im Dezember 2024 veröffentlicht wurde. Es zeigt hervorragende Leistungen in Aufgaben, die komplexes Denken und mehrstufige Verarbeitung erfordern, wie RAG, Werkzeugnutzung und Agenten."}, "compound-beta": {"description": "Compound-beta ist ein hybrides KI-System, das von mehreren öffentlich verfügbaren Modellen in GroqCloud unterstützt wird und intelligent und selektiv Werkzeuge zur Beantwortung von Benutzeranfragen einsetzt."}, "compound-beta-mini": {"description": "Compound-beta-mini ist ein hybrides KI-System, das von öffentlich verfügbaren Modellen in GroqCloud unterstützt wird und intelligent und selektiv Werkzeuge zur Beantwortung von Benutzeranfragen einsetzt."}, "computer-use-preview": {"description": "Das Modell computer-use-preview ist ein speziell für „Computeranwendungstools“ entwickeltes Modell, das darauf trainiert wurde, computerbezogene Aufgaben zu verstehen und auszuführen."}, "dall-e-2": {"description": "Zweite Generation des DALL·E-Modells, unterstützt realistischere und genauere Bildgenerierung, mit einer Auflösung, die viermal so hoch ist wie die der ersten Generation."}, "dall-e-3": {"description": "Das neueste DALL·E-Modell, veröffentlicht im November 2023. Unterstützt realistischere und genauere Bildgenerierung mit verbesserter Detailgenauigkeit."}, "databricks/dbrx-instruct": {"description": "DBRX Instruct bietet zuverlässige Anweisungsverarbeitungsfähigkeiten und unterstützt Anwendungen in verschiedenen Branchen."}, "deepseek-ai/DeepSeek-R1": {"description": "DeepSeek-R1 ist ein durch verstärkendes Lernen (RL) gesteuertes Inferenzmodell, das die Probleme der Wiederholbarkeit und Lesbarkeit im Modell löst. Vor dem RL führte DeepSeek-R1 Kaltstartdaten ein, um die Inferenzleistung weiter zu optimieren. E<PERSON> zeigt in mathematischen, programmierbezogenen und Inferenzaufgaben eine vergleichbare Leistung zu OpenAI-o1 und verbessert durch sorgfältig gestaltete Trainingsmethoden die Gesamteffizienz."}, "deepseek-ai/DeepSeek-R1-0528": {"description": "DeepSeek R1 verbessert durch den Einsatz erhöhter Rechenressourcen und die Einführung algorithmischer Optimierungsmechanismen im Nachtraining signifikant die Tiefe seiner Schlussfolgerungs- und Deduktionsfähigkeiten. Das Modell zeigt hervorragende Leistungen in verschiedenen Benchmark-Tests, einschließlich Mathematik, Programmierung und allgemeiner Logik. Die Gesamtleistung nähert sich führenden Modellen wie O3 und Gemini 2.5 Pro an."}, "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B": {"description": "DeepSeek-R1-0528-Qwen3-8B ist ein Modell, das durch Destillation der Denkprozesskette vom DeepSeek-R1-0528-Modell auf das Qwen3 8B Base Modell gewonnen wurde. E<PERSON> erreicht in Open-Source-Modellen den Stand der Technik (SOTA), übertrifft im AIME 2024 Test Qwen3 8B um 10 % und erreicht die Leistungsstufe von Qwen3-235B-thinking. Das Modell zeigt hervorragende Leistungen in Mathematik, Programmierung und allgemeiner Logik in mehreren Benchmarks. Die Architektur entspricht Qwen3-8B, teilt jedoch die Tokenizer-Konfiguration von DeepSeek-R1-0528."}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": {"description": "Das DeepSeek-R1-Distill-Modell optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten. Das Open-Source-Modell setzt neue Maßstäbe für Multitasking."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": {"description": "Das DeepSeek-R1-Distill-Modell optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten. Das Open-Source-Modell setzt neue Maßstäbe für Multitasking."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"description": "Das DeepSeek-R1-Distill-Modell optimiert die Inferenzleistung durch verstärkendes Lernen und Kaltstartdaten. Das Open-Source-Modell setzt neue Maßstäbe für Multitasking."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": {"description": "DeepSeek-R1-Distill-Qwen-32B ist ein Modell, das durch Wissensdestillation aus Qwen2.5-32B gewonnen wurde. Dieses <PERSON>l wurde mit 800.000 ausgewählten Beispielen, die von DeepSeek-R1 generiert wurden, feinabgestimmt und zeigt herausragende Leistungen in mehreren Bereichen wie Mathematik, Programmierung und Inferenz. Es hat in mehreren Benchmark-Tests, darunter AIME 2024, MATH-500 und GPQA Diamond, hervorragende Ergebnisse erzielt, wobei es in MATH-500 eine Genauigkeit von 94,3 % erreicht hat und damit starke mathematische Schlussfolgerungsfähigkeiten demonstriert."}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {"description": "DeepSeek-R1-Distill-Qwen-7B ist ein Modell, das durch Wissensdestillation aus Qwen2.5-Math-7B gewonnen wurde. Dieses <PERSON>l wurde mit 800.000 ausgewählten Beispielen, die von DeepSeek-R1 generiert wurden, feinabgestimmt und zeigt hervorragende Inferenzfähigkeiten. Es hat in mehreren Benchmark-Tests, darunter eine Genauigkeit von 92,8 % in MATH-500, eine <PERSON>quo<PERSON> von 55,5 % in AIME 2024 und eine Bewertung von 1189 in CodeForces, was starke mathematische und Programmierfähigkeiten für ein 7B-Modell demonstriert."}, "deepseek-ai/DeepSeek-V2.5": {"description": "DeepSeek V2.5 vereint die hervorragenden Merkmale früherer Versionen und verbessert die allgemeinen und kodierenden Fähigkeiten."}, "deepseek-ai/DeepSeek-V3": {"description": "DeepSeek-V3 ist ein hybrides Expertenmodell (MoE) mit 6710 Milliarden Parametern, das eine Multi-Head-Latent-Attention (MLA) und die DeepSeekMoE-Architektur verwendet, kombiniert mit einer Lastenausgleichsstrategie ohne Hilfskosten, um die Inferenz- und Trainingseffizienz zu optimieren. Durch das Pre-Training auf 14,8 Billionen hochwertigen Tokens und anschließendes überwachten Feintuning und verstärkendes Lernen übertrifft DeepSeek-V3 in der Leistung andere Open-Source-Modelle und nähert sich führenden Closed-Source-Modellen."}, "deepseek-ai/deepseek-llm-67b-chat": {"description": "DeepSeek 67B ist ein fortschrittliches Modell, das für komplexe Dialoge trainiert wurde."}, "deepseek-ai/deepseek-r1": {"description": "<PERSON><PERSON>modernes, effizientes LLM, das auf Schlussfolgern, Mathematik und Programmierung spezialisiert ist."}, "deepseek-ai/deepseek-vl2": {"description": "DeepSeek-VL2 ist ein hybrides Expertenmodell (MoE) für visuelle Sprache, das auf DeepSeekMoE-27B basiert und eine spärliche Aktivierung der MoE-Architektur verwendet, um außergewöhnliche Leistungen bei der Aktivierung von nur 4,5 Milliarden Parametern zu erzielen. Dieses Modell zeigt hervorragende Leistungen in mehreren Aufgaben, darunter visuelle Fragenbeantwortung, optische Zeichenerkennung, Dokument-/Tabellen-/Diagrammverständnis und visuelle Lokalisierung."}, "deepseek-chat": {"description": "Ein neues Open-Source-Modell, das allgemeine und Codefähigkeiten kombiniert. Es bewahrt nicht nur die allgemeinen Dialogfähigkeiten des ursprünglichen Chat-Modells und die leistungsstarken Codeverarbeitungsfähigkeiten des Coder-Modells, sondern stimmt auch besser mit menschlichen Präferenzen überein. Darüber hinaus hat DeepSeek-V2.5 in mehreren Bereichen wie Schreibaufgaben und Befolgung von Anweisungen erhebliche Verbesserungen erzielt."}, "deepseek-coder-33B-instruct": {"description": "DeepSeek Coder 33B ist ein Code-<PERSON><PERSON><PERSON><PERSON>dell, das auf 20 Billionen Daten trainiert wurde, von denen 87 % Code und 13 % in Chinesisch und Englisch sind. Das Modell führt eine Fenstergröße von 16K und Aufgaben zur Lückenergänzung ein und bietet projektbezogene Code-Vervollständigung und Fragmentfüllfunktionen."}, "deepseek-coder-v2": {"description": "DeepSeek Coder V2 ist ein Open-Source-Mischexperten-Code-Modell, das in Codeaufgaben hervorragende Leistungen erbringt und mit GPT4-Turbo vergleichbar ist."}, "deepseek-coder-v2:236b": {"description": "DeepSeek Coder V2 ist ein Open-Source-Mischexperten-Code-Modell, das in Codeaufgaben hervorragende Leistungen erbringt und mit GPT4-Turbo vergleichbar ist."}, "deepseek-r1": {"description": "DeepSeek-R1 ist ein durch verstärkendes Lernen (RL) gesteuertes Inferenzmodell, das die Probleme der Wiederholbarkeit und Lesbarkeit im Modell löst. Vor dem RL führte DeepSeek-R1 Kaltstartdaten ein, um die Inferenzleistung weiter zu optimieren. E<PERSON> zeigt in mathematischen, programmierbezogenen und Inferenzaufgaben eine vergleichbare Leistung zu OpenAI-o1 und verbessert durch sorgfältig gestaltete Trainingsmethoden die Gesamteffizienz."}, "deepseek-r1-0528": {"description": "Das voll ausgestattete 685B-Modell, ver<PERSON><PERSON><PERSON>licht am 28. Mai 2025. DeepSeek-R1 nutzt im Nachtrainingsprozess umfangreiche Verstärkungslernverfahren und verbessert die Modell-Inferenzfähigkeit erheblich, selbst bei minimalen annotierten Daten. Es zeigt hohe Leistung und starke Fähigkeiten in Mathematik, Programmierung und natürlicher Sprachlogik."}, "deepseek-r1-70b-fast-online": {"description": "DeepSeek R1 70B <PERSON><PERSON><PERSON>version, die Echtzeit-Online-Suche unterstützt und eine schnellere Reaktionszeit bei gleichbleibender Modellleistung bietet."}, "deepseek-r1-70b-online": {"description": "DeepSeek R1 70B Standardversion, die Echtzeit-Online-Suche unterstützt und sich für Dialoge und Textverarbeitungsaufgaben eignet, die aktuelle Informationen benötigen."}, "deepseek-r1-distill-llama": {"description": "deepseek-r1-distill-llama ist ein Modell, das auf der Grundlage von Llama aus DeepSeek-R1 destilliert wurde."}, "deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 – das größere und intelligentere Modell im DeepSeek-Paket – wurde in die Llama 70B-Architektur destilliert. Basierend auf Benchmark-Tests und menschlicher Bewertung ist dieses Modell intelligenter als das ursprüngliche Llama 70B, insbesondere bei Aufgaben, die mathematische und faktische Genauigkeit erfordern."}, "deepseek-r1-distill-llama-8b": {"description": "Das DeepSeek-R1-Distill Modell wurde durch Wissensdistillationstechniken entwickelt, indem <PERSON>, die von DeepSeek-R1 generiert wurden, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> und andere Open-Source-Modelle feinabgestimmt wurden."}, "deepseek-r1-distill-qianfan-llama-70b": {"description": "Erstmals veröffentlicht am 14. Februar 2025, destil<PERSON>t vom Qianfan-Modellteam auf Basis des Llama3_70B Modells (gebaut mit Meta Llama), wobei auch die Qianfan-Korpora in die Destillationsdaten aufgenommen wurden."}, "deepseek-r1-distill-qianfan-llama-8b": {"description": "Erstmals veröffentlicht am 14. Februar 2025, destil<PERSON>t vom Qianfan-Modellteam auf Basis des Llama3_8B Modells (gebaut mit Meta Llama), wobei auch die Qianfan-Korpora in die Destillationsdaten aufgenommen wurden."}, "deepseek-r1-distill-qwen": {"description": "deepseek-r1-distill-qwen ist ein Modell, das auf der Grundlage von Qwen durch Distillierung aus DeepSeek-R1 erstellt wurde."}, "deepseek-r1-distill-qwen-1.5b": {"description": "Das DeepSeek-R1-Distill Modell wurde durch Wissensdistillationstechniken entwickelt, indem <PERSON>, die von DeepSeek-R1 generiert wurden, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> und andere Open-Source-Modelle feinabgestimmt wurden."}, "deepseek-r1-distill-qwen-14b": {"description": "Das DeepSeek-R1-Distill Modell wurde durch Wissensdistillationstechniken entwickelt, indem <PERSON>, die von DeepSeek-R1 generiert wurden, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> und andere Open-Source-Modelle feinabgestimmt wurden."}, "deepseek-r1-distill-qwen-32b": {"description": "Das DeepSeek-R1-Distill Modell wurde durch Wissensdistillationstechniken entwickelt, indem <PERSON>, die von DeepSeek-R1 generiert wurden, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> und andere Open-Source-Modelle feinabgestimmt wurden."}, "deepseek-r1-distill-qwen-7b": {"description": "Das DeepSeek-R1-Distill Modell wurde durch Wissensdistillationstechniken entwickelt, indem <PERSON>, die von DeepSeek-R1 generiert wurden, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> und andere Open-Source-Modelle feinabgestimmt wurden."}, "deepseek-r1-fast-online": {"description": "DeepSeek R1 Vollschnellversion, die Echtzeit-Online-Suche unterstützt und die leistungsstarken Fähigkeiten von 671B Parametern mit einer schnelleren Reaktionszeit kombiniert."}, "deepseek-r1-online": {"description": "DeepSeek R1 Vollversion mit 671B Parametern, die Echtzeit-Online-Suche unterstützt und über verbesserte Verständnis- und Generierungsfähigkeiten verfügt."}, "deepseek-reasoner": {"description": "Das von DeepSeek entwickelte Inferenzmodell. <PERSON><PERSON> Modell die endgültige Antwort ausgibt, gibt es zunächst eine Denkprozesskette aus, um die Genauigkeit der endgültigen Antwort zu erhöhen."}, "deepseek-v2": {"description": "DeepSeek V2 ist ein effizientes Mixture-of-Experts-<PERSON><PERSON><PERSON><PERSON>dell, das für wirtschaftliche Verarbeitungsanforderungen geeignet ist."}, "deepseek-v2:236b": {"description": "DeepSeek V2 236B ist das Design-Code-Modell von DeepSeek und bietet starke Fähigkeiten zur Codegenerierung."}, "deepseek-v3": {"description": "DeepSeek-V3 ist ein MoE-Modell, das von der Hangzhou DeepSeek Artificial Intelligence Technology Research Co., Ltd. entwickelt wurde. Es hat in mehreren Bewertungen herausragende Ergebnisse erzielt und belegt in den gängigen Rankings den ersten Platz unter den Open-Source-Modellen. Im Vergleich zum V2.5-Modell hat sich die Generierungsgeschwindigkeit um das Dreifache erhöht, was den Nutzern ein schnelleres und flüssigeres Nutzungserlebnis bietet."}, "deepseek-v3-0324": {"description": "DeepSeek-V3-0324 ist ein MoE-Modell mit 671 Milliarden Parametern, das in den Bereichen Programmierung und technische Fähigkeiten, Kontextverständnis und Verarbeitung langer Texte herausragende Vorteile bietet."}, "deepseek/deepseek-chat-v3-0324": {"description": "DeepSeek V3 ist ein Experten-Mischmodell mit 685B Parametern und die neueste Iteration der Flaggschiff-Chatmodellreihe des DeepSeek-Teams.\n\nEs erbt das [DeepSeek V3](/deepseek/deepseek-chat-v3) Modell und zeigt hervorragende Leistungen in verschiedenen Aufgaben."}, "deepseek/deepseek-chat-v3-0324:free": {"description": "DeepSeek V3 ist ein Experten-Mischmodell mit 685B Parametern und die neueste Iteration der Flaggschiff-Chatmodellreihe des DeepSeek-Teams.\n\nEs erbt das [DeepSeek V3](/deepseek/deepseek-chat-v3) Modell und zeigt hervorragende Leistungen in verschiedenen Aufgaben."}, "deepseek/deepseek-r1": {"description": "DeepSeek-R1 hat die Schlussfolgerungsfähigkeiten des Modells erheblich verbessert, selbst bei nur wenigen gekennzeichneten Daten. <PERSON><PERSON> das Modell die endgültige Antwort ausgibt, gibt es zunächst eine Denkprozesskette aus, um die Genauigkeit der endgültigen Antwort zu erhöhen."}, "deepseek/deepseek-r1-0528": {"description": "DeepSeek-R1 verbessert die Modellschlussfolgerungsfähigkeit erheblich, selbst bei sehr begrenzten annotierten Daten. Vor der Ausgabe der endgültigen Antwort generiert das Modell eine Denkprozesskette, um die Genauigkeit der Antwort zu erhöhen."}, "deepseek/deepseek-r1-0528:free": {"description": "DeepSeek-R1 verbessert die Modellschlussfolgerungsfähigkeit erheblich, selbst bei sehr begrenzten annotierten Daten. Vor der Ausgabe der endgültigen Antwort generiert das Modell eine Denkprozesskette, um die Genauigkeit der Antwort zu erhöhen."}, "deepseek/deepseek-r1-distill-llama-70b": {"description": "DeepSeek R1 Distill Llama 70B ist ein große<PERSON> Sprachmodell, das auf Llama3.3 70B basiert und durch Feinabstimmung mit den Ausgaben von DeepSeek R1 eine wettbewerbsfähige Leistung erreicht, die mit großen, fortschrittlichen Modellen vergleichbar ist."}, "deepseek/deepseek-r1-distill-llama-8b": {"description": "DeepSeek R1 Distill Llama 8B ist ein distilliertes großes Sprachmodell, das auf Llama-3.1-8B-Instruct basiert und durch Training mit den Ausgaben von DeepSeek R1 erstellt wurde."}, "deepseek/deepseek-r1-distill-qwen-14b": {"description": "DeepSeek R1 Distill Qwen 14B ist ein distilliertes großes Sprachmodell, das auf Qwen 2.5 14B basiert und durch Training mit den Ausgaben von DeepSeek R1 erstellt wurde. Dieses Modell hat in mehreren Benchmark-Tests OpenAI's o1-mini übertroffen und die neuesten technologischen Fortschritte bei dichten Modellen (state-of-the-art) erzielt. Hier sind einige Ergebnisse der Benchmark-Tests:\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nDas Modell zeigt durch Feinabstimmung mit den Ausgaben von DeepSeek R1 eine wettbewerbsfähige Leistung, die mit grö<PERSON>ren, fortschrittlichen Modellen vergleichbar ist."}, "deepseek/deepseek-r1-distill-qwen-32b": {"description": "DeepSeek R1 Distill Qwen 32B ist ein distilliertes großes Sprachmodell, das auf Qwen 2.5 32B basiert und durch Training mit den Ausgaben von DeepSeek R1 erstellt wurde. Dieses Modell hat in mehreren Benchmark-Tests OpenAI's o1-mini übertroffen und die neuesten technologischen Fortschritte bei dichten Modellen (state-of-the-art) erzielt. Hier sind einige Ergebnisse der Benchmark-Tests:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nDas Modell zeigt durch Feinabstimmung mit den Ausgaben von DeepSeek R1 eine wettbewerbsfähige Leistung, die mit grö<PERSON>ren, fortschrittlichen Modellen vergleichbar ist."}, "deepseek/deepseek-r1/community": {"description": "DeepSeek R1 ist das neueste Open-Source-Modell, das vom DeepSeek-Team veröffentlicht wurde und über eine äußerst leistungsstarke Inferenzleistung verfügt, insbesondere in den Bereichen Mathematik, Programmierung und logisches Denken, die mit dem OpenAI o1-Modell vergleichbar ist."}, "deepseek/deepseek-r1:free": {"description": "DeepSeek-R1 hat die Schlussfolgerungsfähigkeiten des Modells erheblich verbessert, selbst bei nur wenigen gekennzeichneten Daten. <PERSON><PERSON> das Modell die endgültige Antwort ausgibt, gibt es zunächst eine Denkprozesskette aus, um die Genauigkeit der endgültigen Antwort zu erhöhen."}, "deepseek/deepseek-v3": {"description": "DeepSeek-V3 hat einen bedeutenden Durchbruch in der Inferenzgeschwindigkeit im Vergleich zu früheren Modellen erzielt. Es belegt den ersten Platz unter den Open-Source-Modellen und kann mit den weltweit fortschrittlichsten proprietären Modellen konkurrieren. DeepSeek-V3 verwendet die Multi-Head-Latent-Attention (MLA) und die DeepSeekMoE-Architektur, die in DeepSeek-V2 umfassend validiert wurden. Darüber hinaus hat DeepSeek-V3 eine unterstützende verlustfreie Strategie für die Lastenverteilung eingeführt und mehrere Zielvorgaben für das Training von Mehrfachvorhersagen festgelegt, um eine stärkere Leistung zu erzielen."}, "deepseek/deepseek-v3/community": {"description": "DeepSeek-V3 hat einen bedeutenden Durchbruch in der Inferenzgeschwindigkeit im Vergleich zu früheren Modellen erzielt. Es belegt den ersten Platz unter den Open-Source-Modellen und kann mit den weltweit fortschrittlichsten proprietären Modellen konkurrieren. DeepSeek-V3 verwendet die Multi-Head-Latent-Attention (MLA) und die DeepSeekMoE-Architektur, die in DeepSeek-V2 umfassend validiert wurden. Darüber hinaus hat DeepSeek-V3 eine unterstützende verlustfreie Strategie für die Lastenverteilung eingeführt und mehrere Zielvorgaben für das Training von Mehrfachvorhersagen festgelegt, um eine stärkere Leistung zu erzielen."}, "deepseek_r1": {"description": "DeepSeek-R1 ist ein durch verstärktes Lernen (RL) gesteuertes Schlussfolgerungsmodell, das Probleme der Wiederholung und Lesbarkeit im Modell löst. Vor dem RL führte DeepSeek-R1 Kaltstartdaten ein, um die Inferenzleistung weiter zu optimieren. E<PERSON> zeigt in Mathematik, Programmierung und Schlussfolgerungsaufgaben vergleichbare Leistungen zu OpenAI-o1 und hat durch sorgfältig gestaltete Trainingsmethoden die Gesamtleistung verbessert."}, "deepseek_r1_distill_llama_70b": {"description": "DeepSeek-R1-Distill-Llama-70B ist e<PERSON> <PERSON>l, das durch Destillationstraining auf der Basis von Llama-3.3-70B-Instruct entwickelt wurde. Dieses Modell ist Teil der DeepSeek-R1-Serie und zeigt durch die Feinabstimmung mit Beispielen, die von DeepSeek-R1 generiert wurden, hervorragende Leistungen in Mathematik, Programmierung und Schlussfolgerung in mehreren Bereichen."}, "deepseek_r1_distill_qwen_14b": {"description": "DeepSeek-R1-Distill-Qwen-14B ist ein <PERSON>l, das durch Wissensdistillation auf der Basis von Qwen2.5-14B entwickelt wurde. Dieses Modell wurde mit 800.000 ausgewählten Beispielen, die von DeepSeek-R1 generiert wurden, feinabgestimmt und zeigt hervorragende Schlussfolgerungsfähigkeiten."}, "deepseek_r1_distill_qwen_32b": {"description": "DeepSeek-R1-Distill-Qwen-32B ist e<PERSON>l, das durch Wissensdistillation auf der Basis von Qwen2.5-32B entwickelt wurde. Dieses Modell wurde mit 800.000 ausgewählten Beispielen, die von DeepSeek-R1 generiert wurden, feinabgestimmt und zeigt in Mathematik, Programmierung und Schlussfolgerung in mehreren Bereichen herausragende Leistungen."}, "doubao-1.5-lite-32k": {"description": "Doubao-1.5-lite ist das neueste leichte Modell der nächsten Generation, das eine extrem schnelle Reaktionszeit bietet und sowohl in der Leistung als auch in der Latenz weltweit erstklassig ist."}, "doubao-1.5-pro-256k": {"description": "Doubao-1.5-pro-256k ist die umfassend verbesserte Version von Doubao-1.5-Pro, die die Gesamtleistung um 10 % steigert. <PERSON><PERSON> unterstützt Schlussfolgerungen mit einem Kontextfenster von 256k und eine maximale Ausgabelänge von 12k Tokens. Höhere Leistung, gr<PERSON><PERSON>res Fenster und ein hervorragendes Preis-Leistungs-Verhältnis machen es für eine breitere Palette von Anwendungsszenarien geeignet."}, "doubao-1.5-pro-32k": {"description": "Doubao-1.5-pro ist das neueste Hauptmodell der nächsten Generation, dessen Leistung umfassend verbessert wurde und das in den Bereichen Wissen, Code, Schlussfolgerungen usw. herausragende Leistungen zeigt."}, "doubao-1.5-thinking-pro": {"description": "Das Doubao-1.5 Modell für tiefes Denken ist neu und zeichnet sich in Fachbereichen wie Mathematik, Programmierung und wissenschaftlichem Denken sowie in allgemeinen Aufgaben wie kreativem Schreiben aus. Es erreicht oder nähert sich in mehreren renommierten Benchmarks wie AIME 2024, Codeforces und GPQA dem Spitzenlevel der Branche. Es unterstützt ein Kontextfenster von 128k und eine Ausgabe von 16k."}, "doubao-1.5-thinking-pro-m": {"description": "Doubao-1.5 ist ein neues tiefgründiges Denkmodell (m-Version mit nativer multimodaler Tiefeninferenzfähigkeit), das in Fachgebieten wie Mathematik, Programmierung, wissenschaftlichem Denken sowie bei allgemeinen Aufgaben wie kreativem Schreiben herausragende Leistungen zeigt. Erreicht oder nähert sich in renommierten Benchmarks wie AIME 2024, Codeforces und GPQA der Spitzenklasse der Branche. Unterstützt ein Kontextfenster von 128k und eine Ausgabe von 16k."}, "doubao-1.5-thinking-vision-pro": {"description": "Neues visuelles Tiefendenkmodell mit stärkerer allgemeiner multimodaler Verständnis- und Inferenzfähigkeit, das in 37 von 59 öffentlichen Benchmark-Tests SOTA-Leistungen erzielt."}, "doubao-1.5-ui-tars": {"description": "Doubao-1.5-UI-TARS ist ein nativer Agentenmodell für grafische Benutzeroberflächen (GUI). Es interagiert nahtlos mit GUIs durch menschenähnliche Fähigkeiten wie Wahrnehmung, Inferenz und Handlung."}, "doubao-1.5-vision-lite": {"description": "Doubao-1.5-vision-lite ist ein neu verbessertes multimodales großes Modell, das beliebige Auflösungen und extreme Seitenverhältnisse bei der Bilderkennung unterstützt und die Fähigkeiten in visueller Schlussfolgerung, Dokumentenerkennung, Detailverständnis und Befolgung von Anweisungen verbessert. Es unterstützt ein Kontextfenster von 128k und eine maximale Ausgabelänge von 16k Tokens."}, "doubao-1.5-vision-pro": {"description": "Doubao-1.5-vision-pro ist ein neu aufgerüstetes multimodales Großmodell, das Bilderkennung in beliebiger Auflösung und extremen Seitenverhältnissen unterstützt und die Fähigkeiten in visueller Inferenz, Do<PERSON>mentenerkennung, Detailverständnis und Befolgung von Anweisungen verbessert."}, "doubao-1.5-vision-pro-32k": {"description": "Doubao-1.5-vision-pro ist ein neu aufgerüstetes multimodales Großmodell, das Bilderkennung in beliebiger Auflösung und extremen Seitenverhältnissen unterstützt und die Fähigkeiten in visueller Inferenz, Do<PERSON>mentenerkennung, Detailverständnis und Befolgung von Anweisungen verbessert."}, "doubao-lite-128k": {"description": "Bietet extrem schnelle Reaktionszeiten und ein hervorragendes Preis-Leistungs-Verhältnis, um Kunden in verschiedenen Szenarien flexiblere Optionen zu bieten. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 128k."}, "doubao-lite-32k": {"description": "Bietet extrem schnelle Reaktionszeiten und ein hervorragendes Preis-Leistungs-Verhältnis, um Kunden in verschiedenen Szenarien flexiblere Optionen zu bieten. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 32k."}, "doubao-lite-4k": {"description": "Bietet extrem schnelle Reaktionszeiten und ein hervorragendes Preis-Leistungs-Verhältnis, um Kunden in verschiedenen Szenarien flexiblere Optionen zu bieten. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 4k."}, "doubao-pro-256k": {"description": "Das leistungsstärkste Hauptmodell, geeignet für komplexe Aufgaben. Es erzielt hervorragende Ergebnisse in Szenarien wie Referenzfragen, Zusammenfassungen, kreatives Schreiben, Textklassifikation und Rollenspielen. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 256<PERSON>."}, "doubao-pro-32k": {"description": "Das leistungsstärkste Hauptmodell, geeignet für komplexe Aufgaben. Es erzielt hervorragende Ergebnisse in Szenarien wie Referenzfragen, Zusammenfassungen, kreatives Schreiben, Textklassifikation und Rollenspielen. Unterstützt Inferenz und Feintuning mit einem Kontextfenster von 32k."}, "doubao-seed-1.6": {"description": "Doubao-Seed-1.6 ist ein neues multimodales Modell für tiefgehendes Denken, das drei Denkmodi unterstützt: auto, thinking und non-thinking. Im non-thinking-Modus ist die Modellleistung im Vergleich zu Doubao-1.5-pro/250115 deutlich verbessert. Unterstützt ein Kontextfenster von 256k und eine maximale Ausgabelänge von 16k Tokens."}, "doubao-seed-1.6-flash": {"description": "Doubao-Seed-1.6-flash ist ein multimodales Modell für tiefgehendes Denken mit extrem schneller Inferenzgeschwindigkeit, TPOT benötigt nur 10 ms; unterstützt sowohl Text- als auch visuelles Verständnis, die Textverständnisfähigkeit übertrifft die vorherige Lite-Generation, das visuelle Verständnis ist vergleichbar mit den Pro-Modellen der Konkurrenz. Unterstützt ein Kontextfenster von 256k und eine maximale Ausgabelänge von 16k Tokens."}, "doubao-seed-1.6-thinking": {"description": "Das Doubao-Seed-1.6-thinking Modell verfügt über stark verbesserte Denkfähigkeiten. I<PERSON> Vergleich zu Doubao-1.5-thinking-pro wurden die Grundfähigkeiten in Coding, Mathematik und logischem Denken weiter verbessert und unterstützt visuelles Verständnis. Unterstützt ein Kontextfenster von 256k und eine maximale Ausgabelänge von 16k Tokens."}, "doubao-seedream-3-0-t2i-250415": {"description": "Das Doubao-Bildgenerierungsmodell wurde vom ByteDance Seed Team entwickelt und unterstützt sowohl Text- als auch Bildeingaben, um eine hochgradig kontrollierbare und qualitativ hochwertige Bildgenerierung zu bieten. Es erzeugt Bilder basierend auf Text-Prompts."}, "doubao-vision-lite-32k": {"description": "Das Doubao-vision-Modell ist ein multimodales Groß<PERSON><PERSON> von <PERSON> mit starker Bildverständnis- und Inferenzfähigkeit sowie präziser Befehlsinterpretation. Es zeigt starke Leistung bei der Extraktion von Bild- und Textinformationen sowie bei bildbasierten Inferenzaufgaben und eignet sich für komplexere und umfassendere visuelle Frage-Antwort-Aufgaben."}, "doubao-vision-pro-32k": {"description": "Das Doubao-vision-Modell ist ein multimodales Groß<PERSON><PERSON> von <PERSON> mit starker Bildverständnis- und Inferenzfähigkeit sowie präziser Befehlsinterpretation. Es zeigt starke Leistung bei der Extraktion von Bild- und Textinformationen sowie bei bildbasierten Inferenzaufgaben und eignet sich für komplexere und umfassendere visuelle Frage-Antwort-Aufgaben."}, "emohaa": {"description": "Emohaa ist ein psychologisches Modell mit professionellen Beratungsfähigkeiten, das den Nutzern hilft, emotionale Probleme zu verstehen."}, "ernie-3.5-128k": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle deckt eine riesige Menge an chinesischen und englischen Korpora ab und bietet starke allgemeine Fähigkeiten, die die meisten Anforderungen an Dialogfragen, kreative Generierung und Plugin-Anwendungen erfüllen; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ernie-3.5-8k": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle deckt eine riesige Menge an chinesischen und englischen Korpora ab und bietet starke allgemeine Fähigkeiten, die die meisten Anforderungen an Dialogfragen, kreative Generierung und Plugin-Anwendungen erfüllen; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ernie-3.5-8k-preview": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle deckt eine riesige Menge an chinesischen und englischen Korpora ab und bietet starke allgemeine Fähigkeiten, die die meisten Anforderungen an Dialogfragen, kreative Generierung und Plugin-Anwendungen erfüllen; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ernie-4.0-8k-latest": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle hat im Vergleich zu ERNIE 3.5 eine umfassende Verbesserung der Modellfähigkeiten erreicht und ist weit verbreitet in komplexen Aufgabenbereichen anwendbar; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ernie-4.0-8k-preview": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle hat im Vergleich zu ERNIE 3.5 eine umfassende Verbesserung der Modellfähigkeiten erreicht und ist weit verbreitet in komplexen Aufgabenbereichen anwendbar; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten."}, "ernie-4.0-turbo-128k": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle zeigt hervorragende Gesamtergebnisse und ist weit verbreitet in komplexen Aufgabenbereichen anwendbar; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten. Im Vergleich zu ERNIE 4.0 bietet es eine bessere Leistung."}, "ernie-4.0-turbo-8k-latest": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle zeigt hervorragende Gesamtergebnisse und ist weit verbreitet in komplexen Aufgabenbereichen anwendbar; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten. Im Vergleich zu ERNIE 4.0 bietet es eine bessere Leistung."}, "ernie-4.0-turbo-8k-preview": {"description": "Das von Baidu entwickelte Flaggschiff-Modell für große Sprachmodelle zeigt hervorragende Gesamtergebnisse und ist weit verbreitet in komplexen Aufgabenbereichen anwendbar; es unterstützt die automatische Anbindung an das Baidu-Suchplugin, um die Aktualität der Antwortinformationen zu gewährleisten. Im Vergleich zu ERNIE 4.0 bietet es eine bessere Leistung."}, "ernie-4.5-8k-preview": {"description": "Das ERNIE 4.5 Modell ist ein neu <PERSON><PERSON><PERSON><PERSON>, natives multimodales <PERSON>-<PERSON><PERSON> von <PERSON>, das durch die gemeinsame Modellierung mehrerer Modalitäten eine synergistische Optimierung erreicht und über hervorragende multimodale Verständnisfähigkeiten verfügt; es bietet verbesserte Sprachfähigkeiten, umfassende Verbesserungen in Verständnis, Generierung, Logik und Gedächtnis, sowie signifikante Verbesserungen in der Vermeidung von Halluzinationen, logischen Schlussfolgerungen und Programmierfähigkeiten."}, "ernie-4.5-turbo-128k": {"description": "Wenxin 4.5 Turbo hat deutliche Verbesserungen in den Bereichen Halluzinationen reduzieren, logisches Denken und Programmierfähigkeiten. Im Vergleich zu Wenxin 4.5 ist es schneller und kostengünstiger. Die Modellfähigkeiten wurden umfassend verbessert, um besser mit mehrstufigen, langen historischen Dialogen und der Beantwortung von Fragen zu langen Dokumenten umzugehen."}, "ernie-4.5-turbo-32k": {"description": "Wenxin 4.5 Turbo hat deutliche Verbesserungen in den Bereichen Halluzinationen reduzieren, logisches Denken und Programmierfähigkeiten. Im Vergleich zu Wenxin 4.5 ist es schneller und kostengünstiger. Die Fähigkeiten in der Textkreation und Wissensfragen haben sich erheblich verbessert. Die Ausgabelänge und die Verzögerung bei vollständigen Sätzen sind im Vergleich zu ERNIE 4.5 gestiegen."}, "ernie-4.5-turbo-vl-32k": {"description": "Die neueste Version des Wenxin Yi Yan Modells hat signifikante Verbesserungen in den Bereichen Bildverständnis, Kreation, Übersetzung und Programmierung. Es unterstützt erstmals eine Kontextlänge von 32K, und die Verzögerung beim ersten Token wurde erheblich reduziert."}, "ernie-char-8k": {"description": "Das von Baidu entwickelte große Sprachmodell für vertikale Szenarien eignet sich für Anwendungen wie NPCs in Spielen, Kundenservice-Dialoge und Rollenspiele, mit einem klareren und konsistenteren Charakterstil, einer stärkeren Befolgung von Anweisungen und besserer Inferenzleistung."}, "ernie-char-fiction-8k": {"description": "Das von Baidu entwickelte große Sprachmodell für vertikale Szenarien eignet sich für Anwendungen wie NPCs in Spielen, Kundenservice-Dialoge und Rollenspiele, mit einem klareren und konsistenteren Charakterstil, einer stärkeren Befolgung von Anweisungen und besserer Inferenzleistung."}, "ernie-irag-edit": {"description": "Das von Baidu entwickelte ERNIE iRAG Edit Bildbearbeitungsmodell unterstützt Operationen wie Löschen (erase), Neumalen (repaint) und Variationserzeugung (variation) basierend auf Bildern."}, "ernie-lite-8k": {"description": "ERNIE Lite ist ein leichtgewichtiges großes Sprachmodell, das von Baidu entwickelt wurde und sowohl hervorragende Modellleistung als auch Inferenzleistung bietet, geeignet für die Verwendung mit AI-Beschleunigungskarten mit geringer Rechenleistung."}, "ernie-lite-pro-128k": {"description": "Das von Baidu entwickelte leichtgewichtige große Sprachmodell bietet sowohl hervorragende Modellleistung als auch Inferenzleistung, die besser ist als die von ERNIE Lite, und ist geeignet für die Verwendung mit AI-Beschleunigungskarten mit geringer Rechenleistung."}, "ernie-novel-8k": {"description": "Das von Baidu entwickelte allgemeine große Sprachmodell hat deutliche Vorteile in der Fähigkeit zur Fortsetzung von Romanen und kann auch in Szenarien wie Kurzdramen und Filmen eingesetzt werden."}, "ernie-speed-128k": {"description": "Das neueste hochleistungsfähige große Sprachmodell von <PERSON>, das 2024 veröffent<PERSON>t wurde, bietet hervorragende allgemeine Fähigkeiten und eignet sich gut als Basismodell für Feinabstimmungen, um spezifische Szenarien besser zu bewältigen, während es auch hervorragende Inferenzleistungen bietet."}, "ernie-speed-pro-128k": {"description": "Das neueste hochleistungsfähige große Sprachmodell von <PERSON>, das 2024 veröffentlicht wurde, bietet hervorragende allgemeine Fähigkeiten und ist besser als ERNIE Speed, geeignet als Basismodell für Feinabstimmungen, um spezifische Szenarien besser zu bewältigen, während es auch hervorragende Inferenzleistungen bietet."}, "ernie-tiny-8k": {"description": "ERNIE Tiny ist ein hochleistungsfähiges großes Sprachmodell, dessen Bereitstellungs- und Feinabstimmungskosten die niedrigsten unter den Wenshin-Modellen sind."}, "ernie-x1-32k": {"description": "Verfügt über stärkere Fähigkeiten in Verständnis, Planung, Reflexion und Evolution. Als umfassenderes tiefes Denkmodell kombiniert Wenxin X1 Genauigkeit, Kreativität und Ausdruckskraft und zeigt herausragende Leistungen in den Bereichen chinesische Wissensfragen, literarische Kreation, Textverfassung, alltägliche Gespräche, logisches Denken, komplexe Berechnungen und Werkzeugnutzung."}, "ernie-x1-32k-preview": {"description": "Das große Modell ERNIE X1 verfügt über verbesserte Fähigkeiten in Verständnis, Planung, Reflexion und Evolution. Als umfassenderes tiefes Denkmodell kombiniert ERNIE X1 Genauigkeit, Kreativität und Ausdruckskraft und zeigt herausragende Leistungen in den Bereichen chinesische Wissensabfragen, literarisches Schaffen, Textverfassung, alltägliche Gespräche, logisches Denken, komplexe Berechnungen und Werkzeugnutzung."}, "ernie-x1-turbo-32k": {"description": "<PERSON><PERSON> Vergleich zu ERNIE-X1-32K bietet dieses Modell bessere Leistung und Effizienz."}, "flux-1-schnell": {"description": "Ein von Black Forest Labs entwickeltes Text-zu-Bild-Modell mit 12 Milliarden Parametern, das latente adversariale Diffusionsdestillation verwendet und in 1 bis 4 Schritten hochwertige Bilder erzeugen kann. Die Leistung ist vergleichbar mit proprietären Alternativen und wird unter der Apache-2.0-<PERSON><PERSON><PERSON> für private, wissenschaftliche und kommerzielle Nutzung veröffentlicht."}, "flux-dev": {"description": "FLUX.1 [dev] ist ein Open-Source-Gewichtungs- und Feinschlichtungsmodell für nicht-kommerzielle Anwendungen. Es bietet eine Bildqualität und Instruktionsbefolgung ähnlich der professionellen FLUX-Version, jedoch mit höherer Effizienz. Im Vergleich zu Standardmodellen gleicher Größe ist es ressourcenschonender."}, "flux-kontext/dev": {"description": "Frontier Bildbearbeitungsmodell."}, "flux-merged": {"description": "Das FLUX.1-merged Modell kombiniert die tiefgehenden Eigenschaften, die in der Entwicklungsphase von „DEV“ erforscht wurden, mit der hohen Ausführungsgeschwindigkeit von „Schnell“. Dadurch werden sowohl die Leistungsgrenzen des Modells erweitert als auch dessen Anwendungsbereich vergrößert."}, "flux-pro/kontext": {"description": "FLUX.1 Kontext [pro] kann Text und Referenzbilder als Eingabe verarbeiten und ermöglicht nahtlose zielgerichtete lokale Bearbeitungen sowie komplexe umfassende Szenenveränderungen."}, "flux-schnell": {"description": "FLUX.1 [schnell] ist das derzeit fortschrittlichste Open-Source-Modell mit wenigen Schritten, das nicht nur Konkurrenten übertrifft, sondern auch leistungsstärkere nicht-feinabgestimmte Modelle wie Midjourney v6.0 und DALL·E 3 (HD) übertrifft. Das Modell wurde speziell feinabgestimmt, um die gesamte Vielfalt der Vortrainingsausgaben zu bewahren. Im Vergleich zu den aktuell besten Modellen auf dem Markt bietet FLUX.1 [schnell] erhebliche Verbesserungen in visueller Qualität, Instruktionsbefolgung, Größen- und Proportionsänderungen, Schriftartenverarbeitung und Ausgabediversität, was den Nutzern eine reichhaltigere und vielfältigere kreative Bildgenerierung ermöglicht."}, "flux.1-schnell": {"description": "Ein Rectified Flow Transformer mit 12 Milliarden Parametern, der Bilder basierend auf Textbeschreibungen generieren kann."}, "flux/schnell": {"description": "FLUX.1 [schnell] ist ein Streaming-Transformator-Modell mit 12 Milliarden Parametern, das in 1 bis 4 Schritten hochwertige Bilder aus Text generiert und sich für private und kommerzielle Nutzung eignet."}, "gemini-1.0-pro-001": {"description": "Gemini 1.0 Pro 001 (Tuning) bietet stabile und anpassbare Leistung und ist die ideale Wahl für Lösungen komplexer Aufgaben."}, "gemini-1.0-pro-002": {"description": "Gemini 1.0 Pro 002 (Tuning) bietet hervorragende multimodale Unterstützung und konzentriert sich auf die effektive Lösung komplexer Aufgaben."}, "gemini-1.0-pro-latest": {"description": "Gemini 1.0 Pro ist Googles leistungsstarkes KI-Modell, das für die Skalierung einer Vielzahl von Aufgaben konzipiert ist."}, "gemini-1.5-flash-001": {"description": "Gemini 1.5 Flash 001 ist ein effizientes multimodales Modell, das eine breite Anwendbarkeit unterstützt."}, "gemini-1.5-flash-002": {"description": "Gemini 1.5 Flash 002 ist ein effizientes multimodales Modell, das eine breite Palette von <PERSON>n unterstützt."}, "gemini-1.5-flash-8b": {"description": "Gemini 1.5 Flash 8B ist ein leistungsstarkes multimodales Modell, das eine breite Palette von <PERSON>n unterstützt."}, "gemini-1.5-flash-8b-exp-0924": {"description": "Gemini 1.5 Flash 8B 0924 ist das neueste experimentelle Modell, das in Text- und multimodalen Anwendungsfällen erhebliche Leistungsverbesserungen aufweist."}, "gemini-1.5-flash-8b-latest": {"description": "Gemini 1.5 Flash 8B ist ein effizientes multimodales Modell, das eine breite Palette von <PERSON>n unterstützt."}, "gemini-1.5-flash-exp-0827": {"description": "Gemini 1.5 Flash 0827 bietet optimierte multimodale Verarbeitungskapazitäten, die für verschiedene komplexe Aufgaben geeignet sind."}, "gemini-1.5-flash-latest": {"description": "Gemini 1.5 Flash ist Googles neuestes multimodales KI-Modell, das über schnelle Verarbeitungsfähigkeiten verfügt und Text-, Bild- und Videoeingaben unterstützt, um eine effiziente Skalierung für verschiedene Aufgaben zu ermöglichen."}, "gemini-1.5-pro-001": {"description": "Gemini 1.5 Pro 001 ist eine skalierbare multimodale KI-Lösung, die eine breite Palette komplexer Aufgaben unterstützt."}, "gemini-1.5-pro-002": {"description": "Gemini 1.5 Pro 002 ist das neueste produktionsbereite Modell, das eine höhere Ausgabequalität bietet, insbesondere bei mathematischen, langen Kontexten und visuellen Aufgaben erhebliche Verbesserungen aufweist."}, "gemini-1.5-pro-exp-0801": {"description": "Gemini 1.5 Pro 0801 bietet herausragende multimodale Verarbeitungskapazitäten und bringt größere Flexibilität in die Anwendungsentwicklung."}, "gemini-1.5-pro-exp-0827": {"description": "Gemini 1.5 Pro 0827 kombiniert die neuesten Optimierungstechnologien, um eine effizientere multimodale Datenverarbeitung zu ermöglichen."}, "gemini-1.5-pro-latest": {"description": "Gemini 1.5 Pro unterstützt bis zu 2 Millionen Tokens und ist die ideale Wahl für mittelgroße multimodale Modelle, die umfassende Unterstützung für komplexe Aufgaben bieten."}, "gemini-2.0-flash": {"description": "Gemini 2.0 Flash bietet nächste Generation Funktionen und Verbesserungen, einsch<PERSON>ßlich außergewöhnlicher Geschwindigkeit, nativer Werkzeugnutzung, multimodaler Generierung und einem Kontextfenster von 1M Tokens."}, "gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash bietet nächste Generation Funktionen und Verbesserungen, einsch<PERSON>ßlich außergewöhnlicher Geschwindigkeit, nativer Werkzeugnutzung, multimodaler Generierung und einem Kontextfenster von 1M Tokens."}, "gemini-2.0-flash-exp": {"description": "Gemini 2.0 Flash-Modellvariante, die auf Kosteneffizienz und niedrige Latenz optimiert ist."}, "gemini-2.0-flash-exp-image-generation": {"description": "Gemini 2.0 Flash Experimentmodell, das die Bildgenerierung unterstützt"}, "gemini-2.0-flash-lite": {"description": "Gemini 2.0 Flash ist eine Modellvariante, die auf Kosteneffizienz und niedrige Latenz optimiert ist."}, "gemini-2.0-flash-lite-001": {"description": "Gemini 2.0 Flash ist eine Modellvariante, die auf Kosteneffizienz und niedrige Latenz optimiert ist."}, "gemini-2.0-flash-preview-image-generation": {"description": "Gemini 2.0 Flash Vorschau-Modell, unterstützt die Bildgenerierung"}, "gemini-2.5-flash": {"description": "Gemini 2.5 Flash ist Googles kosteneffizientestes Modell und bietet umfassende Funktionen."}, "gemini-2.5-flash-lite": {"description": "Gemini 2.5 Flash-Lite ist Googles kleinstes und kosteneffizientestes Modell, das speziell für den großflächigen Einsatz entwickelt wurde."}, "gemini-2.5-flash-lite-preview-06-17": {"description": "Gemini 2.5 Flash-Lite Preview ist Googles kleinstes und kosteneffizientestes Modell, speziell für den großflächigen Einsatz konzipiert."}, "gemini-2.5-flash-preview-04-17": {"description": "Gemini 2.5 Flash Preview ist das kosteneffizienteste Modell von Google und bietet umfassende Funktionen."}, "gemini-2.5-flash-preview-05-20": {"description": "Gemini 2.5 Flash Preview ist Googles kosteneffizientestes Modell mit umfassenden Funktionen."}, "gemini-2.5-pro": {"description": "Gemini 2.5 Pro ist Googles fortschrittlichstes Denkmodell, das komplexe Probleme in den Bereichen Code, Mathematik und MINT-Fächer lösen kann und große Datensätze, Codebasen und Dokumente mit langem Kontext analysiert."}, "gemini-2.5-pro-preview-03-25": {"description": "Gemini 2.5 Pro Preview ist Googles fortschrittlichstes Denkmodell, das in der Lage ist, komplexe Probleme in den Bereichen Code, Mathematik und STEM zu analysieren sowie große Datensätze, Codebasen und Dokumente mithilfe von langen Kontextanalysen zu verarbeiten."}, "gemini-2.5-pro-preview-05-06": {"description": "Gemini 2.5 Pro Preview ist Googles fortschrittlichstes Denkmodell, das in der Lage ist, komplexe Probleme in den Bereichen Code, Mathematik und STEM zu analysieren und große Datensätze, Codebasen und Dokumente mithilfe von Langzeitkontext zu analysieren."}, "gemini-2.5-pro-preview-06-05": {"description": "Gemini 2.5 Pro Preview ist Googles fortschrittlichstes Denkmodell, das komplexe Probleme in den Bereichen Code, Mathematik und MINT-Fächer lösen kann und große Datensätze, Codebasen und Dokumente mit langem Kontext analysiert."}, "gemma-7b-it": {"description": "Gemma 7B eignet sich für die Verarbeitung von mittelgroßen Aufgaben und bietet ein gutes Kosten-Nutzen-Verhältnis."}, "gemma2": {"description": "Gemma 2 ist ein effizientes Modell von Google, das eine Vielzahl von Anwendungsszenarien von kleinen Anwendungen bis hin zu komplexen Datenverarbeitungen abdeckt."}, "gemma2-9b-it": {"description": "Gemma 2 9B ist ein Modell, das für spezifische Aufgaben und die Integration von Werkzeugen optimiert wurde."}, "gemma2:27b": {"description": "Gemma 2 ist ein effizientes Modell von Google, das eine Vielzahl von Anwendungsszenarien von kleinen Anwendungen bis hin zu komplexen Datenverarbeitungen abdeckt."}, "gemma2:2b": {"description": "Gemma 2 ist ein effizientes Modell von Google, das eine Vielzahl von Anwendungsszenarien von kleinen Anwendungen bis hin zu komplexen Datenverarbeitungen abdeckt."}, "generalv3": {"description": "Spark Pro ist ein hochleistungsfähiges großes Sprachmodell, das für professionelle Bereiche optimiert ist und sich auf Mathematik, Programmierung, Medizin, Bildung und andere Bereiche konzentriert, und unterstützt die Online-Suche sowie integrierte Plugins für Wetter, Datum usw. Das optimierte Modell zeigt hervorragende Leistungen und hohe Effizienz in komplexen Wissensabfragen, Sprachverständnis und hochrangiger Textgenerierung und ist die ideale Wahl für professionelle Anwendungsszenarien."}, "generalv3.5": {"description": "Spark3.5 Max ist die umfassendste Version, die Online-Suche und zahlreiche integrierte Plugins unterstützt. Ihre umfassend optimierten Kernfähigkeiten sowie die Systemrolleneinstellungen und Funktionsaufrufmöglichkeiten ermöglichen eine außergewöhnliche Leistung in verschiedenen komplexen Anwendungsszenarien."}, "glm-4": {"description": "GLM-4 ist die alte Flaggschiffversion, die im Januar 2024 veröffentlicht wurde und mittlerweile durch das leistungsstärkere GLM-4-0520 ersetzt wurde."}, "glm-4-0520": {"description": "GLM-4-0520 ist die neueste Modellversion, die für hochkomplexe und vielfältige Aufgaben konzipiert wurde und hervorragende Leistungen zeigt."}, "glm-4-9b-chat": {"description": "GLM-4-9B-<PERSON><PERSON> zeigt in den Bereichen Semantik, Mathematik, Schlussfolgerungen, Code und Wissen eine hohe Leistung. Es verfügt auch über Funktionen wie Web-Browsing, Code-Ausführung, benutzerdefinierte Toolaufrufe und langes Textverständnis. Es unterstützt 26 Sprachen, darunter <PERSON>isch, Koreanisch und Deutsch."}, "glm-4-air": {"description": "GLM-4-Air ist eine kosteneffiziente Version, die in der Leistung nahe am GLM-4 liegt und schnelle Geschwindigkeiten zu einem erschwinglichen Preis bietet."}, "glm-4-air-250414": {"description": "GLM-4-Air ist die kosteneffiziente Version, deren Leistung nahe an der von GLM-4 liegt und schnelle Geschwindigkeiten zu einem erschwinglichen Preis bietet."}, "glm-4-airx": {"description": "GLM-4-AirX bietet eine effiziente Version von GLM-4-Air mit einer Inferenzgeschwindigkeit von bis zu 2,6-fach."}, "glm-4-alltools": {"description": "GLM-4-AllTools ist ein multifunktionales Agentenmodell, das optimiert wurde, um komplexe Anweisungsplanung und Werkzeugaufrufe zu unterstützen, wie z. B. Web-Browsing, Code-Interpretation und Textgenerierung, geeignet für die Ausführung mehrerer Aufgaben."}, "glm-4-flash": {"description": "GLM-4-Flash ist die ideale Wahl für die Verarbeitung einfacher Aufgaben, mit der schnellsten Geschwindigkeit und dem besten Preis."}, "glm-4-flash-250414": {"description": "GLM-4-<PERSON> ist die ideale Wahl für die Bearbeitung einfacher Aufgaben, mit der schnellsten Geschwindigkeit und kostenlos."}, "glm-4-flashx": {"description": "GLM-4-FlashX ist eine verbesserte Version von Flash mit extrem schneller Inferenzgeschwindigkeit."}, "glm-4-long": {"description": "GLM-4-<PERSON> unterstützt extrem lange Texteingaben und eignet sich für Gedächtnisaufgaben und die Verarbeitung großer Dokumente."}, "glm-4-plus": {"description": "GLM-4-Plus ist das hochintelligente Flaggschiffmodell mit starken Fähigkeiten zur Verarbeitung langer Texte und komplexer Aufgaben, mit umfassenden Leistungsverbesserungen."}, "glm-4.1v-thinking-flash": {"description": "Die GLM-4.1V-Thinking-Serie ist das leistungsstärkste visuelle Modell unter den bekannten 10-Milliarden-Parameter-VLMs und integriert SOTA-Leistungen auf diesem Niveau in verschiedenen visuellen Sprachaufgaben, <PERSON><PERSON><PERSON>, Bildfragen, Fachaufgaben, OCR-Texterkennung, Dokumenten- und Diagramminterpretation, GUI-Agenten, Frontend-Web-Coding und Grounding. In vielen Aufgaben übertrifft es sogar das Qwen2.5-VL-72B mit achtmal so vielen Parametern. Durch fortschrittliche Verstärkungslernverfahren beherrscht das Modell die Chain-of-Thought-Schlussfolgerung, was die Genauigkeit und Detailtiefe der Antworten deutlich verbessert und in Bezug auf Endergebnis und Erklärbarkeit traditionelle Nicht-Thinking-Modelle übertrifft."}, "glm-4.1v-thinking-flashx": {"description": "Die GLM-4.1V-Thinking-Serie ist das leistungsstärkste visuelle Modell unter den bekannten 10-Milliarden-Parameter-VLMs und integriert SOTA-Leistungen auf diesem Niveau in verschiedenen visuellen Sprachaufgaben, <PERSON><PERSON><PERSON>, Bildfragen, Fachaufgaben, OCR-Texterkennung, Dokumenten- und Diagramminterpretation, GUI-Agenten, Frontend-Web-Coding und Grounding. In vielen Aufgaben übertrifft es sogar das Qwen2.5-VL-72B mit achtmal so vielen Parametern. Durch fortschrittliche Verstärkungslernverfahren beherrscht das Modell die Chain-of-Thought-Schlussfolgerung, was die Genauigkeit und Detailtiefe der Antworten deutlich verbessert und in Bezug auf Endergebnis und Erklärbarkeit traditionelle Nicht-Thinking-Modelle übertrifft."}, "glm-4.5": {"description": "Das neueste Flaggschiff-<PERSON><PERSON>, unterstützt den Denkmoduswechsel und erreicht eine umfassende Leistungsfähigkeit auf SOTA-Niveau für Open-Source-Modelle mit einer Kontextlänge von bis zu 128K."}, "glm-4.5-air": {"description": "Die leichtgewichtige Version von GLM-4.5, die Leistung und Kosten-Nutzen-Verhältnis ausbalanciert und flexibel zwischen hybriden Denkmodellen wechseln kann."}, "glm-4.5-airx": {"description": "Die Turbo-Version von GLM-4.5-Air mit schnellerer Reaktionszeit, speziell für großskalige und hochgeschwindigkeitsbedürftige Anwendungen entwickelt."}, "glm-4.5-flash": {"description": "Die kostenlose Version von GLM-4.5, die bei Inferenz, Programmierung und Agentenaufgaben hervorragende Leistungen zeigt."}, "glm-4.5-x": {"description": "Die Turbo-Version von GLM-4.5, die bei starker Leistung eine Generierungsgeschwindigkeit von bis zu 100 Tokens pro Sekunde erreicht."}, "glm-4v": {"description": "GLM-4V bietet starke Fähigkeiten zur Bildverständnis und -schlussfolgerung und unterstützt eine Vielzahl visueller Aufgaben."}, "glm-4v-flash": {"description": "GLM-4V-Flash konzen<PERSON>ert sich auf die effiziente Verarbeitung einzelner Bilder und eignet sich für Szenarien der schnellen Bildanalyse, wie z. B. die Echtzeitanalyse von Bildern oder die Verarbeitung von Bilddaten in großen Mengen."}, "glm-4v-plus": {"description": "GLM-4V-Plus hat die Fähigkeit, Videoinhalte und mehrere Bilder zu verstehen und eignet sich für multimodale Aufgaben."}, "glm-4v-plus-0111": {"description": "GLM-4V-Plus verfügt über die Fähigkeit, Videoinhalte und mehrere Bilder zu verstehen und eignet sich für multimodale Aufgaben."}, "glm-z1-air": {"description": "Schlussfolgerungsmodell: Verfügt über starke Schlussfolgerungsfähigkeiten und eignet sich für Aufgaben, die tiefes Denken erfordern."}, "glm-z1-airx": {"description": "Blitzschlussfolgerung: Bietet extrem schnelle Schlussfolgerungsgeschwindigkeit und starke Schlussfolgerungseffekte."}, "glm-z1-flash": {"description": "Die GLM-Z1-Serie verfügt über starke Fähigkeiten im komplexen logischen Denken und zeigt hervorragende Leistungen in Logik, Mathematik und Programmierung."}, "glm-z1-flashx": {"description": "Hohe Geschwindigkeit zu niedrigem Preis: Flash-verbesserte Version mit ultraschneller Inferenzgeschwindigkeit und schnellerer gleichzeitiger Verarbeitung."}, "glm-zero-preview": {"description": "GLM-Zero-Preview verfügt über starke Fähigkeiten zur komplexen Schlussfolgerung und zeigt hervorragende Leistungen in den Bereichen logisches Denken, Mathematik und Programmierung."}, "google/gemini-2.0-flash-001": {"description": "Gemini 2.0 Flash bietet nächste Generation Funktionen und Verbesserungen, einsch<PERSON>ßlich außergewöhnlicher Geschwindigkeit, nativer Werkzeugnutzung, multimodaler Generierung und einem Kontextfenster von 1M Tokens."}, "google/gemini-2.0-flash-exp:free": {"description": "Gemini 2.0 Flash Experimental ist Googles neuestes experimentelles multimodales KI-Modell, das im Vergleich zu früheren Versionen eine gewisse Qualitätsverbesserung aufweist, insbesondere in Bezug auf Weltwissen, Code und langen Kontext."}, "google/gemini-2.5-flash": {"description": "Gemini 2.5 Flash ist Googles fortschrittl<PERSON><PERSON><PERSON>uptmodell, speziell entwickelt für anspruchsvolle Aufgaben in den Bereichen logisches Denken, Programmierung, Mathematik und Wissenschaft. Es verfügt über eingebaute \"Denkfähigkeiten\", die es ermöglichen, Antworten mit höherer Genauigkeit und detaillierter Kontextverarbeitung zu liefern.\n\nHinweis: Dieses Modell gibt es in zwei Varianten: mit und ohne Denkfähigkeit. Die Preisgestaltung für die Ausgabe variiert erheblich, je nachdem, ob die Denkfähigkeit aktiviert ist. Wenn Sie die Standardvariante (ohne den Suffix \":thinking\") wähl<PERSON>, vermeidet das Modell ausdrücklich die Erzeugung von Denk-Token.\n\nUm die Denkfähigkeit zu nutzen und Denk-Token zu erhalten, müssen Sie die \":thinking\"-<PERSON><PERSON><PERSON> w<PERSON>hl<PERSON>, was zu höheren Kosten für die Denk-Ausgabe führt.\n\nDarüber hinaus kann Gemini 2.5 Flash über den Parameter \"Maximale Tokenanzahl für das Denken\" konfiguriert werden, wie in der Dokumentation beschrieben (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview": {"description": "Gemini 2.5 Flash ist Googles fortschrittlich<PERSON><PERSON> Hauptmodell, das für fortgeschrittenes Denken, Codierung, Mathematik und wissenschaftliche Aufgaben entwickelt wurde. Es enthält die eingebaute Fähigkeit zu \"denken\", was es ihm erm<PERSON><PERSON><PERSON>, Antworten mit höherer Genauigkeit und detaillierter Kontextverarbeitung zu liefern.\n\nHinweis: Dieses Modell hat zwei Varianten: Denken und Nicht-Denken. Die Ausgabepreise variieren erheblich, je nachdem, ob die Denkfähigkeit aktiviert ist oder nicht. Wenn Sie die Standardvariante (ohne den Suffix \":thinking\") w<PERSON>hl<PERSON>, wird das Modell ausdrücklich vermeiden, Denk-Tokens zu generieren.\n\nUm die Denkfähigkeit zu nutzen und Denk-Tokens zu erhalten, müssen Sie die \":thinking\"-<PERSON><PERSON><PERSON> wählen, was zu höheren Preisen für Denk-Ausgaben führt.\n\nDarüber hinaus kann Gemini 2.5 Flash über den Parameter \"maximale Tokenanzahl für das Denken\" konfiguriert werden, wie in der Dokumentation beschrieben (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-flash-preview:thinking": {"description": "Gemini 2.5 Flash ist Googles fortschrittlich<PERSON><PERSON> Hauptmodell, das für fortgeschrittenes Denken, Codierung, Mathematik und wissenschaftliche Aufgaben entwickelt wurde. Es enthält die eingebaute Fähigkeit zu \"denken\", was es ihm erm<PERSON><PERSON><PERSON>, Antworten mit höherer Genauigkeit und detaillierter Kontextverarbeitung zu liefern.\n\nHinweis: Dieses Modell hat zwei Varianten: Denken und Nicht-Denken. Die Ausgabepreise variieren erheblich, je nachdem, ob die Denkfähigkeit aktiviert ist oder nicht. Wenn Sie die Standardvariante (ohne den Suffix \":thinking\") w<PERSON>hl<PERSON>, wird das Modell ausdrücklich vermeiden, Denk-Tokens zu generieren.\n\nUm die Denkfähigkeit zu nutzen und Denk-Tokens zu erhalten, müssen Sie die \":thinking\"-<PERSON><PERSON><PERSON> wählen, was zu höheren Preisen für Denk-Ausgaben führt.\n\nDarüber hinaus kann Gemini 2.5 Flash über den Parameter \"maximale Tokenanzahl für das Denken\" konfiguriert werden, wie in der Dokumentation beschrieben (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning)."}, "google/gemini-2.5-pro": {"description": "Gemini 2.5 Pro ist Googles fortschrittlichstes Denkmodell, das in der Lage ist, komplexe Probleme in den Bereichen Code, Mathematik und MINT-Fächer zu analysieren sowie große Datensätze, Codebasen und Dokumente mit langem Kontext zu untersuchen."}, "google/gemini-2.5-pro-preview": {"description": "Gemini 2.5 Pro Preview ist Googles fortschrittlichstes Denkmodell, das in der Lage ist, komplexe Probleme in den Bereichen Code, Mathematik und MINT zu analysieren sowie große Datensätze, Codebasen und Dokumente mit langem Kontext zu untersuchen."}, "google/gemini-flash-1.5": {"description": "Gemini 1.5 Flash bietet optimierte multimodale Verarbeitungsfähigkeiten, die für verschiedene komplexe Aufgabenszenarien geeignet sind."}, "google/gemini-pro-1.5": {"description": "Gemini 1.5 Pro kombiniert die neuesten Optimierungstechnologien und bietet eine effizientere Verarbeitung multimodaler Daten."}, "google/gemma-2-27b": {"description": "Gemma 2 ist ein effizientes Modell von Google, das eine Vielzahl von Anwendungsszenarien von kleinen Anwendungen bis hin zu komplexer Datenverarbeitung abdeckt."}, "google/gemma-2-27b-it": {"description": "Gemma 2 setzt das Designkonzept von Leichtbau und Effizienz fort."}, "google/gemma-2-2b-it": {"description": "Das leichtgewichtige Anweisungsoptimierungsmodell von Google."}, "google/gemma-2-9b": {"description": "Gemma 2 ist ein effizientes Modell von Google, das eine Vielzahl von Anwendungsszenarien von kleinen Anwendungen bis hin zu komplexer Datenverarbeitung abdeckt."}, "google/gemma-2-9b-it": {"description": "Gemma 2 ist eine leichtgewichtige Open-Source-Textmodellreihe von Google."}, "google/gemma-2-9b-it:free": {"description": "Gemma 2 ist eine leichtgewichtige Open-Source-Textmodellreihe von Google."}, "google/gemma-2b-it": {"description": "Gemma Instruct (2B) bietet grundlegende Anweisungsverarbeitungsfähigkeiten und eignet sich für leichte Anwendungen."}, "google/gemma-3-1b-it": {"description": "Gemma 3 1B ist ein Open-Source-<PERSON><PERSON><PERSON><PERSON><PERSON> von Google, das neue Maßstäbe in Effizienz und Leistung setzt."}, "google/gemma-3-27b-it": {"description": "Gemma 3 27B ist ein Open-Source-<PERSON><PERSON><PERSON><PERSON><PERSON> von Google, das neue Maßstäbe in Bezug auf Effizienz und Leistung setzt."}, "gpt-3.5-turbo": {"description": "GPT 3.5 Turbo eignet sich für eine Vielzahl von Textgenerierungs- und Verständnisaufgaben. Derzeit verweist es auf gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-0125": {"description": "GPT 3.5 Turbo eignet sich für eine Vielzahl von Textgenerierungs- und Verständnisaufgaben. Derzeit verweist es auf gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-1106": {"description": "GPT 3.5 Turbo eignet sich für eine Vielzahl von Textgenerierungs- und Verständnisaufgaben. Derzeit verweist es auf gpt-3.5-turbo-0125."}, "gpt-3.5-turbo-instruct": {"description": "GPT 3.5 Turbo eignet sich für eine Vielzahl von Textgenerierungs- und Verständnisaufgaben. Derzeit verweist es auf gpt-3.5-turbo-0125."}, "gpt-35-turbo": {"description": "GPT 3.5 Turbo ist ein effizientes Modell von OpenAI, das für Chat- und Textgenerierungsaufgaben geeignet ist und parallele Funktionsaufrufe unterstützt."}, "gpt-35-turbo-16k": {"description": "GPT 3.5 Turbo 16k ist ein hochkapazitives Textgenerierungsmodell, das sich für komplexe Aufgaben eignet."}, "gpt-4": {"description": "GPT-4 bietet ein größeres Kontextfenster, das in der Lage ist, längere Texteingaben zu verarbeiten, und eignet sich für Szenarien, die eine umfassende Informationsintegration und Datenanalyse erfordern."}, "gpt-4-0125-preview": {"description": "Das neueste GPT-4 Turbo-Modell verfügt über visuelle Funktionen. Jetzt können visuelle Anfragen im JSON-Format und durch Funktionsaufrufe gestellt werden. GPT-4 Turbo ist eine verbesserte Version, die kosteneffiziente Unterstützung für multimodale Aufgaben bietet. Es findet ein Gleichgewicht zwischen Genauigkeit und Effizienz und eignet sich für Anwendungen, die Echtzeitanpassungen erfordern."}, "gpt-4-0613": {"description": "GPT-4 bietet ein größeres Kontextfenster, das in der Lage ist, längere Texteingaben zu verarbeiten, und eignet sich für Szenarien, die eine umfassende Informationsintegration und Datenanalyse erfordern."}, "gpt-4-1106-preview": {"description": "Das neueste GPT-4 Turbo-Modell verfügt über visuelle Funktionen. Jetzt können visuelle Anfragen im JSON-Format und durch Funktionsaufrufe gestellt werden. GPT-4 Turbo ist eine verbesserte Version, die kosteneffiziente Unterstützung für multimodale Aufgaben bietet. Es findet ein Gleichgewicht zwischen Genauigkeit und Effizienz und eignet sich für Anwendungen, die Echtzeitanpassungen erfordern."}, "gpt-4-32k": {"description": "GPT-4 bietet ein größeres Kontextfenster, das in der Lage ist, längere Texteingaben zu verarbeiten, und eignet sich für Szenarien, die eine umfassende Informationsintegration und Datenanalyse erfordern."}, "gpt-4-32k-0613": {"description": "GPT-4 bietet ein größeres Kontextfenster, das in der Lage ist, längere Texteingaben zu verarbeiten, und eignet sich für Szenarien, die eine umfassende Informationsintegration und Datenanalyse erfordern."}, "gpt-4-turbo": {"description": "Das neueste GPT-4 Turbo-Modell verfügt über visuelle Funktionen. Jetzt können visuelle Anfragen im JSON-Format und durch Funktionsaufrufe gestellt werden. GPT-4 Turbo ist eine verbesserte Version, die kosteneffiziente Unterstützung für multimodale Aufgaben bietet. Es findet ein Gleichgewicht zwischen Genauigkeit und Effizienz und eignet sich für Anwendungen, die Echtzeitanpassungen erfordern."}, "gpt-4-turbo-2024-04-09": {"description": "Das neueste GPT-4 Turbo-Modell verfügt über visuelle Funktionen. Jetzt können visuelle Anfragen im JSON-Format und durch Funktionsaufrufe gestellt werden. GPT-4 Turbo ist eine verbesserte Version, die kosteneffiziente Unterstützung für multimodale Aufgaben bietet. Es findet ein Gleichgewicht zwischen Genauigkeit und Effizienz und eignet sich für Anwendungen, die Echtzeitanpassungen erfordern."}, "gpt-4-turbo-preview": {"description": "Das neueste GPT-4 Turbo-Modell verfügt über visuelle Funktionen. Jetzt können visuelle Anfragen im JSON-Format und durch Funktionsaufrufe gestellt werden. GPT-4 Turbo ist eine verbesserte Version, die kosteneffiziente Unterstützung für multimodale Aufgaben bietet. Es findet ein Gleichgewicht zwischen Genauigkeit und Effizienz und eignet sich für Anwendungen, die Echtzeitanpassungen erfordern."}, "gpt-4-vision-preview": {"description": "Das neueste GPT-4 Turbo-Modell verfügt über visuelle Funktionen. Jetzt können visuelle Anfragen im JSON-Format und durch Funktionsaufrufe gestellt werden. GPT-4 Turbo ist eine verbesserte Version, die kosteneffiziente Unterstützung für multimodale Aufgaben bietet. Es findet ein Gleichgewicht zwischen Genauigkeit und Effizienz und eignet sich für Anwendungen, die Echtzeitanpassungen erfordern."}, "gpt-4.1": {"description": "GPT-4.1 ist unser Flaggschiffmodell für komplexe Aufgaben. Es eignet sich hervorragend zur Lösung von Problemen über verschiedene Bereiche hinweg."}, "gpt-4.1-mini": {"description": "GPT-4.1 mini bietet ein Gleichgewicht zwischen Intelligenz, Geschwindigkeit und Kosten, was es zu einem attraktiven Modell für viele Anwendungsfälle macht."}, "gpt-4.1-nano": {"description": "GPT-4.1 mini bietet ein Gleichgewicht zwischen Intelligenz, Geschwindigkeit und Kosten, was es zu einem attraktiven Modell für viele Anwendungsfälle macht."}, "gpt-4.5-preview": {"description": "Die Forschungs-Vors<PERSON><PERSON> von GPT-4.5, unserem bisher größten und leistungsstärksten GPT-Modell. Es verfügt über umfangreiches Weltwissen und kann die Absichten der Benutzer besser verstehen, was es in kreativen Aufgaben und autonomer Planung herausragend macht. GPT-4.5 akzeptiert Text- und Bild-Eingaben und generiert Textausgaben (einschließlich strukturierter Ausgaben). Es unterstützt wichtige Entwicklerfunktionen wie Funktionsaufrufe, Batch-APIs und Streaming-Ausgaben. In Aufgaben, die kreatives, offenes Denken und Dialog erfordern (wie Schreiben, Lernen oder das Erkunden neuer Ideen), zeigt GPT-4.5 besonders gute Leistungen. Der Wissensstand ist bis Oktober 2023."}, "gpt-4o": {"description": "ChatGPT-4o ist ein dynamisches Modell, das in Echtzeit aktualisiert wird, um die neueste Version zu gewährleisten. Es kombiniert starke Sprachverständnis- und Generierungsfähigkeiten und eignet sich für großangelegte Anwendungsszenarien, einschließlich Kundenservice, Bildung und technische Unterstützung."}, "gpt-4o-2024-05-13": {"description": "ChatGPT-4o ist ein dynamisches Modell, das in Echtzeit aktualisiert wird, um die neueste Version zu gewährleisten. Es kombiniert starke Sprachverständnis- und Generierungsfähigkeiten und eignet sich für großangelegte Anwendungsszenarien, einschließlich Kundenservice, Bildung und technische Unterstützung."}, "gpt-4o-2024-08-06": {"description": "ChatGPT-4o ist ein dynamisches Modell, das in Echtzeit aktualisiert wird, um die neueste Version zu gewährleisten. Es kombiniert starke Sprachverständnis- und Generierungsfähigkeiten und eignet sich für großangelegte Anwendungsszenarien, einschließlich Kundenservice, Bildung und technische Unterstützung."}, "gpt-4o-2024-11-20": {"description": "ChatGPT-4o ist ein dynamisches Modell, das in Echtzeit aktualisiert wird, um die neueste Version zu gewährleisten. Es kombiniert starke Sprachverständnis- und Generierungsfähigkeiten und eignet sich für großangelegte Anwendungsbereiche, einschließlich Kundenservice, Bildung und technischen Support."}, "gpt-4o-audio-preview": {"description": "GPT-4o Audio-Modell, unterstützt Audioeingabe und -ausgabe."}, "gpt-4o-mini": {"description": "GPT-4o mini ist das neueste Modell von OpenAI, das nach GPT-4 Omni veröffentlicht wurde und sowohl Text- als auch Bildinput unterstützt. Als ihr fortschrittlichstes kleines Modell ist es viel günstiger als andere neueste Modelle und kostet über 60 % weniger als GPT-3.5 Turbo. Es behält die fortschrittliche Intelligenz bei und bietet gleichzeitig ein hervorragendes Preis-Leistungs-Verhältnis. GPT-4o mini erzielte 82 % im MMLU-Test und rangiert derzeit in den Chat-Präferenzen über GPT-4."}, "gpt-4o-mini-audio-preview": {"description": "GPT-4o mini Audio Modell, unterstützt Audioeingabe und -ausgabe."}, "gpt-4o-mini-realtime-preview": {"description": "Echtzeitversion von GPT-4o-mini, unterstützt Audio- und Texteingabe sowie -ausgabe in Echtzeit."}, "gpt-4o-mini-search-preview": {"description": "Die GPT-4o mini Suchvorschau ist ein speziell trainiertes Modell zur Interpretation und Ausführung von Websuchanfragen, das die Chat Completions API verwendet. Neben den Token-Gebühren fallen für Websuchanfragen zusätzliche Gebühren pro Tool-Aufruf an."}, "gpt-4o-mini-transcribe": {"description": "GPT-4o Mini Transcribe ist ein Sprach-zu-Text-Modell, das GPT-4o zur Transkription von Audio verwendet. Im Vergleich zum ursprünglichen Whisper-Modell verbessert es die Wortfehlerrate sowie die Spracherkennung und Genauigkeit. Verwenden Sie es für genauere Transkriptionen."}, "gpt-4o-mini-tts": {"description": "GPT-4o mini TTS ist ein Text-to-Speech-Modell, das auf GPT-4o mini basiert und hochwertige Sprachgenerierung bei niedrigeren Kosten bietet."}, "gpt-4o-realtime-preview": {"description": "Echtzeitversion von GPT-4o, unterstützt Audio- und Texteingabe sowie -ausgabe in Echtzeit."}, "gpt-4o-realtime-preview-2024-10-01": {"description": "Echtzeitversion von GPT-4o, unterstützt Audio- und Texteingabe sowie -ausgabe in Echtzeit."}, "gpt-4o-realtime-preview-2025-06-03": {"description": "Echtzeitversion von GPT-4o, unterstützt Echtzeit-Ein- und Ausgabe von Audio und Text."}, "gpt-4o-search-preview": {"description": "Die GPT-4o Suchvorschau ist ein speziell trainiertes Modell zur Interpretation und Ausführung von Websuchanfragen, das die Chat Completions API verwendet. Neben den Token-Gebühren fallen für Websuchanfragen zusätzliche Gebühren pro Tool-Aufruf an."}, "gpt-4o-transcribe": {"description": "GPT-4o Transcribe ist ein Sprach-zu-Text-Modell, das GPT-4o zur Transkription von Audio verwendet. Im Vergleich zum ursprünglichen Whisper-Modell verbessert es die Wortfehlerrate sowie die Spracherkennung und Genauigkeit. Verwenden Sie es für genauere Transkriptionen."}, "gpt-image-1": {"description": "ChatGPT natives multimodales Bildgenerierungsmodell"}, "grok-2-1212": {"description": "Dieses Modell hat Verbesserungen in Bezug auf Genauigkeit, <PERSON><PERSON><PERSON><PERSON><PERSON> von Anweisungen und Mehrsprachigkeit erfahren."}, "grok-2-image-1212": {"description": "Unser neuestes Bildgenerierungsmodell kann lebendige und realistische Bilder basierend auf Text-Prompts erzeugen. Es zeigt hervorragende Leistungen in den Bereichen Marketing, soziale Medien und Unterhaltung."}, "grok-2-vision-1212": {"description": "Dieses Modell hat Verbesserungen in Bezug auf Genauigkeit, <PERSON><PERSON><PERSON><PERSON><PERSON> von Anweisungen und Mehrsprachigkeit erfahren."}, "grok-3": {"description": "Ein Flaggschiffmodell, spezialisiert auf Datenextraktion, Programmierung und Textzusammenfassung für Unternehmensanwendungen, mit tiefgreifendem Wissen in den Bereichen Finanzen, Medizin, Recht und Wissenschaft."}, "grok-3-fast": {"description": "Ein Flaggschiffmodell, spezialisiert auf Datenextraktion, Programmierung und Textzusammenfassung für Unternehmensanwendungen, mit tiefgreifendem Wissen in den Bereichen Finanzen, Medizin, Recht und Wissenschaft."}, "grok-3-mini": {"description": "Ein leichtgewichtiges Modell, das vor der Antwort nachdenkt. Es arbeitet schnell und intelligent, eignet sich für logische Aufgaben ohne tiefgehendes Fachwissen und ermöglicht die Nachverfolgung des ursprünglichen Denkprozesses."}, "grok-3-mini-fast": {"description": "Ein leichtgewichtiges Modell, das vor der Antwort nachdenkt. Es arbeitet schnell und intelligent, eignet sich für logische Aufgaben ohne tiefgehendes Fachwissen und ermöglicht die Nachverfolgung des ursprünglichen Denkprozesses."}, "grok-4": {"description": "Unser neuestes und leistungsstärkstes Flaggschiffmodell, das in der Verarbeitung natürlicher Sprache, mathematischen Berechnungen und logischem Denken herausragende Leistungen erbringt – ein perfekter Allrounder."}, "gryphe/mythomax-l2-13b": {"description": "MythoMax l2 13B ist ein Sprachmodell, das Kreativität und Intelligenz kombiniert und mehrere führende Modelle integriert."}, "hunyuan-a13b": {"description": "Hunyuan ist das erste hybride Schlussfolgerungsmodell, eine Weiterentwicklung von hunyuan-standard-256K mit insgesamt 80 Milliarden Parametern und 13 Milliarden aktivierten Parametern. Standardmäßig im langsamen Denkmodus, unterstützt es den Wechsel zwischen schnellem und langsamem Denkmodus über Parameter oder Anweisungen, wobei der Wechsel durch Voranstellen von query mit / no_think erfolgt. Die Gesamtleistung wurde gegenüber der Vorgängergeneration deutlich verbessert, insbesondere in Mathematik, Naturwissenschaften, Langtextverständnis und Agentenfähigkeiten."}, "hunyuan-code": {"description": "Das neueste Code-Generierung<PERSON><PERSON> von <PERSON>, das auf einem Basismodell mit 200B hochwertigen Code-Daten trainiert wurde, hat ein halbes Jahr lang mit hochwertigen SFT-Daten trainiert, das Kontextfenster auf 8K erhöht und belegt in den automatischen Bewertungsmetriken für die fünf großen Programmiersprachen Spitzenplätze; in den zehn Aspekten der umfassenden Codeaufgabenbewertung für die fünf großen Sprachen liegt die Leistung in der ersten Reihe."}, "hunyuan-functioncall": {"description": "Das neueste MOE-Architektur-FunctionCall-<PERSON><PERSON> von <PERSON>, das mit hochwertigen FunctionCall-Daten trainiert wurde, hat ein Kontextfenster von 32K und führt in mehreren Bewertungsmetriken."}, "hunyuan-large": {"description": "Das Hunyuan-large Modell hat insgesamt etwa 389B Parameter, davon etwa 52B aktivierte Parameter, und ist das derzeit größte und leistungsstärkste Open-Source MoE-Modell mit Transformer-Architektur in der Branche."}, "hunyuan-large-longcontext": {"description": "Besonders gut geeignet für lange Textaufgaben wie Dokumentenzusammenfassungen und Dokumentenfragen, verfügt es auch über die Fähigkeit, allgemeine Textgenerierungsaufgaben zu bearbeiten. Es zeigt hervorragende Leistungen bei der Analyse und Generierung von langen Texten und kann effektiv mit komplexen und detaillierten Anforderungen an die Verarbeitung von langen Inhalten umgehen."}, "hunyuan-large-vision": {"description": "Dieses Modell eignet sich für Szenarien mit Bild- und Textverständnis. Es basiert auf dem Hunyuan Large-Modell und ist ein großes visuelles Sprachmodell, das beliebige Auflösungen und mehrere Bilder plus Texteingaben unterstützt und Textinhalte generiert. Der Fokus liegt auf Aufgaben im Bereich Bild-Text-Verständnis mit deutlichen Verbesserungen in mehrsprachigen Bild-Text-Verständnisfähigkeiten."}, "hunyuan-lite": {"description": "Aufgerüstet auf eine MOE-Struktur mit einem Kontextfenster von 256<PERSON>, führt es in mehreren Bewertungssets in NLP, Code, Mathematik und Industrie zahlreiche Open-Source-Modelle an."}, "hunyuan-lite-vision": {"description": "Das neueste 7B multimodale Modell von <PERSON>, mit einem Kontextfenster von 32K, unterstützt multimodale Dialoge in Chinesisch und Englisch, Objekterkennung in Bildern, Dokumenten- und Tabellenverständnis sowie multimodale Mathematik und übertrifft in mehreren Dimensionen die Bewertungskennzahlen von 7B Wettbewerbsmodellen."}, "hunyuan-pro": {"description": "Ein MOE-32K-Modell für lange Texte mit einer Billion Parametern. Es erreicht in verschiedenen Benchmarks ein absolut führendes Niveau, hat komplexe Anweisungen und Schlussfolgerungen, verfügt über komplexe mathematische Fähigkeiten und unterstützt Funktionsaufrufe, mit Schwerpunkt auf Optimierung in den Bereichen mehrsprachige Übersetzung, Finanzrecht und Medizin."}, "hunyuan-role": {"description": "Das neueste Rollenspielmodell von <PERSON>, das auf dem offiziellen feinabgestimmten Training von <PERSON> basiert, wurde mit einem Datensatz für Rollenspiel-Szenarien weiter trainiert und bietet in Rollenspiel-Szenarien bessere Grundeffekte."}, "hunyuan-standard": {"description": "Verwendet eine verbesserte Routing-Strategie und mildert gleichzeitig die Probleme der Lastenverteilung und Expertenkonvergenz. Bei langen Texten erreicht der Needle-in-a-Haystack-Indikator 99,9%. MOE-32K bietet ein besseres Preis-Leistungs-Verhältnis und ermöglicht die Verarbeitung von langen Texteingaben bei ausgewogenem Effekt und Preis."}, "hunyuan-standard-256K": {"description": "Verwendet eine verbesserte Routing-Strategie und mildert gleichzeitig die Probleme der Lastenverteilung und Expertenkonvergenz. Bei langen Texten erreicht der Needle-in-a-Haystack-Indikator 99,9%. MOE-256K bricht in Länge und Effektivität weiter durch und erweitert die eingabefähige Länge erheblich."}, "hunyuan-standard-vision": {"description": "Das neueste multimodale Modell <PERSON>, das mehrsprachige Antworten unterstützt und sowohl in Chinesisch als auch in Englisch ausgewogen ist."}, "hunyuan-t1-********": {"description": "Umfassende Entwicklung der Modellfähigkeiten in Geistes- und Naturwissenschaften, starke Fähigkeit zur Erfassung langer Textinformationen. Unterstützt die Lösung von wissenschaftlichen Problemen in verschiedenen Schwierigkeitsgraden, einschließlich Mathematik, logischem Denken, Wissenschaft und Code."}, "hunyuan-t1-********": {"description": "Verbesserung der Codegenerierungsfähigkeiten auf Projektebene; Steigerung der Qualität von Textgenerierung und Schreibstil; Verbesserung des Verständnisses von Themen in mehrstufigen Dialogen, Befehlsbefolgung und Wortverständnis; Optimierung von Ausgaben mit gemischten traditionellen und vereinfachten chinesischen Schriftzeichen sowie gemischten chinesisch-englischen Texten."}, "hunyuan-t1-20250529": {"description": "Optimiert für Textkreation und Aufsatzschreiben, verbessert die Fähigkeiten in Frontend-Programmierung, Mathematik und logischem Denken sowie die Befolgung von Anweisungen."}, "hunyuan-t1-20250711": {"description": "Erhebliche Verbesserungen bei anspruchsvoller Mathematik, Logik und Programmierfähigkeiten, Optimierung der Modellstabilität und Steigerung der Leistungsfähigkeit bei langen Texten."}, "hunyuan-t1-latest": {"description": "Das erste ultra-skalierbare Hybrid-Transformer-Mamba-Inferenzmodell der Branche, das die Inferenzfähigkeiten erweitert, eine extrem hohe Dekodierungsgeschwindigkeit bietet und weiter auf menschliche Präferenzen abgestimmt ist."}, "hunyuan-t1-vision": {"description": "Hunyuan ist ein multimodales Verständnis- und Tiefdenkmodell, das native multimodale lange Denkprozesse unterstützt. Es ist spezialisiert auf verschiedene Bildinferenzszenarien und zeigt im Vergleich zu Schnelldenkmodellen umfassende Verbesserungen bei naturwissenschaftlichen Problemen."}, "hunyuan-t1-vision-20250619": {"description": "Die neueste Version des hunyuan t1-vision multimodalen tiefen Denkmodells unterstützt native multimodale Chain-of-Thought-Mechanismen und bietet im Vergleich zur vorherigen Standardversion umfassende Verbesserungen."}, "hunyuan-turbo": {"description": "Die Vorschauversion des neuen großen Sprachmodells von Hunyuan verwendet eine neuartige hybride Expertenmodellstruktur (MoE) und bietet im Vergleich zu Hunyuan-Pro eine schnellere Inferenz und bessere Leistung."}, "hunyuan-turbo-20241223": {"description": "Diese Version optimiert: Datenanweisungs-Skalierung, erhebliche Verbesserung der allgemeinen Generalisierungsfähigkeit des Modells; erhebliche Verbesserung der mathematischen, programmierbaren und logischen Denkfähigkeiten; Optimierung der Fähigkeiten im Textverständnis und der Wortverständnisfähigkeiten; Optimierung der Qualität der Inhaltserzeugung in der Texterstellung."}, "hunyuan-turbo-latest": {"description": "Allgemeine Optimierung der Benutzererfahrung, einschließlich NLP-Verständnis, Texterstellung, Smalltalk, Wissensfragen, Übersetzung, Fachgebieten usw.; Verbesserung der Menschlichkeit, Optimierung der emotionalen Intelligenz des Modells; Verbesserung der Fähigkeit des Modells, bei unklaren Absichten aktiv Klarheit zu schaffen; Verbesserung der Bearbeitungsfähigkeit von Fragen zur Wort- und Satzanalyse; Verbesserung der Qualität und Interaktivität der Kreation; Verbesserung der Mehrfachinteraktionserfahrung."}, "hunyuan-turbo-vision": {"description": "Das neue Flaggschiff-Modell der visuellen Sprache von Hu<PERSON>uan, das eine brandneue Struktur des gemischten Expertenmodells (MoE) verwendet, bietet umfassende Verbesserungen in den Fähigkeiten zur grundlegenden Erkennung, Inhaltserstellung, Wissensfragen und Analyse sowie Schlussfolgerungen im Vergleich zum vorherigen Modell."}, "hunyuan-turbos-20250313": {"description": "Vereinheitlichung des Stils bei mathematischen Lösungswegen und Verstärkung der mehrstufigen mathematischen Frage-Antwort-Interaktion. Optimierung des Antwortstils bei Textkreationen, Entfernung von KI-typischen Merkmalen und Steigerung der literarischen Ausdruckskraft."}, "hunyuan-turbos-20250416": {"description": "Upgrade der vortrainierten Basis zur Stärkung des Befehlsverständnisses und der Befehlsbefolgung; Verbesserung der naturwissenschaftlichen Fähigkeiten in Mathematik, Programmierung, Logik und Wissenschaft während der Feinabstimmungsphase; Steigerung der Qualität in literarischer Kreativität, Textverständnis, Übersetzungsgenauigkeit und Wissensfragen; Verstärkung der Agentenfähigkeiten in verschiedenen Bereichen mit Schwerpunkt auf dem Verständnis mehrstufiger Dialoge."}, "hunyuan-turbos-20250604": {"description": "Upgrade der vortrainierten Basis, verbess<PERSON> und Leseverständnisfähigkeiten, steigert deutlich die Programmier- und naturwissenschaftlichen Kompetenzen und verbessert kontinuierlich die Befolgung komplexer Anweisungen."}, "hunyuan-turbos-latest": {"description": "hunyuan-TurboS ist die neueste Version des Hunyuan-Flaggschiffmodells, das über verbesserte Denkfähigkeiten und ein besseres Nutzungserlebnis verfügt."}, "hunyuan-turbos-longtext-128k-20250325": {"description": "Experte für die Verarbeitung von langen Textaufgaben wie Dokumentenzusammenfassungen und Dokumentenfragen, mit der Fähigkeit, allgemeine Textgenerierungsaufgaben zu bewältigen. Es zeigt hervorragende Leistungen bei der Analyse und Generierung von langen Texten und kann komplexe und detaillierte Anforderungen an die Verarbeitung langer Inhalte effektiv bewältigen."}, "hunyuan-turbos-role-plus": {"description": "Die neueste Version des Hunyuan-Rollenspielsmodells, feinabgestimmt und trainiert von Hunyuan, basiert auf dem Hunyuan-Modell und wurde mit Datensätzen für Rollenspielszenarien weiter trainiert, um in Rollenspielszenarien bessere Grundleistungen zu erzielen."}, "hunyuan-turbos-vision": {"description": "Dieses Modell eignet sich für Szenarien mit Bild- und Textverständnis und basiert auf dem neuesten hunyuan turbos. Es ist ein neues Flaggschiff-Visuell-Sprachmodell, das sich auf Aufgaben des Bild-Text-Verstehens konzen<PERSON>ert, einsch<PERSON>ßlich bildbasierter Entitätenerkennung, Wissensfragen, Textkreation und fotografiebasierter Problemlösung, mit umfassenden Verbesserungen gegenüber der Vorgängerversion."}, "hunyuan-turbos-vision-20250619": {"description": "Die neueste Version des hunyuan turbos-vision Flaggschiff-Visuell-Sprachmodells bietet umfassende Verbesserungen bei Aufgaben des Bild-Text-Verstehens, einschließlich bildbasierter Entitätenerkennung, Wissensfragen, Textkreation und fotografiebasierter Problemlösung, im Vergleich zur vorherigen Standardversion."}, "hunyuan-vision": {"description": "Das neueste multimodale Modell von <PERSON> unterstützt die Eingabe von Bildern und Text zur Generierung von Textinhalten."}, "image-01": {"description": "Neues Bildgenerierungsmodell mit feiner Bilddarstellung, unterstützt Text-zu-Bild und Bild-zu-Bild."}, "image-01-live": {"description": "Bildgenerierungsmodell mit feiner Bilddarstellung, unterstützt Text-zu-Bild und Stil-Einstellungen."}, "imagen-4.0-generate-preview-06-06": {"description": "Imagen 4. Generation Text-zu-Bild Modellserie"}, "imagen-4.0-ultra-generate-preview-06-06": {"description": "Imagen 4. Generation Text-zu-Bild Modellserie Ultra-Version"}, "imagen4/preview": {"description": "Googles hochwertigstes Bildgenerierungsmodell"}, "internlm/internlm2_5-7b-chat": {"description": "InternLM2.5 bietet intelligente Dialoglösungen in mehreren Szenarien."}, "internlm2.5-latest": {"description": "Unsere neueste Modellreihe mit herausragender Schlussfolgerungsleistung, die eine Kontextlänge von 1M unterstützt und über verbesserte Anweisungsbefolgung und Toolaufrufmöglichkeiten verfügt."}, "internlm3-latest": {"description": "Unsere neueste Modellreihe bietet herausragende Inferenzleistungen und führt die Open-Source-Modelle in ihrer Gewichtsklasse an. Standardmäßig verweist sie auf unser neuestes veröffentlichtes InternLM3-Modell."}, "internvl2.5-latest": {"description": "Die von uns weiterhin unterstützte Version InternVL2.5 bietet hervorragende und stabile Leistungen. Standardmäßig verweist es auf unser neuestes veröffentlichtes InternVL2.5-<PERSON><PERSON>, derzeit auf internvl2.5-78b."}, "internvl3-latest": {"description": "Unser neuestes multimodales Großmodell bietet verbesserte Fähigkeiten im Verständnis von Text und Bildern sowie im langfristigen Verständnis von Bildern und erreicht eine Leistung, die mit führenden proprietären Modellen vergleichbar ist. Standardmäßig verweist es auf unser neuestes veröffentlichtes InternVL-Modell, derzeit auf internvl3-78b."}, "irag-1.0": {"description": "Das von Baidu entwickelte iRAG (image based RAG) ist eine durch Suche verstärkte Text-zu-Bild-Technologie, die Baidus Milliarden von Bildressourcen mit leistungsstarken Basismodellen kombiniert, um ultra-realistische Bilder zu erzeugen. Das Gesamtergebnis übertrifft native Text-zu-Bild-Systeme deutlich, wirkt weniger künstlich und ist kostengünstig. iRAG zeichnet sich durch keine Halluzinationen, hohe Realitätsnähe und sofortige Verfügbarkeit aus."}, "jamba-large": {"description": "Unser leistungsstärkstes und fortschrittlichstes Modell, das speziell für die Bewältigung komplexer Aufgaben auf Unternehmensebene entwickelt wurde und herausragende Leistung bietet."}, "jamba-mini": {"description": "Das effizienteste Modell seiner Klasse, das Geschwindigkeit und Qualität vereint und eine kompakte Bauweise aufweist."}, "jina-deepsearch-v1": {"description": "Die Tiefensuche kombiniert Websu<PERSON>, Lesen und Schlussfolgern und ermöglicht umfassende Untersuchungen. Sie können es als einen Agenten betrachten, der Ihre Forschungsaufgaben übernimmt – er führt eine umfassende Suche durch und iteriert mehr<PERSON>ch, bevor er eine Antwort gibt. Dieser Prozess umfasst kontinuierliche Forschung, Schlussfolgerungen und die Lösung von Problemen aus verschiedenen Perspektiven. Dies unterscheidet sich grundlegend von den Standard-Großmodellen, die Antworten direkt aus vortrainierten Daten generieren, so<PERSON><PERSON> von traditionellen RAG-Systemen, die auf einmaligen Oberflächensuchen basieren."}, "kimi-k2": {"description": "Kimi-K2 ist ein von Moonshot AI entwickeltes MoE-Basis-Modell mit herausragenden Code- und Agentenfähigkeiten, insgesamt 1 Billion Parameter und 32 Milliarden aktivierten Parametern. In Benchmark-Tests zu allgemeinem Wissen, Programmierung, Mathematik und Agentenaufgaben übertrifft das K2-Modell andere führende Open-Source-Modelle."}, "kimi-k2-0711-preview": {"description": "kimi-k2 ist ein MoE-Architektur-Basis-Modell mit außergewöhnlichen Fähigkeiten in Code und Agentenfunktionen, mit insgesamt 1 Billion Parametern und 32 Milliarden aktiven Parametern. In Benchmark-Tests zu allgemeinem Wissen, Programmierung, Mathematik und Agenten übertrifft das K2-Modell andere führende Open-Source-Modelle."}, "kimi-latest": {"description": "Das Kimi intelligente Assistenzprodukt verwendet das neueste <PERSON>, das möglicherweise noch instabile Funktionen enthält. Es unterstützt die Bildverarbeitung und wählt automatisch das Abrechnungsmodell 8k/32k/128k basierend auf der Länge des angeforderten Kontexts aus."}, "kimi-thinking-preview": {"description": "Das kimi-thinking-<PERSON> <PERSON><PERSON>’s Dark Side ist ein multimodales Denkmodell mit Fähigkeiten zu multimodalem und allgemeinem logischem Denken. Es ist spezialisiert auf tiefgehende Schlussfolgerungen und hilft dabei, komplexere und schwierigere Aufgaben zu lösen."}, "learnlm-1.5-pro-experimental": {"description": "LearnLM ist ein experimentelles, aufgabenorientiertes Sprachmodell, das darauf trainiert wurde, den Prinzipien der Lernwissenschaft zu entsprechen und in Lehr- und Lernszenarien systematische Anweisungen zu befolgen, als Expertenmentor zu fungieren usw."}, "learnlm-2.0-flash-experimental": {"description": "LearnLM ist ein experimentelles, aufgabenbezogenes Sprachmodell, das darauf trainiert wurde, den Prinzipien der Lernwissenschaft zu entsprechen und in Lehr- und Lernszenarien systematische Anweisungen zu befolgen, als Expertenmentor zu fungieren usw."}, "lite": {"description": "Spark Lite ist ein leichtgewichtiges großes Sprachmodell mit extrem niedriger Latenz und effizienter Verarbeitung, das vollständig kostenlos und offen ist und Echtzeitsuchfunktionen unterstützt. Seine schnelle Reaktionsfähigkeit macht es besonders geeignet für Inferenzanwendungen und Modellanpassungen auf Geräten mit geringer Rechenleistung und bietet den Nutzern ein hervorragendes Kosten-Nutzen-Verhältnis sowie ein intelligentes Erlebnis, insbesondere in den Bereichen Wissensabfragen, Inhaltserstellung und Suchszenarien."}, "llama-2-7b-chat": {"description": "Llama2 ist eine Serie großer Sprachmodelle (LLM), die von Meta entwickelt und als Open Source veröffentlicht wurden. Diese Serie umfasst generative Textmodelle mit einer Parameteranzahl von 7 Milliarden bis 70 Milliarden, die vortrainiert und feinjustiert wurden. Architekturtechnisch ist Llama2 ein autoregressives Sprachmodell, das eine optimierte Transformer-Architektur verwendet. Die angepassten Versionen nutzen überwachte Feinabstimmung (SFT) und Reinforcement Learning mit menschlichem Feedback (RLHF), um den menschlichen Vorlieben für Nützlichkeit und Sicherheit zu entsprechen. Llama2 übertrifft die Leistung der Llama-Serie in mehreren akademischen Datensätzen und bietet Inspiration für die Entwicklung und Gestaltung vieler anderer Modelle."}, "llama-3.1-70b-versatile": {"description": "Llama 3.1 70B bietet leistungsstarke KI-Schlussfolgerungsfähigkeiten, die für komplexe Anwendungen geeignet sind und eine hohe Rechenverarbeitung bei gleichzeitiger Effizienz und Genauigkeit unterstützen."}, "llama-3.1-8b-instant": {"description": "Llama 3.1 8B ist ein leistungsstarkes Modell, das schnelle Textgenerierungsfähigkeiten bietet und sich hervorragend für Anwendungen eignet, die große Effizienz und Kosteneffektivität erfordern."}, "llama-3.1-instruct": {"description": "Das Llama 3.1 Instruktionstuning-Modell ist für Dialogszenarien optimiert und übertrifft in gängigen Branchenbenchmarks viele bestehende Open-Source-Chatmodelle."}, "llama-3.2-11b-vision-instruct": {"description": "Überlegene Bildverarbeitungsfähigkeiten auf hochauflösenden Bildern, geeignet für visuelle Verständnisanwendungen."}, "llama-3.2-11b-vision-preview": {"description": "Llama 3.2 ist da<PERSON><PERSON> au<PERSON>, <PERSON><PERSON><PERSON><PERSON> zu bearbeiten, die visuelle und textuelle Daten kombinieren. Es zeigt hervorragende Leistungen bei Aufgaben wie Bildbeschreibung und visuellen Fragen und Antworten und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "llama-3.2-90b-vision-instruct": {"description": "Erweiterte Bildverarbeitungsfähigkeiten für visuelle Verständnisagentenanwendungen."}, "llama-3.2-90b-vision-preview": {"description": "Llama 3.2 ist da<PERSON><PERSON> au<PERSON>, <PERSON><PERSON><PERSON><PERSON> zu bearbeiten, die visuelle und textuelle Daten kombinieren. Es zeigt hervorragende Leistungen bei Aufgaben wie Bildbeschreibung und visuellen Fragen und Antworten und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "llama-3.2-vision-instruct": {"description": "Das Llama 3.2-Vision-Instruct-Modell ist optimiert für visuelle Erkennung, Bildschlussfolgerungen, Bildbeschreibungen und das Beantworten von allgemeinen Fragen, die mit Bildern zusammenhängen."}, "llama-3.3-70b-instruct": {"description": "Llama 3.3 ist das fortschrittlichste mehrsprachige Open-Source-Sprachmodell der Llama-Serie, das eine Leistung bietet, die mit einem 405B-Modell vergleichbar ist, und das zu extrem niedrigen Kosten. Es basiert auf der Transformer-Architektur und verbessert die Nützlichkeit und Sicherheit durch überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF). Die auf Anweisungen optimierte Version ist speziell für mehrsprachige Dialoge optimiert und übertrifft in mehreren Branchenbenchmarks viele Open-Source- und geschlossene Chat-Modelle. Das Wissensdatum endet im Dezember 2023."}, "llama-3.3-70b-versatile": {"description": "Das Meta Llama 3.3 ist ein mehrsprachiges, g<PERSON><PERSON><PERSON> (LLM), das aus einem vortrainierten und anweisungsorientierten generativen Modell mit 70B (Text-Eingabe/Text-Ausgabe) besteht. Das anweisungsorientierte Modell von Llama 3.3 ist für mehrsprachige Dialoganwendungen optimiert und übertrifft viele verfügbare Open-Source- und Closed-Source-Chat-Modelle bei gängigen Branchenbenchmarks."}, "llama-3.3-instruct": {"description": "Das Llama 3.3 Instruct-Modell ist für Dialogszenarien optimiert und übertrifft in gängigen Branchenbenchmarks viele bestehende Open-Source-Chatmodelle."}, "llama3-70b-8192": {"description": "Meta Llama 3 70B bietet unvergleichliche Fähigkeiten zur Verarbeitung von Komplexität und ist maßgeschneidert für Projekte mit hohen Anforderungen."}, "llama3-8b-8192": {"description": "Meta Llama 3 8B bietet hervorragende Schlussfolgerungsfähigkeiten und eignet sich für eine Vielzahl von Anwendungsanforderungen."}, "llama3-groq-70b-8192-tool-use-preview": {"description": "Llama 3 Groq 70B Tool Use bietet leistungsstarke Werkzeugaufruf-Fähigkeiten und unterstützt die effiziente Verarbeitung komplexer Aufgaben."}, "llama3-groq-8b-8192-tool-use-preview": {"description": "Llama 3 Groq 8B Tool Use ist ein Modell, das für die effiziente Nutzung von Werkzeugen optimiert ist und schnelle parallele Berechnungen unterstützt."}, "llama3.1": {"description": "Llama 3.1 ist ein führendes Modell von Meta, das bis zu 405B Parameter unterstützt und in den Bereichen komplexe Dialoge, mehrsprachige Übersetzungen und Datenanalysen eingesetzt werden kann."}, "llama3.1:405b": {"description": "Llama 3.1 ist ein führendes Modell von Meta, das bis zu 405B Parameter unterstützt und in den Bereichen komplexe Dialoge, mehrsprachige Übersetzungen und Datenanalysen eingesetzt werden kann."}, "llama3.1:70b": {"description": "Llama 3.1 ist ein führendes Modell von Meta, das bis zu 405B Parameter unterstützt und in den Bereichen komplexe Dialoge, mehrsprachige Übersetzungen und Datenanalysen eingesetzt werden kann."}, "llava": {"description": "LLaVA ist ein multimodales Modell, das visuelle Encoder und Vicuna kombiniert und für starke visuelle und sprachliche Verständnisse sorgt."}, "llava-v1.5-7b-4096-preview": {"description": "LLaVA 1.5 7B bietet integrierte visuelle Verarbeitungsfähigkeiten, um komplexe Ausgaben aus visuellen Informationen zu generieren."}, "llava:13b": {"description": "LLaVA ist ein multimodales Modell, das visuelle Encoder und Vicuna kombiniert und für starke visuelle und sprachliche Verständnisse sorgt."}, "llava:34b": {"description": "LLaVA ist ein multimodales Modell, das visuelle Encoder und Vicuna kombiniert und für starke visuelle und sprachliche Verständnisse sorgt."}, "mathstral": {"description": "MathΣtral ist für wissenschaftliche Forschung und mathematische Schlussfolgerungen konzipiert und bietet effektive Rechenfähigkeiten und Ergebnisinterpretationen."}, "max-32k": {"description": "Spark Max 32K bietet eine große Kontextverarbeitungsfähigkeit mit verbesserter Kontextverständnis und logischer Schlussfolgerungsfähigkeit und unterstützt Texteingaben von bis zu 32K Tokens, was es ideal für das Lesen langer Dokumente und private Wissensabfragen macht."}, "megrez-3b-instruct": {"description": "Megrez-3B-Instruct ist ein großes Sprachmodell, das vollständig von Wuxin XinQiong trainiert wurde. Megrez-3B-Instruct zielt darauf ab, durch die Idee der Hardware-Software-Kooperation eine schnelle Inferenz, ein kompaktes Design und eine benutzerfreundliche Endgerätlösung zu schaffen."}, "meta-llama-3-70b-instruct": {"description": "Ein leistungsstarkes Modell mit 70 Milliarden Parametern, das in den Bereichen Schlussfolgerungen, Programmierung und breiten Sprachanwendungen herausragt."}, "meta-llama-3-8b-instruct": {"description": "Ein vielseitiges Modell mit 8 Milliarden Parametern, das für Dialog- und Textgenerierungsaufgaben optimiert ist."}, "meta-llama-3.1-405b-instruct": {"description": "Die Llama 3.1-<PERSON><PERSON>, die auf Anweisungen optimiert sind, sind für mehrsprachige Dialoganwendungen optimiert und übertreffen viele der verfügbaren Open-Source- und geschlossenen Chat-Modelle in gängigen Branchenbenchmarks."}, "meta-llama-3.1-70b-instruct": {"description": "Die Llama 3.1-<PERSON><PERSON>, die auf Anweisungen optimiert sind, sind für mehrsprachige Dialoganwendungen optimiert und übertreffen viele der verfügbaren Open-Source- und geschlossenen Chat-Modelle in gängigen Branchenbenchmarks."}, "meta-llama-3.1-8b-instruct": {"description": "Die Llama 3.1-<PERSON><PERSON>, die auf Anweisungen optimiert sind, sind für mehrsprachige Dialoganwendungen optimiert und übertreffen viele der verfügbaren Open-Source- und geschlossenen Chat-Modelle in gängigen Branchenbenchmarks."}, "meta-llama/Llama-2-13b-chat-hf": {"description": "LLaMA-2 Chat (13B) bietet hervorragende Sprachverarbeitungsfähigkeiten und ein ausgezeichnetes Interaktionserlebnis."}, "meta-llama/Llama-2-70b-hf": {"description": "LLaMA-2 bietet hervorragende Sprachverarbeitungsfähigkeiten und ein großartiges Interaktionserlebnis."}, "meta-llama/Llama-3-70b-chat-hf": {"description": "LLaMA-3 Chat (70B) ist ein leistungsstarkes Chat-Modell, das komplexe Dialoganforderungen unterstützt."}, "meta-llama/Llama-3-8b-chat-hf": {"description": "LLaMA-3 Chat (8B) bietet mehrsprachige Unterstützung und deckt ein breites Spektrum an Fachwissen ab."}, "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 ist da<PERSON><PERSON> au<PERSON>, Aufgaben zu bewältigen, die sowohl visuelle als auch Textdaten kombinieren. Es erzielt hervorragende Ergebnisse bei Aufgaben wie Bildbeschreibung und visueller Fragebeantwortung und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "meta-llama/Llama-3.2-3B-Instruct-Turbo": {"description": "LLaMA 3.2 ist da<PERSON><PERSON> au<PERSON>, Aufgaben zu bewältigen, die sowohl visuelle als auch Textdaten kombinieren. Es erzielt hervorragende Ergebnisse bei Aufgaben wie Bildbeschreibung und visueller Fragebeantwortung und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo": {"description": "LLaMA 3.2 ist da<PERSON><PERSON> au<PERSON>, Aufgaben zu bewältigen, die sowohl visuelle als auch Textdaten kombinieren. Es erzielt hervorragende Ergebnisse bei Aufgaben wie Bildbeschreibung und visueller Fragebeantwortung und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "meta-llama/Llama-3.3-70B-Instruct-Turbo": {"description": "Das Meta Llama 3.3 mehrsprachige große Sprachmodell (LLM) ist ein vortrainiertes und anweisungsoptimiertes Generierungsmodell mit 70B (Textinput/Textoutput). Das anweisungsoptimierte reine Textmodell von Llama 3.3 wurde für mehrsprachige Dialoganwendungen optimiert und übertrifft viele verfügbare Open-Source- und geschlossene Chat-Modelle in gängigen Branchenbenchmarks."}, "meta-llama/Llama-Vision-Free": {"description": "LLaMA 3.2 ist da<PERSON><PERSON> au<PERSON>, Aufgaben zu bewältigen, die sowohl visuelle als auch Textdaten kombinieren. Es erzielt hervorragende Ergebnisse bei Aufgaben wie Bildbeschreibung und visueller Fragebeantwortung und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "meta-llama/Meta-Llama-3-70B-Instruct-Lite": {"description": "Llama 3 70B Instruct Lite ist für Umgebungen geeignet, die hohe Leistung und niedrige Latenz erfordern."}, "meta-llama/Meta-Llama-3-70B-Instruct-Turbo": {"description": "Llama 3 70B Instruct Turbo bietet hervorragende Sprachverständnis- und Generierungsfähigkeiten und eignet sich für die anspruchsvollsten Rechenaufgaben."}, "meta-llama/Meta-Llama-3-8B-Instruct-Lite": {"description": "Llama 3 8B Instruct Lite ist für ressourcenbeschränkte Umgebungen geeignet und bietet eine hervorragende Balance zwischen Leistung und Effizienz."}, "meta-llama/Meta-Llama-3-8B-Instruct-Turbo": {"description": "Llama 3 8B Instruct Turbo ist ein leistungsstarkes großes Sprachmodell, das eine breite Palette von Anwendungsszenarien unterstützt."}, "meta-llama/Meta-Llama-3.1-405B-Instruct": {"description": "LLaMA 3.1 405B ist ein leistungsstarkes Modell für Vortraining und Anweisungsanpassung."}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"description": "Das 405B Llama 3.1 Turbo-Modell bietet eine enorme Kapazität zur Unterstützung von Kontexten für die Verarbeitung großer Datenmengen und zeigt herausragende Leistungen in groß angelegten KI-Anwendungen."}, "meta-llama/Meta-Llama-3.1-70B": {"description": "Llama 3.1 ist das führende Modell von Meta, das bis zu 405B Parameter unterstützt und in komplexen Gesprächen, mehrsprachiger Übersetzung und Datenanalyse eingesetzt werden kann."}, "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo": {"description": "Das Llama 3.1 70B-<PERSON>l wurde feinabgestimmt und eignet sich für hochbelastete Anwendungen, die auf FP8 quantisiert wurden, um eine effizientere Rechenleistung und Genauigkeit zu bieten und in komplexen Szenarien hervorragende Leistungen zu gewährleisten."}, "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo": {"description": "Das Llama 3.1 8B-Modell verwendet FP8-Quantisierung und unterstützt bis zu 131.072 Kontextmarkierungen, es ist eines der besten Open-Source-Modelle, das sich für komplexe Aufgaben eignet und in vielen Branchenbenchmarks übertrifft."}, "meta-llama/llama-3-70b-instruct": {"description": "Llama 3 70B Instruct ist optimiert für qualitativ hochwertige Dialogszenarien und zeigt hervorragende Leistungen in verschiedenen menschlichen Bewertungen."}, "meta-llama/llama-3-8b-instruct": {"description": "Llama 3 8B Instruct optimiert qualitativ hochwertige Dialogszenarien und bietet bessere Leistungen als viele geschlossene Modelle."}, "meta-llama/llama-3.1-70b-instruct": {"description": "Llama 3.1 70B Instruct ist speziell für qualitativ hochwertige Dialoge konzipiert und zeigt herausragende Leistungen in menschlichen Bewertungen, besonders geeignet für hochinteraktive Szenarien."}, "meta-llama/llama-3.1-8b-instruct": {"description": "Llama 3.1 8B Instruct ist die neueste Version von Meta, optimiert für qualitativ hochwertige Dialogszenarien und übertrifft viele führende geschlossene Modelle."}, "meta-llama/llama-3.1-8b-instruct:free": {"description": "LLaMA 3.1 bietet Unterstützung für mehrere Sprachen und gehört zu den führenden generativen Modellen der Branche."}, "meta-llama/llama-3.2-11b-vision-instruct": {"description": "LLaMA 3.2 ist da<PERSON><PERSON> au<PERSON>, <PERSON><PERSON><PERSON><PERSON> zu bearbeiten, die visuelle und textuelle Daten kombinieren. Es zeigt hervorragende Leistungen bei Aufgaben wie Bildbeschreibung und visuellem Fragen und Antworten und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "meta-llama/llama-3.2-3b-instruct": {"description": "meta-llama/llama-3.2-3b-instruct"}, "meta-llama/llama-3.2-90b-vision-instruct": {"description": "LLaMA 3.2 ist da<PERSON><PERSON> au<PERSON>, <PERSON><PERSON><PERSON><PERSON> zu bearbeiten, die visuelle und textuelle Daten kombinieren. Es zeigt hervorragende Leistungen bei Aufgaben wie Bildbeschreibung und visuellem Fragen und Antworten und überbrückt die Kluft zwischen Sprachgenerierung und visueller Schlussfolgerung."}, "meta-llama/llama-3.3-70b-instruct": {"description": "Llama 3.3 ist das fortschrittlichste mehrsprachige Open-Source-Sprachmodell der Llama-Serie, das eine Leistung bietet, die mit einem 405B-Modell vergleichbar ist, und das zu extrem niedrigen Kosten. Es basiert auf der Transformer-Architektur und verbessert die Nützlichkeit und Sicherheit durch überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF). Die auf Anweisungen optimierte Version ist speziell für mehrsprachige Dialoge optimiert und übertrifft in mehreren Branchenbenchmarks viele Open-Source- und geschlossene Chat-Modelle. Das Wissensdatum endet im Dezember 2023."}, "meta-llama/llama-3.3-70b-instruct:free": {"description": "Llama 3.3 ist das fortschrittlichste mehrsprachige Open-Source-Sprachmodell der Llama-Serie, das eine Leistung bietet, die mit einem 405B-Modell vergleichbar ist, und das zu extrem niedrigen Kosten. Es basiert auf der Transformer-Architektur und verbessert die Nützlichkeit und Sicherheit durch überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF). Die auf Anweisungen optimierte Version ist speziell für mehrsprachige Dialoge optimiert und übertrifft in mehreren Branchenbenchmarks viele Open-Source- und geschlossene Chat-Modelle. Das Wissensdatum endet im Dezember 2023."}, "meta.llama3-1-405b-instruct-v1:0": {"description": "Meta Llama 3.1 405B Instruct ist das größte und leistungsstärkste Modell innerhalb des Llama 3.1 Instruct Modells. Es handelt sich um ein hochentwickeltes Modell für dialogbasierte Schlussfolgerungen und die Generierung synthetischer Daten, das auch als Grundlage für die professionelle kontinuierliche Vorab- und Feinabstimmung in bestimmten Bereichen verwendet werden kann. Die mehrsprachigen großen Sprachmodelle (LLMs) von Llama 3.1 sind eine Gruppe von vortrainierten, anweisungsoptimierten Generierungsmodellen, die in den Größen 8B, 70B und 405B (Text-Eingabe/Ausgabe) verfügbar sind. Die anweisungsoptimierten Textmodelle (8B, 70B, 405B) sind speziell für mehrsprachige Dialoganwendungen optimiert und haben in gängigen Branchenbenchmarks viele verfügbare Open-Source-Chat-Modelle übertroffen. Llama 3.1 ist für kommerzielle und Forschungszwecke in mehreren Sprachen konzipiert. Die anweisungsoptimierten Textmodelle eignen sich für assistentengleiche Chats, während die vortrainierten Modelle für verschiedene Aufgaben der natürlichen Sprachgenerierung angepasst werden können. Das Llama 3.1 Modell unterstützt auch die Nutzung seiner Ausgaben zur Verbesserung anderer Modelle, einschließlich der Generierung synthetischer Daten und der Verfeinerung. Llama 3.1 ist ein autoregressives Sprachmodell, das auf einer optimierten Transformer-Architektur basiert. Die angepasste Version verwendet überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF), um den menschlichen Präferenzen für Hilfsbereitschaft und Sicherheit zu entsprechen."}, "meta.llama3-1-70b-instruct-v1:0": {"description": "Die aktualisierte Version von Meta Llama 3.1 70B Instruct umfasst eine erweiterte Kontextlänge von 128K, Mehrsprachigkeit und verbesserte Schlussfolgerungsfähigkeiten. Die von Llama 3.1 bereitgestellten mehrsprachigen großen Sprachmodelle (LLMs) sind eine Gruppe von vortrainierten, anweisungsoptimierten Generierungsmodellen, einschließlich Größen von 8B, 70B und 405B (Textinput/-output). Die anweisungsoptimierten Textmodelle (8B, 70B, 405B) sind für mehrsprachige Dialoganwendungen optimiert und übertreffen viele verfügbare Open-Source-Chat-Modelle in gängigen Branchenbenchmarks. Llama 3.1 ist für kommerzielle und Forschungszwecke in mehreren Sprachen konzipiert. Die anweisungsoptimierten Textmodelle eignen sich für assistentengleiche Chats, während die vortrainierten Modelle für eine Vielzahl von Aufgaben der natürlichen Sprachgenerierung angepasst werden können. Llama 3.1-Modelle unterstützen auch die Nutzung ihrer Ausgaben zur Verbesserung anderer Modelle, einschließlich der Generierung synthetischer Daten und der Verfeinerung. Llama 3.1 ist ein autoregressives Sprachmodell, das mit einer optimierten Transformer-Architektur entwickelt wurde. Die angepassten Versionen verwenden überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF), um den menschlichen Präferenzen für Hilfsbereitschaft und Sicherheit zu entsprechen."}, "meta.llama3-1-8b-instruct-v1:0": {"description": "Die aktualisierte Version von Meta Llama 3.1 8B Instruct umfasst eine erweiterte Kontextlänge von 128K, Mehrsprachigkeit und verbesserte Schlussfolgerungsfähigkeiten. Die von Llama 3.1 bereitgestellten mehrsprachigen großen Sprachmodelle (LLMs) sind eine Gruppe von vortrainierten, anweisungsoptimierten Generierungsmodellen, einschließlich Größen von 8B, 70B und 405B (Textinput/-output). Die anweisungsoptimierten Textmodelle (8B, 70B, 405B) sind für mehrsprachige Dialoganwendungen optimiert und übertreffen viele verfügbare Open-Source-Chat-Modelle in gängigen Branchenbenchmarks. Llama 3.1 ist für kommerzielle und Forschungszwecke in mehreren Sprachen konzipiert. Die anweisungsoptimierten Textmodelle eignen sich für assistentengleiche Chats, während die vortrainierten Modelle für eine Vielzahl von Aufgaben der natürlichen Sprachgenerierung angepasst werden können. Llama 3.1-Modelle unterstützen auch die Nutzung ihrer Ausgaben zur Verbesserung anderer Modelle, einschließlich der Generierung synthetischer Daten und der Verfeinerung. Llama 3.1 ist ein autoregressives Sprachmodell, das mit einer optimierten Transformer-Architektur entwickelt wurde. Die angepassten Versionen verwenden überwachte Feinabstimmung (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF), um den menschlichen Präferenzen für Hilfsbereitschaft und Sicherheit zu entsprechen."}, "meta.llama3-70b-instruct-v1:0": {"description": "Meta Llama 3 ist ein offenes großes Sprachmodell (LLM), das sich an Entwickler, Forscher und Unternehmen richtet und ihnen hilft, ihre Ideen für generative KI zu entwickeln, zu experimentieren und verantwortungsbewusst zu skalieren. Als Teil eines globalen Innovationssystems ist es besonders geeignet für die Erstellung von Inhalten, Dialog-KI, Sprachverständnis, Forschung und Unternehmensanwendungen."}, "meta.llama3-8b-instruct-v1:0": {"description": "Meta Llama 3 ist ein offenes großes Sprachmodell (LLM), das sich an Entwickler, Forscher und Unternehmen richtet und ihnen hilft, ihre Ideen für generative KI zu entwickeln, zu experimentieren und verantwortungsbewusst zu skalieren. Als Teil eines globalen Innovationssystems ist es besonders geeignet für Umgebungen mit begrenzter Rechenleistung und Ressourcen, für Edge-Geräte und schnellere Trainingszeiten."}, "meta/Llama-3.2-11B-Vision-Instruct": {"description": "Exzellente Bildinferenzfähigkeiten bei hochauflösenden Bildern, ideal für Anwendungen im Bereich visuelles Verständnis."}, "meta/Llama-3.2-90B-Vision-Instruct": {"description": "Fortschrittliche Bildinferenzfähigkeiten für visuelle Verständnisagenten."}, "meta/Llama-3.3-70B-Instruct": {"description": "Llama 3.3 ist das fortschrittlichste mehrsprachige Open-Source-Großsprachmodell der Llama-Reihe, das Leistung vergleichbar mit einem 405B-Modell zu sehr niedrigen Kosten bietet. Basierend auf der Transformer-Architektur, verbessert durch überwachtes Feintuning (SFT) und verstärkendes Lernen mit menschlichem Feedback (RLHF) für Nützlichkeit und Sicherheit. Die instruktionsoptimierte Version ist für mehrsprachige Dialoge optimiert und übertrifft viele offene und geschlossene Chatmodelle in verschiedenen Branchenbenchmarks. Wissensstand: Dezember 2023."}, "meta/Meta-Llama-3-70B-Instruct": {"description": "Ein leistungsstarkes Modell mit 70 Milliarden Parametern, das hervorragende Leistungen bei Inferenz, Codierung und vielfältigen Sprachaufgaben zeigt."}, "meta/Meta-Llama-3-8B-Instruct": {"description": "Ein vielseitiges Modell mit 8 Milliarden Parametern, optimiert für Dialog- und Textgenerierungsaufgaben."}, "meta/Meta-Llama-3.1-405B-Instruct": {"description": "Llama 3.1 ist ein instruktionsoptimiertes Textmodell, das für mehrsprachige Dialoganwendungen optimiert wurde und in vielen verfügbaren offenen und geschlossenen Chatmodellen bei gängigen Branchenbenchmarks hervorragende Leistungen zeigt."}, "meta/Meta-Llama-3.1-70B-Instruct": {"description": "Llama 3.1 ist ein instruktionsoptimiertes Textmodell, das für mehrsprachige Dialoganwendungen optimiert wurde und in vielen verfügbaren offenen und geschlossenen Chatmodellen bei gängigen Branchenbenchmarks hervorragende Leistungen zeigt."}, "meta/Meta-Llama-3.1-8B-Instruct": {"description": "Llama 3.1 ist ein instruktionsoptimiertes Textmodell, das für mehrsprachige Dialoganwendungen optimiert wurde und in vielen verfügbaren offenen und geschlossenen Chatmodellen bei gängigen Branchenbenchmarks hervorragende Leistungen zeigt."}, "meta/llama-3.1-405b-instruct": {"description": "Fortgeschrittenes LLM, das die Generierung synthetischer Daten, Wissensverdichtung und Schlussfolgerungen unterstützt, geeignet für Chatbots, Programmierung und spezifische Aufgaben."}, "meta/llama-3.1-70b-instruct": {"description": "Ermöglicht komplexe Gespräche mit hervorragendem Kontextverständnis, Schlussfolgerungsfähigkeiten und Textgenerierungsfähigkeiten."}, "meta/llama-3.1-8b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hochmodernes Modell mit Sprachverständnis, hervorragenden Schlussfolgerungsfähigkeiten und Textgenerierungsfähigkeiten."}, "meta/llama-3.2-11b-vision-instruct": {"description": "Spitzenmäßiges visuelles Sprachmodell, das in der Lage ist, qualitativ hochwertige Schlussfolgerungen aus Bildern zu ziehen."}, "meta/llama-3.2-1b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hochmodernes kleines Sprachmodell mit Sprachverständnis, hervorragenden Schlussfolgerungsfähigkeiten und Textgenerierungsfähigkeiten."}, "meta/llama-3.2-3b-instruct": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hochmodernes kleines Sprachmodell mit Sprachverständnis, hervorragenden Schlussfolgerungsfähigkeiten und Textgenerierungsfähigkeiten."}, "meta/llama-3.2-90b-vision-instruct": {"description": "Spitzenmäßiges visuelles Sprachmodell, das in der Lage ist, qualitativ hochwertige Schlussfolgerungen aus Bildern zu ziehen."}, "meta/llama-3.3-70b-instruct": {"description": "Fortschrittliches LLM, das auf Schlussfolgern, Mathematik, Allgemeinwissen und Funktionsaufrufen spezialisiert ist."}, "microsoft/Phi-3-medium-128k-instruct": {"description": "Dasselbe Phi-3-medium-<PERSON><PERSON>, jedoch mit größerem Kontextfenster, geeignet für RAG oder wenige Eingabeaufforderungen."}, "microsoft/Phi-3-medium-4k-instruct": {"description": "Ein Modell mit 14 Milliarden Parametern, das qualitativ besser als Phi-3-mini ist und sich auf hochwertige, inferenzintensive Daten konzentriert."}, "microsoft/Phi-3-mini-128k-instruct": {"description": "Dasselbe Phi-3-mini-Modell, jedoch mit größerem Kontextfenster, geeignet für RAG oder wenige Eingabeaufforderungen."}, "microsoft/Phi-3-mini-4k-instruct": {"description": "Das kleinste Mitglied der Phi-3-Familie, optimiert für Qualität und geringe Latenz."}, "microsoft/Phi-3-small-128k-instruct": {"description": "Dasselbe Phi-3-small-<PERSON><PERSON>, jedoch mit größerem Kontextfenster, geeignet für RAG oder wenige Eingabeaufforderungen."}, "microsoft/Phi-3-small-8k-instruct": {"description": "Ein Modell mit 7 Milliarden Parametern, das qualitativ besser als Phi-3-mini ist und sich auf hochwertige, inferenzintensive Daten konzentriert."}, "microsoft/Phi-3.5-mini-instruct": {"description": "Aktualisierte Version des Phi-3-mini-Modells."}, "microsoft/Phi-3.5-vision-instruct": {"description": "Aktualisierte Version des Phi-3-vision-Modells."}, "microsoft/WizardLM-2-8x22B": {"description": "WizardLM 2 ist ein Sprachmodell von Microsoft AI, das in komplexen Dialogen, Mehrsprachigkeit, Inferenz und intelligenten Assistenten besonders gut abschneidet."}, "microsoft/wizardlm-2-8x22b": {"description": "WizardLM-2 8x22B ist das fortschrittlichste Wizard-<PERSON><PERSON> von Microsoft AI und zeigt äußerst wettbewerbsfähige Leistungen."}, "minicpm-v": {"description": "MiniCPM-V ist das neue multimodale Großmodell von OpenBMB, das über hervorragende OCR-Erkennungs- und multimodale Verständnisfähigkeiten verfügt und eine Vielzahl von Anwendungsszenarien unterstützt."}, "ministral-3b-latest": {"description": "Ministral 3B ist das weltbeste Edge-Modell von <PERSON>l."}, "ministral-8b-latest": {"description": "Ministral 8B ist das kosteneffizienteste Edge-<PERSON><PERSON> von <PERSON>."}, "mistral": {"description": "Mistral ist ein 7B-<PERSON><PERSON> von Mistral AI, das sich für vielfältige Anforderungen an die Sprachverarbeitung eignet."}, "mistral-ai/Mistral-Large-2411": {"description": "Das Flaggschiffmodell <PERSON>, geeignet für komplexe Aufgaben mit großem Inferenzbedarf oder hoher Spezialisierung (Textgenerierung, Codegenerierung, RAG oder Agenten)."}, "mistral-ai/Mistral-Nemo": {"description": "Mistral Nemo ist ein hochmodernes Sprachmodell (LLM) mit führenden Fähigkeiten in seiner Größenklasse für Inferenz, Weltwissen und Codierung."}, "mistral-ai/mistral-small-2503": {"description": "Mistral Small eignet sich für alle sprachbasierten Aufgaben, die hohe Effizienz und geringe Latenz erfordern."}, "mistral-large": {"description": "Mixtral Large ist das Flaggschiff-Modell von <PERSON>, das die Fähigkeiten zur Codegenerierung, Mathematik und Schlussfolgerungen kombiniert und ein Kontextfenster von 128k unterstützt."}, "mistral-large-instruct": {"description": "Mistral-Large-Instruct-2407 ist ein fortschrittliches dichtes großes Sprachmodell (LLM) mit 123 Milliarden Parametern und verfügt über state-of-the-art-Schließen, Wissen und Codierungsfähigkeiten."}, "mistral-large-latest": {"description": "Mistral Large ist das Flaggschiff-Modell, das sich gut für mehrsprachige Aufgaben, komplexe Schlussfolgerungen und Codegenerierung eignet und die ideale Wahl für hochentwickelte Anwendungen ist."}, "mistral-medium-latest": {"description": "Mistral Medium 3 bietet mit 8-fachen Kosten erstklassige Leistung und vereinfacht grundlegend die Unternehmensbereitstellung."}, "mistral-nemo": {"description": "Mistral Nemo wurde in Zusammenarbeit mit Mistral AI und NVIDIA entwickelt und ist ein leistungsstarkes 12B-Modell."}, "mistral-nemo-instruct": {"description": "Das große Sprachmodell (LLM) Mistral-Nemo-Instruct-2407 ist eine auf Befehle angepasste Version von Mistral-Nemo-Base-2407."}, "mistral-small": {"description": "Mistral Small kann für jede sprachbasierte Aufgabe verwendet werden, die hohe Effizienz und geringe Latenz erfordert."}, "mistral-small-latest": {"description": "Mistral Small ist eine kosteneffiziente, schnelle und zuverlässige Option für Anwendungsfälle wie Übersetzung, Zusammenfassung und Sentimentanalyse."}, "mistralai/Mistral-7B-Instruct-v0.1": {"description": "Mistral (7B) Instruct ist bekannt für seine hohe Leistung und eignet sich für eine Vielzahl von Sprachaufgaben."}, "mistralai/Mistral-7B-Instruct-v0.2": {"description": "Mistral 7B ist ein nach Bedarf feinabgestimmtes Modell, das optimierte Antworten auf Aufgaben bietet."}, "mistralai/Mistral-7B-Instruct-v0.3": {"description": "Mistral (7B) Instruct v0.3 bietet effiziente Rechenleistung und natürliche Sprachverständnisfähigkeiten und eignet sich für eine Vielzahl von Anwendungen."}, "mistralai/Mistral-7B-v0.1": {"description": "Mistral 7B ist ein kompaktes, aber leistungsstarkes Modell, das gut für Batch-Verarbeitung und einfache Aufgaben wie Klassifizierung und Textgenerierung geeignet ist und über gute Schlussfolgerungsfähigkeiten verfügt."}, "mistralai/Mixtral-8x22B-Instruct-v0.1": {"description": "Mixtral-8x22B Instruct (141B) ist ein super großes Sprachmodell, das extrem hohe Verarbeitungsanforderungen unterstützt."}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"description": "Mixtral 8x7B ist ein vortrainiertes sparsames Mischmodell, das für allgemeine Textaufgaben verwendet wird."}, "mistralai/Mixtral-8x7B-v0.1": {"description": "Mixtral 8x7B ist ein sparsames Expertenmodell, das mehrere Parameter nutzt, um die Schlussfolgerungsgeschwindigkeit zu erhöhen, und sich gut für mehrsprachige und Code-Generierungsaufgaben eignet."}, "mistralai/mistral-7b-instruct": {"description": "Mistral 7B Instruct ist ein hochleistungsfähiges Branchenstandardmodell mit Geschwindigkeitsoptimierung und Unterstützung für lange Kontexte."}, "mistralai/mistral-nemo": {"description": "Mistral Nemo ist ein 7,3B-Parameter-Modell mit Unterstützung für mehrere Sprachen und hoher Programmierleistung."}, "mixtral": {"description": "Mixtral ist das Expertenmodell von Mistral AI, das über Open-Source-Gewichte verfügt und Unterstützung bei der Codegenerierung und Sprachverständnis bietet."}, "mixtral-8x7b-32768": {"description": "Mixtral 8x7B bietet hochgradig fehlertolerante parallele Berechnungsfähigkeiten und eignet sich für komplexe Aufgaben."}, "mixtral:8x22b": {"description": "Mixtral ist das Expertenmodell von Mistral AI, das über Open-Source-Gewichte verfügt und Unterstützung bei der Codegenerierung und Sprachverständnis bietet."}, "moonshot-v1-128k": {"description": "Moonshot V1 128K ist ein Modell mit überragenden Fähigkeiten zur Verarbeitung von langen Kontexten, das für die Generierung von sehr langen Texten geeignet ist und die Anforderungen komplexer Generierungsaufgaben erfüllt. Es kann Inhalte mit bis zu 128.000 Tokens verarbeiten und eignet sich hervorragend für Anwendungen in der Forschung, Wissenschaft und der Erstellung großer Dokumente."}, "moonshot-v1-128k-vision-preview": {"description": "<PERSON> Kimi-Visionsmodell (e<PERSON><PERSON><PERSON><PERSON><PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview usw.) kann Bildinhalte verstehen, e<PERSON><PERSON><PERSON><PERSON><PERSON> Bildtext, Bildfarbe und Objektformen."}, "moonshot-v1-32k": {"description": "Moonshot V1 32K bietet die Fähigkeit zur Verarbeitung von mittellangen Kontexten und kann 32.768 Tokens verarbeiten, was es besonders geeignet für die Generierung verschiedener langer Dokumente und komplexer Dialoge macht, die in den Bereichen Inhaltserstellung, Berichtsgenerierung und Dialogsysteme eingesetzt werden."}, "moonshot-v1-32k-vision-preview": {"description": "<PERSON> Kimi-Visionsmodell (e<PERSON><PERSON><PERSON><PERSON><PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview usw.) kann Bildinhalte verstehen, e<PERSON><PERSON><PERSON><PERSON><PERSON> Bildtext, Bildfarbe und Objektformen."}, "moonshot-v1-8k": {"description": "Moonshot V1 8K ist für die Generierung von Kurztextaufgaben konzipiert und bietet eine effiziente Verarbeitungsleistung, die 8.192 Tokens verarbeiten kann. Es eignet sich hervorragend für kurze Dialoge, Notizen und schnelle Inhaltserstellung."}, "moonshot-v1-8k-vision-preview": {"description": "<PERSON> Kimi-Visionsmodell (e<PERSON><PERSON><PERSON><PERSON><PERSON> moonshot-v1-8k-vision-preview/moonshot-v1-32k-vision-preview/moonshot-v1-128k-vision-preview usw.) kann Bildinhalte verstehen, e<PERSON><PERSON><PERSON><PERSON><PERSON> Bildtext, Bildfarbe und Objektformen."}, "moonshot-v1-auto": {"description": "Moonshot V1 Auto kann basierend auf der Anzahl der im aktuellen Kontext verwendeten Tokens das geeignete Modell auswählen."}, "moonshotai/Kimi-Dev-72B": {"description": "Kimi-Dev-72B ist ein Open-Source-Großmodell für Quellcode, das durch umfangreiche Verstärkungslernoptimierung robuste und direkt produktionsreife Patches erzeugen kann. Dieses Modell erreichte auf SWE-bench Verified eine neue Höchstpunktzahl von 60,4 % und stellte damit einen Rekord für Open-Source-Modelle bei automatisierten Software-Engineering-Aufgaben wie Fehlerbehebung und Code-Review auf."}, "moonshotai/Kimi-K2-Instruct": {"description": "Kimi K2 ist ein MoE-Basis-Modell mit herausragenden Code- und Agentenfähigkeiten, insgesamt 1 Billion Parameter und 32 Milliarden aktivierten Parametern. In Benchmark-Tests zu allgemeinem Wissen, Programmierung, Mathematik und Agentenaufgaben übertrifft das K2-Modell andere führende Open-Source-Modelle."}, "moonshotai/kimi-k2-instruct": {"description": "kimi-k2 ist ein MoE-Architektur-Basismodell mit außergewöhnlichen Fähigkeiten in Code und Agenten, mit insgesamt 1 Billion Parametern und 32 Milliarden aktiven Parametern. In Benchmark-Tests zu allgemeinem Wissen, Programmierung, Mathematik und Agenten übertrifft das K2-Modell andere führende Open-Source-Modelle."}, "nousresearch/hermes-2-pro-llama-3-8b": {"description": "Hermes 2 Pro Llama 3 8B ist die aktualisierte Version von Nous Hermes 2 und enthält die neuesten intern entwickelten Datensätze."}, "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF": {"description": "Llama 3.1 Nemotron 70B ist ein von NVIDIA maßgeschneidertes großes Sprachmodell, das darauf abzielt, die Hilfsfähigkeit der von LLM generierten Antworten auf Benutzeranfragen zu verbessern. Dieses Modell hat in Benchmark-Tests wie Arena Hard, AlpacaEval 2 LC und GPT-4-Turbo MT-Bench hervorragende Leistungen gezeigt und belegt bis zum 1. Oktober 2024 den ersten Platz in allen drei automatischen Ausrichtungsbenchmarks. Das Modell wurde mit RLHF (insbesondere REINFORCE), Llama-3.1-Nemotron-70B-Re<PERSON> und HelpSteer2-Preference-Prompts auf dem Llama-3.1-70B-Instruct-<PERSON>l trainiert."}, "nvidia/llama-3.1-nemotron-51b-instruct": {"description": "Einzigartiges Sprachmodell, das unvergleichliche Genauigkeit und Effizienz bietet."}, "nvidia/llama-3.1-nemotron-70b-instruct": {"description": "Llama-3.1-Nemotron-70B-Instruct ist ein von NVIDIA maßgeschneidertes großes Sprachmodell, das darauf abzielt, die Hilfsbereitschaft der von LLM generierten Antworten zu verbessern."}, "o1": {"description": "Konzentriert sich auf fortgeschrittene Inferenz und die Lösung komplexer Probleme, einschließlich mathematischer und wissenschaftlicher Aufgaben. Besonders geeignet für Anwendungen, die ein tiefes Verständnis des Kontexts und die Abwicklung von Arbeitsabläufen erfordern."}, "o1-mini": {"description": "o1-mini ist ein schnelles und kosteneffizientes Inferenzmodell, das für Programmier-, Mathematik- und Wissenschaftsanwendungen entwickelt wurde. Das Modell hat einen Kontext von 128K und einen Wissensstand bis Oktober 2023."}, "o1-preview": {"description": "o1 ist OpenAIs neues Inferenzmodell, das für komplexe Aufgaben geeignet ist, die umfangreiches Allgemeinwissen erfordern. Das Modell hat einen Kontext von 128K und einen Wissensstand bis Oktober 2023."}, "o1-pro": {"description": "Die o1-Serie wurde durch verstärkendes Lernen trainiert, um vor der Antwort nachzudenken und komplexe Schlussfolgerungen zu ziehen. Das o1-pro Modell nutzt mehr Rechenressourcen für tiefere Überlegungen und liefert dadurch kontinuierlich qualitativ hochwertigere Antworten."}, "o3": {"description": "o3 ist ein vielseitiges und leistungsstarkes Modell, das in mehreren Bereichen hervorragende Leistungen zeigt. Es setzt neue Maßstäbe für mathematische, wissenschaftliche, programmiertechnische und visuelle Schlussfolgerungsaufgaben. Es ist auch versiert in technischer Schreibweise und der Befolgung von Anweisungen. Benutzer können es nutzen, um Texte, Code und Bilder zu analysieren und komplexe Probleme mit mehreren Schritten zu lösen."}, "o3-deep-research": {"description": "o3-deep-research ist unser fortschrittlichstes Deep-Research-Modell, das speziell für die Bearbeitung komplexer, mehrstufiger Forschungsaufgaben entwickelt wurde. Es kann Informationen aus dem Internet suchen und zusammenfassen sowie über den MCP-Connector auf Ihre eigenen Daten zugreifen und diese nutzen."}, "o3-mini": {"description": "o3-mini ist unser neuestes kompaktes Inferenzmodell, das bei den gleichen Kosten- und Verzögerungszielen wie o1-mini hohe Intelligenz bietet."}, "o3-pro": {"description": "Das o3-pro Modell verwendet mehr Rechenleistung für tiefere Überlegungen und liefert stets bessere Antworten. Es ist ausschließlich über die Responses API nutzbar."}, "o4-mini": {"description": "o4-mini ist unser neuestes kompaktes Modell der o-Serie. Es wurde für schnelle und effektive Inferenz optimiert und zeigt in Programmier- und visuellen Aufgaben eine hohe Effizienz und Leistung."}, "o4-mini-deep-research": {"description": "o4-mini-deep-research ist unser schnelleres und kostengünstigeres Deep-Research-Modell – ideal für die Bearbeitung komplexer, mehrstufiger Forschungsaufgaben. Es kann Informationen aus dem Internet suchen und zusammenfassen sowie über den MCP-Connector auf Ihre eigenen Daten zugreifen und diese nutzen."}, "open-codestral-mamba": {"description": "Codestral Mamba ist ein auf die Codegenerierung spezialisiertes Mamba 2-<PERSON><PERSON><PERSON><PERSON><PERSON>, das starke Unterstützung für fortschrittliche Code- und Schlussfolgerungsaufgaben bietet."}, "open-mistral-7b": {"description": "Mistral 7B ist ein kompaktes, aber leistungsstarkes Modell, das sich gut für Batch-Verarbeitung und einfache Aufgaben wie Klassifizierung und Textgenerierung eignet und über gute Schlussfolgerungsfähigkeiten verfügt."}, "open-mistral-nemo": {"description": "Mistral Nemo ist ein 12B-Modell, das in Zusammenarbeit mit Nvidia entwickelt wurde und hervorragende Schlussfolgerungs- und Codierungsfähigkeiten bietet, die leicht zu integrieren und zu ersetzen sind."}, "open-mixtral-8x22b": {"description": "Mixtral 8x22B ist ein größeres Expertenmodell, das sich auf komplexe Aufgaben konzentriert und hervorragende Schlussfolgerungsfähigkeiten sowie eine höhere Durchsatzrate bietet."}, "open-mixtral-8x7b": {"description": "Mixtral 8x7B ist ein spärliches Expertenmodell, das mehrere Parameter nutzt, um die Schlussfolgerungsgeschwindigkeit zu erhöhen und sich für die Verarbeitung mehrsprachiger und Codegenerierungsaufgaben eignet."}, "openai/gpt-4.1": {"description": "GPT-4.1 ist unser Flaggschiff-Modell für komplexe Aufgaben. Es eignet sich hervorragend zur Lösung von Problemen über verschiedene Fachgebiete hinweg."}, "openai/gpt-4.1-mini": {"description": "GPT-4.1 mini bietet ein Gleichgewicht zwischen Intelligenz, Geschwindigkeit und Kosten, was es zu einem attraktiven Modell für viele Anwendungsfälle macht."}, "openai/gpt-4.1-nano": {"description": "GPT-4.1 nano ist das schnellste und kosteneffektivste Modell der GPT-4.1-<PERSON><PERSON><PERSON>."}, "openai/gpt-4o": {"description": "ChatGPT-4o ist ein dynamisches Modell, das in Echtzeit aktualisiert wird, um die neueste Version zu gewährleisten. Es kombiniert starke Sprachverständnis- und Generierungsfähigkeiten und eignet sich für großangelegte Anwendungsszenarien, einschließlich Kundenservice, Bildung und technischem Support."}, "openai/gpt-4o-mini": {"description": "GPT-4o mini ist das neueste Modell von OpenAI, das nach GPT-4 Omni veröffentlicht wurde und Text- und Bild-Eingaben unterstützt. Als ihr fortschrittlichstes kleines Modell ist es viel günstiger als andere neueste Modelle und über 60 % günstiger als GPT-3.5 Turbo. Es behält die fortschrittlichste Intelligenz bei und bietet gleichzeitig ein hervorragendes Preis-Leistungs-Verhältnis. GPT-4o mini erzielte 82 % im MMLU-Test und rangiert derzeit in den Chat-Präferenzen über GPT-4."}, "openai/o1": {"description": "o1 ist OpenAIs neues Inferenzmodell, das Bild- und Texteingaben unterstützt und Text ausgibt. Es eignet sich für komplexe Aufgaben, die umfangreiches Allgemeinwissen erfordern. Das Modell verfügt über einen Kontext von 200K und einen Wissensstand bis Oktober 2023."}, "openai/o1-mini": {"description": "o1-mini ist ein schnelles und kosteneffizientes Inferenzmodell, das für Programmier-, Mathematik- und Wissenschaftsanwendungen entwickelt wurde. Das Modell hat einen Kontext von 128K und einen Wissensstand bis Oktober 2023."}, "openai/o1-preview": {"description": "o1 ist OpenAIs neues Inferenzmodell, das für komplexe Aufgaben geeignet ist, die umfangreiches Allgemeinwissen erfordern. Das Modell hat einen Kontext von 128K und einen Wissensstand bis Oktober 2023."}, "openai/o3": {"description": "o3 ist ein leistungsstarkes Allround-Modell, das in mehreren Bereichen hervorragende Leistungen zeigt. Es setzt neue Maßstäbe für mathematische, wissenschaftliche, programmiertechnische und visuelle Denkaufgaben. Es ist auch versiert in technischer Schreibweise und der Befolgung von Anweisungen. Benutzer können es nutzen, um Texte, Code und Bilder zu analysieren und komplexe Probleme mit mehreren Schritten zu lösen."}, "openai/o3-mini": {"description": "o3-mini bietet hohe Intelligenz bei den gleichen Kosten- und Verzögerungszielen wie o1-mini."}, "openai/o3-mini-high": {"description": "o3-mini high ist eine hochintelligente Version mit dem gleichen Kosten- und Verzögerungsziel wie o1-mini."}, "openai/o4-mini": {"description": "o4-mini ist für schnelle und effektive Inferenz optimiert und zeigt in Programmier- und visuellen Aufgaben eine hohe Effizienz und Leistung."}, "openai/o4-mini-high": {"description": "o4-mini Hochleistungsmodell, optimiert für schnelle und effektive Inferenz, zeigt in Programmier- und visuellen Aufgaben eine hohe Effizienz und Leistung."}, "openrouter/auto": {"description": "<PERSON> nach Ko<PERSON><PERSON>, Thema und Komplexität wird Ihre Anfrage an Llama 3 70B <PERSON><PERSON><PERSON><PERSON>, Claude 3.5 Sonnet (selbstregulierend) oder GPT-4o gesendet."}, "phi3": {"description": "Phi-3 ist ein leichtgewichtiges offenes Modell von Microsoft, das für effiziente Integration und großangelegte Wissensschlüsse geeignet ist."}, "phi3:14b": {"description": "Phi-3 ist ein leichtgewichtiges offenes Modell von Microsoft, das für effiziente Integration und großangelegte Wissensschlüsse geeignet ist."}, "pixtral-12b-2409": {"description": "Das Pixtral-Modell zeigt starke Fähigkeiten in Aufgaben wie Diagramm- und Bildverständnis, Dokumentenfragen, multimodale Schlussfolgerungen und Befolgung von Anweisungen. Es kann Bilder in natürlicher Auflösung und Seitenverhältnis aufnehmen und in einem langen Kontextfenster von bis zu 128K Tokens beliebig viele Bilder verarbeiten."}, "pixtral-large-latest": {"description": "Pixtral Large ist ein Open-Source-Multimodalmodell mit 124 Milliarden Parametern, das auf Mistral Large 2 basiert. Dies ist unser zweites Modell in der multimodalen Familie und zeigt fortschrittliche Fähigkeiten im Bereich der Bildverständnis."}, "pro-128k": {"description": "Spark Pro 128K verfügt über eine außergewöhnliche Kontextverarbeitungsfähigkeit und kann bis zu 128K Kontextinformationen verarbeiten, was es besonders geeignet für die Analyse langer Texte und die Verarbeitung langfristiger logischer Zusammenhänge macht. Es bietet in komplexen Textkommunikationen flüssige und konsistente Logik sowie vielfältige Unterstützung für Zitate."}, "qvq-72b-preview": {"description": "Das QVQ-Modell ist ein experimentelles Forschungsmodell, das vom Qwen-Team entwickelt wurde und sich auf die Verbesserung der visuellen Schlussfolgerungsfähigkeiten konzentriert, insbesondere im Bereich der mathematischen Schlussfolgerungen."}, "qvq-max": {"description": "Tongyi Qianwen QVQ visuelles Schlussfolgerungsmodell, unterstützt visuelle Eingaben und Denkprozessketten-Ausgaben, zeigt stärkere Fähigkeiten in Mathematik, Programmierung, visueller Analyse, Kreativität und allgemeinen Aufgaben."}, "qvq-plus": {"description": "Visuelles Schlussfolgerungsmodell. Unterstützt visuelle Eingaben und Denkprozess-Ausgaben. Die Plus-Version, die auf dem qvq-max-<PERSON><PERSON> basiert, bietet schnellere Inferenzgeschwindigkeit sowie ein ausgewogeneres Verhältnis von Leistung und Kosten."}, "qwen-coder-plus": {"description": "<PERSON><PERSON>dell."}, "qwen-coder-turbo": {"description": "<PERSON><PERSON>dell."}, "qwen-coder-turbo-latest": {"description": "<PERSON>-Modell."}, "qwen-long": {"description": "<PERSON>wen ist ein groß angelegtes Sprachmodell, das lange Textkontexte unterstützt und Dialogfunktionen für verschiedene Szenarien wie lange Dokumente und mehrere Dokumente bietet."}, "qwen-math-plus": {"description": "<PERSON><PERSON> Qianwen Mathematikmodell, speziell für mathematische Problemlösungen entwickelt."}, "qwen-math-plus-latest": {"description": "Das Tongyi Qianwen Mathematikmodell ist speziell für die Lösung von mathematischen Problemen konzipiert."}, "qwen-math-turbo": {"description": "<PERSON><PERSON> Qianwen Mathematikmodell, speziell für mathematische Problemlösungen entwickelt."}, "qwen-math-turbo-latest": {"description": "Das Tongyi Qianwen Mathematikmodell ist speziell für die Lösung von mathematischen Problemen konzipiert."}, "qwen-max": {"description": "Qwen Max ist ein großangelegtes Sprachmodell auf Billionenebene, das Eingaben in verschiedenen Sprachen wie Chinesisch und Englisch unterstützt und das API-Modell hinter der aktuellen Produktversion von Qwen 2.5 ist."}, "qwen-omni-turbo": {"description": "Die Qwen-Omni-Modellreihe unterstützt die Eingabe verschiedener Modalitäten, einschließlich Video, Audio, Bild und Text, und gibt Audio und Text aus."}, "qwen-plus": {"description": "Qwen Plus ist die verbesserte Version des großangelegten Sprachmodells, das Eingaben in verschiedenen Sprachen wie Chinesisch und Englisch unterstützt."}, "qwen-turbo": {"description": "Qwen Turbo ist ein großangelegtes Sprachmodell, das Eingaben in verschiedenen Sprachen wie Chinesisch und Englisch unterstützt."}, "qwen-vl-chat-v1": {"description": "Qwen VL unterstützt flexible Interaktionsmethoden, einschließlich Mehrbild-, Mehrfachfragen und kreativen Fähigkeiten."}, "qwen-vl-max": {"description": "Tongyi Qianwen extrem großskaliges visuelles Sprachmodell. Im Vergleich zur erweiterten Version weitere Steigerung der visuellen Schlussfolgerungs- und Befehlsbefolgungsfähigkeiten, bietet ein höheres Niveau visueller Wahrnehmung und Kognition."}, "qwen-vl-max-latest": {"description": "Das Tongyi Qianwen Ultra-Scale Visuelle Sprachmodell. Im Vergleich zur verbesserten Version wurden die Fähigkeiten zur visuellen Schlussfolgerung und Befolgung von Anweisungen weiter gesteigert, was ein höheres Niveau an visueller Wahrnehmung und Kognition bietet."}, "qwen-vl-ocr": {"description": "Tongyi Qianwen OCR ist ein spezialisiertes Modell zur Textextraktion, fokussiert auf Dokumente, Tabellen, Prüfungsaufgaben, Handschrift und andere Bildtypen. Es erkennt verschiedene Sprachen, <PERSON><PERSON><PERSON>, Englisch, Französisch, Japanisch, Koreanisch, Deutsch, Russisch, Italienisch, Vietnamesisch und Arabisch."}, "qwen-vl-plus": {"description": "Erweiterte Version des Tongyi Qianwen großskaligen visuellen Sprachmodells. Deutliche Verbesserung der Detail- und Texterkennungsfähigkeiten, unterstützt Bildauflösungen von über einer Million Pixeln und beliebige Seitenverhältnisse."}, "qwen-vl-plus-latest": {"description": "Die verbesserte Version des Tongyi Qianwen, eines großangelegten visuellen Sprachmodells. Deutlich verbesserte Fähigkeiten zur Detailerkennung und Texterkennung, unterstützt Bildauflösungen von über einer Million Pixel und beliebige Seitenverhältnisse."}, "qwen-vl-v1": {"description": "Initiiert mit dem Qwen-7B-<PERSON><PERSON><PERSON><PERSON><PERSON>, fügt es ein Bildmodell hinzu, das für Bildeingaben mit einer Auflösung von 448 vortrainiert wurde."}, "qwen/qwen-2-7b-instruct": {"description": "Qwen2 ist die brandneue Serie von großen Sprachmodellen von Qwen. Qwen2 7B ist ein transformerbasiertes Modell, das in den Bereichen Sprachverständnis, Mehrsprachigkeit, Programmierung, Mathematik und logisches Denken hervorragende Leistungen zeigt."}, "qwen/qwen-2-7b-instruct:free": {"description": "Qwen2 ist eine neue Serie großer Sprachmodelle mit stärkeren Verständnis- und Generierungsfähigkeiten."}, "qwen/qwen-2-vl-72b-instruct": {"description": "Qwen2-VL ist die neueste Iteration des Qwen-VL-Modells und hat in Benchmark-Tests zur visuellen Verständlichkeit eine fortschrittliche Leistung erreicht, einschließlich MathVista, DocVQA, RealWorldQA und MTVQA. Qwen2-VL kann über 20 Minuten Video verstehen und ermöglicht qualitativ hochwertige, videobasierte Fragen und Antworten, Dialoge und Inhaltserstellung. Es verfügt auch über komplexe Denk- und Entscheidungsfähigkeiten und kann mit mobilen Geräten, Robotern usw. integriert werden, um basierend auf visuellen Umgebungen und Textanweisungen automatisch zu agieren. Neben Englisch und Chinesisch unterstützt Qwen2-VL jetzt auch das Verständnis von Text in Bildern in verschiedenen Sprachen, einschließlich der meisten europäischen Sprachen, Japanisch, Koreanisch, Arabisch und Vietnamesisch."}, "qwen/qwen-2.5-72b-instruct": {"description": "Qwen2.5-72B-Instruct ist eines der neuesten großen Sprachmodell-Serien, die von Alibaba Cloud veröffentlicht wurden. Dieses 72B-Modell hat signifikante Verbesserungen in den Bereichen Codierung und Mathematik. Das Modell bietet auch mehrsprachige Unterstützung und deckt über 29 Sprachen ab, einschließlich Chinesisch und Englisch. Das Modell hat signifikante Verbesserungen in der Befolgung von Anweisungen, im Verständnis von strukturierten Daten und in der Generierung von strukturierten Ausgaben (insbesondere JSON) erzielt."}, "qwen/qwen2.5-32b-instruct": {"description": "Qwen2.5-32B-Instruct ist eines der neuesten großen Sprachmodell-Serien, die von Alibaba Cloud veröffentlicht wurden. Dieses 32B-Modell hat signifikante Verbesserungen in den Bereichen Codierung und Mathematik. Das Modell bietet auch mehrsprachige Unterstützung und deckt über 29 Sprachen ab, einschließlich Chinesisch und Englisch. Das Modell hat signifikante Verbesserungen in der Befolgung von Anweisungen, im Verständnis von strukturierten Daten und in der Generierung von strukturierten Ausgaben (insbesondere JSON) erzielt."}, "qwen/qwen2.5-7b-instruct": {"description": "LLM, das auf Chinesisch und Englisch ausgerichtet ist und sich auf Sprache, Programmierung, Mathematik, Schlussfolgern und andere Bereiche konzentriert."}, "qwen/qwen2.5-coder-32b-instruct": {"description": "Fortgeschrittenes LLM, das die Codegenerierung, Schlussfolgerungen und Korrekturen unterstützt und gängige Programmiersprachen abdeckt."}, "qwen/qwen2.5-coder-7b-instruct": {"description": "Leistungsstarkes, mittelgroßes Codierungsmodell, das 32K Kontextlängen unterstützt und in der mehrsprachigen Programmierung versiert ist."}, "qwen/qwen3-14b": {"description": "Qwen3-14B ist ein kompaktes, 14,8 Milliarden Parameter umfassendes kausales Sprachmodell aus der Qwen3-Serie, das speziell für komplexe Inferenz und effiziente Dialoge entwickelt wurde. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für Mathematik, Programmierung und logische Inferenz und dem \"Nicht-Denk\"-Modus für allgemeine Gespräche. Dieses Modell wurde feinabgestimmt und kann für die Befolgung von Anweisungen, die Nutzung von Agentenwerkzeugen, kreatives Schreiben sowie mehrsprachige Aufgaben in über 100 Sprachen und Dialekten verwendet werden. Es verarbeitet nativ 32K Token-Kontext und kann mithilfe von YaRN auf 131K Token erweitert werden."}, "qwen/qwen3-14b:free": {"description": "Qwen3-14B ist ein kompaktes, 14,8 Milliarden Parameter umfassendes kausales Sprachmodell aus der Qwen3-Serie, das speziell für komplexe Inferenz und effiziente Dialoge entwickelt wurde. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für Mathematik, Programmierung und logische Inferenz und dem \"Nicht-Denk\"-Modus für allgemeine Gespräche. Dieses Modell wurde feinabgestimmt und kann für die Befolgung von Anweisungen, die Nutzung von Agentenwerkzeugen, kreatives Schreiben sowie mehrsprachige Aufgaben in über 100 Sprachen und Dialekten verwendet werden. Es verarbeitet nativ 32K Token-Kontext und kann mithilfe von YaRN auf 131K Token erweitert werden."}, "qwen/qwen3-235b-a22b": {"description": "Qwen3-235B-A22B ist ein 235B Parameter Expertenmischungsmodell (MoE), das von Qwen entwickelt wurde und bei jedem Vorwärtsdurchlauf 22B Parameter aktiviert. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für komplexe Inferenz, Mathematik und Programmieraufgaben und dem \"Nicht-Denk\"-Modus für allgemeine Gespräche. Dieses Modell zeigt starke Inferenzfähigkeiten, mehrsprachige Unterstützung (über 100 Sprachen und Dialekte), fortgeschrittene Befolgung von Anweisungen und die Nutzung von Agentenwerkzeugen. Es verarbeitet nativ ein Kontextfenster von 32K Token und kann mithilfe von YaRN auf 131K Token erweitert werden."}, "qwen/qwen3-235b-a22b:free": {"description": "Qwen3-235B-A22B ist ein 235B Parameter Expertenmischungsmodell (MoE), das von Qwen entwickelt wurde und bei jedem Vorwärtsdurchlauf 22B Parameter aktiviert. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für komplexe Inferenz, Mathematik und Programmieraufgaben und dem \"Nicht-Denk\"-Modus für allgemeine Gespräche. Dieses Modell zeigt starke Inferenzfähigkeiten, mehrsprachige Unterstützung (über 100 Sprachen und Dialekte), fortgeschrittene Befolgung von Anweisungen und die Nutzung von Agentenwerkzeugen. Es verarbeitet nativ ein Kontextfenster von 32K Token und kann mithilfe von YaRN auf 131K Token erweitert werden."}, "qwen/qwen3-30b-a3b": {"description": "Qwen3 ist die neueste Generation der Qwen großen Sprachmodellreihe, die über eine dichte und Expertenmischung (MoE) Architektur verfügt und in den Bereichen Inferenz, mehrsprachige Unterstützung und anspruchsvolle Agentenaufgaben hervorragende Leistungen zeigt. Ihre einzigartige Fähigkeit, nahtlos zwischen komplexen Denkmodi und effizienten Dialogmodi zu wechseln, gewährleistet eine vielseitige und qualitativ hochwertige Leistung.\n\nQwen3 übertrifft deutlich frühere Modelle wie QwQ und Qwen2.5 und bietet herausragende Fähigkeiten in Mathematik, Programmierung, allgemeinem Wissen, kreativem Schreiben und interaktiven Dialogen. Die Variante Qwen3-30B-A3B enthält 30,5 Milliarden Parameter (3,3 Milliarden aktivierte Parameter), 48 Schichten, 128 Experten (jeweils 8 aktivierte für jede Aufgabe) und unterstützt bis zu 131K Token-Kontext (unter Verwendung von YaRN), was einen neuen Standard für Open-Source-Modelle setzt."}, "qwen/qwen3-30b-a3b:free": {"description": "Qwen3 ist die neueste Generation der Qwen großen Sprachmodellreihe, die über eine dichte und Expertenmischung (MoE) Architektur verfügt und in den Bereichen Inferenz, mehrsprachige Unterstützung und anspruchsvolle Agentenaufgaben hervorragende Leistungen zeigt. Ihre einzigartige Fähigkeit, nahtlos zwischen komplexen Denkmodi und effizienten Dialogmodi zu wechseln, gewährleistet eine vielseitige und qualitativ hochwertige Leistung.\n\nQwen3 übertrifft deutlich frühere Modelle wie QwQ und Qwen2.5 und bietet herausragende Fähigkeiten in Mathematik, Programmierung, allgemeinem Wissen, kreativem Schreiben und interaktiven Dialogen. Die Variante Qwen3-30B-A3B enthält 30,5 Milliarden Parameter (3,3 Milliarden aktivierte Parameter), 48 Schichten, 128 Experten (jeweils 8 aktivierte für jede Aufgabe) und unterstützt bis zu 131K Token-Kontext (unter Verwendung von YaRN), was einen neuen Standard für Open-Source-Modelle setzt."}, "qwen/qwen3-32b": {"description": "Qwen3-32B ist ein kompaktes, 32,8 Milliarden Parameter umfassendes kausales Sprachmodell aus der Qwen3-Serie, das für komplexe Inferenz und effiziente Dialoge optimiert wurde. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für Mathematik, Programmierung und logische Inferenz und dem \"Nicht-Denk\"-Modus für schnellere, allgemeine Gespräche. Dieses Modell zeigt starke Leistungen in der Befolgung von Anweisungen, der Nutzung von Agentenwerkzeugen, kreativem Schreiben sowie mehrsprachigen Aufgaben in über 100 Sprachen und Dialekten. Es verarbeitet nativ 32K Token-Kontext und kann mithilfe von YaRN auf 131K Token erweitert werden."}, "qwen/qwen3-32b:free": {"description": "Qwen3-32B ist ein kompaktes, 32,8 Milliarden Parameter umfassendes kausales Sprachmodell aus der Qwen3-Serie, das für komplexe Inferenz und effiziente Dialoge optimiert wurde. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für Mathematik, Programmierung und logische Inferenz und dem \"Nicht-Denk\"-Modus für schnellere, allgemeine Gespräche. Dieses Modell zeigt starke Leistungen in der Befolgung von Anweisungen, der Nutzung von Agentenwerkzeugen, kreativem Schreiben sowie mehrsprachigen Aufgaben in über 100 Sprachen und Dialekten. Es verarbeitet nativ 32K Token-Kontext und kann mithilfe von YaRN auf 131K Token erweitert werden."}, "qwen/qwen3-8b:free": {"description": "Qwen3-8B ist ein kompaktes, 8,2 Milliarden Parameter umfassendes kausales Sprachmodell aus der Qwen3-Serie, das speziell für inferenzintensive Aufgaben und effiziente Dialoge entwickelt wurde. Es unterstützt den nahtlosen Wechsel zwischen dem \"Denk\"-Modus für Mathematik, Programmierung und logische Inferenz und dem \"Nicht-Denk\"-Modus für allgemeine Gespräche. Dieses Modell wurde feinabgestimmt und kann für die Befolgung von Anweisungen, die Integration von Agenten, kreatives Schreiben sowie die mehrsprachige Nutzung in über 100 Sprachen und Dialekten verwendet werden. Es unterstützt nativ ein Kontextfenster von 32K Token und kann über YaRN auf 131K Token erweitert werden."}, "qwen2": {"description": "Qwen2 ist das neue große Sprachmodell von <PERSON>, das mit hervorragender Leistung eine Vielzahl von Anwendungsanforderungen unterstützt."}, "qwen2-72b-instruct": {"description": "Qwen2 ist die neueste Generation von Sprachmodellen, die vom Qwen-Team entwickelt wurde. Es basiert auf der Transformer-Architektur und verwendet Techniken wie die SwiGLU-Aktivierungsfunktion, die Aufmerksamkeits-QKV-Bias (attention QKV bias), die gruppenbasierte Abfrageaufmerksamkeit (group query attention) und eine Mischung aus rutschendem Fenster und voller Aufmerksamkeit (mixture of sliding window attention and full attention). Darüber hinaus hat das Qwen-Team den Tokenizer verbessert, der für die Verarbeitung von natürlicher Sprache und Code optimiert ist."}, "qwen2-7b-instruct": {"description": "Qwen2 ist die neueste Serie von großen Sprachmodellen, die vom Qwen-Team entwickelt wurde. Es basiert auf der Transformer-Architektur und verwendet Techniken wie die SwiGLU-Aktivierungsfunktion, die Aufmerksamkeits-QKV-Bias (attention QKV bias), die Gruppenabfrageaufmerksamkeit (group query attention) und eine Mischung aus rutschendem Fenster und voller Aufmerksamkeit (mixture of sliding window attention and full attention). Zudem hat das Qwen-Team den Tokenizer verbessert, um mehrere natürliche Sprachen und Code besser zu verarbeiten."}, "qwen2.5": {"description": "Qwen2.5 ist das neue, groß angelegte Sprachmodell der Alibaba-Gruppe, das hervorragende Leistungen zur Unterstützung vielfältiger Anwendungsbedürfnisse bietet."}, "qwen2.5-14b-instruct": {"description": "Das 14B-<PERSON><PERSON> von <PERSON> 2.5 ist öffentlich zugänglich."}, "qwen2.5-14b-instruct-1m": {"description": "Tongyi Qianwen 2.5 ist ein Open-Source-Modell mit einer Größe von 72B."}, "qwen2.5-32b-instruct": {"description": "Das 32B-<PERSON><PERSON> von <PERSON> 2.5 ist öffentlich zugänglich."}, "qwen2.5-72b-instruct": {"description": "Das 72B-<PERSON><PERSON> von <PERSON> 2.5 ist öffentlich zugänglich."}, "qwen2.5-7b-instruct": {"description": "Das 7B-<PERSON><PERSON> von <PERSON> 2.5 ist öffentlich zugänglich."}, "qwen2.5-coder-1.5b-instruct": {"description": "Die Open-Source-Version des Qwen-Codemodells."}, "qwen2.5-coder-14b-instruct": {"description": "Open-Source-Version des Tongyi Qianwen Codierungsmodells."}, "qwen2.5-coder-32b-instruct": {"description": "Open-Source-Version des Tongyi Qianwen Code-Modells."}, "qwen2.5-coder-7b-instruct": {"description": "Die Open-Source-Version des Tongyi Qianwen Code-Modells."}, "qwen2.5-coder-instruct": {"description": "Qwen2.5-Coder ist das neueste Modell der Qwen-Serie, speziell für den Codeentwicklungsbereich entwickelt (früher bekannt als CodeQwen)."}, "qwen2.5-instruct": {"description": "Qwen2.5 ist die neueste Serie des Qwen-Sprachmodells. Für Qwen2.5 haben wir mehrere Basis-Sprachmodelle und instruktionsfeinjustierte Sprachmodelle ver<PERSON><PERSON><PERSON><PERSON><PERSON>, deren <PERSON> von 500 Millionen bis 7,2 Milliarden reichen."}, "qwen2.5-math-1.5b-instruct": {"description": "<PERSON> Qwen-Math-Modell verfügt über starke Fähigkeiten zur Lösung mathematischer Probleme."}, "qwen2.5-math-72b-instruct": {"description": "<PERSON> Qwen-Math-Modell verfügt über starke Fähigkeiten zur Lösung mathematischer Probleme."}, "qwen2.5-math-7b-instruct": {"description": "<PERSON> Qwen-Math-Modell verfügt über starke Fähigkeiten zur Lösung mathematischer Probleme."}, "qwen2.5-omni-7b": {"description": "Das Qwen-Omni-Modell der Serie unterstützt die Eingabe verschiedener Modalitäten, einschließlich Video, Audio, Bilder und Text, und gibt Audio und Text aus."}, "qwen2.5-vl-32b-instruct": {"description": "Die Qwen2.5-VL-Modellreihe verbessert die Intelligenz, Praktikabilität und Anwendbarkeit des Modells, sodass es in Szenarien wie natürlichen Dialogen, Inhaltserstellung, Fachwissensdiensten und Codeentwicklung besser abschneidet. Die 32B-Version verwendet Techniken des verstärkenden Lernens zur Optimierung des Modells. Im Vergleich zu anderen Modellen der Qwen2.5-VL-Reihe bietet sie einen für Menschen präferierten Ausgabe-Stil, Fähigkeiten zur Inferenz komplexer mathematischer Probleme sowie die Fähigkeit zur feingranularen Bildverarbeitung und -inferenz."}, "qwen2.5-vl-72b-instruct": {"description": "Verbesserte Befolgung von Anwei<PERSON>gen, Mathematik, Problemlösung und Programmierung, gesteigerte Erkennungsfähigkeiten für alle Arten von visuellen Elementen, Unterstützung für die präzise Lokalisierung visueller Elemente in verschiedenen Formaten, Verständnis von langen Videodateien (maximal 10 Minuten) und sekundengenauer Ereigniszeitpunktlokalisierung, Fähigkeit zur zeitlichen Einordnung und Geschwindigkeitsverständnis, Unterstützung für die Steuerung von OS- oder Mobile-Agenten basierend auf Analyse- und Lokalisierungsfähigkeiten, starke Fähigkeit zur Extraktion von Schlüsselinformationen und JSON-Format-Ausgabe. Diese Version ist die leistungsstärkste Version der 72B-Serie."}, "qwen2.5-vl-7b-instruct": {"description": "Verbesserte Befolgung von Anwei<PERSON>gen, Mathematik, Problemlösung und Programmierung, gesteigerte Erkennungsfähigkeiten für alle Arten von visuellen Elementen, Unterstützung für die präzise Lokalisierung visueller Elemente in verschiedenen Formaten, Verständnis von langen Videodateien (maximal 10 Minuten) und sekundengenauer Ereigniszeitpunktlokalisierung, Fähigkeit zur zeitlichen Einordnung und Geschwindigkeitsverständnis, Unterstützung für die Steuerung von OS- oder Mobile-Agenten basierend auf Analyse- und Lokalisierungsfähigkeiten, starke Fähigkeit zur Extraktion von Schlüsselinformationen und JSON-Format-Ausgabe. Diese Version ist die leistungsstärkste Version der 72B-Serie."}, "qwen2.5-vl-instruct": {"description": "Qwen2.5-VL ist die neueste Version des visuellen Sprachmodells in der Qwen-Modellfamilie."}, "qwen2.5:0.5b": {"description": "Qwen2.5 ist das neue, groß angelegte Sprachmodell der Alibaba-Gruppe, das hervorragende Leistungen zur Unterstützung vielfältiger Anwendungsbedürfnisse bietet."}, "qwen2.5:1.5b": {"description": "Qwen2.5 ist das neue, groß angelegte Sprachmodell der Alibaba-Gruppe, das hervorragende Leistungen zur Unterstützung vielfältiger Anwendungsbedürfnisse bietet."}, "qwen2.5:72b": {"description": "Qwen2.5 ist das neue, groß angelegte Sprachmodell der Alibaba-Gruppe, das hervorragende Leistungen zur Unterstützung vielfältiger Anwendungsbedürfnisse bietet."}, "qwen2:0.5b": {"description": "Qwen2 ist das neue große Sprachmodell von <PERSON>, das mit hervorragender Leistung eine Vielzahl von Anwendungsanforderungen unterstützt."}, "qwen2:1.5b": {"description": "Qwen2 ist das neue große Sprachmodell von <PERSON>, das mit hervorragender Leistung eine Vielzahl von Anwendungsanforderungen unterstützt."}, "qwen2:72b": {"description": "Qwen2 ist das neue große Sprachmodell von <PERSON>, das mit hervorragender Leistung eine Vielzahl von Anwendungsanforderungen unterstützt."}, "qwen3": {"description": "Qwen3 ist das neue, groß<PERSON><PERSON><PERSON><PERSON> S<PERSON>ch<PERSON>dell von <PERSON>, das mit hervorragender Leistung vielfältige Anwendungsbedürfnisse unterstützt."}, "qwen3-0.6b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-1.7b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-14b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-235b-a22b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-235b-a22b-instruct-2507": {"description": "Open-Source-Modell im nicht-denkenden Modus basierend auf Qwen3, mit leichten Verbesserungen in subjektiver Kreativität und Modellsicherheit gegenüber der Vorgängerversion (Tongyi Qianwen 3-235B-A22B)."}, "qwen3-235b-a22b-thinking-2507": {"description": "Open-Source-Modell im Denkmodus basierend auf Qwen3, mit erheblichen Verbesserungen in Logik, allgemeinen Fähigkeiten, Wissensabdeckung und Kreativität gegenüber der Vorgängerversion (Tongyi Qianwen 3-235B-A22B). Geeignet für anspruchsvolle und stark schlussfolgernde Szenarien."}, "qwen3-30b-a3b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-30b-a3b-instruct-2507": {"description": "Im Vergleich zur vorherigen Version (Qwen3-30B-A3B) wurde die allgemeine Leistungsfähigkeit in Chinesisch, Englisch und mehreren Sprachen deutlich verbessert. Spezielle Optimierungen für subjektive und offene Aufgaben führen zu einer deutlich besseren Übereinstimmung mit den Nutzerpräferenzen und ermöglichen hilfreichere Antworten."}, "qwen3-30b-a3b-thinking-2507": {"description": "Basierend auf dem Denkmodus-Open-Source-Model<PERSON> von Qwen3 wurden im Vergleich zur vorherigen Version (Tongyi Qianwen 3-30B-A3B) die logischen Fähigkeiten, die allgemeine Leistungsfähigkeit, das Wissen und die Kreativität erheblich verbessert. Es eignet sich für anspruchsvolle Szenarien mit starker Argumentation."}, "qwen3-32b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-4b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-8b": {"description": "Qwen3 ist ein neues, leistungsstarkes Modell der nächsten Generation, das in den Bereichen Inferenz, Allgemeinwissen, Agenten und Mehrsprachigkeit erhebliche Fortschritte erzielt hat und den Wechsel zwischen Denkmodi unterstützt."}, "qwen3-coder-480b-a35b-instruct": {"description": "Open-Source-Code-<PERSON><PERSON> von <PERSON>. Das neueste qwen3-coder-480b-a35b-instruct basiert auf Qwen3, verfügt über starke Coding-Agent-Fähigkeiten, ist versiert im Werkzeugaufruf und in der Umgebungskommunikation und ermöglicht selbstständiges Programmieren mit hervorragender Codequalität und allgemeinen Fähigkeiten."}, "qwen3-coder-plus": {"description": "Tongyi Qianwen Code-Modell. Die neueste Qwen3-Coder-Plus-Serie basiert auf Qwen3, verfügt über starke Coding-Agent-Fähigkeiten, ist versiert im Werkzeugaufruf und in der Umgebungskommunikation und ermöglicht selbstständiges Programmieren mit hervorragender Codequalität und allgemeinen Fähigkeiten."}, "qwq": {"description": "QwQ ist ein experimentelles Forschungsmodell, das sich auf die Verbesserung der KI-Inferenzfähigkeiten konzentriert."}, "qwq-32b": {"description": "Das QwQ-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, das auf dem Qwen2.5-32B-<PERSON><PERSON> trainiert wurde, hat durch verstärktes Lernen die Inferenzfähigkeiten des Modells erheblich verbessert. Die Kernmetriken des Modells, wie mathematische Codes (AIME 24/25, LiveCodeBench) sowie einige allgemeine Metriken (IFEval, LiveBench usw.), erreichen das Niveau der DeepSeek-R1 Vollversion, wobei alle Metriken deutlich die ebenfalls auf Qwen2.5-32B basierende DeepSeek-R1-Distill-Qwen-32B übertreffen."}, "qwq-32b-preview": {"description": "Das QwQ-Modell ist ein experimentelles Forschungsmodell, das vom Qwen-Team entwickelt wurde und sich auf die Verbesserung der KI-Inferenzfähigkeiten konzentriert."}, "qwq-plus": {"description": "Das QwQ-Inferenzmodell basiert auf dem Qwen2.5-Modell und verbessert die Modellschlussfolgerungsfähigkeiten durch verstärkendes Lernen erheblich. Die Kernmetriken in Mathematik und Programmierung (AIME 24/25, LiveCodeBench) sowie einige allgemeine Metriken (IFEval, LiveBench usw.) erreichen das volle Leistungsniveau von DeepSeek-R1."}, "qwq_32b": {"description": "Ein mittelgroßes Schlussfolgerungsmodell der Qwen-Serie. Im Vergleich zu traditionellen Modellen mit Anweisungsoptimierung zeigt QwQ, das über Denk- und Schlussfolgerungsfähigkeiten verfügt, in nachgelagerten Aufgaben, insbesondere bei der Lösung schwieriger Probleme, eine signifikante Leistungssteigerung."}, "r1-1776": {"description": "R1-1776 ist eine Version des DeepSeek R1 Modells, die nachtrainiert wurde, um unverfälschte, unvoreingenommene Fakteninformationen bereitzustellen."}, "solar-mini": {"description": "Solar Mini ist ein kompaktes LLM, das besser abschneidet als GPT-3.5 und über starke Mehrsprachigkeitsfähigkeiten verfügt. Es unterstützt Englisch und Koreanisch und bietet eine effiziente und kompakte Lösung."}, "solar-mini-ja": {"description": "Solar Mini (Ja) erweitert die Fähigkeiten von Solar Mini und konzentriert sich auf Japanisch, während es gleichzeitig in der Nutzung von Englisch und Koreanisch hohe Effizienz und hervorragende Leistung beibehält."}, "solar-pro": {"description": "Solar Pro ist ein hochintelligentes LLM, das von Upstage entwickelt wurde und sich auf die Befolgung von Anweisungen mit einer einzigen GPU konzentriert, mit einem IFEval-Score von über 80. Derzeit unterstützt es Englisch, die offizielle Version ist für November 2024 geplant und wird die Sprachunterstützung und Kontextlänge erweitern."}, "sonar": {"description": "Ein leichtgewichtiges Suchprodukt, das auf kontextbezogener Suche basiert und schneller und günstiger ist als Sonar Pro."}, "sonar-deep-research": {"description": "Deep Research führt umfassende Expertenforschung durch und fasst diese in zugänglichen, umsetzbaren Berichten zusammen."}, "sonar-pro": {"description": "Ein fortschrittliches Suchprodukt, das kontextbezogene Suche unterstützt und erweiterte Abfragen sowie Nachverfolgung ermöglicht."}, "sonar-reasoning": {"description": "Ein neues API-Produkt, das von DeepSeek-Inferenzmodellen unterstützt wird."}, "sonar-reasoning-pro": {"description": "Ein neues API-Produkt, das von dem DeepSeek-Inferenzmodell unterstützt wird."}, "stable-diffusion-3-medium": {"description": "Das neueste Text-zu-Bil<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Stability AI. Diese Version verbessert signifikant Bildqualität, Textverständnis und Stilvielfalt gegenüber Vorgängerversionen, kann komplexe natürliche Sprachaufforderungen präziser interpretieren und erzeugt genauere und vielfältigere Bilder."}, "stable-diffusion-3.5-large": {"description": "stable-diffusion-3.5-large ist ein multimodaler Diffusions-Transformer (MMDiT) mit 800 Millionen Parametern für Text-zu-Bild-Generierung, bietet herausragende Bildqualität und Prompt-Übereinstimmung, unterstützt die Erzeugung von Bildern mit bis zu 1 Million Pixeln und läuft effizient auf handelsüblicher Hardware."}, "stable-diffusion-3.5-large-turbo": {"description": "stable-diffusion-3.5-large-turbo basiert auf stable-diffusion-3.5-large und verwendet adversariale Diffusionsdestillation (ADD) für höhere Geschwindigkeit."}, "stable-diffusion-v1.5": {"description": "stable-diffusion-v1.5 wurde mit den Gewichten des stable-diffusion-v1.2 Checkpoints initialisiert und mit 595k Schritten bei 512x512 Auflösung auf „laion-aesthetics v2 5+“ feinabgestimmt. Dabei wurde die Textkonditionierung um 10 % reduziert, um die geführte Stichprobenahme ohne Klassifikator zu verbessern."}, "stable-diffusion-xl": {"description": "stable-diffusion-xl bringt bedeutende Verbesserungen gegenüber v1.5 und erreicht eine Qualität, die mit dem aktuellen Open-Source-Text-zu-Bild-SOTA-Modell Midjourney vergleichbar ist. Zu den Verbesserungen zählen ein dreimal größeres UNet-Backbone, ein Verfeinerungsmodul zur Qualitätssteigerung der generierten Bilder sowie effizientere Trainingstechniken."}, "stable-diffusion-xl-base-1.0": {"description": "Ein von Stability AI entwickeltes und Open-Source-Text-zu-Bild-Großmodell mit branchenführender kreativer Bildgenerierungsfähigkeit. Es verfügt über exzellente Instruktionsverständnisfähigkeiten und unterstützt die Definition von Inverse Prompts zur präzisen Inhaltserzeugung."}, "step-1-128k": {"description": "Bietet ein ausgewogenes Verhältnis zwischen Leistung und Kosten, geeignet für allgemeine Szenarien."}, "step-1-256k": {"description": "Verfügt über die Fähigkeit zur Verarbeitung ultra-langer Kontexte, besonders geeignet für die Analyse langer Dokumente."}, "step-1-32k": {"description": "Unterstützt mittellange Dialoge und eignet sich für verschiedene Anwendungsszenarien."}, "step-1-8k": {"description": "<PERSON><PERSON><PERSON>, geeignet für leichte Aufgaben."}, "step-1-flash": {"description": "Hochgeschwindigkeitsmodell, geeignet für Echtzeitdialoge."}, "step-1.5v-mini": {"description": "Dieses Modell verfügt über starke Fähigkeiten zur Videoanalyse."}, "step-1o-turbo-vision": {"description": "Dieses Modell verfügt über starke Fähigkeiten zur Bildverständnis und übertrifft 1o in den Bereichen Mathematik und Programmierung. Das Modell ist kleiner als 1o und bietet eine schnellere Ausgabegeschwindigkeit."}, "step-1o-vision-32k": {"description": "Dieses Modell verfügt über starke Fähigkeiten zur Bildverständnis. Im Vergleich zu den Modellen der Schritt-1v-Serie bietet es eine verbesserte visuelle Leistung."}, "step-1v-32k": {"description": "Unterstützt visuelle Eingaben und verbessert die multimodale Interaktionserfahrung."}, "step-1v-8k": {"description": "Kleinvisualmodell, geeignet für grundlegende Text- und Bildaufgaben."}, "step-1x-edit": {"description": "Dieses Modell ist auf Bildbearbeitungsaufgaben spezialisiert und kann Bilder basierend auf vom Nutzer bereitgestellten Bildern und Textbeschreibungen modifizieren und verbessern. Es unterstützt verschiedene Eingabeformate, einschließlich Textbeschreibungen und Beispielbilder, versteht die Nutzerintention und erzeugt entsprechende Bildbearbeitungsergebnisse."}, "step-1x-medium": {"description": "Dieses Modell verfügt über starke Bildgenerierungsfähigkeiten und unterstützt Texteingaben. Es bietet native chinesische Unterstützung, versteht und verarbeitet chinesische Textbeschreibungen besser, erfasst semantische Informationen präziser und wandelt sie in Bildmerkmale um, um genauere Bildgenerierung zu ermöglichen. Das Modell erzeugt hochauflösende, qualitativ hochwertige Bilder und besitzt eine gewisse Stilübertragungsfähigkeit."}, "step-2-16k": {"description": "Unterstützt groß angelegte Kontextinteraktionen und eignet sich für komplexe Dialogszenarien."}, "step-2-16k-exp": {"description": "Experimentelle Version des step-2 Modells, die die neuesten Funktionen enthält und kontinuierlich aktualisiert wird. Nicht für den Einsatz in produktiven Umgebungen empfohlen."}, "step-2-mini": {"description": "Ein ultraschnelles Großmodell, das auf der neuen, selbstentwickelten Attention-Architektur MFA basiert. Es erreicht mit extrem niedrigen Kosten ähnliche Ergebnisse wie Schritt 1 und bietet gleichzeitig eine höhere Durchsatzrate und schnellere Reaktionszeiten. Es kann allgemeine Aufgaben bearbeiten und hat besondere Fähigkeiten im Bereich der Codierung."}, "step-2x-large": {"description": "Das neue Generationen-Bild<PERSON><PERSON> von Step Star konzentriert sich auf Bildgenerierung und kann basierend auf Textbeschreibungen des Nutzers hochwertige Bilder erzeugen. Das neue Modell erzeugt realistischere Bildtexturen und bietet verbesserte Fähigkeiten bei der Erzeugung chinesischer und englischer Schriftzeichen."}, "step-r1-v-mini": {"description": "Dieses Modell ist ein leistungsstarkes Schlussfolgerungsmodell mit starker Bildverständnisfähigkeit, das in der Lage ist, Bild- und Textinformationen zu verarbeiten und nach tiefem Denken Textinhalte zu generieren. Es zeigt herausragende Leistungen im Bereich der visuellen Schlussfolgerung und verfügt über erstklassige Fähigkeiten in Mathematik, Programmierung und Textschlussfolgerung. Die Kontextlänge beträgt 100k."}, "taichu_llm": {"description": "Das Zīdōng Taichu Sprachmodell verfügt über außergewöhnliche Sprachverständnisfähigkeiten sowie Fähigkeiten in Textgenerierung, Wissensabfrage, Programmierung, mathematischen Berechnungen, logischem Denken, Sentimentanalyse und Textzusammenfassung. Es kombiniert innovativ große Datenvortrainings mit reichhaltigem Wissen aus mehreren Quellen, verfeinert kontinuierlich die Algorithmen und absorbiert ständig neues Wissen aus umfangreichen Textdaten in Bezug auf Vokabular, Struktur, Grammatik und Semantik, um die Leistung des Modells kontinuierlich zu verbessern. Es bietet den Nutzern bequemere Informationen und Dienstleistungen sowie ein intelligenteres Erlebnis."}, "taichu_o1": {"description": "taichu_o1 ist ein neues großes Schlussfolgerungsmodell, das durch multimodale Interaktion und verstärktes Lernen menschenähnliche Denkprozesse ermöglicht, komplexe Entscheidungsfindungen unterstützt und dabei präzise Ausgaben liefert, während es die Denkpfade des Modells zeigt. Es eignet sich für Szenarien wie strategische Analysen und tiefes Denken."}, "taichu_vl": {"description": "Integriert Fähigkeiten wie Bildverständnis, Wissensübertragung und logische Attribution und zeigt herausragende Leistungen im Bereich der Bild-Text-Fragen."}, "tencent/Hunyuan-A13B-Instruct": {"description": "Hunyuan-A13B-Instruct verfügt über 80 Milliarden Parameter, von denen 13 Milliarden aktiviert werden können, um mit größeren Modellen zu konkurrieren. Es unterstützt eine hybride Denkweise aus „schnellem Denken/langsamem Denken“; die Verarbeitung langer Texte ist stabil; durch BFCL-v3 und τ-Bench validiert, übertrifft die Agentenfähigkeit andere Modelle; in Kombination mit GQA und mehreren Quantisierungsformaten ermöglicht es effiziente Inferenz."}, "text-embedding-3-large": {"description": "Das leistungsstärkste Vektormodell, geeignet für englische und nicht-englische Aufgaben."}, "text-embedding-3-small": {"description": "Effizientes und kostengünstiges neues Embedding-Modell, geeignet für Wissensabruf, RAG-Anwendungen und andere Szenarien."}, "thudm/glm-4-32b": {"description": "GLM-4-32B-0414 ist ein 32B zweisprachiges (Chinesisch-Englisch) offenes Gewicht Sprachmodell, das für die Codegenerierung, Funktionsaufrufe und agentenbasierte Aufgaben optimiert wurde. Es wurde auf 15T hochwertigen und wiederholten Daten vortrainiert und weiter verfeinert durch menschliche Präferenzanpassung, Ablehnungs-Sampling und Verstärkungslernen. Das Modell zeigt hervorragende Leistungen bei komplexem Denken, Artefakterstellung und strukturierten Ausgaben und erreicht in mehreren Benchmark-Tests eine Leistung, die mit GPT-4o und DeepSeek-V3-0324 vergleichbar ist."}, "thudm/glm-4-32b:free": {"description": "GLM-4-32B-0414 ist ein 32B zweisprachiges (Chinesisch-Englisch) offenes Gewicht Sprachmodell, das für die Codegenerierung, Funktionsaufrufe und agentenbasierte Aufgaben optimiert wurde. Es wurde auf 15T hochwertigen und wiederholten Daten vortrainiert und weiter verfeinert durch menschliche Präferenzanpassung, Ablehnungs-Sampling und Verstärkungslernen. Das Modell zeigt hervorragende Leistungen bei komplexem Denken, Artefakterstellung und strukturierten Ausgaben und erreicht in mehreren Benchmark-Tests eine Leistung, die mit GPT-4o und DeepSeek-V3-0324 vergleichbar ist."}, "thudm/glm-4-9b-chat": {"description": "Die Open-Source-Version des neuesten vortrainierten Modells der GLM-4-Serie, das von Zhizhu AI veröffentlicht wurde."}, "thudm/glm-4-9b:free": {"description": "GLM-4-9B-0414 ist ein Sprachmodell mit 9 Milliarden Parametern aus der GLM-4-Serie, das von THUDM entwickelt wurde. GLM-4-9B-0414 verwendet die gleichen Verstärkungs- und Ausrichtungsstrategien wie das größere 32B-Modell und erzielt in Bezug auf seine Größe hohe Leistungen, was es für ressourcenbeschränkte Bereitstellungen geeignet macht, die dennoch starke Sprachverständnis- und Generierungsfähigkeiten erfordern."}, "thudm/glm-z1-32b": {"description": "GLM-Z1-32B-0414 ist eine verbesserte Denkvariante von GLM-4-32B, die für tiefgehende Mathematik, Logik und codeorientierte Problemlösungen entwickelt wurde. Es verwendet erweiterte Verstärkungslernen (aufgabenspezifisch und basierend auf allgemeinen Paarpräferenzen), um die Leistung bei komplexen mehrstufigen Aufgaben zu verbessern. Im Vergleich zum Basis-GLM-4-32B-Modell hat Z1 die Fähigkeiten im strukturierten Denken und im formalen Bereich erheblich verbessert.\n\nDieses Modell unterstützt die Durchsetzung von \"Denk\"-Schritten durch Prompt-Engineering und bietet verbesserte Kohärenz für Ausgaben im Langformat. Es ist für Agenten-Workflows optimiert und unterstützt langen Kontext (über YaRN), JSON-Toolaufrufe und feinkörnige Sampling-Konfigurationen für stabiles Denken. Besonders geeignet für Anwendungsfälle, die durchdachtes, mehrstufiges Denken oder formale Ableitungen erfordern."}, "thudm/glm-z1-32b:free": {"description": "GLM-Z1-32B-0414 ist eine verbesserte Denkvariante von GLM-4-32B, die für tiefgehende Mathematik, Logik und codeorientierte Problemlösungen entwickelt wurde. Es verwendet erweiterte Verstärkungslernen (aufgabenspezifisch und basierend auf allgemeinen Paarpräferenzen), um die Leistung bei komplexen mehrstufigen Aufgaben zu verbessern. Im Vergleich zum Basis-GLM-4-32B-Modell hat Z1 die Fähigkeiten im strukturierten Denken und im formalen Bereich erheblich verbessert.\n\nDieses Modell unterstützt die Durchsetzung von \"Denk\"-Schritten durch Prompt-Engineering und bietet verbesserte Kohärenz für Ausgaben im Langformat. Es ist für Agenten-Workflows optimiert und unterstützt langen Kontext (über YaRN), JSON-Toolaufrufe und feinkörnige Sampling-Konfigurationen für stabiles Denken. Besonders geeignet für Anwendungsfälle, die durchdachtes, mehrstufiges Denken oder formale Ableitungen erfordern."}, "thudm/glm-z1-9b:free": {"description": "GLM-Z1-9B-0414 ist ein Sprachmodell mit 9B Parametern aus der GLM-4-Serie, das von THUDM entwickelt wurde. Es verwendet Techniken, die ursprünglich auf das größere GLM-Z1-<PERSON><PERSON> angewendet wurden, einsch<PERSON>ß<PERSON> erweiterten verstärkten Lernens, paarweiser Rangordnungsausrichtung und Training für inferenzintensive Aufgaben wie Mathematik, Programmierung und Logik. Trotz seiner kleineren Größe zeigt es starke Leistungen bei allgemeinen Inferenzaufgaben und übertrifft viele Open-Source-Modelle in Bezug auf seine Gewichtung."}, "thudm/glm-z1-rumination-32b": {"description": "THUDM: GLM Z1 Rumination 32B ist ein tiefes Inferenzmodell mit 32B Parametern aus der GLM-4-Z1-Serie, das für komplexe, offene Aufgaben optimiert wurde, die langes Nachdenken erfordern. Es basiert auf glm-4-32b-0414 und hat zusätzliche Phasen des verstärkten Lernens und mehrstufige Ausrichtungsstrategien hinzugefügt, die die \"Reflexions\"-Fähigkeit einführen, die darauf abzielt, erweiterte kognitive Prozesse zu simulieren. Dazu gehören iterative Inferenz, mehrstufige Analysen und werkzeuggestützte Arbeitsabläufe wie Suche, Abruf und zitationsbewusste Synthese.\n\nDieses Modell zeigt hervorragende Leistungen in forschungsorientiertem Schreiben, vergleichender Analyse und komplexen Fragen und Antworten. Es unterstützt Funktionsaufrufe für Such- und Navigationsprimitiven (`search`, `click`, `open`, `finish`), sodass es in agentenbasierten Pipelines verwendet werden kann. Reflexionsverhalten wird durch ein mehrstufiges Regelbelohnungssystem und verzögerte Entscheidungsmechanismen geformt und wird an tiefen Forschungsrahmen wie dem internen Ausrichtungsstapel von OpenAI gemessen. Diese Variante eignet sich für Szenarien, die Tiefe statt Geschwindigkeit erfordern."}, "tngtech/deepseek-r1t-chimera:free": {"description": "DeepSeek-R1T-Chimera wurde durch die Kombination von DeepSeek-R1 und DeepSeek-V3 (0324) erstellt und vereint die Inferenzfähigkeiten von R1 mit den Verbesserungen der Token-Effizienz von V3. Es basiert auf der DeepSeek-MoE Transformer-Architektur und wurde für allgemeine Textgenerierungsaufgaben optimiert.\n\nDieses Modell kombiniert die vortrainierten Gewichte der beiden Quellmodelle, um die Leistung in Inferenz, Effizienz und Befolgung von Anweisungen auszugleichen. Es wird unter der MIT-Lizenz veröffentlicht und ist für Forschungs- und kommerzielle Zwecke gedacht."}, "togethercomputer/StripedHyena-Nous-7B": {"description": "StripedHyena Nous (7B) bietet durch effiziente Strategien und Modellarchitekturen verbesserte Rechenfähigkeiten."}, "tts-1": {"description": "Das neueste Text-zu-<PERSON><PERSON><PERSON>-Modell, optimiert für Geschwindigkeit in Echtzeitszenarien."}, "tts-1-hd": {"description": "Das neueste Text-zu-<PERSON><PERSON><PERSON>-Modell, optimiert für Qualität."}, "upstage/SOLAR-10.7B-Instruct-v1.0": {"description": "Upstage SOLAR Instruct v1 (11B) eignet sich für präzise Anweisungsaufgaben und bietet hervorragende Sprachverarbeitungsfähigkeiten."}, "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {"description": "Claude 3.5 Son<PERSON> hebt den Branchenstandard an, übertrifft die Konkurrenzmodelle und Claude 3 Opus und zeigt in umfangreichen Bewertungen hervorragende Leistungen, während es die Geschwindigkeit und Kosten unserer mittelgroßen Modelle beibehält."}, "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {"description": "Claude 3.7 <PERSON>ett ist das schnellste nächste Modell von Anthropic. Im Vergleich zu Claude 3 Hai<PERSON> hat <PERSON> 3.7 <PERSON><PERSON> in allen Fähigkeiten Verbesserungen erfahren und übertrifft in vielen intellektuellen Benchmark-Tests das größte Modell der vorherigen Generation, Claude 3 Opus."}, "v0-1.0-md": {"description": "Das Modell v0-1.0-md ist ein älteres Modell, das über die v0 API bereitgestellt wird"}, "v0-1.5-lg": {"description": "Das Modell v0-1.5-lg eignet sich für anspruchsvolle Denk- oder Schlussfolgerungsaufgaben"}, "v0-1.5-md": {"description": "Das Modell v0-1.5-md ist für alltägliche Aufgaben und die Generierung von Benutzeroberflächen (UI) geeignet"}, "wan2.2-t2i-flash": {"description": "Wanxiang 2.2 Turbo-Version, das aktuell neueste Modell. Es bietet umfassende Verbesserungen in Kreativität, Stabilität und realistischer Textur, erzeugt schnell und bietet ein hervorragendes Preis-Leistungs-Verhältnis."}, "wan2.2-t2i-plus": {"description": "Wanxiang 2.2 Professional-Version, das aktuell neueste Modell. Es bietet umfassende Verbesserungen in Kreativität, Stabilität und realistischer Textur mit reichhaltigen Details."}, "wanx-v1": {"description": "Basis-Text-zu-Bild-Modell. Entspricht dem allgemeinen Modell 1.0 auf der offiziellen Tongyi Wanxiang Webseite."}, "wanx2.0-t2i-turbo": {"description": "Spezialisiert auf realistische Porträts, mittlere Geschwindigkeit und niedrige Kosten. Entspricht dem Turbo-Modell 2.0 auf der offiziellen Tongyi Wanxiang Webseite."}, "wanx2.1-t2i-plus": {"description": "Vollständig aufgerüstete Version mit reichhaltigeren Bilddetails, etwas langsamer. Entspricht dem professionellen Modell 2.1 auf der offiziellen Tongyi Wanxiang Webseite."}, "wanx2.1-t2i-turbo": {"description": "Vollständig aufgerüstete Version mit schneller Generierung, umfassender Leistung und hervorragendem Preis-Leistungs-Verhältnis. Entspricht dem Turbo-Modell 2.1 auf der offiziellen Tongyi Wanxiang Webseite."}, "whisper-1": {"description": "Universelles Spracherkennungsmodell, unterstützt mehrsprachige Spracherkennung, Sprachübersetzung und Spracherkennung."}, "wizardlm2": {"description": "WizardLM 2 ist ein Sprachmodell von Microsoft AI, das in komplexen Dialogen, mehrsprachigen Anwendungen, Schlussfolgerungen und intelligenten Assistenten besonders gut abschneidet."}, "wizardlm2:8x22b": {"description": "WizardLM 2 ist ein Sprachmodell von Microsoft AI, das in komplexen Dialogen, mehrsprachigen Anwendungen, Schlussfolgerungen und intelligenten Assistenten besonders gut abschneidet."}, "x1": {"description": "Das Spark X1 Modell wird weiter verbessert und erreicht in allgemeinen Aufgaben wie Schlussfolgerungen, Textgenerierung und Sprachverständnis Ergebnisse, die mit OpenAI o1 und DeepSeek R1 vergleichbar sind, basierend auf der bereits führenden Leistung in mathematischen Aufgaben."}, "yi-1.5-34b-chat": {"description": "Yi-1.5 ist eine verbesserte Version von Yi. Es wurde mit einem hochwertigen Korpus von 500B Tokens auf Yi fortlaufend vortrainiert und auf 3M diversen Feinabstimmungsbeispielen feinjustiert."}, "yi-large": {"description": "Das brandneue Modell mit einer Billion Parametern bietet außergewöhnliche Frage- und Textgenerierungsfähigkeiten."}, "yi-large-fc": {"description": "Basierend auf dem yi-large-Modell unterstützt und verstärkt es die Fähigkeit zu Werkzeugaufrufen und eignet sich für verschiedene Geschäftsszenarien, die den Aufbau von Agenten oder Workflows erfordern."}, "yi-large-preview": {"description": "<PERSON><PERSON>he Version, empfohlen wird die Verwendung von yi-large (neue Version)."}, "yi-large-rag": {"description": "Ein fortgeschrittener Dienst, der auf dem leistungsstarken yi-large-<PERSON>l basiert und präzise Antworten durch die Kombination von Abruf- und Generierungstechnologien bietet, sowie Echtzeit-Informationsdienste aus dem gesamten Web."}, "yi-large-turbo": {"description": "Hervorragendes Preis-Leistungs-Verhältnis und außergewöhnliche Leistung. Hochpräzise Feinabstimmung basierend auf Leistung, Schlussfolgerungsgeschwindigkeit und Kosten."}, "yi-lightning": {"description": "Das neueste Hochleistungsmodell, das hochwertige Ausgaben gewährleistet und gleichzeitig die Schlussfolgerungsgeschwindigkeit erheblich verbessert."}, "yi-lightning-lite": {"description": "Leichte Version, empfohlen wird die Verwendung von yi-lightning."}, "yi-medium": {"description": "Mittelgroßes Modell mit verbesserten Feinabstimmungen, ausgewogene Fähigkeiten und gutes Preis-Leistungs-Verhältnis. Tiefgehende Optimierung der Anweisungsbefolgung."}, "yi-medium-200k": {"description": "200K ultra-lange Kontextfenster bieten tiefes Verständnis und Generierungsfähigkeiten für lange Texte."}, "yi-spark": {"description": "Klein und kompakt, ein leichtgewichtiges und schnelles Modell. Bietet verbesserte mathematische Berechnungs- und Programmierfähigkeiten."}, "yi-vision": {"description": "Modell für komplexe visuelle Aufgaben, das hohe Leistungsfähigkeit bei der Bildverarbeitung und -analyse bietet."}, "yi-vision-v2": {"description": "Ein Modell für komplexe visuelle Aufgaben, das leistungsstarke Verständnis- und Analysefähigkeiten auf der Grundlage mehrerer Bilder bietet."}, "zai-org/GLM-4.5": {"description": "GLM-4.5 ist ein speziell für Agentenanwendungen entwickeltes Basismodell mit Mixture-of-Experts-Architektur. Es ist tief optimiert für Werkzeugaufrufe, Web-Browsing, Softwareentwicklung und Frontend-Programmierung und unterstützt nahtlos die Integration in Code-Agenten wie Claude Code und Roo Code. GLM-4.5 verwendet einen hybriden Inferenzmodus und ist für komplexe Schlussfolgerungen sowie den Alltagsgebrauch geeignet."}, "zai-org/GLM-4.5-Air": {"description": "GLM-4.5-Air ist ein speziell für Agentenanwendungen entwickeltes Basismodell mit Mixture-of-Experts-Architektur. Es ist tief optimiert für Werkzeugaufrufe, Web-Browsing, Softwareentwicklung und Frontend-Programmierung und unterstützt nahtlos die Integration in Code-Agenten wie Claude Code und Roo Code. GLM-4.5 verwendet einen hybriden Inferenzmodus und ist für komplexe Schlussfolgerungen sowie den Alltagsgebrauch geeignet."}}