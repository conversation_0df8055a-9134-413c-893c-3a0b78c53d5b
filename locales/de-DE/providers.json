{"ai21": {"description": "AI21 Labs entwickelt Basis-Modelle und KI-Systeme für Unternehmen und beschleunigt die Anwendung generativer KI in der Produktion."}, "ai360": {"description": "360 AI ist die von der 360 Company eingeführte Plattform für KI-Modelle und -Dienste, die eine Vielzahl fortschrittlicher Modelle zur Verarbeitung natürlicher Sprache anbietet, darunter 360GPT2 Pro, 360GPT Pro, 360GPT Turbo und 360GPT Turbo Responsibility 8K. Diese Modelle kombinieren große Parameter mit multimodalen Fähigkeiten und finden breite Anwendung in den Bereichen Textgenerierung, semantisches Verständnis, Dialogsysteme und Codegenerierung. Durch flexible Preisstrategien erfüllt 360 AI die vielfältigen Bedürfnisse der Nutzer, unterstützt Entwickler bei der Integration und fördert die Innovation und Entwicklung intelligenter Anwendungen."}, "aihubmix": {"description": "AiHubMix bietet über eine einheitliche API-Schnittstelle Zugriff auf verschiedene KI-Modelle."}, "anthropic": {"description": "Anthropic ist ein Unternehmen, das sich auf Forschung und Entwicklung im Bereich der künstlichen Intelligenz spezialisiert hat und eine Reihe fortschrittlicher Sprachmodelle anbietet, <PERSON><PERSON><PERSON> 3.5 <PERSON><PERSON>, <PERSON> 3 <PERSON><PERSON>, <PERSON> 3 Opus und Claude 3 Haiku. Diese Modelle erreichen ein ideales Gleichgewicht zwischen Intelligenz, Geschwindigkeit und Kosten und sind für eine Vielzahl von Anwendungsszenarien geeignet, von unternehmensweiten Arbeitslasten bis hin zu schnellen Reaktionen. <PERSON> 3.5 <PERSON><PERSON>, als neuestes Modell, hat in mehreren Bewertungen hervorragend abgeschnitten und bietet gleichzeitig ein hohes Preis-Leistungs-Verhältnis."}, "azure": {"description": "Azure bietet eine Vielzahl fortschrittlicher KI-Modelle, da<PERSON><PERSON> GPT-3.5 und die neueste GPT-4-Serie, die verschiedene Datentypen und komplexe Aufgaben unterstützen und sich auf sichere, zuverlässige und nachhaltige KI-Lösungen konzentrieren."}, "azureai": {"description": "Azure bietet eine Vielzahl fortschrittlicher KI-Modelle, da<PERSON><PERSON> GPT-3.5 und die neueste GPT-4-Serie, die verschiedene Datentypen und komplexe Aufgaben unterstützen und sich auf sichere, zuverlässige und nachhaltige KI-Lösungen konzentrieren."}, "baichuan": {"description": "Baichuan Intelligent ist ein Unternehmen, das sich auf die Forschung und Entwicklung großer KI-Modelle spezialisiert hat. Ihre Modelle zeigen hervorragende Leistungen in chinesischen Aufgaben wie Wissensdatenbanken, Verarbeitung langer Texte und kreative Generierung und übertreffen die gängigen Modelle im Ausland. Baichuan Intelligent verfügt auch über branchenführende multimodale Fähigkeiten und hat in mehreren renommierten Bewertungen hervorragend abgeschnitten. Ihre Modelle umfassen Baichuan 4, Baichuan 3 Turbo und Baichuan 3 Turbo 128k, die jeweils für unterschiedliche Anwendungsszenarien optimiert sind und kosteneffiziente Lösungen bieten."}, "bedrock": {"description": "Bedrock ist ein Service von Amazon AWS, der sich darauf konzen<PERSON>ert, Unternehmen fortschrittliche KI-Sprach- und visuelle Modelle bereitzustellen. Die Modellfamilie umfasst die Claude-Serie von Anthropic, die Llama 3.1-Serie von Meta und mehr, und bietet eine Vielzahl von Optionen von leichtgewichtig bis hochleistungsfähig, die Textgenerierung, Dialoge, Bildverarbeitung und andere Aufgaben unterstützen und für Unternehmensanwendungen unterschiedlicher Größen und Anforderungen geeignet sind."}, "cloudflare": {"description": "<PERSON><PERSON><PERSON> Si<PERSON> von <PERSON>n GPUs betriebene Machine-Learning-Modelle im globalen Netzwerk von Cloudflare aus."}, "cohere": {"description": "Cohere bringt Ihnen die fortschrittlichsten mehrsprachigen Modelle, leistungsstarke Suchfunktionen und einen maßgeschneiderten KI-Arbeitsbereich für moderne Unternehmen – alles integriert in einer sicheren Plattform."}, "deepseek": {"description": "DeepSeek ist ein Unternehmen, das sich auf die Forschung und Anwendung von KI-Technologien spezialisiert hat. Ihr neuestes Modell, DeepSeek-V2.5, kombiniert allgemeine Dialog- und Codeverarbeitungsfähigkeiten und hat signifikante Fortschritte in den Bereichen menschliche Präferenzanpassung, Schreibaufgaben und Befehlsbefolgung erzielt."}, "fal": {"description": "Generative Medienplattform für Entwickler"}, "fireworksai": {"description": "Fireworks AI ist ein führender Anbieter von fortschrittlichen Sprachmodellen, der sich auf Funktionsaufrufe und multimodale Verarbeitung spezialisiert hat. Ihr neuestes Modell, Firefunction V2, basiert auf Llama-3 und ist für Funktionsaufrufe, Dialoge und Befehlsbefolgung optimiert. Das visuelle Sprachmodell FireLLaVA-13B unterstützt gemischte Eingaben von Bildern und Text. Weitere bemerkenswerte Modelle sind die Llama-Serie und die Mixtral-Serie, die effiziente mehrsprachige Befehlsbefolgung und Generierungsunterstützung bieten."}, "giteeai": {"description": "Die serverlose API von Gitee AI bietet KI-Entwicklern einen sofort einsatzbereiten großen Modell-Inferenz-API-Service."}, "github": {"description": "<PERSON><PERSON>-Modellen können Entwickler zu KI-Ingenieuren werden und mit den führenden KI-Modellen der Branche arbeiten."}, "google": {"description": "Die Gemini-Serie von Google ist ihr fortschrittlichstes, universelles KI-Modell, das von Google DeepMind entwickelt wurde und speziell für multimodale Anwendungen konzipiert ist. Es unterstützt nahtlose Verständnis- und Verarbeitungsprozesse für Text, Code, Bilder, Audio und Video. Es ist für eine Vielzahl von Umgebungen geeignet, von Rechenzentren bis hin zu mobilen Geräten, und verbessert erheblich die Effizienz und Anwendbarkeit von KI-Modellen."}, "groq": {"description": "Der LPU-Inferenz-Engine von Groq hat in den neuesten unabhängigen Benchmark-Tests für große Sprachmodelle (LLM) hervorragende Leistungen gezeigt und definiert mit seiner erstaunlichen Geschwindigkeit und Effizienz die Standards für KI-Lösungen neu. Groq ist ein Beispiel für sofortige Inferenzgeschwindigkeit und zeigt in cloudbasierten Bereitstellungen eine gute Leistung."}, "higress": {"description": "Higress ist ein cloud-natives API-Gateway, das intern bei Alibaba entwickelt wurde, um die Probleme von Tengine Reload bei langanhaltenden Verbindungen zu lösen und die unzureichenden Lastverteilungsmöglichkeiten von gRPC/Dubbo zu verbessern."}, "huggingface": {"description": "Die HuggingFace Inference API bietet eine schnelle und kostenlose Möglichkeit, <PERSON><PERSON><PERSON> von Modellen für verschiedene Aufgaben zu erkunden. E<PERSON>, ob Sie Prototypen für neue Anwendungen erstellen oder die Funktionen des maschinellen Lernens ausprobieren, diese API ermöglicht Ihnen den sofortigen Zugriff auf leistungsstarke Modelle aus verschiedenen Bereichen."}, "hunyuan": {"description": "<PERSON> von Tencent entwickeltes großes Sprachmodell, das über starke Fähigkeiten zur Erstellung von Inhalten in chinesischer Sprache, logisches Denkvermögen in komplexen Kontexten und zuverlässige Fähigkeiten zur Aufgabenerfüllung verfügt."}, "infiniai": {"description": "Bietet Anwendungsentwicklern hochleistungs-fähige, benutzerfreundliche und sichere Dienste für große Modelle, die den gesamten Prozess von der Entwicklung großer Modelle bis hin zur Dienstbereitstellung abdecken."}, "internlm": {"description": "Eine Open-Source-Organisation, die sich der Forschung und Entwicklung von großen Modellen und Werkzeugketten widmet. Sie bietet allen KI-Entwicklern eine effiziente und benutzerfreundliche Open-Source-Plattform, die den Zugang zu den neuesten Technologien und Algorithmen für große Modelle ermöglicht."}, "jina": {"description": "Jina AI wurde 2020 gegründet und ist ein führendes Unternehmen im Bereich Such-KI. Unsere Suchplattform umfasst Vektormodelle, Re-Ranker und kleine Sprachmodelle, die Unternehmen dabei helfen, zuverlässige und qualitativ hochwertige generative KI- und multimodale Suchanwendungen zu entwickeln."}, "lmstudio": {"description": "LM Studio ist eine Desktop-Anwendung zum Entwickeln und Experimentieren mit LLMs auf Ihrem Computer."}, "minimax": {"description": "MiniMax ist ein im Jahr 2021 gegründetes Unternehmen für allgemeine künstliche Intelligenz, das sich der gemeinsamen Schaffung von Intelligenz mit den Nutzern widmet. MiniMax hat verschiedene multimodale allgemeine große Modelle entwickelt, da<PERSON><PERSON> ein Textmodell mit Billionen von Para<PERSON>n, ein Sprachmodell und ein Bildmodell. Außerdem wurden Anwendungen wie Conch AI eingeführt."}, "mistral": {"description": "Mistral bietet fortschrittliche allgemeine, spezialisierte und forschungsorientierte Modelle an, die in Bereichen wie komplexe Schlussfolgerungen, mehrsprachige Aufgaben und Code-Generierung weit verbreitet sind. Durch Funktionsaufrufschnittstellen können Benutzer benutzerdefinierte Funktionen integrieren und spezifische Anwendungen realisieren."}, "modelscope": {"description": "ModelScope ist eine von Alibaba Cloud eingeführte Plattform für Modelle als Dienstleistung, die eine Vielzahl von KI-Modellen und Inferenzdiensten anbietet."}, "moonshot": {"description": "Moonshot ist eine Open-Source-Plattform, die von Beijing Dark Side Technology Co., Ltd. eingeführt wurde und eine Vielzahl von Modellen zur Verarbeitung natürlicher Sprache anbietet, die in vielen Bereichen Anwendung finden, da<PERSON><PERSON>, aber nicht beschränkt auf, Inhaltserstellung, akademische Forschung, intelligente Empfehlungen und medizinische Diagnosen, und unterstützt die Verarbeitung langer Texte und komplexer Generierungsaufgaben."}, "novita": {"description": "Novita AI ist eine Plattform, die eine Vielzahl von großen Sprachmodellen und API-Diensten für die KI-Bilderzeugung anbietet, die flexibel, zuverlässig und kosteneffektiv ist. Sie unterstützt die neuesten Open-Source-Modelle wie Llama3 und Mistral und bietet umfassende, benutzerfreundliche und automatisch skalierbare API-Lösungen für die Entwicklung generativer KI-Anwendungen, die für das schnelle Wachstum von KI-Startups geeignet sind."}, "nvidia": {"description": "NVIDIA NIM™ bietet Container für selbstgehostete, GPU-beschleunigte Inferenz-Mikrodienste, die die Bereitstellung von vortrainierten und benutzerdefinierten KI-Modellen in der Cloud, in Rechenzentren, auf RTX™ AI-PCs und Workstations unterstützen."}, "ollama": {"description": "Die von Ollama angebotenen Modelle decken ein breites Spektrum ab, darunter Code-Generierung, mathematische Berechnungen, mehrsprachige Verarbeitung und dialogbasierte Interaktionen, und unterstützen die vielfältigen Anforderungen an unternehmensgerechte und lokal angepasste Bereitstellungen."}, "openai": {"description": "OpenAI ist eine weltweit führende Forschungsinstitution im Bereich der künstlichen Intelligenz, deren entwickelte Modelle wie die GPT-Serie die Grenzen der Verarbeitung natürlicher Sprache vorantreiben. OpenAI setzt sich dafür ein, durch innovative und effiziente KI-Lösungen verschiedene Branchen zu transformieren. Ihre Produkte zeichnen sich durch herausragende Leistung und Wirtschaftlichkeit aus und finden breite Anwendung in Forschung, Wirtschaft und innovativen Anwendungen."}, "openrouter": {"description": "OpenRouter ist eine Serviceplattform, die eine Vielzahl von Schnittstellen für fortschrittliche große Modelle anbietet und OpenAI, Anthropic, LLaMA und mehr unterstützt, um vielfältige Entwicklungs- und Anwendungsbedürfnisse zu erfüllen. Benutzer können je nach ihren Anforderungen flexibel das optimale Modell und den Preis auswählen, um das KI-Erlebnis zu verbessern."}, "perplexity": {"description": "Perplexity ist ein führender Anbieter von Dialoggenerierungsmodellen und bietet eine Vielzahl fortschrittlicher Llama 3.1-Modelle an, die sowohl für Online- als auch Offline-Anwendungen geeignet sind und sich besonders für komplexe Aufgaben der Verarbeitung natürlicher Sprache eignen."}, "ppio": {"description": "PPIO Paiou Cloud bietet stabile und kosteneffiziente Open-Source-Modell-API-Dienste und unterstützt die gesamte DeepSeek-Serie, Llama, Qwen und andere führende große Modelle der Branche."}, "qiniu": {"description": "<PERSON><PERSON> ist ein führender Anbieter von <PERSON>, der schnelle und effiziente API-Dienste für große Modelle bereitstellt, einsch<PERSON>ß<PERSON> der von Ali<PERSON>ba, mit flexiblen Optionen für das Entwickeln und Anwenden von AI-Anwendungen."}, "qwen": {"description": "Tongyi Qianwen ist ein von Ali<PERSON>ba Cloud selbst entwickeltes, groß angelegtes Sprachmodell mit starken Fähigkeiten zur Verarbeitung und Generierung natürlicher Sprache. Es kann eine Vielzahl von <PERSON><PERSON>, <PERSON><PERSON> erstellen, Meinungen äußern und Code schreiben und spielt in mehreren Bereichen eine Rolle."}, "sambanova": {"description": "SambaNova Cloud ermöglicht es Entwicklern, die besten Open-Source-Modelle einfach zu nutzen und von der schnellsten Inferenzgeschwindigkeit zu profitieren."}, "search1api": {"description": "Search1API bietet Zugriff auf die DeepSeek-Modellreihe, die bei Bedarf selbstständig online gehen kann, einschließlich der Standard- und Schnellversion, und unterstützt die Auswahl von Modellen in verschiedenen Parametergrößen."}, "sensenova": {"description": "SenseTime bietet mit der starken Basisunterstützung von SenseTimes großem Gerät effiziente und benutzerfreundliche Full-Stack-Modelldienste."}, "siliconcloud": {"description": "SiliconFlow hat sich zum <PERSON> g<PERSON>, AGI zu beschleunigen, um der Menschheit zu dienen, und die Effizienz großangelegter KI durch eine benutzerfreundliche und kostengünstige GenAI-Stack zu steigern."}, "spark": {"description": "Der Xinghuo-Großmodell von iFLYTEK bietet leistungsstarke KI-Fähigkeiten in mehreren Bereichen und Sprachen und nutzt fortschrittliche Technologien zur Verarbeitung natürlicher Sprache, um innovative Anwendungen für intelligente Hardware, intelligente Medizin, intelligente Finanzen und andere vertikale Szenarien zu entwickeln."}, "stepfun": {"description": "Das StepFun-Großmodell verfügt über branchenführende multimodale und komplexe Schlussfolgerungsfähigkeiten und unterstützt das Verständnis von sehr langen Texten sowie leistungsstarke, autonome Suchmaschinenfunktionen."}, "taichu": {"description": "Das Institut für Automatisierung der Chinesischen Akademie der Wissenschaften und das Wuhan Institute of Artificial Intelligence haben ein neues Generation multimodales großes Modell eingeführt, das umfassende Frage-Antwort-Aufgaben unterstützt, darunter mehrstufige Fragen, Textgenerierung, Bildgenerierung, 3D-Verständnis und Signalverarbeitung, mit stärkeren kognitiven, verstehenden und kreativen Fähigkeiten, die ein neues interaktives Erlebnis bieten."}, "tencentcloud": {"description": "Die atomare Fähigkeit der Wissensmaschine (LLM Knowledge Engine Atomic Power) basiert auf der Entwicklung der Wissensmaschine und bietet eine umfassende Fähigkeit zur Wissensabfrage für Unternehmen und Entwickler. Sie können mit verschiedenen atomaren Fähigkeiten Ihren eigenen Modellservice erstellen und Dokumentenanalysen, -aufteilungen, Embeddings, mehrfache Umformulierungen und andere Dienste kombinieren, um maßgeschneiderte KI-Lösungen für Ihr Unternehmen zu entwickeln."}, "togetherai": {"description": "Together AI strebt an, durch innovative KI-Modelle führende Leistungen zu erzielen und bietet umfangreiche Anpassungsmöglichkeiten, einschließlich schneller Skalierungsunterstützung und intuitiver Bereitstellungsprozesse, um den unterschiedlichen Anforderungen von Unternehmen gerecht zu werden."}, "upstage": {"description": "Upstage konzentriert sich auf die Entwicklung von KI-Modellen für verschiedene geschäftliche Anforderungen, einschließlich Solar LLM und Dokumenten-KI, mit dem Ziel, künstliche allgemeine Intelligenz (AGI) zu erreichen. Es ermöglicht die Erstellung einfacher Dialogagenten über die Chat-API und unterstützt Funktionsaufrufe, Übersetzungen, Einbettungen und spezifische Anwendungsbereiche."}, "v0": {"description": "v0 ist ein Pair-Programming-Assistent, bei dem Sie Ihre Ideen einfach in natürlicher Sprache beschreiben können, und er generiert Code und Benutzeroberflächen (UI) für Ihr Projekt."}, "vertexai": {"description": "Die Gemini-Serie von Google ist das fortschrittlichste, universelle KI-Modell, das von Google DeepMind entwickelt wurde. Es ist speziell für multimodale Anwendungen konzipiert und unterstützt das nahtlose Verständnis und die Verarbeitung von Text, Code, Bildern, Audio und Video. Es eignet sich für eine Vielzahl von Umgebungen, von Rechenzentren bis hin zu mobilen Geräten, und verbessert erheblich die Effizienz und Anwendbarkeit von KI-Modellen."}, "vllm": {"description": "vLLM ist eine schnelle und benutzerfreundliche Bibliothek für LLM-Inferenz und -Dienste."}, "volcengine": {"description": "Die von ByteDance eingeführte Entwicklungsplattform für große Modellservices bietet funktionsreiche, sichere und preislich wettbewerbsfähige Modellaufrufdienste. Sie bietet zudem End-to-End-Funktionen wie Moduldaten, Feinabstimmung, Inferenz und Bewertung, um die Entwicklung Ihrer KI-Anwendungen umfassend zu unterstützen."}, "wenxin": {"description": "Eine unternehmensweite, umfassende Plattform für die Entwicklung und den Service von großen Modellen und KI-nativen Anwendungen, die die vollständigsten und benutzerfreundlichsten Werkzeuge für die Entwicklung generativer KI-Modelle und den gesamten Anwendungsentwicklungsprozess bietet."}, "xai": {"description": "xAI ist ein Unternehmen, das sich der Entwicklung von Künstlicher Intelligenz widmet, um menschliche wissenschaftliche Entdeckungen zu beschleunigen. Unsere Mission ist es, unser gemeinsames Verständnis des Universums voranzutreiben."}, "xinference": {"description": "Xorbits Inference (Xinference) ist eine Open-Source-Plattform zur Vereinfachung der Ausführung und Integration verschiedener KI-Modelle. Mit Xinference können Sie beliebige Open-Source-LLMs, Embedding-Modelle und multimodale Modelle in der Cloud oder lokal ausführen, um leistungsstarke KI-Anwendungen zu erstellen."}, "zeroone": {"description": "01.<PERSON> konzentriert sich auf die künstliche Intelligenz-Technologie der AI 2.0-Ära und fördert aktiv die Innovation und Anwendung von 'Mensch + künstliche Intelligenz', indem sie leistungsstarke Modelle und fortschrittliche KI-Technologien einsetzt, um die Produktivität der Menschen zu steigern und technologische Befähigung zu erreichen."}, "zhipu": {"description": "Zhipu AI bietet eine offene Plattform für multimodale und Sprachmodelle, die eine breite Palette von KI-Anwendungsszenarien unterstützt, da<PERSON><PERSON>bei<PERSON>, Bildverständnis und Programmierhilfe."}}